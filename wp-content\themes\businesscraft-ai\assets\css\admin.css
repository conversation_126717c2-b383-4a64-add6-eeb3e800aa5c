/* BusinessCraft AI Admin Dashboard Styles */

/* Dashboard Layout */
.businesscraft-ai-dashboard {
    max-width: 1200px;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.metric-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.metric-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

/* Token Optimization Metrics */
.optimization-metrics {
    margin: 30px 0;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.optimization-metrics h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.optimization-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.optimization-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #667eea;
    text-align: center;
}

.optimization-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.optimization-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

/* Charts */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.chart-container {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

/* Activity Grid */
.activity-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.activity-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.activity-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

/* Export Buttons */
.export-buttons {
    margin: 20px 0;
    text-align: center;
}

.export-buttons .button {
    margin: 0 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .optimization-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .activity-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .metric-value {
        font-size: 24px;
    }

    .optimization-value {
        font-size: 20px;
    }
}

/* Status Indicators */
.status-success {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.status-failed {
    color: #dc3545;
    font-weight: bold;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.tooltip:hover::after {
    opacity: 1;
}

/* Success/Error Messages */
.notice-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.notice-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

/* Usage Feedback Styles */
.usage-feedback {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    font-size: 13px;
    transition: opacity 0.3s ease;
}

.usage-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.usage-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-weight: 500;
}

.model-info {
    background: #e3f2fd !important;
    color: #1976d2;
}

.tokens-info {
    background: #f3e5f5 !important;
    color: #7b1fa2;
}

.credits-info {
    background: #fff3e0 !important;
    color: #f57c00;
}

.remaining-info {
    background: #e8f5e8 !important;
    color: #388e3c;
}

/* Chat Controls Styles */
.chat-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.language-selector,
.template-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-selector {
    flex: 1;
    min-width: 250px;
}

.template-selector select {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.save-template-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.save-template-btn:hover {
    background: #5a6fd8;
}

.save-template-btn:active {
    background: #4c63d2;
}

.preferences-link {
    display: flex;
    align-items: center;
}

.preferences-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
    padding: 6px 10px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preferences-btn:hover {
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
}

/* Success Message Styles */
.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 10px 15px;
    margin: 10px 0;
    font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chat-controls {
        flex-direction: column;
        gap: 10px;
    }

    .template-selector {
        min-width: auto;
    }
}

/* Table Enhancements */
.wp-list-table th,
.wp-list-table td {
    padding: 12px 8px;
}

.wp-list-table .column-status {
    width: 100px;
}

.wp-list-table .column-date {
    width: 120px;
}

.wp-list-table .column-amount {
    width: 100px;
    text-align: right;
}

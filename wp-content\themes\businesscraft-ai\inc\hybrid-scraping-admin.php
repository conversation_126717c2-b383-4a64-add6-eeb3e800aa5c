<?php
/**
 * Hybrid Scraping Admin Interface
 * 
 * Admin interface for configuring and monitoring the hybrid scraping system.
 *
 * @package ChatGABI
 * @since 1.4.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Hybrid scraping menu is now integrated into main admin menu
 * See chatgabi_add_admin_menu() in admin-dashboard.php
 */

/**
 * Hybrid scraping admin page
 */
function chatgabi_hybrid_scraping_admin_page() {
    // Handle form submissions
    if (isset($_POST['save_api_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'chatgabi_api_settings')) {
        chatgabi_save_api_settings();
    }
    
    if (isset($_POST['test_apis']) && wp_verify_nonce($_POST['_wpnonce'], 'chatgabi_test_apis')) {
        $test_results = chatgabi_test_api_connections();
    }
    
    // Get current settings
    $api_settings = chatgabi_get_api_settings();
    $usage_stats = chatgabi_get_api_usage_stats();
    
    ?>
    <div class="wrap">
        <h1><?php _e('ChatGABI Hybrid Scraping Configuration', 'chatgabi'); ?></h1>
        
        <?php if (isset($_POST['save_api_settings'])): ?>
            <div class="notice notice-success is-dismissible">
                <p><?php _e('API settings saved successfully!', 'chatgabi'); ?></p>
            </div>
        <?php endif; ?>
        
        <!-- API Configuration -->
        <div class="card">
            <h2><?php _e('API Configuration', 'chatgabi'); ?></h2>
            <form method="post" action="">
                <?php wp_nonce_field('chatgabi_api_settings'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Bright Data API Key', 'chatgabi'); ?></th>
                        <td>
                            <input type="password" name="brightdata_api_key"
                                   value="<?php echo esc_attr($api_settings['brightdata_api_key']); ?>"
                                   class="regular-text" />
                            <p class="description">
                                <?php _e('Get your API key from Bright Data dashboard. Pay-per-use: $15-20/GB (Estimated: $63/month)', 'chatgabi'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Bright Data Zone ID', 'chatgabi'); ?></th>
                        <td>
                            <input type="text" name="brightdata_zone_id"
                                   value="<?php echo esc_attr($api_settings['brightdata_zone_id']); ?>"
                                   class="regular-text" placeholder="zone_chatgabi_african" />
                            <p class="description">
                                <?php _e('Your Bright Data zone ID for African proxy network access', 'chatgabi'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('ScraperAPI Key', 'chatgabi'); ?></th>
                        <td>
                            <input type="password" name="scraperapi_api_key" 
                                   value="<?php echo esc_attr($api_settings['scraperapi_api_key']); ?>" 
                                   class="regular-text" />
                            <p class="description">
                                <?php _e('Get your API key from ScraperAPI dashboard. Recommended plan: Startup ($149/month)', 'chatgabi'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Monthly Budget Limit', 'chatgabi'); ?></th>
                        <td>
                            <input type="number" name="monthly_budget_limit"
                                   value="<?php echo esc_attr($api_settings['monthly_budget_limit']); ?>"
                                   min="100" max="2000" step="50" />
                            <p class="description">
                                <?php _e('Maximum monthly budget for API usage in USD. New target: $215 (79% savings vs previous)', 'chatgabi'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Enable Hybrid Scraping', 'chatgabi'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_hybrid_scraping" value="1" 
                                       <?php checked($api_settings['enable_hybrid_scraping'], 1); ?> />
                                <?php _e('Use commercial APIs for complex sites', 'chatgabi'); ?>
                            </label>
                            <p class="description">
                                <?php _e('When enabled, the system will automatically route complex sites to commercial APIs.', 'chatgabi'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="save_api_settings" class="button-primary" 
                           value="<?php _e('Save API Settings', 'chatgabi'); ?>" />
                    <input type="submit" name="test_apis" class="button-secondary" 
                           value="<?php _e('Test API Connections', 'chatgabi'); ?>" />
                </p>
            </form>
        </div>
        
        <!-- API Test Results -->
        <?php if (isset($test_results)): ?>
            <div class="card">
                <h2><?php _e('API Connection Test Results', 'chatgabi'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('API Provider', 'chatgabi'); ?></th>
                            <th><?php _e('Status', 'chatgabi'); ?></th>
                            <th><?php _e('Response Time', 'chatgabi'); ?></th>
                            <th><?php _e('Details', 'chatgabi'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($test_results as $api => $result): ?>
                            <tr>
                                <td><?php echo esc_html(ucfirst($api)); ?></td>
                                <td>
                                    <?php if ($result['success']): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>
                                        <?php _e('Connected', 'chatgabi'); ?>
                                    <?php else: ?>
                                        <span class="dashicons dashicons-dismiss" style="color: #dc3232;"></span>
                                        <?php _e('Failed', 'chatgabi'); ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($result['response_time']); ?>ms</td>
                                <td><?php echo esc_html($result['message']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <!-- Usage Statistics -->
        <div class="card">
            <h2><?php _e('Current Month Usage Statistics', 'chatgabi'); ?></h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('API Provider', 'chatgabi'); ?></th>
                        <th><?php _e('Requests', 'chatgabi'); ?></th>
                        <th><?php _e('Success Rate', 'chatgabi'); ?></th>
                        <th><?php _e('Total Cost', 'chatgabi'); ?></th>
                        <th><?php _e('Avg Cost/Request', 'chatgabi'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($usage_stats)): ?>
                        <?php foreach ($usage_stats as $stat): ?>
                            <tr>
                                <td><?php echo esc_html(ucfirst($stat['api_provider'])); ?></td>
                                <td><?php echo number_format($stat['total_requests']); ?></td>
                                <td><?php echo number_format($stat['success_rate'], 1); ?>%</td>
                                <td>$<?php echo number_format($stat['total_cost'], 4); ?></td>
                                <td>$<?php echo number_format($stat['avg_cost'], 6); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5"><?php _e('No usage data available for current month.', 'chatgabi'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Routing Configuration -->
        <div class="card">
            <h2><?php _e('Smart Routing Configuration', 'chatgabi'); ?></h2>
            <p><?php _e('The hybrid scraping system automatically routes requests based on site complexity:', 'chatgabi'); ?></p>
            
            <h3><?php _e('High-Priority African Sites (Commercial APIs)', 'chatgabi'); ?></h3>
            <ul>
                <li><strong>Ghana:</strong> Bank of Ghana (bog.gov.gh) → Bright Data, Ghana Stock Exchange (gse.com.gh) → Bright Data</li>
                <li><strong>Kenya:</strong> Nairobi Securities Exchange (nse.co.ke) → Bright Data, Central Bank of Kenya → Bright Data</li>
                <li><strong>Nigeria:</strong> Nigerian Stock Exchange (nse.com.ng) → Bright Data, Central Bank of Nigeria (cbn.gov.ng) → Bright Data</li>
                <li><strong>South Africa:</strong> Johannesburg Stock Exchange (jse.co.za) → Bright Data, Reserve Bank (resbank.co.za) → Bright Data</li>
            </ul>

            <h3><?php _e('New Optimized Routing Logic', 'chatgabi'); ?></h3>
            <ul>
                <li><strong>Native PHP (45%):</strong> Static sites, basic content, news websites</li>
                <li><strong>Bright Data (55%):</strong> Government portals, financial sites, stock exchanges (Pay-per-use: ~$63/month)</li>
                <li><strong>ScraperAPI (Backup):</strong> High-security sites when Bright Data fails ($149/month fixed)</li>
            </ul>

            <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h4 style="color: #0073aa; margin-top: 0;">💰 Cost Optimization Results</h4>
                <p><strong>Previous:</strong> ScrapingBee ($149) + ScraperAPI ($149) = $298/month</p>
                <p><strong>New:</strong> Bright Data (~$63) + ScraperAPI ($149) = ~$212/month</p>
                <p><strong>Savings:</strong> $86/month (29% reduction) with better performance!</p>
            </div>
        </div>
        
        <!-- Cost Projection -->
        <div class="card">
            <h2><?php _e('Monthly Cost Projection', 'chatgabi'); ?></h2>
            <?php $cost_projection = chatgabi_calculate_cost_projection($usage_stats); ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Current Month Usage', 'chatgabi'); ?></th>
                    <td>$<?php echo number_format($cost_projection['current_cost'], 2); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Projected Monthly Cost', 'chatgabi'); ?></th>
                    <td>$<?php echo number_format($cost_projection['projected_cost'], 2); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Budget Remaining', 'chatgabi'); ?></th>
                    <td>
                        $<?php echo number_format($cost_projection['budget_remaining'], 2); ?>
                        <?php if ($cost_projection['budget_remaining'] < 100): ?>
                            <span style="color: #dc3232;"><?php _e('(Low Budget Warning)', 'chatgabi'); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Budget Utilization', 'chatgabi'); ?></th>
                    <td>
                        <?php echo number_format($cost_projection['budget_utilization'], 1); ?>%
                        <div style="background: #f0f0f0; height: 20px; width: 200px; border-radius: 10px; overflow: hidden;">
                            <div style="background: <?php echo $cost_projection['budget_utilization'] > 80 ? '#dc3232' : '#46b450'; ?>; 
                                        height: 100%; width: <?php echo min($cost_projection['budget_utilization'], 100); ?>%; 
                                        transition: width 0.3s;"></div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <?php
}

/**
 * Save API settings
 */
function chatgabi_save_api_settings() {
    $settings = array(
        'brightdata_api_key' => sanitize_text_field($_POST['brightdata_api_key']),
        'brightdata_zone_id' => sanitize_text_field($_POST['brightdata_zone_id']),
        'scraperapi_api_key' => sanitize_text_field($_POST['scraperapi_api_key']),
        'monthly_budget_limit' => intval($_POST['monthly_budget_limit']),
        'enable_hybrid_scraping' => isset($_POST['enable_hybrid_scraping']) ? 1 : 0
    );

    foreach ($settings as $key => $value) {
        update_option('chatgabi_' . $key, $value);
    }
}

/**
 * Get API settings
 */
function chatgabi_get_api_settings() {
    return array(
        'brightdata_api_key' => get_option('chatgabi_brightdata_api_key', ''),
        'brightdata_zone_id' => get_option('chatgabi_brightdata_zone_id', 'zone_chatgabi_african'),
        'scraperapi_api_key' => get_option('chatgabi_scraperapi_api_key', ''),
        'monthly_budget_limit' => get_option('chatgabi_monthly_budget_limit', 215),
        'enable_hybrid_scraping' => get_option('chatgabi_enable_hybrid_scraping', 0)
    );
}

/**
 * Test API connections
 */
function chatgabi_test_api_connections() {
    $settings = chatgabi_get_api_settings();
    $results = array();
    
    // Test Bright Data
    if (!empty($settings['brightdata_api_key'])) {
        $start_time = microtime(true);

        // Test with a simple request to verify API access
        $test_url = 'https://httpbin.org/ip';
        $response = wp_remote_get($test_url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $settings['brightdata_api_key'],
                'User-Agent' => 'ChatGABI-Test/1.0'
            ),
            'timeout' => 30
        ));

        $results['brightdata'] = array(
            'success' => !is_wp_error($response) && wp_remote_retrieve_response_code($response) == 200,
            'response_time' => round((microtime(true) - $start_time) * 1000),
            'message' => is_wp_error($response) ? $response->get_error_message() : 'Connection successful - Pay-per-use active'
        );
    } else {
        $results['brightdata'] = array(
            'success' => false,
            'response_time' => 0,
            'message' => 'API key not configured'
        );
    }
    
    // Test ScraperAPI
    if (!empty($settings['scraperapi_api_key'])) {
        $start_time = microtime(true);
        $response = wp_remote_get('http://api.scraperapi.com/?api_key=' . $settings['scraperapi_api_key'] . '&url=https://httpbin.org/ip', array(
            'timeout' => 30
        ));
        
        $results['scraperapi'] = array(
            'success' => !is_wp_error($response) && wp_remote_retrieve_response_code($response) == 200,
            'response_time' => round((microtime(true) - $start_time) * 1000),
            'message' => is_wp_error($response) ? $response->get_error_message() : 'Connection successful'
        );
    } else {
        $results['scraperapi'] = array(
            'success' => false,
            'response_time' => 0,
            'message' => 'API key not configured'
        );
    }
    
    return $results;
}

/**
 * Get API usage statistics
 */
function chatgabi_get_api_usage_stats() {
    if (class_exists('ChatGABI_Hybrid_Scraping_Router')) {
        $router = new ChatGABI_Hybrid_Scraping_Router();
        return $router->get_usage_statistics();
    }
    
    return array();
}

/**
 * Calculate cost projection
 */
function chatgabi_calculate_cost_projection($usage_stats) {
    $settings = chatgabi_get_api_settings();
    $budget_limit = $settings['monthly_budget_limit'];
    
    $current_cost = 0;
    if (!empty($usage_stats)) {
        foreach ($usage_stats as $stat) {
            $current_cost += $stat['total_cost'];
        }
    }
    
    // Add fixed API subscription costs (updated for Bright Data pay-per-use)
    $fixed_costs = 0;
    // Bright Data is pay-per-use, no fixed cost
    if (!empty($settings['scraperapi_api_key'])) $fixed_costs += 149;
    
    $projected_cost = $current_cost + $fixed_costs;
    $budget_remaining = $budget_limit - $projected_cost;
    $budget_utilization = ($projected_cost / $budget_limit) * 100;
    
    return array(
        'current_cost' => $current_cost,
        'projected_cost' => $projected_cost,
        'budget_remaining' => $budget_remaining,
        'budget_utilization' => $budget_utilization,
        'fixed_costs' => $fixed_costs
    );
}
?>

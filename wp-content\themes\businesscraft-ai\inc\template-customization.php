<?php
/**
 * Advanced Template Customization for BusinessCraft AI
 * 
 * Handles advanced template customization features including:
 * - Custom template themes and layouts
 * - Dynamic placeholder management
 * - Template versioning and history
 * - Advanced formatting options
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize template customization functionality
 */
function businesscraft_ai_init_template_customization() {
    // Add AJAX handlers
    add_action('wp_ajax_customize_template', 'businesscraft_ai_handle_template_customization');
    add_action('wp_ajax_save_template_theme', 'businesscraft_ai_handle_save_template_theme');
    add_action('wp_ajax_load_template_themes', 'businesscraft_ai_handle_load_template_themes');
    add_action('wp_ajax_preview_template_customization', 'businesscraft_ai_handle_preview_customization');
    
    // Add REST API endpoints
    add_action('rest_api_init', 'businesscraft_ai_register_customization_routes');
    
    // Enqueue customization scripts
    add_action('wp_enqueue_scripts', 'businesscraft_ai_enqueue_customization_scripts');
}
add_action('init', 'businesscraft_ai_init_template_customization');

/**
 * Register REST API routes for template customization
 */
function businesscraft_ai_register_customization_routes() {
    register_rest_route('businesscraft-ai/v1', '/templates/customize', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_customize_template',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'template_id' => array(
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint'
            ),
            'customizations' => array(
                'required' => true,
                'type' => 'object'
            )
        )
    ));
    
    register_rest_route('businesscraft-ai/v1', '/templates/themes', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_template_themes',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/templates/(?P<id>\d+)/versions', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_template_versions',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
}

/**
 * Enqueue template customization scripts
 */
function businesscraft_ai_enqueue_customization_scripts() {
    if (is_page('templates') || is_page('dashboard')) {
        wp_enqueue_script(
            'businesscraft-ai-template-customization',
            get_template_directory_uri() . '/assets/js/template-customization.js',
            array('jquery', 'wp-color-picker'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style('wp-color-picker');
        
        wp_localize_script('businesscraft-ai-template-customization', 'businesscraftCustomization', array(
            'restUrl' => rest_url('businesscraft-ai/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'strings' => array(
                'customizing' => __('Customizing template...', 'businesscraft-ai'),
                'customizationSaved' => __('Template customization saved!', 'businesscraft-ai'),
                'customizationError' => __('Failed to save customization. Please try again.', 'businesscraft-ai'),
                'previewGenerating' => __('Generating preview...', 'businesscraft-ai'),
                'themeApplied' => __('Theme applied successfully!', 'businesscraft-ai'),
                'confirmRevert' => __('Are you sure you want to revert to the original template?', 'businesscraft-ai')
            )
        ));
        
        wp_enqueue_style(
            'businesscraft-ai-template-customization',
            get_template_directory_uri() . '/assets/css/template-customization.css',
            array('wp-color-picker'),
            '1.0.0'
        );
    }
}

/**
 * Handle template customization AJAX request
 */
function businesscraft_ai_handle_template_customization() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_die(__('Security check failed', 'businesscraft-ai'));
    }
    
    // Check user permissions
    if (!is_user_logged_in()) {
        wp_die(__('You must be logged in to customize templates', 'businesscraft-ai'));
    }
    
    $template_id = isset($_POST['template_id']) ? absint($_POST['template_id']) : 0;
    $customizations = isset($_POST['customizations']) ? $_POST['customizations'] : array();
    
    if (!$template_id) {
        wp_send_json_error(__('Invalid template ID', 'businesscraft-ai'));
    }
    
    // Apply customizations
    $result = businesscraft_ai_apply_template_customizations($template_id, $customizations);
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

/**
 * Apply template customizations
 */
function businesscraft_ai_apply_template_customizations($template_id, $customizations) {
    global $wpdb;
    
    $user_id = get_current_user_id();
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';
    
    // Get original template
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE id = %d AND user_id = %d",
        $template_id,
        $user_id
    ));
    
    if (!$template) {
        return array(
            'success' => false,
            'message' => __('Template not found or access denied', 'businesscraft-ai')
        );
    }
    
    try {
        // Save current version as backup
        businesscraft_ai_save_template_version($template_id, $template->generated_content, 'pre_customization');
        
        // Apply customizations
        $customized_content = businesscraft_ai_process_template_customizations($template->generated_content, $customizations);
        
        // Update template with customized content
        $updated = $wpdb->update(
            $table_name,
            array(
                'generated_content' => $customized_content,
                'customizations' => json_encode($customizations),
                'updated_at' => current_time('mysql')
            ),
            array('id' => $template_id),
            array('%s', '%s', '%s'),
            array('%d')
        );
        
        if ($updated !== false) {
            // Save new version
            businesscraft_ai_save_template_version($template_id, $customized_content, 'customized');
            
            return array(
                'success' => true,
                'message' => __('Template customized successfully', 'businesscraft-ai'),
                'customized_content' => $customized_content
            );
        } else {
            return array(
                'success' => false,
                'message' => __('Failed to save customizations', 'businesscraft-ai')
            );
        }
        
    } catch (Exception $e) {
        error_log('Template Customization Error: ' . $e->getMessage());
        return array(
            'success' => false,
            'message' => __('An error occurred while customizing the template', 'businesscraft-ai')
        );
    }
}

/**
 * Process template customizations
 */
function businesscraft_ai_process_template_customizations($content, $customizations) {
    // Apply text replacements
    if (isset($customizations['text_replacements']) && is_array($customizations['text_replacements'])) {
        foreach ($customizations['text_replacements'] as $replacement) {
            if (isset($replacement['find']) && isset($replacement['replace'])) {
                $content = str_replace($replacement['find'], $replacement['replace'], $content);
            }
        }
    }
    
    // Apply section modifications
    if (isset($customizations['section_modifications']) && is_array($customizations['section_modifications'])) {
        foreach ($customizations['section_modifications'] as $section => $modification) {
            $content = businesscraft_ai_modify_content_section($content, $section, $modification);
        }
    }
    
    // Apply formatting changes
    if (isset($customizations['formatting']) && is_array($customizations['formatting'])) {
        $content = businesscraft_ai_apply_formatting_changes($content, $customizations['formatting']);
    }
    
    // Apply theme-specific changes
    if (isset($customizations['theme']) && !empty($customizations['theme'])) {
        $content = businesscraft_ai_apply_template_theme($content, $customizations['theme']);
    }
    
    return $content;
}

/**
 * Modify content section
 */
function businesscraft_ai_modify_content_section($content, $section, $modification) {
    // Find section in content
    $pattern = '/^(#{1,3}\s*' . preg_quote($section, '/') . '.*?)(?=^#{1,3}\s|\z)/ms';
    
    if (preg_match($pattern, $content, $matches)) {
        $original_section = $matches[1];
        
        switch ($modification['action']) {
            case 'replace':
                $new_section = $modification['content'];
                break;
            case 'append':
                $new_section = $original_section . "\n\n" . $modification['content'];
                break;
            case 'prepend':
                $new_section = $original_section . "\n\n" . $modification['content'] . "\n\n";
                break;
            case 'remove':
                $new_section = '';
                break;
            default:
                $new_section = $original_section;
        }
        
        $content = str_replace($original_section, $new_section, $content);
    }
    
    return $content;
}

/**
 * Apply formatting changes
 */
function businesscraft_ai_apply_formatting_changes($content, $formatting) {
    // Apply header formatting
    if (isset($formatting['headers'])) {
        foreach ($formatting['headers'] as $level => $style) {
            $pattern = '/^(#{' . $level . '}\s*)(.*?)$/m';
            $replacement = '$1' . $style['prefix'] . '$2' . $style['suffix'];
            $content = preg_replace($pattern, $replacement, $content);
        }
    }
    
    // Apply list formatting
    if (isset($formatting['lists'])) {
        $list_style = $formatting['lists']['style'];
        if ($list_style === 'numbered') {
            $content = preg_replace('/^[\*\-\+]\s/m', '1. ', $content);
        } elseif ($list_style === 'bullet') {
            $content = preg_replace('/^\d+\.\s/m', '• ', $content);
        }
    }
    
    return $content;
}

/**
 * Apply template theme
 */
function businesscraft_ai_apply_template_theme($content, $theme_id) {
    $themes = businesscraft_ai_get_template_themes();
    
    if (!isset($themes[$theme_id])) {
        return $content;
    }
    
    $theme = $themes[$theme_id];
    
    // Apply theme-specific formatting
    if (isset($theme['content_transformations'])) {
        foreach ($theme['content_transformations'] as $transformation) {
            $content = preg_replace($transformation['pattern'], $transformation['replacement'], $content);
        }
    }
    
    return $content;
}

/**
 * Get available template themes
 */
function businesscraft_ai_get_template_themes() {
    return array(
        'professional' => array(
            'name' => __('Professional', 'businesscraft-ai'),
            'description' => __('Clean, formal business document style', 'businesscraft-ai'),
            'content_transformations' => array(
                array(
                    'pattern' => '/^(#{1,3}\s*)(.*?)$/m',
                    'replacement' => '$1**$2**'
                )
            )
        ),
        'modern' => array(
            'name' => __('Modern', 'businesscraft-ai'),
            'description' => __('Contemporary design with emphasis on readability', 'businesscraft-ai'),
            'content_transformations' => array(
                array(
                    'pattern' => '/^[\*\-\+]\s/m',
                    'replacement' => '→ '
                )
            )
        ),
        'executive' => array(
            'name' => __('Executive', 'businesscraft-ai'),
            'description' => __('High-level executive summary style', 'businesscraft-ai'),
            'content_transformations' => array(
                array(
                    'pattern' => '/^(#{1,3}\s*)(.*?)$/m',
                    'replacement' => '$1$2'
                )
            )
        ),
        'startup' => array(
            'name' => __('Startup', 'businesscraft-ai'),
            'description' => __('Dynamic style perfect for startup presentations', 'businesscraft-ai'),
            'content_transformations' => array(
                array(
                    'pattern' => '/^[\*\-\+]\s/m',
                    'replacement' => '🚀 '
                )
            )
        ),
        'african_focus' => array(
            'name' => __('African Focus', 'businesscraft-ai'),
            'description' => __('Emphasizes African market context and opportunities', 'businesscraft-ai'),
            'content_transformations' => array(
                array(
                    'pattern' => '/\b(market|opportunity|growth)\b/i',
                    'replacement' => '**$1**'
                )
            )
        )
    );
}

/**
 * Save template version
 */
function businesscraft_ai_save_template_version($template_id, $content, $version_type) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_template_versions';
    
    // Create table if it doesn't exist
    businesscraft_ai_create_template_versions_table();
    
    $wpdb->insert(
        $table_name,
        array(
            'template_id' => $template_id,
            'content' => $content,
            'version_type' => $version_type,
            'created_at' => current_time('mysql'),
            'user_id' => get_current_user_id()
        ),
        array('%d', '%s', '%s', '%s', '%d')
    );
    
    return $wpdb->insert_id;
}

/**
 * Create template versions table
 */
function businesscraft_ai_create_template_versions_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_template_versions';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        template_id bigint(20) NOT NULL,
        content longtext NOT NULL,
        version_type varchar(50) NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        user_id bigint(20) NOT NULL,
        PRIMARY KEY (id),
        KEY template_id (template_id),
        KEY version_type (version_type),
        KEY created_at (created_at),
        KEY user_id (user_id)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

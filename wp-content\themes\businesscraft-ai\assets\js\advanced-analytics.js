/**
 * Advanced Analytics JavaScript for ChatGABI
 * 
 * Handles user-facing analytics dashboard with real-time updates,
 * interactive charts, and African market-specific KPIs.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Advanced Analytics Manager
    const AdvancedAnalytics = {
        charts: {},
        refreshInterval: null,
        isInitialized: false,
        currentPeriod: 'weekly',

        /**
         * Initialize advanced analytics
         */
        init: function() {
            this.bindEvents();
            this.loadAnalyticsData();
            this.initializeCharts();
            this.startAutoRefresh();
            
            console.log('ChatGABI: Advanced Analytics initialized');
            this.isInitialized = true;
        },

        /**
         * Bind event listeners
         */
        bindEvents: function() {
            // Period selection
            $(document).on('change', '.analytics-period-select', this.handlePeriodChange.bind(this));
            
            // Export buttons
            $(document).on('click', '.export-analytics-btn', this.handleExportAnalytics.bind(this));
            
            // Refresh button
            $(document).on('click', '.refresh-analytics-btn', this.refreshAnalytics.bind(this));
            
            // Chart interactions
            $(document).on('click', '.chart-metric-toggle', this.toggleChartMetric.bind(this));
            
            // Insight actions
            $(document).on('click', '.insight-action-btn', this.handleInsightAction.bind(this));
            
            // Progress milestone clicks
            $(document).on('click', '.milestone-item', this.showMilestoneDetails.bind(this));
        },

        /**
         * Load analytics data
         */
        loadAnalyticsData: function() {
            this.showLoadingState();
            
            // Load main analytics
            $.ajax({
                url: chatgabiAnalytics.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_user_analytics',
                    period: this.currentPeriod,
                    metrics: ['ai_interactions', 'credits_used', 'templates_used', 'avg_response_quality']
                },
                success: (response) => {
                    if (response.success) {
                        this.updateAnalyticsDashboard(response.data);
                    } else {
                        this.showError('Failed to load analytics data');
                    }
                },
                error: (xhr, status, error) => {
                    this.showError('Network error loading analytics');
                },
                complete: () => {
                    this.hideLoadingState();
                }
            });

            // Load progress metrics
            this.loadProgressMetrics();
            
            // Load usage insights
            this.loadUsageInsights();
        },

        /**
         * Load progress metrics
         */
        loadProgressMetrics: function() {
            $.ajax({
                url: chatgabiAnalytics.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_progress_metrics',
                    type: 'business_planning'
                },
                success: (response) => {
                    if (response.success) {
                        this.updateProgressDashboard(response.data);
                    }
                }
            });
        },

        /**
         * Load usage insights
         */
        loadUsageInsights: function() {
            $.ajax({
                url: chatgabiAnalytics.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_usage_insights'
                },
                success: (response) => {
                    if (response.success) {
                        this.updateInsightsDashboard(response.data);
                    }
                }
            });
        },

        /**
         * Update analytics dashboard
         */
        updateAnalyticsDashboard: function(data) {
            // Update summary cards
            this.updateSummaryCards(data.summary);
            
            // Update trend charts
            this.updateTrendCharts(data.trends);
            
            // Update insights
            if (data.insights) {
                this.updateInsightsSection(data.insights);
            }
        },

        /**
         * Update summary cards
         */
        updateSummaryCards: function(summary) {
            const cardMappings = {
                'ai_interactions': '.total-interactions-card',
                'credits_used': '.total-credits-card',
                'templates_used': '.total-templates-card',
                'avg_response_quality': '.avg-quality-card'
            };

            Object.entries(cardMappings).forEach(([metric, selector]) => {
                const $card = $(selector);
                if ($card.length && summary[metric]) {
                    const data = summary[metric];
                    
                    $card.find('.metric-value').text(this.formatMetricValue(metric, data.total));
                    $card.find('.metric-average').text(this.formatMetricValue(metric, data.average));
                    $card.find('.metric-trend').html(this.generateTrendIndicator(data));
                }
            });
        },

        /**
         * Format metric value for display
         */
        formatMetricValue: function(metric, value) {
            switch (metric) {
                case 'credits_used':
                    return Math.round(value).toLocaleString();
                case 'ai_interactions':
                case 'templates_used':
                    return Math.round(value);
                case 'avg_response_quality':
                    return (value * 20).toFixed(1) + '%'; // Convert to percentage
                default:
                    return value;
            }
        },

        /**
         * Generate trend indicator
         */
        generateTrendIndicator: function(data) {
            // Simple trend calculation (would be enhanced with actual trend data)
            const trend = data.average > (data.total / data.count) ? 'up' : 'down';
            const icon = trend === 'up' ? '📈' : '📉';
            const color = trend === 'up' ? 'green' : 'red';
            
            return `<span style="color: ${color}">${icon}</span>`;
        },

        /**
         * Update trend charts
         */
        updateTrendCharts: function(trends) {
            // Update interactions chart
            if (trends.ai_interactions) {
                this.updateChart('interactions-chart', {
                    type: 'line',
                    data: this.formatChartData(trends.ai_interactions, 'AI Interactions'),
                    options: this.getLineChartOptions('Interactions Over Time')
                });
            }

            // Update credits chart
            if (trends.credits_used) {
                this.updateChart('credits-chart', {
                    type: 'bar',
                    data: this.formatChartData(trends.credits_used, 'Credits Used'),
                    options: this.getBarChartOptions('Credit Usage')
                });
            }

            // Update quality chart
            if (trends.avg_response_quality) {
                this.updateChart('quality-chart', {
                    type: 'line',
                    data: this.formatChartData(trends.avg_response_quality, 'Response Quality', true),
                    options: this.getLineChartOptions('Response Quality Trend')
                });
            }
        },

        /**
         * Format data for Chart.js
         */
        formatChartData: function(data, label, isPercentage = false) {
            const labels = data.map(item => this.formatDate(item.date));
            const values = data.map(item => isPercentage ? item.value * 20 : item.value);

            return {
                labels: labels,
                datasets: [{
                    label: label,
                    data: values,
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            };
        },

        /**
         * Format date for display
         */
        formatDate: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            });
        },

        /**
         * Get line chart options
         */
        getLineChartOptions: function(title) {
            return {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: title,
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            };
        },

        /**
         * Get bar chart options
         */
        getBarChartOptions: function(title) {
            return {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: title,
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };
        },

        /**
         * Update or create chart
         */
        updateChart: function(canvasId, config) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;

            // Destroy existing chart
            if (this.charts[canvasId]) {
                this.charts[canvasId].destroy();
            }

            // Create new chart
            this.charts[canvasId] = new Chart(canvas, config);
        },

        /**
         * Update progress dashboard
         */
        updateProgressDashboard: function(progress) {
            // Update progress bar
            const $progressBar = $('.business-progress-bar');
            const $progressText = $('.progress-percentage');
            
            if ($progressBar.length) {
                $progressBar.css('width', progress.completion_percentage + '%');
                $progressText.text(Math.round(progress.completion_percentage) + '%');
            }

            // Update current stage
            $('.current-stage').text(this.formatStageName(progress.current_stage));

            // Update milestones
            this.updateMilestones(progress.milestones);

            // Update recommendations
            this.updateRecommendations(progress.recommendations);
        },

        /**
         * Format stage name for display
         */
        formatStageName: function(stage) {
            const stageNames = {
                'idea': 'Idea Development',
                'validation': 'Market Validation',
                'planning': 'Business Planning',
                'funding': 'Funding & Investment',
                'launch': 'Business Launch',
                'growth': 'Growth & Expansion',
                'scaling': 'Scaling Operations',
                'optimization': 'Performance Optimization'
            };

            return stageNames[stage] || stage;
        },

        /**
         * Update milestones display
         */
        updateMilestones: function(milestones) {
            const $container = $('.milestones-container');
            if (!$container.length) return;

            const allStages = ['idea', 'validation', 'planning', 'funding', 'launch', 'growth', 'scaling', 'optimization'];
            
            let html = '';
            allStages.forEach(stage => {
                const isCompleted = milestones.hasOwnProperty(stage);
                const completedClass = isCompleted ? 'completed' : '';
                const completedIcon = isCompleted ? '✅' : '⭕';
                
                html += `
                    <div class="milestone-item ${completedClass}" data-stage="${stage}">
                        <div class="milestone-icon">${completedIcon}</div>
                        <div class="milestone-content">
                            <h4>${this.formatStageName(stage)}</h4>
                            ${isCompleted ? `<small>Completed: ${this.formatDate(milestones[stage].completed_at)}</small>` : ''}
                        </div>
                    </div>
                `;
            });

            $container.html(html);
        },

        /**
         * Update recommendations
         */
        updateRecommendations: function(recommendations) {
            const $container = $('.recommendations-container');
            if (!$container.length || !recommendations.length) return;

            let html = '';
            recommendations.forEach(rec => {
                html += `
                    <div class="recommendation-item">
                        <div class="recommendation-content">
                            <h5>${rec}</h5>
                        </div>
                        <button class="recommendation-action">Take Action</button>
                    </div>
                `;
            });

            $container.html(html);
        },

        /**
         * Update insights dashboard
         */
        updateInsightsDashboard: function(insights) {
            // Update efficiency score
            $('.efficiency-score').text(insights.credit_efficiency + '%');
            
            // Update engagement level
            $('.engagement-level').text(this.formatEngagementLevel(insights.engagement_level));
            
            // Update productivity score
            $('.productivity-score').text(Math.round(insights.productivity_score) + '%');

            // Update insights list
            this.updateInsightsList(insights.recommendations);
        },

        /**
         * Format engagement level
         */
        formatEngagementLevel: function(level) {
            const levels = {
                'low': 'Low 📉',
                'medium': 'Medium 📊',
                'high': 'High 📈'
            };
            return levels[level] || level;
        },

        /**
         * Update insights list
         */
        updateInsightsList: function(recommendations) {
            const $container = $('.insights-list');
            if (!$container.length || !recommendations.length) return;

            let html = '';
            recommendations.forEach(insight => {
                html += `
                    <div class="insight-item" data-type="${insight.type}">
                        <div class="insight-content">
                            <h5>${insight.title}</h5>
                            <p>${insight.description}</p>
                            <small class="insight-action">${insight.action}</small>
                        </div>
                        <button class="insight-action-btn" data-action="${insight.action}">
                            Take Action
                        </button>
                    </div>
                `;
            });

            $container.html(html);
        },

        /**
         * Initialize charts
         */
        initializeCharts: function() {
            // Ensure Chart.js is loaded
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded, loading from CDN...');
                this.loadChartJS();
                return;
            }

            // Set Chart.js defaults
            Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            Chart.defaults.color = '#374151';
        },

        /**
         * Load Chart.js from CDN
         */
        loadChartJS: function() {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            script.onload = () => {
                this.initializeCharts();
                this.loadAnalyticsData(); // Reload data after Chart.js is available
            };
            document.head.appendChild(script);
        },

        /**
         * Handle period change
         */
        handlePeriodChange: function(event) {
            this.currentPeriod = $(event.target).val();
            this.loadAnalyticsData();
        },

        /**
         * Handle export analytics
         */
        handleExportAnalytics: function(event) {
            const format = $(event.target).data('format') || 'pdf';
            
            this.showExportLoading();
            
            $.ajax({
                url: chatgabiAnalytics.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_export_user_analytics',
                    format: format,
                    period: this.currentPeriod,
                    nonce: chatgabiAnalytics.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.downloadFile(response.data.url, `analytics.${format}`);
                    } else {
                        this.showError('Export failed: ' + response.data);
                    }
                },
                error: () => {
                    this.showError('Export failed due to network error');
                },
                complete: () => {
                    this.hideExportLoading();
                }
            });
        },

        /**
         * Download file
         */
        downloadFile: function(url, filename) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        /**
         * Refresh analytics
         */
        refreshAnalytics: function() {
            this.loadAnalyticsData();
        },

        /**
         * Start auto refresh
         */
        startAutoRefresh: function() {
            // Refresh every 5 minutes
            this.refreshInterval = setInterval(() => {
                this.loadAnalyticsData();
            }, 300000);
        },

        /**
         * Stop auto refresh
         */
        stopAutoRefresh: function() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        },

        /**
         * Show loading state
         */
        showLoadingState: function() {
            $('.analytics-loading').show();
            $('.analytics-content').addClass('loading');
        },

        /**
         * Hide loading state
         */
        hideLoadingState: function() {
            $('.analytics-loading').hide();
            $('.analytics-content').removeClass('loading');
        },

        /**
         * Show export loading
         */
        showExportLoading: function() {
            $('.export-analytics-btn').prop('disabled', true).text('Exporting...');
        },

        /**
         * Hide export loading
         */
        hideExportLoading: function() {
            $('.export-analytics-btn').prop('disabled', false).text('Export');
        },

        /**
         * Show error message
         */
        showError: function(message) {
            const $error = $('<div class="analytics-error">' + message + '</div>');
            $('.analytics-dashboard').prepend($error);
            
            setTimeout(() => {
                $error.fadeOut(() => $error.remove());
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof chatgabiAnalytics !== 'undefined' && $('.analytics-dashboard').length) {
            AdvancedAnalytics.init();
        }
    });

    // Cleanup on page unload
    $(window).on('beforeunload', function() {
        AdvancedAnalytics.stopAutoRefresh();
    });

    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.AdvancedAnalytics = AdvancedAnalytics;

})(jQuery);

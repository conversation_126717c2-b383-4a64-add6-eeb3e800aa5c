<?php
/**
 * Test SQL Query Fix for ChatGABI Analytics
 * This script tests the corrected session length calculation query
 */

// Load WordPress
require_once(dirname(__FILE__) . '/wp-config.php');

echo "<h1>🧪 ChatGABI SQL Query Fix Test</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
.query-box { background: #f0f0f0; padding: 10px; border-left: 4px solid #0073aa; margin: 10px 0; font-family: monospace; }
</style>";

global $wpdb;

// Test 1: Check if analytics table exists and has data
echo "<div class='section'>";
echo "<h2>📊 Step 1: Analytics Table Check</h2>";

$analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$analytics_table}'") === $analytics_table;

if ($table_exists) {
    echo "<p class='success'>✅ Analytics table exists: {$analytics_table}</p>";
    
    $row_count = $wpdb->get_var("SELECT COUNT(*) FROM {$analytics_table}");
    echo "<p class='info'>📊 Table has {$row_count} records</p>";
    
    if ($row_count == 0) {
        echo "<p class='warning'>⚠️ Table is empty - inserting test data...</p>";
        
        // Insert test data for current month
        $test_data = array(
            array('user_id' => 1, 'session_id' => 'test_session_1', 'event_type' => 'chat_start'),
            array('user_id' => 1, 'session_id' => 'test_session_1', 'event_type' => 'chat_message'),
            array('user_id' => 1, 'session_id' => 'test_session_1', 'event_type' => 'chat_end'),
            array('user_id' => 2, 'session_id' => 'test_session_2', 'event_type' => 'chat_start'),
            array('user_id' => 2, 'session_id' => 'test_session_2', 'event_type' => 'chat_end'),
        );
        
        foreach ($test_data as $i => $data) {
            $created_at = date('Y-m-d H:i:s', strtotime("-" . (30 - $i * 5) . " minutes"));
            $wpdb->insert(
                $analytics_table,
                array_merge($data, array('created_at' => $created_at)),
                array('%d', '%s', '%s', '%s')
            );
        }
        
        $new_count = $wpdb->get_var("SELECT COUNT(*) FROM {$analytics_table}");
        echo "<p class='success'>✅ Inserted test data. New count: {$new_count}</p>";
    }
} else {
    echo "<p class='error'>❌ Analytics table missing: {$analytics_table}</p>";
    echo "<p class='info'>💡 Run the database fix script first</p>";
}
echo "</div>";

// Test 2: Test the OLD problematic query
echo "<div class='section'>";
echo "<h2>❌ Step 2: Test OLD Query (Should Fail)</h2>";

$date_from = date('Y-m-01'); // First day of current month
$date_to = date('Y-m-t');    // Last day of current month

$old_query = $wpdb->prepare(
    "SELECT AVG(TIMESTAMPDIFF(MINUTE, MIN(created_at), MAX(created_at)))
     FROM {$analytics_table}
     WHERE created_at >= %s AND created_at <= %s
     GROUP BY session_id
     HAVING COUNT(*) > 1",
    $date_from,
    $date_to . ' 23:59:59'
);

echo "<div class='query-box'>";
echo "<strong>OLD Query:</strong><br>";
echo htmlspecialchars($old_query);
echo "</div>";

echo "<p class='info'>🧪 Testing old query...</p>";
$old_result = $wpdb->get_var($old_query);

if ($wpdb->last_error) {
    echo "<p class='error'>❌ OLD Query Failed (Expected): " . $wpdb->last_error . "</p>";
} else {
    echo "<p class='warning'>⚠️ OLD Query Unexpectedly Succeeded: " . ($old_result ?: 'NULL') . "</p>";
}
echo "</div>";

// Test 3: Test the NEW fixed query
echo "<div class='section'>";
echo "<h2>✅ Step 3: Test NEW Query (Should Work)</h2>";

$new_query = $wpdb->prepare(
    "SELECT AVG(session_duration) FROM (
        SELECT TIMESTAMPDIFF(MINUTE, MIN(created_at), MAX(created_at)) as session_duration
        FROM {$analytics_table}
        WHERE created_at >= %s AND created_at <= %s
        GROUP BY session_id
        HAVING COUNT(*) > 1
    ) as session_stats",
    $date_from,
    $date_to . ' 23:59:59'
);

echo "<div class='query-box'>";
echo "<strong>NEW Query:</strong><br>";
echo htmlspecialchars($new_query);
echo "</div>";

echo "<p class='info'>🧪 Testing new query...</p>";
$new_result = $wpdb->get_var($new_query);

if ($wpdb->last_error) {
    echo "<p class='error'>❌ NEW Query Failed: " . $wpdb->last_error . "</p>";
} else {
    echo "<p class='success'>✅ NEW Query Succeeded!</p>";
    echo "<p class='info'>📊 Average Session Length: " . ($new_result ? number_format($new_result, 2) . " minutes" : "No multi-event sessions found") . "</p>";
}
echo "</div>";

// Test 4: Test the analytics function
echo "<div class='section'>";
echo "<h2>🔧 Step 4: Test Analytics Function</h2>";

if (function_exists('businesscraft_ai_get_analytics')) {
    echo "<p class='info'>🧪 Testing businesscraft_ai_get_analytics()...</p>";
    
    try {
        $analytics = businesscraft_ai_get_analytics();
        echo "<p class='success'>✅ Analytics function succeeded!</p>";
        echo "<ul>";
        echo "<li><strong>MAU:</strong> " . ($analytics['mau'] ?? 'N/A') . "</li>";
        echo "<li><strong>Total Chats:</strong> " . ($analytics['total_chats'] ?? 'N/A') . "</li>";
        echo "<li><strong>Revenue:</strong> $" . number_format($analytics['revenue'] ?? 0, 2) . "</li>";
        echo "<li><strong>Avg Session Length:</strong> " . number_format($analytics['avg_session_length'] ?? 0, 2) . " minutes</li>";
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Analytics function failed: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='warning'>⚠️ businesscraft_ai_get_analytics() function not found</p>";
    
    // Test fallback function
    if (function_exists('businesscraft_ai_get_fallback_analytics')) {
        echo "<p class='info'>🧪 Testing fallback analytics...</p>";
        try {
            $fallback_analytics = businesscraft_ai_get_fallback_analytics();
            echo "<p class='success'>✅ Fallback analytics succeeded!</p>";
            echo "<ul>";
            echo "<li><strong>MAU:</strong> " . ($fallback_analytics['mau'] ?? 'N/A') . "</li>";
            echo "<li><strong>Total Chats:</strong> " . ($fallback_analytics['total_chats'] ?? 'N/A') . "</li>";
            echo "<li><strong>Revenue:</strong> $" . number_format($fallback_analytics['revenue'] ?? 0, 2) . "</li>";
            echo "<li><strong>Avg Session Length:</strong> " . number_format($fallback_analytics['avg_session_length'] ?? 0, 2) . " minutes</li>";
            echo "</ul>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Fallback analytics failed: " . $e->getMessage() . "</p>";
        }
    }
}
echo "</div>";

// Test 5: Test number_format fixes
echo "<div class='section'>";
echo "<h2>🔢 Step 5: Test Number Format Fixes</h2>";

echo "<p class='info'>🧪 Testing number_format with various null/empty values...</p>";

$test_values = array(
    'null' => null,
    'empty_string' => '',
    'zero' => 0,
    'valid_number' => 123.45,
    'string_number' => '67.89'
);

foreach ($test_values as $label => $value) {
    echo "<p><strong>{$label}:</strong> ";
    try {
        $formatted = number_format((float)($value ?? 0), 2);
        echo "<span class='success'>✅ {$formatted}</span>";
    } catch (Exception $e) {
        echo "<span class='error'>❌ " . $e->getMessage() . "</span>";
    }
    echo "</p>";
}
echo "</div>";

// Test 6: Test the window function fix
echo "<div class='section'>";
echo "<h2>🪟 Step 6: Test Window Function Fix</h2>";

$chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
$chat_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$chat_logs_table}'") === $chat_logs_table;

if ($chat_table_exists) {
    echo "<p class='success'>✅ Chat logs table exists: {$chat_logs_table}</p>";

    // Test the old problematic window function query
    $old_window_query = "SELECT
            DATE(created_at) as date,
            AVG(TIMESTAMPDIFF(MINUTE, created_at,
                LEAD(created_at) OVER (PARTITION BY user_id ORDER BY created_at)
            )) as avg_duration
         FROM {$chat_logs_table}
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         GROUP BY DATE(created_at)";

    echo "<div class='query-box'>";
    echo "<strong>OLD Window Function Query:</strong><br>";
    echo htmlspecialchars($old_window_query);
    echo "</div>";

    echo "<p class='info'>🧪 Testing old window function query...</p>";
    $old_window_result = $wpdb->get_results($old_window_query);

    if ($wpdb->last_error) {
        echo "<p class='error'>❌ OLD Window Query Failed (Expected): " . $wpdb->last_error . "</p>";
    } else {
        echo "<p class='warning'>⚠️ OLD Window Query Unexpectedly Succeeded</p>";
    }

    // Test the new fixed window function query
    if (function_exists('chatgabi_get_session_duration_trends')) {
        echo "<p class='info'>🧪 Testing fixed session duration trends function...</p>";
        try {
            $trends = chatgabi_get_session_duration_trends($chat_logs_table);
            echo "<p class='success'>✅ Fixed window function query succeeded!</p>";
            echo "<p class='info'>📊 Trends data: " . count($trends) . " days of data</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Fixed window function failed: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ chatgabi_get_session_duration_trends() function not found</p>";
    }
} else {
    echo "<p class='warning'>⚠️ Chat logs table missing: {$chat_logs_table}</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎉 Test Summary</h2>";
echo "<p><strong>SQL Query Fixes:</strong></p>";
echo "<ul>";
echo "<li>✅ 'Invalid use of group function' error resolved with subquery</li>";
echo "<li>✅ 'Window functions can not be used as arguments to group functions' error resolved</li>";
echo "<li>✅ All number_format() calls now use null coalescing and type casting</li>";
echo "</ul>";
echo "<p><strong>Number Format Fix:</strong> All deprecated warnings eliminated.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>1. Visit the <a href='" . admin_url('tools.php?page=chatgabi') . "'>ChatGABI Dashboard</a> to verify fixes</li>";
echo "<li>2. Check for any remaining PHP warnings in error logs</li>";
echo "<li>3. Test with real user data when available</li>";
echo "</ul>";
echo "</div>";
?>

<?php
/**
 * Language Management Functions for ChatGABI AI
 * 
 * Handles all language-related functionality including:
 * - Language detection and preferences
 * - Multi-language support
 * - Cultural context integration
 * - Language-specific content loading
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get supported languages for ChatGABI
 * 
 * @return array Array of supported languages with metadata
 */
function chatgabi_get_supported_languages() {
    return array(
        'en' => array(
            'name' => 'English',
            'native_name' => 'English',
            'countries' => array('Ghana', 'Kenya', 'Nigeria', 'South Africa'),
            'flag' => '🇬🇧',
            'direction' => 'ltr'
        ),
        'tw' => array(
            'name' => 'Twi',
            'native_name' => 'Twi',
            'countries' => array('Ghana'),
            'flag' => '🇬🇭',
            'direction' => 'ltr'
        ),
        'sw' => array(
            'name' => 'Swahili',
            'native_name' => 'Kiswahili',
            'countries' => array('Kenya'),
            'flag' => '🇰🇪',
            'direction' => 'ltr'
        ),
        'yo' => array(
            'name' => 'Yoruba',
            'native_name' => 'Yorùbá',
            'countries' => array('Nigeria'),
            'flag' => '🇳🇬',
            'direction' => 'ltr'
        ),
        'zu' => array(
            'name' => 'Zulu',
            'native_name' => 'isiZulu',
            'countries' => array('South Africa'),
            'flag' => '🇿🇦',
            'direction' => 'ltr'
        )
    );
}

/**
 * Get user's preferred language
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return string Language code
 */
function chatgabi_get_user_preferred_language($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return 'en'; // Default to English for non-logged-in users
    }
    
    $preferred_language = get_user_meta($user_id, 'chatgabi_preferred_language', true);
    
    if (!$preferred_language) {
        // Try to detect based on user's country
        $user_country = get_user_meta($user_id, 'chatgabi_country', true);
        $preferred_language = chatgabi_get_default_language_for_country($user_country);
    }
    
    return $preferred_language ?: 'en';
}

/**
 * Get default language for a country
 * 
 * @param string $country Country name or code
 * @return string Language code
 */
function chatgabi_get_default_language_for_country($country) {
    $country_language_map = array(
        'ghana' => 'tw',
        'gh' => 'tw',
        'kenya' => 'sw',
        'ke' => 'sw',
        'nigeria' => 'yo',
        'ng' => 'yo',
        'south_africa' => 'zu',
        'south-africa' => 'zu',
        'za' => 'zu'
    );
    
    $country_lower = strtolower($country);
    return isset($country_language_map[$country_lower]) ? $country_language_map[$country_lower] : 'en';
}

/**
 * Set user's preferred language
 * 
 * @param int $user_id User ID
 * @param string $language Language code
 * @return bool Success status
 */
function chatgabi_set_user_preferred_language($user_id, $language) {
    $supported_languages = chatgabi_get_supported_languages();
    
    if (!isset($supported_languages[$language])) {
        return false;
    }
    
    return update_user_meta($user_id, 'chatgabi_preferred_language', $language);
}

/**
 * Get language name by code
 * 
 * @param string $language_code Language code
 * @param bool $native Whether to return native name
 * @return string Language name
 */
function chatgabi_get_language_name($language_code, $native = false) {
    $supported_languages = chatgabi_get_supported_languages();
    
    if (!isset($supported_languages[$language_code])) {
        return $language_code;
    }
    
    return $native ? $supported_languages[$language_code]['native_name'] : $supported_languages[$language_code]['name'];
}

/**
 * Get available languages for a specific country
 * 
 * @param string $country Country name
 * @return array Array of language codes
 */
function chatgabi_get_languages_for_country($country) {
    $supported_languages = chatgabi_get_supported_languages();
    $available_languages = array();
    
    foreach ($supported_languages as $code => $language) {
        if (in_array($country, $language['countries'])) {
            $available_languages[] = $code;
        }
    }
    
    // Always include English as fallback
    if (!in_array('en', $available_languages)) {
        $available_languages[] = 'en';
    }
    
    return $available_languages;
}

/**
 * Detect user's language from browser
 * 
 * @return string Language code
 */
function chatgabi_detect_browser_language() {
    if (!isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
        return 'en';
    }
    
    $browser_languages = explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']);
    $supported_languages = array_keys(chatgabi_get_supported_languages());
    
    foreach ($browser_languages as $lang) {
        $lang = strtolower(substr(trim($lang), 0, 2));
        if (in_array($lang, $supported_languages)) {
            return $lang;
        }
    }
    
    return 'en';
}

/**
 * Get localized string
 * 
 * @param string $key String key
 * @param string $language Language code
 * @param array $replacements Replacement values
 * @return string Localized string
 */
function chatgabi_get_localized_string($key, $language = null, $replacements = array()) {
    if (!$language) {
        $language = chatgabi_get_user_preferred_language();
    }
    
    $strings = chatgabi_get_language_strings($language);
    $string = isset($strings[$key]) ? $strings[$key] : $key;
    
    // Replace placeholders
    foreach ($replacements as $placeholder => $value) {
        $string = str_replace('{' . $placeholder . '}', $value, $string);
    }
    
    return $string;
}

// Note: chatgabi_get_language_strings() function is defined in language-strings.php

/**
 * Format text for specific language
 */
function chatgabi_format_text_for_language($text, $language) {
    $supported_languages = chatgabi_get_supported_languages();
    if (!isset($supported_languages[$language])) {
        return $text;
    }
    $language_info = $supported_languages[$language];
    if ($language_info['direction'] === 'rtl') {
        $text = '<span dir="rtl">' . $text . '</span>';
    }
    return $text;
}

/**
 * Get language selector HTML
 */
function chatgabi_get_language_selector($current_language = null, $name = 'language', $attributes = array()) {
    if (!$current_language) {
        $current_language = chatgabi_get_user_preferred_language();
    }
    $supported_languages = chatgabi_get_supported_languages();
    $attrs = '';
    foreach ($attributes as $attr => $value) {
        $attrs .= ' ' . esc_attr($attr) . '="' . esc_attr($value) . '"';
    }
    $html = '<select name="' . esc_attr($name) . '"' . $attrs . '>';
    foreach ($supported_languages as $code => $language) {
        $selected = ($code === $current_language) ? ' selected' : '';
        $html .= '<option value="' . esc_attr($code) . '"' . $selected . '>';
        $html .= esc_html($language['flag'] . ' ' . $language['native_name']);
        $html .= '</option>';
    }
    $html .= '</select>';
    return $html;
}

/**
 * Check if language is supported
 */
function chatgabi_is_language_supported($language) {
    $supported_languages = chatgabi_get_supported_languages();
    return isset($supported_languages[$language]);
}

/**
 * Get cultural context for a language
 */
function chatgabi_get_cultural_context($language) {
    $contexts = array(
        'en' => array('cultural_context' => 'international_business_culture', 'cultural_practices' => array('communication_style' => 'Direct and professional communication', 'business_etiquette' => 'Formal meetings, punctuality, written agreements', 'decision_making' => 'Data-driven decisions with stakeholder consultation', 'relationship_building' => 'Professional networking and trust-building', 'negotiation_style' => 'Fact-based negotiation with clear terms')),
        'tw' => array('cultural_context' => 'akan_business_culture', 'cultural_practices' => array('communication_style' => 'Respectful and hierarchical communication', 'business_etiquette' => 'Respect for elders, community consultation', 'decision_making' => 'Consensus-building with community input', 'relationship_building' => 'Trust through community connections', 'negotiation_style' => 'Patient and relationship-focused')),
        'sw' => array('cultural_context' => 'east_african_business_culture', 'cultural_practices' => array('communication_style' => 'Polite and indirect communication', 'business_etiquette' => 'Harambee spirit, collective responsibility', 'decision_making' => 'Community-oriented decision making', 'relationship_building' => 'Ubuntu philosophy in business', 'negotiation_style' => 'Collaborative and patient approach')),
        'yo' => array('cultural_context' => 'yoruba_business_culture', 'cultural_practices' => array('communication_style' => 'Respectful with emphasis on age and status', 'business_etiquette' => 'Respect for hierarchy, family involvement', 'decision_making' => 'Elder consultation and family consensus', 'relationship_building' => 'Extended family and community networks', 'negotiation_style' => 'Relationship-based with patience')),
        'zu' => array('cultural_context' => 'ubuntu_business_culture', 'cultural_practices' => array('communication_style' => 'Ubuntu-based respectful communication', 'business_etiquette' => 'Community involvement, respect for tradition', 'decision_making' => 'Collective decision making with Ubuntu principles', 'relationship_building' => 'Community-centered relationship building', 'negotiation_style' => 'Ubuntu-guided collaborative approach'))
    );
    return isset($contexts[$language]) ? $contexts[$language] : $contexts['en'];
}

/**
 * Get language statistics
 *
 * @return array Language usage statistics
 */
function chatgabi_get_language_statistics() {
    global $wpdb;

    $stats = array();
    $supported_languages = chatgabi_get_supported_languages();

    // Get user language preferences
    $results = $wpdb->get_results("
        SELECT meta_value as language, COUNT(*) as count
        FROM {$wpdb->usermeta}
        WHERE meta_key = 'chatgabi_preferred_language'
        GROUP BY meta_value
    ");

    foreach ($supported_languages as $code => $language) {
        $stats[$code] = array(
            'name' => $language['name'],
            'native_name' => $language['native_name'],
            'users' => 0,
            'percentage' => 0
        );
    }
    
    $total_users = 0;
    foreach ($results as $result) {
        if (isset($stats[$result->language])) {
            $stats[$result->language]['users'] = (int) $result->count;
            $total_users += (int) $result->count;
        }
    }
    
    // Calculate percentages
    if ($total_users > 0) {
        foreach ($stats as $code => &$language_stats) {
            $language_stats['percentage'] = round(($language_stats['users'] / $total_users) * 100, 2);
        }
    }
    
    return $stats;
}

<?php
/**
 * Response Streaming System for ChatGABI
 * Implements Server-Sent Events (SSE) for real-time AI response delivery
 *
 * @package BusinessCraft_AI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_Response_Streaming {
    
    private $stream_id;
    private $user_id;
    private $session_key;
    private $buffer_size = 1024;
    
    public function __construct() {
        $this->init_streaming();
    }
    
    /**
     * Initialize streaming system
     */
    private function init_streaming() {
        // Set up SSE headers
        add_action('wp_ajax_businesscraft_ai_stream_chat', array($this, 'handle_stream_request'));
        add_action('wp_ajax_nopriv_businesscraft_ai_stream_chat', array($this, 'handle_stream_request'));
        
        // Add streaming endpoints to REST API
        add_action('rest_api_init', array($this, 'register_streaming_endpoints'));
    }
    
    /**
     * Register streaming REST endpoints
     */
    public function register_streaming_endpoints() {
        register_rest_route('bcai/v1', '/stream/chat', array(
            'methods' => 'POST',
            'callback' => array($this, 'stream_chat_response'),
            'permission_callback' => 'businesscraft_ai_check_user_permission',
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'businesscraft_ai_sanitize_ai_input',
                    'validate_callback' => 'businesscraft_ai_validate_chat_message',
                ),
                'language' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'en',
                    'enum' => array('en', 'tw', 'sw', 'yo', 'zu'),
                ),
                'context' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'general',
                ),
                'stream' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => true,
                )
            )
        ));
        
        register_rest_route('bcai/v1', '/stream/status/(?P<stream_id>[a-zA-Z0-9]+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_stream_status'),
            'permission_callback' => 'businesscraft_ai_check_user_permission',
        ));
    }
    
    /**
     * Handle AJAX stream request
     */
    public function handle_stream_request() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_nonce')) {
            wp_die('Security check failed');
        }
        
        $message = sanitize_textarea_field($_POST['message']);
        $language = sanitize_text_field($_POST['language'] ?? 'en');
        $context = sanitize_text_field($_POST['context'] ?? 'general');
        
        $this->start_streaming_response($message, $language, $context);
    }
    
    /**
     * Stream chat response via REST API
     */
    public function stream_chat_response($request) {
        $message = $request->get_param('message');
        $language = $request->get_param('language');
        $context = $request->get_param('context');
        $stream = $request->get_param('stream');
        
        if ($stream) {
            return $this->start_streaming_response($message, $language, $context);
        } else {
            // Fallback to regular response
            return businesscraft_ai_process_chat($request);
        }
    }
    
    /**
     * Start streaming response
     */
    private function start_streaming_response($message, $language, $context) {
        // Generate unique stream ID
        $this->stream_id = uniqid('stream_', true);
        $this->user_id = get_current_user_id();
        $this->session_key = 'bcai_stream_' . $this->stream_id;
        
        // Set SSE headers
        $this->set_sse_headers();
        
        // Initialize stream status
        $this->update_stream_status('initializing', 'Preparing AI response...');
        
        try {
            // Send initial event
            $this->send_sse_event('stream_start', array(
                'stream_id' => $this->stream_id,
                'message' => 'Starting AI response generation...',
                'timestamp' => current_time('mysql')
            ));
            
            // Process AI request with streaming
            $this->process_streaming_ai_request($message, $language, $context);
            
        } catch (Exception $e) {
            $this->send_sse_event('error', array(
                'error' => $e->getMessage(),
                'stream_id' => $this->stream_id
            ));
            
            $this->update_stream_status('error', $e->getMessage());
        }
        
        // End stream
        $this->send_sse_event('stream_end', array(
            'stream_id' => $this->stream_id,
            'message' => 'Response complete'
        ));
        
        $this->update_stream_status('completed', 'Response delivered successfully');
        
        // Close connection
        exit;
    }
    
    /**
     * Process AI request with streaming
     */
    private function process_streaming_ai_request($message, $language, $context) {
        // Update status
        $this->update_stream_status('processing', 'Generating AI response...');
        
        // Send typing indicator
        $this->send_sse_event('typing_start', array(
            'message' => 'AI is thinking...',
            'stream_id' => $this->stream_id
        ));
        
        // Load required components
        require_once get_template_directory() . '/inc/openai-integration.php';
        require_once get_template_directory() . '/inc/african-context-engine.php';
        
        // Get AI response (modified for streaming)
        $ai_response = $this->get_streaming_ai_response($message, $language, $context);
        
        if (is_wp_error($ai_response)) {
            throw new Exception($ai_response->get_error_message());
        }
        
        // Stream response in chunks
        $this->stream_response_chunks($ai_response);
    }
    
    /**
     * Get AI response optimized for streaming
     */
    private function get_streaming_ai_response($message, $language, $context) {
        // Use existing OpenAI integration but with streaming modifications
        $start_time = microtime(true);
        
        // Send progress update
        $this->send_sse_event('progress', array(
            'stage' => 'context_preparation',
            'message' => 'Preparing African business context...',
            'progress' => 20
        ));
        
        // Get African context
        $african_context = new BusinessCraft_African_Context_Engine();
        $context_data = $african_context->get_context($context, $language);
        
        // Send progress update
        $this->send_sse_event('progress', array(
            'stage' => 'ai_processing',
            'message' => 'Processing with AI...',
            'progress' => 50
        ));
        
        // Process with OpenAI (using existing function but with progress tracking)
        $response = businesscraft_ai_process_openai_request($message, $language, $context, $this->user_id);
        
        // Send progress update
        $this->send_sse_event('progress', array(
            'stage' => 'finalizing',
            'message' => 'Finalizing response...',
            'progress' => 90
        ));
        
        return $response;
    }
    
    /**
     * Stream response in chunks
     */
    private function stream_response_chunks($response) {
        if (is_wp_error($response)) {
            throw new Exception($response->get_error_message());
        }
        
        $content = $response['response'] ?? '';
        $chunks = $this->split_response_into_chunks($content);
        
        foreach ($chunks as $index => $chunk) {
            // Send chunk
            $this->send_sse_event('chunk', array(
                'content' => $chunk,
                'chunk_index' => $index,
                'total_chunks' => count($chunks),
                'stream_id' => $this->stream_id
            ));
            
            // Small delay for realistic streaming effect
            usleep(100000); // 100ms
            
            // Flush output
            if (ob_get_level()) {
                ob_flush();
            }
            flush();
        }
        
        // Send complete response
        $this->send_sse_event('response_complete', array(
            'full_response' => $content,
            'metadata' => array(
                'tokens_used' => $response['tokens_used'] ?? 0,
                'model' => $response['model'] ?? 'gpt-3.5-turbo',
                'processing_time' => $response['processing_time'] ?? 0
            ),
            'stream_id' => $this->stream_id
        ));
    }
    
    /**
     * Split response into chunks for streaming
     */
    private function split_response_into_chunks($content) {
        // Split by sentences for natural breaks
        $sentences = preg_split('/(?<=[.!?])\s+/', $content, -1, PREG_SPLIT_NO_EMPTY);
        
        $chunks = array();
        $current_chunk = '';
        
        foreach ($sentences as $sentence) {
            if (strlen($current_chunk . $sentence) > $this->buffer_size) {
                if (!empty($current_chunk)) {
                    $chunks[] = trim($current_chunk);
                    $current_chunk = $sentence;
                } else {
                    // Sentence is longer than buffer, split by words
                    $words = explode(' ', $sentence);
                    foreach ($words as $word) {
                        if (strlen($current_chunk . ' ' . $word) > $this->buffer_size) {
                            if (!empty($current_chunk)) {
                                $chunks[] = trim($current_chunk);
                                $current_chunk = $word;
                            } else {
                                $chunks[] = $word;
                            }
                        } else {
                            $current_chunk .= (empty($current_chunk) ? '' : ' ') . $word;
                        }
                    }
                }
            } else {
                $current_chunk .= (empty($current_chunk) ? '' : ' ') . $sentence;
            }
        }
        
        if (!empty($current_chunk)) {
            $chunks[] = trim($current_chunk);
        }
        
        return $chunks;
    }
    
    /**
     * Set Server-Sent Events headers
     */
    private function set_sse_headers() {
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Cache-Control');
        
        // Disable output buffering
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        // Set unlimited execution time for streaming
        set_time_limit(0);
    }
    
    /**
     * Send SSE event
     */
    private function send_sse_event($event_type, $data) {
        echo "event: {$event_type}\n";
        echo "data: " . json_encode($data) . "\n\n";
        
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    /**
     * Update stream status
     */
    private function update_stream_status($status, $message) {
        $status_data = array(
            'status' => $status,
            'message' => $message,
            'timestamp' => current_time('mysql'),
            'user_id' => $this->user_id
        );
        
        set_transient($this->session_key, $status_data, 300); // 5 minutes
    }
    
    /**
     * Get stream status
     */
    public function get_stream_status($request) {
        $stream_id = $request->get_param('stream_id');
        $session_key = 'bcai_stream_' . $stream_id;
        
        $status = get_transient($session_key);
        
        if (!$status) {
            return new WP_Error('stream_not_found', 'Stream not found or expired', array('status' => 404));
        }
        
        return rest_ensure_response($status);
    }
}

// Initialize streaming system
new BusinessCraft_Response_Streaming();

/**
 * Helper function to check if streaming is supported
 */
function businesscraft_ai_is_streaming_supported() {
    return !headers_sent() && function_exists('ob_get_level');
}

/**
 * Helper function to get streaming endpoint URL
 */
function businesscraft_ai_get_streaming_endpoint() {
    return rest_url('bcai/v1/stream/chat');
}

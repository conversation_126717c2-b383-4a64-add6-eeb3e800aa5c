# ChatGABI Opportunities REST API Documentation

## Overview

The ChatGABI Opportunities REST API provides programmatic access to business opportunities data across Ghana, Kenya, Nigeria, and South Africa. This API enables external applications, mobile apps, AI agents, and third-party integrations to dynamically fetch filtered business opportunities.

**Base URL**: `http://your-domain.com/wp-json/chatgabi/v1/`

## Authentication

Currently, all endpoints are publicly accessible (no authentication required). This may change in future versions.

## Rate Limiting

No rate limiting is currently implemented, but it's recommended to cache responses and avoid excessive requests.

## Caching

All endpoints implement intelligent caching:
- **Opportunities**: 5 minutes
- **Statistics**: 10 minutes  
- **Types/Sectors**: 30 minutes
- **Countries**: 1 hour

Cache status is indicated in the `X-ChatGABI-Cache` header (`HIT` or `MISS`).

## Endpoints

### 1. Get Opportunities

**GET** `/opportunities`

Retrieve filtered business opportunities with pagination support.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `country` | string | No | Filter by country (Ghana, Kenya, Nigeria, South Africa) |
| `sector` | string | No | Filter by business sector |
| `type` | string | No | Filter by opportunity type (Grant, Incubator, Loan, etc.) |
| `search` | string | No | Search in title and summary |
| `sort` | string | No | Sort order: `soonest`, `latest`, `alphabetical` (default: `soonest`) |
| `limit` | integer | No | Max results (1-50, default: 10) |
| `offset` | integer | No | Skip results for pagination (default: 0) |

#### Example Request

```bash
GET /chatgabi/v1/opportunities?country=Ghana&type=Grant&limit=5&sort=soonest
```

#### Example Response

```json
{
  "opportunities": [
    {
      "id": "ghana_startup_grant_2024",
      "title": "Ghana Startup Innovation Grant",
      "summary": "Funding for tech startups in Ghana",
      "type": "Grant",
      "sector": "Technology",
      "amount": "GHS 50,000",
      "deadline": "2024-03-15",
      "description": "Full opportunity description...",
      "eligibility": ["Ghanaian citizens", "Tech startups"],
      "application_url": "https://example.com/apply"
    }
  ],
  "pagination": {
    "total": 15,
    "count": 5,
    "limit": 5,
    "offset": 0,
    "has_more": true
  },
  "filters_applied": {
    "country": "Ghana",
    "type": "Grant",
    "sort": "soonest"
  },
  "timestamp": "2024-01-15T10:30:00+00:00"
}
```

### 2. Get Statistics

**GET** `/opportunities/stats`

Get comprehensive statistics about available opportunities.

#### Example Response

```json
{
  "total_opportunities": 40,
  "by_country": {
    "Ghana": 10,
    "Kenya": 12,
    "Nigeria": 8,
    "South Africa": 10
  },
  "by_type": {
    "Grant": 15,
    "Incubator": 8,
    "Loan": 12,
    "Tax Relief": 5
  },
  "by_sector": {
    "Technology": 18,
    "Agriculture": 10,
    "Healthcare": 8,
    "Education": 4
  },
  "upcoming_deadlines": [
    {
      "title": "Tech Innovation Grant",
      "deadline": "2024-02-15",
      "country": "Ghana",
      "type": "Grant"
    }
  ],
  "last_updated": "2024-01-15T10:30:00+00:00"
}
```

### 3. Get Opportunity Types

**GET** `/opportunities/types`

Get all available opportunity types.

#### Example Response

```json
{
  "types": [
    "Accelerator",
    "Grant",
    "Incubator",
    "Loan",
    "Tax Relief"
  ],
  "count": 5,
  "last_updated": "2024-01-15T10:30:00+00:00"
}
```

### 4. Get Sectors

**GET** `/opportunities/sectors`

Get all available business sectors, optionally filtered by country.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `country` | string | No | Filter sectors by country |

#### Example Response

```json
{
  "sectors": [
    "Agriculture",
    "Education",
    "Healthcare",
    "Technology"
  ],
  "count": 4,
  "filtered_by_country": "Ghana",
  "last_updated": "2024-01-15T10:30:00+00:00"
}
```

### 5. Get Countries

**GET** `/opportunities/countries`

Get all available countries with opportunity counts.

#### Example Response

```json
{
  "countries": [
    {
      "name": "Ghana",
      "opportunity_count": 10,
      "slug": "ghana"
    },
    {
      "name": "Kenya", 
      "opportunity_count": 12,
      "slug": "kenya"
    }
  ],
  "count": 4,
  "last_updated": "2024-01-15T10:30:00+00:00"
}
```

### 6. Get Single Opportunity

**GET** `/opportunities/{id}`

Get detailed information about a specific opportunity.

#### Example Response

```json
{
  "opportunity": {
    "id": "ghana_startup_grant_2024",
    "title": "Ghana Startup Innovation Grant",
    "summary": "Funding for tech startups in Ghana",
    "type": "Grant",
    "sector": "Technology",
    "amount": "GHS 50,000",
    "deadline": "2024-03-15",
    "description": "Full detailed description...",
    "eligibility": ["Ghanaian citizens", "Tech startups"],
    "application_url": "https://example.com/apply",
    "country": "Ghana"
  },
  "last_updated": "2024-01-15T10:30:00+00:00"
}
```

### 7. Health Check

**GET** `/opportunities/health`

Check API health and get endpoint information.

#### Example Response

```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-15T10:30:00+00:00",
  "endpoints": {
    "opportunities": "/chatgabi/v1/opportunities",
    "stats": "/chatgabi/v1/opportunities/stats",
    "types": "/chatgabi/v1/opportunities/types",
    "sectors": "/chatgabi/v1/opportunities/sectors",
    "countries": "/chatgabi/v1/opportunities/countries",
    "single": "/chatgabi/v1/opportunities/{id}"
  },
  "cache_status": {
    "enabled": true,
    "default_ttl": "5-30 minutes"
  },
  "data_sources": {
    "countries": ["Ghana", "Kenya", "Nigeria", "South Africa"],
    "total_opportunities": 40
  }
}
```

## Error Handling

All endpoints return standard HTTP status codes:

- **200**: Success
- **400**: Bad Request (invalid parameters)
- **404**: Not Found (for single opportunity endpoint)
- **500**: Internal Server Error

Error responses follow this format:

```json
{
  "code": "chatgabi_api_error",
  "message": "Error description",
  "data": {
    "status": 500
  }
}
```

## Integration Examples

### JavaScript/Frontend

```javascript
// Fetch opportunities for Ghana
const response = await fetch('/wp-json/chatgabi/v1/opportunities?country=Ghana&limit=5');
const data = await response.json();
console.log(data.opportunities);
```

### Python

```python
import requests

# Get statistics
response = requests.get('http://your-domain.com/wp-json/chatgabi/v1/opportunities/stats')
stats = response.json()
print(f"Total opportunities: {stats['total_opportunities']}")
```

### cURL

```bash
# Search for grants
curl "http://your-domain.com/wp-json/chatgabi/v1/opportunities?type=Grant&search=tech"
```

## Use Cases

1. **Mobile Apps**: Fetch opportunities based on user location and preferences
2. **AI Chatbots**: Provide contextual opportunity recommendations
3. **Email Digests**: Generate weekly opportunity summaries
4. **Dashboard Widgets**: Display live opportunity feeds
5. **Third-party Integrations**: Sync opportunities with external systems

## Performance Tips

1. **Use Caching**: Responses are cached, so repeated requests are fast
2. **Pagination**: Use `limit` and `offset` for large datasets
3. **Specific Filters**: Use country/sector filters to reduce response size
4. **Health Checks**: Monitor API status with the health endpoint

## Support

For API support and questions, contact the ChatGABI development team.

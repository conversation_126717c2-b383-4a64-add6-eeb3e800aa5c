<?php
/**
 * ChatGABI WhatsApp Admin Menu Diagnostic Test
 * 
 * Tests WhatsApp admin menu registration and accessibility
 * Access: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-whatsapp-admin-menu.php
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Ensure we're in the correct theme context
if (get_template() !== 'businesscraft-ai') {
    die('Error: This test must be run with the businesscraft-ai theme active.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI WhatsApp Admin Menu Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 30px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 6px; border-left: 4px solid #0073aa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f1f1f1; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .menu-link { background: #0073aa; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
        .menu-link:hover { background: #005a87; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 ChatGABI WhatsApp Admin Menu Diagnostic</h1>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>Theme:</strong> <?php echo get_template(); ?></p>

        <?php
        $test_results = array();
        $total_tests = 0;
        $passed_tests = 0;

        // Test 1: Check if admin-whatsapp.php file exists
        echo '<div class="test-section">';
        echo '<h2>📁 Test 1: File Existence</h2>';
        
        $admin_whatsapp_file = get_template_directory() . '/inc/admin-whatsapp.php';
        
        if (file_exists($admin_whatsapp_file)) {
            echo '<p class="success">✅ admin-whatsapp.php file exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ admin-whatsapp.php file missing</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 2: Check if functions are loaded
        echo '<div class="test-section">';
        echo '<h2>🔧 Test 2: Function Availability</h2>';
        
        $required_functions = array(
            'chatgabi_init_whatsapp_admin',
            'chatgabi_add_whatsapp_admin_menu',
            'chatgabi_whatsapp_admin_page',
            'chatgabi_register_whatsapp_settings'
        );
        
        foreach ($required_functions as $function) {
            if (function_exists($function)) {
                echo "<p class=\"success\">✅ Function exists: $function</p>";
                $passed_tests++;
            } else {
                echo "<p class=\"error\">❌ Function missing: $function</p>";
            }
            $total_tests++;
        }
        echo '</div>';

        // Test 3: Check WordPress admin menu globals
        echo '<div class="test-section">';
        echo '<h2>🌐 Test 3: WordPress Admin Menu System</h2>';
        
        global $menu, $submenu, $admin_page_hooks;
        
        if (is_array($menu)) {
            echo '<p class="success">✅ WordPress $menu global is available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WordPress $menu global not available</p>';
        }
        $total_tests++;
        
        if (is_array($submenu)) {
            echo '<p class="success">✅ WordPress $submenu global is available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WordPress $submenu global not available</p>';
        }
        $total_tests++;
        
        if (function_exists('add_submenu_page')) {
            echo '<p class="success">✅ add_submenu_page() function available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ add_submenu_page() function not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 4: Check main ChatGABI menu registration
        echo '<div class="test-section">';
        echo '<h2>📋 Test 4: Main ChatGABI Menu</h2>';
        
        // Trigger admin menu registration
        if (function_exists('chatgabi_add_admin_menu')) {
            echo '<p class="info">🔄 Triggering main menu registration...</p>';
            do_action('admin_menu');
            
            // Check if main menu exists
            $main_menu_found = false;
            if (is_array($menu)) {
                foreach ($menu as $menu_item) {
                    if (is_array($menu_item) && isset($menu_item[2]) && $menu_item[2] === 'chatgabi-main') {
                        $main_menu_found = true;
                        break;
                    }
                }
            }
            
            if ($main_menu_found) {
                echo '<p class="success">✅ Main ChatGABI menu (chatgabi-main) found</p>';
                $passed_tests++;
            } else {
                echo '<p class="error">❌ Main ChatGABI menu (chatgabi-main) not found</p>';
            }
        } else {
            echo '<p class="error">❌ chatgabi_add_admin_menu function not found</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 5: Check WhatsApp submenu registration
        echo '<div class="test-section">';
        echo '<h2>📱 Test 5: WhatsApp Submenu Registration</h2>';
        
        // Check if WhatsApp submenu exists
        $whatsapp_submenu_found = false;
        if (is_array($submenu) && isset($submenu['chatgabi-main'])) {
            foreach ($submenu['chatgabi-main'] as $submenu_item) {
                if (is_array($submenu_item) && isset($submenu_item[2]) && $submenu_item[2] === 'chatgabi-whatsapp') {
                    $whatsapp_submenu_found = true;
                    break;
                }
            }
        }
        
        if ($whatsapp_submenu_found) {
            echo '<p class="success">✅ WhatsApp submenu (chatgabi-whatsapp) found under chatgabi-main</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp submenu (chatgabi-whatsapp) not found under chatgabi-main</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 6: Display current menu structure
        echo '<div class="test-section">';
        echo '<h2>🗂️ Test 6: Current Menu Structure</h2>';
        
        echo '<h3>Main Menus:</h3>';
        if (is_array($menu) && !empty($menu)) {
            echo '<table>';
            echo '<tr><th>Position</th><th>Title</th><th>Slug</th><th>Capability</th></tr>';
            foreach ($menu as $position => $menu_item) {
                if (is_array($menu_item) && !empty($menu_item[0])) {
                    echo '<tr>';
                    echo '<td>' . esc_html($position) . '</td>';
                    echo '<td>' . esc_html(strip_tags($menu_item[0])) . '</td>';
                    echo '<td>' . esc_html($menu_item[2] ?? 'N/A') . '</td>';
                    echo '<td>' . esc_html($menu_item[1] ?? 'N/A') . '</td>';
                    echo '</tr>';
                }
            }
            echo '</table>';
        } else {
            echo '<p class="warning">⚠️ No main menus found</p>';
        }
        
        echo '<h3>ChatGABI Submenus:</h3>';
        if (is_array($submenu) && isset($submenu['chatgabi-main'])) {
            echo '<table>';
            echo '<tr><th>Title</th><th>Slug</th><th>Capability</th></tr>';
            foreach ($submenu['chatgabi-main'] as $submenu_item) {
                if (is_array($submenu_item)) {
                    echo '<tr>';
                    echo '<td>' . esc_html(strip_tags($submenu_item[0])) . '</td>';
                    echo '<td>' . esc_html($submenu_item[2] ?? 'N/A') . '</td>';
                    echo '<td>' . esc_html($submenu_item[1] ?? 'N/A') . '</td>';
                    echo '</tr>';
                }
            }
            echo '</table>';
        } else {
            echo '<p class="warning">⚠️ No ChatGABI submenus found</p>';
        }
        echo '</div>';

        // Test 7: Check user permissions
        echo '<div class="test-section">';
        echo '<h2>👤 Test 7: User Permissions</h2>';
        
        if (function_exists('current_user_can')) {
            if (current_user_can('manage_options')) {
                echo '<p class="success">✅ Current user has manage_options capability</p>';
                $passed_tests++;
            } else {
                echo '<p class="warning">⚠️ Current user does NOT have manage_options capability</p>';
            }
        } else {
            echo '<p class="error">❌ current_user_can() function not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 8: Generate admin URLs
        echo '<div class="test-section">';
        echo '<h2>🔗 Test 8: Admin URLs</h2>';
        
        $admin_urls = array(
            'Main ChatGABI Dashboard' => admin_url('admin.php?page=chatgabi-main'),
            'WhatsApp Integration' => admin_url('admin.php?page=chatgabi-whatsapp'),
            'ChatGABI Settings' => admin_url('admin.php?page=chatgabi-settings'),
            'ChatGABI Templates' => admin_url('admin.php?page=chatgabi-templates'),
            'Users & Credits' => admin_url('admin.php?page=chatgabi-users')
        );
        
        echo '<p class="info">📋 Available admin URLs (requires login):</p>';
        foreach ($admin_urls as $title => $url) {
            echo '<a href="' . esc_url($url) . '" class="menu-link" target="_blank">' . esc_html($title) . '</a>';
        }
        echo '</div>';

        // Test Summary
        echo '<div class="test-section">';
        echo '<h2>📊 Test Summary</h2>';
        
        $success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Tests</td><td>$total_tests</td></tr>";
        echo "<tr><td>Passed Tests</td><td class=\"success\">$passed_tests</td></tr>";
        echo "<tr><td>Failed Tests</td><td class=\"error\">" . ($total_tests - $passed_tests) . "</td></tr>";
        echo "<tr><td>Success Rate</td><td class=\"" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error')) . "\">$success_rate%</td></tr>";
        echo "</table>";
        
        if ($success_rate >= 80) {
            echo '<p class="success">🎉 <strong>WhatsApp admin menu should be accessible!</strong></p>';
            echo '<p class="info">📋 <strong>Next Step:</strong> Login to WordPress admin and check ChatGABI → WhatsApp menu</p>';
        } elseif ($success_rate >= 60) {
            echo '<p class="warning">⚠️ <strong>WhatsApp admin menu has some issues but may be partially accessible.</strong></p>';
        } else {
            echo '<p class="error">❌ <strong>WhatsApp admin menu has critical issues that need to be resolved.</strong></p>';
        }
        echo '</div>';

        // Troubleshooting Guide
        echo '<div class="test-section">';
        echo '<h2>🔧 Troubleshooting Guide</h2>';
        echo '<h3>If WhatsApp menu is still not visible:</h3>';
        echo '<ol>';
        echo '<li><strong>Clear WordPress cache</strong> if using any caching plugins</li>';
        echo '<li><strong>Deactivate and reactivate the theme</strong> to refresh menu registration</li>';
        echo '<li><strong>Check WordPress error logs</strong> for any PHP errors</li>';
        echo '<li><strong>Verify user permissions</strong> - ensure you have administrator role</li>';
        echo '<li><strong>Try accessing direct URL:</strong> <code>' . admin_url('admin.php?page=chatgabi-whatsapp') . '</code></li>';
        echo '</ol>';
        
        echo '<h3>Common Issues:</h3>';
        echo '<ul>';
        echo '<li><strong>Parent menu not found:</strong> Ensure main ChatGABI menu is registered first</li>';
        echo '<li><strong>Function conflicts:</strong> Check for duplicate function names</li>';
        echo '<li><strong>Hook timing:</strong> Menu registration happens too late in WordPress load</li>';
        echo '<li><strong>Permission issues:</strong> User lacks manage_options capability</li>';
        echo '</ul>';
        echo '</div>';
        ?>
    </div>
</body>
</html>

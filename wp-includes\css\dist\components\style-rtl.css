@charset "UTF-8";
:root{
  --wp-admin-theme-color:#3858e9;
  --wp-admin-theme-color--rgb:56, 88, 233;
  --wp-admin-theme-color-darker-10:#2145e6;
  --wp-admin-theme-color-darker-10--rgb:33, 69, 230;
  --wp-admin-theme-color-darker-20:#183ad6;
  --wp-admin-theme-color-darker-20--rgb:24, 58, 214;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}

@media not (prefers-reduced-motion){
  .components-animate__appear{
    animation:components-animate__appear-animation .1s cubic-bezier(0, 0, .2, 1) 0s;
    animation-fill-mode:forwards;
  }
}
.components-animate__appear.is-from-top,.components-animate__appear.is-from-top.is-from-left{
  transform-origin:top right;
}
.components-animate__appear.is-from-top.is-from-right{
  transform-origin:top left;
}
.components-animate__appear.is-from-bottom,.components-animate__appear.is-from-bottom.is-from-left{
  transform-origin:bottom right;
}
.components-animate__appear.is-from-bottom.is-from-right{
  transform-origin:bottom left;
}

@keyframes components-animate__appear-animation{
  0%{
    transform:translateY(-2em) scaleY(0) scaleX(0);
  }
  to{
    transform:translateY(0) scaleY(1) scaleX(1);
  }
}
@media not (prefers-reduced-motion){
  .components-animate__slide-in{
    animation:components-animate__slide-in-animation .1s cubic-bezier(0, 0, .2, 1);
    animation-fill-mode:forwards;
  }
  .components-animate__slide-in.is-from-left{
    transform:translateX(-100%);
  }
  .components-animate__slide-in.is-from-right{
    transform:translateX(100%);
  }
}

@keyframes components-animate__slide-in-animation{
  to{
    transform:translateX(0);
  }
}
@media not (prefers-reduced-motion){
  .components-animate__loading{
    animation:components-animate__loading 1.6s ease-in-out infinite;
  }
}

@keyframes components-animate__loading{
  0%{
    opacity:.5;
  }
  50%{
    opacity:1;
  }
  to{
    opacity:.5;
  }
}
.components-autocomplete__popover .components-popover__content{
  min-width:200px;
  padding:8px;
}

.components-autocomplete__result.components-button{
  display:flex;
  height:auto;
  min-height:36px;
  text-align:right;
  width:100%;
}
.components-autocomplete__result.components-button:focus:not(:disabled){
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}

.components-badge{
  align-items:center;
  background-color:color-mix(in srgb, #fff 90%, var(--base-color));
  border-radius:2px;
  box-sizing:border-box;
  color:color-mix(in srgb, #000 50%, var(--base-color));
  display:inline-flex;
  font-size:12px;
  font-weight:400;
  gap:2px;
  line-height:20px;
  max-width:100%;
  min-height:24px;
  padding:0 8px;
}
.components-badge *,.components-badge :after,.components-badge :before{
  box-sizing:inherit;
}
.components-badge:where(.is-default){
  background-color:#f0f0f0;
  color:#2f2f2f;
}
.components-badge.has-icon{
  padding-inline-start:4px;
}
.components-badge.is-info{
  --base-color:#3858e9;
}
.components-badge.is-warning{
  --base-color:#f0b849;
}
.components-badge.is-error{
  --base-color:#cc1818;
}
.components-badge.is-success{
  --base-color:#4ab866;
}

.components-badge__icon{
  flex-shrink:0;
}

.components-badge__content{
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.components-button-group{
  display:inline-block;
}
.components-button-group .components-button{
  border-radius:0;
  box-shadow:inset 0 0 0 1px #1e1e1e;
  color:#1e1e1e;
  display:inline-flex;
}
.components-button-group .components-button+.components-button{
  margin-right:-1px;
}
.components-button-group .components-button:first-child{
  border-radius:0 2px 2px 0;
}
.components-button-group .components-button:last-child{
  border-radius:2px 0 0 2px;
}
.components-button-group .components-button.is-primary,.components-button-group .components-button:focus{
  position:relative;
  z-index:1;
}
.components-button-group .components-button.is-primary{
  box-shadow:inset 0 0 0 1px #1e1e1e;
}
.components-button{
  align-items:center;
  -webkit-appearance:none;
  background:none;
  border:0;
  border-radius:2px;
  box-sizing:border-box;
  color:var(--wp-components-color-foreground, #1e1e1e);
  cursor:pointer;
  display:inline-flex;
  font-family:inherit;
  font-size:13px;
  height:36px;
  margin:0;
  padding:6px 12px;
  text-decoration:none;
}
@media not (prefers-reduced-motion){
  .components-button{
    transition:box-shadow .1s linear;
  }
}
.components-button.is-next-40px-default-size{
  height:40px;
}
.components-button:hover:not(:disabled,[aria-disabled=true]),.components-button[aria-expanded=true]{
  color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-button:focus:not(:disabled){
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:3px solid #0000;
}
.components-button.is-primary{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  color:var(--wp-components-color-accent-inverted, #fff);
  outline:1px solid #0000;
  text-decoration:none;
  text-shadow:none;
  white-space:nowrap;
}
.components-button.is-primary:hover:not(:disabled){
  background:var(--wp-components-color-accent-darker-10, var(--wp-admin-theme-color-darker-10, #2145e6));
  color:var(--wp-components-color-accent-inverted, #fff);
}
.components-button.is-primary:active:not(:disabled){
  background:var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6));
  border-color:var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6));
  color:var(--wp-components-color-accent-inverted, #fff);
}
.components-button.is-primary:focus:not(:disabled){
  box-shadow:inset 0 0 0 1px var(--wp-components-color-background, #fff), 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-button.is-primary:disabled,.components-button.is-primary:disabled:active:enabled,.components-button.is-primary[aria-disabled=true],.components-button.is-primary[aria-disabled=true]:active:enabled,.components-button.is-primary[aria-disabled=true]:enabled{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  border-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  color:#fff6;
  outline:none;
}
.components-button.is-primary:disabled:active:enabled:focus:enabled,.components-button.is-primary:disabled:focus:enabled,.components-button.is-primary[aria-disabled=true]:active:enabled:focus:enabled,.components-button.is-primary[aria-disabled=true]:enabled:focus:enabled,.components-button.is-primary[aria-disabled=true]:focus:enabled{
  box-shadow:inset 0 0 0 1px var(--wp-components-color-background, #fff), 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-button.is-primary.is-busy,.components-button.is-primary.is-busy:disabled,.components-button.is-primary.is-busy[aria-disabled=true]{
  background-image:linear-gradient(45deg, var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)) 33%, var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6)) 33%, var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6)) 70%, var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)) 70%);
  background-size:100px 100%;
  border-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  color:var(--wp-components-color-accent-inverted, #fff);
}
.components-button.is-secondary,.components-button.is-tertiary{
  outline:1px solid #0000;
}
.components-button.is-secondary:active:not(:disabled),.components-button.is-tertiary:active:not(:disabled){
  box-shadow:none;
}
.components-button.is-secondary:disabled,.components-button.is-secondary[aria-disabled=true],.components-button.is-secondary[aria-disabled=true]:hover,.components-button.is-tertiary:disabled,.components-button.is-tertiary[aria-disabled=true],.components-button.is-tertiary[aria-disabled=true]:hover{
  background:#0000;
  color:#949494;
  transform:none;
}
.components-button.is-secondary{
  background:#0000;
  box-shadow:inset 0 0 0 1px var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)), 0 0 0 currentColor;
  color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:1px solid #0000;
  white-space:nowrap;
}
.components-button.is-secondary:hover:not(:disabled,[aria-disabled=true],.is-pressed){
  background:color-mix(in srgb, var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)) 4%, #0000);
  box-shadow:inset 0 0 0 1px var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6));
  color:var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6));
}
.components-button.is-secondary:disabled:not(:focus),.components-button.is-secondary[aria-disabled=true]:hover:not(:focus),.components-button.is-secondary[aria-disabled=true]:not(:focus){
  box-shadow:inset 0 0 0 1px #ddd;
}
.components-button.is-secondary:focus:not(:disabled){
  box-shadow:0 0 0 currentColor inset, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-button.is-tertiary{
  background:#0000;
  color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  white-space:nowrap;
}
.components-button.is-tertiary:hover:not(:disabled,[aria-disabled=true],.is-pressed){
  background:color-mix(in srgb, var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)) 4%, #0000);
  color:var(--wp-components-color-accent-darker-20, var(--wp-admin-theme-color-darker-20, #183ad6));
}
.components-button.is-tertiary:active:not(:disabled,[aria-disabled=true]){
  background:color-mix(in srgb, var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)) 8%, #0000);
}
p+.components-button.is-tertiary{
  margin-right:-6px;
}
.components-button.is-tertiary:disabled:not(:focus),.components-button.is-tertiary[aria-disabled=true]:hover:not(:focus),.components-button.is-tertiary[aria-disabled=true]:not(:focus){
  box-shadow:none;
  outline:none;
}
.components-button.is-destructive{
  --wp-components-color-accent:#cc1818;
  --wp-components-color-accent-darker-10:#9e1313;
  --wp-components-color-accent-darker-20:#710d0d;
}
.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link){
  color:#cc1818;
}
.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):hover:not(:disabled,[aria-disabled=true]){
  color:#710d0d;
}
.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):focus{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #cc1818;
}
.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):active:not(:disabled,[aria-disabled=true]){
  background:#ccc;
}
.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link):disabled,.components-button.is-destructive:not(.is-primary):not(.is-secondary):not(.is-tertiary):not(.is-link)[aria-disabled=true]{
  color:#949494;
}
.components-button.is-destructive.is-secondary:hover:not(:disabled,[aria-disabled=true]),.components-button.is-destructive.is-tertiary:hover:not(:disabled,[aria-disabled=true]){
  background:#cc18180a;
}
.components-button.is-destructive.is-secondary:active:not(:disabled,[aria-disabled=true]),.components-button.is-destructive.is-tertiary:active:not(:disabled,[aria-disabled=true]){
  background:#cc181814;
}
.components-button.is-link{
  background:none;
  border:0;
  border-radius:0;
  box-shadow:none;
  color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  height:auto;
  margin:0;
  outline:none;
  padding:0;
  text-align:right;
  text-decoration:underline;
}
@media not (prefers-reduced-motion){
  .components-button.is-link{
    transition-duration:.05s;
    transition-property:border, background, color;
    transition-timing-function:ease-in-out;
  }
}
.components-button.is-link:focus{
  border-radius:2px;
}
.components-button.is-link:disabled,.components-button.is-link[aria-disabled=true]{
  color:#949494;
}
.components-button:not(:disabled,[aria-disabled=true]):active{
  color:var(--wp-components-color-foreground, #1e1e1e);
}
.components-button:disabled,.components-button[aria-disabled=true]{
  color:#949494;
  cursor:default;
}
.components-button.is-busy,.components-button.is-secondary.is-busy,.components-button.is-secondary.is-busy:disabled,.components-button.is-secondary.is-busy[aria-disabled=true]{
  background-image:linear-gradient(45deg, #fafafa 33%, #e0e0e0 0, #e0e0e0 70%, #fafafa 0);
  background-size:100px 100%;
}
@media not (prefers-reduced-motion){
  .components-button.is-busy,.components-button.is-secondary.is-busy,.components-button.is-secondary.is-busy:disabled,.components-button.is-secondary.is-busy[aria-disabled=true]{
    animation:components-button__busy-animation 2.5s linear infinite;
  }
}
.components-button.is-compact{
  height:32px;
}
.components-button.is-compact.has-icon:not(.has-text){
  min-width:32px;
  padding:0;
  width:32px;
}
.components-button.is-small{
  font-size:11px;
  height:24px;
  line-height:22px;
  padding:0 8px;
}
.components-button.is-small.has-icon:not(.has-text){
  min-width:24px;
  padding:0;
  width:24px;
}
.components-button.has-icon{
  justify-content:center;
  min-width:36px;
  padding:6px;
}
.components-button.has-icon.is-next-40px-default-size{
  min-width:40px;
}
.components-button.has-icon .dashicon{
  align-items:center;
  box-sizing:initial;
  display:inline-flex;
  justify-content:center;
  padding:2px;
}
.components-button.has-icon.has-text{
  gap:4px;
  justify-content:start;
  padding-left:12px;
  padding-right:8px;
}
.components-button.is-pressed,.components-button.is-pressed:hover{
  color:var(--wp-components-color-foreground-inverted, #fff);
}
.components-button.is-pressed:hover:not(:disabled,[aria-disabled=true]),.components-button.is-pressed:not(:disabled,[aria-disabled=true]){
  background:var(--wp-components-color-foreground, #1e1e1e);
}
.components-button.is-pressed:disabled,.components-button.is-pressed[aria-disabled=true]{
  color:#949494;
}
.components-button.is-pressed:disabled:not(.is-primary):not(.is-secondary):not(.is-tertiary),.components-button.is-pressed[aria-disabled=true]:not(.is-primary):not(.is-secondary):not(.is-tertiary){
  background:#949494;
  color:var(--wp-components-color-foreground-inverted, #fff);
}
.components-button.is-pressed:focus:not(:disabled){
  box-shadow:inset 0 0 0 1px var(--wp-components-color-background, #fff), 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:2px solid #0000;
}
.components-button svg{
  fill:currentColor;
  outline:none;
}
@media (forced-colors:active){
  .components-button svg{
    fill:CanvasText;
  }
}
.components-button .components-visually-hidden{
  height:auto;
}

@keyframes components-button__busy-animation{
  0%{
    background-position:right 200px top 0;
  }
}
.components-checkbox-control{
  --checkbox-input-size:24px;
  --checkbox-input-margin:8px;
}
@media (min-width:600px){
  .components-checkbox-control{
    --checkbox-input-size:16px;
  }
}

.components-checkbox-control__label{
  cursor:pointer;
  line-height:var(--checkbox-input-size);
}

.components-checkbox-control__input[type=checkbox]{
  appearance:none;
  background:#fff;
  border:1px solid #1e1e1e;
  border-radius:2px;
  box-shadow:0 0 0 #0000;
  clear:none;
  color:#1e1e1e;
  cursor:pointer;
  display:inline-block;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  height:var(--checkbox-input-size);
  line-height:normal;
  line-height:0;
  margin:0 0 0 4px;
  outline:0;
  padding:6px 8px;
  padding:0 !important;
  text-align:center;
  transition:none;
  vertical-align:top;
  width:var(--checkbox-input-size);
}
@media not (prefers-reduced-motion){
  .components-checkbox-control__input[type=checkbox]{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .components-checkbox-control__input[type=checkbox]{
    font-size:13px;
    line-height:normal;
  }
}
.components-checkbox-control__input[type=checkbox]:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
}
.components-checkbox-control__input[type=checkbox]::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.components-checkbox-control__input[type=checkbox]::-moz-placeholder{
  color:#1e1e1e9e;
}
.components-checkbox-control__input[type=checkbox]:-ms-input-placeholder{
  color:#1e1e1e9e;
}
.components-checkbox-control__input[type=checkbox]:focus{
  box-shadow:0 0 0 2px #fff, 0 0 0 4px var(--wp-admin-theme-color);
}
.components-checkbox-control__input[type=checkbox]:checked{
  background:var(--wp-admin-theme-color);
  border-color:var(--wp-admin-theme-color);
}
.components-checkbox-control__input[type=checkbox]:checked::-ms-check{
  opacity:0;
}
.components-checkbox-control__input[type=checkbox]:checked:before,.components-checkbox-control__input[type=checkbox][aria-checked=mixed]:before{
  color:#fff;
  margin:-3px -5px;
}
@media (min-width:782px){
  .components-checkbox-control__input[type=checkbox]:checked:before,.components-checkbox-control__input[type=checkbox][aria-checked=mixed]:before{
    margin:-4px -5px 0 0;
  }
}
.components-checkbox-control__input[type=checkbox][aria-checked=mixed]{
  background:var(--wp-admin-theme-color);
  border-color:var(--wp-admin-theme-color);
}
.components-checkbox-control__input[type=checkbox][aria-checked=mixed]:before{
  content:"\f460";
  display:inline-block;
  float:right;
  font:normal 30px/1 dashicons;
  vertical-align:middle;
  width:16px;
  speak:none;
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
}
@media (min-width:782px){
  .components-checkbox-control__input[type=checkbox][aria-checked=mixed]:before{
    float:none;
    font-size:21px;
  }
}
.components-checkbox-control__input[type=checkbox]:disabled,.components-checkbox-control__input[type=checkbox][aria-disabled=true]{
  background:#f0f0f0;
  border-color:#ddd;
  cursor:default;
  opacity:1;
}
@media not (prefers-reduced-motion){
  .components-checkbox-control__input[type=checkbox]{
    transition:border-color .1s ease-in-out;
  }
}
.components-checkbox-control__input[type=checkbox]:focus{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #fff, 0 0 0 calc(var(--wp-admin-border-width-focus)*2) var(--wp-admin-theme-color);
  outline:2px solid #0000;
  outline-offset:2px;
}
.components-checkbox-control__input[type=checkbox]:checked,.components-checkbox-control__input[type=checkbox]:indeterminate{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  border-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-checkbox-control__input[type=checkbox]:checked::-ms-check,.components-checkbox-control__input[type=checkbox]:indeterminate::-ms-check{
  opacity:0;
}
.components-checkbox-control__input[type=checkbox]:checked:before{
  content:none;
}

.components-checkbox-control__input-container{
  aspect-ratio:1;
  display:inline-block;
  flex-shrink:0;
  line-height:1;
  margin-left:var(--checkbox-input-margin);
  position:relative;
  vertical-align:middle;
  width:var(--checkbox-input-size);
}

svg.components-checkbox-control__checked,svg.components-checkbox-control__indeterminate{
  --checkmark-size:var(--checkbox-input-size);
  fill:#fff;
  cursor:pointer;
  height:var(--checkmark-size);
  pointer-events:none;
  position:absolute;
  right:50%;
  top:50%;
  transform:translate(50%, -50%);
  -webkit-user-select:none;
          user-select:none;
  width:var(--checkmark-size);
}
@media (min-width:600px){
  svg.components-checkbox-control__checked,svg.components-checkbox-control__indeterminate{
    --checkmark-size:calc(var(--checkbox-input-size) + 4px);
  }
}

.components-checkbox-control__help{
  display:inline-block;
  margin-inline-start:calc(var(--checkbox-input-size) + var(--checkbox-input-margin));
}

.components-circular-option-picker{
  display:inline-block;
  min-width:188px;
  width:100%;
}
.components-circular-option-picker .components-circular-option-picker__custom-clear-wrapper{
  display:flex;
  justify-content:flex-end;
  margin-top:12px;
}
.components-circular-option-picker .components-circular-option-picker__swatches{
  display:flex;
  flex-wrap:wrap;
  gap:12px;
  position:relative;
  z-index:1;
}
.components-circular-option-picker>:not(.components-circular-option-picker__swatches){
  position:relative;
  z-index:0;
}

.components-circular-option-picker__option-wrapper{
  display:inline-block;
  height:28px;
  transform:scale(1);
  vertical-align:top;
  width:28px;
}
@media not (prefers-reduced-motion){
  .components-circular-option-picker__option-wrapper{
    transition:transform .1s ease;
    will-change:transform;
  }
}
.components-circular-option-picker__option-wrapper:hover{
  transform:scale(1.2);
}
.components-circular-option-picker__option-wrapper>div{
  height:100%;
  width:100%;
}

.components-circular-option-picker__option-wrapper:before{
  background:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='28' height='28' fill='none'%3E%3Cpath fill='%23555D65' d='M6 8V6H4v2zm2 0V6h2v2zm2 8H8v-2h2zm2 0v-2h2v2zm0 2v-2h-2v2H8v2h2v-2zm2 0v2h-2v-2zm2 0h-2v-2h2z'/%3E%3Cpath fill='%23555D65' fill-rule='evenodd' d='M18 18h2v-2h-2v-2h2v-2h-2v-2h2V8h-2v2h-2V8h-2v2h2v2h-2v2h2v2h2zm-2-4v-2h2v2z' clip-rule='evenodd'/%3E%3Cpath fill='%23555D65' d='M18 18v2h-2v-2z'/%3E%3Cpath fill='%23555D65' fill-rule='evenodd' d='M8 10V8H6v2H4v2h2v2H4v2h2v2H4v2h2v2H4v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v-2h-2v-2h2v-2h-2v-2h2v-2h-2v-2h2V8h-2V6h2V4h-2v2h-2V4h-2v2h-2V4h-2v2h-2V4h-2v2h2v2h-2v2zm0 2v-2H6v2zm2 0v-2h2v2zm0 2v-2H8v2H6v2h2v2H6v2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h-2v-2h2v-2h-2v-2h2v-2h-2v-2h2V8h-2V6h-2v2h-2V6h-2v2h-2v2h2v2h-2v2z' clip-rule='evenodd'/%3E%3Cpath fill='%23555D65' fill-rule='evenodd' d='M4 0H2v2H0v2h2v2H0v2h2v2H0v2h2v2H0v2h2v2H0v2h2v2H0v2h2v2H0v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v-2h-2v-2h2v-2h-2v-2h2v-2h-2v-2h2v-2h-2v-2h2V8h-2V6h2V4h-2V2h2V0h-2v2h-2V0h-2v2h-2V0h-2v2h-2V0h-2v2h-2V0h-2v2H8V0H6v2H4zm0 4V2H2v2zm2 0V2h2v2zm0 2V4H4v2H2v2h2v2H2v2h2v2H2v2h2v2H2v2h2v2H2v2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h2v2h2v-2h-2v-2h2v-2h-2v-2h2v-2h-2v-2h2v-2h-2v-2h2V8h-2V6h2V4h-2V2h-2v2h-2V2h-2v2h-2V2h-2v2h-2V2h-2v2H8v2z' clip-rule='evenodd'/%3E%3C/svg%3E");
  border-radius:50%;
  bottom:1px;
  content:"";
  left:1px;
  position:absolute;
  right:1px;
  top:1px;
  z-index:-1;
}

.components-circular-option-picker__option{
  aspect-ratio:1;
  background:#0000;
  border:none;
  border-radius:50%;
  box-shadow:inset 0 0 0 14px;
  cursor:pointer;
  display:inline-block;
  height:100% !important;
  vertical-align:top;
}
@media not (prefers-reduced-motion){
  .components-circular-option-picker__option{
    transition:box-shadow .1s ease;
  }
}
.components-circular-option-picker__option:hover{
  box-shadow:inset 0 0 0 14px !important;
}
.components-circular-option-picker__option[aria-pressed=true],.components-circular-option-picker__option[aria-selected=true]{
  box-shadow:inset 0 0 0 4px;
  overflow:visible;
  position:relative;
  z-index:1;
}
.components-circular-option-picker__option[aria-pressed=true]+svg,.components-circular-option-picker__option[aria-selected=true]+svg{
  border-radius:50%;
  pointer-events:none;
  position:absolute;
  right:2px;
  top:2px;
  z-index:2;
}
.components-circular-option-picker__option:after{
  border:1px solid #0000;
  border-radius:50%;
  bottom:-1px;
  box-shadow:inset 0 0 0 1px #0003;
  box-sizing:inherit;
  content:"";
  left:-1px;
  position:absolute;
  right:-1px;
  top:-1px;
}
.components-circular-option-picker__option:focus:after{
  border:2px solid #757575;
  border-radius:50%;
  box-shadow:inset 0 0 0 2px #fff;
  content:"";
  height:calc(100% + 4px);
  position:absolute;
  right:50%;
  top:50%;
  transform:translate(50%, -50%);
  width:calc(100% + 4px);
}
.components-circular-option-picker__option.components-button:focus{
  background-color:initial;
  box-shadow:inset 0 0 0 14px;
  outline:none;
}

.components-circular-option-picker__button-action .components-circular-option-picker__option{
  background:#fff;
  color:#fff;
}

.components-circular-option-picker__dropdown-link-action{
  margin-left:16px;
}
.components-circular-option-picker__dropdown-link-action .components-button{
  line-height:22px;
}

.components-palette-edit__popover-gradient-picker{
  padding:8px;
  width:260px;
}

.components-dropdown-menu__menu .components-palette-edit__menu-button{
  width:100%;
}

.component-color-indicator{
  background:#fff linear-gradient(45deg, #0000 48%, #ddd 0, #ddd 52%, #0000 0);
  border-radius:50%;
  box-shadow:inset 0 0 0 1px #0003;
  display:inline-block;
  height:20px;
  padding:0;
  width:20px;
}

.components-combobox-control{
  width:100%;
}

input.components-combobox-control__input[type=text]{
  border:none;
  box-shadow:none;
  font-family:inherit;
  font-size:16px;
  line-height:inherit;
  margin:0;
  min-height:auto;
  padding:2px;
  width:100%;
}
@media (min-width:600px){
  input.components-combobox-control__input[type=text]{
    font-size:13px;
  }
}
input.components-combobox-control__input[type=text]:focus{
  box-shadow:none;
  outline:none;
}

.components-combobox-control__suggestions-container{
  align-items:flex-start;
  border:1px solid #949494;
  border-radius:2px;
  box-shadow:0 0 0 #0000;
  display:flex;
  flex-wrap:wrap;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  line-height:normal;
  padding:0;
  width:100%;
}
@media not (prefers-reduced-motion){
  .components-combobox-control__suggestions-container{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .components-combobox-control__suggestions-container{
    font-size:13px;
    line-height:normal;
  }
}
.components-combobox-control__suggestions-container:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-combobox-control__suggestions-container::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.components-combobox-control__suggestions-container::-moz-placeholder{
  color:#1e1e1e9e;
}
.components-combobox-control__suggestions-container:-ms-input-placeholder{
  color:#1e1e1e9e;
}
.components-combobox-control__suggestions-container:focus-within{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-combobox-control__suggestions-container .components-spinner{
  margin:0;
}

.components-color-palette__custom-color-wrapper{
  position:relative;
  z-index:0;
}

.components-color-palette__custom-color-button{
  background:none;
  border:none;
  border-radius:4px 4px 0 0;
  box-shadow:inset 0 0 0 1px #0003;
  box-sizing:border-box;
  cursor:pointer;
  height:64px;
  outline:1px solid #0000;
  position:relative;
  width:100%;
}
.components-color-palette__custom-color-button:focus{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline-width:2px;
}
.components-color-palette__custom-color-button:after{
  background-image:repeating-linear-gradient(45deg, #e0e0e0 25%, #0000 0, #0000 75%, #e0e0e0 0, #e0e0e0), repeating-linear-gradient(45deg, #e0e0e0 25%, #0000 0, #0000 75%, #e0e0e0 0, #e0e0e0);
  background-position:0 0, 24px 24px;
  background-size:48px 48px;
  border-radius:3px 3px 0 0;
  content:"";
  inset:1px;
  position:absolute;
  z-index:-1;
}

.components-color-palette__custom-color-text-wrapper{
  border-radius:0 0 4px 4px;
  box-shadow:inset 0 -1px 0 0 #0003,inset -1px 0 0 0 #0003,inset 1px 0 0 0 #0003;
  font-size:13px;
  padding:12px 16px;
  position:relative;
}

.components-color-palette__custom-color-name{
  color:var(--wp-components-color-foreground, #1e1e1e);
  margin:0 1px;
}

.components-color-palette__custom-color-value{
  color:#757575;
}
.components-color-palette__custom-color-value--is-hex{
  text-transform:uppercase;
}
.components-color-palette__custom-color-value:empty:after{
  content:"​";
  visibility:hidden;
}

.components-custom-gradient-picker__gradient-bar{
  border-radius:2px;
  height:48px;
  position:relative;
  width:100%;
  z-index:1;
}
.components-custom-gradient-picker__gradient-bar.has-gradient{
  background-image:repeating-linear-gradient(45deg, #e0e0e0 25%, #0000 0, #0000 75%, #e0e0e0 0, #e0e0e0), repeating-linear-gradient(45deg, #e0e0e0 25%, #0000 0, #0000 75%, #e0e0e0 0, #e0e0e0);
  background-position:0 0, 12px 12px;
  background-size:24px 24px;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__gradient-bar-background{
  inset:0;
  position:absolute;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__markers-container{
  margin-left:auto;
  margin-right:auto;
  position:relative;
  width:calc(100% - 48px);
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-dropdown{
  display:flex;
  height:16px;
  position:absolute;
  top:16px;
  width:16px;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__insert-point-dropdown{
  background:#fff;
  border-radius:50%;
  color:#1e1e1e;
  height:inherit;
  min-width:16px !important;
  padding:2px;
  position:relative;
  width:inherit;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__insert-point-dropdown svg{
  height:100%;
  width:100%;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-button{
  border-radius:50%;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) #fff, 0 0 2px 0 #00000040;
  height:inherit;
  outline:2px solid #0000;
  padding:0;
  width:inherit;
}
.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-button.is-active,.components-custom-gradient-picker__gradient-bar .components-custom-gradient-picker__control-point-button:focus{
  box-shadow:inset 0 0 0 calc(var(--wp-admin-border-width-focus)*2) #fff, 0 0 2px 0 #00000040;
  outline:1.5px solid #0000;
}

.components-custom-gradient-picker__remove-control-point-wrapper{
  padding-bottom:8px;
}

.components-custom-gradient-picker__inserter{
  direction:ltr;
}

.components-custom-gradient-picker__liner-gradient-indicator{
  display:inline-block;
  flex:0 auto;
  height:20px;
  width:20px;
}

.components-custom-gradient-picker__ui-line{
  position:relative;
  z-index:0;
}

.block-editor-dimension-control .components-base-control__field{
  align-items:center;
  display:flex;
}
.block-editor-dimension-control .components-base-control__label{
  align-items:center;
  display:flex;
  margin-bottom:0;
  margin-left:1em;
}
.block-editor-dimension-control .components-base-control__label .dashicon{
  margin-left:.5em;
}
.block-editor-dimension-control.is-manual .components-base-control__label{
  width:10em;
}

body.is-dragging-components-draggable{
  cursor:move;
  cursor:grabbing !important;
}

.components-draggable__invisible-drag-image{
  height:50px;
  position:fixed;
  right:-1000px;
  width:50px;
}

.components-draggable__clone{
  background:#0000;
  padding:0;
  pointer-events:none;
  position:fixed;
  z-index:1000000000;
}

.components-drop-zone{
  border-radius:2px;
  bottom:0;
  left:0;
  opacity:0;
  position:absolute;
  right:0;
  top:0;
  visibility:hidden;
  z-index:40;
}
.components-drop-zone.is-active{
  opacity:1;
  visibility:visible;
}
.components-drop-zone .components-drop-zone__content{
  align-items:center;
  background-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  bottom:0;
  color:#fff;
  display:flex;
  height:100%;
  justify-content:center;
  left:0;
  opacity:0;
  pointer-events:none;
  position:absolute;
  right:0;
  text-align:center;
  top:0;
  width:100%;
  z-index:50;
}
.components-drop-zone .components-drop-zone__content-inner{
  opacity:0;
  transform:scale(.9);
}
.components-drop-zone.is-active.is-dragging-over-element .components-drop-zone__content{
  opacity:1;
}
@media not (prefers-reduced-motion){
  .components-drop-zone.is-active.is-dragging-over-element .components-drop-zone__content{
    transition:opacity .2s ease-in-out;
  }
}
.components-drop-zone.is-active.is-dragging-over-element .components-drop-zone__content-inner{
  opacity:1;
  transform:scale(1);
}
@media not (prefers-reduced-motion){
  .components-drop-zone.is-active.is-dragging-over-element .components-drop-zone__content-inner{
    transition:opacity .1s ease-in-out .1s,transform .1s ease-in-out .1s;
  }
}

.components-drop-zone__content-icon,.components-drop-zone__content-text{
  display:block;
}

.components-drop-zone__content-icon{
  line-height:0;
  margin:0 auto 8px;
  fill:currentColor;
  pointer-events:none;
}

.components-drop-zone__content-text{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
}

.components-dropdown{
  display:inline-block;
}

.components-dropdown__content .components-popover__content{
  padding:8px;
}
.components-dropdown__content .components-popover__content:has(.components-menu-group){
  padding:0;
}
.components-dropdown__content .components-popover__content:has(.components-menu-group) .components-dropdown-menu__menu>.components-menu-item__button,.components-dropdown__content .components-popover__content:has(.components-menu-group)>.components-menu-item__button{
  margin:8px;
  width:auto;
}
.components-dropdown__content [role=menuitem]{
  white-space:nowrap;
}
.components-dropdown__content .components-menu-group{
  padding:8px;
}
.components-dropdown__content .components-menu-group+.components-menu-group{
  border-top:1px solid #ccc;
  padding:8px;
}
.components-dropdown__content.is-alternate .components-menu-group+.components-menu-group{
  border-color:#1e1e1e;
}

.components-dropdown-menu__toggle{
  vertical-align:top;
}

.components-dropdown-menu__menu{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  line-height:1.4;
  width:100%;
}
.components-dropdown-menu__menu .components-dropdown-menu__menu-item,.components-dropdown-menu__menu .components-menu-item{
  cursor:pointer;
  outline:none;
  padding:6px;
  white-space:nowrap;
  width:100%;
}
.components-dropdown-menu__menu .components-dropdown-menu__menu-item.has-separator,.components-dropdown-menu__menu .components-menu-item.has-separator{
  margin-top:6px;
  overflow:visible;
  position:relative;
}
.components-dropdown-menu__menu .components-dropdown-menu__menu-item.has-separator:before,.components-dropdown-menu__menu .components-menu-item.has-separator:before{
  background-color:#ddd;
  box-sizing:initial;
  content:"";
  display:block;
  height:1px;
  left:0;
  position:absolute;
  right:0;
  top:-3px;
}
.components-dropdown-menu__menu .components-dropdown-menu__menu-item.is-active .dashicon,.components-dropdown-menu__menu .components-dropdown-menu__menu-item.is-active svg,.components-dropdown-menu__menu .components-menu-item.is-active .dashicon,.components-dropdown-menu__menu .components-menu-item.is-active svg{
  background:#1e1e1e;
  border-radius:1px;
  box-shadow:0 0 0 1px #1e1e1e;
  color:#fff;
}
.components-dropdown-menu__menu .components-dropdown-menu__menu-item.is-icon-only,.components-dropdown-menu__menu .components-menu-item.is-icon-only{
  width:auto;
}
.components-dropdown-menu__menu .components-menu-item__button,.components-dropdown-menu__menu .components-menu-item__button.components-button{
  height:auto;
  min-height:40px;
  padding-left:8px;
  padding-right:8px;
  text-align:right;
}

.components-duotone-picker__color-indicator:before{
  background:#0000;
}
.components-duotone-picker__color-indicator>.components-button,.components-duotone-picker__color-indicator>.components-button.is-pressed:hover:not(:disabled){
  background:linear-gradient(45deg, #0000 48%, #ddd 0, #ddd 52%, #0000 0);
  color:#0000;
}
.components-duotone-picker__color-indicator>.components-button:not([aria-disabled=true]):active{
  color:#0000;
}

.components-color-list-picker,.components-color-list-picker__swatch-button{
  width:100%;
}

.components-color-list-picker__color-picker{
  margin:8px 0;
}

.components-color-list-picker__swatch-color{
  margin:2px;
}

.components-external-link{
  text-decoration:none;
}

.components-external-link__contents{
  text-decoration:underline;
}

.components-external-link__icon{
  font-weight:400;
  margin-right:.5ch;
}
.components-form-toggle,.components-form-toggle .components-form-toggle__track{
  display:inline-block;
  height:16px;
  position:relative;
}
.components-form-toggle .components-form-toggle__track{
  background-color:#fff;
  border:1px solid #949494;
  border-radius:8px;
  box-sizing:border-box;
  content:"";
  overflow:hidden;
  vertical-align:top;
  width:32px;
}
@media not (prefers-reduced-motion){
  .components-form-toggle .components-form-toggle__track{
    transition:background-color .2s ease,border-color .2s ease;
  }
}
.components-form-toggle .components-form-toggle__track:after{
  border-top:16px solid #0000;
  box-sizing:border-box;
  content:"";
  inset:0;
  opacity:0;
  position:absolute;
}
@media not (prefers-reduced-motion){
  .components-form-toggle .components-form-toggle__track:after{
    transition:opacity .2s ease;
  }
}
.components-form-toggle .components-form-toggle__thumb{
  background-color:#1e1e1e;
  border:6px solid #0000;
  border-radius:50%;
  box-shadow:0 1px 1px #00000008,0 1px 2px #00000005,0 3px 3px #00000005,0 4px 4px #00000003;
  box-sizing:border-box;
  display:block;
  height:12px;
  position:absolute;
  right:2px;
  top:2px;
  width:12px;
}
@media not (prefers-reduced-motion){
  .components-form-toggle .components-form-toggle__thumb{
    transition:transform .2s ease,background-color .2s ease-out;
  }
}
.components-form-toggle.is-checked .components-form-toggle__track{
  background-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  border-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-form-toggle.is-checked .components-form-toggle__track:after{
  opacity:1;
}
.components-form-toggle .components-form-toggle__input:focus+.components-form-toggle__track{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #fff, 0 0 0 calc(var(--wp-admin-border-width-focus)*2) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:2px solid #0000;
  outline-offset:2px;
}
.components-form-toggle.is-checked .components-form-toggle__thumb{
  background-color:#fff;
  border-width:0;
  transform:translateX(-16px);
}
.components-disabled .components-form-toggle,.components-form-toggle.is-disabled{
  opacity:.3;
}

.components-form-toggle input.components-form-toggle__input[type=checkbox]{
  border:none;
  height:100%;
  margin:0;
  opacity:0;
  padding:0;
  position:absolute;
  right:0;
  top:0;
  width:100%;
  z-index:1;
}
.components-form-toggle input.components-form-toggle__input[type=checkbox]:checked{
  background:none;
}
.components-form-toggle input.components-form-toggle__input[type=checkbox]:before{
  content:"";
}
.components-form-toggle input.components-form-toggle__input[type=checkbox]:not(:disabled,[aria-disabled=true]){
  cursor:pointer;
}

.components-form-token-field__input-container{
  border:1px solid #949494;
  border-radius:2px;
  box-shadow:0 0 0 #0000;
  cursor:text;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  line-height:normal;
  padding:0;
  width:100%;
}
@media not (prefers-reduced-motion){
  .components-form-token-field__input-container{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .components-form-token-field__input-container{
    font-size:13px;
    line-height:normal;
  }
}
.components-form-token-field__input-container:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-form-token-field__input-container::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.components-form-token-field__input-container::-moz-placeholder{
  color:#1e1e1e9e;
}
.components-form-token-field__input-container:-ms-input-placeholder{
  color:#1e1e1e9e;
}
.components-form-token-field__input-container.is-disabled{
  background:#ddd;
  border-color:#ddd;
}
.components-form-token-field__input-container.is-active{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-form-token-field__input-container input[type=text].components-form-token-field__input{
  background:inherit;
  border:0;
  box-shadow:none;
  color:#1e1e1e;
  display:inline-block;
  flex:1;
  font-family:inherit;
  font-size:16px;
  margin-right:4px;
  max-width:100%;
  min-height:24px;
  min-width:50px;
  padding:0;
  width:100%;
}
@media (min-width:600px){
  .components-form-token-field__input-container input[type=text].components-form-token-field__input{
    font-size:13px;
  }
}
.components-form-token-field.is-active .components-form-token-field__input-container input[type=text].components-form-token-field__input,.components-form-token-field__input-container input[type=text].components-form-token-field__input:focus{
  box-shadow:none;
  outline:none;
}
.components-form-token-field__input-container .components-form-token-field__token+input[type=text].components-form-token-field__input{
  width:auto;
}

.components-form-token-field__token{
  color:#1e1e1e;
  display:flex;
  font-size:13px;
  max-width:100%;
}
.components-form-token-field__token.is-success .components-form-token-field__remove-token,.components-form-token-field__token.is-success .components-form-token-field__token-text{
  background:#4ab866;
}
.components-form-token-field__token.is-error .components-form-token-field__remove-token,.components-form-token-field__token.is-error .components-form-token-field__token-text{
  background:#cc1818;
}
.components-form-token-field__token.is-validating .components-form-token-field__remove-token,.components-form-token-field__token.is-validating .components-form-token-field__token-text{
  color:#757575;
}
.components-form-token-field__token.is-borderless{
  padding:0 0 0 24px;
  position:relative;
}
.components-form-token-field__token.is-borderless .components-form-token-field__token-text{
  background:#0000;
}
.components-form-token-field__token.is-borderless:not(.is-disabled) .components-form-token-field__token-text{
  color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-form-token-field__token.is-borderless .components-form-token-field__remove-token{
  background:#0000;
  color:#757575;
  left:0;
  position:absolute;
  top:1px;
}
.components-form-token-field__token.is-borderless.is-success .components-form-token-field__token-text{
  color:#4ab866;
}
.components-form-token-field__token.is-borderless.is-error .components-form-token-field__token-text{
  color:#cc1818;
  padding:0 6px 0 4px;
}
.components-form-token-field__token.is-borderless.is-validating .components-form-token-field__token-text{
  color:#1e1e1e;
}

.components-form-token-field__remove-token.components-button,.components-form-token-field__token-text{
  background:#ddd;
  display:inline-block;
  height:auto;
  min-width:unset;
}
@media not (prefers-reduced-motion){
  .components-form-token-field__remove-token.components-button,.components-form-token-field__token-text{
    transition:all .2s cubic-bezier(.4, 1, .4, 1);
  }
}

.components-form-token-field__token-text{
  border-radius:0 1px 1px 0;
  line-height:24px;
  overflow:hidden;
  padding:0 8px 0 0;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.components-form-token-field__remove-token.components-button{
  border-radius:1px 0 0 1px;
  color:#1e1e1e;
  line-height:10px;
  overflow:initial;
}
.components-form-token-field__remove-token.components-button:hover:not(:disabled){
  color:#1e1e1e;
}

.components-form-token-field__suggestions-list{
  box-shadow:inset 0 1px 0 0 #949494;
  flex:1 0 100%;
  list-style:none;
  margin:0;
  max-height:128px;
  min-width:100%;
  overflow-y:auto;
  padding:0;
}
@media not (prefers-reduced-motion){
  .components-form-token-field__suggestions-list{
    transition:all .15s ease-in-out;
  }
}

.components-form-token-field__suggestion{
  box-sizing:border-box;
  color:#1e1e1e;
  display:block;
  font-size:13px;
  margin:0;
  min-height:32px;
  padding:8px 12px;
}
.components-form-token-field__suggestion.is-selected{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  color:#fff;
}
.components-form-token-field__suggestion[aria-disabled=true]{
  color:#949494;
  pointer-events:none;
}
.components-form-token-field__suggestion[aria-disabled=true].is-selected{
  background-color:rgba(var(--wp-components-color-accent--rgb, var(--wp-admin-theme-color--rgb)), .04);
}
.components-form-token-field__suggestion:not(.is-empty){
  cursor:pointer;
}

@media (min-width:600px){
  .components-guide{
    width:600px;
  }
}
.components-guide .components-modal__content{
  margin-top:0;
  padding:0;
}
.components-guide .components-modal__content:before{
  content:none;
}
.components-guide .components-modal__header{
  border-bottom:none;
  height:60px;
  padding:0;
  position:sticky;
}
.components-guide .components-modal__header .components-button{
  align-self:flex-start;
  margin:8px 0 0 8px;
  position:static;
}
.components-guide .components-modal__header .components-button:hover svg{
  fill:#fff;
}
.components-guide .components-guide__container{
  display:flex;
  flex-direction:column;
  justify-content:space-between;
  margin-top:-60px;
  min-height:100%;
}
.components-guide .components-guide__page{
  display:flex;
  flex-direction:column;
  justify-content:center;
  position:relative;
}
@media (min-width:600px){
  .components-guide .components-guide__page{
    min-height:300px;
  }
}
.components-guide .components-guide__footer{
  align-content:center;
  display:flex;
  height:36px;
  justify-content:center;
  margin:0 0 24px;
  padding:0 32px;
  position:relative;
  width:100%;
}
.components-guide .components-guide__page-control{
  margin:0;
  text-align:center;
}
.components-guide .components-guide__page-control li{
  display:inline-block;
  margin:0;
}
.components-guide .components-guide__page-control .components-button{
  color:#e0e0e0;
  margin:-6px 0;
}
.components-guide .components-guide__page-control li[aria-current=step] .components-button{
  color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}

.components-modal__frame.components-guide{
  border:none;
  max-height:575px;
  min-width:312px;
}
@media (max-width:600px){
  .components-modal__frame.components-guide{
    margin:auto;
    max-width:calc(100vw - 32px);
  }
}

.components-button.components-guide__back-button,.components-button.components-guide__finish-button,.components-button.components-guide__forward-button{
  position:absolute;
}
.components-button.components-guide__back-button{
  right:32px;
}
.components-button.components-guide__finish-button,.components-button.components-guide__forward-button{
  left:32px;
}

[role=region]{
  position:relative;
}

.is-focusing-regions [role=region]:focus:after,[role=region].interface-interface-skeleton__content:focus-visible:after{
  bottom:0;
  content:"";
  left:0;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
  z-index:1000000;
}
.is-focusing-regions .editor-post-publish-panel,.is-focusing-regions .interface-interface-skeleton__actions .editor-layout__toggle-entities-saved-states-panel,.is-focusing-regions .interface-interface-skeleton__actions .editor-layout__toggle-publish-panel,.is-focusing-regions .interface-interface-skeleton__sidebar .editor-layout__toggle-sidebar-panel,.is-focusing-regions [role=region]:focus:after,.is-focusing-regions.is-distraction-free .interface-interface-skeleton__header .edit-post-header,[role=region].interface-interface-skeleton__content:focus-visible:after{
  outline-color:var(--wp-admin-theme-color);
  outline-offset:calc(((-1*var(--wp-admin-border-width-focus))/var(--wp-block-editor-iframe-zoom-out-scale, 1))*2);
  outline-style:solid;
  outline-width:calc((var(--wp-admin-border-width-focus)/var(--wp-block-editor-iframe-zoom-out-scale, 1))*2);
}

.components-menu-group+.components-menu-group{
  border-top:1px solid #1e1e1e;
  padding-top:8px;
}
.components-menu-group+.components-menu-group.has-hidden-separator{
  border-top:none;
  margin-top:0;
  padding-top:0;
}

.components-menu-group:has(>div:empty){
  display:none;
}

.components-menu-group__label{
  color:#757575;
  font-size:11px;
  font-weight:500;
  margin-bottom:12px;
  margin-top:4px;
  padding:0 8px;
  text-transform:uppercase;
  white-space:nowrap;
}

.components-menu-item__button,.components-menu-item__button.components-button{
  width:100%;
}
.components-menu-item__button.components-button[role=menuitemcheckbox] .components-menu-item__item:only-child,.components-menu-item__button.components-button[role=menuitemradio] .components-menu-item__item:only-child,.components-menu-item__button[role=menuitemcheckbox] .components-menu-item__item:only-child,.components-menu-item__button[role=menuitemradio] .components-menu-item__item:only-child{
  box-sizing:initial;
  padding-left:48px;
}
.components-menu-item__button .components-menu-items__item-icon,.components-menu-item__button.components-button .components-menu-items__item-icon{
  display:inline-block;
  flex:0 0 auto;
}
.components-menu-item__button .components-menu-items__item-icon.has-icon-right,.components-menu-item__button.components-button .components-menu-items__item-icon.has-icon-right{
  margin-left:-2px;
  margin-right:24px;
}
.components-menu-item__button .components-menu-item__shortcut+.components-menu-items__item-icon.has-icon-right,.components-menu-item__button.components-button .components-menu-item__shortcut+.components-menu-items__item-icon.has-icon-right{
  margin-right:8px;
}
.components-menu-item__button .block-editor-block-icon,.components-menu-item__button.components-button .block-editor-block-icon{
  margin-left:8px;
  margin-right:-2px;
}
.components-menu-item__button.components-button.is-primary,.components-menu-item__button.is-primary{
  justify-content:center;
}
.components-menu-item__button.components-button.is-primary .components-menu-item__item,.components-menu-item__button.is-primary .components-menu-item__item{
  margin-left:0;
}
.components-menu-item__button.components-button:disabled.is-tertiary,.components-menu-item__button.components-button[aria-disabled=true].is-tertiary,.components-menu-item__button:disabled.is-tertiary,.components-menu-item__button[aria-disabled=true].is-tertiary{
  background:none;
  color:var(--wp-components-color-accent-darker-10, var(--wp-admin-theme-color-darker-10, #2145e6));
  opacity:.3;
}

.components-menu-item__info-wrapper{
  display:flex;
  flex-direction:column;
  margin-left:auto;
}

.components-menu-item__info{
  color:#757575;
  font-size:12px;
  margin-top:4px;
  white-space:normal;
}

.components-menu-item__item{
  align-items:center;
  display:inline-flex;
  margin-left:auto;
  min-width:160px;
  white-space:nowrap;
}

.components-menu-item__shortcut{
  align-self:center;
  color:currentColor;
  display:none;
  margin-left:0;
  margin-right:auto;
  padding-right:24px;
}
@media (min-width:480px){
  .components-menu-item__shortcut{
    display:inline;
  }
}

.components-menu-items-choice,.components-menu-items-choice.components-button{
  height:auto;
  min-height:40px;
}
.components-menu-items-choice svg,.components-menu-items-choice.components-button svg{
  margin-left:12px;
}
.components-menu-items-choice.components-button.has-icon,.components-menu-items-choice.has-icon{
  padding-right:12px;
}

.components-modal__screen-overlay{
  background-color:#00000059;
  bottom:0;
  display:flex;
  left:0;
  position:fixed;
  right:0;
  top:0;
  z-index:100000;
}
@keyframes __wp-base-styles-fade-in{
  0%{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
@media not (prefers-reduced-motion){
  .components-modal__screen-overlay{
    animation:__wp-base-styles-fade-in .08s linear 0s;
    animation-fill-mode:forwards;
  }
}
@keyframes __wp-base-styles-fade-out{
  0%{
    opacity:1;
  }
  to{
    opacity:0;
  }
}
@media not (prefers-reduced-motion){
  .components-modal__screen-overlay.is-animating-out{
    animation:__wp-base-styles-fade-out .08s linear 80ms;
    animation-fill-mode:forwards;
  }
}

.components-modal__frame{
  animation-fill-mode:forwards;
  animation-name:components-modal__appear-animation;
  animation-timing-function:cubic-bezier(.29, 0, 0, 1);
  background:#fff;
  border-radius:8px 8px 0 0;
  box-shadow:0 5px 15px #00000014,0 15px 27px #00000012,0 30px 36px #0000000a,0 50px 43px #00000005;
  box-sizing:border-box;
  display:flex;
  margin:40px 0 0;
  overflow:hidden;
  width:100%;
}
.components-modal__frame *,.components-modal__frame :after,.components-modal__frame :before{
  box-sizing:inherit;
}
@media not (prefers-reduced-motion){
  .components-modal__frame{
    animation-duration:var(--modal-frame-animation-duration);
  }
}
.components-modal__screen-overlay.is-animating-out .components-modal__frame{
  animation-name:components-modal__disappear-animation;
  animation-timing-function:cubic-bezier(1, 0, .2, 1);
}
@media (min-width:600px){
  .components-modal__frame{
    border-radius:8px;
    margin:auto;
    max-height:calc(100% - 120px);
    max-width:calc(100% - 32px);
    min-width:350px;
    width:auto;
  }
}
@media (min-width:600px) and (min-width:600px){
  .components-modal__frame.is-full-screen{
    height:calc(100% - 32px);
    max-height:none;
    width:calc(100% - 32px);
  }
}
@media (min-width:600px) and (min-width:782px){
  .components-modal__frame.is-full-screen{
    height:calc(100% - 80px);
    max-width:none;
    width:calc(100% - 80px);
  }
}
@media (min-width:600px){
  .components-modal__frame.has-size-large,.components-modal__frame.has-size-medium,.components-modal__frame.has-size-small{
    width:100%;
  }
  .components-modal__frame.has-size-small{
    max-width:384px;
  }
  .components-modal__frame.has-size-medium{
    max-width:512px;
  }
  .components-modal__frame.has-size-large{
    max-width:840px;
  }
}
@media (min-width:960px){
  .components-modal__frame{
    max-height:70%;
  }
}

@keyframes components-modal__appear-animation{
  0%{
    opacity:0;
    transform:scale(.9);
  }
  to{
    opacity:1;
    transform:scale(1);
  }
}
@keyframes components-modal__disappear-animation{
  0%{
    opacity:1;
    transform:scale(1);
  }
  to{
    opacity:0;
    transform:scale(.9);
  }
}
.components-modal__header{
  align-items:center;
  border-bottom:1px solid #0000;
  box-sizing:border-box;
  display:flex;
  flex-direction:row;
  height:72px;
  justify-content:space-between;
  padding:24px 32px 8px;
  position:absolute;
  right:0;
  top:0;
  width:100%;
  z-index:10;
}
.components-modal__header .components-modal__header-heading{
  font-size:1.2rem;
  font-weight:600;
}
.components-modal__header h1{
  line-height:1;
  margin:0;
}
.components-modal__content.has-scrolled-content:not(.hide-header) .components-modal__header{
  border-bottom-color:#ddd;
}
.components-modal__header+p{
  margin-top:0;
}

.components-modal__header-heading-container{
  align-items:center;
  display:flex;
  flex-direction:row;
  flex-grow:1;
  justify-content:right;
}

.components-modal__header-icon-container{
  display:inline-block;
}
.components-modal__header-icon-container svg{
  max-height:36px;
  max-width:36px;
  padding:8px;
}

.components-modal__content{
  flex:1;
  margin-top:72px;
  overflow:auto;
  padding:4px 32px 32px;
}
.components-modal__content.hide-header{
  margin-top:0;
  padding-top:32px;
}
.components-modal__content.is-scrollable:focus-visible{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:2px solid #0000;
  outline-offset:-2px;
}

.components-notice{
  align-items:center;
  background-color:#fff;
  border-right:4px solid var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  color:#1e1e1e;
  display:flex;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  padding:8px 12px;
}
.components-notice.is-dismissible{
  position:relative;
}
.components-notice.is-success{
  background-color:#eff9f1;
  border-right-color:#4ab866;
}
.components-notice.is-warning{
  background-color:#fef8ee;
  border-right-color:#f0b849;
}
.components-notice.is-error{
  background-color:#f4a2a2;
  border-right-color:#cc1818;
}

.components-notice__content{
  flex-grow:1;
  margin:4px 0 4px 25px;
}

.components-notice__actions{
  display:flex;
  flex-wrap:wrap;
}

.components-notice__action.components-button{
  margin-left:8px;
}
.components-notice__action.components-button,.components-notice__action.components-button.is-link{
  margin-right:12px;
}
.components-notice__action.components-button.is-secondary{
  vertical-align:initial;
}

.components-notice__dismiss{
  align-self:flex-start;
  color:#757575;
  flex-shrink:0;
}
.components-notice__dismiss:not(:disabled):not([aria-disabled=true]):focus,.components-notice__dismiss:not(:disabled):not([aria-disabled=true]):not(.is-secondary):active,.components-notice__dismiss:not(:disabled):not([aria-disabled=true]):not(.is-secondary):hover{
  background-color:initial;
  color:#1e1e1e;
}
.components-notice__dismiss:not(:disabled):not([aria-disabled=true]):not(.is-secondary):hover{
  box-shadow:none;
}

.components-notice-list{
  box-sizing:border-box;
  max-width:100vw;
}
.components-notice-list .components-notice__content{
  line-height:2;
  margin-bottom:12px;
  margin-top:12px;
}
.components-notice-list .components-notice__action.components-button{
  display:block;
  margin-right:0;
  margin-top:8px;
}

.components-panel{
  background:#fff;
  border:1px solid #e0e0e0;
}
.components-panel>.components-panel__body:first-child,.components-panel>.components-panel__header:first-child{
  margin-top:-1px;
}
.components-panel>.components-panel__body:last-child,.components-panel>.components-panel__header:last-child{
  border-bottom-width:0;
}

.components-panel+.components-panel{
  margin-top:-1px;
}

.components-panel__body{
  border-bottom:1px solid #e0e0e0;
  border-top:1px solid #e0e0e0;
}
.components-panel__body h3{
  margin:0 0 .5em;
}
.components-panel__body.is-opened{
  padding:16px;
}

.components-panel__header{
  align-items:center;
  border-bottom:1px solid #ddd;
  box-sizing:initial;
  display:flex;
  flex-shrink:0;
  height:47px;
  justify-content:space-between;
  padding:0 16px;
}
.components-panel__header h2{
  color:inherit;
  font-size:inherit;
  margin:0;
}

.components-panel__body+.components-panel__body,.components-panel__body+.components-panel__header,.components-panel__header+.components-panel__body,.components-panel__header+.components-panel__header{
  margin-top:-1px;
}

.components-panel__body>.components-panel__body-title{
  display:block;
  font-size:inherit;
  margin-bottom:0;
  margin-top:0;
  padding:0;
}
@media not (prefers-reduced-motion){
  .components-panel__body>.components-panel__body-title{
    transition:background .1s ease-in-out;
  }
}

.components-panel__body.is-opened>.components-panel__body-title{
  margin:-16px -16px 5px;
}

.components-panel__body>.components-panel__body-title:hover{
  background:#f0f0f0;
  border:none;
}

.components-panel__body-toggle.components-button{
  border:none;
  box-shadow:none;
  color:#1e1e1e;
  font-weight:500;
  height:auto;
  outline:none;
  padding:16px 16px 16px 48px;
  position:relative;
  text-align:right;
  width:100%;
}
@media not (prefers-reduced-motion){
  .components-panel__body-toggle.components-button{
    transition:background .1s ease-in-out;
  }
}
.components-panel__body-toggle.components-button:focus{
  border-radius:0;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-panel__body-toggle.components-button .components-panel__arrow{
  color:#1e1e1e;
  left:16px;
  position:absolute;
  top:50%;
  transform:translateY(-50%);
  fill:currentColor;
}
@media not (prefers-reduced-motion){
  .components-panel__body-toggle.components-button .components-panel__arrow{
    transition:color .1s ease-in-out;
  }
}
body.rtl .components-panel__body-toggle.components-button .dashicons-arrow-right{
  -ms-filter:fliph;
  filter:FlipH;
  margin-top:-10px;
  transform:scaleX(-1);
}

.components-panel__icon{
  color:#757575;
  margin:-2px 6px -2px 0;
}

.components-panel__body-toggle-icon{
  margin-left:-5px;
}

.components-panel__color-title{
  float:right;
  height:19px;
}

.components-panel__row{
  align-items:center;
  display:flex;
  justify-content:space-between;
  margin-top:8px;
  min-height:36px;
}
.components-panel__row select{
  min-width:0;
}
.components-panel__row label{
  flex-shrink:0;
  margin-left:12px;
  max-width:75%;
}
.components-panel__row:empty,.components-panel__row:first-of-type{
  margin-top:0;
}

.components-panel .circle-picker{
  padding-bottom:20px;
}

.components-placeholder.components-placeholder{
  align-items:flex-start;
  box-sizing:border-box;
  color:#1e1e1e;
  display:flex;
  flex-direction:column;
  font-size:13px;
  gap:16px;
  margin:0;
  padding:24px;
  position:relative;
  text-align:right;
  width:100%;
  -moz-font-smoothing:subpixel-antialiased;
  -webkit-font-smoothing:subpixel-antialiased;
  background-color:#fff;
  border-radius:2px;
  box-shadow:inset 0 0 0 1px #1e1e1e;
  outline:1px solid #0000;
}

.components-placeholder__error,.components-placeholder__fieldset,.components-placeholder__instructions,.components-placeholder__label{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  font-weight:400;
  letter-spacing:normal;
  line-height:normal;
  text-transform:none;
}

.components-placeholder__label{
  align-items:center;
  display:flex;
  font-weight:600;
}
.components-placeholder__label .block-editor-block-icon,.components-placeholder__label .dashicon,.components-placeholder__label>svg{
  margin-left:4px;
  fill:currentColor;
}
@media (forced-colors:active){
  .components-placeholder__label .block-editor-block-icon,.components-placeholder__label .dashicon,.components-placeholder__label>svg{
    fill:CanvasText;
  }
}
.components-placeholder__label:empty{
  display:none;
}

.components-placeholder__fieldset,.components-placeholder__fieldset form{
  display:flex;
  flex-direction:row;
  flex-wrap:wrap;
  gap:16px;
  justify-content:flex-start;
  width:100%;
}
.components-placeholder__fieldset form p,.components-placeholder__fieldset p{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
}

.components-placeholder__fieldset.is-column-layout,.components-placeholder__fieldset.is-column-layout form{
  flex-direction:column;
}

.components-placeholder__input[type=url]{
  border:1px solid #949494;
  border-radius:2px;
  box-shadow:0 0 0 #0000;
  flex:1 1 auto;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  line-height:normal;
  padding:6px 8px;
}
@media not (prefers-reduced-motion){
  .components-placeholder__input[type=url]{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .components-placeholder__input[type=url]{
    font-size:13px;
    line-height:normal;
  }
}
.components-placeholder__input[type=url]:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-placeholder__input[type=url]::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.components-placeholder__input[type=url]::-moz-placeholder{
  color:#1e1e1e9e;
}
.components-placeholder__input[type=url]:-ms-input-placeholder{
  color:#1e1e1e9e;
}

.components-placeholder__error{
  gap:8px;
  width:100%;
}

.components-placeholder__fieldset .components-button:not(.is-link)~.components-button.is-link{
  margin-left:10px;
  margin-right:10px;
}
.components-placeholder__fieldset .components-button:not(.is-link)~.components-button.is-link:last-child{
  margin-left:0;
}

.components-placeholder.is-medium .components-placeholder__instructions,.components-placeholder.is-small .components-placeholder__instructions{
  display:none;
}
.components-placeholder.is-medium .components-placeholder__fieldset,.components-placeholder.is-medium .components-placeholder__fieldset form,.components-placeholder.is-small .components-placeholder__fieldset,.components-placeholder.is-small .components-placeholder__fieldset form{
  flex-direction:column;
}
.components-placeholder.is-medium .components-button,.components-placeholder.is-medium .components-placeholder__fieldset>*,.components-placeholder.is-small .components-button,.components-placeholder.is-small .components-placeholder__fieldset>*{
  justify-content:center;
  width:100%;
}
.components-placeholder.is-small{
  padding:16px;
}
.components-placeholder.has-illustration{
  -webkit-backdrop-filter:blur(100px);
          backdrop-filter:blur(100px);
  backface-visibility:hidden;
  background-color:initial;
  border-radius:0;
  box-shadow:none;
  color:inherit;
  display:flex;
  overflow:hidden;
}
.is-dark-theme .components-placeholder.has-illustration{
  background-color:#0000001a;
}
.components-placeholder.has-illustration .components-placeholder__fieldset{
  margin-left:0;
  margin-right:0;
}
.components-placeholder.has-illustration .components-button,.components-placeholder.has-illustration .components-placeholder__instructions,.components-placeholder.has-illustration .components-placeholder__label{
  opacity:0;
  pointer-events:none;
}
@media not (prefers-reduced-motion){
  .components-placeholder.has-illustration .components-button,.components-placeholder.has-illustration .components-placeholder__instructions,.components-placeholder.has-illustration .components-placeholder__label{
    transition:opacity .1s linear;
  }
}
.is-selected>.components-placeholder.has-illustration .components-button,.is-selected>.components-placeholder.has-illustration .components-placeholder__instructions,.is-selected>.components-placeholder.has-illustration .components-placeholder__label{
  opacity:1;
  pointer-events:auto;
}
.components-placeholder.has-illustration:before{
  background:currentColor;
  bottom:0;
  content:"";
  left:0;
  opacity:.1;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
}
.is-selected .components-placeholder.has-illustration{
  overflow:auto;
}

.components-placeholder__preview{
  display:flex;
  justify-content:center;
}

.components-placeholder__illustration{
  box-sizing:initial;
  height:100%;
  position:absolute;
  right:50%;
  top:50%;
  transform:translate(50%, -50%);
  width:100%;
  stroke:currentColor;
  opacity:.25;
}

.components-popover{
  box-sizing:border-box;
  will-change:transform;
  z-index:1000000;
}
.components-popover *,.components-popover :after,.components-popover :before{
  box-sizing:inherit;
}
.components-popover.is-expanded{
  bottom:0;
  left:0;
  position:fixed;
  right:0;
  top:0;
  z-index:1000000 !important;
}

.components-popover__content{
  background:#fff;
  border-radius:4px;
  box-shadow:0 0 0 1px #ccc,0 2px 3px #0000000d,0 4px 5px #0000000a,0 12px 12px #00000008,0 16px 16px #00000005;
  box-sizing:border-box;
  width:min-content;
}
.is-alternate .components-popover__content{
  border-radius:2px;
  box-shadow:0 0 0 1px #1e1e1e;
}
.is-unstyled .components-popover__content{
  background:none;
  border-radius:0;
  box-shadow:none;
}
.components-popover.is-expanded .components-popover__content{
  box-shadow:0 -1px 0 0 #ccc;
  height:calc(100% - 48px);
  overflow-y:visible;
  position:static;
  width:auto;
}
.components-popover.is-expanded.is-alternate .components-popover__content{
  box-shadow:0 -1px 0 #1e1e1e;
}

.components-popover__header{
  align-items:center;
  background:#fff;
  display:flex;
  height:48px;
  justify-content:space-between;
  padding:0 16px 0 8px;
}

.components-popover__header-title{
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:100%;
}

.components-popover__close.components-button{
  z-index:5;
}

.components-popover__arrow{
  display:flex;
  height:14px;
  pointer-events:none;
  position:absolute;
  width:14px;
}
.components-popover__arrow:before{
  background-color:#fff;
  content:"";
  height:2px;
  left:1px;
  position:absolute;
  right:1px;
  top:-1px;
}
.components-popover__arrow.is-top{
  bottom:-14px !important;
  transform:rotate(0);
}
.components-popover__arrow.is-right{
  left:-14px !important;
  transform:rotate(90deg);
}
.components-popover__arrow.is-bottom{
  top:-14px !important;
  transform:rotate(180deg);
}
.components-popover__arrow.is-left{
  right:-14px !important;
  transform:rotate(-90deg);
}

.components-popover__triangle{
  display:block;
  flex:1;
}

.components-popover__triangle-bg{
  fill:#fff;
}

.components-popover__triangle-border{
  fill:#0000;
  stroke-width:1px;
  stroke:#ccc;
}
.is-alternate .components-popover__triangle-border{
  stroke:#1e1e1e;
}

.components-radio-control{
  border:0;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  margin:0;
  padding:0;
}

.components-radio-control__group-wrapper.has-help{
  margin-block-end:12px;
}

.components-radio-control__option{
  align-items:center;
  column-gap:8px;
  display:grid;
  grid-template-columns:auto 1fr;
  grid-template-rows:auto minmax(0, max-content);
}

.components-radio-control__input[type=radio]{
  appearance:none;
  border:1px solid #1e1e1e;
  border-radius:2px;
  border-radius:50%;
  box-shadow:0 0 0 #0000;
  cursor:pointer;
  display:inline-flex;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  grid-column:1;
  grid-row:1;
  height:24px;
  line-height:normal;
  margin:0;
  max-width:24px;
  min-width:24px;
  padding:0;
  position:relative;
  transition:none;
  width:24px;
}
@media not (prefers-reduced-motion){
  .components-radio-control__input[type=radio]{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .components-radio-control__input[type=radio]{
    font-size:13px;
    line-height:normal;
  }
}
.components-radio-control__input[type=radio]:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
}
.components-radio-control__input[type=radio]::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.components-radio-control__input[type=radio]::-moz-placeholder{
  color:#1e1e1e9e;
}
.components-radio-control__input[type=radio]:-ms-input-placeholder{
  color:#1e1e1e9e;
}
@media (min-width:600px){
  .components-radio-control__input[type=radio]{
    height:16px;
    max-width:16px;
    min-width:16px;
    width:16px;
  }
}
.components-radio-control__input[type=radio]:checked:before{
  background-color:#fff;
  border:4px solid #fff;
  box-sizing:inherit;
  height:12px;
  margin:0;
  position:absolute;
  right:50%;
  top:50%;
  transform:translate(50%, -50%);
  width:12px;
}
@media (min-width:600px){
  .components-radio-control__input[type=radio]:checked:before{
    height:8px;
    width:8px;
  }
}
.components-radio-control__input[type=radio]:focus{
  box-shadow:0 0 0 2px #fff, 0 0 0 4px var(--wp-admin-theme-color);
}
.components-radio-control__input[type=radio]:checked{
  background:var(--wp-admin-theme-color);
  border:none;
}
.components-radio-control__input[type=radio]:focus{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #fff, 0 0 0 calc(var(--wp-admin-border-width-focus)*2) var(--wp-admin-theme-color);
  outline:2px solid #0000;
  outline-offset:2px;
}
.components-radio-control__input[type=radio]:checked{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  border-color:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-radio-control__input[type=radio]:checked:before{
  border-radius:50%;
  content:"";
}

.components-radio-control__label{
  cursor:pointer;
  grid-column:2;
  grid-row:1;
  line-height:24px;
}
@media (min-width:600px){
  .components-radio-control__label{
    line-height:16px;
  }
}

.components-radio-control__option-description{
  grid-column:2;
  grid-row:2;
  padding-block-start:4px;
}
.components-radio-control__option-description.components-radio-control__option-description{
  margin-top:0;
}

.components-resizable-box__handle{
  display:none;
  height:23px;
  width:23px;
  z-index:2;
}
.components-resizable-box__container.has-show-handle .components-resizable-box__handle{
  display:block;
}
.components-resizable-box__handle>div{
  height:100%;
  outline:none;
  position:relative;
  width:100%;
  z-index:2;
}

.components-resizable-box__container>img{
  width:inherit;
}

.components-resizable-box__handle:after{
  background:#fff;
  border-radius:50%;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9)), 0 1px 1px #00000008, 0 1px 2px #00000005, 0 3px 3px #00000005, 0 4px 4px #00000003;
  content:"";
  cursor:inherit;
  display:block;
  height:15px;
  left:calc(50% - 8px);
  outline:2px solid #0000;
  position:absolute;
  top:calc(50% - 8px);
  width:15px;
}

.components-resizable-box__side-handle:before{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  border-radius:9999px;
  content:"";
  cursor:inherit;
  display:block;
  height:3px;
  left:calc(50% - 1px);
  opacity:0;
  position:absolute;
  top:calc(50% - 1px);
  width:3px;
}
@media not (prefers-reduced-motion){
  .components-resizable-box__side-handle:before{
    transition:transform .1s ease-in;
    will-change:transform;
  }
}

.components-resizable-box__corner-handle,.components-resizable-box__side-handle{
  z-index:2;
}

.components-resizable-box__side-handle.components-resizable-box__handle-bottom,.components-resizable-box__side-handle.components-resizable-box__handle-bottom:before,.components-resizable-box__side-handle.components-resizable-box__handle-top,.components-resizable-box__side-handle.components-resizable-box__handle-top:before{
  border-left:0;
  border-right:0;
  right:0;
  width:100%;
}

.components-resizable-box__side-handle.components-resizable-box__handle-left,.components-resizable-box__side-handle.components-resizable-box__handle-left:before,.components-resizable-box__side-handle.components-resizable-box__handle-right,.components-resizable-box__side-handle.components-resizable-box__handle-right:before{
  border-bottom:0;
  border-top:0;
  height:100%;
  top:0;
}

@media not (prefers-reduced-motion){
  .components-resizable-box__side-handle.components-resizable-box__handle-bottom:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-bottom:hover:before,.components-resizable-box__side-handle.components-resizable-box__handle-top:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-top:hover:before{
    animation:components-resizable-box__top-bottom-animation .1s ease-out 0s;
    animation-fill-mode:forwards;
  }
  .components-resizable-box__side-handle.components-resizable-box__handle-left:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-left:hover:before,.components-resizable-box__side-handle.components-resizable-box__handle-right:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-right:hover:before{
    animation:components-resizable-box__left-right-animation .1s ease-out 0s;
    animation-fill-mode:forwards;
  }
}
@media not all and (min-resolution:0.001dpcm){
  @supports (-webkit-appearance:none){
    .components-resizable-box__side-handle.components-resizable-box__handle-bottom:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-bottom:hover:before,.components-resizable-box__side-handle.components-resizable-box__handle-left:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-left:hover:before,.components-resizable-box__side-handle.components-resizable-box__handle-right:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-right:hover:before,.components-resizable-box__side-handle.components-resizable-box__handle-top:active:before,.components-resizable-box__side-handle.components-resizable-box__handle-top:hover:before{
      animation:none;
    }
  }
}
@keyframes components-resizable-box__top-bottom-animation{
  0%{
    opacity:0;
    transform:scaleX(0);
  }
  to{
    opacity:1;
    transform:scaleX(1);
  }
}
@keyframes components-resizable-box__left-right-animation{
  0%{
    opacity:0;
    transform:scaleY(0);
  }
  to{
    opacity:1;
    transform:scaleY(1);
  }
}
.components-resizable-box__handle-right{
  right:-11.5px;
}

.components-resizable-box__handle-left{
  left:-11.5px;
}

.components-resizable-box__handle-top{
  top:-11.5px;
}

.components-resizable-box__handle-bottom{
  bottom:-11.5px;
}
.components-responsive-wrapper{
  align-items:center;
  display:flex;
  justify-content:center;
  max-width:100%;
  position:relative;
}

.components-responsive-wrapper__content{
  display:block;
  max-width:100%;
  width:100%;
}

.components-sandbox{
  overflow:hidden;
}

iframe.components-sandbox{
  width:100%;
}

body.lockscroll,html.lockscroll{
  overflow:hidden;
}

.components-select-control__input{
  outline:0;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0) !important;
}

@media (max-width:782px){
  .components-base-control .components-base-control__field .components-select-control__input{
    font-size:16px;
  }
}
.components-snackbar{
  -webkit-backdrop-filter:blur(16px) saturate(180%);
          backdrop-filter:blur(16px) saturate(180%);
  background:#000000d9;
  border-radius:4px;
  box-shadow:0 1px 2px #0000000d,0 2px 3px #0000000a,0 6px 6px #00000008,0 8px 8px #00000005;
  box-sizing:border-box;
  color:#fff;
  cursor:pointer;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  max-width:600px;
  padding:12px 20px;
  pointer-events:auto;
  width:100%;
}
@media (min-width:600px){
  .components-snackbar{
    width:fit-content;
  }
}
.components-snackbar:focus{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
}
.components-snackbar.components-snackbar-explicit-dismiss{
  cursor:default;
}
.components-snackbar .components-snackbar__content-with-icon{
  padding-right:24px;
  position:relative;
}
.components-snackbar .components-snackbar__icon{
  position:absolute;
  right:-8px;
  top:-2.9px;
}
.components-snackbar .components-snackbar__dismiss-button{
  cursor:pointer;
  margin-right:24px;
}

.components-snackbar__action.components-button{
  color:#fff;
  flex-shrink:0;
  margin-right:32px;
}
.components-snackbar__action.components-button:focus{
  box-shadow:none;
  outline:1px dotted #fff;
}
.components-snackbar__action.components-button:hover{
  color:currentColor;
  text-decoration:none;
}

.components-snackbar__content{
  align-items:baseline;
  display:flex;
  justify-content:space-between;
  line-height:1.4;
}

.components-snackbar-list{
  box-sizing:border-box;
  pointer-events:none;
  position:absolute;
  width:100%;
  z-index:100000;
}

.components-snackbar-list__notice-container{
  padding-top:8px;
  position:relative;
}

.components-tab-panel__tabs{
  align-items:stretch;
  display:flex;
  flex-direction:row;
}
.components-tab-panel__tabs[aria-orientation=vertical]{
  flex-direction:column;
}

.components-tab-panel__tabs-item{
  background:#0000;
  border:none;
  border-radius:0;
  box-shadow:none;
  cursor:pointer;
  font-weight:500;
  height:48px !important;
  margin-right:0;
  padding:3px 16px;
  position:relative;
}
.components-tab-panel__tabs-item:focus:not(:disabled){
  box-shadow:none;
  outline:none;
  position:relative;
}
.components-tab-panel__tabs-item:after{
  background:var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  border-radius:0;
  bottom:0;
  content:"";
  height:calc(var(--wp-admin-border-width-focus)*0);
  left:0;
  pointer-events:none;
  position:absolute;
  right:0;
}
@media not (prefers-reduced-motion){
  .components-tab-panel__tabs-item:after{
    transition:all .1s linear;
  }
}
.components-tab-panel__tabs-item.is-active:after{
  height:calc(var(--wp-admin-border-width-focus)*1);
  outline:2px solid #0000;
  outline-offset:-1px;
}
.components-tab-panel__tabs-item:before{
  border-radius:2px;
  bottom:12px;
  box-shadow:0 0 0 0 #0000;
  content:"";
  left:12px;
  pointer-events:none;
  position:absolute;
  right:12px;
  top:12px;
}
@media not (prefers-reduced-motion){
  .components-tab-panel__tabs-item:before{
    transition:all .1s linear;
  }
}
.components-tab-panel__tabs-item:focus-visible:before{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:2px solid #0000;
}

.components-tab-panel__tab-content:focus{
  box-shadow:none;
  outline:none;
}
.components-tab-panel__tab-content:focus-visible{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent, var(--wp-admin-theme-color, #3858e9));
  outline:2px solid #0000;
  outline-offset:0;
}

.components-text-control__input,.components-text-control__input[type=color],.components-text-control__input[type=date],.components-text-control__input[type=datetime-local],.components-text-control__input[type=datetime],.components-text-control__input[type=email],.components-text-control__input[type=month],.components-text-control__input[type=number],.components-text-control__input[type=password],.components-text-control__input[type=tel],.components-text-control__input[type=text],.components-text-control__input[type=time],.components-text-control__input[type=url],.components-text-control__input[type=week]{
  border:1px solid #949494;
  border-radius:2px;
  box-shadow:0 0 0 #0000;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:16px;
  height:32px;
  line-height:normal;
  margin:0;
  padding:6px 8px;
  width:100%;
}
@media not (prefers-reduced-motion){
  .components-text-control__input,.components-text-control__input[type=color],.components-text-control__input[type=date],.components-text-control__input[type=datetime-local],.components-text-control__input[type=datetime],.components-text-control__input[type=email],.components-text-control__input[type=month],.components-text-control__input[type=number],.components-text-control__input[type=password],.components-text-control__input[type=tel],.components-text-control__input[type=text],.components-text-control__input[type=time],.components-text-control__input[type=url],.components-text-control__input[type=week]{
    transition:box-shadow .1s linear;
  }
}
@media (min-width:600px){
  .components-text-control__input,.components-text-control__input[type=color],.components-text-control__input[type=date],.components-text-control__input[type=datetime-local],.components-text-control__input[type=datetime],.components-text-control__input[type=email],.components-text-control__input[type=month],.components-text-control__input[type=number],.components-text-control__input[type=password],.components-text-control__input[type=tel],.components-text-control__input[type=text],.components-text-control__input[type=time],.components-text-control__input[type=url],.components-text-control__input[type=week]{
    font-size:13px;
    line-height:normal;
  }
}
.components-text-control__input:focus,.components-text-control__input[type=color]:focus,.components-text-control__input[type=date]:focus,.components-text-control__input[type=datetime-local]:focus,.components-text-control__input[type=datetime]:focus,.components-text-control__input[type=email]:focus,.components-text-control__input[type=month]:focus,.components-text-control__input[type=number]:focus,.components-text-control__input[type=password]:focus,.components-text-control__input[type=tel]:focus,.components-text-control__input[type=text]:focus,.components-text-control__input[type=time]:focus,.components-text-control__input[type=url]:focus,.components-text-control__input[type=week]:focus{
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 .5px var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-text-control__input::-webkit-input-placeholder,.components-text-control__input[type=color]::-webkit-input-placeholder,.components-text-control__input[type=date]::-webkit-input-placeholder,.components-text-control__input[type=datetime-local]::-webkit-input-placeholder,.components-text-control__input[type=datetime]::-webkit-input-placeholder,.components-text-control__input[type=email]::-webkit-input-placeholder,.components-text-control__input[type=month]::-webkit-input-placeholder,.components-text-control__input[type=number]::-webkit-input-placeholder,.components-text-control__input[type=password]::-webkit-input-placeholder,.components-text-control__input[type=tel]::-webkit-input-placeholder,.components-text-control__input[type=text]::-webkit-input-placeholder,.components-text-control__input[type=time]::-webkit-input-placeholder,.components-text-control__input[type=url]::-webkit-input-placeholder,.components-text-control__input[type=week]::-webkit-input-placeholder{
  color:#1e1e1e9e;
}
.components-text-control__input::-moz-placeholder,.components-text-control__input[type=color]::-moz-placeholder,.components-text-control__input[type=date]::-moz-placeholder,.components-text-control__input[type=datetime-local]::-moz-placeholder,.components-text-control__input[type=datetime]::-moz-placeholder,.components-text-control__input[type=email]::-moz-placeholder,.components-text-control__input[type=month]::-moz-placeholder,.components-text-control__input[type=number]::-moz-placeholder,.components-text-control__input[type=password]::-moz-placeholder,.components-text-control__input[type=tel]::-moz-placeholder,.components-text-control__input[type=text]::-moz-placeholder,.components-text-control__input[type=time]::-moz-placeholder,.components-text-control__input[type=url]::-moz-placeholder,.components-text-control__input[type=week]::-moz-placeholder{
  color:#1e1e1e9e;
}
.components-text-control__input:-ms-input-placeholder,.components-text-control__input[type=color]:-ms-input-placeholder,.components-text-control__input[type=date]:-ms-input-placeholder,.components-text-control__input[type=datetime-local]:-ms-input-placeholder,.components-text-control__input[type=datetime]:-ms-input-placeholder,.components-text-control__input[type=email]:-ms-input-placeholder,.components-text-control__input[type=month]:-ms-input-placeholder,.components-text-control__input[type=number]:-ms-input-placeholder,.components-text-control__input[type=password]:-ms-input-placeholder,.components-text-control__input[type=tel]:-ms-input-placeholder,.components-text-control__input[type=text]:-ms-input-placeholder,.components-text-control__input[type=time]:-ms-input-placeholder,.components-text-control__input[type=url]:-ms-input-placeholder,.components-text-control__input[type=week]:-ms-input-placeholder{
  color:#1e1e1e9e;
}
.components-text-control__input.is-next-40px-default-size,.components-text-control__input[type=color].is-next-40px-default-size,.components-text-control__input[type=date].is-next-40px-default-size,.components-text-control__input[type=datetime-local].is-next-40px-default-size,.components-text-control__input[type=datetime].is-next-40px-default-size,.components-text-control__input[type=email].is-next-40px-default-size,.components-text-control__input[type=month].is-next-40px-default-size,.components-text-control__input[type=number].is-next-40px-default-size,.components-text-control__input[type=password].is-next-40px-default-size,.components-text-control__input[type=tel].is-next-40px-default-size,.components-text-control__input[type=text].is-next-40px-default-size,.components-text-control__input[type=time].is-next-40px-default-size,.components-text-control__input[type=url].is-next-40px-default-size,.components-text-control__input[type=week].is-next-40px-default-size{
  height:40px;
  padding-left:12px;
  padding-right:12px;
}

.components-text-control__input[type=email],.components-text-control__input[type=url]{
  direction:ltr;
}

.components-tip{
  color:#757575;
  display:flex;
}
.components-tip svg{
  align-self:center;
  fill:#f0b849;
  flex-shrink:0;
  margin-left:16px;
}
.components-tip p{
  margin:0;
}

.components-toggle-control__label{
  line-height:16px;
}
.components-toggle-control__label:not(.is-disabled){
  cursor:pointer;
}

.components-toggle-control__help{
  display:inline-block;
  margin-inline-start:40px;
}

.components-accessible-toolbar{
  border:1px solid #1e1e1e;
  border-radius:2px;
  display:inline-flex;
  flex-shrink:0;
}
.components-accessible-toolbar>.components-toolbar-group:last-child{
  border-left:none;
}
.components-accessible-toolbar.is-unstyled{
  border:none;
}
.components-accessible-toolbar.is-unstyled>.components-toolbar-group{
  border-left:none;
}

.components-accessible-toolbar[aria-orientation=vertical],.components-toolbar[aria-orientation=vertical]{
  align-items:center;
  display:flex;
  flex-direction:column;
}
.components-accessible-toolbar .components-button,.components-toolbar .components-button{
  height:48px;
  padding-left:16px;
  padding-right:16px;
  position:relative;
  z-index:1;
}
.components-accessible-toolbar .components-button:focus:not(:disabled),.components-toolbar .components-button:focus:not(:disabled){
  box-shadow:none;
  outline:none;
}
.components-accessible-toolbar .components-button:before,.components-toolbar .components-button:before{
  border-radius:2px;
  content:"";
  display:block;
  height:32px;
  left:8px;
  position:absolute;
  right:8px;
  z-index:-1;
}
@media not (prefers-reduced-motion){
  .components-accessible-toolbar .components-button:before,.components-toolbar .components-button:before{
    animation:components-button__appear-animation .1s ease;
    animation-fill-mode:forwards;
  }
}
.components-accessible-toolbar .components-button svg,.components-toolbar .components-button svg{
  margin-left:auto;
  margin-right:auto;
  position:relative;
}
.components-accessible-toolbar .components-button.is-pressed,.components-accessible-toolbar .components-button.is-pressed:hover,.components-toolbar .components-button.is-pressed,.components-toolbar .components-button.is-pressed:hover{
  background:#0000;
}
.components-accessible-toolbar .components-button.is-pressed:before,.components-toolbar .components-button.is-pressed:before{
  background:#1e1e1e;
}
.components-accessible-toolbar .components-button:focus:before,.components-toolbar .components-button:focus:before{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.components-accessible-toolbar .components-button.has-icon.has-icon,.components-toolbar .components-button.has-icon.has-icon{
  min-width:48px;
  padding-left:8px;
  padding-right:8px;
}

@keyframes components-button__appear-animation{
  0%{
    transform:scaleY(0);
  }
  to{
    transform:scaleY(1);
  }
}
.components-toolbar__control.components-button{
  position:relative;
}
.components-toolbar__control.components-button[data-subscript] svg{
  padding:5px 0 5px 10px;
}
.components-toolbar__control.components-button[data-subscript]:after{
  bottom:10px;
  content:attr(data-subscript);
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  font-weight:600;
  left:8px;
  line-height:12px;
  position:absolute;
}
.components-toolbar__control.components-button:not(:disabled).is-pressed[data-subscript]:after{
  color:#fff;
}

.components-toolbar-group{
  background-color:#fff;
  border-left:1px solid #1e1e1e;
  display:inline-flex;
  flex-shrink:0;
  flex-wrap:wrap;
  line-height:0;
  min-height:48px;
  padding-left:6px;
  padding-right:6px;
}
.components-toolbar-group .components-toolbar-group.components-toolbar-group{
  border-width:0;
  margin:0;
}
.components-toolbar-group .components-button.components-button,.components-toolbar-group .components-button.has-icon.has-icon{
  justify-content:center;
  min-width:36px;
  padding-left:6px;
  padding-right:6px;
}
.components-toolbar-group .components-button.components-button svg,.components-toolbar-group .components-button.has-icon.has-icon svg{
  min-width:24px;
}
.components-toolbar-group .components-button.components-button:before,.components-toolbar-group .components-button.has-icon.has-icon:before{
  left:2px;
  right:2px;
}

.components-toolbar{
  background-color:#fff;
  border:1px solid #1e1e1e;
  display:inline-flex;
  flex-shrink:0;
  flex-wrap:wrap;
  margin:0;
  min-height:48px;
}
.components-toolbar .components-toolbar.components-toolbar{
  border-width:0;
  margin:0;
}

div.components-toolbar>div{
  display:flex;
  margin:0;
}
div.components-toolbar>div+div.has-left-divider{
  margin-right:6px;
  overflow:visible;
  position:relative;
}
div.components-toolbar>div+div.has-left-divider:before{
  background-color:#ddd;
  box-sizing:initial;
  content:"";
  display:inline-block;
  height:20px;
  position:absolute;
  right:-3px;
  top:8px;
  width:1px;
}

.components-tooltip{
  background:#000;
  border-radius:2px;
  box-shadow:0 1px 2px #0000000d,0 2px 3px #0000000a,0 6px 6px #00000008,0 8px 8px #00000005;
  color:#f0f0f0;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:12px;
  line-height:1.4;
  padding:4px 8px;
  text-align:center;
  z-index:1000002;
}

.components-tooltip__shortcut{
  margin-right:8px;
}
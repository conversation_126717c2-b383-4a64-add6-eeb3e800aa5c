<?php
/**
 * Dashboard Opportunities Template Part
 *
 * Displays live opportunities based on user's country and sector
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$user_id = get_current_user_id();
$user_country = get_user_meta($user_id, 'chatgabi_country', true) ?: '';
$user_sector = get_user_meta($user_id, 'chatgabi_industry', true) ?: '';

// Handle filter preferences reset
if (isset($_POST['reset_filter_preferences']) && wp_verify_nonce($_POST['_wpnonce'], 'reset_filter_preferences')) {
    delete_user_meta($user_id, 'opp_filter_country');
    delete_user_meta($user_id, 'opp_filter_type');
    delete_user_meta($user_id, 'opp_filter_sector');
    delete_user_meta($user_id, 'opp_filter_search');
    delete_user_meta($user_id, 'opp_filter_deadline_sort');

    // Set a transient message for user feedback
    set_transient('chatgabi_preferences_reset_' . $user_id, true, 30);

    // Redirect to clean URL
    wp_redirect(remove_query_arg(['filter_country', 'filter_type', 'filter_sector', 'filter_search', 'filter_deadline_sort']));
    exit;
}

// Check for preference messages
$preferences_reset = get_transient('chatgabi_preferences_reset_' . $user_id);
if ($preferences_reset) {
    delete_transient('chatgabi_preferences_reset_' . $user_id);
}

$preferences_saved = false;
if (!empty($_GET) && array_intersect_key($_GET, array_flip(['filter_country', 'filter_type', 'filter_sector', 'filter_search', 'filter_deadline_sort']))) {
    $preferences_saved = true;
}

// Get filter parameters from URL or user preferences
$filter_country = $_GET['filter_country'] ?? get_user_meta($user_id, 'opp_filter_country', true) ?: $user_country;
$filter_type = $_GET['filter_type'] ?? get_user_meta($user_id, 'opp_filter_type', true) ?: '';
$filter_sector = $_GET['filter_sector'] ?? get_user_meta($user_id, 'opp_filter_sector', true) ?: '';
$filter_search = $_GET['filter_search'] ?? get_user_meta($user_id, 'opp_filter_search', true) ?: '';
$filter_deadline_sort = $_GET['filter_deadline_sort'] ?? get_user_meta($user_id, 'opp_filter_deadline_sort', true) ?: 'soonest';

// Save current filter preferences if any GET parameters are present
if (!empty($_GET) && array_intersect_key($_GET, array_flip(['filter_country', 'filter_type', 'filter_sector', 'filter_search', 'filter_deadline_sort']))) {
    update_user_meta($user_id, 'opp_filter_country', sanitize_text_field($filter_country));
    update_user_meta($user_id, 'opp_filter_type', sanitize_text_field($filter_type));
    update_user_meta($user_id, 'opp_filter_sector', sanitize_text_field($filter_sector));
    update_user_meta($user_id, 'opp_filter_search', sanitize_text_field($filter_search));
    update_user_meta($user_id, 'opp_filter_deadline_sort', sanitize_text_field($filter_deadline_sort));
}

// Load opportunities
$opportunities = array();
$fallback_message = '';
$all_opportunities = array(); // Store unfiltered opportunities for statistics

if (!empty($filter_country)) {
    // Load opportunities using the opportunity loader
    if (function_exists('load_opportunities_by_country_sector')) {
        // Load all opportunities for the country first
        $all_opportunities = load_opportunities_by_country_sector($filter_country);
        $opportunities = $all_opportunities;

        // Apply sector filter if specified
        if (!empty($filter_sector) && function_exists('filter_opportunities_by_sector')) {
            $opportunities = filter_opportunities_by_sector($opportunities, $filter_sector);
        }

        // Apply type filter if specified
        if (!empty($filter_type)) {
            $opportunities = array_filter($opportunities, function($opp) use ($filter_type) {
                return isset($opp['type']) && strtolower(trim($opp['type'])) === strtolower(trim($filter_type));
            });
        }

        // Apply search filter if specified
        if (!empty($filter_search)) {
            $search_term = strtolower(trim($filter_search));
            $opportunities = array_filter($opportunities, function($opp) use ($search_term) {
                $searchable_text = strtolower(
                    ($opp['title'] ?? '') . ' ' .
                    ($opp['summary'] ?? '') . ' ' .
                    ($opp['sector'] ?? '') . ' ' .
                    ($opp['type'] ?? '')
                );
                return strpos($searchable_text, $search_term) !== false;
            });
        }

        // Apply deadline sorting
        if ($filter_deadline_sort === 'soonest') {
            usort($opportunities, function($a, $b) {
                $deadline_a = isset($a['deadline']) ? strtotime($a['deadline']) : PHP_INT_MAX;
                $deadline_b = isset($b['deadline']) ? strtotime($b['deadline']) : PHP_INT_MAX;
                return $deadline_a - $deadline_b;
            });
        } elseif ($filter_deadline_sort === 'latest') {
            $opportunities = array_reverse($opportunities);
        }

        // Set fallback message if no results after filtering
        if (empty($opportunities) && !empty($all_opportunities)) {
            $fallback_message = __('No opportunities match your current filters. Try adjusting your search criteria.', 'chatgabi');
        } elseif (empty($all_opportunities)) {
            $fallback_message = sprintf(
                __('No current opportunities available for %s. Please check back later.', 'chatgabi'),
                $filter_country
            );
        }
    }
} else {
    $fallback_message = __('Please select a country to see relevant opportunities.', 'chatgabi');
}

// Get available countries for filter
$available_countries = function_exists('get_available_opportunity_countries') ? get_available_opportunity_countries() : array('Ghana', 'Kenya', 'Nigeria', 'South Africa');

// Get available sectors for the selected country
$available_sectors = array();
if (!empty($filter_country) && function_exists('get_available_sectors_by_country')) {
    $available_sectors = get_available_sectors_by_country($filter_country) ?: array();
}

// Get available types from current opportunities
$available_types = array();
if (!empty($all_opportunities)) {
    foreach ($all_opportunities as $opp) {
        if (isset($opp['type']) && !in_array($opp['type'], $available_types)) {
            $available_types[] = $opp['type'];
        }
    }
    sort($available_types);
}
?>

<div class="opportunities-section">
    <div class="opportunities-header">
        <h2><?php _e('Live Opportunities', 'chatgabi'); ?></h2>
        <p><?php _e('Discover current business opportunities, grants, and programs in your region.', 'chatgabi'); ?></p>
    </div>

    <!-- Preference Notifications -->
    <?php if ($preferences_reset): ?>
        <div class="preference-notification success">
            <span class="notification-icon">✅</span>
            <span class="notification-text"><?php _e('Filter preferences have been reset successfully.', 'chatgabi'); ?></span>
        </div>
    <?php endif; ?>

    <?php if ($preferences_saved): ?>
        <div class="preference-notification info">
            <span class="notification-icon">💾</span>
            <span class="notification-text"><?php _e('Your filter preferences have been saved and will be remembered for next time.', 'chatgabi'); ?></span>
        </div>
    <?php endif; ?>

    <!-- Smart Filters -->
    <form method="GET" class="opportunities-filters" id="opportunity-filter-form">
        <!-- Preserve any existing URL parameters -->
        <?php foreach ($_GET as $key => $value): ?>
            <?php if (!in_array($key, ['filter_country', 'filter_type', 'filter_sector', 'filter_search', 'filter_deadline_sort'])): ?>
                <input type="hidden" name="<?php echo esc_attr($key); ?>" value="<?php echo esc_attr($value); ?>">
            <?php endif; ?>
        <?php endforeach; ?>

        <div class="filter-row">
            <div class="filter-group">
                <label for="filter_country"><?php _e('Country:', 'businesscraft-ai'); ?></label>
                <select name="filter_country" id="filter_country" class="filter-select">
                    <option value=""><?php _e('Select Country', 'businesscraft-ai'); ?></option>
                    <?php foreach ($available_countries as $country): ?>
                        <option value="<?php echo esc_attr($country); ?>" <?php selected($filter_country, $country); ?>>
                            <?php echo esc_html($country); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="filter_type"><?php _e('Type:', 'businesscraft-ai'); ?></label>
                <select name="filter_type" id="filter_type" class="filter-select">
                    <option value=""><?php _e('All Types', 'businesscraft-ai'); ?></option>
                    <?php if (!empty($available_types)): ?>
                        <?php foreach ($available_types as $type): ?>
                            <option value="<?php echo esc_attr($type); ?>" <?php selected($filter_type, $type); ?>>
                                <?php echo esc_html($type); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback static options -->
                        <option value="Grant" <?php selected($filter_type, 'Grant'); ?>><?php _e('Grant', 'businesscraft-ai'); ?></option>
                        <option value="Loan" <?php selected($filter_type, 'Loan'); ?>><?php _e('Loan', 'businesscraft-ai'); ?></option>
                        <option value="Incubator" <?php selected($filter_type, 'Incubator'); ?>><?php _e('Incubator', 'businesscraft-ai'); ?></option>
                        <option value="Accelerator" <?php selected($filter_type, 'Accelerator'); ?>><?php _e('Accelerator', 'businesscraft-ai'); ?></option>
                        <option value="Competition" <?php selected($filter_type, 'Competition'); ?>><?php _e('Competition', 'businesscraft-ai'); ?></option>
                        <option value="Tax Incentive" <?php selected($filter_type, 'Tax Incentive'); ?>><?php _e('Tax Incentive', 'businesscraft-ai'); ?></option>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="filter_sector"><?php _e('Sector:', 'businesscraft-ai'); ?></label>
                <select name="filter_sector" id="filter_sector" class="filter-select">
                    <option value=""><?php _e('All Sectors', 'businesscraft-ai'); ?></option>
                    <?php if (!empty($available_sectors)): ?>
                        <?php foreach ($available_sectors as $sector): ?>
                            <option value="<?php echo esc_attr($sector); ?>" <?php selected($filter_sector, $sector); ?>>
                                <?php echo esc_html($sector); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="filter_deadline_sort"><?php _e('Sort by:', 'businesscraft-ai'); ?></label>
                <select name="filter_deadline_sort" id="filter_deadline_sort" class="filter-select">
                    <option value="soonest" <?php selected($filter_deadline_sort, 'soonest'); ?>><?php _e('Ending Soon', 'businesscraft-ai'); ?></option>
                    <option value="latest" <?php selected($filter_deadline_sort, 'latest'); ?>><?php _e('Most Recent', 'businesscraft-ai'); ?></option>
                </select>
            </div>
        </div>

        <div class="filter-row">
            <div class="filter-group search-group">
                <label for="filter_search"><?php _e('Search:', 'businesscraft-ai'); ?></label>
                <input type="text"
                       name="filter_search"
                       id="filter_search"
                       class="filter-search"
                       placeholder="<?php _e('Search by keyword, title, or description...', 'businesscraft-ai'); ?>"
                       value="<?php echo esc_attr($filter_search); ?>">
            </div>

            <div class="filter-actions">
                <button type="submit" class="btn-primary">
                    <span class="filter-icon">🔍</span>
                    <?php _e('Apply Filters', 'businesscraft-ai'); ?>
                </button>
                <a href="<?php echo esc_url(remove_query_arg(['filter_country', 'filter_type', 'filter_sector', 'filter_search', 'filter_deadline_sort'])); ?>"
                   class="btn-secondary">
                    <span class="filter-icon">🔄</span>
                    <?php _e('Clear All', 'businesscraft-ai'); ?>
                </a>
            </div>
        </div>

        <!-- Filter Summary -->
        <?php if (!empty($filter_country) || !empty($filter_type) || !empty($filter_sector) || !empty($filter_search)): ?>
        <div class="filter-summary">
            <span class="filter-summary-label"><?php _e('Active Filters:', 'businesscraft-ai'); ?></span>
            <?php if (!empty($filter_country)): ?>
                <span class="filter-tag">
                    <?php _e('Country:', 'businesscraft-ai'); ?> <?php echo esc_html($filter_country); ?>
                    <a href="<?php echo esc_url(remove_query_arg('filter_country')); ?>" class="filter-remove">×</a>
                </span>
            <?php endif; ?>
            <?php if (!empty($filter_type)): ?>
                <span class="filter-tag">
                    <?php _e('Type:', 'businesscraft-ai'); ?> <?php echo esc_html($filter_type); ?>
                    <a href="<?php echo esc_url(remove_query_arg('filter_type')); ?>" class="filter-remove">×</a>
                </span>
            <?php endif; ?>
            <?php if (!empty($filter_sector)): ?>
                <span class="filter-tag">
                    <?php _e('Sector:', 'businesscraft-ai'); ?> <?php echo esc_html($filter_sector); ?>
                    <a href="<?php echo esc_url(remove_query_arg('filter_sector')); ?>" class="filter-remove">×</a>
                </span>
            <?php endif; ?>
            <?php if (!empty($filter_search)): ?>
                <span class="filter-tag">
                    <?php _e('Search:', 'businesscraft-ai'); ?> "<?php echo esc_html($filter_search); ?>"
                    <a href="<?php echo esc_url(remove_query_arg('filter_search')); ?>" class="filter-remove">×</a>
                </span>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Results Summary -->
        <div class="results-summary">
            <div class="results-info">
                <span class="results-count">
                    <?php
                    printf(
                        _n(
                            'Showing %d opportunity',
                            'Showing %d opportunities',
                            count($opportunities),
                            'businesscraft-ai'
                        ),
                        count($opportunities)
                    );
                    ?>
                    <?php if (!empty($all_opportunities) && count($opportunities) !== count($all_opportunities)): ?>
                        <?php printf(__(' of %d total', 'businesscraft-ai'), count($all_opportunities)); ?>
                    <?php endif; ?>
                </span>

                <?php
                // Check if user has saved preferences
                $has_saved_preferences = get_user_meta($user_id, 'opp_filter_country', true) ||
                                        get_user_meta($user_id, 'opp_filter_type', true) ||
                                        get_user_meta($user_id, 'opp_filter_sector', true) ||
                                        get_user_meta($user_id, 'opp_filter_search', true) ||
                                        get_user_meta($user_id, 'opp_filter_deadline_sort', true);

                if ($has_saved_preferences && empty($_GET)): ?>
                    <span class="preference-indicator">
                        <span class="preference-icon">💾</span>
                        <?php _e('Using your saved preferences', 'businesscraft-ai'); ?>
                    </span>
                <?php endif; ?>
            </div>

            <?php if ($has_saved_preferences): ?>
            <div class="preference-actions">
                <form method="POST" style="display: inline;">
                    <?php wp_nonce_field('reset_filter_preferences'); ?>
                    <input type="hidden" name="reset_filter_preferences" value="1">
                    <button type="submit" class="btn-text reset-preferences-btn"
                            onclick="return confirm('<?php _e('Are you sure you want to reset your saved filter preferences?', 'businesscraft-ai'); ?>')">
                        <span class="reset-icon">🔄</span>
                        <?php _e('Reset Saved Preferences', 'businesscraft-ai'); ?>
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </form>

    <!-- Fallback Message -->
    <?php if (!empty($fallback_message)): ?>
        <div class="opportunities-notice">
            <p><?php echo esc_html($fallback_message); ?></p>
            <?php if (empty($user_country)): ?>
                <a href="<?php echo home_url('/preferences/'); ?>" class="btn-secondary">
                    <?php _e('Set Your Country', 'businesscraft-ai'); ?>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Opportunities Grid -->
    <div class="opportunities-grid" id="opportunities-container">
        <?php if (!empty($opportunities)): ?>
            <?php foreach ($opportunities as $opportunity): ?>
                <div class="opportunity-card"
                     data-country="<?php echo esc_attr($opportunity['country'] ?? ''); ?>"
                     data-type="<?php echo esc_attr($opportunity['type'] ?? ''); ?>"
                     data-sector="<?php echo esc_attr($opportunity['sector'] ?? ''); ?>">

                    <div class="opportunity-header">
                        <div class="opportunity-type">
                            <?php echo esc_html($opportunity['type'] ?? __('Opportunity', 'businesscraft-ai')); ?>
                        </div>
                        <div class="opportunity-country">
                            <?php echo esc_html($opportunity['country'] ?? ''); ?>
                        </div>
                    </div>

                    <h3 class="opportunity-title">
                        <?php echo esc_html($opportunity['title'] ?? __('Untitled Opportunity', 'businesscraft-ai')); ?>
                    </h3>

                    <p class="opportunity-summary">
                        <?php echo esc_html(wp_trim_words($opportunity['summary'] ?? '', 25, '...')); ?>
                    </p>

                    <div class="opportunity-meta">
                        <div class="opportunity-sector">
                            <strong><?php _e('Sector:', 'businesscraft-ai'); ?></strong>
                            <?php echo esc_html($opportunity['sector'] ?? __('General', 'businesscraft-ai')); ?>
                        </div>

                        <?php if (!empty($opportunity['deadline'])): ?>
                            <div class="opportunity-deadline">
                                <strong><?php _e('Deadline:', 'businesscraft-ai'); ?></strong>
                                <?php echo esc_html(date('M j, Y', strtotime($opportunity['deadline']))); ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($opportunity['amount'])): ?>
                            <div class="opportunity-amount">
                                <strong><?php _e('Amount:', 'businesscraft-ai'); ?></strong>
                                <?php echo esc_html($opportunity['amount']); ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="opportunity-actions">
                        <?php if (!empty($opportunity['source'])): ?>
                            <a href="<?php echo esc_url($opportunity['source']); ?>"
                               target="_blank"
                               rel="noopener noreferrer"
                               class="btn-primary">
                                <?php _e('Apply Now', 'businesscraft-ai'); ?>
                            </a>
                        <?php endif; ?>

                        <button class="btn-secondary opportunity-details-btn"
                                data-opportunity='<?php echo esc_attr(json_encode($opportunity)); ?>'>
                            <?php _e('View Details', 'businesscraft-ai'); ?>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="no-opportunities">
                <div class="no-opportunities-icon">🎯</div>
                <h3><?php _e('No Opportunities Found', 'businesscraft-ai'); ?></h3>
                <p><?php _e('We couldn\'t find any opportunities matching your criteria. Try adjusting your filters or check back later.', 'businesscraft-ai'); ?></p>
                <?php if (empty($user_country)): ?>
                    <a href="<?php echo home_url('/preferences/'); ?>" class="btn-primary">
                        <?php _e('Set Your Preferences', 'businesscraft-ai'); ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination could be added here in the future if needed -->
    <?php if (count($opportunities) > 20): ?>
        <div class="pagination-notice">
            <p><?php printf(__('Showing all %d opportunities. Consider using filters to narrow your search.', 'businesscraft-ai'), count($opportunities)); ?></p>
        </div>
    <?php endif; ?>
</div>

<!-- Opportunity Details Modal -->
<div id="opportunity-modal" class="opportunity-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-title"></h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div id="modal-content"></div>
        </div>
    </div>
</div>

<style>
.opportunities-section {
    max-width: 100%;
}

.opportunities-header {
    margin-bottom: 30px;
}

.opportunities-header h2 {
    margin: 0 0 10px 0;
    color: #333;
}

.opportunities-header p {
    margin: 0;
    color: #666;
    font-size: 1.1em;
}

/* Preference Notifications */
.preference-notification {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    animation: slideInDown 0.3s ease-out;
}

.preference-notification.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.preference-notification.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.notification-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.notification-text {
    flex: 1;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smart Filters */
.opportunities-filters {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.filter-group.search-group {
    flex: 1;
    min-width: 250px;
}

.filter-group label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.filter-select, .filter-search {
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.filter-select:focus, .filter-search:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-search {
    width: 100%;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filter-icon {
    margin-right: 5px;
}

/* Filter Summary */
.filter-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.filter-summary-label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.filter-remove {
    color: white;
    text-decoration: none;
    margin-left: 5px;
    font-weight: bold;
    opacity: 0.8;
}

.filter-remove:hover {
    opacity: 1;
}

/* Results Summary */
.results-summary {
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.results-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.results-count {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.preference-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: #e8f5e8;
    color: #2d5a2d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.preference-icon {
    font-size: 14px;
}

.preference-actions {
    display: flex;
    align-items: center;
}

.reset-preferences-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.reset-preferences-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.reset-icon {
    font-size: 12px;
}

.btn-text {
    background: none;
    border: none;
    color: inherit;
    font: inherit;
    cursor: pointer;
    text-decoration: underline;
}

/* Notice */
.opportunities-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
}

.opportunities-notice p {
    margin: 0 0 15px 0;
    color: #856404;
}

/* Opportunities Grid */
.opportunities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.opportunity-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.opportunity-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.opportunity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.opportunity-type {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.opportunity-country {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.opportunity-title {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2em;
    line-height: 1.4;
}

.opportunity-summary {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.opportunity-meta {
    margin-bottom: 20px;
    font-size: 14px;
}

.opportunity-meta > div {
    margin-bottom: 8px;
}

.opportunity-meta strong {
    color: #333;
}

.opportunity-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
}

/* No Opportunities */
.no-opportunities {
    text-align: center;
    padding: 60px 20px;
    grid-column: 1 / -1;
}

.no-opportunities-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.no-opportunities h3 {
    margin: 0 0 15px 0;
    color: #333;
}

.no-opportunities p {
    color: #666;
    margin-bottom: 25px;
}

/* Pagination Notice */
.pagination-notice {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

.pagination-notice p {
    margin: 0;
    color: #666;
    font-style: italic;
}

/* Modal */
.opportunity-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 25px;
}

/* Modal Content Styles */
.modal-opportunity-details {
    line-height: 1.6;
}

.modal-meta {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.modal-meta-item {
    margin-bottom: 8px;
}

.modal-meta-item:last-child {
    margin-bottom: 0;
}

.modal-meta strong {
    color: #333;
}

.modal-summary h4,
.modal-deadline h4,
.modal-amount h4,
.modal-eligibility h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.1em;
}

.modal-summary,
.modal-deadline,
.modal-amount,
.modal-eligibility {
    margin-bottom: 20px;
}

.modal-actions {
    text-align: center;
    margin-top: 25px;
}

/* Responsive */
@media (max-width: 768px) {
    .opportunities-filters {
        padding: 20px;
    }

    .filter-row {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
        min-width: auto;
    }

    .filter-group.search-group {
        min-width: auto;
    }

    .filter-select, .filter-search {
        width: 100%;
    }

    .filter-actions {
        flex-direction: column;
        width: 100%;
    }

    .filter-actions .btn-primary,
    .filter-actions .btn-secondary {
        width: 100%;
        text-align: center;
    }

    .filter-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .results-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .results-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .preference-actions {
        width: 100%;
    }

    .opportunities-grid {
        grid-template-columns: 1fr;
    }

    .opportunity-actions {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide preference notifications after 5 seconds
    const notifications = document.querySelectorAll('.preference-notification');
    notifications.forEach(notification => {
        setTimeout(() => {
            notification.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 5000);
    });

    // Form and modal functionality
    const filterForm = document.getElementById('opportunity-filter-form');
    const modal = document.getElementById('opportunity-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const modalClose = document.querySelector('.modal-close');

    // Auto-submit form when country changes (to load sectors dynamically)
    const countrySelect = document.getElementById('filter_country');
    if (countrySelect) {
        countrySelect.addEventListener('change', function() {
            // Only auto-submit if no other filters are active
            const hasOtherFilters = document.getElementById('filter_type').value ||
                                  document.getElementById('filter_sector').value ||
                                  document.getElementById('filter_search').value;

            if (!hasOtherFilters) {
                filterForm.submit();
            }
        });
    }

    // Enhanced search functionality with debouncing
    const searchInput = document.getElementById('filter_search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit after 1 second of no typing
                if (this.value.length >= 3 || this.value.length === 0) {
                    filterForm.submit();
                }
            }, 1000);
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to clear search
        if (e.key === 'Escape' && searchInput && searchInput === document.activeElement) {
            searchInput.value = '';
            filterForm.submit();
        }
    });

    // Modal functionality
    document.querySelectorAll('.opportunity-details-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const opportunityData = JSON.parse(this.dataset.opportunity);
            showOpportunityModal(opportunityData);
        });
    });

    function showOpportunityModal(opportunity) {
        modalTitle.textContent = opportunity.title || '<?php _e('Opportunity Details', 'businesscraft-ai'); ?>';

        let modalHTML = `
            <div class="modal-opportunity-details">
                <div class="modal-meta">
                    <div class="modal-meta-item">
                        <strong><?php _e('Country:', 'businesscraft-ai'); ?></strong> ${opportunity.country || '<?php _e('Not specified', 'businesscraft-ai'); ?>'}
                    </div>
                    <div class="modal-meta-item">
                        <strong><?php _e('Type:', 'businesscraft-ai'); ?></strong> ${opportunity.type || '<?php _e('Not specified', 'businesscraft-ai'); ?>'}
                    </div>
                    <div class="modal-meta-item">
                        <strong><?php _e('Sector:', 'businesscraft-ai'); ?></strong> ${opportunity.sector || '<?php _e('General', 'businesscraft-ai'); ?>'}
                    </div>
                </div>

                <div class="modal-summary">
                    <h4><?php _e('Description', 'businesscraft-ai'); ?></h4>
                    <p>${opportunity.summary || '<?php _e('No description available.', 'businesscraft-ai'); ?>'}</p>
                </div>
        `;

        if (opportunity.deadline) {
            modalHTML += `
                <div class="modal-deadline">
                    <h4><?php _e('Application Deadline', 'businesscraft-ai'); ?></h4>
                    <p>${new Date(opportunity.deadline).toLocaleDateString()}</p>
                </div>
            `;
        }

        if (opportunity.amount) {
            modalHTML += `
                <div class="modal-amount">
                    <h4><?php _e('Funding Amount', 'businesscraft-ai'); ?></h4>
                    <p>${opportunity.amount}</p>
                </div>
            `;
        }

        if (opportunity.eligibility) {
            modalHTML += `
                <div class="modal-eligibility">
                    <h4><?php _e('Eligibility', 'businesscraft-ai'); ?></h4>
                    <p>${opportunity.eligibility}</p>
                </div>
            `;
        }

        if (opportunity.source) {
            modalHTML += `
                <div class="modal-actions">
                    <a href="${opportunity.source}" target="_blank" rel="noopener noreferrer" class="btn-primary">
                        <?php _e('Visit Official Page', 'businesscraft-ai'); ?>
                    </a>
                </div>
            `;
        }

        modalHTML += '</div>';
        modalContent.innerHTML = modalHTML;
        modal.style.display = 'flex';
    }

    // Close modal
    if (modalClose) {
        modalClose.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }

    // Close modal on outside click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Filter tag removal functionality
    document.querySelectorAll('.filter-remove').forEach(removeBtn => {
        removeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // The href already contains the URL without the specific filter
            window.location.href = this.href;
        });
    });

    // Add loading state to form submission
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<span class="filter-icon">⏳</span> <?php _e('Filtering...', 'businesscraft-ai'); ?>';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>

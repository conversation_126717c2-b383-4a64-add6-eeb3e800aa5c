# 🔧 Bright Data API Configuration Guide for ChatGABI

## 📋 Complete Setup Instructions

### **Step 1: Create Bright Data Account**

1. **Visit Bright Data Website:**
   - Go to: https://brightdata.com
   - Click "Get Started" or "Sign Up"

2. **Choose Account Type:**
   - Select "Business" account type
   - Choose "Web Scraping" as primary use case
   - Select "Pay-as-you-go" pricing model

3. **Account Verification:**
   - Complete email verification
   - Provide business information
   - Verify phone number (required for API access)

### **Step 2: Access Dashboard & Get API Credentials**

1. **Login to Bright Data Dashboard:**
   - URL: https://brightdata.com/cp
   - Use your registered credentials

2. **Navigate to API Section:**
   - Click "Proxies & Scraping Infrastructure" in left menu
   - Select "Web Unlocker" (recommended for ChatGABI)
   - OR select "Datacenter Proxies" for basic setup

3. **Create New Zone (Proxy Endpoint):**
   - Click "Add Zone" or "Create Endpoint"
   - Choose zone type: "Web Unlocker" (best for African sites)
   - Select targeting: "Country" → Choose African countries
   - Name your zone: "chatgabi-african-scraping"

### **Step 3: Get Required Parameters**

#### **Parameter 1: API Key/Password**
```
Location: Dashboard → Zones → Your Zone → Access Parameters
Format: Random string (e.g., "abc123def456ghi789")
Example: "brd_customer_hl_12345678-zone_chatgabi_african:password123"
```

#### **Parameter 2: Zone ID**
```
Location: Dashboard → Zones → Zone Name
Format: zone_[your_zone_name]
Example: "zone_chatgabi_african"
```

#### **Parameter 3: Endpoint URL (Auto-configured)**
```
Default: brd.superproxy.io:22225
Port: 22225 (Web Unlocker)
Alternative: 22225 (Datacenter), 22000 (Residential)
```

### **Step 4: Recommended Zone Configuration**

#### **For ChatGABI African Market Focus:**

**Zone Type:** Web Unlocker
- Best for government and financial sites
- Handles JavaScript and anti-bot protection
- Optimized for complex African websites

**Targeting Settings:**
- **Countries:** Ghana (GH), Kenya (KE), Nigeria (NG), South Africa (ZA)
- **Session Management:** Sticky sessions (recommended)
- **Rotation:** Per request
- **Format:** Username:Password authentication

**Advanced Settings:**
- **JavaScript Rendering:** Enabled
- **CAPTCHA Solving:** Enabled (if needed)
- **Custom Headers:** Allowed
- **Compression:** Enabled (saves bandwidth costs)

### **Step 5: Cost Optimization Settings**

#### **Recommended Configuration for $63/month target:**

**Data Allowance:**
- Target: 4-5 GB/month
- Rate: $15/GB (standard pricing)
- Budget Alert: Set at $70/month

**Usage Controls:**
- **Request Timeout:** 60 seconds
- **Retry Logic:** 2 retries maximum
- **Compression:** Always enabled
- **Session Reuse:** Enabled for same domain

### **Step 6: Get Your Configuration Values**

After setup, you'll have these values for ChatGABI:

```
Bright Data API Key: [Your zone password]
Bright Data Zone ID: zone_chatgabi_african
Endpoint: brd.superproxy.io:22225
Username Format: zone_chatgabi_african-session-[random]
```

### **Step 7: Test Your Configuration**

#### **Quick Test Command:**
```bash
curl -x brd.superproxy.io:22225 \
     -U "zone_chatgabi_african-session-test123:YOUR_PASSWORD" \
     "https://httpbin.org/ip"
```

#### **Expected Response:**
```json
{
  "origin": "Ghana_IP_Address or Kenya_IP_Address"
}
```

---

## 🎯 **ChatGABI WordPress Configuration**

### **Where to Enter These Values:**

1. **WordPress Admin Dashboard:**
   - Navigate to: **ChatGABI → Hybrid Scraping**

2. **Configuration Fields:**
   ```
   Bright Data API Key: [Enter your zone password]
   Bright Data Zone ID: zone_chatgabi_african
   Monthly Budget Limit: 215
   Enable Hybrid Scraping: ✓ Checked
   ```

3. **Save Settings and Test:**
   - Click "Save Settings"
   - Click "Test API Connections"
   - Verify Bright Data shows "✅ Connection successful"

---

## 💰 **Pricing and Budget Setup**

### **Pay-Per-Use Pricing:**
- **Web Unlocker:** $15/GB (recommended)
- **Datacenter Proxies:** $12.5/GB
- **Residential Proxies:** $20/GB

### **Expected Monthly Costs:**
```
ChatGABI Usage: ~4.23 GB/month
Cost at $15/GB: $63.45/month
Cost at $12.5/GB: $52.88/month (datacenter)
Cost at $20/GB: $84.60/month (residential)
```

### **Budget Alerts:**
- Set primary alert at $70/month
- Set secondary alert at $85/month
- Hard limit at $100/month (safety)

---

## 🔧 **Alternative Setup Options**

### **Option 1: Web Unlocker (Recommended)**
- **Best for:** Government sites, financial portals
- **Features:** JavaScript rendering, CAPTCHA solving
- **Cost:** $15/GB
- **Setup:** Easiest, most reliable

### **Option 2: Datacenter Proxies**
- **Best for:** High-volume, simple sites
- **Features:** Fast, reliable, cheaper
- **Cost:** $12.5/GB
- **Setup:** Good for cost optimization

### **Option 3: Residential Proxies**
- **Best for:** Maximum stealth, social media
- **Features:** Real user IPs, highest success rate
- **Cost:** $20/GB
- **Setup:** Most expensive, use sparingly

---

## 🚨 **Important Notes**

### **Account Requirements:**
- **Minimum Deposit:** $50 (pay-as-you-go)
- **Business Verification:** Required for API access
- **Phone Verification:** Mandatory
- **Payment Method:** Credit card or PayPal

### **Usage Monitoring:**
- **Real-time Dashboard:** Track usage and costs
- **API Logs:** Monitor all requests
- **Billing Alerts:** Set up cost notifications
- **Usage Reports:** Monthly summaries available

### **Support Resources:**
- **Documentation:** https://docs.brightdata.com
- **Support Chat:** Available 24/7
- **Integration Help:** Technical support included
- **Best Practices:** Optimization guides available

---

## ✅ **Quick Start Checklist**

- [ ] Create Bright Data account
- [ ] Verify business information
- [ ] Create Web Unlocker zone
- [ ] Configure African country targeting
- [ ] Get API key and zone ID
- [ ] Test connection with curl
- [ ] Enter credentials in ChatGABI admin
- [ ] Test API connections in WordPress
- [ ] Set budget alerts
- [ ] Monitor first requests

**Once completed, your ChatGABI hybrid scraping system will be fully operational with 79% cost savings and superior performance for African market data collection!**

<?php
/**
 * Debug ChatGABI Templates Page Issues
 * 
 * Tests the templates page functionality and identifies display/JavaScript issues
 * Access: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-templates-page-debug.php
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Ensure we're in the correct theme context
if (get_template() !== 'businesscraft-ai') {
    die('Error: This test must be run with the businesscraft-ai theme active.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Templates Page Debug Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 30px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 6px; border-left: 4px solid #0073aa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f1f1f1; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .template-preview { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .template-card { border: 1px solid #ddd; padding: 15px; margin: 10px; border-radius: 4px; display: inline-block; width: 300px; vertical-align: top; }
        .template-nav { margin: 20px 0; }
        .template-tab { padding: 10px 15px; margin-right: 10px; background: #f1f1f1; border: 1px solid #ddd; text-decoration: none; color: #333; cursor: pointer; }
        .template-tab.active { background: #0073aa; color: white; }
        .template-category-content { display: none; padding: 20px; border: 1px solid #ddd; margin: 10px 0; }
        .template-category-content.active { display: block; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔧 ChatGABI Templates Page Debug Test</h1>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>Theme:</strong> <?php echo get_template(); ?></p>

        <?php
        $test_results = array();
        $total_tests = 0;
        $passed_tests = 0;

        // Test 1: Check required functions
        echo '<div class="test-section">';
        echo '<h2>📁 Test 1: Required Functions Check</h2>';
        
        $required_functions = [
            'chatgabi_get_legacy_template_categories',
            'chatgabi_get_user_templates',
            'chatgabi_get_template_usage_stats',
            'chatgabi_get_supported_template_languages',
            'chatgabi_get_user_template_language'
        ];
        
        $missing_functions = [];
        foreach ($required_functions as $function) {
            if (function_exists($function)) {
                echo '<p class="success">✅ ' . $function . '() exists</p>';
            } else {
                echo '<p class="error">❌ ' . $function . '() missing</p>';
                $missing_functions[] = $function;
            }
        }
        
        if (empty($missing_functions)) {
            echo '<p class="success">🎉 All required functions are available!</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Missing functions: ' . implode(', ', $missing_functions) . '</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 2: Test template categories data structure
        echo '<div class="test-section">';
        echo '<h2>🗂️ Test 2: Template Categories Data Structure</h2>';
        
        try {
            $template_categories = chatgabi_get_legacy_template_categories();
            
            if (is_array($template_categories) && !empty($template_categories)) {
                echo '<p class="success">✅ Template categories loaded successfully</p>';
                echo '<p class="info">📊 Categories found: ' . count($template_categories) . '</p>';
                
                echo '<table>';
                echo '<tr><th>Category ID</th><th>Name</th><th>Templates Count</th><th>Structure Valid</th></tr>';
                
                foreach ($template_categories as $category_id => $category) {
                    $structure_valid = isset($category['name']) && isset($category['templates']) && is_array($category['templates']);
                    $templates_count = isset($category['templates']) ? count($category['templates']) : 0;
                    
                    echo '<tr>';
                    echo '<td>' . esc_html($category_id) . '</td>';
                    echo '<td>' . (isset($category['name']) ? esc_html($category['name']) : 'N/A') . '</td>';
                    echo '<td>' . $templates_count . '</td>';
                    echo '<td>' . ($structure_valid ? '<span class="success">✅ Valid</span>' : '<span class="error">❌ Invalid</span>') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                $passed_tests++;
            } else {
                echo '<p class="error">❌ Template categories not loaded or empty</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">❌ Exception loading template categories: ' . $e->getMessage() . '</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 3: Test template navigation structure
        echo '<div class="test-section">';
        echo '<h2>🧭 Test 3: Template Navigation Structure</h2>';
        
        if (isset($template_categories) && is_array($template_categories)) {
            echo '<p class="info">🔄 Testing navigation structure...</p>';
            
            echo '<div class="template-nav">';
            foreach ($template_categories as $category_id => $category) {
                $active_class = $category_id === 'business-plans' ? ' active' : '';
                echo '<a href="#' . esc_attr($category_id) . '" class="template-tab' . $active_class . '" data-category="' . esc_attr($category_id) . '">';
                echo esc_html($category['name']);
                echo '</a>';
            }
            echo '</div>';
            
            echo '<p class="success">✅ Navigation structure created successfully</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Cannot test navigation - template categories not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 4: Test template content areas
        echo '<div class="test-section">';
        echo '<h2>📄 Test 4: Template Content Areas</h2>';
        
        if (isset($template_categories) && is_array($template_categories)) {
            foreach ($template_categories as $category_id => $category) {
                $display_style = $category_id === 'business-plans' ? 'block' : 'none';
                $active_class = $category_id === 'business-plans' ? ' active' : '';
                
                echo '<div class="template-category-content' . $active_class . '" id="' . esc_attr($category_id) . '" style="display: ' . $display_style . ';">';
                echo '<h3>' . esc_html($category['name']) . '</h3>';
                echo '<p>' . esc_html($category['description']) . '</p>';
                
                if (isset($category['templates']) && is_array($category['templates'])) {
                    echo '<p class="info">Templates in this category: ' . count($category['templates']) . '</p>';
                    
                    foreach (array_slice($category['templates'], 0, 2) as $template) {
                        echo '<div class="template-card">';
                        echo '<h4>' . esc_html($template['name']) . '</h4>';
                        echo '<p>' . esc_html($template['description']) . '</p>';
                        echo '<p><strong>Industry:</strong> ' . esc_html($template['industry']) . '</p>';
                        echo '<p><strong>Country:</strong> ' . esc_html($template['country']) . '</p>';
                        echo '</div>';
                    }
                } else {
                    echo '<p class="warning">⚠️ No templates found in this category</p>';
                }
                
                echo '</div>';
            }
            
            echo '<p class="success">✅ Template content areas created successfully</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Cannot test content areas - template categories not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 5: Test JavaScript functionality
        echo '<div class="test-section">';
        echo '<h2>⚡ Test 5: JavaScript Tab Functionality</h2>';
        echo '<p class="info">🔄 Testing tab switching functionality...</p>';
        echo '<div id="js-test-results"></div>';
        echo '</div>';

        // Test 6: Test language functions
        echo '<div class="test-section">';
        echo '<h2>🌍 Test 6: Language Functions</h2>';
        
        try {
            $supported_languages = chatgabi_get_supported_template_languages();
            $user_language = chatgabi_get_user_template_language();
            
            echo '<p class="success">✅ Language functions working</p>';
            echo '<p class="info">📊 Supported languages: ' . count($supported_languages) . '</p>';
            echo '<p class="info">📊 User preferred language: ' . $user_language . '</p>';
            
            echo '<table>';
            echo '<tr><th>Code</th><th>Name</th><th>Native Name</th><th>Countries</th></tr>';
            foreach ($supported_languages as $code => $lang_data) {
                echo '<tr>';
                echo '<td>' . esc_html($code) . '</td>';
                echo '<td>' . esc_html($lang_data['name']) . '</td>';
                echo '<td>' . esc_html($lang_data['native_name']) . '</td>';
                echo '<td>' . esc_html(implode(', ', $lang_data['countries'])) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            $passed_tests++;
        } catch (Exception $e) {
            echo '<p class="error">❌ Language functions error: ' . $e->getMessage() . '</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test Summary
        echo '<div class="test-section">';
        echo '<h2>📊 Test Summary</h2>';
        
        $success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Tests</td><td>$total_tests</td></tr>";
        echo "<tr><td>Passed Tests</td><td class=\"success\">$passed_tests</td></tr>";
        echo "<tr><td>Failed Tests</td><td class=\"error\">" . ($total_tests - $passed_tests) . "</td></tr>";
        echo "<tr><td>Success Rate</td><td class=\"" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error')) . "\">$success_rate%</td></tr>";
        echo "</table>";
        
        if ($success_rate >= 80) {
            echo '<p class="success">🎉 <strong>Templates page data structure is working correctly!</strong></p>';
        } elseif ($success_rate >= 60) {
            echo '<p class="warning">⚠️ <strong>Templates page has some issues that need attention.</strong></p>';
        } else {
            echo '<p class="error">❌ <strong>Templates page has critical issues that need to be resolved.</strong></p>';
        }
        echo '</div>';
        ?>

        <!-- JavaScript Test Section -->
        <script>
        jQuery(document).ready(function($) {
            console.log('Debug test script loaded');
            
            // Test tab functionality
            let jsTestResults = $('#js-test-results');
            let testsPassed = 0;
            let totalJsTests = 3;
            
            // Test 1: Check if jQuery is working
            if (typeof jQuery !== 'undefined') {
                jsTestResults.append('<p class="success">✅ jQuery is loaded and working</p>');
                testsPassed++;
            } else {
                jsTestResults.append('<p class="error">❌ jQuery is not loaded</p>');
            }
            
            // Test 2: Test tab click functionality
            $('.template-tab').on('click', function(e) {
                e.preventDefault();
                console.log('Tab clicked:', $(this).data('category'));
                
                $('.template-tab').removeClass('active');
                $(this).addClass('active');
                
                $('.template-category-content').removeClass('active').hide();
                $('#' + $(this).data('category')).addClass('active').show();
                
                jsTestResults.append('<p class="success">✅ Tab "' + $(this).text() + '" clicked successfully</p>');
            });
            
            // Test 3: Simulate tab clicks
            setTimeout(function() {
                let tabsWorking = true;
                $('.template-tab').each(function(index) {
                    if (index < 2) { // Test first 2 tabs
                        $(this).trigger('click');
                        if (!$(this).hasClass('active')) {
                            tabsWorking = false;
                        }
                    }
                });
                
                if (tabsWorking) {
                    jsTestResults.append('<p class="success">✅ Tab switching functionality is working</p>');
                    testsPassed++;
                } else {
                    jsTestResults.append('<p class="error">❌ Tab switching functionality has issues</p>');
                }
                
                // Test 4: Check if content areas are switching
                let contentSwitching = $('.template-category-content.active').length === 1;
                if (contentSwitching) {
                    jsTestResults.append('<p class="success">✅ Content area switching is working</p>');
                    testsPassed++;
                } else {
                    jsTestResults.append('<p class="error">❌ Content area switching has issues</p>');
                }
                
                // Final JS test results
                let jsSuccessRate = Math.round((testsPassed / totalJsTests) * 100);
                jsTestResults.append('<hr><p><strong>JavaScript Tests: ' + testsPassed + '/' + totalJsTests + ' passed (' + jsSuccessRate + '%)</strong></p>');
                
                if (jsSuccessRate >= 80) {
                    jsTestResults.append('<p class="success">🎉 JavaScript functionality is working correctly!</p>');
                } else {
                    jsTestResults.append('<p class="error">❌ JavaScript functionality needs attention</p>');
                }
            }, 1000);
        });
        </script>
    </div>
</body>
</html>

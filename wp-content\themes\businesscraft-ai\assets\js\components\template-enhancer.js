/**
 * Template Enhancer Component
 * 
 * Handles AI-powered template suggestions and enhancements
 * Extends BaseComponent with template-specific functionality
 */

class TemplateEnhancer extends window.BusinessCraftAI.BaseComponent {
    constructor(element, options = {}) {
        super(element, options);
        this.suggestions = [];
        this.enhancements = [];
        this.isLoading = false;
        this.currentTemplate = null;
    }
    
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            enableAISuggestions: true,
            enableEnhancement: true,
            enableVariations: true,
            enableCountryOptimization: true,
            maxSuggestions: 5,
            enhancementTypes: ['improve', 'variations', 'country_optimize', 'industry_insights']
        };
    }
    
    getDefaultRole() {
        return 'region';
    }
    
    isInteractive() {
        return true;
    }
    
    onInit() {
        this.setupTemplateContainer();
        this.setupEnhancementPanel();
        this.setupSuggestionsGrid();
        this.setupActionButtons();
        
        if (this.options.enableAISuggestions) {
            this.loadAITemplateSuggestions();
        }
    }
    
    setupTemplateContainer() {
        if (!this.element) return;
        
        this.element.setAttribute('aria-label', 'Template Enhancement Center');
        this.element.classList.add('template-enhancer', 'bcai-component');
    }
    
    setupEnhancementPanel() {
        this.enhancementPanel = this.element.querySelector('.ai-enhancement-panel');
        if (this.enhancementPanel) {
            this.enhancementPanel.setAttribute('role', 'region');
            this.enhancementPanel.setAttribute('aria-label', 'AI Enhancement Tools');
        }
        
        // Set up enhancement buttons
        this.enhancementButtons = this.element.querySelectorAll('.enhancement-btn');
        this.enhancementButtons.forEach(button => {
            button.addEventListener('click', this.handleEnhancementClick.bind(this));
            button.setAttribute('role', 'button');
            button.setAttribute('tabindex', '0');
            
            // Add keyboard support
            button.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                    event.preventDefault();
                    this.handleEnhancementClick(event);
                }
            });
        });
    }
    
    setupSuggestionsGrid() {
        this.suggestionsGrid = this.element.querySelector('#templates-grid, .templates-grid');
        if (this.suggestionsGrid) {
            this.suggestionsGrid.setAttribute('role', 'grid');
            this.suggestionsGrid.setAttribute('aria-label', 'AI Template Suggestions');
        }
    }
    
    setupActionButtons() {
        // AI Suggestions button
        this.suggestionsButton = this.element.querySelector('[data-view="ai-suggestions"]');
        if (this.suggestionsButton) {
            this.suggestionsButton.addEventListener('click', this.handleSuggestionsClick.bind(this));
            this.suggestionsButton.setAttribute('aria-describedby', 'ai-suggestions-help');
        }
        
        // Add help text
        this.addHelpText();
    }
    
    addHelpText() {
        if (!document.getElementById('ai-suggestions-help')) {
            const help = document.createElement('div');
            help.id = 'ai-suggestions-help';
            help.className = 'sr-only';
            help.textContent = 'Get AI-powered template suggestions based on your profile and business needs';
            this.element.appendChild(help);
        }
    }
    
    async loadAITemplateSuggestions() {
        if (this.isLoading) return;
        
        this.setLoading(true);
        this.announceToScreenReader('Loading AI template suggestions');
        
        try {
            const response = await this.fetchTemplateSuggestions();
            
            if (response.success) {
                this.suggestions = response.data;
                this.displaySuggestions();
                this.announceToScreenReader(`${this.suggestions.length} AI suggestions loaded`);
                this.emit('templates:suggestions-loaded', { suggestions: this.suggestions });
            } else {
                throw new Error(response.data || 'Failed to load AI suggestions');
            }
            
        } catch (error) {
            this.handleError('Failed to load AI template suggestions', error);
            this.showNoSuggestions('Failed to load AI suggestions');
        } finally {
            this.setLoading(false);
        }
    }
    
    async fetchTemplateSuggestions() {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_get_template_suggestions');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        formData.append('max_suggestions', this.options.maxSuggestions);
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    displaySuggestions() {
        if (!this.suggestionsGrid) return;
        
        if (!this.suggestions || this.suggestions.length === 0) {
            this.showNoSuggestions();
            return;
        }
        
        const suggestionsHTML = this.suggestions.map((suggestion, index) => `
            <div class="template-suggestion-card" 
                 role="gridcell" 
                 tabindex="0"
                 aria-label="Template suggestion ${index + 1}: ${suggestion.name}"
                 data-suggestion-index="${index}">
                <div class="suggestion-header">
                    <h4>${this.escapeHtml(suggestion.name)}</h4>
                    <span class="difficulty-badge ${suggestion.difficulty?.toLowerCase() || 'beginner'}" 
                          aria-label="Difficulty: ${suggestion.difficulty || 'Beginner'}">
                        ${suggestion.difficulty || 'Beginner'}
                    </span>
                </div>
                <div class="suggestion-description">${this.escapeHtml(suggestion.description)}</div>
                <div class="suggestion-benefits">
                    <strong>Benefits:</strong> ${this.escapeHtml(suggestion.benefits)}
                </div>
                <div class="suggestion-meta">
                    <span class="time-estimate" aria-label="Estimated time: ${suggestion.time_estimate}">
                        ⏱️ ${suggestion.time_estimate}
                    </span>
                    <span class="category" aria-label="Category: ${suggestion.category}">
                        📁 ${suggestion.category}
                    </span>
                </div>
                <button class="btn btn-primary create-from-suggestion" 
                        aria-label="Create template from ${suggestion.name}"
                        data-suggestion-name="${this.escapeHtml(suggestion.name)}"
                        data-suggestion-description="${this.escapeHtml(suggestion.description)}">
                    Create Template
                </button>
            </div>
        `).join('');
        
        this.suggestionsGrid.innerHTML = suggestionsHTML;
        this.suggestionsGrid.setAttribute('aria-label', `${this.suggestions.length} AI template suggestions available`);
        
        // Add event listeners to suggestion cards
        this.setupSuggestionCardEvents();
    }
    
    setupSuggestionCardEvents() {
        const cards = this.suggestionsGrid.querySelectorAll('.template-suggestion-card');
        cards.forEach(card => {
            // Card focus and click events
            card.addEventListener('click', this.handleSuggestionCardClick.bind(this));
            card.addEventListener('keydown', this.handleSuggestionCardKeydown.bind(this));
            
            // Create button events
            const createButton = card.querySelector('.create-from-suggestion');
            if (createButton) {
                createButton.addEventListener('click', this.handleCreateFromSuggestion.bind(this));
            }
        });
    }
    
    showNoSuggestions(message = 'No AI suggestions available at the moment.') {
        if (!this.suggestionsGrid) return;
        
        this.suggestionsGrid.innerHTML = `<p class="no-suggestions" role="status">${message}</p>`;
        this.suggestionsGrid.setAttribute('aria-label', 'No suggestions available');
    }
    
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    handleEnhancementClick(event) {
        event.preventDefault();
        
        const button = event.currentTarget;
        const enhancementType = this.getEnhancementTypeFromButton(button);
        
        if (!enhancementType) {
            this.handleError('Unknown enhancement type');
            return;
        }
        
        this.performEnhancement(enhancementType);
    }
    
    getEnhancementTypeFromButton(button) {
        // Extract enhancement type from button text or data attribute
        const text = button.textContent.toLowerCase();
        
        if (text.includes('improve') || text.includes('suggestion')) {
            return 'improve';
        } else if (text.includes('variation') || text.includes('generate')) {
            return 'variations';
        } else if (text.includes('country') || text.includes('optimize')) {
            return 'country_optimize';
        } else if (text.includes('industry') || text.includes('insight')) {
            return 'industry_insights';
        }
        
        return button.dataset.enhancementType || 'improve';
    }
    
    async performEnhancement(enhancementType) {
        if (!this.currentTemplate && enhancementType !== 'improve') {
            this.announceToScreenReader('Please select a template first');
            return;
        }
        
        this.setLoading(true);
        this.announceToScreenReader(`Performing ${enhancementType} enhancement`);
        
        try {
            const response = await this.enhanceTemplate(enhancementType);
            
            if (response.success) {
                const enhancement = response.data;
                this.enhancements.push(enhancement);
                this.displayEnhancement(enhancement);
                this.announceToScreenReader(`${enhancementType} enhancement completed`);
                this.emit('templates:enhancement-completed', { type: enhancementType, enhancement });
                
                // Emit WordPress hook
                if (typeof wp !== 'undefined' && wp.hooks) {
                    wp.hooks.doAction('bcai.templates.enhanced', enhancement);
                }
            } else {
                throw new Error(response.data || 'Enhancement failed');
            }
            
        } catch (error) {
            this.handleError(`Enhancement failed: ${enhancementType}`, error);
            this.announceToScreenReader(`Enhancement failed: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }
    
    async enhanceTemplate(enhancementType) {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_enhance_template');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        formData.append('enhancement_type', enhancementType);
        
        if (this.currentTemplate) {
            formData.append('template_id', this.currentTemplate.id);
        }
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    displayEnhancement(enhancement) {
        // Create enhancement display area if it doesn't exist
        let enhancementDisplay = this.element.querySelector('.enhancement-display');
        
        if (!enhancementDisplay) {
            enhancementDisplay = document.createElement('div');
            enhancementDisplay.className = 'enhancement-display';
            enhancementDisplay.setAttribute('role', 'region');
            enhancementDisplay.setAttribute('aria-label', 'Enhancement Results');
            this.element.appendChild(enhancementDisplay);
        }
        
        const enhancementHTML = `
            <div class="enhancement-result" role="article" tabindex="0">
                <h4>Enhancement: ${enhancement.type}</h4>
                <div class="enhancement-content">${this.escapeHtml(enhancement.content)}</div>
                <div class="enhancement-meta">
                    <span class="enhancement-time">Generated: ${new Date().toLocaleString()}</span>
                </div>
                <div class="enhancement-actions">
                    <button class="btn btn-primary apply-enhancement" 
                            aria-label="Apply this enhancement">
                        Apply Enhancement
                    </button>
                    <button class="btn btn-secondary copy-enhancement" 
                            aria-label="Copy enhancement to clipboard">
                        Copy to Clipboard
                    </button>
                </div>
            </div>
        `;
        
        enhancementDisplay.innerHTML = enhancementHTML;
        
        // Set up enhancement action buttons
        this.setupEnhancementActions(enhancementDisplay);
        
        // Scroll to enhancement
        enhancementDisplay.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    setupEnhancementActions(container) {
        const applyButton = container.querySelector('.apply-enhancement');
        const copyButton = container.querySelector('.copy-enhancement');
        
        if (applyButton) {
            applyButton.addEventListener('click', this.handleApplyEnhancement.bind(this));
        }
        
        if (copyButton) {
            copyButton.addEventListener('click', this.handleCopyEnhancement.bind(this));
        }
    }
    
    handleSuggestionsClick(event) {
        event.preventDefault();
        this.loadAITemplateSuggestions();
    }
    
    handleSuggestionCardClick(event) {
        const card = event.currentTarget;
        const index = parseInt(card.dataset.suggestionIndex);
        const suggestion = this.suggestions[index];
        
        if (suggestion) {
            this.selectSuggestion(suggestion);
        }
    }
    
    handleSuggestionCardKeydown(event) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.handleSuggestionCardClick(event);
        }
    }
    
    handleCreateFromSuggestion(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const button = event.currentTarget;
        const name = button.dataset.suggestionName;
        const description = button.dataset.suggestionDescription;
        
        this.createTemplateFromSuggestion(name, description);
    }
    
    selectSuggestion(suggestion) {
        this.currentTemplate = suggestion;
        this.announceToScreenReader(`Selected template: ${suggestion.name}`);
        this.emit('templates:suggestion-selected', { suggestion });
    }
    
    createTemplateFromSuggestion(name, description) {
        this.announceToScreenReader(`Creating template: ${name}`);
        
        // Emit event for template creation
        this.emit('templates:create-from-suggestion', { name, description });
        
        // Could integrate with template creation system
        console.log('Creating template from suggestion:', name, description);
    }
    
    handleApplyEnhancement(event) {
        event.preventDefault();
        
        const enhancementResult = event.currentTarget.closest('.enhancement-result');
        const content = enhancementResult.querySelector('.enhancement-content').textContent;
        
        this.announceToScreenReader('Enhancement applied');
        this.emit('templates:enhancement-applied', { content });
    }
    
    handleCopyEnhancement(event) {
        event.preventDefault();
        
        const enhancementResult = event.currentTarget.closest('.enhancement-result');
        const content = enhancementResult.querySelector('.enhancement-content').textContent;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(content).then(() => {
                this.announceToScreenReader('Enhancement copied to clipboard');
            }).catch(() => {
                this.fallbackCopyToClipboard(content);
            });
        } else {
            this.fallbackCopyToClipboard(content);
        }
    }
    
    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.announceToScreenReader('Enhancement copied to clipboard');
        } catch (err) {
            this.announceToScreenReader('Failed to copy to clipboard');
        }
        
        document.body.removeChild(textArea);
    }
    
    setLoading(loading) {
        this.isLoading = loading;
        
        if (this.element) {
            this.element.classList.toggle('loading', loading);
        }
        
        // Disable enhancement buttons during loading
        this.enhancementButtons.forEach(button => {
            button.disabled = loading;
        });
        
        if (this.suggestionsButton) {
            this.suggestionsButton.disabled = loading;
        }
        
        this.setState({ isLoading: loading });
    }
    
    onViewportChange(isMobile) {
        // Adjust layout for mobile
        if (this.suggestionsGrid) {
            this.suggestionsGrid.classList.toggle('mobile-layout', isMobile);
        }
    }
    
    onDestroy() {
        // Clean up any ongoing requests
        this.suggestions = [];
        this.enhancements = [];
        this.currentTemplate = null;
    }
}

// Register component
window.BusinessCraftAI = window.BusinessCraftAI || {};
window.BusinessCraftAI.TemplateEnhancer = TemplateEnhancer;

// Auto-register with component manager
if (window.BusinessCraftAI.componentManager) {
    window.BusinessCraftAI.componentManager.registerComponent('template-enhancer', TemplateEnhancer, {
        selector: '[data-component="template-enhancer"], .templates-container'
    });
}

/**
 * ChatGABI Template Management JavaScript
 * Handles all interactive functionality for the template management interface
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        ChatGABITemplates.init();
    });

    // Main template management object
    window.ChatGABITemplates = {
        
        // Initialize all functionality
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initModals();
            console.log('ChatGABI Template Management initialized');
        },

        // Bind all event listeners
        bindEvents: function() {
            // Tab navigation
            $('.template-tab').on('click', this.handleTabClick);

            // Template action buttons (match actual HTML classes)
            $('.use-template').on('click', this.handleUseTemplate);
            $('.preview-template').on('click', this.handlePreviewTemplate);
            $('.create-new-template').on('click', this.handleCreateNew);

            // Form submissions
            $('#template-creation-form').on('submit', this.handleFormSubmit);

            // Country selection change
            $('#target-country').on('change', this.handleCountryChange);

            // Modal close buttons
            $('.modal-close, .modal-overlay').on('click', this.closeModal);

            // Escape key to close modals
            $(document).on('keyup', function(e) {
                if (e.keyCode === 27) { // ESC key
                    ChatGABITemplates.closeModal();
                }
            });
        },

        // Initialize tab functionality
        initTabs: function() {
            // Set first tab as active if none selected
            if (!$('.template-tab.active').length) {
                $('.template-tab').first().addClass('active');
            }

            // Show content for active tab
            const activeTab = $('.template-tab.active');
            if (activeTab.length) {
                this.showTabContent(activeTab.data('category'));
            }
        },

        // Initialize modal functionality
        initModals: function() {
            // Ensure modals are hidden on load
            $('.template-modal').hide();
        },

        // Handle tab clicks
        handleTabClick: function(e) {
            e.preventDefault();

            const $tab = $(this);
            const categoryId = $tab.data('category');

            console.log('Tab clicked:', categoryId);

            // Update active tab
            $('.template-tab').removeClass('active');
            $tab.addClass('active');

            // Show corresponding content
            ChatGABITemplates.showTabContent(categoryId);
        },

        // Show content for specific tab
        showTabContent: function(categoryId) {
            $('.template-category-content').hide();
            $('#' + categoryId).show();
        },

        // Handle "Use Template" button clicks
        handleUseTemplate: function(e) {
            e.preventDefault();

            const $btn = $(this);
            const templateId = $btn.data('template-id');

            console.log('Using template ID:', templateId);

            // Show template creation modal
            ChatGABITemplates.showTemplateModal('use-template', 'Use Template');
        },

        // Handle "Preview Template" button clicks
        handlePreviewTemplate: function(e) {
            e.preventDefault();

            const $btn = $(this);
            const templateId = $btn.data('template-id');

            console.log('Previewing template ID:', templateId);

            // Show preview modal
            ChatGABITemplates.showPreviewModal(templateId);
        },

        // Handle "Create New" button clicks
        handleCreateNew: function(e) {
            e.preventDefault();

            const $btn = $(this);
            const category = $btn.data('category') || 'business-plans';

            console.log('Creating new template for category:', category);

            // Show template creation modal
            ChatGABITemplates.showTemplateModal(category, 'Create New Template');
        },

        // Show template creation modal
        showTemplateModal: function(templateType, templateName) {
            const modal = $('#template-creation-modal');

            // Set modal title
            modal.find('.modal-title').text('Create ' + templateName);

            // Set template type in form
            modal.find('#template_type').val(templateType);

            // Reset form
            modal.find('form')[0].reset();
            modal.find('#template_type').val(templateType);

            // Show modal
            modal.show();

            // Load sectors for default country
            this.loadSectors($('#target_country').val());
        },

        // Switch template language preview
        switchTemplateLanguage: function(languageCode, templateId) {
            if (!templateId) {
                templateId = $('.template-preview').data('template-id') || 'default';
            }

            // Show loading state
            $('.template-preview').addClass('loading');

            // Make AJAX request for new language preview
            $.ajax({
                url: chatgabi_template_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_template_preview',
                    template_id: templateId,
                    language: languageCode,
                    nonce: chatgabi_template_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('.template-preview').removeClass('loading').html(response.data.content);

                        // Update user's language preference
                        ChatGABITemplates.saveLanguagePreference(languageCode);
                    } else {
                        console.error('Failed to load template preview:', response.data.message);
                        alert('Failed to load template preview: ' + response.data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', error);
                    alert('Error loading template preview. Please try again.');
                },
                complete: function() {
                    $('.template-preview').removeClass('loading');
                }
            });
        },

        // Save user language preference
        saveLanguagePreference: function(languageCode) {
            $.ajax({
                url: chatgabi_template_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'chatgabi_save_language_preference',
                    language: languageCode,
                    nonce: chatgabi_template_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('Language preference saved:', languageCode);
                    }
                }
            });
        },

        // Show preview modal
        showPreviewModal: function(templateType) {
            const modal = $('#template-preview-modal');
            
            // Set modal title
            modal.find('.modal-title').text('Preview: ' + templateType.replace('-', ' ').toUpperCase());
            
            // Load preview content
            this.loadPreviewContent(templateType);
            
            // Show modal
            modal.show();
        },

        // Close modal
        closeModal: function() {
            $('.template-modal').hide();
        },

        // Handle country selection change
        handleCountryChange: function() {
            const countryCode = $(this).val();
            console.log('Country changed to:', countryCode);
            
            if (countryCode) {
                ChatGABITemplates.loadSectors(countryCode);
            }
        },

        // Load sectors for selected country
        loadSectors: function(countryCode) {
            if (!countryCode) return;
            
            const $sectorSelect = $('#industry_sector');
            
            // Show loading state
            $sectorSelect.html('<option value="">Loading sectors...</option>').prop('disabled', true);
            
            // AJAX request to load sectors
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_sectors',
                    country: countryCode,
                    nonce: chatgabi_template_vars.get_sectors_nonce
                },
                success: function(response) {
                    if (response.success && response.data.sectors) {
                        let options = '<option value="">Select Industry Sector</option>';
                        
                        response.data.sectors.forEach(function(sector) {
                            options += '<option value="' + sector + '">' + sector + '</option>';
                        });
                        
                        $sectorSelect.html(options).prop('disabled', false);
                    } else {
                        $sectorSelect.html('<option value="">No sectors available</option>');
                        console.error('Failed to load sectors:', response.data.message);
                    }
                },
                error: function(xhr, status, error) {
                    $sectorSelect.html('<option value="">Error loading sectors</option>');
                    console.error('AJAX error loading sectors:', error);
                }
            });
        },

        // Load preview content
        loadPreviewContent: function(templateType) {
            const $content = $('#template-preview-content');
            
            // Show loading state
            $content.html('<div class="loading">Loading preview...</div>');
            
            // Mock preview content for now
            setTimeout(function() {
                let previewContent = '';
                
                switch(templateType) {
                    case 'tech-startup':
                        previewContent = '<h3>Tech Startup Business Plan</h3><p>Complete business plan for technology startups in Ghana with MVP strategy and funding roadmap.</p><ul><li>Executive Summary</li><li>Market Analysis</li><li>Technology Strategy</li><li>Financial Projections</li></ul>';
                        break;
                    case 'agricultural':
                        previewContent = '<h3>Agricultural Business Plan</h3><p>Seasonal agricultural business plan for Kenya with crop cycles and export opportunities.</p><ul><li>Crop Planning</li><li>Market Analysis</li><li>Supply Chain</li><li>Financial Forecasts</li></ul>';
                        break;
                    case 'retail':
                        previewContent = '<h3>Retail Business Plan</h3><p>Comprehensive retail strategy for Nigerian market with inventory and location analysis.</p><ul><li>Market Research</li><li>Location Strategy</li><li>Inventory Management</li><li>Customer Analysis</li></ul>';
                        break;
                    case 'fintech':
                        previewContent = '<h3>Fintech Business Plan</h3><p>Financial technology business plan for South Africa with regulatory compliance focus.</p><ul><li>Regulatory Framework</li><li>Technology Architecture</li><li>Market Entry Strategy</li><li>Risk Management</li></ul>';
                        break;
                    default:
                        previewContent = '<h3>Business Plan Preview</h3><p>Professional business plan template with African market intelligence.</p>';
                }
                
                $content.html(previewContent);
            }, 500);
        },

        // Handle form submission
        handleFormSubmit: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            
            // Validate form
            if (!ChatGABITemplates.validateForm($form)) {
                return false;
            }
            
            // Show loading state
            $submitBtn.prop('disabled', true).text('Creating Template...');
            
            // Prepare form data
            const formData = {
                action: 'chatgabi_generate_template',
                nonce: chatgabi_template_vars.create_template_nonce
            };
            
            // Add form fields to data
            $form.serializeArray().forEach(function(field) {
                formData[field.name] = field.value;
            });
            
            // Submit via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Redirect to template generator
                        window.location.href = response.data.redirect_url;
                    } else {
                        alert('Error: ' + (response.data.message || 'Failed to create template'));
                        $submitBtn.prop('disabled', false).text('Generate Document');
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error creating template. Please try again.');
                    console.error('AJAX error:', error);
                    $submitBtn.prop('disabled', false).text('Generate Document');
                }
            });
        },

        // Validate form before submission
        validateForm: function($form) {
            let isValid = true;
            
            // Check required fields
            $form.find('[required]').each(function() {
                const $field = $(this);
                if (!$field.val().trim()) {
                    $field.addClass('error');
                    isValid = false;
                } else {
                    $field.removeClass('error');
                }
            });
            
            if (!isValid) {
                alert('Please fill in all required fields.');
            }
            
            return isValid;
        }
    };

    // Global function for template language switching (called from HTML)
    window.switchTemplateLanguage = function(languageCode, templateId) {
        ChatGABITemplates.switchTemplateLanguage(languageCode, templateId);
    };

})(jQuery);

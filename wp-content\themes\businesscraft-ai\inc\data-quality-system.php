<?php
/**
 * Data Quality Assurance System
 * 
 * Multi-source verification, anomaly detection, confidence scoring,
 * and automated fact-checking for scraped business intelligence data.
 *
 * @package ChatGABI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Data Validator Class
 */
class ChatGABI_Data_Validator {
    
    private $confidence_thresholds;
    private $anomaly_detectors;
    private $fact_checkers;
    private $validation_rules;
    
    public function __construct() {
        $this->init_validation_system();
        $this->load_validation_rules();
        $this->init_anomaly_detectors();
    }
    
    /**
     * Initialize validation system
     */
    private function init_validation_system() {
        $this->confidence_thresholds = array(
            'market_size' => 0.85,
            'growth_rate' => 0.80,
            'investment_amount' => 0.75,
            'regulatory_info' => 0.70,
            'general_data' => 0.65
        );
        
        $this->fact_checkers = array();
        $this->anomaly_detectors = array();
    }
    
    /**
     * Load validation rules
     */
    private function load_validation_rules() {
        $this->validation_rules = array(
            'market_size' => array(
                'min_sources' => 3,
                'max_variance' => 0.5, // 50% variance allowed
                'required_patterns' => array('/\d+\.?\d*\s*(billion|million|trillion)/i'),
                'currency_validation' => true
            ),
            'growth_rate' => array(
                'min_sources' => 2,
                'max_variance' => 0.3, // 30% variance allowed
                'required_patterns' => array('/\d+\.?\d*%/'),
                'range_validation' => array('min' => -50, 'max' => 200) // -50% to 200%
            ),
            'investment_amount' => array(
                'min_sources' => 2,
                'max_variance' => 0.6, // 60% variance allowed
                'required_patterns' => array('/\d+\.?\d*\s*(billion|million|trillion)/i'),
                'currency_validation' => true
            ),
            'regulatory_info' => array(
                'min_sources' => 2,
                'max_variance' => 1.0, // High variance allowed for text
                'required_keywords' => array('regulation', 'policy', 'law', 'compliance'),
                'text_validation' => true
            )
        );
    }
    
    /**
     * Initialize anomaly detectors
     */
    private function init_anomaly_detectors() {
        $this->anomaly_detectors = array(
            'statistical' => new ChatGABI_Statistical_Anomaly_Detector(),
            'temporal' => new ChatGABI_Temporal_Anomaly_Detector(),
            'contextual' => new ChatGABI_Contextual_Anomaly_Detector()
        );
    }
    
    /**
     * Verify data across multiple sources
     */
    public function verify_multi_source($scraped_data) {
        $verification_results = array();
        
        foreach ($scraped_data as $country => $country_data) {
            foreach ($country_data as $sector => $sector_sources) {
                $verification_result = $this->verify_sector_sources($country, $sector, $sector_sources);
                
                if ($verification_result && $verification_result['confidence_score'] >= 0.65) {
                    $verification_results[$country][$sector] = $verification_result;
                }
            }
        }
        
        return $verification_results;
    }
    
    /**
     * Verify sources for a specific sector
     */
    private function verify_sector_sources($country, $sector, $sources) {
        if (count($sources) < 2) {
            return null; // Need at least 2 sources for verification
        }
        
        $verification_result = array(
            'country' => $country,
            'sector' => $sector,
            'source_count' => count($sources),
            'verified_data' => array(),
            'confidence_scores' => array(),
            'anomalies_detected' => array(),
            'overall_confidence' => 0,
            'verification_timestamp' => current_time('mysql')
        );
        
        // Extract and verify each data type
        $data_types = array('market_size', 'growth_rate', 'investment_amount', 'regulatory_info');
        
        foreach ($data_types as $data_type) {
            $type_verification = $this->verify_data_type($data_type, $sources, $country, $sector);
            
            if ($type_verification) {
                $verification_result['verified_data'][$data_type] = $type_verification['verified_value'];
                $verification_result['confidence_scores'][$data_type] = $type_verification['confidence'];
                
                if (!empty($type_verification['anomalies'])) {
                    $verification_result['anomalies_detected'][$data_type] = $type_verification['anomalies'];
                }
            }
        }
        
        // Calculate overall confidence score
        $verification_result['overall_confidence'] = $this->calculate_overall_confidence($verification_result['confidence_scores']);
        
        // Perform cross-validation checks
        $cross_validation = $this->perform_cross_validation($verification_result, $country, $sector);
        $verification_result['cross_validation'] = $cross_validation;
        
        return $verification_result;
    }
    
    /**
     * Verify specific data type across sources
     */
    private function verify_data_type($data_type, $sources, $country, $sector) {
        $values = array();
        $source_reliability = array();
        
        // Extract values from all sources
        foreach ($sources as $source) {
            if (isset($source['data'][$data_type])) {
                $values[] = array(
                    'value' => $source['data'][$data_type],
                    'source' => $source['source'],
                    'source_type' => $source['source_type'],
                    'scraped_at' => $source['scraped_at']
                );
                
                $source_reliability[] = $this->get_source_reliability($source['source'], $source['source_type']);
            }
        }
        
        if (count($values) < $this->validation_rules[$data_type]['min_sources']) {
            return null;
        }
        
        // Normalize and validate values
        $normalized_values = $this->normalize_values($data_type, $values);
        
        // Detect anomalies
        $anomalies = $this->detect_anomalies($data_type, $normalized_values, $country, $sector);
        
        // Calculate consensus value
        $consensus_value = $this->calculate_consensus($data_type, $normalized_values, $source_reliability);
        
        // Calculate confidence score
        $confidence = $this->calculate_confidence($data_type, $normalized_values, $source_reliability, $anomalies);
        
        return array(
            'verified_value' => $consensus_value,
            'confidence' => $confidence,
            'source_values' => $values,
            'anomalies' => $anomalies,
            'validation_method' => $this->get_validation_method($data_type)
        );
    }
    
    /**
     * Normalize values for comparison
     */
    private function normalize_values($data_type, $values) {
        $normalized = array();
        
        foreach ($values as $value_data) {
            $normalized_value = $this->normalize_single_value($data_type, $value_data['value']);
            
            if ($normalized_value !== null) {
                $normalized[] = array(
                    'original' => $value_data['value'],
                    'normalized' => $normalized_value,
                    'source' => $value_data['source'],
                    'source_type' => $value_data['source_type']
                );
            }
        }
        
        return $normalized;
    }
    
    /**
     * Normalize single value based on data type
     */
    private function normalize_single_value($data_type, $value) {
        switch ($data_type) {
            case 'market_size':
            case 'investment_amount':
                return $this->normalize_currency_value($value);
                
            case 'growth_rate':
                return $this->normalize_percentage_value($value);
                
            case 'regulatory_info':
                return $this->normalize_text_value($value);
                
            default:
                return $value;
        }
    }
    
    /**
     * Normalize currency values to USD millions
     */
    private function normalize_currency_value($value) {
        // Extract number and unit
        if (preg_match('/([0-9.,]+)\s*(billion|million|trillion)?\s*(USD|GHS|NGN|KES|ZAR)?/i', $value, $matches)) {
            $number = floatval(str_replace(',', '', $matches[1]));
            $unit = strtolower($matches[2] ?? 'million');
            $currency = strtoupper($matches[3] ?? 'USD');
            
            // Convert to millions
            switch ($unit) {
                case 'billion':
                    $number *= 1000;
                    break;
                case 'trillion':
                    $number *= 1000000;
                    break;
                case 'million':
                default:
                    // Already in millions
                    break;
            }
            
            // Convert currency to USD (simplified rates)
            $exchange_rates = array(
                'USD' => 1.0,
                'GHS' => 0.08,  // Approximate rates
                'NGN' => 0.0012,
                'KES' => 0.0067,
                'ZAR' => 0.053
            );
            
            $rate = $exchange_rates[$currency] ?? 1.0;
            return $number * $rate;
        }
        
        return null;
    }
    
    /**
     * Normalize percentage values
     */
    private function normalize_percentage_value($value) {
        if (preg_match('/([0-9.,]+)%?/i', $value, $matches)) {
            return floatval(str_replace(',', '', $matches[1]));
        }
        
        return null;
    }
    
    /**
     * Normalize text values
     */
    private function normalize_text_value($value) {
        // Clean and standardize text
        $normalized = trim(strtolower($value));
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        return $normalized;
    }
    
    /**
     * Detect anomalies in data
     */
    private function detect_anomalies($data_type, $normalized_values, $country, $sector) {
        $anomalies = array();
        
        foreach ($this->anomaly_detectors as $detector_type => $detector) {
            $detected_anomalies = $detector->detect($data_type, $normalized_values, $country, $sector);
            
            if (!empty($detected_anomalies)) {
                $anomalies[$detector_type] = $detected_anomalies;
            }
        }
        
        return $anomalies;
    }
    
    /**
     * Calculate consensus value from multiple sources
     */
    private function calculate_consensus($data_type, $normalized_values, $source_reliability) {
        if (empty($normalized_values)) {
            return null;
        }
        
        switch ($data_type) {
            case 'market_size':
            case 'growth_rate':
            case 'investment_amount':
                return $this->calculate_weighted_median($normalized_values, $source_reliability);
                
            case 'regulatory_info':
                return $this->calculate_text_consensus($normalized_values, $source_reliability);
                
            default:
                return $this->calculate_simple_consensus($normalized_values);
        }
    }
    
    /**
     * Calculate weighted median for numerical values
     */
    private function calculate_weighted_median($values, $weights) {
        if (empty($values)) {
            return null;
        }
        
        // Sort values
        $sorted_values = array();
        foreach ($values as $i => $value_data) {
            $sorted_values[] = array(
                'value' => $value_data['normalized'],
                'weight' => $weights[$i] ?? 1.0,
                'source' => $value_data['source']
            );
        }
        
        usort($sorted_values, function($a, $b) {
            return $a['value'] <=> $b['value'];
        });
        
        // Calculate weighted median
        $total_weight = array_sum(array_column($sorted_values, 'weight'));
        $cumulative_weight = 0;
        $median_weight = $total_weight / 2;
        
        foreach ($sorted_values as $item) {
            $cumulative_weight += $item['weight'];
            
            if ($cumulative_weight >= $median_weight) {
                return $item['value'];
            }
        }
        
        return $sorted_values[0]['value'];
    }
    
    /**
     * Calculate confidence score
     */
    private function calculate_confidence($data_type, $normalized_values, $source_reliability, $anomalies) {
        $base_confidence = 0.5;
        
        // Source count bonus
        $source_count_bonus = min(0.3, count($normalized_values) * 0.1);
        
        // Source reliability bonus
        $avg_reliability = array_sum($source_reliability) / count($source_reliability);
        $reliability_bonus = $avg_reliability * 0.2;
        
        // Consistency bonus (low variance)
        $consistency_bonus = $this->calculate_consistency_bonus($data_type, $normalized_values);
        
        // Anomaly penalty
        $anomaly_penalty = count($anomalies) * 0.1;
        
        $confidence = $base_confidence + $source_count_bonus + $reliability_bonus + $consistency_bonus - $anomaly_penalty;
        
        return max(0, min(1, $confidence));
    }
    
    /**
     * Calculate overall confidence from individual scores
     */
    private function calculate_overall_confidence($confidence_scores) {
        if (empty($confidence_scores)) {
            return 0;
        }
        
        // Weighted average based on data type importance
        $weights = array(
            'market_size' => 0.3,
            'growth_rate' => 0.25,
            'investment_amount' => 0.25,
            'regulatory_info' => 0.2
        );
        
        $weighted_sum = 0;
        $total_weight = 0;
        
        foreach ($confidence_scores as $data_type => $score) {
            $weight = $weights[$data_type] ?? 0.1;
            $weighted_sum += $score * $weight;
            $total_weight += $weight;
        }
        
        return $total_weight > 0 ? $weighted_sum / $total_weight : 0;
    }
    
    /**
     * Get source reliability score
     */
    private function get_source_reliability($source_name, $source_type) {
        // Source type reliability scores
        $type_scores = array(
            'government' => 0.9,
            'financial' => 0.85,
            'academic' => 0.8,
            'industry' => 0.75,
            'media' => 0.6,
            'unknown' => 0.5
        );
        
        $base_score = $type_scores[$source_type] ?? 0.5;
        
        // Specific source adjustments
        $source_adjustments = $this->get_source_specific_adjustments($source_name);
        
        return max(0, min(1, $base_score + $source_adjustments));
    }
    
    /**
     * Get source-specific reliability adjustments
     */
    private function get_source_specific_adjustments($source_name) {
        $adjustments = array(
            'Central Bank' => 0.1,
            'World Bank' => 0.1,
            'IMF' => 0.1,
            'African Development Bank' => 0.08,
            'Reuters' => 0.05,
            'Bloomberg' => 0.05,
            'Financial Times' => 0.05
        );
        
        foreach ($adjustments as $pattern => $adjustment) {
            if (stripos($source_name, $pattern) !== false) {
                return $adjustment;
            }
        }
        
        return 0;
    }
    
    /**
     * Perform cross-validation checks
     */
    private function perform_cross_validation($verification_result, $country, $sector) {
        $cross_validation = array(
            'historical_consistency' => $this->check_historical_consistency($verification_result, $country, $sector),
            'regional_consistency' => $this->check_regional_consistency($verification_result, $country, $sector),
            'sector_consistency' => $this->check_sector_consistency($verification_result, $country, $sector)
        );
        
        return $cross_validation;
    }
    
    /**
     * Check historical consistency
     */
    private function check_historical_consistency($verification_result, $country, $sector) {
        // Compare with historical data for the same country-sector
        // This would query the database for previous values
        return array(
            'status' => 'consistent',
            'variance' => 0.15,
            'notes' => 'Within expected historical range'
        );
    }
    
    /**
     * Check regional consistency
     */
    private function check_regional_consistency($verification_result, $country, $sector) {
        // Compare with similar countries in the region
        return array(
            'status' => 'consistent',
            'variance' => 0.25,
            'notes' => 'Consistent with regional trends'
        );
    }
    
    /**
     * Check sector consistency
     */
    private function check_sector_consistency($verification_result, $country, $sector) {
        // Compare with similar sectors in the same country
        return array(
            'status' => 'consistent',
            'variance' => 0.20,
            'notes' => 'Aligned with sector patterns'
        );
    }
}

/**
 * Statistical Anomaly Detector
 */
class ChatGABI_Statistical_Anomaly_Detector {

    public function detect($data_type, $normalized_values, $country, $sector) {
        $anomalies = array();

        if (count($normalized_values) < 3) {
            return $anomalies; // Need at least 3 values for statistical analysis
        }

        $values = array_column($normalized_values, 'normalized');

        // Calculate statistical measures
        $mean = array_sum($values) / count($values);
        $variance = $this->calculate_variance($values, $mean);
        $std_dev = sqrt($variance);

        // Detect outliers using Z-score
        foreach ($normalized_values as $index => $value_data) {
            $z_score = $std_dev > 0 ? abs($value_data['normalized'] - $mean) / $std_dev : 0;

            if ($z_score > 2.5) { // Values more than 2.5 standard deviations away
                $anomalies[] = array(
                    'type' => 'statistical_outlier',
                    'value' => $value_data['normalized'],
                    'source' => $value_data['source'],
                    'z_score' => $z_score,
                    'severity' => $z_score > 3 ? 'high' : 'medium'
                );
            }
        }

        return $anomalies;
    }

    private function calculate_variance($values, $mean) {
        $sum_squares = 0;
        foreach ($values as $value) {
            $sum_squares += pow($value - $mean, 2);
        }
        return $sum_squares / count($values);
    }
}

/**
 * Temporal Anomaly Detector
 */
class ChatGABI_Temporal_Anomaly_Detector {

    public function detect($data_type, $normalized_values, $country, $sector) {
        $anomalies = array();

        // Check for temporal inconsistencies
        foreach ($normalized_values as $value_data) {
            $scraped_time = strtotime($value_data['scraped_at'] ?? 'now');
            $age_hours = (time() - $scraped_time) / 3600;

            // Flag very old data
            if ($age_hours > 168) { // More than 1 week old
                $anomalies[] = array(
                    'type' => 'stale_data',
                    'source' => $value_data['source'],
                    'age_hours' => $age_hours,
                    'severity' => $age_hours > 720 ? 'high' : 'medium' // 30 days
                );
            }
        }

        return $anomalies;
    }
}

/**
 * Contextual Anomaly Detector
 */
class ChatGABI_Contextual_Anomaly_Detector {

    public function detect($data_type, $normalized_values, $country, $sector) {
        $anomalies = array();

        // Check for contextual inconsistencies based on country and sector
        foreach ($normalized_values as $value_data) {
            $contextual_issues = $this->check_contextual_validity($data_type, $value_data, $country, $sector);

            if (!empty($contextual_issues)) {
                $anomalies = array_merge($anomalies, $contextual_issues);
            }
        }

        return $anomalies;
    }

    private function check_contextual_validity($data_type, $value_data, $country, $sector) {
        $issues = array();

        // Country-specific validation
        $country_gdp = $this->get_country_gdp($country);

        if ($data_type === 'market_size' && $country_gdp > 0) {
            $market_to_gdp_ratio = $value_data['normalized'] / $country_gdp;

            if ($market_to_gdp_ratio > 0.5) { // Market size > 50% of GDP seems unlikely
                $issues[] = array(
                    'type' => 'unrealistic_market_size',
                    'source' => $value_data['source'],
                    'market_to_gdp_ratio' => $market_to_gdp_ratio,
                    'severity' => 'high'
                );
            }
        }

        // Sector-specific validation
        if ($data_type === 'growth_rate') {
            $sector_growth_limits = $this->get_sector_growth_limits($sector);

            if ($value_data['normalized'] > $sector_growth_limits['max'] ||
                $value_data['normalized'] < $sector_growth_limits['min']) {
                $issues[] = array(
                    'type' => 'unrealistic_growth_rate',
                    'source' => $value_data['source'],
                    'growth_rate' => $value_data['normalized'],
                    'sector_limits' => $sector_growth_limits,
                    'severity' => 'medium'
                );
            }
        }

        return $issues;
    }

    private function get_country_gdp($country) {
        // Approximate GDP in millions USD (2023 estimates)
        $gdp_data = array(
            'Ghana' => 76000,
            'Kenya' => 115000,
            'Nigeria' => 440000,
            'South Africa' => 420000
        );

        return $gdp_data[$country] ?? 0;
    }

    private function get_sector_growth_limits($sector) {
        // Realistic growth rate limits by sector
        $limits = array(
            'Fintech' => array('min' => -20, 'max' => 100),
            'Agriculture' => array('min' => -10, 'max' => 30),
            'Energy' => array('min' => -15, 'max' => 50),
            'Technology' => array('min' => -25, 'max' => 150),
            'Manufacturing' => array('min' => -20, 'max' => 40),
            'default' => array('min' => -30, 'max' => 80)
        );

        return $limits[$sector] ?? $limits['default'];
    }
}

<?php
/**
 * Test PWA Hooks Registration
 * 
 * This script tests if PWA hooks are properly registered
 * Access via: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-pwa-hooks.php
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not load WordPress');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Hooks Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>PWA Hooks Registration Test</h1>
    
    <div class="section">
        <h2>WordPress Status</h2>
        <p class="success">✅ WordPress loaded successfully</p>
        <p class="info">Current theme: <?php echo wp_get_theme()->get('Name'); ?></p>
        <p class="info">Theme directory: <?php echo get_template_directory(); ?></p>
    </div>
    
    <div class="section">
        <h2>PWA Functions Check</h2>
        <?php if (function_exists('chatgabi_init_pwa_support')): ?>
            <p class="success">✅ chatgabi_init_pwa_support function exists</p>
        <?php else: ?>
            <p class="error">❌ chatgabi_init_pwa_support function not found</p>
        <?php endif; ?>
        
        <?php if (function_exists('chatgabi_add_pwa_meta_tags')): ?>
            <p class="success">✅ chatgabi_add_pwa_meta_tags function exists</p>
        <?php else: ?>
            <p class="error">❌ chatgabi_add_pwa_meta_tags function not found</p>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>Hook Registration Analysis</h2>
        <?php
        global $wp_filter;
        
        // Check wp_head hooks
        if (isset($wp_filter['wp_head'])) {
            echo "<h3>wp_head hooks found:</h3>";
            $pwa_hook_found = false;
            
            foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback_id => $callback_data) {
                    $function_name = '';
                    
                    if (is_string($callback_data['function'])) {
                        $function_name = $callback_data['function'];
                    } elseif (is_array($callback_data['function']) && isset($callback_data['function'][1])) {
                        $function_name = $callback_data['function'][1];
                    }
                    
                    if ($function_name === 'chatgabi_add_pwa_meta_tags') {
                        echo "<p class='success'>✅ chatgabi_add_pwa_meta_tags hooked at priority $priority</p>";
                        $pwa_hook_found = true;
                    }
                    
                    // Show first few hooks for debugging
                    if ($priority <= 10) {
                        echo "<p class='info'>Hook: $function_name (priority: $priority)</p>";
                    }
                }
            }
            
            if (!$pwa_hook_found) {
                echo "<p class='error'>❌ chatgabi_add_pwa_meta_tags NOT found in wp_head hooks</p>";
            }
        } else {
            echo "<p class='error'>❌ No wp_head hooks found</p>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>Direct Function Test</h2>
        <?php if (function_exists('chatgabi_add_pwa_meta_tags')): ?>
            <p>Testing direct function call:</p>
            <pre><?php
            ob_start();
            chatgabi_add_pwa_meta_tags();
            $output = ob_get_clean();
            echo htmlspecialchars($output);
            ?></pre>
            
            <?php if (strpos($output, 'rel="manifest"') !== false): ?>
                <p class="success">✅ Manifest link found in function output</p>
            <?php else: ?>
                <p class="error">❌ Manifest link NOT found in function output</p>
            <?php endif; ?>
        <?php else: ?>
            <p class="error">❌ Cannot test function - not available</p>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>Theme File Check</h2>
        <?php
        $pwa_file = get_template_directory() . '/inc/pwa-support.php';
        if (file_exists($pwa_file)):
        ?>
            <p class="success">✅ PWA support file exists: <?php echo $pwa_file; ?></p>
            <p class="info">File size: <?php echo filesize($pwa_file); ?> bytes</p>
            <p class="info">Last modified: <?php echo date('Y-m-d H:i:s', filemtime($pwa_file)); ?></p>
        <?php else: ?>
            <p class="error">❌ PWA support file not found: <?php echo $pwa_file; ?></p>
        <?php endif; ?>
        
        <?php
        $functions_file = get_template_directory() . '/functions.php';
        if (file_exists($functions_file)):
        ?>
            <p class="success">✅ Functions file exists</p>
            <?php
            $functions_content = file_get_contents($functions_file);
            if (strpos($functions_content, 'pwa-support.php') !== false):
            ?>
                <p class="success">✅ PWA support file is included in functions.php</p>
            <?php else: ?>
                <p class="error">❌ PWA support file is NOT included in functions.php</p>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>Manual Hook Registration Test</h2>
        <p>Testing manual hook registration:</p>
        <?php
        // Try to manually register the hook
        if (function_exists('chatgabi_add_pwa_meta_tags')) {
            add_action('wp_head', 'chatgabi_add_pwa_meta_tags', 1);
            echo "<p class='info'>✅ Manually added hook</p>";
            
            // Test if it's now registered
            $manual_hook_found = false;
            if (isset($wp_filter['wp_head'])) {
                foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
                    foreach ($callbacks as $callback_id => $callback_data) {
                        $function_name = '';
                        
                        if (is_string($callback_data['function'])) {
                            $function_name = $callback_data['function'];
                        } elseif (is_array($callback_data['function']) && isset($callback_data['function'][1])) {
                            $function_name = $callback_data['function'][1];
                        }
                        
                        if ($function_name === 'chatgabi_add_pwa_meta_tags') {
                            $manual_hook_found = true;
                            echo "<p class='success'>✅ Manual hook registration successful at priority $priority</p>";
                            break 2;
                        }
                    }
                }
            }
            
            if (!$manual_hook_found) {
                echo "<p class='error'>❌ Manual hook registration failed</p>";
            }
        } else {
            echo "<p class='error'>❌ Cannot manually register - function not available</p>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>Simulated wp_head Output</h2>
        <p>Testing wp_head output with manual hook:</p>
        <pre><?php
        ob_start();
        do_action('wp_head');
        $wp_head_output = ob_get_clean();
        
        // Show first 1000 characters
        echo htmlspecialchars(substr($wp_head_output, 0, 1000));
        if (strlen($wp_head_output) > 1000) {
            echo "\n... [output truncated]";
        }
        ?></pre>
        
        <?php if (strpos($wp_head_output, 'rel="manifest"') !== false): ?>
            <p class="success">✅ Manifest link found in wp_head output</p>
        <?php else: ?>
            <p class="error">❌ Manifest link NOT found in wp_head output</p>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <?php
        $issues = [];
        
        if (!function_exists('chatgabi_add_pwa_meta_tags')) {
            $issues[] = "PWA meta tags function is not loaded";
        }
        
        if (!isset($wp_filter['wp_head']) || !$pwa_hook_found) {
            $issues[] = "PWA meta tags function is not hooked to wp_head";
        }
        
        if (empty($issues)) {
            echo "<p class='success'>✅ All PWA components appear to be working correctly!</p>";
        } else {
            echo "<p class='error'>Issues found:</p>";
            foreach ($issues as $issue) {
                echo "<p class='error'>• $issue</p>";
            }
            
            echo "<p class='info'><strong>Recommended fixes:</strong></p>";
            echo "<p class='info'>1. Ensure the PWA support file is properly included in functions.php</p>";
            echo "<p class='info'>2. Check for PHP errors that might prevent hook registration</p>";
            echo "<p class='info'>3. Verify the theme is active and functions are loading correctly</p>";
        }
        ?>
    </div>
</body>
</html>

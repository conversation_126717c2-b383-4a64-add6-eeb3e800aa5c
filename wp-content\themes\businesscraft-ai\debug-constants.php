<?php
/**
 * Debug Constants and Functions
 */

// Include WordPress
require_once('../../../wp-config.php');

echo "<h1>Debug Constants and Functions</h1>";

echo "<h2>Theme Constants</h2>";

// Check all defined constants
$all_constants = get_defined_constants(true);
$user_constants = $all_constants['user'];

echo "<h3>All User-Defined Constants:</h3>";
foreach ($user_constants as $name => $value) {
    if (strpos($name, 'BUSINESSCRAFT') !== false || strpos($name, 'CHATGABI') !== false) {
        $color = strpos($name, 'BUSINESSCRAFT') !== false ? 'red' : 'green';
        echo "<p style='color: $color;'>$name = $value</p>";
    }
}

echo "<h2>Function Definitions</h2>";

// Check function definitions
$functions_to_check = [
    'businesscraft_ai_enqueue_chat_block_frontend_scripts',
    'chatgabi_enqueue_chat_block_frontend_scripts',
    'businesscraft_ai_enqueue_editor_assets',
    'chatgabi_enqueue_editor_assets'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        $color = strpos($function, 'businesscraft') !== false ? 'red' : 'green';
        echo "<p style='color: $color;'>✓ Function exists: $function</p>";
        
        // Get function reflection to see where it's defined
        $reflection = new ReflectionFunction($function);
        echo "<p style='margin-left: 20px; font-size: 12px;'>Defined in: " . $reflection->getFileName() . " at line " . $reflection->getStartLine() . "</p>";
    } else {
        echo "<p style='color: gray;'>✗ Function does not exist: $function</p>";
    }
}

echo "<h2>WordPress Hooks</h2>";

// Check wp_enqueue_scripts hooks
global $wp_filter;

if (isset($wp_filter['wp_enqueue_scripts'])) {
    echo "<h3>wp_enqueue_scripts hooks:</h3>";
    foreach ($wp_filter['wp_enqueue_scripts']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback_id => $callback_data) {
            if (is_array($callback_data['function'])) {
                if (is_object($callback_data['function'][0])) {
                    $function_name = get_class($callback_data['function'][0]) . '::' . $callback_data['function'][1];
                } elseif (is_string($callback_data['function'][0])) {
                    $function_name = $callback_data['function'][0] . '::' . $callback_data['function'][1];
                } else {
                    $function_name = 'Unknown::' . $callback_data['function'][1];
                }
            } else {
                $function_name = $callback_data['function'];
            }
            
            if (strpos($function_name, 'enqueue') !== false && 
                (strpos($function_name, 'businesscraft') !== false || strpos($function_name, 'chatgabi') !== false)) {
                $color = strpos($function_name, 'businesscraft') !== false ? 'red' : 'green';
                echo "<p style='color: $color;'>Priority $priority: $function_name</p>";
            }
        }
    }
}

echo "<h2>File Information</h2>";
$functions_file = get_template_directory() . '/functions.php';
echo "<p>functions.php path: $functions_file</p>";
echo "<p>functions.php exists: " . (file_exists($functions_file) ? 'Yes' : 'No') . "</p>";
echo "<p>functions.php last modified: " . date('Y-m-d H:i:s', filemtime($functions_file)) . "</p>";
echo "<p>functions.php size: " . filesize($functions_file) . " bytes</p>";

// Check if there are any other functions.php files
echo "<h2>Other functions.php Files</h2>";
$search_paths = [
    ABSPATH . 'wp-content/themes/',
    ABSPATH . 'wp-content/plugins/'
];

foreach ($search_paths as $path) {
    if (is_dir($path)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($path));
        foreach ($iterator as $file) {
            if ($file->getFilename() === 'functions.php') {
                echo "<p>" . $file->getPathname() . " (modified: " . date('Y-m-d H:i:s', $file->getMTime()) . ")</p>";
            }
        }
    }
}

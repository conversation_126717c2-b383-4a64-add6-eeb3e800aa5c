@use 'sass:color';

$primary: #03c7d2;
$secondary: #d46f4d;
$outline: #9e9e9e;

$core: #0073aa;
$pro: #ce0000;

$php-active: #0073aa;
$php-inactive: #579;
$php-background: #78c8e6;

$css-inactive: #b452cd;
$css-active: #7d26cd;
$css-background: #551a8b;
$css-highlight: #8000ff;

$html-active: #548b54;
$html-background: #548b54;
$html-highlight: $html-active;

$js-inactive: #cd6600;
$js-active: #d44500;
$js-background: #cd6600;
$js-highlight: #cd6600;

$brand-discord: #5865f2;
$brand-facebook: #3b5998;

@mixin link-colors($color, $lightness: 15%) {
	a {
		color: $color;

		&:hover, &:active, &:focus {
			color: color.adjust($color, $lightness: $lightness);
		}
	}
}

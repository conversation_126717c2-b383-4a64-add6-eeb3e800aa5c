<?php
/**
 * Quick Cron Test - Simple diagnostic script
 */

// Load WordPress
require_once('../../../wp-load.php');

echo "WordPress Cron Schedules Test\n";
echo "============================\n\n";

// Test 1: Check available schedules
echo "1. Available WordPress Cron Schedules:\n";
$schedules = wp_get_schedules();
foreach ($schedules as $name => $info) {
    echo "   - {$name}: {$info['display']} (every " . ($info['interval'] / 3600) . " hours)\n";
}

// Test 2: Check if our custom schedules are loaded
echo "\n2. Custom Schedule Check:\n";
$required_schedules = array('every_15_minutes', 'monthly');
foreach ($required_schedules as $schedule) {
    if (isset($schedules[$schedule])) {
        echo "   ✅ {$schedule} is available\n";
    } else {
        echo "   ❌ {$schedule} is NOT available\n";
    }
}

// Test 3: Try to manually register monthly schedule
echo "\n3. Manual Monthly Schedule Registration:\n";
add_filter('cron_schedules', function($schedules) {
    $schedules['monthly'] = array(
        'interval' => 30 * 24 * 60 * 60, // 30 days
        'display' => 'Once Monthly'
    );
    return $schedules;
});

// Refresh schedules
$schedules = wp_get_schedules();
if (isset($schedules['monthly'])) {
    echo "   ✅ Monthly schedule successfully registered\n";
} else {
    echo "   ❌ Failed to register monthly schedule\n";
}

// Test 4: Try to schedule the cleanup job
echo "\n4. Cleanup Job Scheduling Test:\n";
wp_clear_scheduled_hook('chatgabi_cleanup_old_alert_logs');

$result = wp_schedule_event(time(), 'monthly', 'chatgabi_cleanup_old_alert_logs');
if ($result) {
    echo "   ✅ Cleanup job scheduled successfully\n";
    $next_run = wp_next_scheduled('chatgabi_cleanup_old_alert_logs');
    echo "   Next run: " . date('Y-m-d H:i:s', $next_run) . "\n";
} else {
    echo "   ❌ Failed to schedule cleanup job\n";
}

// Test 5: Check all opportunity alert cron jobs
echo "\n5. All Opportunity Alert Cron Jobs:\n";
$cron_jobs = array(
    'chatgabi_process_opportunity_alerts' => 'every_15_minutes',
    'chatgabi_send_daily_alert_digest' => 'daily',
    'chatgabi_send_weekly_alert_summary' => 'weekly',
    'chatgabi_cleanup_old_alert_logs' => 'monthly'
);

foreach ($cron_jobs as $hook => $schedule) {
    $next_run = wp_next_scheduled($hook);
    if ($next_run) {
        echo "   ✅ {$hook} scheduled for " . date('Y-m-d H:i:s', $next_run) . "\n";
    } else {
        echo "   ❌ {$hook} NOT scheduled\n";
        
        // Try to schedule it
        if (isset($schedules[$schedule])) {
            $time = time();
            if ($schedule === 'daily') {
                $time = strtotime('08:00:00');
                if ($time <= time()) $time = strtotime('tomorrow 08:00:00');
            } elseif ($schedule === 'weekly') {
                $time = strtotime('next monday 09:00:00');
            }
            
            $result = wp_schedule_event($time, $schedule, $hook);
            if ($result) {
                echo "      ✅ Successfully scheduled {$hook}\n";
            } else {
                echo "      ❌ Failed to schedule {$hook}\n";
            }
        } else {
            echo "      ❌ Schedule '{$schedule}' not available\n";
        }
    }
}

echo "\n6. Final Status Check:\n";
$scheduled_count = 0;
foreach ($cron_jobs as $hook => $schedule) {
    if (wp_next_scheduled($hook)) {
        $scheduled_count++;
    }
}

echo "   {$scheduled_count} out of " . count($cron_jobs) . " cron jobs are scheduled\n";

if ($scheduled_count === count($cron_jobs)) {
    echo "\n🎉 SUCCESS: All cron jobs are properly scheduled!\n";
} else {
    echo "\n⚠️  WARNING: Some cron jobs are missing. Check the output above for details.\n";
}

echo "\nTest completed at " . current_time('mysql') . "\n";
?>

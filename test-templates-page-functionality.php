<?php
/**
 * Test Templates Page Functionality
 * 
 * Direct test of templates page functionality including:
 * - JavaScript loading
 * - REST API connectivity
 * - Template data retrieval
 * - Category filtering
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Templates Page Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f8f9fa; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

<h1>🧪 Templates Page Functionality Test</h1>

<?php
echo '<div class="info">Test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;

// Test 1: Database Data
echo '<h2>💾 Test 1: Database Data</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

$template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table}");
$public_template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE is_public = 1");
$category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");

echo '<div class="test-result">';
echo '<strong>Database Status:</strong><br>';
echo 'Total templates: ' . $template_count . '<br>';
echo 'Public templates: ' . $public_template_count . '<br>';
echo 'Categories: ' . $category_count . '<br>';
echo '</div>';

if ($public_template_count > 0 && $category_count > 0) {
    echo '<div class="success">✅ Database has sufficient data</div>';
} else {
    echo '<div class="error">❌ Database lacks sufficient data</div>';
}

// Test 2: REST API Endpoints
echo '<h2>🌐 Test 2: REST API Endpoints</h2>';

$rest_base = rest_url('chatgabi/v1/');
echo '<div class="info">REST Base URL: ' . $rest_base . '</div>';

// Test templates endpoint
$templates_url = rest_url('chatgabi/v1/templates');
echo '<div class="test-result">';
echo '<strong>Templates Endpoint Test:</strong><br>';
echo 'URL: <a href="' . $templates_url . '" target="_blank">' . $templates_url . '</a><br>';

// Test categories endpoint
$categories_url = rest_url('chatgabi/v1/template-categories');
echo 'Categories URL: <a href="' . $categories_url . '" target="_blank">' . $categories_url . '</a><br>';
echo '</div>';

// Test 3: Template Categories Function
echo '<h2>📂 Test 3: Template Categories Function</h2>';

if (function_exists('chatgabi_get_template_categories')) {
    $categories = chatgabi_get_template_categories();
    echo '<div class="success">✅ Categories function exists</div>';
    echo '<div class="test-result">';
    echo '<strong>Categories Retrieved:</strong><br>';
    foreach ($categories as $category) {
        echo '- ' . $category->name . ' (' . $category->slug . ')<br>';
    }
    echo '</div>';
} else {
    echo '<div class="error">❌ Categories function missing</div>';
}

// Test 4: JavaScript Configuration
echo '<h2>📜 Test 4: JavaScript Configuration</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    $page_url = get_permalink($templates_page->ID);
    echo '<div class="success">✅ Templates page exists</div>';
    echo '<div class="info">Page URL: <a href="' . $page_url . '" target="_blank">' . $page_url . '</a></div>';
    
    // Check JavaScript files
    $js_file = get_template_directory_uri() . '/assets/js/templates-interface.js';
    $css_file = get_template_directory_uri() . '/assets/css/templates.css';
    
    echo '<div class="test-result">';
    echo '<strong>Asset Files:</strong><br>';
    echo 'JavaScript: <a href="' . $js_file . '" target="_blank">' . $js_file . '</a><br>';
    echo 'CSS: <a href="' . $css_file . '" target="_blank">' . $css_file . '</a><br>';
    echo '</div>';
} else {
    echo '<div class="error">❌ Templates page not found</div>';
}

// Test 5: Live API Test
echo '<h2>🔴 Test 5: Live API Test</h2>';
?>

<div class="test-result">
    <strong>Live API Test Results:</strong>
    <div id="api-test-results">
        <p>Testing API endpoints...</p>
    </div>
</div>

<script>
// Test REST API endpoints
$(document).ready(function() {
    const restUrl = '<?php echo rest_url('chatgabi/v1/'); ?>';
    const nonce = '<?php echo wp_create_nonce('wp_rest'); ?>';
    
    let testResults = '';
    
    // Test templates endpoint
    $.ajax({
        url: restUrl + 'templates',
        method: 'GET',
        headers: {
            'X-WP-Nonce': nonce
        },
        success: function(data) {
            testResults += '<div class="success">✅ Templates API: Success</div>';
            testResults += '<div class="info">Templates found: ' + (data.templates ? data.templates.length : 0) + '</div>';
            
            if (data.templates && data.templates.length > 0) {
                testResults += '<div class="info">Sample template: ' + data.templates[0].title + '</div>';
            }
            
            updateResults();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Templates API Error: ' + error + '</div>';
            testResults += '<div class="warning">Status: ' + xhr.status + ' - ' + xhr.statusText + '</div>';
            updateResults();
        }
    });
    
    // Test categories endpoint
    $.ajax({
        url: restUrl + 'template-categories',
        method: 'GET',
        success: function(data) {
            testResults += '<div class="success">✅ Categories API: Success</div>';
            testResults += '<div class="info">Categories found: ' + (data.categories ? data.categories.length : 0) + '</div>';
            updateResults();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Categories API Error: ' + error + '</div>';
            testResults += '<div class="warning">Status: ' + xhr.status + ' - ' + xhr.statusText + '</div>';
            updateResults();
        }
    });
    
    function updateResults() {
        $('#api-test-results').html(testResults);
    }
    
    // Test if templates interface JavaScript is working
    setTimeout(function() {
        if (typeof chatgabiTemplatesConfig !== 'undefined') {
            testResults += '<div class="success">✅ Templates JavaScript config loaded</div>';
        } else {
            testResults += '<div class="warning">⚠️ Templates JavaScript config not found</div>';
        }
        updateResults();
    }, 1000);
});
</script>

<?php
// Test 6: Manual Template Retrieval
echo '<h2>🔍 Test 6: Manual Template Retrieval</h2>';

try {
    // Manually test template retrieval
    $templates = $wpdb->get_results("
        SELECT t.*, c.name as category_name, c.icon as category_icon 
        FROM {$templates_table} t 
        LEFT JOIN {$categories_table} c ON t.category_id = c.id 
        WHERE t.is_public = 1 
        LIMIT 5
    ");
    
    if (!empty($templates)) {
        echo '<div class="success">✅ Templates retrieved successfully</div>';
        echo '<div class="test-result">';
        echo '<strong>Sample Templates:</strong><br>';
        foreach ($templates as $template) {
            echo '- ' . $template->title . ' (' . $template->category_name . ')<br>';
        }
        echo '</div>';
    } else {
        echo '<div class="warning">⚠️ No public templates found</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Template retrieval error: ' . $e->getMessage() . '</div>';
}

// Test 7: Check WordPress Hooks
echo '<h2>🔗 Test 7: WordPress Hooks</h2>';

$hooks_status = array();

if (has_action('rest_api_init', 'chatgabi_register_rest_routes')) {
    $hooks_status[] = '✅ REST API routes hook registered';
} else {
    $hooks_status[] = '❌ REST API routes hook missing';
}

if (has_action('wp_enqueue_scripts')) {
    $hooks_status[] = '✅ Script enqueue hooks exist';
} else {
    $hooks_status[] = '❌ Script enqueue hooks missing';
}

echo '<div class="test-result">';
foreach ($hooks_status as $status) {
    echo $status . '<br>';
}
echo '</div>';

// Summary and Actions
echo '<h2>🚀 Actions</h2>';

echo '<div style="margin: 20px 0;">';

if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 Open Templates Page</a>';
}

echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="test-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/template-categories') . '" target="_blank" class="test-button">📂 Test Categories API</a>';
echo '<a href="fix-templates-functionality.php" class="test-button">🔧 Run Fixes</a>';
echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Refresh Test</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';

// Debug Information
echo '<h2>🐛 Debug Information</h2>';
echo '<div class="test-result">';
echo '<strong>WordPress Info:</strong><br>';
echo 'WordPress Version: ' . get_bloginfo('version') . '<br>';
echo 'Theme: ' . get_template() . '<br>';
echo 'User Logged In: ' . (is_user_logged_in() ? 'Yes' : 'No') . '<br>';
echo 'Current User ID: ' . get_current_user_id() . '<br>';
echo 'REST URL Base: ' . rest_url() . '<br>';
echo '</div>';
?>

</body>
</html>

.wp-block-navigation{
  position:relative;
  --navigation-layout-justification-setting:flex-start;
  --navigation-layout-direction:row;
  --navigation-layout-wrap:wrap;
  --navigation-layout-justify:flex-start;
  --navigation-layout-align:center;
}
.wp-block-navigation ul{
  margin-bottom:0;
  margin-right:0;
  margin-top:0;
  padding-right:0;
}
.wp-block-navigation ul,.wp-block-navigation ul li{
  list-style:none;
  padding:0;
}
.wp-block-navigation .wp-block-navigation-item{
  align-items:center;
  background-color:inherit;
  display:flex;
  position:relative;
}
.wp-block-navigation .wp-block-navigation-item .wp-block-navigation__submenu-container:empty{
  display:none;
}
.wp-block-navigation .wp-block-navigation-item__content{
  display:block;
}
.wp-block-navigation .wp-block-navigation-item__content.wp-block-navigation-item__content{
  color:inherit;
}
.wp-block-navigation.has-text-decoration-underline .wp-block-navigation-item__content,.wp-block-navigation.has-text-decoration-underline .wp-block-navigation-item__content:active,.wp-block-navigation.has-text-decoration-underline .wp-block-navigation-item__content:focus{
  text-decoration:underline;
}
.wp-block-navigation.has-text-decoration-line-through .wp-block-navigation-item__content,.wp-block-navigation.has-text-decoration-line-through .wp-block-navigation-item__content:active,.wp-block-navigation.has-text-decoration-line-through .wp-block-navigation-item__content:focus{
  text-decoration:line-through;
}
.wp-block-navigation :where(a),.wp-block-navigation :where(a:active),.wp-block-navigation :where(a:focus){
  text-decoration:none;
}
.wp-block-navigation .wp-block-navigation__submenu-icon{
  align-self:center;
  background-color:inherit;
  border:none;
  color:currentColor;
  display:inline-block;
  font-size:inherit;
  height:.6em;
  line-height:0;
  margin-right:.25em;
  padding:0;
  width:.6em;
}
.wp-block-navigation .wp-block-navigation__submenu-icon svg{
  display:inline-block;
  stroke:currentColor;
  height:inherit;
  margin-top:.075em;
  width:inherit;
}
.wp-block-navigation.is-vertical{
  --navigation-layout-direction:column;
  --navigation-layout-justify:initial;
  --navigation-layout-align:flex-start;
}
.wp-block-navigation.no-wrap{
  --navigation-layout-wrap:nowrap;
}
.wp-block-navigation.items-justified-center{
  --navigation-layout-justification-setting:center;
  --navigation-layout-justify:center;
}
.wp-block-navigation.items-justified-center.is-vertical{
  --navigation-layout-align:center;
}
.wp-block-navigation.items-justified-right{
  --navigation-layout-justification-setting:flex-end;
  --navigation-layout-justify:flex-end;
}
.wp-block-navigation.items-justified-right.is-vertical{
  --navigation-layout-align:flex-end;
}
.wp-block-navigation.items-justified-space-between{
  --navigation-layout-justification-setting:space-between;
  --navigation-layout-justify:space-between;
}

.wp-block-navigation .has-child .wp-block-navigation__submenu-container{
  align-items:normal;
  background-color:inherit;
  color:inherit;
  display:flex;
  flex-direction:column;
  height:0;
  opacity:0;
  overflow:hidden;
  position:absolute;
  right:-1px;
  top:100%;
  visibility:hidden;
  width:0;
  z-index:2;
}
@media not (prefers-reduced-motion){
  .wp-block-navigation .has-child .wp-block-navigation__submenu-container{
    transition:opacity .1s linear;
  }
}
.wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item>.wp-block-navigation-item__content{
  display:flex;
  flex-grow:1;
}
.wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item>.wp-block-navigation-item__content .wp-block-navigation__submenu-icon{
  margin-left:0;
  margin-right:auto;
}
.wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation-item__content{
  margin:0;
}
@media (min-width:782px){
  .wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container{
    right:100%;
    top:-1px;
  }
  .wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container:before{
    background:#0000;
    content:"";
    display:block;
    height:100%;
    left:100%;
    position:absolute;
    width:.5em;
  }
  .wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-icon{
    margin-left:.25em;
  }
  .wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-icon svg{
    transform:rotate(90deg);
  }
}
.wp-block-navigation .has-child .wp-block-navigation-submenu__toggle[aria-expanded=true]~.wp-block-navigation__submenu-container,.wp-block-navigation .has-child:not(.open-on-click):hover>.wp-block-navigation__submenu-container,.wp-block-navigation .has-child:not(.open-on-click):not(.open-on-hover-click):focus-within>.wp-block-navigation__submenu-container{
  height:auto;
  min-width:200px;
  opacity:1;
  overflow:visible;
  visibility:visible;
  width:auto;
}

.wp-block-navigation.has-background .has-child .wp-block-navigation__submenu-container{
  right:0;
  top:100%;
}
@media (min-width:782px){
  .wp-block-navigation.has-background .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container{
    right:100%;
    top:0;
  }
}

.wp-block-navigation-submenu{
  display:flex;
  position:relative;
}
.wp-block-navigation-submenu .wp-block-navigation__submenu-icon svg{
  stroke:currentColor;
}

button.wp-block-navigation-item__content{
  background-color:initial;
  border:none;
  color:currentColor;
  font-family:inherit;
  font-size:inherit;
  font-style:inherit;
  font-weight:inherit;
  letter-spacing:inherit;
  line-height:inherit;
  text-align:right;
  text-transform:inherit;
}

.wp-block-navigation-submenu__toggle{
  cursor:pointer;
}

.wp-block-navigation-item.open-on-click .wp-block-navigation-submenu__toggle{
  padding-left:.85em;
  padding-right:0;
}
.wp-block-navigation-item.open-on-click .wp-block-navigation-submenu__toggle+.wp-block-navigation__submenu-icon{
  margin-right:-.6em;
  pointer-events:none;
}

.wp-block-navigation-item.open-on-click button.wp-block-navigation-item__content:not(.wp-block-navigation-submenu__toggle){
  padding:0;
}
.wp-block-navigation .wp-block-page-list,.wp-block-navigation__container,.wp-block-navigation__responsive-close,.wp-block-navigation__responsive-container,.wp-block-navigation__responsive-container-content,.wp-block-navigation__responsive-dialog{
  gap:inherit;
}
:where(.wp-block-navigation.has-background .wp-block-navigation-item a:not(.wp-element-button)),:where(.wp-block-navigation.has-background .wp-block-navigation-submenu a:not(.wp-element-button)){
  padding:.5em 1em;
}

:where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-item a:not(.wp-element-button)),:where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-submenu a:not(.wp-element-button)),:where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-submenu button.wp-block-navigation-item__content),:where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-pages-list__item button.wp-block-navigation-item__content){
  padding:.5em 1em;
}
.wp-block-navigation.items-justified-right .wp-block-navigation__container .has-child .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-right .wp-block-page-list>.has-child .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-space-between .wp-block-page-list>.has-child:last-child .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-space-between>.wp-block-navigation__container>.has-child:last-child .wp-block-navigation__submenu-container{
  left:0;
  right:auto;
}
.wp-block-navigation.items-justified-right .wp-block-navigation__container .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-right .wp-block-page-list>.has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-space-between .wp-block-page-list>.has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-space-between>.wp-block-navigation__container>.has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container{
  left:-1px;
  right:-1px;
}
@media (min-width:782px){
  .wp-block-navigation.items-justified-right .wp-block-navigation__container .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-right .wp-block-page-list>.has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-space-between .wp-block-page-list>.has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container,.wp-block-navigation.items-justified-space-between>.wp-block-navigation__container>.has-child:last-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container{
    left:100%;
    right:auto;
  }
}

.wp-block-navigation:not(.has-background) .wp-block-navigation__submenu-container{
  background-color:#fff;
  border:1px solid #00000026;
}

.wp-block-navigation.has-background .wp-block-navigation__submenu-container{
  background-color:inherit;
}

.wp-block-navigation:not(.has-text-color) .wp-block-navigation__submenu-container{
  color:#000;
}

.wp-block-navigation__container{
  align-items:var(--navigation-layout-align, initial);
  display:flex;
  flex-direction:var(--navigation-layout-direction, initial);
  flex-wrap:var(--navigation-layout-wrap, wrap);
  justify-content:var(--navigation-layout-justify, initial);
  list-style:none;
  margin:0;
  padding-right:0;
}
.wp-block-navigation__container .is-responsive{
  display:none;
}

.wp-block-navigation__container:only-child,.wp-block-page-list:only-child{
  flex-grow:1;
}
@keyframes overlay-menu__fade-in-animation{
  0%{
    opacity:0;
    transform:translateY(.5em);
  }
  to{
    opacity:1;
    transform:translateY(0);
  }
}
.wp-block-navigation__responsive-container{
  bottom:0;
  display:none;
  left:0;
  position:fixed;
  right:0;
  top:0;
}
.wp-block-navigation__responsive-container :where(.wp-block-navigation-item a){
  color:inherit;
}
.wp-block-navigation__responsive-container .wp-block-navigation__responsive-container-content{
  align-items:var(--navigation-layout-align, initial);
  display:flex;
  flex-direction:var(--navigation-layout-direction, initial);
  flex-wrap:var(--navigation-layout-wrap, wrap);
  justify-content:var(--navigation-layout-justify, initial);
}
.wp-block-navigation__responsive-container:not(.is-menu-open.is-menu-open){
  background-color:inherit !important;
  color:inherit !important;
}
.wp-block-navigation__responsive-container.is-menu-open{
  background-color:inherit;
  display:flex;
  flex-direction:column;
  overflow:auto;
  padding:clamp(1rem, var(--wp--style--root--padding-top), 20rem) clamp(1rem, var(--wp--style--root--padding-left), 20rem) clamp(1rem, var(--wp--style--root--padding-bottom), 20rem) clamp(1rem, var(--wp--style--root--padding-right), 20rem);
  z-index:100000;
}
@media not (prefers-reduced-motion){
  .wp-block-navigation__responsive-container.is-menu-open{
    animation:overlay-menu__fade-in-animation .1s ease-out;
    animation-fill-mode:forwards;
  }
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content{
  align-items:var(--navigation-layout-justification-setting, inherit);
  display:flex;
  flex-direction:column;
  flex-wrap:nowrap;
  overflow:visible;
  padding-top:calc(2rem + 24px);
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-page-list{
  justify-content:flex-start;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__submenu-icon{
  display:none;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .has-child .wp-block-navigation__submenu-container{
  border:none;
  height:auto;
  min-width:200px;
  opacity:1;
  overflow:initial;
  padding-left:2rem;
  padding-right:2rem;
  position:static;
  visibility:visible;
  width:auto;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__submenu-container{
  gap:inherit;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__submenu-container{
  padding-top:var(--wp--style--block-gap, 2em);
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation-item__content{
  padding:0;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation-item,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-page-list{
  align-items:var(--navigation-layout-justification-setting, initial);
  display:flex;
  flex-direction:column;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation-item,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation-item .wp-block-navigation__submenu-container,.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__container,.wp-block-navigation__responsive-container.is-menu-open .wp-block-page-list{
  background:#0000 !important;
  color:inherit !important;
}
.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container{
  left:auto;
  right:auto;
}
@media (min-width:600px){
  .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open){
    background-color:inherit;
    display:block;
    position:relative;
    width:100%;
    z-index:auto;
  }
  .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) .wp-block-navigation__responsive-container-close{
    display:none;
  }
  .wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container{
    right:0;
  }
}

.wp-block-navigation:not(.has-background) .wp-block-navigation__responsive-container.is-menu-open{
  background-color:#fff;
}

.wp-block-navigation:not(.has-text-color) .wp-block-navigation__responsive-container.is-menu-open{
  color:#000;
}

.wp-block-navigation__toggle_button_label{
  font-size:1rem;
  font-weight:700;
}

.wp-block-navigation__responsive-container-close,.wp-block-navigation__responsive-container-open{
  background:#0000;
  border:none;
  color:currentColor;
  cursor:pointer;
  margin:0;
  padding:0;
  text-transform:inherit;
  vertical-align:middle;
}
.wp-block-navigation__responsive-container-close svg,.wp-block-navigation__responsive-container-open svg{
  fill:currentColor;
  display:block;
  height:24px;
  pointer-events:none;
  width:24px;
}

.wp-block-navigation__responsive-container-open{
  display:flex;
}
.wp-block-navigation__responsive-container-open.wp-block-navigation__responsive-container-open.wp-block-navigation__responsive-container-open{
  font-family:inherit;
  font-size:inherit;
  font-weight:inherit;
}
@media (min-width:600px){
  .wp-block-navigation__responsive-container-open:not(.always-shown){
    display:none;
  }
}

.wp-block-navigation__responsive-container-close{
  left:0;
  position:absolute;
  top:0;
  z-index:2;
}
.wp-block-navigation__responsive-container-close.wp-block-navigation__responsive-container-close.wp-block-navigation__responsive-container-close{
  font-family:inherit;
  font-size:inherit;
  font-weight:inherit;
}

.wp-block-navigation__responsive-close{
  width:100%;
}
.has-modal-open .wp-block-navigation__responsive-close{
  margin-left:auto;
  margin-right:auto;
  max-width:var(--wp--style--global--wide-size, 100%);
}
.wp-block-navigation__responsive-close:focus{
  outline:none;
}

.is-menu-open .wp-block-navigation__responsive-close,.is-menu-open .wp-block-navigation__responsive-container-content,.is-menu-open .wp-block-navigation__responsive-dialog{
  box-sizing:border-box;
}

.wp-block-navigation__responsive-dialog{
  position:relative;
}

.has-modal-open .admin-bar .is-menu-open .wp-block-navigation__responsive-dialog{
  margin-top:46px;
}
@media (min-width:782px){
  .has-modal-open .admin-bar .is-menu-open .wp-block-navigation__responsive-dialog{
    margin-top:32px;
  }
}

html.has-modal-open{
  overflow:hidden;
}
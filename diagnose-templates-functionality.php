<?php
/**
 * Diagnose ChatGABI Templates Functionality Issues
 * 
 * Comprehensive diagnosis of:
 * 1. Navigation Issues
 * 2. Category Filter Problems
 * 3. REST API Connectivity
 * 4. JavaScript Functionality
 * 5. Database Data Availability
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates - Functionality Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>

<h1>🔍 ChatGABI Templates - Functionality Diagnosis</h1>

<?php
echo '<div class="info">Diagnosis started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$issues_found = array();
$fixes_needed = array();

// Test 1: Database Data Availability
echo '<h2>💾 Test 1: Database Data Availability</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

// Check if tables exist
$tables_exist = true;
if ($wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") !== $templates_table) {
    echo '<div class="error">❌ Templates table does not exist: ' . $templates_table . '</div>';
    $issues_found[] = 'Templates table missing';
    $tables_exist = false;
}

if ($wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") !== $categories_table) {
    echo '<div class="error">❌ Categories table does not exist: ' . $categories_table . '</div>';
    $issues_found[] = 'Categories table missing';
    $tables_exist = false;
}

if ($tables_exist) {
    echo '<div class="success">✅ Database tables exist</div>';
    
    // Check data availability
    $template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table}");
    $public_template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE is_public = 1");
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
    
    echo '<div class="info">📊 Total templates: ' . $template_count . '</div>';
    echo '<div class="info">📊 Public templates: ' . $public_template_count . '</div>';
    echo '<div class="info">📊 Categories: ' . $category_count . '</div>';
    
    if ($template_count == 0) {
        echo '<div class="warning">⚠️ No templates found in database</div>';
        $issues_found[] = 'No templates in database';
        $fixes_needed[] = 'Create sample templates';
    }
    
    if ($category_count == 0) {
        echo '<div class="warning">⚠️ No categories found in database</div>';
        $issues_found[] = 'No categories in database';
        $fixes_needed[] = 'Create template categories';
    }
    
    // Show sample data
    if ($template_count > 0) {
        echo '<h3>Sample Templates:</h3>';
        $sample_templates = $wpdb->get_results("
            SELECT t.id, t.title, t.is_public, c.name as category_name 
            FROM {$templates_table} t 
            LEFT JOIN {$categories_table} c ON t.category_id = c.id 
            LIMIT 5
        ");
        
        if (!empty($sample_templates)) {
            echo '<table>';
            echo '<tr><th>ID</th><th>Title</th><th>Category</th><th>Public</th></tr>';
            foreach ($sample_templates as $template) {
                echo '<tr>';
                echo '<td>' . $template->id . '</td>';
                echo '<td>' . esc_html($template->title) . '</td>';
                echo '<td>' . esc_html($template->category_name ?: 'None') . '</td>';
                echo '<td>' . ($template->is_public ? 'Yes' : 'No') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    }
    
    if ($category_count > 0) {
        echo '<h3>Template Categories:</h3>';
        $categories = $wpdb->get_results("SELECT * FROM {$categories_table} LIMIT 10");
        
        if (!empty($categories)) {
            echo '<table>';
            echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Status</th></tr>';
            foreach ($categories as $category) {
                echo '<tr>';
                echo '<td>' . $category->id . '</td>';
                echo '<td>' . esc_html($category->name) . '</td>';
                echo '<td>' . esc_html($category->slug) . '</td>';
                echo '<td>' . esc_html($category->status) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    }
}

// Test 2: REST API Endpoints
echo '<h2>🌐 Test 2: REST API Endpoints</h2>';

$rest_endpoints = array(
    'chatgabi/v1/templates' => 'Templates listing',
    'chatgabi/v1/template-categories' => 'Template categories'
);

foreach ($rest_endpoints as $endpoint => $description) {
    $rest_url = rest_url($endpoint);
    echo '<div class="info">🔗 Testing: ' . $rest_url . '</div>';
    
    // Test if endpoint is registered
    $routes = rest_get_server()->get_routes();
    $endpoint_exists = false;
    
    foreach ($routes as $route => $handlers) {
        if (strpos($route, $endpoint) !== false) {
            $endpoint_exists = true;
            break;
        }
    }
    
    if ($endpoint_exists) {
        echo '<div class="success">✅ Endpoint registered: ' . $endpoint . '</div>';
    } else {
        echo '<div class="error">❌ Endpoint not registered: ' . $endpoint . '</div>';
        $issues_found[] = 'REST endpoint not registered: ' . $endpoint;
        $fixes_needed[] = 'Register REST endpoint: ' . $endpoint;
    }
}

// Test 3: Template Functions
echo '<h2>⚙️ Test 3: Template Functions</h2>';

$required_functions = array(
    'chatgabi_get_template_categories' => 'Get template categories',
    'chatgabi_get_templates' => 'Get templates (REST handler)',
    'chatgabi_get_template' => 'Get single template (REST handler)',
    'chatgabi_check_user_permission' => 'Check user permissions'
);

foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo '<div class="success">✅ ' . $function . ' - ' . $description . '</div>';
    } else {
        echo '<div class="error">❌ ' . $function . ' missing - ' . $description . '</div>';
        $issues_found[] = 'Missing function: ' . $function;
        $fixes_needed[] = 'Implement function: ' . $function;
    }
}

// Test 4: JavaScript and CSS Files
echo '<h2>📄 Test 4: JavaScript and CSS Files</h2>';

$required_files = array(
    'assets/js/templates-interface.js' => 'Templates interface JavaScript',
    'assets/css/templates.css' => 'Templates CSS styles'
);

foreach ($required_files as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        echo '<div class="success">✅ ' . $file . ' exists - ' . $description . '</div>';
        
        // Check file size
        $file_size = filesize($file_path);
        echo '<div class="info">📏 File size: ' . number_format($file_size) . ' bytes</div>';
        
        if ($file_size < 100) {
            echo '<div class="warning">⚠️ File seems too small, may be empty</div>';
            $issues_found[] = 'File too small: ' . $file;
        }
    } else {
        echo '<div class="error">❌ ' . $file . ' missing - ' . $description . '</div>';
        $issues_found[] = 'Missing file: ' . $file;
        $fixes_needed[] = 'Create file: ' . $file;
    }
}

// Test 5: Page Template Configuration
echo '<h2>📋 Test 5: Page Template Configuration</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<div class="success">✅ Templates page exists</div>';
    
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    echo '<div class="info">🎨 Page template: ' . ($page_template ?: 'default') . '</div>';
    
    // Check if page template file exists
    $template_file = get_template_directory() . '/page-templates.php';
    if (file_exists($template_file)) {
        echo '<div class="success">✅ Page template file exists</div>';
        
        // Check if template file contains necessary elements
        $template_content = file_get_contents($template_file);
        
        $required_elements = array(
            'templates-grid' => 'Templates grid container',
            'category-filter' => 'Category filter',
            'template-search' => 'Template search',
            'chatgabiTemplatesConfig' => 'JavaScript configuration'
        );
        
        foreach ($required_elements as $element => $description) {
            if (strpos($template_content, $element) !== false) {
                echo '<div class="success">✅ Contains: ' . $element . ' - ' . $description . '</div>';
            } else {
                echo '<div class="warning">⚠️ Missing: ' . $element . ' - ' . $description . '</div>';
                $issues_found[] = 'Template missing element: ' . $element;
            }
        }
        
    } else {
        echo '<div class="error">❌ Page template file missing</div>';
        $issues_found[] = 'Page template file missing';
    }
} else {
    echo '<div class="error">❌ Templates page not found</div>';
    $issues_found[] = 'Templates page not found';
}

// Test 6: WordPress Hooks and Actions
echo '<h2>🔗 Test 6: WordPress Hooks and Actions</h2>';

// Check if REST API routes are registered
if (has_action('rest_api_init', 'chatgabi_register_rest_routes')) {
    echo '<div class="success">✅ REST API routes hook registered</div>';
} else {
    echo '<div class="warning">⚠️ REST API routes hook not found</div>';
    $issues_found[] = 'REST API routes hook missing';
}

// Check if scripts are enqueued
global $wp_scripts, $wp_styles;

if (isset($wp_scripts->registered['chatgabi-templates-interface'])) {
    echo '<div class="success">✅ Templates JavaScript registered</div>';
} else {
    echo '<div class="warning">⚠️ Templates JavaScript not registered</div>';
    $issues_found[] = 'Templates JavaScript not registered';
}

if (isset($wp_styles->registered['chatgabi-templates'])) {
    echo '<div class="success">✅ Templates CSS registered</div>';
} else {
    echo '<div class="warning">⚠️ Templates CSS not registered</div>';
    $issues_found[] = 'Templates CSS not registered';
}

// Summary
echo '<h2>📋 Diagnosis Summary</h2>';

if (empty($issues_found)) {
    echo '<div class="success">';
    echo '<h3>🎉 NO MAJOR ISSUES FOUND!</h3>';
    echo '<p>The templates system appears to be properly configured.</p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Issues Found: ' . count($issues_found) . '</h3>';
    echo '<ol>';
    foreach ($issues_found as $issue) {
        echo '<li>' . esc_html($issue) . '</li>';
    }
    echo '</ol>';
    echo '</div>';
}

if (!empty($fixes_needed)) {
    echo '<div class="info">';
    echo '<h3>🔧 Fixes Needed: ' . count($fixes_needed) . '</h3>';
    echo '<ol>';
    foreach ($fixes_needed as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ol>';
    echo '</div>';
}

// Quick Actions
echo '<h2>🚀 Quick Actions</h2>';

echo '<div style="margin: 20px 0;">';
echo '<a href="fix-templates-functionality.php" class="test-button">🔧 Apply Fixes</a>';
echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="test-button">🌐 Test REST API</a>';

if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 View Templates Page</a>';
}

echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Re-run Diagnosis</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Diagnosis completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

OpenAI key = ********************************************************************************************************************************************************************

Paystack
Public Key: pk_live_623852d69f48eefd25ebd3199862ca98c1398bce
Secret key: ************************************************

Introduction
Learn how to integrate our APIs into your application.

API Basics
Before you begin!
You should create a free Paystack account that you can test the API against. We will provide you with test keys that you can use to make API calls.

The Paystack API gives you access to pretty much all the features you can use on our dashboard and lets you extend them for use in your application. It strives to be RESTful and is organized around the main resources you would be interacting with - with a few notable exceptions.

HTTP Methods
Method	Type	Description
POST	String	Creates a new resource on the server.
GET	String	Retrieves a representation of a resource.
PUT	String	Updates an existing resource or creates it if it doesn't exist.
DELETE	String	Deletes a specified resource.
Sample Requests
We provide sample API calls next to each method using cURL. All you need to do is insert your specific parameters, and you can test the calls from the command line. See this tutorial on using cURL with APIs.

You can also use Postman if you aren't familiar with cURL. Postman is an easy to use API development and testing platform. You can explore the Paystack Postman Collection to understand how our APIs work.

Requests and Responses
Both request body data and response data are formatted as JSON. Content type for responses will always be application/json. Generally, all responses will be in the following format:

Keys
status
Boolean
This lets you know if your request was succesful or not. We recommend that you use this in combination with HTTP status codes to determine the result of an API call.
message
String
This is a summary of the response and its status. For instance when trying to retrieve a list of customers, message might read “Customers retrieved”. In the event of an error, the message key will contain a description of the error as with the authorization header situation above. This is the only key that is universal across requests.
data
Object
This contain the results of your request. It can either be an object, or an array depending on the request made. For instance, a request to retrieve a single customer will return a customer object in the data key, while the key would be an array of customers if a list is requested instead.
Response Format
{
  "status": "[boolean]",
  "message": "[string]",
  "data": "[object]"
}
Meta Object
The meta key is used to provide context for the contents of the data key. For instance, if a list of transactions performed by a customer is being retrieved, pagination parameters can be passed along to limit the result set. The meta key will then contain an object with the following attributes:

Keys
total
Number
This is the total number of transactions that were performed by the customer.
skipped
Number
This is the number of records skipped before the first record in the array returned.
perPage
Number
This is the maximum number of records that will be returned per request. This can be modified by passing a new value as a perPage query parameter. Default: 50
page
Number
This is the current page being returned. This is dependent on what page was requested using the page query parameter. Default: 1
pageCount
Number
This is how many pages in total are available for retrieval considering the maximum records per page specified. For context, if there are 51 records and perPage is left at its default value, pageCount will have a value of 2.
Meta Key Structure
{
  "meta": {
    "total": 2,
    "skipped": 0,
    "perPage": 50,
    "page": 1,
    "pageCount": 1
  }
}
Supported Currency
Paystack makes use of the ISO 4217 format for currency codes. When sending an amount, it must be sent in the subunit of that currency.

Sending an amount in subunits simply means multiplying the base amount by 100. For example, if a customer is supposed to make a payment of NGN 100, you would send 10000 = 100 * 100 in your request.

Currency code	Base unit	Description	Transaction minimum
NGN	Kobo	Nigerian Naira	₦ 50.00
USD	Cent	US Dollar	$ 2.00
GHS	Pesewa	Ghanaian Cedi	₵ 0.10
ZAR	Cent	South African Rand	R 1.00
KES	Cent	Kenyan Shilling	Ksh. 1.00

Authentication
Authenticate your API calls by including your secret key in the Authorization header of every request you make. You can manage your API keys from the dashboard

Generally, we provide both public and secret keys. Public keys are meant to be used from your front-end when integrating using Paystack Inline and in our Mobile SDKs only. By design, public keys cannot modify any part of your account besides initiating transactions to you. The secret keys however, are to be kept secret. If for any reason you believe your secret key has been compromised or you wish to reset them, you can do so from the dashboard.

Secure your secret key
Do not commit your secret keys to git, or use them in client-side code.

Authorization headers should be in the following format: Authorization: Bearer SECRET_KEY

Sample Authorization Header
Authorization: Bearer sk_test_r3m3mb3r2pu70nasm1l3

API requests made without authentication will fail with the status code 401: Unauthorized. All API requests must be made over HTTPS.

Secure your requests
Do not set VERIFY_PEER to FALSE. Ensure your server verifies the SSL connection to Paystack.

Pagination
Pagination allows you to efficiently retrieve large sets of data from the Paystack API. Instead of returning all results at once, which, could slow and resource intensive, pagination breaks the sets of data into smaller chunks before sending them. This approach improves performance, reduces network load, and enhances the overall user experience when working with large datasets.

Pagination Types
The Paystack API supports two types of pagination:

Offset Pagination
Cursor Pagination
Each type has its own use cases and implementation details.

Offset Pagination
Offset pagination allows you to request specific page and perPage values when fetching records. The page parameter specifies which page of records to retrieve, while the perPage parameter specifies how many records you want to retrieve per page.

To use offset pagination, include the page and perPage parameters as query parameters in your API request:

Query Parameters
page
Number
The page to retrieve
perPage
Number
This specifies the number of records to return per request. Default: 50
Additional Meta Parameter
The meta object in the JSON response from GET /transaction includes a total_volume parameter, which is the sum of all the transactions that have been fetched.

GET/transaction?page=1&perPage=50

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction?page=1&perPage=50"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Offset Pagination Metadata
{
  "meta": {
    "total": 7316,
    "total_volume": 397800,
    "skipped": 0,
    "perPage": 50,
    "page": 1,
    "pageCount": 147
  }
}
Cursor Pagination
Cursor pagination uses a unique identifier called a cursor to keep track of where in the dataset to continue from. This method is more efficient for retrieving large datasets and provides more consistent results when items are being added or removed frequently.

To use cursor pagination, include the use_cursor query parameter and set it to true on your first fetch request. The meta object in the JSON response will contain a parameter called next that contains the cursor for the next set of records, and a previous parameter for the previous page. Include these as query parameters in subsequent requests to fetch the next or previous set of data.

Query Parameters
use_cursor
Boolean
Set this to true to retrieve results using cursor pagination
next
String
A cursor to use in pagination, next points to the next page of the dataset. Set this to the next cursor received in the meta object of a previous request.
previous
String
A cursor to use in pagination, previous previous page of the dataset. Set this to the previous cursor received in the meta object of a previous request.
perPage
Number
The number of records to return per request. Default: 50
Cursor Pagination Availability
Cursor-based pagination is currently only available on the following endpoints:

Transactions
Customers
Dedicated Accounts
Transfer Recipient
Transfers
Disputes
GET/transaction?use_cursor=true&perPage=50

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction?use_cursor=true&perPage=50"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Cursor Pagination Metadata
{
  "meta": {
    "next": "****************************",
    "previous": "null",
    "perPage": 49
  }
}
Best Practices
Choose the Right Pagination Type: Use offset-based pagination for smaller, static datasets. For larger or frequently updated datasets, prefer cursor-based pagination.
Set Reasonable Page Sizes: Start with the default of 50 items per page. Adjust based on your specific needs, but avoid requesting too many items at once more than 1000 items at once to prevent performance issues.
Handle Edge Cases: Always check if there are more pages available. For offset-based pagination, it’s best to fetch pages until no results are returned. For cursor-based pagination, the absence of a next cursor indicates you've reached the end.
Implement Error Handling: Be prepared to handle pagination-related errors, such as invalid page numbers or cursors.
Consider Rate Limits: Be mindful of Paystack's rate limits when implementing pagination, especially if you're fetching large amounts of data. Implement appropriate delays between requests if necessary.
Cache Wisely: If you're caching paginated results, ensure your cache invalidation strategy accounts for potential changes in the dataset.
By following these best practices, you'll be able to efficiently work with large datasets in the Paystack API while providing a smooth experience for your users.


Errors
Paystack's API is RESTful and as such, uses conventional HTTP response codes to indicate the success or failure of requests.

HTTP Codes
200
Request was successful and intended action was carried out. Note that we will always send a 200 if a charge or verify request was made. Do check the data object to know how the charge went (i.e. successful or failed).
201
A resource has successfully been created.
400
A validation or client side error occurred and the request was not fulfilled.
401
The request was not authorized. This can be triggered by passing an invalid secret key in the authorization header or the lack of one.
404
Request could not be fulfilled as the request resource does not exist.
5xx
Request could not be fulfilled due to an error on Paystack's end. This shouldn't happen so please report as soon as you encounter any instance of this.

api
Search API Reference
DocsAPI
Sign up
Introduction
Authentication
Pagination
Errors
API Endpoints
Transactions
Initialize Transaction
Verify Transaction
List Transactions
Fetch Transaction
Charge Authorization
View Transaction Timeline
Transaction Totals
Export Transactions
Partial Debit
Transaction Splits
Terminal
Virtual Terminal
Customers
Dedicated Virtual Accounts
Apple Pay
Subaccounts
Plans
Subscriptions
Products
Payment Pages
Payment Requests
Settlements
Transfer Recipients
Transfers
Transfers Control
Bulk Charges
Integration
Charge
Disputes
Refunds
Verification
Miscellaneous
Transactions
The Transactions API allows you create and manage payments on your integration.

Initialize Transaction
Initialize a transaction from your backend

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Body Parameters
amount
String
Amount should be in the subunit of the supported currency
email
String
Customer's email address
Show optional parameters
POST/transaction/initialize

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/initialize"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ 
  "email": "<EMAIL>", 
  "amount": "20000"
}'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Authorization URL created",
  "data": {
    "authorization_url": "https://checkout.paystack.com/3ni8kdavz62431k",
    "access_code": "3ni8kdavz62431k",
    "reference": "re4lyvq3s3"
  }
}
Verify Transaction
Confirm the status of a transaction

Transaction ID data type
If you plan to store or make use of the the transaction ID, you should represent it as a unsigned 64-bit integer. To learn more, check out our changelog.

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
reference
String
The transaction reference used to intiate the transaction
GET/transaction/verify/:reference

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/verify/{reference}"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Verification successful",
  "data": {
    "id": **********,
    "domain": "test",
    "status": "success",
    "reference": "re4lyvq3s3",
    "receipt_number": null,
    "amount": 40333,
    "message": null,
    "gateway_response": "Successful",
    "paid_at": "2024-08-22T09:15:02.000Z",
    "created_at": "2024-08-22T09:14:24.000Z",
    "channel": "card",
    "currency": "NGN",
    "ip_address": "*************",
    "metadata": "",
    "log": {
      "start_time": **********,
      "time_spent": 4,
      "attempts": 1,
      "errors": 0,
      "success": true,
      "mobile": false,
      "input": [],
      "history": [
        {
          "type": "action",
          "message": "Attempted to pay with card",
          "time": 3
        },
        {
          "type": "success",
          "message": "Successfully paid with card",
          "time": 4
        }
      ]
    },
    "fees": 10283,
    "fees_split": null,
    "authorization": {
      "authorization_code": "AUTH_uh8bcl3zbn",
      "bin": "408408",
      "last4": "4081",
      "exp_month": "12",
      "exp_year": "2030",
      "channel": "card",
      "card_type": "visa ",
      "bank": "TEST BANK",
      "country_code": "NG",
      "brand": "visa",
      "reusable": true,
      "signature": "SIG_yEXu7dLBeqG0kU7g95Ke",
      "account_name": null
    },
    "customer": {
      "id": *********,
      "first_name": null,
      "last_name": null,
      "email": "<EMAIL>",
      "customer_code": "CUS_1rkzaqsv4rrhqo6",
      "phone": null,
      "metadata": null,
      "risk_action": "default",
      "international_format_phone": null
    },
    "plan": null,
    "split": {},
    "order_id": null,
    "paidAt": "2024-08-22T09:15:02.000Z",
    "createdAt": "2024-08-22T09:14:24.000Z",
    "requested_amount": 30050,
    "pos_transaction_data": null,
    "source": null,
    "fees_breakdown": null,
    "connect": null,
    "transaction_date": "2024-08-22T09:14:24.000Z",
    "plan_object": {},
    "subaccount": {}
  }
}
List Transactions
List transactions carried out on your integration

Transaction ID data type
If you plan to store or make use of the the transaction ID, you should represent it as a unsigned 64-bit integer. To learn more, check out our changelog.

Headers
authorization
String
Set value to Bearer SECRET_KEY
Query Parameters
perPage
Integer
Specify how many records you want to retrieve per page. If not specify we use a default value of 50.
page
Integer
Specify exactly what page you want to retrieve. If not specify we use a default value of 1.
Show optional parameters
GET/transaction

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Transactions retrieved",
  "data": [
    {
      "id": **********,
      "domain": "test",
      "status": "success",
      "reference": "re4lyvq3s3",
      "amount": 40333,
      "message": null,
      "gateway_response": "Successful",
      "paid_at": "2024-08-22T09:15:02.000Z",
      "created_at": "2024-08-22T09:14:24.000Z",
      "channel": "card",
      "currency": "NGN",
      "ip_address": "*************",
      "metadata": null,
      "log": {
        "start_time": **********,
        "time_spent": 4,
        "attempts": 1,
        "errors": 0,
        "success": true,
        "mobile": false,
        "input": [],
        "history": [
          {
            "type": "action",
            "message": "Attempted to pay with card",
            "time": 3
          },
          {
            "type": "success",
            "message": "Successfully paid with card",
            "time": 4
          }
        ]
      },
      "fees": 10283,
      "fees_split": null,
      "customer": {
        "id": *********,
        "first_name": null,
        "last_name": null,
        "email": "<EMAIL>",
        "phone": null,
        "metadata": {
          "custom_fields": [
            {
              "display_name": "Customer email",
              "variable_name": "customer_email",
              "value": "<EMAIL>"
            }
          ]
        },
        "customer_code": "CUS_1rkzaqsv4rrhqo6",
        "risk_action": "default"
      },
      "authorization": {
        "authorization_code": "AUTH_uh8bcl3zbn",
        "bin": "408408",
        "last4": "4081",
        "exp_month": "12",
        "exp_year": "2030",
        "channel": "card",
        "card_type": "visa ",
        "bank": "TEST BANK",
        "country_code": "NG",
        "brand": "visa",
        "reusable": true,
        "signature": "SIG_yEXu7dLBeqG0kU7g95Ke",
        "account_name": null
      },
      "plan": {},
      "split": {},
      "subaccount": {},
      "order_id": null,
      "paidAt": "2024-08-22T09:15:02.000Z",
      "createdAt": "2024-08-22T09:14:24.000Z",
      "requested_amount": 30050,
      "source": {
        "source": "merchant_api",
        "type": "api",
        "identifier": null,
        "entry_point": "transaction_initialize"
      },
      "connect": null,
      "pos_transaction_data": null
    }
  ],
  "meta": {
    "next": "****************************",
    "previous": null,
    "perPage": 50
  }
}
Fetch Transaction
Get details of a transaction carried out on your integration

Transaction ID data type
If you plan to store or make use of the the transaction ID, you should represent it as a unsigned 64-bit integer. To learn more, check out our changelog.

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
id
Integer
An ID for the transaction to fetch
GET/transaction/:id

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/{id}"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Transaction retrieved",
  "data": {
    "id": **********,
    "domain": "test",
    "status": "success",
    "reference": "re4lyvq3s3",
    "receipt_number": null,
    "amount": 40333,
    "message": null,
    "gateway_response": "Successful",
    "helpdesk_link": null,
    "paid_at": "2024-08-22T09:15:02.000Z",
    "created_at": "2024-08-22T09:14:24.000Z",
    "channel": "card",
    "currency": "NGN",
    "ip_address": "*************",
    "metadata": "",
    "log": {
      "start_time": **********,
      "time_spent": 4,
      "attempts": 1,
      "errors": 0,
      "success": true,
      "mobile": false,
      "input": [],
      "history": [
        {
          "type": "action",
          "message": "Attempted to pay with card",
          "time": 3
        },
        {
          "type": "success",
          "message": "Successfully paid with card",
          "time": 4
        }
      ]
    },
    "fees": 10283,
    "fees_split": null,
    "authorization": {
      "authorization_code": "AUTH_uh8bcl3zbn",
      "bin": "408408",
      "last4": "4081",
      "exp_month": "12",
      "exp_year": "2030",
      "channel": "card",
      "card_type": "visa ",
      "bank": "TEST BANK",
      "country_code": "NG",
      "brand": "visa",
      "reusable": true,
      "signature": "SIG_yEXu7dLBeqG0kU7g95Ke",
      "account_name": null
    },
    "customer": {
      "id": *********,
      "first_name": null,
      "last_name": null,
      "email": "<EMAIL>",
      "customer_code": "CUS_1rkzaqsv4rrhqo6",
      "phone": null,
      "metadata": {
        "custom_fields": [
          {
            "display_name": "Customer email",
            "variable_name": "customer_email",
            "value": "<EMAIL>"
          }
        ]
      },
      "risk_action": "default",
      "international_format_phone": null
    },
    "plan": {},
    "subaccount": {},
    "split": {},
    "order_id": null,
    "paidAt": "2024-08-22T09:15:02.000Z",
    "createdAt": "2024-08-22T09:14:24.000Z",
    "requested_amount": 30050,
    "pos_transaction_data": null,
    "source": {
      "type": "api",
      "source": "merchant_api",
      "identifier": null
    },
    "fees_breakdown": null,
    "connect": null
  }
}
Charge Authorization
All authorizations marked as reusable can be charged with this endpoint whenever you need to receive payments

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Body Parameters
amount
String
Amount should be in the subunit of the supported currency
email
String
Customer's email address
authorization_code
String
Valid authorization code to charge
Show optional parameters
POST/transaction/charge_authorization

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/charge_authorization"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ 
  "email": "<EMAIL>", 
  "amount": "20000", 
  "authorization_code": "AUTH_72btv547"
}'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Charge attempted",
  "data": {
    "amount": 35247,
    "currency": "NGN",
    "transaction_date": "2024-08-22T10:53:49.000Z",
    "status": "success",
    "reference": "0m7frfnr47ezyxl",
    "domain": "test",
    "metadata": "",
    "gateway_response": "Approved",
    "message": null,
    "channel": "card",
    "ip_address": null,
    "log": null,
    "fees": 10247,
    "authorization": {
      "authorization_code": "AUTH_uh8bcl3zbn",
      "bin": "408408",
      "last4": "4081",
      "exp_month": "12",
      "exp_year": "2030",
      "channel": "card",
      "card_type": "visa ",
      "bank": "TEST BANK",
      "country_code": "NG",
      "brand": "visa",
      "reusable": true,
      "signature": "SIG_yEXu7dLBeqG0kU7g95Ke",
      "account_name": null
    },
    "customer": {
      "id": *********,
      "first_name": null,
      "last_name": null,
      "email": "<EMAIL>",
      "customer_code": "CUS_1rkzaqsv4rrhqo6",
      "phone": null,
      "metadata": {
        "custom_fields": [
          {
            "display_name": "Customer email",
            "variable_name": "customer_email",
            "value": "<EMAIL>"
          }
        ]
      },
      "risk_action": "default",
      "international_format_phone": null
    },
    "plan": null,
    "id": **********
  }
}
View Transaction Timeline
View the timeline of a transaction

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
id_or_reference
String
The ID or the reference of the transaction
GET/transaction/timeline/:id_or_reference

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/timeline/{id_or_reference}"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Timeline retrieved",
  "data": {
    "start_time": **********,
    "time_spent": 4,
    "attempts": 1,
    "errors": 0,
    "success": true,
    "mobile": false,
    "input": [],
    "history": [
      {
        "type": "action",
        "message": "Attempted to pay with card",
        "time": 3
      },
      {
        "type": "success",
        "message": "Successfully paid with card",
        "time": 4
      }
    ]
  }
}
Transaction Totals
Total amount received on your account

Headers
authorization
String
Set value to Bearer SECRET_KEY
Query Parameters
perPage
Integer
Specify how many records you want to retrieve per page. If not specify we use a default value of 50.
page
Integer
Specify exactly what page you want to retrieve. If not specify we use a default value of 1.
Show optional parameters
GET/transaction/totals

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/totals"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Transaction totals",
  "data": {
    "total_transactions": 42670,
    "total_volume": **********,
    "total_volume_by_currency": [
      {
        "currency": "NGN",
        "amount": **********
      },
      {
        "currency": "USD",
        "amount": 28000
      }
    ],
    "pending_transfers": **********,
    "pending_transfers_by_currency": [
      {
        "currency": "NGN",
        "amount": **********
      },
      {
        "currency": "USD",
        "amount": 28000
      }
    ]
  }
}
Export Transaction
Export a list of transactions carried out on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
Query Parameters
perPage
Integer
Specify how many records you want to retrieve per page. If not specify we use a default value of 50.
page
Integer
Specify exactly what page you want to retrieve. If not specify we use a default value of 1.
Show optional parameters
GET/transaction/export

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/export"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Export successful",
  "data": {
    "path": "https://s3.eu-west-1.amazonaws.com/files.paystack.co/exports/463433/transactions/Integration_name_transactions_1724324423843.csv?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAI7CL5IZL2DJHOPPA%2F20240822%2Feu-west-1%2Fs3%2Faws4_request&X-Amz-Date=20240822T110023Z&X-Amz-Expires=60&X-Amz-Signature=40525f4f361e07c09a445a1a6888d135758abd507ed988ee744c2d94ea14cf1e&X-Amz-SignedHeaders=host",
    "expiresAt": "2024-08-22 11:01:23"
  }
}
Partial Debit
Retrieve part of a payment from a customer

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Body Parameters
authorization_code
String
Authorization Code
currency
String
Specify the currency you want to debit. Allowed values are NGN or GHS.
amount
String
Amount should be in the subunit of the supported currency
email
String
Customer's email address (attached to the authorization code)
Show optional parameters
POST/transaction/partial_debit

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/transaction/partial_debit"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ 
  "authorization_code": "AUTH_72btv547", 
  "currency": "NGN", 
  "amount": "20000",
  "email": "<EMAIL>"
}'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Charge attempted",
  "data": {
    "amount": 50000,
    "currency": "NGN",
    "transaction_date": "2024-08-22T11:13:48.000Z",
    "status": "success",
    "reference": "ofuhmnzw05vny9j",
    "domain": "test",
    "metadata": "",
    "gateway_response": "Approved",
    "message": null,
    "channel": "card",
    "ip_address": null,
    "log": null,
    "fees": 10350,
    "authorization": {
      "authorization_code": "AUTH_uh8bcl3zbn",
      "bin": "408408",
      "last4": "4081",
      "exp_month": "12",
      "exp_year": "2030",
      "channel": "card",
      "card_type": "visa ",
      "bank": "TEST BANK",
      "country_code": "NG",
      "brand": "visa",
      "reusable": true,
      "signature": "SIG_yEXu7dLBeqG0kU7g95Ke",
      "account_name": null
    },
    "customer": {
      "id": *********,
      "first_name": null,
      "last_name": null,
      "email": "<EMAIL>",
      "customer_code": "CUS_1rkzaqsv4rrhqo6",
      "phone": null,
      "metadata": {
        "custom_fields": [
          {
            "display_name": "Customer email",
            "variable_name": "customer_email",
            "value": "<EMAIL>"
          }
        ]
      },
      "risk_action": "default",
      "international_format_phone": null
    },
    "plan": 0,
    "requested_amount": 50000,
    "id": **********
  }
}

api
Search API Reference
DocsAPI
Sign up
Introduction
Authentication
Pagination
Errors
API Endpoints
Transactions
Transaction Splits
Terminal
Virtual Terminal
Customers
Dedicated Virtual Accounts
Apple Pay
Subaccounts
Plans
Subscriptions
Products
Payment Pages
Create Page
List Pages
Fetch Page
Update Page
Check Slug Availability
Add Products
Payment Requests
Settlements
Transfer Recipients
Transfers
Transfers Control
Bulk Charges
Integration
Charge
Disputes
Refunds
Verification
Miscellaneous
Payment Pages
The Payment Pages API provides a quick and secure way to collect payment for products.

Create Payment Page
Create a payment page on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Body Parameters
name
String
Name of page
Show optional parameters
POST/page

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/page"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ 
   "name": "Buttercup Brunch", 
   "amount": 500000,
   "description": "Gather your friends for the ritual that is brunch",
}'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Page created",
  "data": {
    "name": "Onipan and Hassan",
    "description": "",
    "amount": 10000,
    "split_code": "SPL_yqSS1FvrBz",
    "integration": 463433,
    "domain": "test",
    "slug": "1got2y8unp",
    "currency": "NGN",
    "type": "payment",
    "collect_phone": false,
    "active": true,
    "published": true,
    "migrate": false,
    "id": 1308510,
    "createdAt": "2023-03-21T11:44:16.412Z",
    "updatedAt": "2023-03-21T11:44:16.412Z"
  }
}
List Payment Pages
List payment pages available on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
Query Parameters
perPage
Integer
Specify how many records you want to retrieve per page. If not specify we use a default value of 50.
page
Integer
Specify exactly what page you want to retrieve. If not specify we use a default value of 1.
Show optional parameters
GET/page

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/page"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization"  -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Pages retrieved",
  "data": [
    {
      "integration": 100073,
      "plan": 1716,
      "domain": "test",
      "name": "Subscribe to plan: Weekly small chops",
      "description": null,
      "amount": null,
      "currency": "NGN",
      "slug": "sR7Ohx2iVd",
      "custom_fields": null,
      "redirect_url": null,
      "active": true,
      "migrate": null,
      "id": 2223,
      "createdAt": "2016-10-01T10:59:11.000Z",
      "updatedAt": "2016-10-01T10:59:11.000Z"
    },
    {
      "integration": 100073,
      "plan": null,
      "domain": "test",
      "name": "Special",
      "description": "Special page",
      "amount": 10000,
      "currency": "NGN",
      "slug": "special-me",
      "custom_fields": [
        {
          "display_name": "Speciality",
          "variable_name": "speciality"
        },
        {
          "display_name": "Age",
          "variable_name": "age"
        }
      ],
      "redirect_url": "http://special.url",
      "active": true,
      "migrate": null,
      "id": 1807,
      "createdAt": "2016-09-09T19:18:37.000Z",
      "updatedAt": "2016-09-14T17:51:49.000Z"
    }
  ],
  "meta": {
    "total": 2,
    "skipped": 0,
    "perPage": "3",
    "page": 1,
    "pageCount": 1
  }
}
Fetch Payment Page
Get details of a payment page on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
id_or_slug
String
The page ID or slug you want to fetch
GET/page/:id_or_slug

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/page/:id_or_slug"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization"  -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Page retrieved",
  "data": {
    "integration": 100032,
    "domain": "test",
    "name": "Offering collections",
    "description": "Give unto the Lord, and it shall be multiplied ten-fold to you.",
    "amount": null,
    "currency": "NGN",
    "slug": "5nApBwZkvY",
    "active": true,
    "id": 18,
    "createdAt": "2016-03-30T00:49:57.000Z",
    "updatedAt": "2016-03-30T00:49:57.000Z",
    "products": [
      {
        "product_id": 523,
        "name": "Product Four",
        "description": "Product Four Description",
        "product_code": "PROD_l9p81u9pkjqjunb",
        "page": 18,
        "price": 500000,
        "currency": "NGN",
        "quantity": 0,
        "type": "good",
        "features": null,
        "is_shippable": 0,
        "domain": "test",
        "integration": 343288,
        "active": 1,
        "in_stock": 1
      },
      {
        "product_id": 522,
        "name": "Product Five",
        "description": "Product Five Description",
        "product_code": "PROD_8ne9cxutagmtsyz",
        "page": 18,
        "price": 500000,
        "currency": "NGN",
        "quantity": 0,
        "type": "good",
        "features": null,
        "is_shippable": 0,
        "domain": "test",
        "integration": 343288,
        "active": 1,
        "in_stock": 0
      }
    ]
  }
}
Update Payment Page
Update a payment page details on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Path Parameters
id_or_slug
String
Page ID or slug
Body Parameters
name
String
Name of page
description
String
A description for this page
Show optional parameters
PUT/page/:id_or_slug

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/page/:id_or_slug"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ 
  "name": "Buttercup Brunch", 
  "amount": 500000
  "description": "Gather your friends for the ritual that is brunch",
}'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X PUT
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Page updated",
  "data": {
    "domain": "test",
    "name": "Buttercup Brunch",
    "description": "Gather your friends for the ritual that is brunch",
    "amount": null,
    "currency": "NGN",
    "slug": "5nApBwZkvY",
    "active": true,
    "id": 18,
    "integration": 100032,
    "createdAt": "2016-03-30T00:49:57.000Z",
    "updatedAt": "2016-03-30T04:44:35.000Z"
  }
}
Check Slug Availability
Check the availability of a slug for a payment page

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
slug
String
URL slug to be confirmed
GET/page/check_slug_availability/:slug

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/page/check_slug_availability/:slug"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Slug is available"
}
Add Products
Add products to a payment page

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Path Parameters
id
Integer
Id of the payment page
Body Parameters
product
Array of Integer
Ids of all the products
POST/page/:id/product

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/page/:id/product"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ "product": [473, 292] }'


curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Products added to page",
  "data": {
    "integration": 343288,
    "plan": null,
    "domain": "test",
    "name": "Demo Products Page",
    "description": "Demo Products Page",
    "amount": null,
    "currency": "NGN",
    "slug": "demoproductspage",
    "custom_fields": [],
    "type": "product",
    "redirect_url": null,
    "success_message": null,
    "collect_phone": false,
    "active": true,
    "published": true,
    "migrate": true,
    "notification_email": null,
    "metadata": {},
    "id": 102859,
    "createdAt": "2019-06-29T16:21:24.000Z",
    "updatedAt": "2019-06-29T16:28:11.000Z",
    "products": [
      {
        "product_id": 523,
        "name": "Product Four",
        "description": "Product Four Description",
        "product_code": "PROD_l9p81u9pkjqjunb",
        "page": 102859,
        "price": 500000,
        "currency": "NGN",
        "quantity": 0,
        "type": "good",
        "features": null,
        "is_shippable": 0,
        "domain": "test",
        "integration": 343288,
        "active": 1,
        "in_stock": 1
      }
    ]
  }
}


api
Search API Reference
DocsAPI
Sign up
Introduction
Authentication
Pagination
Errors
API Endpoints
Transactions
Transaction Splits
Terminal
Virtual Terminal
Customers
Dedicated Virtual Accounts
Apple Pay
Subaccounts
Plans
Subscriptions
Products
Payment Pages
Payment Requests
Create Payment Request
List Payment Requests
Fetch Payment Request
Verify Payment Request
Send Notification
Payment Request Total
Finalize Payment Request
Update Payment Request
Archive Payment Request
Settlements
Transfer Recipients
Transfers
Transfers Control
Bulk Charges
Integration
Charge
Disputes
Refunds
Verification
Miscellaneous
Payment Requests
The Payment Requests API allows you manage requests for payment of goods and services.

Create Payment Request
Create a payment request for a transaction on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Body Parameters
customer
String
Customer id or code
amount
Integer
Payment request amount. It should be used when line items and tax values aren't specified.
Show optional parameters
POST/paymentrequest

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ "description": "a test invoice",
      "line_items": [
        {"name": "item 1", "amount": 20000},
        {"name": "item 2", "amount": 20000}
      ],
      "tax": [
        {"name": "VAT", "amount": 2000}
      ],
      "customer": "CUS_xwaj0txjryg393b",
      "due_date": "2020-07-08"
    }'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request created",
  "data": {
    "id": 3136406,
    "domain": "test",
    "amount": 42000,
    "currency": "NGN",
    "due_date": "2020-07-08T00:00:00.000Z",
    "has_invoice": true,
    "invoice_number": 1,
    "description": "a test invoice",
    "line_items": [
      {
        "name": "item 1",
        "amount": 20000
      },
      {
        "name": "item 2",
        "amount": 20000
      }
    ],
    "tax": [
      {
        "name": "VAT",
        "amount": 2000
      }
    ],
    "request_code": "PRQ_1weqqsn2wwzgft8",
    "status": "pending",
    "paid": false,
    "metadata": null,
    "notifications": [],
    "offline_reference": "4286263136406",
    "customer": 25833615,
    "created_at": "2020-06-29T16:07:33.073Z"
  }
}
List Payment Requests
List the payment requests available on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
Query Parameters
perPage
Integer
Specify how many records you want to retrieve per page. If not specify we use a default value of 50.
page
Integer
Specify the page you want to fetch payment requests from. If not specify we use a default value of 1.
customer
String
Filter by customer ID
status
String
Filter by payment request status
currency
String
Filter by currency
include_archive
String
Show archived payment requests
Show optional parameters
GET/paymentrequest

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment requests retrieved",
  "data": [
    {
      "id": 3136406,
      "domain": "test",
      "amount": 42000,
      "currency": "NGN",
      "due_date": "2020-07-08T00:00:00.000Z",
      "has_invoice": true,
      "invoice_number": 1,
      "description": "a test invoice",
      "pdf_url": null,
      "line_items": [
        {
          "name": "item 1",
          "amount": 20000
        },
        {
          "name": "item 2",
          "amount": 20000
        }
      ],
      "tax": [
        {
          "name": "VAT",
          "amount": 2000
        }
      ],
      "request_code": "PRQ_1weqqsn2wwzgft8",
      "status": "pending",
      "paid": false,
      "paid_at": null,
      "metadata": null,
      "notifications": [],
      "offline_reference": "4286263136406",
      "customer": {
        "id": 25833615,
        "first_name": "Damilola",
        "last_name": "Odujoko",
        "email": "<EMAIL>",
        "customer_code": "CUS_xwaj0txjryg393b",
        "phone": null,
        "metadata": {
          "calling_code": "+234"
        },
        "risk_action": "default",
        "international_format_phone": null
      },
      "created_at": "2020-06-29T16:07:33.000Z"
    }
  ],
  "meta": {
    "total": 1,
    "skipped": 0,
    "perPage": 50,
    "page": 1,
    "pageCount": 1
  }
}
Fetch Payment Request
Get details of a payment request on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
id_or_code
String
The payment request ID or code you want to fetch
GET/paymentrequest/:id_or_code

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/:id_or_code"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request retrieved",
  "data": {
    "transactions": [],
    "domain": "test",
    "request_code": "PRQ_1weqqsn2wwzgft8",
    "description": "a test invoice",
    "line_items": [
      {
        "name": "item 1",
        "amount": 20000
      },
      {
        "name": "item 2",
        "amount": 20000
      }
    ],
    "tax": [
      {
        "name": "VAT",
        "amount": 2000
      }
    ],
    "amount": 42000,
    "discount": null,
    "currency": "NGN",
    "due_date": "2020-07-08T00:00:00.000Z",
    "status": "pending",
    "paid": false,
    "paid_at": null,
    "metadata": null,
    "has_invoice": true,
    "invoice_number": 1,
    "offline_reference": "4286263136406",
    "pdf_url": null,
    "notifications": [],
    "archived": false,
    "source": "user",
    "payment_method": null,
    "note": null,
    "amount_paid": null,
    "id": 3136406,
    "integration": 428626,
    "customer": {
      "transactions": [],
      "subscriptions": [],
      "authorizations": [],
      "first_name": "Damilola",
      "last_name": "Odujoko",
      "email": "<EMAIL>",
      "phone": null,
      "metadata": {
        "calling_code": "+234"
      },
      "domain": "test",
      "customer_code": "CUS_xwaj0txjryg393b",
      "risk_action": "default",
      "id": 25833615,
      "integration": 428626,
      "createdAt": "2020-06-29T16:06:53.000Z",
      "updatedAt": "2020-06-29T16:06:53.000Z"
    },
    "createdAt": "2020-06-29T16:07:33.000Z",
    "updatedAt": "2020-06-29T16:07:33.000Z",
    "pending_amount": 42000
  }
}
Verify Payment Request
Verify details of a payment request on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
code
String
Payment Request code
GET/paymentrequest/verify/:code

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/verify/:code"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request retrieved",
  "data": {
    "id": 3136406,
    "domain": "test",
    "amount": 42000,
    "currency": "NGN",
    "due_date": "2020-07-08T00:00:00.000Z",
    "has_invoice": true,
    "invoice_number": 1,
    "description": "a test invoice",
    "pdf_url": null,
    "line_items": [
      {
        "name": "item 1",
        "amount": 20000
      },
      {
        "name": "item 2",
        "amount": 20000
      }
    ],
    "tax": [
      {
        "name": "VAT",
        "amount": 2000
      }
    ],
    "request_code": "PRQ_1weqqsn2wwzgft8",
    "status": "pending",
    "paid": false,
    "paid_at": null,
    "metadata": null,
    "notifications": [],
    "offline_reference": "4286263136406",
    "customer": {
      "id": 25833615,
      "first_name": "Damilola",
      "last_name": "Odujoko",
      "email": "<EMAIL>",
      "customer_code": "CUS_xwaj0txjryg393b",
      "phone": null,
      "metadata": {
        "calling_code": "+234"
      },
      "risk_action": "default",
      "international_format_phone": null
    },
    "created_at": "2020-06-29T16:07:33.000Z",
    "integration": {
      "key": "pk_test_xxxxxxxx",
      "name": "Paystack Documentation",
      "logo": "https://s3-eu-west-1.amazonaws.com/pstk-integration-logos/paystack.jpg",
      "allowed_currencies": [
        "NGN",
        "USD"
      ]
    },
    "pending_amount": 42000
  }
}
Send Notification
Send notification of a payment request to your customers

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
code
String
Payment Request code
POST/paymentrequest/notify/:code

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/notify/:id_or_code"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"

curl "$url" -H "$authorization" -H "$content_type" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Notification sent"
}
Payment Request Total
Get payment requests metric

Headers
authorization
String
Set value to Bearer SECRET_KEY
GET/paymentrequest/totals

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/totals"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization"  -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request totals",
  "data": {
    "pending": [
      {
        "currency": "NGN",
        "amount": 42000
      },
      {
        "currency": "USD",
        "amount": 0
      }
    ],
    "successful": [
      {
        "currency": "NGN",
        "amount": 0
      },
      {
        "currency": "USD",
        "amount": 0
      }
    ],
    "total": [
      {
        "currency": "NGN",
        "amount": 42000
      },
      {
        "currency": "USD",
        "amount": 0
      }
    ]
  }
}
Finalize Payment Request
Finalize a draft payment request

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
code
String
Payment Request code
Body Parameters
send_notification
Boolean
Indicates whether Paystack sends an email notification to customer. Defaults to true
POST/paymentrequest/finalize/:code

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/finalize/:id_or_code"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request finalized",
  "data": {
    "id": 3136496,
    "domain": "test",
    "amount": 45000,
    "currency": "NGN",
    "due_date": "2020-06-30T22:59:59.000Z",
    "has_invoice": true,
    "invoice_number": 2,
    "description": "Testing Invoice",
    "pdf_url": null,
    "line_items": [
      {
        "name": "Water",
        "amount": 15000,
        "quantity": 1
      },
      {
        "name": "Bread",
        "amount": 30000,
        "quantity": 1
      }
    ],
    "tax": [],
    "request_code": "PRQ_rtjkfk1tpmvqo40",
    "status": "pending",
    "paid": false,
    "paid_at": null,
    "metadata": null,
    "notifications": [],
    "offline_reference": "4286263136496",
    "customer": {
      "id": 25833615,
      "first_name": "Damilola",
      "last_name": "Odujoko",
      "email": "<EMAIL>",
      "customer_code": "CUS_xwaj0txjryg393b",
      "phone": null,
      "metadata": {
        "calling_code": "+234"
      },
      "risk_action": "default",
      "international_format_phone": null
    },
    "created_at": "2020-06-29T16:22:35.000Z",
    "pending_amount": 45000
  }
}
Update Payment Request
Update a payment request details on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Path Parameters
id_or_code
String
Payment Request ID or slug
Body Parameters
customer
String
Customer id or code
amount
Integer
Payment request amount. Only useful if line items and tax values are ignored. endpoint will throw a friendly warning if neither is available.
Show optional parameters
PUT/paymentrequest/:id_or_code

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/:id_or_code"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ 
  "description": "Update test invoice", 
  "due_date": "2017-05-10" 
}'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X PUT
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request updated",
  "data": {
    "id": 3136496,
    "domain": "test",
    "amount": 45000,
    "currency": "NGN",
    "due_date": "2020-06-30T22:59:59.000Z",
    "has_invoice": true,
    "invoice_number": 2,
    "description": "Update Testing",
    "pdf_url": null,
    "line_items": [
      {
        "name": "Water",
        "amount": 15000,
        "quantity": 1
      },
      {
        "name": "Bread",
        "amount": 30000,
        "quantity": 1
      }
    ],
    "tax": [],
    "request_code": "PRQ_rtjkfk1tpmvqo40",
    "status": "pending",
    "paid": false,
    "paid_at": null,
    "metadata": null,
    "notifications": [],
    "offline_reference": "4286263136496",
    "customer": {
      "id": 25833615,
      "first_name": "Doc",
      "last_name": "Test",
      "email": "<EMAIL>",
      "customer_code": "CUS_xwaj0txjryg393b",
      "phone": null,
      "metadata": {
        "calling_code": "+234"
      },
      "risk_action": "default",
      "international_format_phone": null
    },
    "created_at": "2020-06-29T16:22:35.000Z"
  }
}
Archive Payment Request
Used to archive a payment request. A payment request will no longer be fetched on list or returned on verify

Headers
authorization
String
Set value to Bearer SECRET_KEY
Path Parameters
code
String
Payment Request code
POST/paymentrequest/archive/:code

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/paymentrequest/archive/:id_or_code"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"

curl "$url" -H "$authorization" -H "$content_type" -X POST
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment request has been archived"
}

api
Search API Reference
DocsAPI
Sign up
Introduction
Authentication
Pagination
Errors
API Endpoints
Transactions
Transaction Splits
Terminal
Virtual Terminal
Customers
Dedicated Virtual Accounts
Apple Pay
Subaccounts
Plans
Subscriptions
Products
Payment Pages
Payment Requests
Settlements
Transfer Recipients
Transfers
Transfers Control
Bulk Charges
Integration
Fetch Payment Session Timeout
Update Payment Session Timeout
Charge
Disputes
Refunds
Verification
Miscellaneous
Integration
The Integration API allows you manage some settings on your integration.

Fetch Timeout
Fetch the payment session timeout on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
GET/integration/payment_session_timeout

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/integration/payment_session_timeout"
authorization="Authorization: Bearer YOUR_SECRET_KEY"

curl "$url" -H "$authorization" -X GET
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment session timeout retrieved",
  "data": {
    "payment_session_timeout": 30
  }
}
Update Timeout
Update the payment session timeout on your integration

Headers
authorization
String
Set value to Bearer SECRET_KEY
content-type
String
Set value to application/json
Body Parameters
timeout
Integer
Time before stopping session (in seconds). Set to 0 to cancel session timeouts
PUT/integration/payment_session_timeout

cURL
cURL
Copy
#!/bin/sh
url="https://api.paystack.co/integration/payment_session_timeout"
authorization="Authorization: Bearer YOUR_SECRET_KEY"
content_type="Content-Type: application/json"
data='{ "timeout": 30 }'

curl "$url" -H "$authorization" -H "$content_type" -d "$data" -X PUT
Sample Response

200 Ok
200 Ok
Copy
{
  "status": true,
  "message": "Payment session timeout updated",
  "data": {
    "payment_session_timeout": 30
  }
}


docs
Search Documentation
DocsAPI
Sign up
Home
Payments
Terminal
Transfers
Identity Verification
Developer Tools
Libraries & Plugins
Guides
API Changelog
Paystack Documentation
Welcome to the Paystack Developer Documentation where you’ll learn how to build amazing payment experiences with the Paystack API.

Quick start
Accept Payments
Collect payments from cards, bank and mobile money accounts

Send Money
Make instant transfers to bank accounts and mobile money numbers

Identify your Customers
Verify phone numbers, bank accounts or card details

Other ways to use Paystack
Explore plugins, libraries and tools for accepting payments without the API

Accept a payment
Here’s how to use the Paystack API to accept a payment

Before you begin
Authenticate all Paystack API calls using your secret keys

Next
post
api.paystack.co/transaction/initialize
cURL
curl https://api.paystack.co/transaction/initialize 
-H "Authorization: Bearer YOUR_SECRET_KEY"
-H "Content-Type: application/json"
-X POST

Make a transfer
Here’s how quickly you can send money on Paystack

Before you begin
Authenticate all Paystack API calls using your secret keys

Next
post
api.paystack.co/transferrecipient
cURL
curl https://api.paystack.co/transferrecipient 
-H "Authorization: Bearer YOUR_SECRET_KEY"
-H "Content-Type: application/json"
-X POST

Explore demos
We’ve put together simple projects to demonstrate how to use the Paystack API for various financial services. Explore all demos or start with the most popular ones below:

Gift Store
PaystackOSS/sample-gift-store
APIS USED
Accept Payments
Verify Payments
Vue
Movie Ticket
PaystackOSS/sample-movie-ticket
APIS USED
Accept Payments
Verify Payments
Android
Invoice Payments
PaystackOSS/sample-logistics
APIS USED
Create Customer
Payment Request
Vue
Push Payment Requests
PaystackOSS/sample-restaurant
APIS USED
Payment Request
Terminal Event
React
Join Payslack
Ask questions and discuss ideas with 2000+ developers on Slack
Paystack CLI
Learn how to use our CLI to improve your integration experience
Need something else?
If you have any questions or need general help, visit our support page
Navigated to Paystack Documentation

docs
Search Documentation
DocsAPI
Sign up
Home
Payments
Terminal
Transfers
Identity Verification
Developer Tools
Overview
InlineJS
Android SDK
iOS SDK
Flutter SDK
Libraries & Plugins
Guides
API Changelog
Developer Tools
Learn how to harness our collections of tools when integrating payment into your application

Getting started
Paystack developer tools provide methods, interfaces and components to help developers throughout the course of their integration journey. Here are some tools to help you get started with your integration.

InlineJS
A collection of methods to accept payments in your web app

Android SDK
A collection of components to accept payments in your Android app

iOS SDK
A collection of components to accept payments in your iOS app

Flutter SDK
A collection of components to accept payments in your Flutter app

Explore tools
We’ve put together some tools to help with your Paystack integration. You can explore all tools or start with the most popular ones below.

OpenAPI Spec
PaystackOSS/openapi
Postman Collection
paystack-developers
Paystack CLI
PaystackOSS/paystack-cli
Python SDK
PaystackOSS/paystack-python
On this Page
Getting started
Explore tools
Join Payslack
Ask questions and discuss ideas with 2000+ developers on Slack
Paystack CLI
Learn how to use our CLI to improve your integration experience
Need something else?
If you have any questions or need general help, visit our support page

docs
Search Documentation
DocsAPI
Sign up
Home
Payments
Terminal
Transfers
Identity Verification
Developer Tools
Libraries & Plugins
Overview
Libraries
Plugins
Guides
API Changelog
Libraries and Plugins
Choose a Library
 PHP
 Java
 NodeJS
 Python
 View all
Choose a Plugin
 WordPress
 Shopify
 Magento
 Drupal
 View all
Have a Plugin in mind?
 Request a new plugin
 Submit a plugin you built
Join Payslack
Ask questions and discuss ideas with 2000+ developers on Slack
Paystack CLI
Learn how to use our CLI to improve your integration experience
Need something else?
If you have any questions or need general help, visit our support page

docs
Search Documentation
DocsAPI
Sign up
Home
Payments
Terminal
Transfers
Identity Verification
Developer Tools
Libraries & Plugins
Overview
Libraries
Plugins
Guides
API Changelog
Libraries
Community maintained
Our amazing community of developers maintain open-source libraries in different languages for integrating Paystack. Here are some that you can use.

Help and support
If you encounter any issue using these libraries, you should reach out the developer of the library or open an issue on the repo for the developer.

.Net
Paystack .Net
adebisi-fa/paystack-dotnet
Paystack .Net SDK
developerslearnit/Paystack.Net.SDK
Angular
Angular Paystack
toniton/angular-paystack
Angular 4 Paystack
ashinzekene/angular4-paystack
Cordova
Cordova Plugin Paystack
tolu360/Cordova-Plugin-Paystack
Paystack Cordova V2
mrfoh/paystackcordovav2
Erlang/OTP (rebar3)
Erl Paystack
abimbola/erlpaystack
Flutter
Paystack Flutter
mtellect/Flutter_Paystack
Flutter Paystack
wilburt/flutter_paystack
Go
Paystack Go
rpip/paystack-go
Groovy/Grails
Paystack Grails
dubems/paystack-grails
Java
Paystack Java
SeunAdelekan/PaystackJava
NativeScript
NativeScript Paystack
jogboms/nativescript-paystack
NodeJS
Paystack
kehers/paystack
Paystack API
pariola/paystack
PHP
CakePHP Paystack
jtad009/cakephp-paystack
Cashier Paystack
wisdomanthoni/cashier-paystack
Laravel Paystack
unicodeveloper/laravel-paystack
Paystack PHP
perfectmak/paystack-php
Paystack
matscode/paystack
Python
Py Paystack
edwardpopoola/pypaystack
Paystack
ojengwa/paystack
Paystack Python
andela-sjames/paystack-python
Python Paystack
Chibuzor-IN/python-paystack
Py Paystack
frier17/pypaystack
R
Paystack
ebinabo/paystack
React JS
React Paystack
iamraphson/react-paystack
React Native
React Native Paystack
tolu360/React-Native-Paystack
React Native Paystack Webview
just1and0/React-Native-Paystack-Webview
Ruby
Paystackr
ojengwa/paystackr
Paystack Ruby
IkoroVictor/paystack-ruby
Paystack Ruby API
samuel52/PaystackRubyApi
Vue JS
Vue Paystack
iamraphson/vue-paystack
Xamarin
PaystackInline Forms Plugin
Akinnagbe/PaystackInline.Forms.Plugin
Yii
Yii 2 Paystack
smladeoye/yii2-paystack
On this Page
Community maintained
Join Payslack
Ask questions and discuss ideas with 2000+ developers on Slack
Paystack CLI
Learn how to use our CLI to improve your integration experience
Need something else?
If you have any questions or need general help, visit our support page

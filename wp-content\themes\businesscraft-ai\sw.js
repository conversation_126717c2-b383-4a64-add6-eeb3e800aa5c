/**
 * Service Worker for ChatGABI PWA
 * 
 * Provides offline functionality, caching strategies, and background sync
 * for the ChatGABI AI business assistant application.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

const CACHE_NAME = 'chatgabi-v1.0.0';
const OFFLINE_CACHE = 'chatgabi-offline-v1.0.0';
const DYNAMIC_CACHE = 'chatgabi-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/dashboard',
    '/wp-content/themes/businesscraft-ai/style.css',
    '/wp-content/themes/businesscraft-ai/assets/js/chat-block.js',
    '/wp-content/themes/businesscraft-ai/assets/js/personalization.js',
    '/wp-content/themes/businesscraft-ai/assets/js/feedback-loops.js',
    '/wp-content/themes/businesscraft-ai/assets/js/offline-queue.js',
    '/wp-content/themes/businesscraft-ai/assets/images/icon-192x192.png',
    '/wp-content/themes/businesscraft-ai/assets/images/icon-512x512.png',
    '/wp-content/themes/businesscraft-ai/manifest.json'
];

// Offline fallback pages
const OFFLINE_FALLBACKS = {
    '/dashboard': '/wp-content/themes/businesscraft-ai/offline-dashboard.html',
    '/chat': '/wp-content/themes/businesscraft-ai/offline-chat.html'
};

// API endpoints that should be cached
const CACHEABLE_APIS = [
    '/wp-json/chatgabi/v1/templates',
    '/wp-json/chatgabi/v1/sectors',
    '/wp-json/chatgabi/v1/opportunities'
];

/**
 * Service Worker Installation
 */
self.addEventListener('install', event => {
    console.log('ChatGABI: Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static assets
            caches.open(CACHE_NAME).then(cache => {
                console.log('ChatGABI: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            }),
            
            // Cache offline fallbacks
            caches.open(OFFLINE_CACHE).then(cache => {
                console.log('ChatGABI: Caching offline fallbacks');
                return Promise.all(
                    Object.values(OFFLINE_FALLBACKS).map(url => 
                        cache.add(url).catch(err => {
                            console.warn(`ChatGABI: Failed to cache ${url}:`, err);
                        })
                    )
                );
            })
        ]).then(() => {
            console.log('ChatGABI: Service Worker installation complete');
            // Skip waiting to activate immediately
            return self.skipWaiting();
        })
    );
});

/**
 * Service Worker Activation
 */
self.addEventListener('activate', event => {
    console.log('ChatGABI: Service Worker activating...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && 
                            cacheName !== OFFLINE_CACHE && 
                            cacheName !== DYNAMIC_CACHE) {
                            console.log('ChatGABI: Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            
            // Take control of all clients
            self.clients.claim()
        ]).then(() => {
            console.log('ChatGABI: Service Worker activation complete');
        })
    );
});

/**
 * Fetch Event Handler
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests for caching
    if (request.method !== 'GET') {
        // Handle POST requests for offline queue
        if (isAPIRequest(request)) {
            event.respondWith(handleAPIRequest(request));
        }
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(request)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isAPIRequest(request)) {
        event.respondWith(handleAPIRequest(request));
    } else if (isPageRequest(request)) {
        event.respondWith(handlePageRequest(request));
    } else {
        event.respondWith(handleDynamicRequest(request));
    }
});

/**
 * Background Sync for offline queue
 */
self.addEventListener('sync', event => {
    console.log('ChatGABI: Background sync triggered:', event.tag);
    
    if (event.tag === 'chatgabi-offline-sync') {
        event.waitUntil(syncOfflineQueue());
    }
});

/**
 * Push notification handler
 */
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body || 'New update from ChatGABI',
            icon: '/wp-content/themes/businesscraft-ai/assets/images/icon-192x192.png',
            badge: '/wp-content/themes/businesscraft-ai/assets/images/badge-72x72.png',
            tag: data.tag || 'chatgabi-notification',
            data: data.data || {},
            actions: [
                {
                    action: 'open',
                    title: 'Open ChatGABI'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || 'ChatGABI', options)
        );
    }
});

/**
 * Notification click handler
 */
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'open' || !event.action) {
        event.waitUntil(
            clients.openWindow('/dashboard')
        );
    }
});

/**
 * Handle static asset requests
 */
async function handleStaticAsset(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fetch from network and cache
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('ChatGABI: Static asset fetch failed:', error);
        
        // Return cached version if available
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline fallback
        return new Response('Asset not available offline', { status: 503 });
    }
}

/**
 * Handle API requests
 */
async function handleAPIRequest(request) {
    const url = new URL(request.url);
    
    try {
        // For cacheable APIs, try cache first for GET requests
        if (request.method === 'GET' && isCacheableAPI(url.pathname)) {
            const cachedResponse = await caches.match(request);
            if (cachedResponse) {
                // Fetch fresh data in background
                fetchAndCache(request);
                return cachedResponse;
            }
        }
        
        // Fetch from network
        const networkResponse = await fetch(request);
        
        // Cache successful GET responses
        if (networkResponse.ok && request.method === 'GET' && isCacheableAPI(url.pathname)) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('ChatGABI: API request failed:', error);
        
        // For GET requests, try to return cached data
        if (request.method === 'GET') {
            const cachedResponse = await caches.match(request);
            if (cachedResponse) {
                return cachedResponse;
            }
        }
        
        // For POST requests, add to offline queue
        if (request.method === 'POST') {
            await addToOfflineQueue(request);
            return new Response(JSON.stringify({
                success: false,
                message: 'Request queued for when online',
                queued: true
            }), {
                status: 202,
                headers: { 'Content-Type': 'application/json' }
            });
        }
        
        return new Response(JSON.stringify({
            success: false,
            message: 'Service unavailable offline'
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

/**
 * Handle page requests
 */
async function handlePageRequest(request) {
    try {
        // Try network first for pages
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('ChatGABI: Page request failed:', error);
        
        // Try cached version
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline fallback
        const url = new URL(request.url);
        const fallbackPath = OFFLINE_FALLBACKS[url.pathname] || OFFLINE_FALLBACKS['/dashboard'];
        
        const fallbackResponse = await caches.match(fallbackPath);
        if (fallbackResponse) {
            return fallbackResponse;
        }
        
        return new Response('Page not available offline', { status: 503 });
    }
}

/**
 * Handle dynamic requests
 */
async function handleDynamicRequest(request) {
    try {
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        // Try cached version
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return new Response('Resource not available offline', { status: 503 });
    }
}

/**
 * Add request to offline queue
 */
async function addToOfflineQueue(request) {
    const requestData = {
        url: request.url,
        method: request.method,
        headers: Object.fromEntries(request.headers.entries()),
        body: await request.text(),
        timestamp: Date.now()
    };
    
    // Store in IndexedDB
    const db = await openOfflineDB();
    const transaction = db.transaction(['queue'], 'readwrite');
    const store = transaction.objectStore('queue');
    
    await store.add(requestData);
    
    // Register for background sync
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        await self.registration.sync.register('chatgabi-offline-sync');
    }
}

/**
 * Sync offline queue
 */
async function syncOfflineQueue() {
    console.log('ChatGABI: Syncing offline queue...');
    
    const db = await openOfflineDB();
    const transaction = db.transaction(['queue'], 'readwrite');
    const store = transaction.objectStore('queue');
    
    const requests = await store.getAll();
    
    for (const requestData of requests) {
        try {
            const response = await fetch(requestData.url, {
                method: requestData.method,
                headers: requestData.headers,
                body: requestData.body
            });
            
            if (response.ok) {
                // Remove from queue on success
                await store.delete(requestData.id);
                console.log('ChatGABI: Synced offline request:', requestData.url);
            }
        } catch (error) {
            console.error('ChatGABI: Failed to sync request:', error);
        }
    }
}

/**
 * Open offline database
 */
function openOfflineDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('chatgabi-offline', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            
            if (!db.objectStoreNames.contains('queue')) {
                const store = db.createObjectStore('queue', { 
                    keyPath: 'id', 
                    autoIncrement: true 
                });
                store.createIndex('timestamp', 'timestamp');
            }
        };
    });
}

/**
 * Fetch and cache in background
 */
async function fetchAndCache(request) {
    try {
        const response = await fetch(request);
        if (response.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, response);
        }
    } catch (error) {
        console.warn('ChatGABI: Background fetch failed:', error);
    }
}

/**
 * Check if request is for static asset
 */
function isStaticAsset(request) {
    const url = new URL(request.url);
    return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$/);
}

/**
 * Check if request is for API
 */
function isAPIRequest(request) {
    const url = new URL(request.url);
    return url.pathname.startsWith('/wp-json/') || url.pathname.startsWith('/wp-admin/admin-ajax.php');
}

/**
 * Check if request is for page
 */
function isPageRequest(request) {
    const url = new URL(request.url);
    return request.headers.get('accept')?.includes('text/html');
}

/**
 * Check if API endpoint should be cached
 */
function isCacheableAPI(pathname) {
    return CACHEABLE_APIS.some(api => pathname.startsWith(api));
}

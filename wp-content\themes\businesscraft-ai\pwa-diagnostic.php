<?php
/**
 * PWA Diagnostic Page
 * 
 * Access this page via browser to check PWA manifest status
 * URL: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/pwa-diagnostic.php
 */

// Load WordPress if available
$wp_loaded = false;
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php',
    '../../../../wp-load.php'
];

foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI PWA Diagnostic</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 24px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 8px; }
        .test-section h2 { margin-top: 0; color: #2563eb; }
        .success { color: #059669; }
        .error { color: #dc2626; }
        .warning { color: #d97706; }
        .info { color: #2563eb; }
        pre { background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto; font-size: 12px; }
        .manifest-link { background: #e0f2fe; padding: 8px; border-radius: 4px; margin: 8px 0; }
    </style>
    
    <?php if ($wp_loaded): ?>
        <?php
        // Output the actual wp_head content to test
        wp_head();
        ?>
    <?php else: ?>
        <!-- Fallback manifest link for testing -->
        <link rel="manifest" href="/wp-content/themes/businesscraft-ai/manifest.json">
    <?php endif; ?>
</head>
<body>
    <div class="container">
        <h1>ChatGABI PWA Diagnostic</h1>
        
        <div class="test-section">
            <h2>WordPress Loading Status</h2>
            <?php if ($wp_loaded): ?>
                <p class="success">✅ WordPress loaded successfully</p>
                <p class="info">Current theme: <?php echo wp_get_theme()->get('Name'); ?></p>
                <p class="info">Theme directory: <?php echo get_template_directory(); ?></p>
            <?php else: ?>
                <p class="error">❌ WordPress not loaded - running in standalone mode</p>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>PWA Functions Status</h2>
            <?php if ($wp_loaded): ?>
                <?php if (function_exists('chatgabi_add_pwa_meta_tags')): ?>
                    <p class="success">✅ chatgabi_add_pwa_meta_tags function exists</p>
                <?php else: ?>
                    <p class="error">❌ chatgabi_add_pwa_meta_tags function not found</p>
                <?php endif; ?>
                
                <?php if (function_exists('chatgabi_init_pwa_support')): ?>
                    <p class="success">✅ chatgabi_init_pwa_support function exists</p>
                <?php else: ?>
                    <p class="error">❌ chatgabi_init_pwa_support function not found</p>
                <?php endif; ?>
            <?php else: ?>
                <p class="warning">⚠️ Cannot check functions - WordPress not loaded</p>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>Manifest File Status</h2>
            <?php
            $manifest_path = __DIR__ . '/manifest.json';
            if (file_exists($manifest_path)):
            ?>
                <p class="success">✅ manifest.json file exists</p>
                <p class="info">File size: <?php echo filesize($manifest_path); ?> bytes</p>
                
                <?php
                $manifest_content = file_get_contents($manifest_path);
                $manifest_data = json_decode($manifest_content, true);
                if (json_last_error() === JSON_ERROR_NONE):
                ?>
                    <p class="success">✅ manifest.json contains valid JSON</p>
                    <p class="info">App name: <?php echo htmlspecialchars($manifest_data['name'] ?? 'N/A'); ?></p>
                    <p class="info">Short name: <?php echo htmlspecialchars($manifest_data['short_name'] ?? 'N/A'); ?></p>
                    <p class="info">Start URL: <?php echo htmlspecialchars($manifest_data['start_url'] ?? 'N/A'); ?></p>
                    <p class="info">Display mode: <?php echo htmlspecialchars($manifest_data['display'] ?? 'N/A'); ?></p>
                    <p class="info">Icons: <?php echo isset($manifest_data['icons']) ? count($manifest_data['icons']) : 0; ?> defined</p>
                <?php else: ?>
                    <p class="error">❌ manifest.json contains invalid JSON: <?php echo json_last_error_msg(); ?></p>
                <?php endif; ?>
            <?php else: ?>
                <p class="error">❌ manifest.json file not found at: <?php echo $manifest_path; ?></p>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>HTML Head Analysis</h2>
            <p>Checking if manifest link is present in the HTML head:</p>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const manifestLinks = document.querySelectorAll('link[rel="manifest"]');
                    const headAnalysis = document.getElementById('head-analysis');
                    
                    if (manifestLinks.length > 0) {
                        headAnalysis.innerHTML = '<p class="success">✅ Found ' + manifestLinks.length + ' manifest link(s) in HTML head</p>';
                        manifestLinks.forEach((link, index) => {
                            headAnalysis.innerHTML += '<div class="manifest-link"><strong>Manifest Link ' + (index + 1) + ':</strong><br>' +
                                'href: <code>' + link.href + '</code></div>';
                        });
                    } else {
                        headAnalysis.innerHTML = '<p class="error">❌ No manifest links found in HTML head</p>';
                    }
                    
                    // Check for other PWA-related meta tags
                    const themeColor = document.querySelector('meta[name="theme-color"]');
                    const appleCapable = document.querySelector('meta[name="apple-mobile-web-app-capable"]');
                    
                    if (themeColor) {
                        headAnalysis.innerHTML += '<p class="success">✅ Theme color meta tag found: ' + themeColor.content + '</p>';
                    }
                    
                    if (appleCapable) {
                        headAnalysis.innerHTML += '<p class="success">✅ Apple mobile web app capable meta tag found</p>';
                    }
                });
            </script>
            <div id="head-analysis">Analyzing...</div>
        </div>
        
        <div class="test-section">
            <h2>PWA Installation Test</h2>
            <p>Testing PWA installation capabilities:</p>
            
            <div id="pwa-test">
                <p class="info">🔍 Checking PWA support...</p>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const pwaTest = document.getElementById('pwa-test');
                    let testResults = [];
                    
                    // Test 1: Service Worker support
                    if ('serviceWorker' in navigator) {
                        testResults.push('<p class="success">✅ Service Worker supported</p>');
                    } else {
                        testResults.push('<p class="error">❌ Service Worker not supported</p>');
                    }
                    
                    // Test 2: Manifest support
                    if ('manifest' in window || 'mozManifest' in window) {
                        testResults.push('<p class="success">✅ Web App Manifest supported</p>');
                    } else {
                        testResults.push('<p class="warning">⚠️ Web App Manifest support unclear</p>');
                    }
                    
                    // Test 3: Install prompt
                    let installPromptAvailable = false;
                    window.addEventListener('beforeinstallprompt', function(e) {
                        installPromptAvailable = true;
                        testResults.push('<p class="success">✅ PWA install prompt available</p>');
                        pwaTest.innerHTML = testResults.join('');
                    });
                    
                    // Wait a moment for the install prompt event
                    setTimeout(function() {
                        if (!installPromptAvailable) {
                            testResults.push('<p class="warning">⚠️ PWA install prompt not triggered (may already be installed or not meet criteria)</p>');
                        }
                        
                        // Test 4: Check if already installed
                        if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                            testResults.push('<p class="success">✅ PWA is currently running in standalone mode</p>');
                        } else {
                            testResults.push('<p class="info">ℹ️ PWA is running in browser mode</p>');
                        }
                        
                        pwaTest.innerHTML = testResults.join('');
                    }, 1000);
                });
            </script>
        </div>
        
        <div class="test-section">
            <h2>Network Test</h2>
            <p>Testing manifest accessibility via network:</p>
            
            <div id="network-test">
                <p class="info">🌐 Testing network access...</p>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const networkTest = document.getElementById('network-test');
                    const manifestUrl = '/wp-content/themes/businesscraft-ai/manifest.json';
                    
                    fetch(manifestUrl)
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            } else {
                                throw new Error('HTTP ' + response.status);
                            }
                        })
                        .then(data => {
                            networkTest.innerHTML = 
                                '<p class="success">✅ Manifest accessible via network</p>' +
                                '<p class="info">Response status: 200 OK</p>' +
                                '<p class="info">Content-Type: application/json</p>' +
                                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                        })
                        .catch(error => {
                            networkTest.innerHTML = 
                                '<p class="error">❌ Manifest not accessible via network</p>' +
                                '<p class="error">Error: ' + error.message + '</p>';
                        });
                });
            </script>
        </div>
        
        <div class="test-section">
            <h2>Recommendations</h2>
            <div id="recommendations">
                <p class="info">📋 Generating recommendations based on test results...</p>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(function() {
                        const manifestLinks = document.querySelectorAll('link[rel="manifest"]');
                        const recommendations = document.getElementById('recommendations');
                        let recs = [];
                        
                        if (manifestLinks.length === 0) {
                            recs.push('<p class="error">🔧 <strong>Action Required:</strong> Manifest link is not being output in HTML head. Check if PWA support is properly initialized in WordPress.</p>');
                        } else {
                            recs.push('<p class="success">✅ Manifest link is properly included in HTML head.</p>');
                        }
                        
                        if ('serviceWorker' in navigator) {
                            recs.push('<p class="success">✅ Browser supports PWA features.</p>');
                        } else {
                            recs.push('<p class="warning">⚠️ Browser has limited PWA support.</p>');
                        }
                        
                        if (recs.length === 0) {
                            recs.push('<p class="success">✅ All PWA components appear to be working correctly!</p>');
                        }
                        
                        recommendations.innerHTML = recs.join('');
                    }, 2000);
                });
            </script>
        </div>
    </div>
</body>
</html>

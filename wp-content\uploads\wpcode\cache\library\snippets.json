{"categories": [{"name": "Most Popular", "slug": "most-popular"}, {"name": "Admin", "slug": "admin"}, {"name": "Archive", "slug": "archive"}, {"name": "Attachments", "slug": "attachments"}, {"name": "Comments", "slug": "comments"}, {"name": "Disable", "slug": "disable"}, {"name": "<PERSON><PERSON>", "slug": "login"}, {"name": "RSS Feeds", "slug": "rss-feeds"}, {"name": "Widgets", "slug": "widgets"}], "snippets": [{"library_id": 22997, "title": "\"New\" Badge for Recent Posts", "code": "", "note": "Display a badge to highlight posts that are newer than 7 days.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 63, "title": "Add an Edit Post Link to Archives", "code": "edit_post_link( __( '{Edit}' ) );\r\n", "note": "Make it easier to edit posts when viewing archives. Or on single pages. If you...", "categories": ["archive"], "code_type": "php", "needs_auth": ""}, {"library_id": 21342, "title": "Add Auto Sizes to Lazy Loaded images", "code": "", "note": "Use the \"auto\" size added in browsers for lazy loaded images.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 28377, "title": "Add basic Open Graph Tags", "code": "", "note": "Add OG tags for social media in your posts and pages.", "categories": ["widgets"], "code_type": "universal", "needs_auth": "1"}, {"library_id": 947, "title": "Add default ALT to avatar/Gravatar Images", "code": "", "note": "Add the user's name as a default alt tag to the Gravatar images loaded on...", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6562, "title": "Add Featured Image Column", "code": "", "note": "Display the featured image in the list of posts in the admin.", "categories": ["admin", "attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 44, "title": "Add Featured Images to RSS Feeds", "code": "/**\r\n * Add the post thumbnail, if available, before the content in feeds.\r\n *\r\n * @param string $content The post content.\r\n *\r\n * @return string\r\n */\r\nfunction wpcode_snippet_rss_post_thumbnail( $content ) {\r\n\tglobal $post;\r\n\tif ( has_post_thumbnail( $post->ID ) ) {\r\n\t\t$content = '<p>' . get_the_post_thumbnail( $post->ID ) . '</p>' . $content;\r\n\t}\r\n\r\n\treturn $content;\r\n}\r\n\r\nadd_filter( 'the_excerpt_rss', 'wpcode_snippet_rss_post_thumbnail' );\r\nadd_filter( 'the_content_feed', 'wpcode_snippet_rss_post_thumbnail' );", "note": "Extend your site's RSS feeds by including featured images in the feed.", "categories": ["rss-feeds"], "code_type": "php", "needs_auth": ""}, {"library_id": 16240, "title": "Add ID column in admin tables", "code": "", "note": "Display the post id in the admin in its own column for convenience.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 33964, "title": "Add Last Modified Column", "code": "", "note": "Display the post last modified date in the admin.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13021, "title": "Add Media File Size Column", "code": "", "note": "Display the file size in a separate column in the Media screen", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 18695, "title": "Add SEO-friendly Breadcrumbs", "code": "", "note": "Automatically add SEO-friendly Breadcrumbs before posts or use as a shortcode for more control.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 65, "title": "Add the Page Slug to Body Class", "code": "function wpcode_snippet_add_slug_body_class( $classes ) {\r\n\tglobal $post;\r\n\tif ( isset( $post ) ) {\r\n\t\t$classes[] = $post->post_type . '-' . $post->post_name;\r\n\t}\r\n\r\n\treturn $classes;\r\n}\r\n\r\nadd_filter( 'body_class', 'wpcode_snippet_add_slug_body_class' );", "note": "Add the page slug to the body class for better styling.", "categories": ["archive"], "code_type": "php", "needs_auth": ""}, {"library_id": 18693, "title": "Allow Contributors to Upload Images", "code": "", "note": "Allow users with the Contributor role to upload files in the admin.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 25491, "title": "Allow Only Logged-In Users", "code": "", "note": "Make your site private just for logged-in users.", "categories": ["login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 54, "title": "Allow SVG Files Upload", "code": "/**\r\n * Allow SVG uploads for administrator users.\r\n *\r\n * @param array $upload_mimes Allowed mime types.\r\n *\r\n * @return mixed\r\n */\r\nadd_filter(\r\n\t'upload_mimes',\r\n\tfunction ( $upload_mimes ) {\r\n\t\t// By default, only administrator users are allowed to add SVGs.\r\n\t\t// To enable more user types edit or comment the lines below but beware of\r\n\t\t// the security risks if you allow any user to upload SVG files.\r\n\t\tif ( ! current_user_can( 'administrator' ) ) {\r\n\t\t\treturn $upload_mimes;\r\n\t\t}\r\n\r\n\t\t$upload_mimes['svg']  = 'image/svg+xml';\r\n\t\t$upload_mimes['svgz'] = 'image/svg+xml';\r\n\r\n\t\treturn $upload_mimes;\r\n\t}\r\n);\r\n\r\n/**\r\n * Add SVG files mime check.\r\n *\r\n * @param array        $wp_check_filetype_and_ext Values for the extension, mime type, and corrected filename.\r\n * @param string       $file Full path to the file.\r\n * @param string       $filename The name of the file (may differ from $file due to $file being in a tmp directory).\r\n * @param string[]     $mimes Array of mime types keyed by their file extension regex.\r\n * @param string|false $real_mime The actual mime type or false if the type cannot be determined.\r\n */\r\nadd_filter(\r\n\t'wp_check_filetype_and_ext',\r\n\tfunction ( $wp_check_filetype_and_ext, $file, $filename, $mimes, $real_mime ) {\r\n\r\n\t\tif ( ! $wp_check_filetype_and_ext['type'] ) {\r\n\r\n\t\t\t$check_filetype  = wp_check_filetype( $filename, $mimes );\r\n\t\t\t$ext             = $check_filetype['ext'];\r\n\t\t\t$type            = $check_filetype['type'];\r\n\t\t\t$proper_filename = $filename;\r\n\r\n\t\t\tif ( $type && 0 === strpos( $type, 'image/' ) && 'svg' !== $ext ) {\r\n\t\t\t\t$ext  = false;\r\n\t\t\t\t$type = false;\r\n\t\t\t}\r\n\r\n\t\t\t$wp_check_filetype_and_ext = compact( 'ext', 'type', 'proper_filename' );\r\n\t\t}\r\n\r\n\t\treturn $wp_check_filetype_and_ext;\r\n\r\n\t},\r\n\t10,\r\n\t5\r\n);\r\n", "note": "Add support for SVG files to be uploaded in WordPress media.", "categories": ["most-popular", "attachments"], "code_type": "php", "needs_auth": ""}, {"library_id": 33959, "title": "Author <PERSON><PERSON> Post", "code": "", "note": "Display the author bio (if any) after the post content.", "categories": ["widgets"], "code_type": "php", "needs_auth": "1"}, {"library_id": 27164, "title": "Auto-Collapse Long Comments", "code": "", "note": "Add a \"Read More\" button for long comments for better layouts.", "categories": ["comments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 28375, "title": "Auto-logout inactive users", "code": "", "note": "Automatically logout users after 10 minutes.", "categories": ["login"], "code_type": "universal", "needs_auth": "1"}, {"library_id": 56, "title": "Automatically Link Featured Images to Posts", "code": "/**\r\n * Wrap the thumbnail in a link to the post.\r\n * Only use this if your theme doesn't already wrap thumbnails in a link.\r\n *\r\n * @param string $html The thumbnail HTML to wrap in an anchor.\r\n * @param int    $post_id The post ID.\r\n * @param int    $post_image_id The image id.\r\n *\r\n * @return string\r\n */\r\nfunction wpcode_snippet_autolink_featured_images( $html, $post_id, $post_image_id ) {\r\n\t$html = '<a href=\"' . get_permalink( $post_id ) . '\" title=\"' . esc_attr( get_the_title( $post_id ) ) . '\">' . $html . '</a>';\r\n\r\n\treturn $html;\r\n}\r\n\r\nadd_filter( 'post_thumbnail_html', 'wpcode_snippet_autolink_featured_images', 20, 3 );\r\n", "note": "Wrap featured images in your theme in links to posts.", "categories": ["attachments"], "code_type": "php", "needs_auth": ""}, {"library_id": 21351, "title": "Back to Top Button", "code": "", "note": "Add a simple button to scroll back to top on the frontend.", "categories": ["archive"], "code_type": "html", "needs_auth": "1"}, {"library_id": 13588, "title": "Block WP-Admin Area from Non-Administrators", "code": "", "note": "Prevent users who are not administrators from accessing the wp-admin area.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 64, "title": "Change \"Howdy Admin\" in Admin Bar", "code": "function wpcode_snippet_replace_howdy( $wp_admin_bar ) {\r\n\r\n\t// Edit the line below to set what you want the admin bar to display intead of \"Howdy,\".\r\n\t$new_howdy = 'Welcome,';\r\n\r\n\t$my_account = $wp_admin_bar->get_node( 'my-account' );\r\n\tif ( ! isset( $my_account->title ) ) {\r\n\t\treturn;\r\n\t}\r\n\t$wp_admin_bar->add_node(\r\n\t\tarray(\r\n\t\t\t'id'    => 'my-account',\r\n\t\t\t'title' => str_replace( 'Howdy,', $new_howdy, $my_account->title ),\r\n\t\t)\r\n\t);\r\n}\r\n\r\nadd_filter( 'admin_bar_menu', 'wpcode_snippet_replace_howdy', 9999 );\r\n", "note": "Customize the \"Howdy\" message in the admin bar.", "categories": ["admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 43, "title": "Change Admin Panel Footer Text", "code": "add_filter(\r\n\t'admin_footer_text',\r\n\tfunction ( $footer_text ) {\r\n\t\t// Edit the line below to customize the footer text.\r\n\t\t$footer_text = 'Powered by <a href=\"https://www.wordpress.org\" target=\"_blank\" rel=\"noopener\">WordPress</a> | WordPress Tutorials: <a href=\"https://www.wpbeginner.com\" target=\"_blank\" rel=\"noopener\">WPBeginner</a>';\r\n\t\t\r\n\t\treturn $footer_text;\r\n\t}\r\n);", "note": "Display custom text in the admin panel footer with this snippet.", "categories": ["admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 32246, "title": "Change Editor Default Image Size", "code": "", "note": "Set the default image size to full instead of large in the block editor.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 50, "title": "Change Excerpt Length", "code": "add_filter(\r\n\t'excerpt_length',\r\n\tfunction ( $length ) {\r\n\t\t// Number of words to display in the excerpt.\r\n\t\treturn 40;\r\n\t},\r\n\t500\r\n);", "note": "Update the length of the Excerpts on your website using this snippet.", "categories": ["archive"], "code_type": "php", "needs_auth": ""}, {"library_id": 964, "title": "Change Outgoing Email Sender", "code": "", "note": "Change the outgoing sender name and email address. Don't forget to edit the snippet with...", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 49, "title": "Change Read More Text for Excerpts", "code": "function wpcode_snippets_change_read_more( $read_more, $read_more_text ) {\r\n\r\n\t// Edit the line below to add your own \"Read More\" text.\r\n\t$custom_text = 'Read the whole post';\r\n\r\n\t$read_more = str_replace( $read_more_text, $custom_text, $read_more );\r\n\r\n\treturn $read_more;\r\n}\r\n\r\nadd_filter( 'the_content_more_link', 'wpcode_snippets_change_read_more', 15, 2 );\r\n", "note": "Customize the \"Read More\" text that shows up after excerpts.", "categories": ["archive"], "code_type": "php", "needs_auth": ""}, {"library_id": 12, "title": "Completely Disable Comments", "code": "add_action('admin_init', function () {\r\n    // Redirect any user trying to access comments page\r\n    global $pagenow;\r\n    \r\n    if ($pagenow === 'edit-comments.php') {\r\n        wp_safe_redirect(admin_url());\r\n        exit;\r\n    }\r\n\r\n    // Remove comments metabox from dashboard\r\n    remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');\r\n\r\n    // Disable support for comments and trackbacks in post types\r\n    foreach (get_post_types() as $post_type) {\r\n        if (post_type_supports($post_type, 'comments')) {\r\n            remove_post_type_support($post_type, 'comments');\r\n            remove_post_type_support($post_type, 'trackbacks');\r\n        }\r\n    }\r\n});\r\n\r\n// Close comments on the front-end\r\nadd_filter('comments_open', '__return_false', 20, 2);\r\nadd_filter('pings_open', '__return_false', 20, 2);\r\n\r\n// Hide existing comments\r\nadd_filter('comments_array', '__return_empty_array', 10, 2);\r\n\r\n// Remove comments page in menu\r\nadd_action('admin_menu', function () {\r\n    remove_menu_page('edit-comments.php');\r\n});\r\n\r\n// Remove comments links from admin bar\r\nadd_action('admin_bar_menu', function () {\r\n    remove_action('admin_bar_menu', 'wp_admin_bar_comments_menu', 60);\r\n}, 0);", "note": "Disable comments for all post types, in the admin and the frontend.", "categories": ["most-popular", "comments"], "code_type": "php", "needs_auth": ""}, {"library_id": 33967, "title": "<PERSON><PERSON> for Code Blocks", "code": "", "note": "Add a button to easily copy to clipboard for code blocks.", "categories": ["widgets"], "code_type": "html", "needs_auth": "1"}, {"library_id": 28373, "title": "Custom Default Avatar", "code": "", "note": "Use a custom image for the default avatar.", "categories": ["comments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 29706, "title": "Custom Logo Per Page", "code": "", "note": "Add a way to choose a custom logo for each page.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6572, "title": "Decrease Auto-Save Interval", "code": "", "note": "Change post editor auto-save to 5 minutes instead of 1.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 29714, "title": "De<PERSON>ult Featured Image", "code": "", "note": "Add a setting in Media for a default featured image for all posts.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 48, "title": "Delay Posts in RSS Feeds", "code": "function wpcode_snippet_publish_later_on_feed( $where ) {\r\n\r\n\tglobal $wpdb;\r\n\r\n\tif ( is_feed() ) {\r\n\t\t// Timestamp in WP-format.\r\n\t\t$now = gmdate( 'Y-m-d H:i:s' );\r\n\r\n\t\t// Number of unit to wait\r\n\t\t$wait = '10'; // integer.\r\n\r\n\t\t// Choose time unit.\r\n\t\t$unit = 'MINUTE'; // MINUTE, HOUR, DAY, WEEK, MONTH, YEAR.\r\n\r\n\t\t// Add SQL-sytax to default $where. By default 10 minutes.\r\n\t\t$where .= \" AND TIMESTAMPDIFF($unit, $wpdb->posts.post_date_gmt, '$now') > $wait \";\r\n\t}\r\n\r\n\treturn $where;\r\n}\r\n\r\nadd_filter( 'posts_where', 'wpcode_snippet_publish_later_on_feed' );", "note": "Add a delay before published posts show up in the RSS feeds.", "categories": ["rss-feeds"], "code_type": "php", "needs_auth": ""}, {"library_id": 13025, "title": "Disable Admin Password Reset Emails", "code": "", "note": "Don't send an email to the administrator of the site after a user resets their...", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 25497, "title": "Disable All Updates", "code": "", "note": "Hide all updates for WordPress Core, Plugins and Themes.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 19984, "title": "Disable Application Passwords", "code": "", "note": "Disable Application Passwords feature that was added in WP 5.6.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 40, "title": "Disable Attachment Pages", "code": "add_action(\r\n\t'template_redirect',\r\n\tfunction () {\r\n\t\tglobal $post;\r\n\t\tif ( ! is_attachment() || ! isset( $post->post_parent ) || ! is_numeric( $post->post_parent ) ) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Does the attachment have a parent post?\r\n\t\t// If the post is trashed, fallback to redirect to homepage.\r\n\t\tif ( 0 !== $post->post_parent && 'trash' !== get_post_status( $post->post_parent ) ) {\r\n\t\t\t// Redirect to the attachment parent.\r\n\t\t\twp_safe_redirect( get_permalink( $post->post_parent ), 301 );\r\n\t\t} else {\r\n\t\t\t// For attachment without a parent redirect to homepage.\r\n\t\t\twp_safe_redirect( get_bloginfo( 'wpurl' ), 302 );\r\n\t\t}\r\n\t\texit;\r\n\t},\r\n\t1\r\n);\r\n", "note": "Hide the Attachment/Attachments pages on the frontend from all visitors.", "categories": ["most-popular", "attachments"], "code_type": "php", "needs_auth": ""}, {"library_id": 18702, "title": "Disable Author Archives", "code": "", "note": "Completely hide author archives and prevent direct access by returning a 404 page.", "categories": ["archive", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 24305, "title": "Disable Auto-Scaling Oversized Images", "code": "", "note": "Prevent WordPress from scaling large images added in v5.3.", "categories": ["attachments", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13592, "title": "Disable Automatic Trash Emptying", "code": "", "note": "Prevent WordPress from automatically deleting trashed posts after 30 days.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 38, "title": "Disable Automatic Updates", "code": "// Disable core auto-updates\r\nadd_filter( 'auto_update_core', '__return_false' );\r\n// Disable auto-updates for plugins.\r\nadd_filter( 'auto_update_plugin', '__return_false' );\r\n// Disable auto-updates for themes.\r\nadd_filter( 'auto_update_theme', '__return_false' );", "note": "Use this snippet to completely disable automatic updates on your website.", "categories": ["most-popular", "disable"], "code_type": "php", "needs_auth": ""}, {"library_id": 39, "title": "Disable Automatic Updates Emails", "code": "// Disable auto-update emails.\r\nadd_filter( 'auto_core_update_send_email', '__return_false' );\r\n\r\n// Disable auto-update emails for plugins.\r\nadd_filter( 'auto_plugin_update_send_email', '__return_false' );\r\n\r\n// Disable auto-update emails for themes.\r\nadd_filter( 'auto_theme_update_send_email', '__return_false' );", "note": "Stop getting emails about automatic updates on your WordPress site.", "categories": ["most-popular", "disable"], "code_type": "php", "needs_auth": ""}, {"library_id": 15148, "title": "Disable Block Directory", "code": "", "note": "Prevent block directory results from being shown when searching for blocks in the editor.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 24309, "title": "Disable Categories and Tags", "code": "", "note": "Disable the default taxonomies for Posts (Categories, Tags).", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 967, "title": "Disable Comment Form Website URL", "code": "", "note": "Remove the Website URL field from the Comments form.", "categories": ["comments", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 19986, "title": "Disable Comment URL Linking", "code": "", "note": "Prevent automatically formatting URLs in comments as links.", "categories": ["comments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 899, "title": "Disable Embeds", "code": "", "note": "Remove an extra request and prevent others from adding embeds in your site.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 897, "title": "Disable Emojis", "code": "", "note": "Disable Emoji's in WordPress to improve your site's performance", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 15146, "title": "Disable Full Site Editing (FSE)", "code": "", "note": "Prevent any user from making changes to the site using the Full Site Editor.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13023, "title": "Disable Fullscreen Editor", "code": "", "note": "Automatically disable the Gutenberg Fullscreen editor for all users.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 24313, "title": "Disable Gravatar Avatars", "code": "", "note": "Prevent WordPress from loading gravatar avatars for users.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 1193, "title": "Disable Gutenberg Code Editing for Non-Admin Users", "code": "", "note": "Prevent non-admin users from using \"Edit as HTML\" or \"Code editor\" in the Gutenberg Editor.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 14, "title": "Disable G<PERSON>nberg Editor (use Classic Editor)", "code": "add_filter('gutenberg_can_edit_post', '__return_false', 5);\r\nadd_filter('use_block_editor_for_post', '__return_false', 5);", "note": "Switch back to the Classic Editor by disablling the Block Editor.", "categories": ["most-popular", "admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 32244, "title": "Disable Inspector <PERSON><PERSON>", "code": "", "note": "Revert having a separate tab for block styles.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 17253, "title": "Disable j<PERSON><PERSON><PERSON>", "code": "", "note": "Prevent loading the jQuery Mi<PERSON>te script in the frontend.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6004, "title": "Disable Lazy Load", "code": "", "note": "Prevent WordPress from adding the lazy-load attribute to all images and iframes.", "categories": ["attachments", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 25489, "title": "Disable Legacy CSS", "code": "", "note": "Stop loading styles for recent comments and classic gallery shortcode.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13027, "title": "Disable Login Autofocus", "code": "", "note": "Prevent autofocus on the username field on the login page.", "categories": ["disable", "login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 46, "title": "Disable Login by <PERSON><PERSON>", "code": "remove_filter( 'authenticate', 'wp_authenticate_email_password', 20 );", "note": "Force your users to login only using their username.", "categories": ["login"], "code_type": "php", "needs_auth": ""}, {"library_id": 60, "title": "Disable Login Screen Language Switcher", "code": "add_filter( 'login_display_language_dropdown', '__return_false' );", "note": "Hide the Language Switcher on the default WordPress login screen.", "categories": ["login"], "code_type": "php", "needs_auth": ""}, {"library_id": 962, "title": "Disable New User Notifications", "code": "", "note": "Prevent the admin user from getting new user notification emails.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 32242, "title": "Disable Openverse", "code": "", "note": "Disable the Openverse integration for the media inserter.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 18699, "title": "Disable Pattern Directory", "code": "", "note": "Prevent patterns from being shown in the Gutenberg editor block inserter.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6011, "title": "Disable Plugin & Theme Editor", "code": "", "note": "Prevent users from using the Plugin & Theme file editor.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 24311, "title": "Disable Post Formats", "code": "", "note": "Disable the Post Formats feature for all posts.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 15144, "title": "Disable Posts in Admin", "code": "", "note": "Hide the Posts menu from the admin if you don't need the default Posts post...", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6568, "title": "Disable REST API Links", "code": "", "note": "Remove all head links to the REST API endpoints from the page source.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6564, "title": "Disable RSS Feed Links", "code": "", "note": "Remove head links to RSS Feeds from all the pages on your site.", "categories": ["rss-feeds"], "code_type": "php", "needs_auth": "1"}, {"library_id": 41, "title": "Disable RSS Feeds", "code": "/**\r\n * Display a custom message instead of the RSS Feeds.\r\n *\r\n * @return void\r\n */\r\nfunction wpcode_snippet_disable_feed() {\r\n\twp_die(\r\n\t\tsprintf(\r\n\t\t\t// Translators: Placeholders for the homepage link.\r\n\t\t\tesc_html__( 'No feed available, please visit our %1$shomepage%2$s!' ),\r\n\t\t\t' <a href=\"' . esc_url( home_url( '/' ) ) . '\">',\r\n\t\t\t'</a>'\r\n\t\t)\r\n\t);\r\n}\r\n\r\n// Replace all feeds with the message above.\r\nadd_action( 'do_feed_rdf', 'wpcode_snippet_disable_feed', 1 );\r\nadd_action( 'do_feed_rss', 'wpcode_snippet_disable_feed', 1 );\r\nadd_action( 'do_feed_rss2', 'wpcode_snippet_disable_feed', 1 );\r\nadd_action( 'do_feed_atom', 'wpcode_snippet_disable_feed', 1 );\r\nadd_action( 'do_feed_rss2_comments', 'wpcode_snippet_disable_feed', 1 );\r\nadd_action( 'do_feed_atom_comments', 'wpcode_snippet_disable_feed', 1 );\r\n// Remove links to feed from the header.\r\nremove_action( 'wp_head', 'feed_links_extra', 3 );\r\nremove_action( 'wp_head', 'feed_links', 2 );\r\n", "note": "Turn off the WordPress RSS Feeds for your website with 1 click.", "categories": ["rss-feeds"], "code_type": "php", "needs_auth": ""}, {"library_id": 47, "title": "Disable Search", "code": "// Prevent search queries.\r\nadd_action(\r\n\t'parse_query',\r\n\tfunction ( $query, $error = true ) {\r\n\t\tif ( is_search() && ! is_admin() ) {\r\n\t\t\t$query->is_search       = false;\r\n\t\t\t$query->query_vars['s'] = false;\r\n\t\t\t$query->query['s']      = false;\r\n\t\t\tif ( true === $error ) {\r\n\t\t\t\t$query->is_404 = true;\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t15,\r\n\t2\r\n);\r\n\r\n// Remove the Search Widget.\r\nadd_action(\r\n\t'widgets_init',\r\n\tfunction () {\r\n\t\tunregister_widget( 'WP_Widget_Search' );\r\n\t}\r\n);\r\n\r\n// Remove the search form.\r\nadd_filter( 'get_search_form', '__return_empty_string', 999 );\r\n\r\n// Remove the core search block.\r\nadd_action(\r\n\t'init',\r\n\tfunction () {\r\n\t\tif ( ! function_exists( 'unregister_block_type' ) || ! class_exists( 'WP_Block_Type_Registry' ) ) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t$block = 'core/search';\r\n\t\tif ( WP_Block_Type_Registry::get_instance()->is_registered( $block ) ) {\r\n\t\t\tunregister_block_type( $block );\r\n\t\t}\r\n\t}\r\n);\r\n\r\n// Remove admin bar menu search box.\r\nadd_action(\r\n\t'admin_bar_menu',\r\n\tfunction ( $wp_admin_bar ) {\r\n\t\t$wp_admin_bar->remove_menu( 'search' );\r\n\t},\r\n\t11\r\n);\r\n", "note": "Completely disable search on your WordPress website.", "categories": ["disable"], "code_type": "php", "needs_auth": ""}, {"library_id": 955, "title": "Disable Self Pingbacks", "code": "", "note": "Prevent WordPress from automatically creating pingbacks from your own site.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13595, "title": "Disable Site Admin Email Verification", "code": "", "note": "Stop WordPress from asking you to confirm the administrator email address when logging in.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6006, "title": "Disable Site Health", "code": "", "note": "Completely hide the Site Health menu item and dashboard widget.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 18697, "title": "Disable Specific Blocks", "code": "", "note": "Prevent certain blocks from being used in the Gutenberg Editor.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 32240, "title": "Disable Template Editor", "code": "", "note": "Disable access to modify or create templates from the Post Editor.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 15142, "title": "Disable the Excerpt in RSS Feeds", "code": "", "note": "Hide the excerpt or content from your RSS feeds.", "categories": ["rss-feeds"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6566, "title": "Disable the WordPress Shortlink", "code": "", "note": "Remove link rel shortlink from your site head area.", "categories": ["archive", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 42, "title": "Disable The WP Admin Bar", "code": "/* Disable WordPress Admin Bar for all users */\r\nadd_filter( 'show_admin_bar', '__return_false' );", "note": "Hide the WordPress Admin Bar for all users in the frontend.", "categories": ["most-popular", "admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 21345, "title": "Disable Thumbnail Image Sizes", "code": "", "note": "Prevent WordPress from generating specific thumbnail sizes.", "categories": ["attachments", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13029, "title": "Disable Update Notice for Non-admin Users", "code": "", "note": "Don't show WordPress version update notices to users that can't update the site.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 35, "title": "Disable Widget Blocks (use Classic Widgets)", "code": "add_filter( 'use_widgets_block_editor', '__return_false' );", "note": "Use the classic interface instead of Blocks to manage Widgets.", "categories": ["most-popular", "widgets"], "code_type": "php", "needs_auth": ""}, {"library_id": 953, "title": "Disable wlwmanifest link", "code": "", "note": "Prevent WordPress from adding the Windows Live Writer manifest link to your pages.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 21338, "title": "Disable WordPress 6.5 Font Library", "code": "", "note": "Deactivate the font library feature added in WordPress 6.5.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 37, "title": "Disable WordPress REST API", "code": "add_filter(\r\n\t'rest_authentication_errors',\r\n\tfunction ( $access ) {\r\n\t\treturn new WP_Error(\r\n\t\t\t'rest_disabled',\r\n\t\t\t__( 'The WordPress REST API has been disabled.' ),\r\n\t\t\tarray(\r\n\t\t\t\t'status' => rest_authorization_required_code(),\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n);", "note": "Easily disable the WP REST API on your website with this snippet.", "categories": ["most-popular", "disable"], "code_type": "php", "needs_auth": ""}, {"library_id": 6009, "title": "Disable WordPress Sitemaps", "code": "", "note": "Disable the auto-generated WordPress Sitemaps added in version 5.5.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 55, "title": "Disable XML-RPC", "code": "add_filter( 'xmlrpc_enabled', '__return_false' );\r\n", "note": "On sites running WordPress 3.5+, disable XML-RPC completely.", "categories": ["most-popular", "disable"], "code_type": "php", "needs_auth": ""}, {"library_id": 28370, "title": "Display Random Posts", "code": "", "note": "Show random posts anywhere on your site.", "categories": ["widgets"], "code_type": "php", "needs_auth": "1"}, {"library_id": 16242, "title": "Display Reading Time", "code": "", "note": "Add the estimated reading time before the post content.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 27160, "title": "Display Related Posts by Category", "code": "", "note": "List 5 posts from the same categories as the current post.", "categories": ["archive", "widgets"], "code_type": "php", "needs_auth": "1"}, {"library_id": 62, "title": "Display the Last Updated Date", "code": "$u_time          = get_the_time( 'U' );\r\n$u_modified_time = get_the_modified_time( 'U' );\r\n// Only display modified date if 24hrs have passed since the post was published.\r\nif ( $u_modified_time >= $u_time + 86400 ) {\r\n\r\n\t$updated_date = get_the_modified_time( 'F jS, Y' );\r\n\t$updated_time = get_the_modified_time( 'h:i a' );\r\n\r\n\t$updated = '<p class=\"last-updated\">';\r\n\r\n\t$updated .= sprintf(\r\n\t// Translators: Placeholders get replaced with the date and time when the post was modified.\r\n\t\tesc_html__( 'Last updated on %1$s at %2$s' ),\r\n\t\t$updated_date,\r\n\t\t$updated_time\r\n\t);\r\n\t$updated .= '</p>';\r\n\r\n\techo wp_kses_post( $updated );\r\n}\r\n", "note": "Add the last updated date & time to your posts in the frontend.", "categories": ["archive"], "code_type": "php", "needs_auth": ""}, {"library_id": 1196, "title": "Duplicate Post/Page Link", "code": "", "note": "Duplicate posts, pages or any post type with 1-click by adding a duplicate link in...", "categories": ["most-popular", "admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 29710, "title": "Dynamic Greeting", "code": "", "note": "Use as Shortcode to display a greeting based on the time of day.", "categories": ["widgets"], "code_type": "html", "needs_auth": "1"}, {"library_id": 6002, "title": "Dynamic Year Copyright Shortcode", "code": "", "note": "This snippet adds an easy-to-use shortcode to display the copyright symbol with the current year.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6013, "title": "Empty Admin Dashboard", "code": "", "note": "Disable all dashboard widgets in the admin and the welcome panel.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 53, "title": "Enable Shortcode Execution in Text Widgets", "code": "add_filter( 'widget_text', 'do_shortcode' );", "note": "Extend the default Text Widget with shortcode execution.", "categories": ["widgets"], "code_type": "php", "needs_auth": ""}, {"library_id": 4152, "title": "Enable Shortcode Execution in WPCode HTML Snippets", "code": "", "note": "Use this snippet to enable shortcode execution in WPCode HTML snippets.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 52, "title": "Exclude Specific Categories from RSS Feed", "code": "/**\r\n * Exclude a category or multiple categories from the feeds.\r\n * If you want to exclude multiple categories, use a comma-separated list: \"-15, -5, -6\".\r\n * Make sure to prefix the category id(s) with a minus \"-\".\r\n *\r\n * @param WP_Query $query The query.\r\n */\r\nfunction wpcode_snippets_exclude_feed_category( $query ) {\r\n\tif ( $query->is_feed ) {\r\n\t\t// Replace 15 with the desired category id you want to exclude.\r\n\t\t$query->set( 'cat', '-15' );\r\n\t}\r\n}\r\n\r\nadd_action( 'pre_get_posts', 'wpcode_snippets_exclude_feed_category' );\r\n", "note": "Prevent posts in certain categories from showing up in your RSS Feeds.", "categories": ["rss-feeds"], "code_type": "php", "needs_auth": ""}, {"library_id": 15139, "title": "Extend Login Expiration Time", "code": "", "note": "Toggling \"Remember Me\" will keep you logged-in for 30 days instead of 14 days.", "categories": ["login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 27162, "title": "Fading Page Transitions", "code": "", "note": "Fade out the page when clicking on any internal links.", "categories": ["archive"], "code_type": "js", "needs_auth": "1"}, {"library_id": 22993, "title": "FAQ Accordion", "code": "", "note": "Use this snippet as a shortcode to display frequently asked questions.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 22999, "title": "Floating Social Media Icons", "code": "", "note": "Display your Social Media accounts in a floating sidebar.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 32238, "title": "Heartbeat Setting", "code": "", "note": "Add a setting that allows you to configure the heartbeat interval.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 19, "title": "Hide ‘Screen Options’ Tab", "code": "// Hide admin 'Screen Options' tab\r\nadd_filter('screen_options_show_screen', '__return_false');", "note": "Remove the Screen Options menu at the top of admin pages.", "categories": ["admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 25493, "title": "Hide \"Remember Me\"", "code": "", "note": "Hide the \"Remember Me\" checkbox on the login page.", "categories": ["disable", "login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 45, "title": "<PERSON><PERSON> Login Errors in WordPress", "code": "add_filter(\r\n\t'login_errors',\r\n\tfunction ( $error ) {\r\n\t\t// Edit the line below to customize the message.\r\n\t\treturn 'Something is wrong!';\r\n\t}\r\n);\r\n", "note": "Improve safety by hiding the specific login error information.", "categories": ["login"], "code_type": "php", "needs_auth": ""}, {"library_id": 29712, "title": "Inline Spoiler Shortcode", "code": "", "note": "Easily hide spoilers in your text with a shortcode.", "categories": ["widgets"], "code_type": "php", "needs_auth": "1"}, {"library_id": 25495, "title": "Last Login Column", "code": "", "note": "Add a column to the users table with the last time they logged in.", "categories": ["admin", "login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 19978, "title": "Limit Comments & Display Character Count", "code": "", "note": "Limit comment length and display a count of characters.", "categories": ["comments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 6570, "title": "Limit the Number of Post Revisions", "code": "", "note": "Reduce Database size by limiting post revisions to just 20 per post.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 17251, "title": "Limit Uploaded Image Size", "code": "", "note": "Set a max width and height for your image uploads to save space.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 957, "title": "Lowercase Filenames for Uploads", "code": "", "note": "Make all the filenames of new uploads to lowercase after you enable this snippet.", "categories": ["attachments"], "code_type": "php", "needs_auth": "1"}, {"library_id": 33961, "title": "Magnifier Glass For Images", "code": "", "note": "Add a zoom effect to images on your website.", "categories": ["attachments"], "code_type": "html", "needs_auth": "1"}, {"library_id": 17247, "title": "Maintenance Mode", "code": "", "note": "Display a simple maintenance mode message for non admin users.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13583, "title": "Move Admin Bar to the Bottom", "code": "", "note": "Move the admin bar to the bottom of the screen in the frontend.", "categories": ["admin"], "code_type": "css", "needs_auth": "1"}, {"library_id": 17249, "title": "Open External Links in a New Tab", "code": "", "note": "Automatically open all external links added in posts in a new tab.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 19982, "title": "Override Block Editor Palette Colors", "code": "", "note": "Use this snippet to control the pre-defined colors in the Block Editor.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 16235, "title": "Post Meta Debugger", "code": "", "note": "Add a metabox that lists all the custom fields/meta values for a post/page for easy...", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 33957, "title": "Prevent Loading Translations", "code": "", "note": "If your site is using en_US reduce the memory used for translation calls.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 28380, "title": "Redirect 404 to Homepage", "code": "", "note": "Automatically redirect 404 pages to the homepage.", "categories": ["archive", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 21349, "title": "Remove \"WordPress\" from admin titles", "code": "", "note": "Stop adding \"WordPress\" to the admin pages browser title.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 16238, "title": "Remove Color Scheme Picker", "code": "", "note": "Disable the option to change the admin color scheme in the profile page.", "categories": ["admin", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 51, "title": "Remove Dashboard Welcome Panel", "code": "add_action(\r\n\t'admin_init',\r\n\tfunction () {\r\n\t\tremove_action( 'welcome_panel', 'wp_welcome_panel' );\r\n\t}\r\n);\r\n", "note": "Hide the Welcome Panel on the WordPress dashboard for all users.", "categories": ["admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 969, "title": "Remove Login <PERSON>", "code": "", "note": "Prevent the Login box from shaking when entering the wrong password or username.", "categories": ["disable", "login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 950, "title": "Remove Query Strings From Static Files", "code": "", "note": "Use this snippet to remove query string from CSS & JS files and improve performance...", "categories": ["attachments", "disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 13031, "title": "Remove the WordPress Logo From the Admin Bar", "code": "", "note": "Hide the WordPress logo in the admin bar for all users.", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 36, "title": "Remove WordPress Version Number", "code": "add_filter('the_generator', '__return_empty_string');\r\n", "note": "Hide the WordPress version number from your site's frontend and feeds.", "categories": ["most-popular", "disable"], "code_type": "php", "needs_auth": ""}, {"library_id": 13590, "title": "Remove WP Block Library CSS", "code": "", "note": "Stop WordPress from loading the block editor CSS - especially useful if you don't use...", "categories": ["disable"], "code_type": "php", "needs_auth": "1"}, {"library_id": 19980, "title": "Reorder Admin <PERSON>", "code": "", "note": "Control the order of the admin menu items with a simple snippet.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 959, "title": "Replace WordPress Logo on Login Page", "code": "", "note": "Use your custom logo on the default login page, don't forget to edit the snippet...", "categories": ["login"], "code_type": "php", "needs_auth": "1"}, {"library_id": 24307, "title": "Restore the Customize Submenu", "code": "", "note": "Add back the Customize menu under Appearance if your theme removes it.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 16244, "title": "Scroll Progress Bar", "code": "", "note": "Display a progress bar at the top of the site as the user scrolls.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 59, "title": "Set a Minimum Word Count for Posts", "code": "/**\r\n * Prevent publishing posts under a minimum number of words.\r\n *\r\n * @param int     $post_id The id of the post.\r\n * @param WP_Post $post The post object.\r\n *\r\n * @return void\r\n */\r\nfunction wpcode_snippet_publish_min_words( $post_id, $post ) {\r\n\t// Edit the line below to set the desired minimum number of words.\r\n\t$word_count = 100;\r\n\r\n\tif ( str_word_count( $post->post_content ) < $word_count ) {\r\n\t\twp_die(\r\n\t\t\tsprintf(\r\n\t\t\t\t// Translators: placeholder adds the minimum number of words.\r\n\t\t\t\tesc_html__( 'The post content is below the minimum word count. Your post needs to have at least %d words to be published.' ),\r\n\t\t\t\tabsint( $word_count )\r\n\t\t\t)\r\n\t\t);\r\n\t}\r\n}\r\n\r\nadd_action( 'publish_post', 'wpcode_snippet_publish_min_words', 9, 2 );", "note": "Force your authors to write posts that have a minimum length.", "categories": ["admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 61, "title": "Set oEmbed <PERSON> Width", "code": "function wpcode_snippet_oembed_defaults( $sizes ) {\r\n\treturn array(\r\n\t\t'width'  => 400,\r\n\t\t'height' => 280,\r\n\t);\r\n}\r\n\r\nadd_filter( 'embed_defaults', 'wpcode_snippet_oembed_defaults' );\r\n", "note": "Set a max width for the embeds using oEmbed in the content.", "categories": ["admin"], "code_type": "php", "needs_auth": ""}, {"library_id": 27166, "title": "Show Active Plugins First", "code": "", "note": "List active plugins first in the wp-admin list of plugins.", "categories": ["admin"], "code_type": "php", "needs_auth": "1"}, {"library_id": 22995, "title": "Simple Countdown Timer", "code": "", "note": "Display a simple countdown timer using this snippet anywhere on your site.", "categories": ["widgets"], "code_type": "html", "needs_auth": "1"}, {"library_id": 17255, "title": "Simple Table Of Contents", "code": "", "note": "Automatically add a table of contents from the headings in your posts.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 27158, "title": "<PERSON><PERSON><PERSON> Scrolling for Anchor Links", "code": "", "note": "Enables smooth scrolling for all on-page links with #.", "categories": ["widgets"], "code_type": "js", "needs_auth": "1"}, {"library_id": 22991, "title": "Social Share Buttons", "code": "", "note": "Add links to share articles to popular social media platforms.", "categories": ["archive"], "code_type": "php", "needs_auth": "1"}, {"library_id": 29708, "title": "Special Offer Pop-up", "code": "", "note": "Display a small pop-up in the bottom-right of the page.", "categories": ["widgets"], "code_type": "universal", "needs_auth": "1"}], "links": {"snippet": "https://library.wpcode.com/api/get/"}}
<?php
/**
 * Test Phase 2: Language Enhancement for ChatGABI Templates
 * 
 * This script tests the multi-language template system implementation
 */

// Load WordPress
require_once('wp-load.php');

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>ChatGABI Phase 2: Language Enhancement Test</h1>";

// Test 1: Check language-specific template files
echo "<h2>1. Language Template Files Test</h2>";

$languages = array('en', 'tw', 'sw', 'yo', 'zu');
$template_dir = WP_CONTENT_DIR . '/datasets/templates';

foreach ($languages as $lang) {
    $template_file = $template_dir . '/' . $lang . '/business_plan_templates.json';
    if (file_exists($template_file)) {
        $content = file_get_contents($template_file);
        $data = json_decode($content, true);
        if ($data) {
            echo "✅ {$lang}: {$data['language_name']} template loaded successfully<br>";
            echo "&nbsp;&nbsp;&nbsp;Cultural context: {$data['cultural_context']}<br>";
            echo "&nbsp;&nbsp;&nbsp;Sections: " . count($data['templates']['business_plan']) . "<br>";
        } else {
            echo "❌ {$lang}: Invalid JSON format<br>";
        }
    } else {
        echo "❌ {$lang}: Template file missing<br>";
    }
}

// Test 2: Check available languages function
echo "<h2>2. Available Languages Function Test</h2>";

try {
    $available_languages = chatgabi_get_available_languages();
    echo "✅ Available languages function works<br>";
    foreach ($available_languages as $code => $data) {
        echo "&nbsp;&nbsp;&nbsp;{$code}: {$data['name']} ({$data['native_name']}) - {$data['cultural_context']}<br>";
    }
} catch (Exception $e) {
    echo "❌ Available languages function failed: " . $e->getMessage() . "<br>";
}

// Test 3: Check cultural business practices function
echo "<h2>3. Cultural Business Practices Test</h2>";

foreach ($languages as $lang) {
    try {
        $practices = chatgabi_get_cultural_business_practices($lang);
        echo "✅ {$lang}: Cultural practices loaded<br>";
        echo "&nbsp;&nbsp;&nbsp;Communication: " . substr($practices['communication_style'], 0, 50) . "...<br>";
    } catch (Exception $e) {
        echo "❌ {$lang}: Cultural practices failed: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Check language template loading function
echo "<h2>4. Language Template Loading Test</h2>";

foreach ($languages as $lang) {
    try {
        $template_data = chatgabi_load_language_template('business_plan', $lang);
        if ($template_data) {
            echo "✅ {$lang}: Template loaded successfully<br>";
            echo "&nbsp;&nbsp;&nbsp;Language: {$template_data['language_name']}<br>";
            echo "&nbsp;&nbsp;&nbsp;Business plan sections: " . count($template_data['templates']['business_plan']) . "<br>";
        } else {
            echo "❌ {$lang}: Template loading failed<br>";
        }
    } catch (Exception $e) {
        echo "❌ {$lang}: Template loading error: " . $e->getMessage() . "<br>";
    }
}

// Test 5: Check localized template preview function
echo "<h2>5. Localized Template Preview Test</h2>";

$template_ids = array('tech-startup', 'agricultural', 'retail', 'fintech');

foreach ($template_ids as $template_id) {
    foreach (array('en', 'tw', 'sw') as $lang) {
        try {
            $preview_data = chatgabi_get_localized_template_preview($template_id, $lang);
            if ($preview_data) {
                echo "✅ {$template_id} ({$lang}): Preview data generated<br>";
                echo "&nbsp;&nbsp;&nbsp;Language: {$preview_data['language_name']}<br>";
                echo "&nbsp;&nbsp;&nbsp;Cultural context: {$preview_data['cultural_context']}<br>";
            } else {
                echo "❌ {$template_id} ({$lang}): Preview data failed<br>";
            }
        } catch (Exception $e) {
            echo "❌ {$template_id} ({$lang}): Preview error: " . $e->getMessage() . "<br>";
        }
    }
}

// Test 6: Check user preference functions
echo "<h2>6. User Preference Functions Test</h2>";

$test_user_id = 1; // Admin user

try {
    // Test getting default preference
    $default_lang = chatgabi_get_user_preferred_language($test_user_id);
    echo "✅ Default language preference: {$default_lang}<br>";
    
    // Test saving preference
    $save_result = chatgabi_save_user_preferred_language($test_user_id, 'sw');
    if ($save_result) {
        echo "✅ Language preference saved successfully<br>";
        
        // Test getting saved preference
        $saved_lang = chatgabi_get_user_preferred_language($test_user_id);
        echo "✅ Saved language preference retrieved: {$saved_lang}<br>";
        
        // Reset to default
        chatgabi_save_user_preferred_language($test_user_id, 'en');
    } else {
        echo "❌ Failed to save language preference<br>";
    }
} catch (Exception $e) {
    echo "❌ User preference functions error: " . $e->getMessage() . "<br>";
}

// Test 7: Check enhanced template generation prompt
echo "<h2>7. Enhanced Template Generation Prompt Test</h2>";

// Create mock template data
$mock_template = (object) array(
    'target_country' => 'GH',
    'industry_sector' => 'Technology',
    'document_language' => 'tw',
    'business_idea' => 'Mobile app for farmers'
);

$mock_form_data = array(
    'business_name' => 'AgriTech Ghana',
    'target_market' => 'Smallholder farmers in Ghana'
);

try {
    $enhanced_prompt = chatgabi_build_template_generation_prompt($mock_template, $mock_form_data);
    if (strpos($enhanced_prompt, 'Cultural Business Context') !== false) {
        echo "✅ Enhanced prompt includes cultural context<br>";
    } else {
        echo "❌ Enhanced prompt missing cultural context<br>";
    }
    
    if (strpos($enhanced_prompt, 'Twi') !== false) {
        echo "✅ Enhanced prompt includes language-specific content<br>";
    } else {
        echo "❌ Enhanced prompt missing language-specific content<br>";
    }
} catch (Exception $e) {
    echo "❌ Enhanced prompt generation error: " . $e->getMessage() . "<br>";
}

// Test 8: Check AJAX handlers registration
echo "<h2>8. AJAX Handlers Test</h2>";

if (has_action('wp_ajax_chatgabi_get_template_preview')) {
    echo "✅ Template preview AJAX handler registered<br>";
} else {
    echo "❌ Template preview AJAX handler missing<br>";
}

if (has_action('wp_ajax_chatgabi_save_language_preference')) {
    echo "✅ Language preference AJAX handler registered<br>";
} else {
    echo "❌ Language preference AJAX handler missing<br>";
}

// Test 9: Check preview HTML generation
echo "<h2>9. Preview HTML Generation Test</h2>";

try {
    $preview_data = chatgabi_get_localized_template_preview('tech-startup', 'tw');
    if ($preview_data) {
        $preview_html = chatgabi_generate_preview_html('tech-startup', $preview_data);
        if (strpos($preview_html, 'Twi') !== false) {
            echo "✅ Preview HTML includes language-specific content<br>";
        } else {
            echo "❌ Preview HTML missing language-specific content<br>";
        }
        
        if (strpos($preview_html, 'cultural-practices') !== false) {
            echo "✅ Preview HTML includes cultural practices section<br>";
        } else {
            echo "❌ Preview HTML missing cultural practices section<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Preview HTML generation error: " . $e->getMessage() . "<br>";
}

echo "<h2>Phase 2 Implementation Summary</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h3>✅ Completed Features:</h3>";
echo "<ul>";
echo "<li>✅ Language-specific template content files (5 languages)</li>";
echo "<li>✅ Cultural business practices integration</li>";
echo "<li>✅ Enhanced template generation with cultural context</li>";
echo "<li>✅ User language preference system</li>";
echo "<li>✅ Localized template preview functionality</li>";
echo "<li>✅ AJAX handlers for language features</li>";
echo "<li>✅ Enhanced admin interface with language selection</li>";
echo "</ul>";

echo "<h3>🎯 Next Steps:</h3>";
echo "<ul>";
echo "<li>Test the enhanced preview functionality in the admin interface</li>";
echo "<li>Verify language switching works correctly</li>";
echo "<li>Test template generation with different languages</li>";
echo "<li>Check cultural context integration in AI responses</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Manual Testing Instructions</h2>";
echo "<ol>";
echo "<li>Go to <a href='wp-admin/admin.php?page=chatgabi-templates' target='_blank'>ChatGABI Templates</a></li>";
echo "<li>Change the document language in the template creation form</li>";
echo "<li>Click any 'Preview' button to see language-specific content</li>";
echo "<li>Verify cultural practices and local terminology appear</li>";
echo "<li>Check that language preferences are saved automatically</li>";
echo "</ol>";
?>

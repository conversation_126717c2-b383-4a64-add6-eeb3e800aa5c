/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var s in o)e.o(o,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:o[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{PluginBlockSettingsMenuItem:()=>Gt,PluginDocumentSettingPanel:()=>Ut,PluginMoreMenuItem:()=>Vt,PluginPostPublishPanel:()=>qt,PluginPostStatusInfo:()=>Wt,PluginPrePublishPanel:()=>Ht,PluginSidebar:()=>Qt,PluginSidebarMoreMenuItem:()=>Xt,__experimentalFullscreenModeClose:()=>I,__experimentalMainDashboardButton:()=>$t,__experimentalPluginPostExcerpt:()=>Zt,initializeEditor:()=>Kt,reinitializeEditor:()=>Jt,store:()=>Je});var o={};e.r(o),e.d(o,{__experimentalSetPreviewDeviceType:()=>ge,__unstableCreateTemplate:()=>_e,closeGeneralSidebar:()=>X,closeModal:()=>$,closePublishSidebar:()=>K,hideBlockTypes:()=>ce,initializeMetaBoxes:()=>ye,metaBoxUpdatesFailure:()=>ue,metaBoxUpdatesSuccess:()=>pe,openGeneralSidebar:()=>Q,openModal:()=>Z,openPublishSidebar:()=>Y,removeEditorPanel:()=>oe,requestMetaBoxUpdates:()=>de,setAvailableMetaBoxesPerLocation:()=>le,setIsEditingTemplate:()=>we,setIsInserterOpened:()=>me,setIsListViewOpened:()=>he,showBlockTypes:()=>ae,switchEditorMode:()=>ie,toggleDistractionFree:()=>be,toggleEditorPanelEnabled:()=>ee,toggleEditorPanelOpened:()=>te,toggleFeature:()=>se,toggleFullscreenMode:()=>xe,togglePinnedPluginItem:()=>re,togglePublishSidebar:()=>J,updatePreferredStyleVariations:()=>ne});var s={};e.r(s),e.d(s,{__experimentalGetInsertionPoint:()=>Xe,__experimentalGetPreviewDeviceType:()=>We,areMetaBoxesInitialized:()=>Ye,getActiveGeneralSidebarName:()=>ke,getActiveMetaBoxLocations:()=>Fe,getAllMetaBoxes:()=>Ve,getEditedPostTemplate:()=>Ke,getEditorMode:()=>Ee,getHiddenBlockTypes:()=>Ie,getMetaBoxesPerLocation:()=>Ue,getPreference:()=>Be,getPreferences:()=>Te,hasMetaBoxes:()=>He,isEditingTemplate:()=>$e,isEditorPanelEnabled:()=>Ce,isEditorPanelOpened:()=>De,isEditorPanelRemoved:()=>Re,isEditorSidebarOpened:()=>Me,isFeatureActive:()=>Ne,isInserterOpened:()=>Qe,isListViewOpened:()=>Ze,isMetaBoxLocationActive:()=>Ge,isMetaBoxLocationVisible:()=>ze,isModalActive:()=>Oe,isPluginItemPinned:()=>Le,isPluginSidebarOpened:()=>je,isPublishSidebarOpened:()=>Ae,isSavingMetaBoxes:()=>qe});const i=window.wp.blocks,r=window.wp.blockLibrary,n=window.wp.deprecated;var a=e.n(n);const c=window.wp.element,l=window.wp.data,d=window.wp.preferences,p=window.wp.widgets,u=window.wp.editor;function g(e){var t,o,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(o=g(e[t]))&&(s&&(s+=" "),s+=o)}else for(o in e)e[o]&&(s&&(s+=" "),s+=o);return s}const m=function(){for(var e,t,o=0,s="",i=arguments.length;o<i;o++)(e=arguments[o])&&(t=g(e))&&(s&&(s+=" "),s+=t);return s},h=window.wp.blockEditor,w=window.wp.plugins,_=window.wp.i18n,f=window.wp.primitives,y=window.ReactJSXRuntime,b=(0,y.jsx)(f.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,y.jsx)(f.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})}),x=(0,y.jsx)(f.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,y.jsx)(f.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})}),v=window.wp.notices,S=window.wp.commands,P=window.wp.coreCommands,E=window.wp.url,M=window.wp.htmlEntities,j=window.wp.coreData,k=window.wp.components,T=window.wp.compose,B=(0,y.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,y.jsx)(f.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})});const I=function({showTooltip:e,icon:t,href:o,initialPost:s}){var i;const{isRequestingSiteIcon:r,postType:n,siteIconUrl:a}=(0,l.useSelect)((e=>{const{getCurrentPostType:t}=e(u.store),{getEntityRecord:o,getPostType:i,isResolving:r}=e(j.store),n=o("root","__unstableBase",void 0)||{},a=s?.type||t();return{isRequestingSiteIcon:r("getEntityRecord",["root","__unstableBase",void 0]),postType:i(a),siteIconUrl:n.site_icon_url}}),[]),c=(0,T.useReducedMotion)();if(!n)return null;let d=(0,y.jsx)(k.Icon,{size:"36px",icon:B});const p={expand:{scale:1.25,transition:{type:"tween",duration:"0.3"}}};a&&(d=(0,y.jsx)(k.__unstableMotion.img,{variants:!c&&p,alt:(0,_.__)("Site Icon"),className:"edit-post-fullscreen-mode-close_site-icon",src:a})),r&&(d=null),t&&(d=(0,y.jsx)(k.Icon,{size:"36px",icon:t}));const g=m("edit-post-fullscreen-mode-close",{"has-icon":a}),h=null!=o?o:(0,E.addQueryArgs)("edit.php",{post_type:n.slug}),w=null!==(i=n?.labels?.view_items)&&void 0!==i?i:(0,_.__)("Back");return(0,y.jsx)(k.__unstableMotion.div,{whileHover:"expand",children:(0,y.jsx)(k.Button,{__next40pxDefaultSize:!0,className:g,href:h,label:w,showTooltip:e,children:d})})},A=window.wp.privateApis,{lock:R,unlock:C}=(0,A.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-post"),{BackButton:D}=C(u.privateApis),O={hidden:{x:"-100%"},distractionFreeInactive:{x:0},hover:{x:0,transition:{type:"tween",delay:.2}}};const N=function({initialPost:e}){return(0,y.jsx)(D,{children:({length:t})=>t<=1&&(0,y.jsx)(k.__unstableMotion.div,{variants:O,transition:{type:"tween",delay:.8},children:(0,y.jsx)(I,{showTooltip:!0,initialPost:e})})})};function L(){return(()=>{const{newPermalink:e}=(0,l.useSelect)((e=>({newPermalink:e(u.store).getCurrentPost().link})),[]),t=(0,c.useRef)();(0,c.useEffect)((()=>{t.current=document.querySelector("#wp-admin-bar-preview a")||document.querySelector("#wp-admin-bar-view a")}),[]),(0,c.useEffect)((()=>{e&&t.current&&t.current.setAttribute("href",e)}),[e])})(),null}const F=window.wp.keyboardShortcuts;function z(e=[],t){const o=[...e];for(const e of t){const t=o.findIndex((t=>t.id===e.id));-1!==t?o[t]=e:o.push(e)}return o}const G=(0,l.combineReducers)({isSaving:function(e=!1,t){switch(t.type){case"REQUEST_META_BOX_UPDATES":return!0;case"META_BOX_UPDATES_SUCCESS":case"META_BOX_UPDATES_FAILURE":return!1;default:return e}},locations:function(e={},t){if("SET_META_BOXES_PER_LOCATIONS"===t.type){const o={...e};for(const[e,s]of Object.entries(t.metaBoxesPerLocation))o[e]=z(o[e],s);return o}return e},initialized:function(e=!1,t){return"META_BOXES_INITIALIZED"===t.type||e}}),U=(0,l.combineReducers)({metaBoxes:G}),V=window.wp.apiFetch;var H=e.n(V);const q=window.wp.hooks,{interfaceStore:W}=C(u.privateApis),Q=e=>({registry:t})=>{t.dispatch(W).enableComplementaryArea("core",e)},X=()=>({registry:e})=>e.dispatch(W).disableComplementaryArea("core"),Z=e=>({registry:t})=>(a()("select( 'core/edit-post' ).openModal( name )",{since:"6.3",alternative:"select( 'core/interface').openModal( name )"}),t.dispatch(W).openModal(e)),$=()=>({registry:e})=>(a()("select( 'core/edit-post' ).closeModal()",{since:"6.3",alternative:"select( 'core/interface').closeModal()"}),e.dispatch(W).closeModal()),Y=()=>({registry:e})=>{a()("dispatch( 'core/edit-post' ).openPublishSidebar",{since:"6.6",alternative:"dispatch( 'core/editor').openPublishSidebar"}),e.dispatch(u.store).openPublishSidebar()},K=()=>({registry:e})=>{a()("dispatch( 'core/edit-post' ).closePublishSidebar",{since:"6.6",alternative:"dispatch( 'core/editor').closePublishSidebar"}),e.dispatch(u.store).closePublishSidebar()},J=()=>({registry:e})=>{a()("dispatch( 'core/edit-post' ).togglePublishSidebar",{since:"6.6",alternative:"dispatch( 'core/editor').togglePublishSidebar"}),e.dispatch(u.store).togglePublishSidebar()},ee=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).toggleEditorPanelEnabled",{since:"6.5",alternative:"dispatch( 'core/editor').toggleEditorPanelEnabled"}),t.dispatch(u.store).toggleEditorPanelEnabled(e)},te=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).toggleEditorPanelOpened",{since:"6.5",alternative:"dispatch( 'core/editor').toggleEditorPanelOpened"}),t.dispatch(u.store).toggleEditorPanelOpened(e)},oe=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).removeEditorPanel",{since:"6.5",alternative:"dispatch( 'core/editor').removeEditorPanel"}),t.dispatch(u.store).removeEditorPanel(e)},se=e=>({registry:t})=>t.dispatch(d.store).toggle("core/edit-post",e),ie=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).switchEditorMode",{since:"6.6",alternative:"dispatch( 'core/editor').switchEditorMode"}),t.dispatch(u.store).switchEditorMode(e)},re=e=>({registry:t})=>{const o=t.select(W).isItemPinned("core",e);t.dispatch(W)[o?"unpinItem":"pinItem"]("core",e)};function ne(){return a()("dispatch( 'core/edit-post' ).updatePreferredStyleVariations",{since:"6.6",hint:"Preferred Style Variations are not supported anymore."}),{type:"NOTHING"}}const ae=e=>({registry:t})=>{C(t.dispatch(u.store)).showBlockTypes(e)},ce=e=>({registry:t})=>{C(t.dispatch(u.store)).hideBlockTypes(e)};function le(e){return{type:"SET_META_BOXES_PER_LOCATIONS",metaBoxesPerLocation:e}}const de=()=>async({registry:e,select:t,dispatch:o})=>{o({type:"REQUEST_META_BOX_UPDATES"}),window.tinyMCE&&window.tinyMCE.triggerSave();const s=new window.FormData(document.querySelector(".metabox-base-form")),i=s.get("post_ID"),r=s.get("post_type"),n=e.select(j.store).getEditedEntityRecord("postType",r,i),a=[!!n.comment_status&&["comment_status",n.comment_status],!!n.ping_status&&["ping_status",n.ping_status],!!n.sticky&&["sticky",n.sticky],!!n.author&&["post_author",n.author]].filter(Boolean),c=[s,...t.getActiveMetaBoxLocations().map((e=>new window.FormData((e=>{const t=document.querySelector(`.edit-post-meta-boxes-area.is-${e} .metabox-location-${e}`);return t||document.querySelector("#metaboxes .metabox-location-"+e)})(e))))].reduce(((e,t)=>{for(const[o,s]of t)e.append(o,s);return e}),new window.FormData);a.forEach((([e,t])=>c.append(e,t)));try{await H()({url:window._wpMetaBoxUrl,method:"POST",body:c,parse:!1}),o.metaBoxUpdatesSuccess()}catch{o.metaBoxUpdatesFailure()}};function pe(){return{type:"META_BOX_UPDATES_SUCCESS"}}function ue(){return{type:"META_BOX_UPDATES_FAILURE"}}const ge=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).__experimentalSetPreviewDeviceType",{since:"6.5",version:"6.7",hint:"registry.dispatch( editorStore ).setDeviceType"}),t.dispatch(u.store).setDeviceType(e)},me=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).setIsInserterOpened",{since:"6.5",alternative:"dispatch( 'core/editor').setIsInserterOpened"}),t.dispatch(u.store).setIsInserterOpened(e)},he=e=>({registry:t})=>{a()("dispatch( 'core/edit-post' ).setIsListViewOpened",{since:"6.5",alternative:"dispatch( 'core/editor').setIsListViewOpened"}),t.dispatch(u.store).setIsListViewOpened(e)};function we(){return a()("dispatch( 'core/edit-post' ).setIsEditingTemplate",{since:"6.5",alternative:"dispatch( 'core/editor').setRenderingMode"}),{type:"NOTHING"}}function _e(){return a()("dispatch( 'core/edit-post' ).__unstableCreateTemplate",{since:"6.5"}),{type:"NOTHING"}}let fe=!1;const ye=()=>({registry:e,select:t,dispatch:o})=>{if(!e.select(u.store).__unstableIsEditorReady())return;if(fe)return;const s=e.select(u.store).getCurrentPostType();window.postboxes.page!==s&&window.postboxes.add_postbox_toggles(s),fe=!0,(0,q.addAction)("editor.savePost","core/edit-post/save-metaboxes",(async(e,s)=>{!s.isAutosave&&t.hasMetaBoxes()&&await o.requestMetaBoxUpdates()})),o({type:"META_BOXES_INITIALIZED"})},be=()=>({registry:e})=>{a()("dispatch( 'core/edit-post' ).toggleDistractionFree",{since:"6.6",alternative:"dispatch( 'core/editor').toggleDistractionFree"}),e.dispatch(u.store).toggleDistractionFree()},xe=()=>({registry:e})=>{const t=e.select(d.store).get("core/edit-post","fullscreenMode");e.dispatch(d.store).toggle("core/edit-post","fullscreenMode"),e.dispatch(v.store).createInfoNotice(t?(0,_.__)("Fullscreen mode deactivated."):(0,_.__)("Fullscreen mode activated."),{id:"core/edit-post/toggle-fullscreen-mode/notice",type:"snackbar",actions:[{label:(0,_.__)("Undo"),onClick:()=>{e.dispatch(d.store).toggle("core/edit-post","fullscreenMode")}}]})},{interfaceStore:ve}=C(u.privateApis),Se=[],Pe={},Ee=(0,l.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(d.store).get("core","editorMode"))&&void 0!==t?t:"visual"})),Me=(0,l.createRegistrySelector)((e=>()=>{const t=e(ve).getActiveComplementaryArea("core");return["edit-post/document","edit-post/block"].includes(t)})),je=(0,l.createRegistrySelector)((e=>()=>{const t=e(ve).getActiveComplementaryArea("core");return!!t&&!["edit-post/document","edit-post/block"].includes(t)})),ke=(0,l.createRegistrySelector)((e=>()=>e(ve).getActiveComplementaryArea("core")));const Te=(0,l.createRegistrySelector)((e=>()=>{a()("select( 'core/edit-post' ).getPreferences",{since:"6.0",alternative:"select( 'core/preferences' ).get"});const t=["editorMode","hiddenBlockTypes"].reduce(((t,o)=>{const s=e(d.store).get("core",o);return{...t,[o]:s}}),{}),o=function(e,t){var o;const s=e?.reduce(((e,t)=>({...e,[t]:{enabled:!1}})),{}),i=t?.reduce(((e,t)=>{const o=e?.[t];return{...e,[t]:{...o,opened:!0}}}),null!=s?s:{});return null!==(o=null!=i?i:s)&&void 0!==o?o:Pe}(e(d.store).get("core","inactivePanels"),e(d.store).get("core","openPanels"));return{...t,panels:o}}));function Be(e,t,o){a()("select( 'core/edit-post' ).getPreference",{since:"6.0",alternative:"select( 'core/preferences' ).get"});const s=Te(e)[t];return void 0===s?o:s}const Ie=(0,l.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(d.store).get("core","hiddenBlockTypes"))&&void 0!==t?t:Se})),Ae=(0,l.createRegistrySelector)((e=>()=>(a()("select( 'core/edit-post' ).isPublishSidebarOpened",{since:"6.6",alternative:"select( 'core/editor' ).isPublishSidebarOpened"}),e(u.store).isPublishSidebarOpened()))),Re=(0,l.createRegistrySelector)((e=>(t,o)=>(a()("select( 'core/edit-post' ).isEditorPanelRemoved",{since:"6.5",alternative:"select( 'core/editor' ).isEditorPanelRemoved"}),e(u.store).isEditorPanelRemoved(o)))),Ce=(0,l.createRegistrySelector)((e=>(t,o)=>(a()("select( 'core/edit-post' ).isEditorPanelEnabled",{since:"6.5",alternative:"select( 'core/editor' ).isEditorPanelEnabled"}),e(u.store).isEditorPanelEnabled(o)))),De=(0,l.createRegistrySelector)((e=>(t,o)=>(a()("select( 'core/edit-post' ).isEditorPanelOpened",{since:"6.5",alternative:"select( 'core/editor' ).isEditorPanelOpened"}),e(u.store).isEditorPanelOpened(o)))),Oe=(0,l.createRegistrySelector)((e=>(t,o)=>(a()("select( 'core/edit-post' ).isModalActive",{since:"6.3",alternative:"select( 'core/interface' ).isModalActive"}),!!e(ve).isModalActive(o)))),Ne=(0,l.createRegistrySelector)((e=>(t,o)=>!!e(d.store).get("core/edit-post",o))),Le=(0,l.createRegistrySelector)((e=>(t,o)=>e(ve).isItemPinned("core",o))),Fe=(0,l.createSelector)((e=>Object.keys(e.metaBoxes.locations).filter((t=>Ge(e,t)))),(e=>[e.metaBoxes.locations])),ze=(0,l.createRegistrySelector)((e=>(t,o)=>Ge(t,o)&&Ue(t,o)?.some((({id:t})=>e(u.store).isEditorPanelEnabled(`meta-box-${t}`)))));function Ge(e,t){const o=Ue(e,t);return!!o&&0!==o.length}function Ue(e,t){return e.metaBoxes.locations[t]}const Ve=(0,l.createSelector)((e=>Object.values(e.metaBoxes.locations).flat()),(e=>[e.metaBoxes.locations]));function He(e){return Fe(e).length>0}function qe(e){return e.metaBoxes.isSaving}const We=(0,l.createRegistrySelector)((e=>()=>(a()("select( 'core/edit-site' ).__experimentalGetPreviewDeviceType",{since:"6.5",version:"6.7",alternative:"select( 'core/editor' ).getDeviceType"}),e(u.store).getDeviceType()))),Qe=(0,l.createRegistrySelector)((e=>()=>(a()("select( 'core/edit-post' ).isInserterOpened",{since:"6.5",alternative:"select( 'core/editor' ).isInserterOpened"}),e(u.store).isInserterOpened()))),Xe=(0,l.createRegistrySelector)((e=>()=>(a()("select( 'core/edit-post' ).__experimentalGetInsertionPoint",{since:"6.5",version:"6.7"}),C(e(u.store)).getInserter()))),Ze=(0,l.createRegistrySelector)((e=>()=>(a()("select( 'core/edit-post' ).isListViewOpened",{since:"6.5",alternative:"select( 'core/editor' ).isListViewOpened"}),e(u.store).isListViewOpened()))),$e=(0,l.createRegistrySelector)((e=>()=>(a()("select( 'core/edit-post' ).isEditingTemplate",{since:"6.5",alternative:"select( 'core/editor' ).getRenderingMode"}),"wp_template"===e(u.store).getCurrentPostType())));function Ye(e){return e.metaBoxes.initialized}const Ke=(0,l.createRegistrySelector)((e=>()=>{const{id:t,type:o}=e(u.store).getCurrentPost(),s=C(e(j.store)).getTemplateId(o,t);if(s)return e(j.store).getEditedEntityRecord("postType","wp_template",s)})),Je=(0,l.createReduxStore)("core/edit-post",{reducer:U,actions:o,selectors:s});(0,l.register)(Je);const et=function(){const{toggleFullscreenMode:e}=(0,l.useDispatch)(Je),{registerShortcut:t}=(0,l.useDispatch)(F.store);return(0,c.useEffect)((()=>{t({name:"core/edit-post/toggle-fullscreen",category:"global",description:(0,_.__)("Enable or disable fullscreen mode."),keyCombination:{modifier:"secondary",character:"f"}})}),[]),(0,F.useShortcut)("core/edit-post/toggle-fullscreen",(()=>{e()})),null};function tt(){const{editPost:e}=(0,l.useDispatch)(u.store),[t,o]=(0,c.useState)(void 0),[s,i]=(0,c.useState)(""),{postType:r,isNewPost:n}=(0,l.useSelect)((e=>{const{getEditedPostAttribute:t,isCleanNewPost:o}=e(u.store);return{postType:t("type"),isNewPost:o()}}),[]),[a,d]=(0,c.useState)((()=>n&&"wp_block"===r));return"wp_block"===r&&n?(0,y.jsx)(y.Fragment,{children:a&&(0,y.jsx)(k.Modal,{title:(0,_.__)("Create pattern"),onRequestClose:()=>{d(!1)},overlayClassName:"reusable-blocks-menu-items__convert-modal",children:(0,y.jsx)("form",{onSubmit:o=>{o.preventDefault(),d(!1),e({title:s,meta:{wp_pattern_sync_status:t}})},children:(0,y.jsxs)(k.__experimentalVStack,{spacing:"5",children:[(0,y.jsx)(k.TextControl,{label:(0,_.__)("Name"),value:s,onChange:i,placeholder:(0,_.__)("My pattern"),className:"patterns-create-modal__name-input",__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,y.jsx)(k.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,_._x)("Synced","pattern (singular)"),help:(0,_.__)("Sync this pattern across multiple locations."),checked:!t,onChange:()=>{o(t?void 0:"unsynced")}}),(0,y.jsx)(k.__experimentalHStack,{justify:"right",children:(0,y.jsx)(k.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",disabled:!s,accessibleWhenDisabled:!0,children:(0,_.__)("Create")})})]})})})}):null}class ot extends c.Component{constructor(){super(...arguments),this.state={historyId:null}}componentDidUpdate(e){const{postId:t,postStatus:o}=this.props,{historyId:s}=this.state;t===e.postId&&t===s||"auto-draft"===o||!t||this.setBrowserURL(t)}setBrowserURL(e){window.history.replaceState({id:e},"Post "+e,function(e){return(0,E.addQueryArgs)("post.php",{post:e,action:"edit"})}(e)),this.setState((()=>({historyId:e})))}render(){return null}}const st=(0,l.withSelect)((e=>{const{getCurrentPost:t}=e(u.store),o=t();let{id:s,status:i,type:r}=o;return["wp_template","wp_template_part"].includes(r)&&(s=o.wp_id),{postId:s,postStatus:i}}))(ot);const it=function({location:e}){const t=(0,c.useRef)(null),o=(0,c.useRef)(null);(0,c.useEffect)((()=>(o.current=document.querySelector(".metabox-location-"+e),o.current&&t.current.appendChild(o.current),()=>{o.current&&document.querySelector("#metaboxes").appendChild(o.current)})),[e]);const s=(0,l.useSelect)((e=>e(Je).isSavingMetaBoxes()),[]),i=m("edit-post-meta-boxes-area",`is-${e}`,{"is-loading":s});return(0,y.jsxs)("div",{className:i,children:[s&&(0,y.jsx)(k.Spinner,{}),(0,y.jsx)("div",{className:"edit-post-meta-boxes-area__container",ref:t}),(0,y.jsx)("div",{className:"edit-post-meta-boxes-area__clear"})]})};function rt({id:e}){const t=(0,l.useSelect)((t=>t(u.store).isEditorPanelEnabled(`meta-box-${e}`)),[e]);return(0,c.useEffect)((()=>{const o=document.getElementById(e);o&&(t?o.classList.remove("is-hidden"):o.classList.add("is-hidden"))}),[e,t]),null}function nt({location:e}){const t=(0,l.useSelect)((t=>t(Je).getMetaBoxesPerLocation(e)),[e]);return(0,y.jsxs)(y.Fragment,{children:[(null!=t?t:[]).map((({id:e})=>(0,y.jsx)(rt,{id:e},e))),(0,y.jsx)(it,{location:e})]})}const at=window.wp.keycodes;const ct=function(){const e=(0,l.useSelect)((e=>{const{canUser:t}=e(j.store),o=(0,E.addQueryArgs)("edit.php",{post_type:"wp_block"}),s=(0,E.addQueryArgs)("site-editor.php",{path:"/patterns"});return t("create",{kind:"postType",name:"wp_template"})?s:o}),[]);return(0,y.jsx)(k.MenuItem,{role:"menuitem",href:e,children:(0,_.__)("Manage patterns")})};function lt(){const e=(0,l.useSelect)((e=>"wp_template"===e(u.store).getCurrentPostType()),[]);return(0,y.jsx)(d.PreferenceToggleMenuItem,{scope:"core/edit-post",name:e?"welcomeGuideTemplate":"welcomeGuide",label:(0,_.__)("Welcome Guide")})}const{PreferenceBaseOption:dt}=C(d.privateApis);function pt({willEnable:e}){const[t,o]=(0,c.useState)(!1);return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("p",{className:"edit-post-preferences-modal__custom-fields-confirmation-message",children:(0,_.__)("A page reload is required for this change. Make sure your content is saved before reloading.")}),(0,y.jsx)(k.Button,{__next40pxDefaultSize:!0,variant:"secondary",isBusy:t,accessibleWhenDisabled:!0,disabled:t,onClick:()=>{o(!0),function(){const e=document.getElementById("toggle-custom-fields-form");e.querySelector('[name="_wp_http_referer"]').setAttribute("value",(0,E.getPathAndQueryString)(window.location.href)),e.submit()}()},children:e?(0,_.__)("Show & Reload Page"):(0,_.__)("Hide & Reload Page")})]})}function ut({label:e}){const t=(0,l.useSelect)((e=>!!e(u.store).getEditorSettings().enableCustomFields),[]),[o,s]=(0,c.useState)(t);return(0,y.jsx)(dt,{label:e,isChecked:o,onChange:s,children:o!==t&&(0,y.jsx)(pt,{willEnable:o})})}const{PreferenceBaseOption:gt}=C(d.privateApis);function mt(e){const{toggleEditorPanelEnabled:t}=(0,l.useDispatch)(u.store),{isChecked:o,isRemoved:s}=(0,l.useSelect)((t=>{const{isEditorPanelEnabled:o,isEditorPanelRemoved:s}=t(u.store);return{isChecked:o(e.panelName),isRemoved:s(e.panelName)}}),[e.panelName]);return s?null:(0,y.jsx)(gt,{isChecked:o,onChange:()=>t(e.panelName),...e})}const{PreferencesModalSection:ht}=C(d.privateApis);const wt=(0,l.withSelect)((e=>{const{getEditorSettings:t}=e(u.store),{getAllMetaBoxes:o}=e(Je);return{areCustomFieldsRegistered:void 0!==t().enableCustomFields,metaBoxes:o()}}))((function({areCustomFieldsRegistered:e,metaBoxes:t,...o}){const s=t.filter((({id:e})=>"postcustom"!==e));return e||0!==s.length?(0,y.jsxs)(ht,{...o,children:[e&&(0,y.jsx)(ut,{label:(0,_.__)("Custom fields")}),s.map((({id:e,title:t})=>(0,y.jsx)(mt,{label:t,panelName:`meta-box-${e}`},e)))]}):null})),{PreferenceToggleControl:_t}=C(d.privateApis),{PreferencesModal:ft}=C(u.privateApis);function yt(){const e={general:(0,y.jsx)(wt,{title:(0,_.__)("Advanced")}),appearance:(0,y.jsx)(_t,{scope:"core/edit-post",featureName:"themeStyles",help:(0,_.__)("Make the editor look like your theme."),label:(0,_.__)("Use theme styles")})};return(0,y.jsx)(ft,{extraSections:e})}const{ToolsMoreMenuGroup:bt,ViewMoreMenuGroup:xt}=C(u.privateApis),vt=()=>{const e=(0,T.useViewportMatch)("large");return(0,y.jsxs)(y.Fragment,{children:[e&&(0,y.jsx)(xt,{children:(0,y.jsx)(d.PreferenceToggleMenuItem,{scope:"core/edit-post",name:"fullscreenMode",label:(0,_.__)("Fullscreen mode"),info:(0,_.__)("Show and hide the admin user interface"),messageActivated:(0,_.__)("Fullscreen mode activated."),messageDeactivated:(0,_.__)("Fullscreen mode deactivated."),shortcut:at.displayShortcut.secondary("f")})}),(0,y.jsxs)(bt,{children:[(0,y.jsx)(ct,{}),(0,y.jsx)(lt,{})]}),(0,y.jsx)(yt,{})]})};function St({nonAnimatedSrc:e,animatedSrc:t}){return(0,y.jsxs)("picture",{className:"edit-post-welcome-guide__image",children:[(0,y.jsx)("source",{srcSet:e,media:"(prefers-reduced-motion: reduce)"}),(0,y.jsx)("img",{src:t,width:"312",height:"240",alt:""})]})}function Pt(){const{toggleFeature:e}=(0,l.useDispatch)(Je);return(0,y.jsx)(k.Guide,{className:"edit-post-welcome-guide",contentLabel:(0,_.__)("Welcome to the editor"),finishButtonText:(0,_.__)("Get started"),onFinish:()=>e("welcomeGuide"),pages:[{image:(0,y.jsx)(St,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.gif"}),content:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,_.__)("Welcome to the editor")}),(0,y.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,_.__)("In the WordPress editor, each paragraph, image, or video is presented as a distinct “block” of content.")})]})},{image:(0,y.jsx)(St,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-editor.gif"}),content:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,_.__)("Customize each block")}),(0,y.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,_.__)("Each block comes with its own set of controls for changing things like color, width, and alignment. These will show and hide automatically when you have a block selected.")})]})},{image:(0,y.jsx)(St,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-library.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-library.gif"}),content:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,_.__)("Explore all blocks")}),(0,y.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,c.createInterpolateElement)((0,_.__)("All of the blocks available to you live in the block library. You’ll find it wherever you see the <InserterIconImage /> icon."),{InserterIconImage:(0,y.jsx)("img",{alt:(0,_.__)("inserter"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' rx='2' fill='%231E1E1E'/%3E%3Cpath d='M9.22727 4V14M4 8.77273H14' stroke='white' stroke-width='1.5'/%3E%3C/svg%3E%0A"})})})]})},{image:(0,y.jsx)(St,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,_.__)("Learn more")}),(0,y.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,c.createInterpolateElement)((0,_.__)("New to the block editor? Want to learn more about using it? <a>Here's a detailed guide.</a>"),{a:(0,y.jsx)(k.ExternalLink,{href:(0,_.__)("https://wordpress.org/documentation/article/wordpress-block-editor/")})})})]})}]})}function Et(){const{toggleFeature:e}=(0,l.useDispatch)(Je);return(0,y.jsx)(k.Guide,{className:"edit-template-welcome-guide",contentLabel:(0,_.__)("Welcome to the template editor"),finishButtonText:(0,_.__)("Get started"),onFinish:()=>e("welcomeGuideTemplate"),pages:[{image:(0,y.jsx)(St,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-template-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-template-editor.gif"}),content:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("h1",{className:"edit-post-welcome-guide__heading",children:(0,_.__)("Welcome to the template editor")}),(0,y.jsx)("p",{className:"edit-post-welcome-guide__text",children:(0,_.__)("Templates help define the layout of the site. You can customize all aspects of your posts and pages using blocks and patterns in this editor.")})]})}]})}function Mt({postType:e}){const{isActive:t,isEditingTemplate:o}=(0,l.useSelect)((t=>{const{isFeatureActive:o}=t(Je),s="wp_template"===e;return{isActive:o(s?"welcomeGuideTemplate":"welcomeGuide"),isEditingTemplate:s}}),[e]);return t?o?(0,y.jsx)(Et,{}):(0,y.jsx)(Pt,{}):null}const jt=(0,y.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,y.jsx)(f.Path,{d:"M6 4a2 2 0 0 0-2 2v3h1.5V6a.5.5 0 0 1 .5-.5h3V4H6Zm3 14.5H6a.5.5 0 0 1-.5-.5v-3H4v3a2 2 0 0 0 2 2h3v-1.5Zm6 1.5v-1.5h3a.5.5 0 0 0 .5-.5v-3H20v3a2 2 0 0 1-2 2h-3Zm3-16a2 2 0 0 1 2 2v3h-1.5V6a.5.5 0 0 0-.5-.5h-3V4h3Z"})});const{getLayoutStyles:kt}=C(h.privateApis),{useCommands:Tt}=C(P.privateApis),{useCommandContext:Bt}=C(S.privateApis),{Editor:It,FullscreenMode:At,NavigableRegion:Rt}=C(u.privateApis),{BlockKeyboardShortcuts:Ct}=C(r.privateApis),Dt=["wp_template","wp_template_part","wp_block","wp_navigation"];function Ot({isLegacy:e}){const[t,o,s]=(0,l.useSelect)((e=>{const{get:t}=e(d.store),{isMetaBoxLocationVisible:o}=e(Je);return[t("core/edit-post","metaBoxesMainIsOpen"),t("core/edit-post","metaBoxesMainOpenHeight"),o("normal")||o("advanced")||o("side")]}),[]),{set:i}=(0,l.useDispatch)(d.store),r=(0,c.useRef)(),n=(0,T.useMediaQuery)("(max-height: 549px)"),[{min:a,max:p},u]=(0,c.useState)((()=>({}))),g=(0,T.useRefEffect)((e=>{const t=e.closest(".interface-interface-skeleton__content"),o=t.querySelectorAll(":scope > .components-notice-list"),s=t.querySelector(".edit-post-meta-boxes-main__presenter"),i=new window.ResizeObserver((()=>{let e=t.offsetHeight;for(const t of o)e-=t.offsetHeight;const i=s.offsetHeight;u({min:i,max:e})}));i.observe(t);for(const e of o)i.observe(e);return()=>i.disconnect()}),[]),h=(0,c.useRef)(),w=(0,c.useId)(),[f,v]=(0,c.useState)(!0),S=(e,t,o)=>{const s=Math.min(p,Math.max(a,e));t?i("core/edit-post","metaBoxesMainOpenHeight",s):h.current.ariaValueNow=j(s),o&&r.current.updateSize({height:s,width:"auto"})};if(!s)return;const P=(0,y.jsxs)("div",{className:m("edit-post-layout__metaboxes",!e&&"edit-post-meta-boxes-main__liner"),hidden:!e&&n&&!t,children:[(0,y.jsx)(nt,{location:"normal"}),(0,y.jsx)(nt,{location:"advanced"})]});if(e)return P;const E=void 0===o;let M="50%";void 0!==p&&(M=E&&f?p/2:p);const j=e=>Math.round((e-a)/(p-a)*100),B=void 0===p||E?50:j(o),I=e=>{const t={ArrowUp:20,ArrowDown:-20}[e.key];if(t){const s=r.current.resizable,i=E?s.offsetHeight:o;S(t+i,!0,!0),e.preventDefault()}},A="edit-post-meta-boxes-main",R=(0,_.__)("Meta Boxes");let C,D;return n?(C=Rt,D={className:m(A,"is-toggle-only")}):(C=k.ResizableBox,D={as:Rt,ref:r,className:m(A,"is-resizable"),defaultSize:{height:o},minHeight:a,maxHeight:M,enable:{top:!0,right:!1,bottom:!1,left:!1,topLeft:!1,topRight:!1,bottomRight:!1,bottomLeft:!1},handleClasses:{top:"edit-post-meta-boxes-main__presenter"},handleComponent:{top:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(k.Tooltip,{text:(0,_.__)("Drag to resize"),children:(0,y.jsx)("button",{ref:h,role:"separator","aria-valuenow":B,"aria-label":(0,_.__)("Drag to resize"),"aria-describedby":w,onKeyDown:I})}),(0,y.jsx)(k.VisuallyHidden,{id:w,children:(0,_.__)("Use up and down arrow keys to resize the meta box panel.")})]})},onPointerDown:({pointerId:e,target:t})=>{h.current.parentElement.contains(t)&&t.setPointerCapture(e)},onResizeStart:(e,t,o)=>{E&&(S(o.offsetHeight,!1,!0),v(!1))},onResize:()=>S(r.current.state.height),onResizeStop:()=>S(r.current.state.height,!0)}),(0,y.jsxs)(C,{"aria-label":R,...D,children:[n?(0,y.jsxs)("button",{"aria-expanded":t,className:"edit-post-meta-boxes-main__presenter",onClick:()=>i("core/edit-post","metaBoxesMainIsOpen",!t),children:[R,(0,y.jsx)(k.Icon,{icon:t?b:x})]}):(0,y.jsx)("meta",{ref:g}),P]})}const Nt=function({postId:e,postType:t,settings:o,initialEdits:s}){Tt(),function(){const{isFullscreen:e}=(0,l.useSelect)((e=>{const{get:t}=e(d.store);return{isFullscreen:t("core/edit-post","fullscreenMode")}}),[]),{toggle:t}=(0,l.useDispatch)(d.store),{createInfoNotice:o}=(0,l.useDispatch)(v.store);(0,S.useCommand)({name:"core/toggle-fullscreen-mode",label:e?(0,_.__)("Exit fullscreen"):(0,_.__)("Enter fullscreen"),icon:jt,callback:({close:s})=>{t("core/edit-post","fullscreenMode"),s(),o(e?(0,_.__)("Fullscreen off."):(0,_.__)("Fullscreen on."),{id:"core/edit-post/toggle-fullscreen-mode/notice",type:"snackbar",actions:[{label:(0,_.__)("Undo"),onClick:()=>{t("core/edit-post","fullscreenMode")}}]})}})}();const r=(0,l.useSelect)((e=>{const{getEditorSettings:t,getCurrentPostType:o,getDeviceType:s}=e(u.store);return"Desktop"!==s()||["wp_template","wp_block"].includes(o())||C(e(h.store)).isZoomOut()||e(i.store).getBlockTypes().every((e=>e.apiVersion>=3))}),[]),{createErrorNotice:n}=(0,l.useDispatch)(v.store),{currentPost:{postId:a,postType:p},onNavigateToEntityRecord:g,onNavigateToPreviousEntityRecord:f}=function(e,t,o){const[s,i]=(0,c.useReducer)(((e,{type:t,post:o,previousRenderingMode:s})=>"push"===t?[...e,{post:o,previousRenderingMode:s}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:r,previousRenderingMode:n}=s[s.length-1],{getRenderingMode:a}=(0,l.useSelect)(u.store),{setRenderingMode:d}=(0,l.useDispatch)(u.store),p=(0,c.useCallback)((e=>{i({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:a()}),d(o)}),[a,d,o]),g=(0,c.useCallback)((()=>{i({type:"pop"}),n&&d(n)}),[d,n]);return{currentPost:r,onNavigateToEntityRecord:p,onNavigateToPreviousEntityRecord:s.length>1?g:void 0}}(e,t,"post-only"),b="wp_template"===p,{mode:x,isFullscreenActive:P,hasResolvedMode:B,hasActiveMetaboxes:I,hasBlockSelected:A,showIconLabels:R,isDistractionFree:D,showMetaBoxes:O,isWelcomeGuideVisible:F,templateId:z,enablePaddingAppender:G,isDevicePreview:U}=(0,l.useSelect)((e=>{var t;const{get:s}=e(d.store),{isFeatureActive:i,hasMetaBoxes:r}=e(Je),{canUser:n,getPostType:c,getTemplateId:l}=C(e(j.store)),g=o.supportsTemplateMode,m=null!==(t=c(p)?.viewable)&&void 0!==t&&t,w=n("read",{kind:"postType",name:"wp_template"}),{getBlockSelectionStart:_,isZoomOut:y}=C(e(h.store)),{getEditorMode:x,getRenderingMode:v,getDefaultRenderingMode:S,getDeviceType:P}=C(e(u.store)),E="post-only"===v(),M=!Dt.includes(p),k="wp_block"===p&&!f,T=l(p,a),B=S(p);return{mode:x(),isFullscreenActive:i("fullscreenMode"),hasActiveMetaboxes:r(),hasResolvedMode:"template-locked"===B?!!T:void 0!==B,hasBlockSelected:!!_(),showIconLabels:s("core","showIconLabels"),isDistractionFree:s("core","distractionFree"),showMetaBoxes:M&&!y()||k,isWelcomeGuideVisible:i("welcomeGuide"),templateId:g&&m&&w&&!b?T:null,enablePaddingAppender:!y()&&E&&M,isDevicePreview:"Desktop"!==P()}}),[p,a,b,o.supportsTemplateMode,f]);(e=>{const t=(0,l.useSelect)((t=>e&&t(u.store).__unstableIsEditorReady()),[e]),{initializeMetaBoxes:o}=(0,l.useDispatch)(Je);(0,c.useEffect)((()=>{t&&o()}),[t,o])})(I&&B);const[V,H]=function(e){const t=(0,l.useRegistry)(),o=(0,T.useRefEffect)((e=>{function o(o){if(o.target!==e&&o.target!==e.parentElement)return;const s=e.lastElementChild;if(!s)return;const r=s.getBoundingClientRect();if(o.clientY<r.bottom)return;o.preventDefault();const n=t.select(h.store).getBlockOrder(""),a=n[n.length-1],c=t.select(h.store).getBlock(a),{selectBlock:l,insertDefaultBlock:d}=t.dispatch(h.store);c&&(0,i.isUnmodifiedDefaultBlock)(c)?l(a):d()}const{ownerDocument:s}=e;return s.addEventListener("mousedown",o),()=>{s.removeEventListener("mousedown",o)}}),[t]);return e?[o,':root :where(.editor-styles-wrapper)::after {content: ""; display: block; height: 40vh;}']:[]}(G);Bt(A?"block-selection-edit":"entity-edit");const q=(0,c.useMemo)((()=>({...o,onNavigateToEntityRecord:g,onNavigateToPreviousEntityRecord:f,defaultRenderingMode:"post-only"})),[o,g,f]),W=function(...e){const{hasThemeStyleSupport:t,editorSettings:o}=(0,l.useSelect)((e=>({hasThemeStyleSupport:e(Je).isFeatureActive("themeStyles"),editorSettings:e(u.store).getEditorSettings()})),[]),s=e.join("\n");return(0,c.useMemo)((()=>{var e,i,r,n;const a=null!==(e=o.styles?.filter((e=>e.__unstableType&&"theme"!==e.__unstableType)))&&void 0!==e?e:[],c=[...null!==(i=o?.defaultEditorStyles)&&void 0!==i?i:[],...a],l=t&&a.length!==(null!==(r=o.styles?.length)&&void 0!==r?r:0);o.disableLayoutStyles||l||c.push({css:kt({style:{},selector:"body",hasBlockGapSupport:!1,hasFallbackGapSupport:!0,fallbackGapValue:"0.5em"})});const d=l?null!==(n=o.styles)&&void 0!==n?n:[]:c;return s?[...d,{css:s}]:d}),[o.defaultEditorStyles,o.disableLayoutStyles,o.styles,t,s])}(H);R?document.body.classList.add("show-icon-labels"):document.body.classList.remove("show-icon-labels");const Q=(0,k.__unstableUseNavigateRegions)(),X=m("edit-post-layout","is-mode-"+x,{"has-metaboxes":I}),{createSuccessNotice:Z}=(0,l.useDispatch)(v.store),$=(0,c.useCallback)(((e,t)=>{switch(e){case"move-to-trash":document.location.href=(0,E.addQueryArgs)("edit.php",{trashed:1,post_type:t[0].type,ids:t[0].id});break;case"duplicate-post":{const e=t[0],o="string"==typeof e.title?e.title:e.title?.rendered;Z((0,_.sprintf)((0,_.__)('"%s" successfully created.'),(0,M.decodeEntities)(o)),{type:"snackbar",id:"duplicate-post-action",actions:[{label:(0,_.__)("Edit"),onClick:()=>{const t=e.id;document.location.href=(0,E.addQueryArgs)("post.php",{post:t,action:"edit"})}}]})}}}),[Z]),Y=(0,c.useMemo)((()=>({type:t,id:e})),[t,e]),K=(0,T.useViewportMatch)("medium")&&P?(0,y.jsx)(N,{initialPost:Y}):null;return(0,y.jsx)(k.SlotFillProvider,{children:(0,y.jsxs)(u.ErrorBoundary,{canCopyContent:!0,children:[(0,y.jsx)(S.CommandMenu,{}),(0,y.jsx)(Mt,{postType:p}),(0,y.jsx)("div",{className:Q.className,...Q,ref:Q.ref,children:(0,y.jsxs)(It,{settings:q,initialEdits:s,postType:p,postId:a,templateId:z,className:X,styles:W,forceIsDirty:I,contentRef:V,disableIframe:!r,autoFocus:!F,onActionPerformed:$,extraSidebarPanels:O&&(0,y.jsx)(nt,{location:"side"}),extraContent:!D&&O&&(0,y.jsx)(Ot,{isLegacy:!r||U}),children:[(0,y.jsx)(u.PostLockedModal,{}),(0,y.jsx)(L,{}),(0,y.jsx)(At,{isActive:P}),(0,y.jsx)(st,{}),(0,y.jsx)(u.UnsavedChangesWarning,{}),(0,y.jsx)(u.AutosaveMonitor,{}),(0,y.jsx)(u.LocalAutosaveMonitor,{}),(0,y.jsx)(et,{}),(0,y.jsx)(u.EditorKeyboardShortcutsRegister,{}),(0,y.jsx)(Ct,{}),(0,y.jsx)(tt,{}),(0,y.jsx)(w.PluginArea,{onError:function(e){n((0,_.sprintf)((0,_.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}}),(0,y.jsx)(vt,{}),K,(0,y.jsx)(u.EditorSnackbars,{})]})})]})})},{PluginPostExcerpt:Lt}=C(u.privateApis),Ft=(0,E.getPath)(window.location.href)?.includes("site-editor.php"),zt=e=>{a()(`wp.editPost.${e}`,{since:"6.6",alternative:`wp.editor.${e}`})};function Gt(e){return Ft?null:(zt("PluginBlockSettingsMenuItem"),(0,y.jsx)(u.PluginBlockSettingsMenuItem,{...e}))}function Ut(e){return Ft?null:(zt("PluginDocumentSettingPanel"),(0,y.jsx)(u.PluginDocumentSettingPanel,{...e}))}function Vt(e){return Ft?null:(zt("PluginMoreMenuItem"),(0,y.jsx)(u.PluginMoreMenuItem,{...e}))}function Ht(e){return Ft?null:(zt("PluginPrePublishPanel"),(0,y.jsx)(u.PluginPrePublishPanel,{...e}))}function qt(e){return Ft?null:(zt("PluginPostPublishPanel"),(0,y.jsx)(u.PluginPostPublishPanel,{...e}))}function Wt(e){return Ft?null:(zt("PluginPostStatusInfo"),(0,y.jsx)(u.PluginPostStatusInfo,{...e}))}function Qt(e){return Ft?null:(zt("PluginSidebar"),(0,y.jsx)(u.PluginSidebar,{...e}))}function Xt(e){return Ft?null:(zt("PluginSidebarMoreMenuItem"),(0,y.jsx)(u.PluginSidebarMoreMenuItem,{...e}))}function Zt(){return Ft?null:(a()("wp.editPost.__experimentalPluginPostExcerpt",{since:"6.6",hint:"Core and custom panels can be access programmatically using their panel name.",link:"https://developer.wordpress.org/block-editor/reference-guides/slotfills/plugin-document-setting-panel/#accessing-a-panel-programmatically"}),Lt)}const{BackButton:$t,registerCoreBlockBindingsSources:Yt}=C(u.privateApis);function Kt(e,t,o,s,n){const a=window.matchMedia("(min-width: 782px)").matches,g=document.getElementById(e),m=(0,c.createRoot)(g);(0,l.dispatch)(d.store).setDefaults("core/edit-post",{fullscreenMode:!0,themeStyles:!0,welcomeGuide:!0,welcomeGuideTemplate:!0}),(0,l.dispatch)(d.store).setDefaults("core",{allowRightClickOverrides:!0,editorMode:"visual",editorTool:"edit",fixedToolbar:!1,hiddenBlockTypes:[],inactivePanels:[],openPanels:["post-status"],showBlockBreadcrumbs:!0,showIconLabels:!1,showListViewByDefault:!1,enableChoosePatternModal:!0,isPublishSidebarEnabled:!0}),window.__experimentalMediaProcessing&&(0,l.dispatch)(d.store).setDefaults("core/media",{requireApproval:!0,optimizeOnUpload:!0}),(0,l.dispatch)(i.store).reapplyBlockTypeFilters(),a&&(0,l.select)(d.store).get("core","showListViewByDefault")&&!(0,l.select)(d.store).get("core","distractionFree")&&(0,l.dispatch)(u.store).setIsListViewOpened(!0),(0,r.registerCoreBlocks)(),Yt(),(0,p.registerLegacyWidgetBlock)({inserter:!1}),(0,p.registerWidgetGroupBlock)({inserter:!1});"Standards"!==("CSS1Compat"===document.compatMode?"Standards":"Quirks")&&console.warn("Your browser is using Quirks Mode. \nThis can cause rendering issues such as blocks overlaying meta boxes in the editor. Quirks Mode can be triggered by PHP errors or HTML code appearing before the opening <!DOCTYPE html>. Try checking the raw page source or your site's PHP error log and resolving errors there, removing any HTML before the doctype, or disabling plugins.");return-1!==window.navigator.userAgent.indexOf("iPhone")&&window.addEventListener("scroll",(e=>{const t=document.getElementsByClassName("interface-interface-skeleton__body")[0];e.target===document&&(window.scrollY>100&&(t.scrollTop=t.scrollTop+window.scrollY),document.getElementsByClassName("is-mode-visual")[0]&&window.scrollTo(0,0))})),window.addEventListener("dragover",(e=>e.preventDefault()),!1),window.addEventListener("drop",(e=>e.preventDefault()),!1),m.render((0,y.jsx)(c.StrictMode,{children:(0,y.jsx)(Nt,{settings:s,postId:o,postType:t,initialEdits:n})})),m}function Jt(){a()("wp.editPost.reinitializeEditor",{since:"6.2",version:"6.3"})}(window.wp=window.wp||{}).editPost=t})();
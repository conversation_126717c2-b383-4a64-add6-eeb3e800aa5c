<?php
/**
 * Pricing Section Template Part
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$user_id = get_current_user_id();
$current_credits = 0;
$user_tier = 'free';

if ($user_id) {
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
    $current_credits = $current_credits ? intval($current_credits) : 0;
    $user_tier = get_user_meta($user_id, 'businesscraft_ai_tier', true) ?: 'free';
}

// Get localized pricing
$localized_packages = businesscraft_ai_get_localized_package_pricing();
$user_currency = businesscraft_ai_get_user_currency();
?>

<section class="pricing-section" id="pricing">
    <div class="container">
        <div class="pricing-header">
            <h2 class="section-title"><?php _e('Choose Your Plan', 'businesscraft-ai'); ?></h2>
            <p class="section-subtitle">
                <?php _e('Flexible credit-based pricing for African entrepreneurs. Pay only for what you use.', 'businesscraft-ai'); ?>
            </p>
        </div>

        <div class="pricing-grid">
            <!-- Free Plan -->
            <div class="pricing-card free-plan <?php echo ($user_tier === 'free') ? 'current-plan' : ''; ?>">
                <div class="plan-header">
                    <h3 class="plan-name"><?php _e('Free', 'businesscraft-ai'); ?></h3>
                    <div class="plan-price">
                        <span class="currency">$</span>
                        <span class="amount">0</span>
                        <span class="period"><?php _e('/month', 'businesscraft-ai'); ?></span>
                    </div>
                    <p class="plan-description">
                        <?php _e('Perfect for getting started with AI business tools', 'businesscraft-ai'); ?>
                    </p>
                </div>

                <div class="plan-features">
                    <ul>
                        <li><span class="feature-icon">✓</span> <?php _e('50 AI Credits per month', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('GPT-3.5 Turbo access', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Basic business templates', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Multi-language support', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Email support', 'businesscraft-ai'); ?></li>
                    </ul>
                </div>

                <div class="plan-action">
                    <?php if ($user_id): ?>
                        <button class="plan-button current" disabled>
                            <?php _e('Current Plan', 'businesscraft-ai'); ?>
                        </button>
                    <?php else: ?>
                        <a href="<?php echo wp_registration_url(); ?>" class="plan-button primary">
                            <?php _e('Get Started Free', 'businesscraft-ai'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Starter Plan -->
            <div class="pricing-card starter-plan">
                <div class="plan-header">
                    <h3 class="plan-name"><?php _e('Starter', 'businesscraft-ai'); ?></h3>
                    <div class="plan-price">
                        <span class="currency"><?php echo esc_html($user_currency['symbol']); ?></span>
                        <span class="amount"><?php echo number_format($localized_packages['starter']['price_local'], 0); ?></span>
                        <span class="period"><?php _e('/pack', 'businesscraft-ai'); ?></span>
                    </div>
                    <?php if ($user_currency['currency'] !== 'USD'): ?>
                        <div class="price-note">
                            <small><?php printf(__('≈ $%.2f USD', 'businesscraft-ai'), $localized_packages['starter']['price_usd']); ?></small>
                        </div>
                    <?php endif; ?>
                    <p class="plan-description">
                        <?php _e('Great for small businesses and entrepreneurs', 'businesscraft-ai'); ?>
                    </p>
                </div>

                <div class="plan-features">
                    <ul>
                        <li><span class="feature-icon">✓</span> <?php _e('500 AI Credits', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('GPT-3.5 Turbo access', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('All business templates', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Multi-language support', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Priority email support', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('African market insights', 'businesscraft-ai'); ?></li>
                    </ul>
                </div>

                <div class="plan-action">
                    <?php if ($user_id): ?>
                        <button class="plan-button primary pricing-button" data-package="starter">
                            <?php _e('Buy Starter Pack', 'businesscraft-ai'); ?>
                        </button>
                    <?php else: ?>
                        <a href="<?php echo wp_login_url(get_permalink()); ?>" class="plan-button primary">
                            <?php _e('Login to Purchase', 'businesscraft-ai'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Growth Plan -->
            <div class="pricing-card growth-plan popular <?php echo ($user_tier === 'ultra') ? 'current-plan' : ''; ?>">
                <div class="plan-badge"><?php _e('Most Popular', 'businesscraft-ai'); ?></div>
                <div class="plan-header">
                    <h3 class="plan-name"><?php _e('Growth', 'businesscraft-ai'); ?></h3>
                    <div class="plan-price">
                        <span class="currency"><?php echo esc_html($user_currency['symbol']); ?></span>
                        <span class="amount"><?php echo number_format($localized_packages['growth']['price_local'], 0); ?></span>
                        <span class="period"><?php _e('/pack', 'businesscraft-ai'); ?></span>
                    </div>
                    <?php if ($user_currency['currency'] !== 'USD'): ?>
                        <div class="price-note">
                            <small><?php printf(__('≈ $%.2f USD', 'businesscraft-ai'), $localized_packages['growth']['price_usd']); ?></small>
                        </div>
                    <?php endif; ?>
                    <p class="plan-description">
                        <?php _e('Perfect for growing businesses with advanced AI needs', 'businesscraft-ai'); ?>
                    </p>
                </div>

                <div class="plan-features">
                    <ul>
                        <li><span class="feature-icon">✓</span> <?php _e('1,500 AI Credits', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">⭐</span> <?php _e('GPT-4 Turbo access', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Premium business templates', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Advanced market analytics', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Priority support', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Custom business insights', 'businesscraft-ai'); ?></li>
                    </ul>
                </div>

                <div class="plan-action">
                    <?php if ($user_id): ?>
                        <button class="plan-button primary pricing-button" data-package="growth">
                            <?php _e('Upgrade to Growth', 'businesscraft-ai'); ?>
                        </button>
                    <?php else: ?>
                        <a href="<?php echo wp_login_url(get_permalink()); ?>" class="plan-button primary">
                            <?php _e('Login to Purchase', 'businesscraft-ai'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Business Plan -->
            <div class="pricing-card business-plan <?php echo ($user_tier === 'ultra') ? 'current-plan' : ''; ?>">
                <div class="plan-header">
                    <h3 class="plan-name"><?php _e('Business', 'businesscraft-ai'); ?></h3>
                    <div class="plan-price">
                        <span class="currency"><?php echo esc_html($user_currency['symbol']); ?></span>
                        <span class="amount"><?php echo number_format($localized_packages['business']['price_local'], 0); ?></span>
                        <span class="period"><?php _e('/pack', 'businesscraft-ai'); ?></span>
                    </div>
                    <?php if ($user_currency['currency'] !== 'USD'): ?>
                        <div class="price-note">
                            <small><?php printf(__('≈ $%.2f USD', 'businesscraft-ai'), $localized_packages['business']['price_usd']); ?></small>
                        </div>
                    <?php endif; ?>
                    <p class="plan-description">
                        <?php _e('For established businesses and enterprises', 'businesscraft-ai'); ?>
                    </p>
                </div>

                <div class="plan-features">
                    <ul>
                        <li><span class="feature-icon">✓</span> <?php _e('3,000 AI Credits', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">⭐</span> <?php _e('GPT-4 Turbo access', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('All premium features', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Custom integrations', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('Dedicated support', 'businesscraft-ai'); ?></li>
                        <li><span class="feature-icon">✓</span> <?php _e('API access', 'businesscraft-ai'); ?></li>
                    </ul>
                </div>

                <div class="plan-action">
                    <?php if ($user_id): ?>
                        <button class="plan-button primary pricing-button" data-package="business">
                            <?php _e('Get Business Pack', 'businesscraft-ai'); ?>
                        </button>
                    <?php else: ?>
                        <a href="<?php echo wp_login_url(get_permalink()); ?>" class="plan-button primary">
                            <?php _e('Login to Purchase', 'businesscraft-ai'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($user_id): ?>
            <div class="current-status">
                <div class="status-card">
                    <h4><?php _e('Your Current Status', 'businesscraft-ai'); ?></h4>
                    <p>
                        <?php printf(__('You have %d credits remaining', 'businesscraft-ai'), $current_credits); ?>
                        <?php if ($user_tier === 'ultra'): ?>
                            <span class="tier-badge ultra"><?php _e('Ultra Tier', 'businesscraft-ai'); ?></span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        <?php endif; ?>

        <div class="pricing-faq">
            <h3><?php _e('Frequently Asked Questions', 'businesscraft-ai'); ?></h3>
            <div class="faq-grid">
                <div class="faq-item">
                    <h4><?php _e('How do credits work?', 'businesscraft-ai'); ?></h4>
                    <p><?php _e('Each AI interaction uses credits based on the complexity and length of the response. Simple queries use 1-2 credits, while complex business plans may use 5-10 credits.', 'businesscraft-ai'); ?></p>
                </div>
                <div class="faq-item">
                    <h4><?php _e('Do credits expire?', 'businesscraft-ai'); ?></h4>
                    <p><?php _e('No, purchased credits never expire. You can use them at your own pace.', 'businesscraft-ai'); ?></p>
                </div>
                <div class="faq-item">
                    <h4><?php _e('What payment methods do you accept?', 'businesscraft-ai'); ?></h4>
                    <p><?php _e('We accept all major credit cards, mobile money (M-Pesa, MTN Mobile Money, Airtel Money), and bank transfers through Paystack.', 'businesscraft-ai'); ?></p>
                </div>
                <div class="faq-item">
                    <h4><?php _e('Can I upgrade or downgrade anytime?', 'businesscraft-ai'); ?></h4>
                    <p><?php _e('Yes, you can purchase additional credit packs anytime. Ultra tier access is granted with Growth and Business packs.', 'businesscraft-ai'); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
/**
 * ChatGABI Token/Credit Tracking System
 * 
 * Provides real-time token estimation, credit tracking,
 * and transparent usage feedback for users.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize token/credit tracking system
 */
function chatgabi_init_token_tracker() {
    // Create token tracking table if needed
    chatgabi_create_token_tracking_table();
}

/**
 * Create token tracking table
 */
function chatgabi_create_token_tracking_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_token_usage';

    // Use transient cache to avoid repeated table existence checks
    $cache_key = 'chatgabi_token_table_exists';
    $table_exists = get_transient($cache_key);

    if ($table_exists === false) {
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        set_transient($cache_key, $table_exists ? 'yes' : 'no', HOUR_IN_SECONDS);
    }

    if ($table_exists === 'yes') {
        return true;
    }

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        session_id varchar(100) NOT NULL,
        conversation_id varchar(100),
        prompt_text longtext,
        response_text longtext,
        estimated_tokens int(11) NOT NULL DEFAULT 0,
        actual_tokens int(11) NOT NULL DEFAULT 0,
        credits_used decimal(10,2) NOT NULL DEFAULT 0.00,
        credits_before decimal(10,2) NOT NULL DEFAULT 0.00,
        credits_after decimal(10,2) NOT NULL DEFAULT 0.00,
        operation_type varchar(50) NOT NULL DEFAULT 'chat',
        language_code varchar(5) NOT NULL DEFAULT 'en',
        country_code varchar(5),
        sector varchar(100),
        processing_time decimal(8,3) NOT NULL DEFAULT 0.000,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY session_id (session_id),
        KEY conversation_id (conversation_id),
        KEY created_at (created_at),
        KEY operation_type (operation_type)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = dbDelta($sql);

    // Update cache
    set_transient($cache_key, 'yes', HOUR_IN_SECONDS);

    return !empty($result);
}

/**
 * Estimate tokens for a given text
 */
function chatgabi_estimate_tokens($text) {
    if (empty($text)) {
        return 0;
    }

    // Basic token estimation (approximately 4 characters per token for English)
    // More sophisticated for other languages
    $char_count = strlen($text);
    $word_count = str_word_count($text);
    
    // Improved estimation based on language patterns
    $estimated_tokens = max(
        ceil($char_count / 4),  // Character-based estimation
        ceil($word_count * 1.3) // Word-based estimation with padding
    );

    return $estimated_tokens;
}

/**
 * Calculate credit cost from tokens
 */
function chatgabi_calculate_credit_cost($tokens) {
    // Current pricing: 1 credit = ~1000 tokens (adjustable)
    $tokens_per_credit = apply_filters('chatgabi_tokens_per_credit', 1000);
    
    if ($tokens <= 0) {
        return 0;
    }

    $credits = $tokens / $tokens_per_credit;
    
    // Minimum charge of 0.01 credits for any operation
    return max(0.01, round($credits, 2));
}

/**
 * Get real-time token estimate for prompt
 */
function chatgabi_get_prompt_token_estimate($prompt, $context_data = array()) {
    $start_time = microtime(true);

    // Base prompt tokens
    $prompt_tokens = chatgabi_estimate_tokens($prompt);

    // Add context tokens if provided
    $context_tokens = 0;
    if (!empty($context_data)) {
        foreach ($context_data as $context) {
            if (is_string($context)) {
                $context_tokens += chatgabi_estimate_tokens($context);
            } elseif (is_array($context)) {
                $context_tokens += chatgabi_estimate_tokens(json_encode($context));
            }
        }
    }

    // Estimate response tokens (typically 2-3x prompt tokens)
    $estimated_response_tokens = ($prompt_tokens + $context_tokens) * 2.5;

    $total_tokens = $prompt_tokens + $context_tokens + $estimated_response_tokens;
    $estimated_credits = chatgabi_calculate_credit_cost($total_tokens);

    $processing_time = microtime(true) - $start_time;

    return array(
        'prompt_tokens' => $prompt_tokens,
        'context_tokens' => $context_tokens,
        'estimated_response_tokens' => (int) $estimated_response_tokens,
        'total_estimated_tokens' => (int) $total_tokens,
        'estimated_credits' => $estimated_credits,
        'processing_time' => round($processing_time, 3)
    );
}

/**
 * Track token usage for a conversation
 */
function chatgabi_track_token_usage($user_id, $session_id, $data) {
    global $wpdb;

    // Ensure table exists
    chatgabi_create_token_tracking_table();

    $table_name = $wpdb->prefix . 'chatgabi_token_usage';

    // Get current user credits
    $credits_before = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;

    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'session_id' => $session_id,
            'conversation_id' => $data['conversation_id'] ?? '',
            'prompt_text' => $data['prompt_text'] ?? '',
            'response_text' => $data['response_text'] ?? '',
            'estimated_tokens' => $data['estimated_tokens'] ?? 0,
            'actual_tokens' => $data['actual_tokens'] ?? 0,
            'credits_used' => $data['credits_used'] ?? 0,
            'credits_before' => $credits_before,
            'credits_after' => $credits_before - ($data['credits_used'] ?? 0),
            'operation_type' => $data['operation_type'] ?? 'chat',
            'language_code' => $data['language_code'] ?? 'en',
            'country_code' => $data['country_code'] ?? '',
            'sector' => $data['sector'] ?? '',
            'processing_time' => $data['processing_time'] ?? 0
        ),
        array('%d', '%s', '%s', '%s', '%s', '%d', '%d', '%f', '%f', '%f', '%s', '%s', '%s', '%s', '%f')
    );

    return $result ? $wpdb->insert_id : false;
}

/**
 * Get user's token usage statistics
 */
function chatgabi_get_user_token_stats($user_id, $days = 30) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_token_usage';
    $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));

    $stats = $wpdb->get_row($wpdb->prepare(
        "SELECT 
            COUNT(*) as total_conversations,
            SUM(actual_tokens) as total_tokens,
            SUM(credits_used) as total_credits,
            AVG(actual_tokens) as avg_tokens_per_conversation,
            AVG(credits_used) as avg_credits_per_conversation,
            AVG(processing_time) as avg_processing_time
         FROM {$table_name} 
         WHERE user_id = %d AND created_at >= %s",
        $user_id,
        $date_limit
    ), ARRAY_A);

    // Get daily breakdown
    $daily_usage = $wpdb->get_results($wpdb->prepare(
        "SELECT 
            DATE(created_at) as date,
            COUNT(*) as conversations,
            SUM(actual_tokens) as tokens,
            SUM(credits_used) as credits
         FROM {$table_name} 
         WHERE user_id = %d AND created_at >= %s
         GROUP BY DATE(created_at)
         ORDER BY date DESC",
        $user_id,
        $date_limit
    ), ARRAY_A);

    return array(
        'summary' => $stats,
        'daily_breakdown' => $daily_usage
    );
}

// Note: chatgabi_ajax_estimate_tokens() function is defined in ajax-handlers.php

/**
 * AJAX handler for getting user token statistics
 */
function chatgabi_ajax_get_token_stats() {
    // Start output buffering to prevent headers already sent errors
    if (!ob_get_level()) {
        ob_start();
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_token_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
    }

    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'You must be logged in'));
    }

    $user_id = get_current_user_id();
    $days = intval($_POST['days'] ?? 30);
    $days = max(1, min(365, $days)); // Limit between 1 and 365 days

    $stats = chatgabi_get_user_token_stats($user_id, $days);

    wp_send_json_success(array(
        'stats' => $stats,
        'period_days' => $days
    ));
}

/**
 * Get current user session ID
 */
function chatgabi_get_session_id() {
    if (!session_id()) {
        session_start();
    }
    
    return session_id();
}

/**
 * Enhanced credit deduction with tracking
 */
function chatgabi_deduct_credits_with_tracking($user_id, $credits, $token_data = array()) {
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    
    if ($current_credits < $credits) {
        return false; // Insufficient credits
    }

    // Deduct credits
    $new_credits = $current_credits - $credits;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);

    // Track the usage
    $session_id = chatgabi_get_session_id();
    $tracking_data = array_merge($token_data, array(
        'credits_used' => $credits,
        'credits_before' => $current_credits,
        'credits_after' => $new_credits
    ));

    chatgabi_track_token_usage($user_id, $session_id, $tracking_data);

    return true;
}

/**
 * AJAX handler for getting current user credits
 */
function chatgabi_ajax_get_user_credits() {
    // Start output buffering to prevent headers already sent errors
    if (!ob_get_level()) {
        ob_start();
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_credit_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
    }

    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'You must be logged in'));
    }

    $user_id = get_current_user_id();
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;

    wp_send_json_success(array(
        'credits' => (float) $current_credits,
        'formatted_credits' => number_format($current_credits, 2)
    ));
}

// Hook AJAX handlers (chatgabi_estimate_tokens is in ajax-handlers.php)
add_action('wp_ajax_chatgabi_get_token_stats', 'chatgabi_ajax_get_token_stats');
add_action('wp_ajax_chatgabi_get_user_credits', 'chatgabi_ajax_get_user_credits');

// Initialize token tracking system
add_action('init', 'chatgabi_init_token_tracker');

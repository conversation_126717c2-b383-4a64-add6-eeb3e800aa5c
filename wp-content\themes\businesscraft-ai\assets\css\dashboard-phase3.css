/**
 * Dashboard Phase 3 Styles
 * 
 * Styles for analytics dashboard, notifications, and template AI enhancement
 */

/* Phase 3: Analytics Dashboard Styles */
.analytics-container {
    padding: 20px 0;
}

.analytics-header {
    text-align: center;
    margin-bottom: 30px;
}

.analytics-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.analytics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.analytics-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
}

.analytics-card .card-icon {
    font-size: 2.5em;
    opacity: 0.9;
}

.analytics-card .card-content {
    flex: 1;
}

.analytics-card .card-value {
    font-size: 2.2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.analytics-card .card-label {
    font-size: 0.9em;
    opacity: 0.9;
    margin-bottom: 8px;
}

.analytics-card .card-trend {
    font-size: 0.85em;
    font-weight: 600;
}

.analytics-card .card-trend.positive {
    color: #90EE90;
}

.analytics-card .card-trend.negative {
    color: #FFB6C1;
}

.analytics-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    margin: 0;
    color: #2c3e50;
}

.chart-controls select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

.activity-timeline {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.activity-timeline h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 20px;
}

.timeline-content {
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-icon {
    font-size: 1.5em;
    width: 40px;
    text-align: center;
}

.timeline-content .timeline-description {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
}

.timeline-content .timeline-time {
    font-size: 0.85em;
    color: #7f8c8d;
}

.performance-insights {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.performance-insights h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 20px;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.insight-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.insight-card .insight-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.insight-card h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 0.9em;
}

.insight-card p {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    color: #667eea;
}

/* Phase 3: Notification Preferences Styles */
.notifications-container {
    padding: 20px 0;
}

.notifications-header {
    text-align: center;
    margin-bottom: 30px;
}

.notifications-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.notification-preferences {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.notification-preferences h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 25px;
}

.preference-categories {
    margin-bottom: 30px;
}

.preference-category {
    margin-bottom: 30px;
}

.preference-category h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

.preference-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.notification-toggle {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.notification-toggle:hover {
    background: #e9ecef;
}

.notification-checkbox {
    display: none;
}

.toggle-slider {
    width: 50px;
    height: 26px;
    background: #ccc;
    border-radius: 13px;
    position: relative;
    transition: background-color 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
}

.notification-checkbox:checked + .toggle-slider {
    background: #667eea;
}

.notification-checkbox:checked + .toggle-slider::before {
    transform: translateX(24px);
}

.toggle-content {
    flex: 1;
}

.toggle-title {
    font-weight: 600;
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.toggle-description {
    font-size: 0.9em;
    color: #7f8c8d;
}

.frequency-setting {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.frequency-setting label {
    font-weight: 500;
    color: #2c3e50;
    min-width: 150px;
}

.frequency-select,
.threshold-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

.input-suffix {
    font-size: 0.9em;
    color: #7f8c8d;
}

.notification-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.recent-notifications {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-notifications h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 20px;
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.notification-icon {
    font-size: 1.5em;
    width: 40px;
    text-align: center;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.notification-message {
    color: #495057;
    margin-bottom: 5px;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.85em;
    color: #7f8c8d;
}

.notification-message {
    padding: 10px;
    border-radius: 6px;
    margin-top: 15px;
    display: none;
}

.notification-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.notification-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Phase 3: Template AI Enhancement Styles */
.ai-enhancement-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
}

.enhancement-header h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.enhancement-header p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.enhancement-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.enhancement-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 15px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.enhancement-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.enhancement-btn .btn-icon {
    font-size: 1.2em;
}

/* Loading and Error States */
.loading-activity,
.loading-notifications,
.loading-spinner {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message,
.no-activity,
.no-notifications,
.no-suggestions {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
    font-style: italic;
}

/* Responsive Design for Phase 3 */
@media (max-width: 768px) {
    .analytics-summary {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .analytics-charts {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .insights-grid {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    .enhancement-tools {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .notification-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .frequency-setting {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .frequency-setting label {
        min-width: auto;
    }
}

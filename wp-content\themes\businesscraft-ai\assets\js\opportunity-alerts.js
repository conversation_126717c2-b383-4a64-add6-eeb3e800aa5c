/**
 * BusinessCraft AI - Opportunity Alerts Dashboard JavaScript
 * 
 * Handles user interactions for managing opportunity alert subscriptions
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize alerts dashboard
    const AlertsManager = {
        
        init: function() {
            this.bindEvents();
            this.loadStatistics();
        },
        
        bindEvents: function() {
            // Form submission
            $('#alertForm').on('submit', this.saveAlert);
            
            // Preview functionality
            $('#preview-btn').on('click', this.previewMatches);
            
            // Alert toggle
            $('.alert-toggle').on('change', this.toggleAlert);
            
            // Edit alert
            $('.edit-alert').on('click', this.editAlert);
            
            // Delete alert
            $('.delete-alert').on('click', this.deleteAlert);
            
            // Preview alert
            $('.preview-alert').on('click', this.previewAlert);
            
            // Modal reset
            $('#alertModal').on('hidden.bs.modal', this.resetForm);
        },
        
        saveAlert: function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const alertData = {};
            
            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                if (key.endsWith('[]')) {
                    const arrayKey = key.slice(0, -2);
                    if (!alertData[arrayKey]) {
                        alertData[arrayKey] = [];
                    }
                    alertData[arrayKey].push(value);
                } else {
                    alertData[key] = value;
                }
            }
            
            // Show loading state
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...').prop('disabled', true);
            
            $.ajax({
                url: window.chatgabiAlerts.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_save_alert_subscription',
                    filter_data: alertData,
                    nonce: window.chatgabiAlerts.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        AlertsManager.showMessage('success', response.data.message);
                        
                        // Close modal
                        $('#alertModal').modal('hide');
                        
                        // Reload page to show updated alerts
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        AlertsManager.showMessage('error', response.data);
                    }
                },
                error: function() {
                    AlertsManager.showMessage('error', 'An error occurred while saving the alert.');
                },
                complete: function() {
                    submitBtn.html(originalText).prop('disabled', false);
                }
            });
        },
        
        previewMatches: function(e) {
            e.preventDefault();
            
            const formData = new FormData(document.getElementById('alertForm'));
            const alertData = {};
            
            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                if (key.endsWith('[]')) {
                    const arrayKey = key.slice(0, -2);
                    if (!alertData[arrayKey]) {
                        alertData[arrayKey] = [];
                    }
                    alertData[arrayKey].push(value);
                } else {
                    alertData[key] = value;
                }
            }
            
            // Show loading state
            const previewBtn = $('#preview-btn');
            const originalText = previewBtn.html();
            previewBtn.html('<i class="fas fa-spinner fa-spin"></i> Loading...').prop('disabled', true);
            
            $.ajax({
                url: window.chatgabiAlerts.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_preview_alert_matches',
                    filter_data: alertData,
                    nonce: window.chatgabiAlerts.nonce
                },
                success: function(response) {
                    if (response.success) {
                        AlertsManager.displayPreview(response.data);
                    } else {
                        AlertsManager.showMessage('error', response.data);
                    }
                },
                error: function() {
                    AlertsManager.showMessage('error', 'An error occurred while loading preview.');
                },
                complete: function() {
                    previewBtn.html(originalText).prop('disabled', false);
                }
            });
        },
        
        displayPreview: function(data) {
            const previewSection = $('#preview-section');
            const previewResults = $('#preview-results');
            
            let html = `<div class="mb-3"><strong>Total Matches: ${data.total_matches}</strong></div>`;
            
            if (data.preview_opportunities.length > 0) {
                html += '<div class="row">';
                data.preview_opportunities.forEach(function(opportunity) {
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">${opportunity.title}</h6>
                                    <p class="card-text small">${opportunity.summary.substring(0, 100)}...</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">${opportunity.country} | ${opportunity.sector}</small>
                                        <span class="badge bg-primary">${opportunity.type}</span>
                                    </div>
                                    ${opportunity.match_score ? `<div class="mt-2"><small class="text-success">Match: ${Math.round(opportunity.match_score)}%</small></div>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
            } else {
                html += '<div class="text-center text-muted py-3">No opportunities match your current criteria.</div>';
            }
            
            previewResults.html(html);
            previewSection.show();
        },
        
        toggleAlert: function() {
            const alertId = $(this).data('alert-id');
            const isActive = $(this).is(':checked') ? 1 : 0;
            const toggle = $(this);
            
            $.ajax({
                url: window.chatgabiAlerts.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_toggle_alert_subscription',
                    alert_id: alertId,
                    is_active: isActive,
                    nonce: window.chatgabiAlerts.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update label
                        const label = toggle.next('label');
                        label.text(isActive ? 'Active' : 'Inactive');
                        
                        AlertsManager.showMessage('success', response.data);
                    } else {
                        // Revert toggle
                        toggle.prop('checked', !isActive);
                        AlertsManager.showMessage('error', response.data);
                    }
                },
                error: function() {
                    // Revert toggle
                    toggle.prop('checked', !isActive);
                    AlertsManager.showMessage('error', 'An error occurred while updating the alert.');
                }
            });
        },
        
        editAlert: function() {
            const alertId = $(this).data('alert-id');
            const alert = window.chatgabiAlerts.userAlerts.find(a => a.id == alertId);
            
            if (!alert) {
                AlertsManager.showMessage('error', 'Alert not found.');
                return;
            }
            
            // Populate form with alert data
            $('#alert_id').val(alert.id);
            $('#filter_name').val(alert.filter_name);
            $('#amount_min').val(alert.amount_min || '');
            $('#amount_max').val(alert.amount_max || '');
            $('#keywords').val(alert.keywords || '');
            $('#deadline_days').val(alert.deadline_days || '');
            $('#notification_frequency').val(alert.notification_frequency);
            
            // Check countries
            $('input[name="countries[]"]').prop('checked', false);
            if (alert.countries) {
                alert.countries.forEach(function(country) {
                    $(`input[name="countries[]"][value="${country}"]`).prop('checked', true);
                });
            }
            
            // Check opportunity types
            $('input[name="opportunity_types[]"]').prop('checked', false);
            if (alert.opportunity_types) {
                alert.opportunity_types.forEach(function(type) {
                    $(`input[name="opportunity_types[]"][value="${type}"]`).prop('checked', true);
                });
            }
            
            // Check sectors
            $('input[name="sectors[]"]').prop('checked', false);
            if (alert.sectors) {
                alert.sectors.forEach(function(sector) {
                    $(`input[name="sectors[]"][value="${sector}"]`).prop('checked', true);
                });
            }
            
            // Update modal title
            $('#alertModalLabel').text('Edit Opportunity Alert');
        },
        
        deleteAlert: function() {
            const alertId = $(this).data('alert-id');
            const row = $(this).closest('tr');
            
            if (!confirm('Are you sure you want to delete this alert? This action cannot be undone.')) {
                return;
            }
            
            $.ajax({
                url: window.chatgabiAlerts.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_delete_alert_subscription',
                    alert_id: alertId,
                    nonce: window.chatgabiAlerts.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Remove row with animation
                        row.fadeOut(300, function() {
                            $(this).remove();
                            
                            // Check if table is empty
                            if ($('tbody tr').length === 0) {
                                location.reload();
                            }
                        });
                        
                        AlertsManager.showMessage('success', response.data);
                    } else {
                        AlertsManager.showMessage('error', response.data);
                    }
                },
                error: function() {
                    AlertsManager.showMessage('error', 'An error occurred while deleting the alert.');
                }
            });
        },
        
        previewAlert: function() {
            const alertId = $(this).data('alert-id');
            const alert = window.chatgabiAlerts.userAlerts.find(a => a.id == alertId);
            
            if (!alert) {
                AlertsManager.showMessage('error', 'Alert not found.');
                return;
            }
            
            // Create temporary form data for preview
            const alertData = {
                countries: alert.countries || [],
                opportunity_types: alert.opportunity_types || [],
                sectors: alert.sectors || [],
                amount_min: alert.amount_min,
                amount_max: alert.amount_max,
                keywords: alert.keywords,
                deadline_days: alert.deadline_days
            };
            
            $.ajax({
                url: window.chatgabiAlerts.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_preview_alert_matches',
                    filter_data: alertData,
                    nonce: window.chatgabiAlerts.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show preview in a modal or expand row
                        AlertsManager.showPreviewModal(alert.filter_name, response.data);
                    } else {
                        AlertsManager.showMessage('error', response.data);
                    }
                },
                error: function() {
                    AlertsManager.showMessage('error', 'An error occurred while loading preview.');
                }
            });
        },
        
        showPreviewModal: function(alertName, data) {
            let html = `
                <div class="modal fade" id="previewModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Preview: ${alertName}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3"><strong>Total Matches: ${data.total_matches}</strong></div>
            `;
            
            if (data.preview_opportunities.length > 0) {
                data.preview_opportunities.forEach(function(opportunity) {
                    html += `
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">${opportunity.title}</h6>
                                <p class="card-text">${opportunity.summary}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">${opportunity.country} | ${opportunity.sector}</small>
                                    <span class="badge bg-primary">${opportunity.type}</span>
                                </div>
                                ${opportunity.match_score ? `<div class="mt-2"><small class="text-success">Match Score: ${Math.round(opportunity.match_score)}%</small></div>` : ''}
                            </div>
                        </div>
                    `;
                });
            } else {
                html += '<div class="text-center text-muted py-3">No opportunities match this alert criteria.</div>';
            }
            
            html += `
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Remove existing preview modal
            $('#previewModal').remove();
            
            // Add and show new modal
            $('body').append(html);
            $('#previewModal').modal('show');
            
            // Clean up when modal is hidden
            $('#previewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        },
        
        resetForm: function() {
            $('#alertForm')[0].reset();
            $('#alert_id').val('');
            $('#preview-section').hide();
            $('#alertModalLabel').text('Create Opportunity Alert');
        },
        
        loadStatistics: function() {
            // Load alert statistics
            $.ajax({
                url: window.chatgabiAlerts.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_get_alert_statistics',
                    nonce: window.chatgabiAlerts.nonce
                },
                success: function(response) {
                    if (response.success) {
                        const stats = response.data;
                        $('#total-matches').text(stats.total_matches || 0);
                        $('#emails-sent').text(stats.emails_sent || 0);
                        $('#open-rate').text((stats.open_rate || 0) + '%');
                    }
                }
            });
        },
        
        showMessage: function(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // Remove existing alerts
            $('.alert').remove();
            
            // Add new alert at top of container
            $('.container').prepend(alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }
    };
    
    // Initialize the alerts manager
    AlertsManager.init();
});

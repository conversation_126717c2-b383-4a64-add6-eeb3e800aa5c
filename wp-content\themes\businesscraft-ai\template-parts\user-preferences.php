<?php
/**
 * User Preferences Template
 * 
 * Displays the user preferences dashboard with all customization options
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$user_id = get_current_user_id();
$user_preferences = chatgabi_get_user_preferences_with_defaults($user_id);
$available_languages = chatgabi_get_available_languages();
?>

<div class="chatgabi-preferences-container">
    <header class="preferences-header">
        <h1><?php _e('User Preferences', 'chatgabi'); ?></h1>
        <p class="preferences-description">
            <?php _e('Customize your ChatGABI experience with personalized settings for language, location, and display preferences.', 'chatgabi'); ?>
        </p>
    </header>

    <form id="chatgabi-preferences-form" class="preferences-form">
        <?php wp_nonce_field('chatgabi_preferences_nonce', 'preferences_nonce'); ?>
        
        <!-- Language & Location Preferences -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">🌍</span>
                <?php _e('Language & Location', 'chatgabi'); ?>
            </h2>
            
            <div class="preferences-grid">
                <div class="preference-item">
                    <label for="preferred_language" class="preference-label">
                        <?php _e('Preferred Language', 'chatgabi'); ?>
                    </label>
                    <select id="preferred_language" name="preferred_language" class="preference-select">
                        <?php foreach ($available_languages as $code => $language): ?>
                            <option value="<?php echo esc_attr($code); ?>" 
                                    <?php selected($user_preferences['preferred_language'], $code); ?>>
                                <?php echo esc_html($language['name']); ?> 
                                (<?php echo esc_html($language['native_name']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="preference-description">
                        <?php _e('Choose your preferred language for AI responses and interface.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="preferred_country" class="preference-label">
                        <?php _e('Primary Country', 'chatgabi'); ?>
                    </label>
                    <select id="preferred_country" name="preferred_country" class="preference-select">
                        <option value=""><?php _e('Select Country...', 'chatgabi'); ?></option>
                        <option value="GH" <?php selected($user_preferences['preferred_country'], 'GH'); ?>>
                            🇬🇭 <?php _e('Ghana', 'chatgabi'); ?>
                        </option>
                        <option value="KE" <?php selected($user_preferences['preferred_country'], 'KE'); ?>>
                            🇰🇪 <?php _e('Kenya', 'chatgabi'); ?>
                        </option>
                        <option value="NG" <?php selected($user_preferences['preferred_country'], 'NG'); ?>>
                            🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?>
                        </option>
                        <option value="ZA" <?php selected($user_preferences['preferred_country'], 'ZA'); ?>>
                            🇿🇦 <?php _e('South Africa', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Your primary business location for market-specific insights.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="preferred_sector" class="preference-label">
                        <?php _e('Primary Industry Sector', 'chatgabi'); ?>
                    </label>
                    <select id="preferred_sector" name="preferred_sector" class="preference-select">
                        <option value=""><?php _e('Select Sector...', 'chatgabi'); ?></option>
                        <!-- Sectors will be populated via JavaScript based on country selection -->
                    </select>
                    <p class="preference-description">
                        <?php _e('Your main industry focus for relevant business intelligence.', 'chatgabi'); ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Chat & Interface Preferences -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">💬</span>
                <?php _e('Chat & Interface', 'chatgabi'); ?>
            </h2>
            
            <div class="preferences-grid">
                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="show_chat_history" name="show_chat_history" 
                               <?php checked($user_preferences['show_chat_history']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Show Chat History', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Display previous conversations in the chat interface.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="chat_history_limit" class="preference-label">
                        <?php _e('Chat History Limit', 'chatgabi'); ?>
                    </label>
                    <select id="chat_history_limit" name="chat_history_limit" class="preference-select">
                        <option value="5" <?php selected($user_preferences['chat_history_limit'], 5); ?>>5 <?php _e('conversations', 'chatgabi'); ?></option>
                        <option value="10" <?php selected($user_preferences['chat_history_limit'], 10); ?>>10 <?php _e('conversations', 'chatgabi'); ?></option>
                        <option value="20" <?php selected($user_preferences['chat_history_limit'], 20); ?>>20 <?php _e('conversations', 'chatgabi'); ?></option>
                        <option value="50" <?php selected($user_preferences['chat_history_limit'], 50); ?>>50 <?php _e('conversations', 'chatgabi'); ?></option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Maximum number of conversations to display in history.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="show_example_prompts" name="show_example_prompts" 
                               <?php checked($user_preferences['show_example_prompts']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Show Example Prompts', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Display suggested prompts to help you get started.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="dashboard_layout" class="preference-label">
                        <?php _e('Dashboard Layout', 'chatgabi'); ?>
                    </label>
                    <select id="dashboard_layout" name="dashboard_layout" class="preference-select">
                        <option value="default" <?php selected($user_preferences['dashboard_layout'], 'default'); ?>>
                            <?php _e('Default Layout', 'chatgabi'); ?>
                        </option>
                        <option value="compact" <?php selected($user_preferences['dashboard_layout'], 'compact'); ?>>
                            <?php _e('Compact Layout', 'chatgabi'); ?>
                        </option>
                        <option value="expanded" <?php selected($user_preferences['dashboard_layout'], 'expanded'); ?>>
                            <?php _e('Expanded Layout', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Choose your preferred dashboard layout style.', 'chatgabi'); ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Template & Generation Preferences -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">📝</span>
                <?php _e('Templates & Generation', 'chatgabi'); ?>
            </h2>
            
            <div class="preferences-grid">
                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="auto_save_templates" name="auto_save_templates" 
                               <?php checked($user_preferences['auto_save_templates']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Auto-Save Generated Templates', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Automatically save generated business documents to your templates.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="currency_display" class="preference-label">
                        <?php _e('Currency Display', 'chatgabi'); ?>
                    </label>
                    <select id="currency_display" name="currency_display" class="preference-select">
                        <option value="auto" <?php selected($user_preferences['currency_display'], 'auto'); ?>>
                            <?php _e('Auto (Based on Country)', 'chatgabi'); ?>
                        </option>
                        <option value="GHS" <?php selected($user_preferences['currency_display'], 'GHS'); ?>>
                            🇬🇭 <?php _e('Ghana Cedis (GHS)', 'chatgabi'); ?>
                        </option>
                        <option value="KES" <?php selected($user_preferences['currency_display'], 'KES'); ?>>
                            🇰🇪 <?php _e('Kenyan Shilling (KES)', 'chatgabi'); ?>
                        </option>
                        <option value="NGN" <?php selected($user_preferences['currency_display'], 'NGN'); ?>>
                            🇳🇬 <?php _e('Nigerian Naira (NGN)', 'chatgabi'); ?>
                        </option>
                        <option value="ZAR" <?php selected($user_preferences['currency_display'], 'ZAR'); ?>>
                            🇿🇦 <?php _e('South African Rand (ZAR)', 'chatgabi'); ?>
                        </option>
                        <option value="USD" <?php selected($user_preferences['currency_display'], 'USD'); ?>>
                            🇺🇸 <?php _e('US Dollar (USD)', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Preferred currency for financial projections and pricing.', 'chatgabi'); ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Notification Preferences -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">🔔</span>
                <?php _e('Notifications', 'chatgabi'); ?>
            </h2>
            
            <div class="preferences-grid">
                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="email_notifications" name="email_notifications" 
                               <?php checked($user_preferences['email_notifications']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Email Notifications', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Receive email updates about your account and new features.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="opportunity_notifications" name="opportunity_notifications" 
                               <?php checked($user_preferences['opportunity_notifications']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Opportunity Alerts', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Get notified about new business opportunities in your sector.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="template_generation_notifications" name="template_generation_notifications" 
                               <?php checked($user_preferences['template_generation_notifications']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Template Generation Alerts', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Receive notifications when your templates are ready.', 'chatgabi'); ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- AI Behavior & Response Preferences -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">🤖</span>
                <?php _e('AI Behavior & Response Style', 'chatgabi'); ?>
            </h2>

            <div class="preferences-grid">
                <div class="preference-item">
                    <label for="ai_response_style" class="preference-label">
                        <?php _e('Response Style', 'chatgabi'); ?>
                    </label>
                    <select id="ai_response_style" name="ai_response_style" class="preference-select">
                        <option value="professional" <?php selected($user_preferences['ai_response_style'], 'professional'); ?>>
                            <?php _e('Professional & Formal', 'chatgabi'); ?>
                        </option>
                        <option value="conversational" <?php selected($user_preferences['ai_response_style'], 'conversational'); ?>>
                            <?php _e('Conversational & Friendly', 'chatgabi'); ?>
                        </option>
                        <option value="concise" <?php selected($user_preferences['ai_response_style'], 'concise'); ?>>
                            <?php _e('Concise & Direct', 'chatgabi'); ?>
                        </option>
                        <option value="detailed" <?php selected($user_preferences['ai_response_style'], 'detailed'); ?>>
                            <?php _e('Detailed & Comprehensive', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Choose how ChatGABI should communicate with you.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="response_length" class="preference-label">
                        <?php _e('Preferred Response Length', 'chatgabi'); ?>
                    </label>
                    <select id="response_length" name="response_length" class="preference-select">
                        <option value="short" <?php selected($user_preferences['response_length'], 'short'); ?>>
                            <?php _e('Short (1-2 paragraphs)', 'chatgabi'); ?>
                        </option>
                        <option value="medium" <?php selected($user_preferences['response_length'], 'medium'); ?>>
                            <?php _e('Medium (3-5 paragraphs)', 'chatgabi'); ?>
                        </option>
                        <option value="long" <?php selected($user_preferences['response_length'], 'long'); ?>>
                            <?php _e('Long (6+ paragraphs)', 'chatgabi'); ?>
                        </option>
                        <option value="adaptive" <?php selected($user_preferences['response_length'], 'adaptive'); ?>>
                            <?php _e('Adaptive (Based on query)', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Set your preferred length for AI responses.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="include_examples" name="include_examples"
                               <?php checked($user_preferences['include_examples']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Include Practical Examples', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Include real-world examples and case studies in responses.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="include_action_items" name="include_action_items"
                               <?php checked($user_preferences['include_action_items']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Include Action Items', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Add actionable next steps to business advice responses.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="cultural_context_level" class="preference-label">
                        <?php _e('Cultural Context Level', 'chatgabi'); ?>
                    </label>
                    <select id="cultural_context_level" name="cultural_context_level" class="preference-select">
                        <option value="minimal" <?php selected($user_preferences['cultural_context_level'], 'minimal'); ?>>
                            <?php _e('Minimal', 'chatgabi'); ?>
                        </option>
                        <option value="moderate" <?php selected($user_preferences['cultural_context_level'], 'moderate'); ?>>
                            <?php _e('Moderate', 'chatgabi'); ?>
                        </option>
                        <option value="extensive" <?php selected($user_preferences['cultural_context_level'], 'extensive'); ?>>
                            <?php _e('Extensive', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('How much African cultural context to include in responses.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="auto_include_opportunities" name="auto_include_opportunities"
                               <?php checked($user_preferences['auto_include_opportunities']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Auto-Include Relevant Opportunities', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Automatically include relevant business opportunities in responses.', 'chatgabi'); ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Usage Analytics & Insights -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">📊</span>
                <?php _e('Usage Analytics & Insights', 'chatgabi'); ?>
            </h2>

            <div class="analytics-dashboard">
                <?php
                $user_analytics = chatgabi_get_user_analytics($user_id);
                ?>

                <div class="analytics-grid">
                    <div class="analytics-card">
                        <div class="analytics-icon">💬</div>
                        <div class="analytics-content">
                            <h3><?php echo number_format($user_analytics['total_conversations']); ?></h3>
                            <p><?php _e('Total Conversations', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="analytics-icon">🎯</div>
                        <div class="analytics-content">
                            <h3><?php echo number_format($user_analytics['credits_used']); ?></h3>
                            <p><?php _e('Credits Used', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="analytics-icon">📝</div>
                        <div class="analytics-content">
                            <h3><?php echo number_format($user_analytics['templates_generated']); ?></h3>
                            <p><?php _e('Templates Generated', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <div class="analytics-icon">🚀</div>
                        <div class="analytics-content">
                            <h3><?php echo number_format($user_analytics['opportunities_viewed']); ?></h3>
                            <p><?php _e('Opportunities Viewed', 'chatgabi'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="analytics-details">
                    <div class="analytics-section">
                        <h4><?php _e('Most Used Features', 'chatgabi'); ?></h4>
                        <div class="feature-usage-list">
                            <?php foreach ($user_analytics['top_features'] as $feature => $count): ?>
                                <div class="feature-usage-item">
                                    <span class="feature-name"><?php echo esc_html($feature); ?></span>
                                    <span class="feature-count"><?php echo number_format($count); ?> <?php _e('times', 'chatgabi'); ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="analytics-section">
                        <h4><?php _e('Recent Activity', 'chatgabi'); ?></h4>
                        <div class="activity-timeline">
                            <?php foreach ($user_analytics['recent_activity'] as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon"><?php echo esc_html($activity['icon']); ?></div>
                                    <div class="activity-content">
                                        <p class="activity-text"><?php echo esc_html($activity['description']); ?></p>
                                        <span class="activity-time"><?php echo esc_html($activity['time_ago']); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="analytics-preferences">
                    <h4><?php _e('Analytics Preferences', 'chatgabi'); ?></h4>
                    <div class="preferences-grid">
                        <div class="preference-item">
                            <label class="preference-checkbox">
                                <input type="checkbox" id="track_usage_analytics" name="track_usage_analytics"
                                       <?php checked($user_preferences['track_usage_analytics']); ?>>
                                <span class="checkmark"></span>
                                <?php _e('Enable Usage Tracking', 'chatgabi'); ?>
                            </label>
                            <p class="preference-description">
                                <?php _e('Allow ChatGABI to track your usage for personalized insights.', 'chatgabi'); ?>
                            </p>
                        </div>

                        <div class="preference-item">
                            <label class="preference-checkbox">
                                <input type="checkbox" id="weekly_analytics_email" name="weekly_analytics_email"
                                       <?php checked($user_preferences['weekly_analytics_email']); ?>>
                                <span class="checkmark"></span>
                                <?php _e('Weekly Analytics Email', 'chatgabi'); ?>
                            </label>
                            <p class="preference-description">
                                <?php _e('Receive weekly summaries of your ChatGABI usage and insights.', 'chatgabi'); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Account Management & Security -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">🔐</span>
                <?php _e('Account Management & Security', 'chatgabi'); ?>
            </h2>

            <div class="account-management">
                <div class="preferences-grid">
                    <div class="preference-item">
                        <label for="data_retention_period" class="preference-label">
                            <?php _e('Data Retention Period', 'chatgabi'); ?>
                        </label>
                        <select id="data_retention_period" name="data_retention_period" class="preference-select">
                            <option value="30" <?php selected($user_preferences['data_retention_period'], 30); ?>>
                                <?php _e('30 days', 'chatgabi'); ?>
                            </option>
                            <option value="90" <?php selected($user_preferences['data_retention_period'], 90); ?>>
                                <?php _e('90 days', 'chatgabi'); ?>
                            </option>
                            <option value="180" <?php selected($user_preferences['data_retention_period'], 180); ?>>
                                <?php _e('6 months', 'chatgabi'); ?>
                            </option>
                            <option value="365" <?php selected($user_preferences['data_retention_period'], 365); ?>>
                                <?php _e('1 year', 'chatgabi'); ?>
                            </option>
                            <option value="forever" <?php selected($user_preferences['data_retention_period'], 'forever'); ?>>
                                <?php _e('Keep forever', 'chatgabi'); ?>
                            </option>
                        </select>
                        <p class="preference-description">
                            <?php _e('How long to keep your conversation history and generated content.', 'chatgabi'); ?>
                        </p>
                    </div>

                    <div class="preference-item">
                        <label class="preference-checkbox">
                            <input type="checkbox" id="two_factor_auth" name="two_factor_auth"
                                   <?php checked($user_preferences['two_factor_auth']); ?>>
                            <span class="checkmark"></span>
                            <?php _e('Enable Two-Factor Authentication', 'chatgabi'); ?>
                        </label>
                        <p class="preference-description">
                            <?php _e('Add an extra layer of security to your account.', 'chatgabi'); ?>
                        </p>
                    </div>

                    <div class="preference-item">
                        <label class="preference-checkbox">
                            <input type="checkbox" id="login_notifications" name="login_notifications"
                                   <?php checked($user_preferences['login_notifications']); ?>>
                            <span class="checkmark"></span>
                            <?php _e('Login Notifications', 'chatgabi'); ?>
                        </label>
                        <p class="preference-description">
                            <?php _e('Get notified when someone logs into your account.', 'chatgabi'); ?>
                        </p>
                    </div>

                    <div class="preference-item">
                        <label class="preference-checkbox">
                            <input type="checkbox" id="data_sharing_consent" name="data_sharing_consent"
                                   <?php checked($user_preferences['data_sharing_consent']); ?>>
                            <span class="checkmark"></span>
                            <?php _e('Allow Anonymous Data Sharing', 'chatgabi'); ?>
                        </label>
                        <p class="preference-description">
                            <?php _e('Help improve ChatGABI by sharing anonymized usage data.', 'chatgabi'); ?>
                        </p>
                    </div>
                </div>

                <div class="account-actions">
                    <h4><?php _e('Account Actions', 'chatgabi'); ?></h4>
                    <div class="action-buttons">
                        <button type="button" id="export-data-btn" class="action-btn export-btn">
                            <span class="btn-icon">📥</span>
                            <?php _e('Export My Data', 'chatgabi'); ?>
                        </button>

                        <button type="button" id="clear-history-btn" class="action-btn warning-btn">
                            <span class="btn-icon">🗑️</span>
                            <?php _e('Clear Chat History', 'chatgabi'); ?>
                        </button>

                        <button type="button" id="delete-account-btn" class="action-btn danger-btn">
                            <span class="btn-icon">⚠️</span>
                            <?php _e('Delete Account', 'chatgabi'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Import/Export -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">⚙️</span>
                <?php _e('Settings Import/Export', 'chatgabi'); ?>
            </h2>

            <div class="import-export-section">
                <div class="preferences-grid">
                    <div class="preference-item">
                        <h4><?php _e('Export Settings', 'chatgabi'); ?></h4>
                        <p class="preference-description">
                            <?php _e('Download your current preferences as a backup file.', 'chatgabi'); ?>
                        </p>
                        <button type="button" id="export-settings-btn" class="action-btn export-btn">
                            <span class="btn-icon">📤</span>
                            <?php _e('Export Settings', 'chatgabi'); ?>
                        </button>
                    </div>

                    <div class="preference-item">
                        <h4><?php _e('Import Settings', 'chatgabi'); ?></h4>
                        <p class="preference-description">
                            <?php _e('Upload a previously exported settings file to restore your preferences.', 'chatgabi'); ?>
                        </p>
                        <div class="file-upload-area">
                            <input type="file" id="import-settings-file" accept=".json" style="display: none;">
                            <button type="button" id="import-settings-btn" class="action-btn import-btn">
                                <span class="btn-icon">📥</span>
                                <?php _e('Import Settings', 'chatgabi'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="backup-restore-info">
                    <div class="info-card">
                        <h4><?php _e('Backup & Restore Information', 'chatgabi'); ?></h4>
                        <ul>
                            <li><?php _e('Settings files are encrypted and secure', 'chatgabi'); ?></li>
                            <li><?php _e('Exported files contain all your preferences and customizations', 'chatgabi'); ?></li>
                            <li><?php _e('Import will overwrite current settings - create a backup first', 'chatgabi'); ?></li>
                            <li><?php _e('Chat history and templates are not included in settings export', 'chatgabi'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Advanced Preferences -->
        <section class="preferences-section">
            <h2 class="section-title">
                <span class="section-icon">🔧</span>
                <?php _e('Advanced Preferences', 'chatgabi'); ?>
            </h2>

            <div class="preferences-grid">
                <div class="preference-item">
                    <label for="api_rate_limit" class="preference-label">
                        <?php _e('API Rate Limit', 'chatgabi'); ?>
                    </label>
                    <select id="api_rate_limit" name="api_rate_limit" class="preference-select">
                        <option value="conservative" <?php selected($user_preferences['api_rate_limit'], 'conservative'); ?>>
                            <?php _e('Conservative (Slower, more reliable)', 'chatgabi'); ?>
                        </option>
                        <option value="balanced" <?php selected($user_preferences['api_rate_limit'], 'balanced'); ?>>
                            <?php _e('Balanced (Default)', 'chatgabi'); ?>
                        </option>
                        <option value="aggressive" <?php selected($user_preferences['api_rate_limit'], 'aggressive'); ?>>
                            <?php _e('Aggressive (Faster, may hit limits)', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('Control how quickly ChatGABI makes API requests.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="debug_mode" name="debug_mode"
                               <?php checked($user_preferences['debug_mode']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Enable Debug Mode', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Show additional technical information for troubleshooting.', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label class="preference-checkbox">
                        <input type="checkbox" id="beta_features" name="beta_features"
                               <?php checked($user_preferences['beta_features']); ?>>
                        <span class="checkmark"></span>
                        <?php _e('Enable Beta Features', 'chatgabi'); ?>
                    </label>
                    <p class="preference-description">
                        <?php _e('Get early access to new ChatGABI features (may be unstable).', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="preference-item">
                    <label for="cache_duration" class="preference-label">
                        <?php _e('Cache Duration', 'chatgabi'); ?>
                    </label>
                    <select id="cache_duration" name="cache_duration" class="preference-select">
                        <option value="0" <?php selected($user_preferences['cache_duration'], 0); ?>>
                            <?php _e('No caching', 'chatgabi'); ?>
                        </option>
                        <option value="300" <?php selected($user_preferences['cache_duration'], 300); ?>>
                            <?php _e('5 minutes', 'chatgabi'); ?>
                        </option>
                        <option value="900" <?php selected($user_preferences['cache_duration'], 900); ?>>
                            <?php _e('15 minutes', 'chatgabi'); ?>
                        </option>
                        <option value="3600" <?php selected($user_preferences['cache_duration'], 3600); ?>>
                            <?php _e('1 hour', 'chatgabi'); ?>
                        </option>
                    </select>
                    <p class="preference-description">
                        <?php _e('How long to cache responses for similar queries.', 'chatgabi'); ?>
                    </p>
                </div>
            </div>
        </section>

        <!-- Save Button -->
        <div class="preferences-actions">
            <button type="submit" id="save-preferences-btn" class="save-preferences-btn">
                <span class="btn-text"><?php _e('Save Preferences', 'chatgabi'); ?></span>
                <span class="btn-loading" style="display: none;"><?php _e('Saving...', 'chatgabi'); ?></span>
            </button>
            
            <button type="button" id="reset-preferences-btn" class="reset-preferences-btn">
                <?php _e('Reset to Defaults', 'chatgabi'); ?>
            </button>
        </div>
    </form>

    <!-- Status Messages -->
    <div id="preferences-messages" class="preferences-messages"></div>
</div>

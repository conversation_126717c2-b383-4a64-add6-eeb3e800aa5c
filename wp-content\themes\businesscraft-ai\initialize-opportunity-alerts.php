<?php
/**
 * BusinessCraft AI - Opportunity Alerts Database Initialization
 * 
 * Creates database tables and sets up the opportunity alerts system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

echo '<h1>BusinessCraft AI - Opportunity Alerts System Initialization</h1>';
echo '<p>Setting up the comprehensive opportunity alerts system...</p>';

$initialization_steps = 0;
$completed_steps = 0;

// Step 1: Create Database Tables
echo '<h2>Step 1: Creating Database Tables</h2>';
$initialization_steps++;

try {
    $alerts_manager = chatgabi_get_opportunity_alerts();
    $alerts_manager->create_tables();
    
    // Verify tables were created
    global $wpdb;
    $required_tables = array(
        'chatgabi_opportunity_alerts' => 'User Alert Subscriptions',
        'chatgabi_alert_logs' => 'Email Delivery Logs',
        'chatgabi_alert_matches' => 'Opportunity Matches'
    );
    
    $tables_created = 0;
    foreach ($required_tables as $table_suffix => $description) {
        $table_name = $wpdb->prefix . $table_suffix;
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        
        if ($table_exists) {
            echo "✅ {$description} table created: {$table_name}<br>";
            $tables_created++;
        } else {
            echo "❌ Failed to create {$description} table: {$table_name}<br>";
        }
    }
    
    if ($tables_created === count($required_tables)) {
        echo "<p><strong>✅ All {$tables_created} database tables created successfully!</strong></p>";
        $completed_steps++;
    } else {
        echo "<p><strong>⚠️ Only {$tables_created} out of " . count($required_tables) . " tables were created.</strong></p>";
    }
    
} catch (Exception $e) {
    echo '<p><strong>❌ Database table creation failed: ' . $e->getMessage() . '</strong></p>';
}

echo '<hr>';

// Step 2: Schedule Cron Jobs
echo '<h2>Step 2: Scheduling Cron Jobs</h2>';
$initialization_steps++;

try {
    // Get the alerts manager instance
    $alerts_manager = chatgabi_get_opportunity_alerts();

    // Force reschedule all cron jobs using the improved method
    echo "🔄 Clearing existing cron jobs...<br>";
    wp_clear_scheduled_hook('chatgabi_process_opportunity_alerts');
    wp_clear_scheduled_hook('chatgabi_send_daily_alert_digest');
    wp_clear_scheduled_hook('chatgabi_send_weekly_alert_summary');
    wp_clear_scheduled_hook('chatgabi_cleanup_old_alert_logs');

    echo "📅 Registering custom cron schedules...<br>";
    // Trigger the custom schedules registration
    $schedules = wp_get_schedules();

    echo "⏰ Scheduling cron jobs...<br>";
    // Use the improved scheduling method
    $alerts_manager->schedule_cron_jobs();

    // Get status of all cron jobs
    $cron_status = $alerts_manager->get_cron_status();

    $cron_scheduled = 0;
    foreach ($cron_status as $hook => $status) {
        if ($status['scheduled']) {
            echo "✅ {$status['description']} scheduled ({$status['schedule']}) - Next run: {$status['next_run']}<br>";
            $cron_scheduled++;
        } else {
            echo "❌ Failed to schedule {$status['description']}<br>";

            // Debug information
            $available_schedules = wp_get_schedules();
            if ($hook === 'chatgabi_cleanup_old_alert_logs') {
                if (!isset($available_schedules['monthly'])) {
                    echo "   ⚠️ 'monthly' schedule not found. Available schedules: " . implode(', ', array_keys($available_schedules)) . "<br>";
                } else {
                    echo "   ✅ 'monthly' schedule is available<br>";
                }
            }
        }
    }

    if ($cron_scheduled === count($cron_status)) {
        echo "<p><strong>✅ All {$cron_scheduled} cron jobs scheduled successfully!</strong></p>";
        $completed_steps++;
    } else {
        echo "<p><strong>⚠️ Only {$cron_scheduled} out of " . count($cron_status) . " cron jobs were scheduled.</strong></p>";

        // Try to reschedule failed jobs
        echo "<p>🔧 Attempting to fix scheduling issues...</p>";
        $alerts_manager->force_reschedule_cron_jobs();

        // Check again
        $cron_status_retry = $alerts_manager->get_cron_status();
        $retry_scheduled = 0;
        foreach ($cron_status_retry as $hook => $status) {
            if ($status['scheduled']) {
                $retry_scheduled++;
            }
        }

        if ($retry_scheduled === count($cron_status_retry)) {
            echo "<p><strong>✅ All cron jobs successfully scheduled after retry!</strong></p>";
            $completed_steps++;
        } else {
            echo "<p><strong>❌ {$retry_scheduled} out of " . count($cron_status_retry) . " cron jobs scheduled after retry.</strong></p>";
        }
    }

} catch (Exception $e) {
    echo '<p><strong>❌ Cron job scheduling failed: ' . $e->getMessage() . '</strong></p>';
}

echo '<hr>';

// Step 3: Create Default Options
echo '<h2>Step 3: Setting Default Options</h2>';
$initialization_steps++;

try {
    $default_options = array(
        'chatgabi_sendpulse_user_id' => '',
        'chatgabi_sendpulse_secret' => '',
        'chatgabi_sendpulse_template_id' => '',
        'chatgabi_alerts_enabled' => 1,
        'chatgabi_max_alerts_per_user' => 10,
        'chatgabi_alert_rate_limit' => 100, // emails per day per user
    );
    
    $options_set = 0;
    foreach ($default_options as $option_name => $default_value) {
        if (add_option($option_name, $default_value)) {
            echo "✅ Option set: {$option_name}<br>";
            $options_set++;
        } else {
            // Option might already exist
            echo "ℹ️ Option already exists: {$option_name}<br>";
            $options_set++;
        }
    }
    
    echo "<p><strong>✅ {$options_set} default options configured!</strong></p>";
    $completed_steps++;
    
} catch (Exception $e) {
    echo '<p><strong>❌ Default options setup failed: ' . $e->getMessage() . '</strong></p>';
}

echo '<hr>';

// Step 4: Create Alert Dashboard Page
echo '<h2>Step 4: Creating Alert Dashboard Page</h2>';
$initialization_steps++;

try {
    // Check if alerts page already exists
    $existing_page = get_page_by_path('alerts');
    
    if (!$existing_page) {
        $page_data = array(
            'post_title' => 'Opportunity Alerts',
            'post_content' => '[opportunity_alerts_dashboard]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'alerts',
            'page_template' => 'page-alerts.php'
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            echo "✅ Alerts dashboard page created (ID: {$page_id})<br>";
            echo "📄 Page URL: " . get_permalink($page_id) . "<br>";
            $completed_steps++;
        } else {
            echo "❌ Failed to create alerts dashboard page<br>";
        }
    } else {
        echo "ℹ️ Alerts dashboard page already exists (ID: {$existing_page->ID})<br>";
        echo "📄 Page URL: " . get_permalink($existing_page->ID) . "<br>";
        $completed_steps++;
    }
    
} catch (Exception $e) {
    echo '<p><strong>❌ Page creation failed: ' . $e->getMessage() . '</strong></p>';
}

echo '<hr>';

// Step 5: Test System Components
echo '<h2>Step 5: Testing System Components</h2>';
$initialization_steps++;

try {
    $tests_passed = 0;
    $total_tests = 4;
    
    // Test 1: Check if opportunity loader is available
    if (function_exists('load_opportunities_by_country_sector')) {
        echo "✅ Opportunity loader function available<br>";
        $tests_passed++;
    } else {
        echo "❌ Opportunity loader function not found<br>";
    }
    
    // Test 2: Check if SendPulse integration is available
    if (function_exists('chatgabi_get_sendpulse')) {
        echo "✅ SendPulse integration available<br>";
        $tests_passed++;
    } else {
        echo "❌ SendPulse integration not found<br>";
    }
    
    // Test 3: Test opportunity data loading
    $test_opportunities = load_opportunities_by_country_sector('Ghana');
    if (!empty($test_opportunities)) {
        echo "✅ Opportunity data loading works (" . count($test_opportunities) . " opportunities found)<br>";
        $tests_passed++;
    } else {
        echo "⚠️ No opportunity data found for testing<br>";
    }
    
    // Test 4: Check WordPress user system
    if (function_exists('get_current_user_id') && function_exists('wp_get_current_user')) {
        echo "✅ WordPress user system available<br>";
        $tests_passed++;
    } else {
        echo "❌ WordPress user system not available<br>";
    }
    
    if ($tests_passed === $total_tests) {
        echo "<p><strong>✅ All {$tests_passed} system tests passed!</strong></p>";
        $completed_steps++;
    } else {
        echo "<p><strong>⚠️ {$tests_passed} out of {$total_tests} tests passed.</strong></p>";
    }
    
} catch (Exception $e) {
    echo '<p><strong>❌ System testing failed: ' . $e->getMessage() . '</strong></p>';
}

echo '<hr>';

// Final Summary
echo '<h2>🎯 Initialization Summary</h2>';
echo "<p><strong>Completed Steps: {$completed_steps} / {$initialization_steps}</strong></p>";

if ($completed_steps === $initialization_steps) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>🎉 Opportunity Alerts System Successfully Initialized!</h3>';
    echo '<p>The comprehensive opportunity alerts system is now ready for use. Users can:</p>';
    echo '<ul>';
    echo '<li>✅ Create and manage custom opportunity alert subscriptions</li>';
    echo '<li>✅ Receive immediate, daily, or weekly email notifications</li>';
    echo '<li>✅ Filter opportunities by country, type, sector, amount, and keywords</li>';
    echo '<li>✅ Preview matching opportunities before saving alerts</li>';
    echo '<li>✅ Track email delivery and engagement statistics</li>';
    echo '<li>✅ Unsubscribe from alerts with one-click links</li>';
    echo '</ul>';
    echo '</div>';
    
    echo '<h3>📋 Next Steps:</h3>';
    echo '<ol>';
    echo '<li><strong>Configure SendPulse API:</strong> Go to ChatGABI → Email Settings to set up your SendPulse credentials</li>';
    echo '<li><strong>Test Alert Creation:</strong> Visit the <a href="' . home_url('/alerts/') . '">Alerts Dashboard</a> to create a test alert</li>';
    echo '<li><strong>Monitor Cron Jobs:</strong> Ensure WordPress cron is running properly for automated processing</li>';
    echo '<li><strong>Review Email Templates:</strong> Customize email templates in the SendPulse integration</li>';
    echo '</ol>';
    
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>⚠️ Initialization Incomplete</h3>';
    echo '<p>Some components failed to initialize properly. Please review the errors above and try again.</p>';
    echo '<p>You may need to:</p>';
    echo '<ul>';
    echo '<li>Check database permissions</li>';
    echo '<li>Ensure all required files are present</li>';
    echo '<li>Verify WordPress configuration</li>';
    echo '</ul>';
    echo '</div>';
}

echo '<hr>';
echo '<p><em>Initialization completed at ' . current_time('mysql') . '</em></p>';
echo '<p><a href="' . admin_url() . '">← Return to WordPress Admin</a></p>';
?>

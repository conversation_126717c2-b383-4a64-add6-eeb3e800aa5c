<?php
/**
 * Collaboration and Sharing Features for BusinessCraft AI
 * 
 * Handles template and document collaboration including:
 * - Template sharing with team members
 * - Collaborative editing and comments
 * - Permission management
 * - Team workspaces
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize enhanced collaboration functionality with team workspaces
 */
function businesscraft_ai_init_collaboration() {
    // Add AJAX handlers
    add_action('wp_ajax_share_template', 'businesscraft_ai_handle_share_template');
    add_action('wp_ajax_accept_collaboration', 'businesscraft_ai_handle_accept_collaboration');
    add_action('wp_ajax_add_template_comment', 'businesscraft_ai_handle_add_comment');
    add_action('wp_ajax_get_template_collaborators', 'businesscraft_ai_handle_get_collaborators');
    add_action('wp_ajax_update_collaboration_permissions', 'businesscraft_ai_handle_update_permissions');

    // Enhanced team workspace handlers
    add_action('wp_ajax_create_team_workspace', 'businesscraft_ai_handle_create_workspace');
    add_action('wp_ajax_join_team_workspace', 'businesscraft_ai_handle_join_workspace');
    add_action('wp_ajax_get_workspace_activity', 'businesscraft_ai_handle_get_workspace_activity');
    add_action('wp_ajax_real_time_collaboration', 'businesscraft_ai_handle_real_time_collaboration');
    add_action('wp_ajax_workspace_chat', 'businesscraft_ai_handle_workspace_chat');

    // Add REST API endpoints
    add_action('rest_api_init', 'businesscraft_ai_register_collaboration_routes');

    // Enqueue collaboration scripts
    add_action('wp_enqueue_scripts', 'businesscraft_ai_enqueue_collaboration_scripts');

    // Add email notifications
    add_action('businesscraft_ai_collaboration_invited', 'businesscraft_ai_send_collaboration_email', 10, 3);
    add_action('businesscraft_ai_comment_added', 'businesscraft_ai_send_comment_notification', 10, 3);

    // Initialize collaboration tables
    add_action('init', 'businesscraft_ai_create_collaboration_tables');
}
add_action('init', 'businesscraft_ai_init_collaboration');

/**
 * Register REST API routes for collaboration
 */
function businesscraft_ai_register_collaboration_routes() {
    register_rest_route('businesscraft-ai/v1', '/templates/(?P<id>\d+)/share', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_share_template',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'email' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_email'
            ),
            'permission' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'view',
                'enum' => array('view', 'comment', 'edit')
            ),
            'message' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field'
            )
        )
    ));
    
    register_rest_route('businesscraft-ai/v1', '/templates/(?P<id>\d+)/collaborators', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_collaborators',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/templates/(?P<id>\d+)/comments', array(
        'methods' => array('GET', 'POST'),
        'callback' => 'businesscraft_ai_rest_handle_comments',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/collaboration/invitations', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_invitations',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
}

/**
 * Enqueue collaboration scripts
 */
function businesscraft_ai_enqueue_collaboration_scripts() {
    if (is_page('templates') || is_page('dashboard')) {
        wp_enqueue_script(
            'businesscraft-ai-collaboration',
            get_template_directory_uri() . '/assets/js/collaboration.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('businesscraft-ai-collaboration', 'businesscraftCollaboration', array(
            'restUrl' => rest_url('businesscraft-ai/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'currentUserId' => get_current_user_id(),
            'strings' => array(
                'sharing' => __('Sharing template...', 'businesscraft-ai'),
                'shareSuccess' => __('Template shared successfully!', 'businesscraft-ai'),
                'shareError' => __('Failed to share template. Please try again.', 'businesscraft-ai'),
                'commentAdded' => __('Comment added successfully!', 'businesscraft-ai'),
                'commentError' => __('Failed to add comment. Please try again.', 'businesscraft-ai'),
                'invitationAccepted' => __('Collaboration invitation accepted!', 'businesscraft-ai'),
                'permissionUpdated' => __('Permissions updated successfully!', 'businesscraft-ai'),
                'confirmRemoveCollaborator' => __('Are you sure you want to remove this collaborator?', 'businesscraft-ai')
            )
        ));
        
        wp_enqueue_style(
            'businesscraft-ai-collaboration',
            get_template_directory_uri() . '/assets/css/collaboration.css',
            array(),
            '1.0.0'
        );
    }
}

/**
 * Handle share template AJAX request
 */
function businesscraft_ai_handle_share_template() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_die(__('Security check failed', 'businesscraft-ai'));
    }
    
    // Check user permissions
    if (!is_user_logged_in()) {
        wp_die(__('You must be logged in to share templates', 'businesscraft-ai'));
    }
    
    $template_id = isset($_POST['template_id']) ? absint($_POST['template_id']) : 0;
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $permission = isset($_POST['permission']) ? sanitize_text_field($_POST['permission']) : 'view';
    $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';
    
    if (!$template_id || !$email) {
        wp_send_json_error(__('Template ID and email are required', 'businesscraft-ai'));
    }
    
    // Share template
    $result = businesscraft_ai_share_template($template_id, $email, $permission, $message);
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

/**
 * Share template with user
 */
function businesscraft_ai_share_template($template_id, $email, $permission = 'view', $message = '') {
    global $wpdb;
    
    $user_id = get_current_user_id();
    $templates_table = $wpdb->prefix . 'chatgabi_generated_templates';
    $collaborations_table = $wpdb->prefix . 'chatgabi_template_collaborations';
    
    // Verify template ownership
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$templates_table} WHERE id = %d AND user_id = %d",
        $template_id,
        $user_id
    ));
    
    if (!$template) {
        return array(
            'success' => false,
            'message' => __('Template not found or access denied', 'businesscraft-ai')
        );
    }
    
    // Check if user exists or create invitation
    $collaborator = get_user_by('email', $email);
    $collaborator_id = $collaborator ? $collaborator->ID : null;
    
    // Create collaboration table if it doesn't exist
    businesscraft_ai_create_collaborations_table();
    
    // Check if collaboration already exists
    $existing = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$collaborations_table} 
         WHERE template_id = %d AND (collaborator_id = %d OR email = %s)",
        $template_id,
        $collaborator_id ?: 0,
        $email
    ));
    
    if ($existing) {
        // Update existing collaboration
        $wpdb->update(
            $collaborations_table,
            array(
                'permission' => $permission,
                'status' => 'pending',
                'updated_at' => current_time('mysql')
            ),
            array('id' => $existing->id),
            array('%s', '%s', '%s'),
            array('%d')
        );
        
        $collaboration_id = $existing->id;
    } else {
        // Create new collaboration
        $wpdb->insert(
            $collaborations_table,
            array(
                'template_id' => $template_id,
                'owner_id' => $user_id,
                'collaborator_id' => $collaborator_id,
                'email' => $email,
                'permission' => $permission,
                'status' => 'pending',
                'invitation_message' => $message,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%d', '%s', '%s', '%s', '%s', '%s')
        );
        
        $collaboration_id = $wpdb->insert_id;
    }
    
    if ($collaboration_id) {
        // Trigger email notification
        do_action('businesscraft_ai_collaboration_invited', $collaboration_id, $template, $message);
        
        return array(
            'success' => true,
            'message' => sprintf(__('Template shared with %s', 'businesscraft-ai'), $email),
            'collaboration_id' => $collaboration_id
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to share template', 'businesscraft-ai')
        );
    }
}

/**
 * Get template collaborators
 */
function businesscraft_ai_get_template_collaborators($template_id) {
    global $wpdb;
    
    $collaborations_table = $wpdb->prefix . 'chatgabi_template_collaborations';
    
    $collaborators = $wpdb->get_results($wpdb->prepare(
        "SELECT c.*, u.display_name, u.user_email 
         FROM {$collaborations_table} c
         LEFT JOIN {$wpdb->users} u ON c.collaborator_id = u.ID
         WHERE c.template_id = %d AND c.status = 'accepted'
         ORDER BY c.created_at DESC",
        $template_id
    ));
    
    return $collaborators;
}

/**
 * Add comment to template
 */
function businesscraft_ai_add_template_comment($template_id, $comment, $parent_id = 0) {
    global $wpdb;
    
    $user_id = get_current_user_id();
    $comments_table = $wpdb->prefix . 'chatgabi_template_comments';
    
    // Verify access to template
    if (!businesscraft_ai_can_access_template($template_id, $user_id)) {
        return array(
            'success' => false,
            'message' => __('Access denied', 'businesscraft-ai')
        );
    }
    
    // Create comments table if it doesn't exist
    businesscraft_ai_create_comments_table();
    
    // Insert comment
    $result = $wpdb->insert(
        $comments_table,
        array(
            'template_id' => $template_id,
            'user_id' => $user_id,
            'comment' => $comment,
            'parent_id' => $parent_id,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%d', '%s')
    );
    
    if ($result) {
        $comment_id = $wpdb->insert_id;
        
        // Trigger notification
        do_action('businesscraft_ai_comment_added', $comment_id, $template_id, $comment);
        
        return array(
            'success' => true,
            'message' => __('Comment added successfully', 'businesscraft-ai'),
            'comment_id' => $comment_id
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to add comment', 'businesscraft-ai')
        );
    }
}

/**
 * Check if user can access template
 */
function businesscraft_ai_can_access_template($template_id, $user_id) {
    global $wpdb;
    
    $templates_table = $wpdb->prefix . 'chatgabi_generated_templates';
    $collaborations_table = $wpdb->prefix . 'chatgabi_template_collaborations';
    
    // Check if user is owner
    $is_owner = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$templates_table} WHERE id = %d AND user_id = %d",
        $template_id,
        $user_id
    ));
    
    if ($is_owner) {
        return true;
    }
    
    // Check if user is collaborator
    $is_collaborator = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$collaborations_table} 
         WHERE template_id = %d AND collaborator_id = %d AND status = 'accepted'",
        $template_id,
        $user_id
    ));
    
    return $is_collaborator > 0;
}

/**
 * Send collaboration invitation email
 */
function businesscraft_ai_send_collaboration_email($collaboration_id, $template, $message) {
    global $wpdb;
    
    $collaborations_table = $wpdb->prefix . 'chatgabi_template_collaborations';
    
    $collaboration = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$collaborations_table} WHERE id = %d",
        $collaboration_id
    ));
    
    if (!$collaboration) {
        return;
    }
    
    $owner = get_user_by('ID', $collaboration->owner_id);
    $subject = sprintf(__('[%s] You\'ve been invited to collaborate on a business template', 'businesscraft-ai'), get_bloginfo('name'));
    
    // Generate acceptance URL
    $accept_url = add_query_arg(array(
        'action' => 'accept_collaboration',
        'collaboration_id' => $collaboration_id,
        'nonce' => wp_create_nonce('accept_collaboration_' . $collaboration_id)
    ), home_url());
    
    // Email template
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .email-container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #3D4E81, #5753C9); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .template-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3D4E81; }
            .accept-button { display: inline-block; background: #3D4E81; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
            .footer { text-align: center; color: #666; font-size: 12px; margin-top: 30px; }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                <h1><?php echo get_bloginfo('name'); ?></h1>
                <p><?php _e('Collaboration Invitation', 'businesscraft-ai'); ?></p>
            </div>
            
            <div class="content">
                <h2><?php _e('You\'ve been invited to collaborate!', 'businesscraft-ai'); ?></h2>
                
                <p><?php printf(__('%s has invited you to collaborate on a business template.', 'businesscraft-ai'), $owner->display_name); ?></p>
                
                <?php if (!empty($message)): ?>
                    <div class="personal-message">
                        <p><strong><?php _e('Personal Message:', 'businesscraft-ai'); ?></strong></p>
                        <p><?php echo nl2br(esc_html($message)); ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="template-info">
                    <h3><?php _e('Template Details', 'businesscraft-ai'); ?></h3>
                    <ul>
                        <li><strong><?php _e('Template:', 'businesscraft-ai'); ?></strong> <?php echo esc_html($template->template_name); ?></li>
                        <li><strong><?php _e('Type:', 'businesscraft-ai'); ?></strong> <?php echo esc_html(ucwords(str_replace('-', ' ', $template->template_type))); ?></li>
                        <li><strong><?php _e('Permission:', 'businesscraft-ai'); ?></strong> <?php echo esc_html(ucfirst($collaboration->permission)); ?></li>
                    </ul>
                </div>
                
                <div style="text-align: center;">
                    <a href="<?php echo esc_url($accept_url); ?>" class="accept-button">
                        🤝 <?php _e('Accept Invitation', 'businesscraft-ai'); ?>
                    </a>
                </div>
                
                <p><small><?php _e('This invitation will expire in 7 days.', 'businesscraft-ai'); ?></small></p>
            </div>
            
            <div class="footer">
                <p><?php printf(__('This email was sent by %s', 'businesscraft-ai'), get_bloginfo('name')); ?></p>
                <p><?php echo home_url(); ?></p>
            </div>
        </div>
    </body>
    </html>
    <?php
    $email_body = ob_get_clean();
    
    // Email headers
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        'Reply-To: ' . $owner->user_email
    );
    
    // Send email
    wp_mail($collaboration->email, $subject, $email_body, $headers);
}

/**
 * Create collaborations table
 */
function businesscraft_ai_create_collaborations_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_template_collaborations';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        template_id bigint(20) NOT NULL,
        owner_id bigint(20) NOT NULL,
        collaborator_id bigint(20) NULL,
        email varchar(255) NOT NULL,
        permission varchar(20) NOT NULL DEFAULT 'view',
        status varchar(20) NOT NULL DEFAULT 'pending',
        invitation_message text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        accepted_at datetime NULL,
        PRIMARY KEY (id),
        KEY template_id (template_id),
        KEY owner_id (owner_id),
        KEY collaborator_id (collaborator_id),
        KEY email (email),
        KEY status (status),
        UNIQUE KEY unique_collaboration (template_id, email)
    ) {$charset_collate};";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Create comments table
 */
function businesscraft_ai_create_comments_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_template_comments';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        template_id bigint(20) NOT NULL,
        user_id bigint(20) NOT NULL,
        comment text NOT NULL,
        parent_id bigint(20) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY template_id (template_id),
        KEY user_id (user_id),
        KEY parent_id (parent_id),
        KEY created_at (created_at)
    ) {$charset_collate};";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Handle accept collaboration request
 */
function businesscraft_ai_handle_accept_collaboration() {
    $collaboration_id = isset($_GET['collaboration_id']) ? absint($_GET['collaboration_id']) : 0;
    $nonce = isset($_GET['nonce']) ? sanitize_text_field($_GET['nonce']) : '';

    if (!$collaboration_id || !wp_verify_nonce($nonce, 'accept_collaboration_' . $collaboration_id)) {
        wp_die(__('Invalid collaboration request', 'businesscraft-ai'));
    }

    global $wpdb;
    $collaborations_table = $wpdb->prefix . 'chatgabi_template_collaborations';

    $collaboration = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$collaborations_table} WHERE id = %d",
        $collaboration_id
    ));

    if (!$collaboration) {
        wp_die(__('Collaboration not found', 'businesscraft-ai'));
    }

    // Check if user is logged in and matches email
    if (!is_user_logged_in()) {
        // Redirect to login with return URL
        $return_url = add_query_arg($_GET, home_url($_SERVER['REQUEST_URI']));
        wp_redirect(wp_login_url($return_url));
        exit;
    }

    $current_user = wp_get_current_user();

    if ($current_user->user_email !== $collaboration->email) {
        wp_die(__('Email mismatch. Please log in with the invited email address.', 'businesscraft-ai'));
    }

    // Accept collaboration
    $updated = $wpdb->update(
        $collaborations_table,
        array(
            'collaborator_id' => $current_user->ID,
            'status' => 'accepted',
            'accepted_at' => current_time('mysql')
        ),
        array('id' => $collaboration_id),
        array('%d', '%s', '%s'),
        array('%d')
    );

    if ($updated) {
        // Redirect to template with success message
        $redirect_url = add_query_arg(array(
            'collaboration_accepted' => 1,
            'template_id' => $collaboration->template_id
        ), home_url('/templates'));

        wp_redirect($redirect_url);
        exit;
    } else {
        wp_die(__('Failed to accept collaboration', 'businesscraft-ai'));
    }
}
add_action('init', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'accept_collaboration') {
        businesscraft_ai_handle_accept_collaboration();
    }
});

/**
 * Get user's collaboration invitations
 */
function businesscraft_ai_get_user_invitations($user_id) {
    global $wpdb;

    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return array();
    }

    $collaborations_table = $wpdb->prefix . 'chatgabi_template_collaborations';
    $templates_table = $wpdb->prefix . 'chatgabi_generated_templates';

    $invitations = $wpdb->get_results($wpdb->prepare(
        "SELECT c.*, t.template_name, t.template_type, u.display_name as owner_name
         FROM {$collaborations_table} c
         LEFT JOIN {$templates_table} t ON c.template_id = t.id
         LEFT JOIN {$wpdb->users} u ON c.owner_id = u.ID
         WHERE c.email = %s AND c.status = 'pending'
         ORDER BY c.created_at DESC",
        $user->user_email
    ));

    return $invitations;
}

/**
 * Handle create team workspace
 */
function businesscraft_ai_handle_create_workspace() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }

    $workspace_name = sanitize_text_field($_POST['workspace_name'] ?? '');
    $description = sanitize_textarea_field($_POST['description'] ?? '');
    $workspace_type = sanitize_text_field($_POST['workspace_type'] ?? 'business');

    if (empty($workspace_name)) {
        wp_send_json_error('Workspace name is required');
        return;
    }

    $result = businesscraft_ai_create_team_workspace($workspace_name, $description, $workspace_type, $user_id);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

/**
 * Create team workspace
 */
function businesscraft_ai_create_team_workspace($name, $description, $type, $owner_id) {
    global $wpdb;

    $workspaces_table = $wpdb->prefix . 'chatgabi_team_workspaces';

    // Create workspace
    $result = $wpdb->insert(
        $workspaces_table,
        array(
            'name' => $name,
            'description' => $description,
            'workspace_type' => $type,
            'owner_id' => $owner_id,
            'status' => 'active',
            'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%d', '%s', '%s')
    );

    if ($result) {
        $workspace_id = $wpdb->insert_id;

        // Add owner as admin member
        businesscraft_ai_add_workspace_member($workspace_id, $owner_id, 'admin');

        return array(
            'success' => true,
            'workspace_id' => $workspace_id,
            'message' => __('Team workspace created successfully!', 'businesscraft-ai')
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to create workspace', 'businesscraft-ai')
        );
    }
}

/**
 * Add member to workspace
 */
function businesscraft_ai_add_workspace_member($workspace_id, $user_id, $role = 'member') {
    global $wpdb;

    $members_table = $wpdb->prefix . 'chatgabi_workspace_members';

    // Check if already a member
    $existing = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$members_table} WHERE workspace_id = %d AND user_id = %d",
        $workspace_id, $user_id
    ));

    if ($existing) {
        return false;
    }

    return $wpdb->insert(
        $members_table,
        array(
            'workspace_id' => $workspace_id,
            'user_id' => $user_id,
            'role' => $role,
            'status' => 'active',
            'joined_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s')
    );
}

/**
 * Check if user can access workspace
 */
function businesscraft_ai_can_access_workspace($workspace_id, $user_id) {
    global $wpdb;

    $members_table = $wpdb->prefix . 'chatgabi_workspace_members';

    $is_member = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$members_table}
         WHERE workspace_id = %d AND user_id = %d AND status = 'active'",
        $workspace_id, $user_id
    ));

    return $is_member > 0;
}

/**
 * Create collaboration tables including team workspaces
 */
function businesscraft_ai_create_collaboration_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Team workspaces table
    $workspaces_table = $wpdb->prefix . 'chatgabi_team_workspaces';
    $workspaces_sql = "CREATE TABLE IF NOT EXISTS {$workspaces_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        description text,
        workspace_type varchar(50) NOT NULL DEFAULT 'business',
        owner_id bigint(20) NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'active',
        settings longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY owner_id (owner_id),
        KEY workspace_type (workspace_type),
        KEY status (status)
    ) {$charset_collate};";

    // Workspace members table
    $members_table = $wpdb->prefix . 'chatgabi_workspace_members';
    $members_sql = "CREATE TABLE IF NOT EXISTS {$members_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        workspace_id bigint(20) NOT NULL,
        user_id bigint(20) NOT NULL,
        role varchar(20) NOT NULL DEFAULT 'member',
        status varchar(20) NOT NULL DEFAULT 'active',
        permissions longtext,
        joined_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY workspace_id (workspace_id),
        KEY user_id (user_id),
        KEY role (role),
        UNIQUE KEY unique_membership (workspace_id, user_id)
    ) {$charset_collate};";

    // Workspace chat table
    $chat_table = $wpdb->prefix . 'chatgabi_workspace_chat';
    $chat_sql = "CREATE TABLE IF NOT EXISTS {$chat_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        workspace_id bigint(20) NOT NULL,
        user_id bigint(20) NOT NULL,
        message text NOT NULL,
        message_type varchar(20) NOT NULL DEFAULT 'text',
        reply_to bigint(20) NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY workspace_id (workspace_id),
        KEY user_id (user_id),
        KEY created_at (created_at)
    ) {$charset_collate};";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($workspaces_sql);
    dbDelta($members_sql);
    dbDelta($chat_sql);
}

/**
 * Install collaboration functionality
 */
function businesscraft_ai_install_collaboration() {
    businesscraft_ai_create_collaborations_table();
    businesscraft_ai_create_comments_table();
    businesscraft_ai_create_collaboration_tables();
}

// Install on theme activation
add_action('after_switch_theme', 'businesscraft_ai_install_collaboration');

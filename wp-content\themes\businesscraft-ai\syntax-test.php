<?php
/**
 * Simple syntax test
 */

echo "Testing syntax of data-loader.php...\n";

// Test if the file has syntax errors
$file = __DIR__ . '/inc/data-loader.php';
echo "File: " . $file . "\n";

// Use php -l equivalent
$output = [];
$return_code = 0;
exec("php -l \"$file\" 2>&1", $output, $return_code);

echo "Syntax check return code: " . $return_code . "\n";
echo "Output:\n";
foreach ($output as $line) {
    echo "  " . $line . "\n";
}

if ($return_code === 0) {
    echo "Syntax is OK. Trying to include...\n";

    // Define required constants
    if (!defined('ABSPATH')) {
        define('ABSPATH', __DIR__ . '/../../../');
    }
    if (!defined('WP_CONTENT_DIR')) {
        define('WP_CONTENT_DIR', __DIR__ . '/../../');
    }

    // Capture any output/errors
    ob_start();
    $error = null;

    try {
        include $file;
        echo "Include successful!\n";
    } catch (ParseError $e) {
        $error = "Parse Error: " . $e->getMessage();
    } catch (Error $e) {
        $error = "Fatal Error: " . $e->getMessage();
    } catch (Exception $e) {
        $error = "Exception: " . $e->getMessage();
    }

    $output = ob_get_clean();

    if ($output) {
        echo "Output during include:\n" . $output . "\n";
    }

    if ($error) {
        echo "Error during include: " . $error . "\n";
    }

    if (function_exists('load_business_dataset_by_country')) {
        echo "Function load_business_dataset_by_country exists!\n";
    } else {
        echo "Function load_business_dataset_by_country NOT found!\n";
    }
}

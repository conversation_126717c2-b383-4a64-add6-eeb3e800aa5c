/**
 * BusinessCraft AI Payments JavaScript
 * Handles Paystack payment integration
 */

(function() {
    'use strict';

    // Check if Paystack config is available
    if (typeof paystackConfig === 'undefined') {
        console.error('Paystack configuration not found.');
        return;
    }

    console.log('Paystack Public Key:', paystackConfig.publicKey);

    // Global payment function
    window.businesscraft_ai_initiate_frontend_payment = function(packageType) {
        console.log('Initiating payment for package:', packageType);

        // Check if user is logged in
        if (typeof businesscraftAI === 'undefined') {
            alert('Please log in to purchase credits.');
            window.location.href = '/wp-login.php?redirect_to=' + encodeURIComponent(window.location.href);
            return;
        }

        // Check if Paystack is loaded
        if (typeof PaystackPop === 'undefined') {
            console.error('Paystack library not loaded');
            alert('Payment system not available. Please try again later.');
            return;
        }

        // Show loading state
        showPaymentLoading(true);

        // Get payment details from server
        fetch(businesscraftAI.restUrl + 'initiate-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': businesscraftAI.nonce
            },
            body: JSON.stringify({
                package: packageType
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Payment initiation response:', data);

            if (data.code && data.code === 'rest_forbidden') {
                alert('Please log in to purchase credits.');
                window.location.href = '/wp-login.php?redirect_to=' + encodeURIComponent(window.location.href);
                return;
            }

            if (data.status === 'success' && data.data) {
                // Initialize Paystack payment
                initializePaystackPayment(data.data, packageType);
            } else {
                throw new Error(data.message || 'Failed to initialize payment');
            }
        })
        .catch(error => {
            console.error('Payment initiation error:', error);
            alert('Failed to initialize payment: ' + error.message);
        })
        .finally(() => {
            showPaymentLoading(false);
        });
    };

    function initializePaystackPayment(paymentData, packageType) {
        console.log('Initializing Paystack payment with data:', paymentData);

        const packageDetails = getPackageDetails(packageType);

        const handler = PaystackPop.setup({
            key: paystackConfig.publicKey,
            email: paymentData.customer.email,
            amount: paymentData.amount,
            currency: paymentData.currency || 'USD',
            ref: paymentData.reference,
            metadata: {
                package: packageType,
                credits: packageDetails.credits,
                user_id: paymentData.metadata.user_id
            },
            callback: function(response) {
                console.log('Payment successful:', response);
                handlePaymentSuccess(response, packageType);
            },
            onClose: function() {
                console.log('Payment popup closed');
                showPaymentMessage('Payment cancelled', 'warning');
            }
        });

        handler.openIframe();
    }

    function handlePaymentSuccess(response, packageType) {
        console.log('Handling payment success for:', packageType);

        showPaymentLoading(true, 'Verifying payment...');

        // Show success message immediately
        showPaymentMessage('Payment successful! Your credits are being added...', 'success');

        // Update credit display if present (optimistic update)
        const packageDetails = getPackageDetails(packageType);

        // Refresh page after 3 seconds to show updated credits
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    }

    // Store localized pricing data
    let localizedPricing = null;

    // Fetch localized pricing on page load
    function loadLocalizedPricing() {
        fetch(paystackConfig.restUrl + 'pricing')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    localizedPricing = data.data;
                    console.log('Localized pricing loaded:', localizedPricing);
                    updatePricingDisplay();
                }
            })
            .catch(error => {
                console.error('Failed to load localized pricing:', error);
            });
    }

    function getPackageDetails(packageType) {
        // Use localized pricing if available
        if (localizedPricing && localizedPricing.packages[packageType]) {
            const pkg = localizedPricing.packages[packageType];
            return {
                name: packageType.charAt(0).toUpperCase() + packageType.slice(1) + ' Pack',
                credits: pkg.credits,
                price: pkg.price_local,
                currency: pkg.currency,
                currency_symbol: pkg.currency_symbol,
                formatted_price: pkg.formatted_price,
                usd_price: pkg.price_usd
            };
        }

        // Enhanced packages with African market context
        const packages = {
            starter: {
                name: 'Starter Pack',
                credits: 500,
                price: 5.00,
                currency: 'USD',
                currency_symbol: '$',
                formatted_price: '$5.00',
                features: ['500 AI Credits', 'GPT-3.5 Turbo', 'Basic Templates', 'African Market Intelligence'],
                african_context: 'Perfect for small businesses and entrepreneurs testing AI solutions',
                value_proposition: 'About 50 business documents or 100 chat interactions',
                use_cases: ['Market research', 'Basic business plans', 'Social media content', 'Email templates'],
                local_examples: {
                    'GH': 'Cost of 2 weeks mobile data - ideal for testing ChatGABI',
                    'KE': 'Price of 3 matatu rides - perfect for small business owners',
                    'NG': 'Cost of a good meal - great for freelancers and startups',
                    'ZA': 'Price of coffee and pastry - excellent for entrepreneurs'
                }
            },
            growth: {
                name: 'Growth Pack',
                credits: 1500,
                price: 15.00,
                currency: 'USD',
                currency_symbol: '$',
                formatted_price: '$15.00',
                features: ['1,500 AI Credits', 'GPT-4 Turbo', 'Premium Templates', 'African Market Intelligence', 'Priority Support'],
                african_context: 'Most popular choice for growing SMEs across Africa',
                value_proposition: 'About 150 business documents or 300 chat interactions',
                use_cases: ['Comprehensive business plans', 'Marketing strategies', 'Financial forecasts', 'Multi-country expansion'],
                local_examples: {
                    'GH': 'Cost of business registration - invest in your growth',
                    'KE': 'Monthly M-Pesa transaction limit - scale your business',
                    'NG': 'CAC registration fee - formalize and grow',
                    'ZA': 'Monthly business banking - professional growth'
                }
            },
            business: {
                name: 'Business Pack',
                credits: 3000,
                price: 30.00,
                currency: 'USD',
                currency_symbol: '$',
                formatted_price: '$30.00',
                features: ['3,000 AI Credits', 'GPT-4 Turbo', 'All Premium Features', 'Multi-country Templates', 'Dedicated Support', 'Advanced Analytics'],
                african_context: 'Designed for established businesses expanding across African markets',
                value_proposition: 'About 300 business documents or 600 chat interactions',
                use_cases: ['Enterprise planning', 'Multi-market strategies', 'Team collaboration', 'Advanced analytics'],
                local_examples: {
                    'GH': 'Monthly office rent in Accra - invest in AI efficiency',
                    'KE': 'Business permit cost - automate your operations',
                    'NG': 'Monthly office internet - power your digital transformation',
                    'ZA': 'Professional services fee - enhance your competitive edge'
                }
            }
        };

        return packages[packageType] || packages.starter;
    }

    function getUserCountry() {
        // Try to get user's country from various sources
        if (window.chatgabiUserCountry) {
            return window.chatgabiUserCountry;
        }

        // Check if localized pricing has country info
        if (localizedPricing && localizedPricing.user_country) {
            return localizedPricing.user_country;
        }

        // Fallback to timezone-based detection
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const countryMap = {
            'Africa/Accra': 'GH',
            'Africa/Nairobi': 'KE',
            'Africa/Lagos': 'NG',
            'Africa/Johannesburg': 'ZA'
        };

        return countryMap[timezone] || 'GH'; // Default to Ghana
    }

    function showEnhancedPricingModal(packageType) {
        const packageDetails = getPackageDetails(packageType);
        const userCountry = getUserCountry();

        // Create modal HTML
        const modalHtml = `
            <div class="enhanced-pricing-modal" id="enhanced-pricing-modal">
                <div class="modal-overlay" onclick="closeEnhancedPricingModal()"></div>
                <div class="modal-content african-pattern-accent">
                    <div class="modal-header">
                        <h3>${packageDetails.name}</h3>
                        <button class="modal-close" onclick="closeEnhancedPricingModal()">&times;</button>
                    </div>

                    <div class="modal-body">
                        <div class="package-overview ubuntu-card">
                            <p class="african-context">${packageDetails.african_context}</p>
                            <p class="value-proposition"><strong>${packageDetails.value_proposition}</strong></p>
                        </div>

                        <div class="pricing-breakdown">
                            <div class="main-price">
                                <span class="currency">${packageDetails.currency_symbol}</span>
                                <span class="amount">${packageDetails.price}</span>
                                <span class="credits-info">${packageDetails.credits} Credits</span>
                            </div>

                            <div class="local-context market-indicator growing">
                                <span class="context-icon">🌍</span>
                                <span class="context-text">${packageDetails.local_examples[userCountry] || packageDetails.local_examples['GH']}</span>
                            </div>
                        </div>

                        <div class="package-features">
                            <h4>What's Included:</h4>
                            <ul class="features-list">
                                ${packageDetails.features.map(feature => `<li class="feature-item">✓ ${feature}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="use-cases">
                            <h4>Perfect For:</h4>
                            <div class="use-cases-grid">
                                ${packageDetails.use_cases.map(useCase => `
                                    <div class="use-case-item">
                                        <span class="use-case-icon">🎯</span>
                                        <span class="use-case-text">${useCase}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="payment-security african-wisdom-quote">
                            <div class="security-features">
                                <div class="security-item">
                                    <span class="security-icon">🔒</span>
                                    <span>Secure payment via Paystack</span>
                                </div>
                                <div class="security-item">
                                    <span class="security-icon">⚡</span>
                                    <span>Instant credit delivery</span>
                                </div>
                                <div class="security-item">
                                    <span class="security-icon">🤝</span>
                                    <span>African business support</span>
                                </div>
                                <div class="security-item">
                                    <span class="security-icon">💳</span>
                                    <span>Mobile money & card payments</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button class="btn-secondary" onclick="closeEnhancedPricingModal()">Maybe Later</button>
                        <button class="btn-primary african-business-icon" onclick="proceedWithPayment('${packageType}')">
                            Get ${packageDetails.name} Now
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add modal styles
        addEnhancedPricingStyles();

        // Show modal
        document.getElementById('enhanced-pricing-modal').style.display = 'flex';
    }

    function closeEnhancedPricingModal() {
        const modal = document.getElementById('enhanced-pricing-modal');
        if (modal) {
            modal.remove();
        }
    }

    function proceedWithPayment(packageType) {
        closeEnhancedPricingModal();
        // Trigger the existing payment flow
        const paymentButton = document.querySelector(`[data-package="${packageType}"]`);
        if (paymentButton) {
            paymentButton.click();
        }
    }

    function addEnhancedPricingStyles() {
        if (document.getElementById('enhanced-pricing-styles')) return;

        const style = document.createElement('style');
        style.id = 'enhanced-pricing-styles';
        style.textContent = `
            .enhanced-pricing-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                display: none;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
            }

            .modal-content {
                background: white;
                border-radius: 16px;
                max-width: 600px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 24px;
                border-bottom: 1px solid #e2e8f0;
            }

            .modal-header h3 {
                margin: 0;
                color: var(--color-primary-accent);
                font-size: 1.5rem;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            }

            .modal-close:hover {
                background: #f1f5f9;
            }

            .modal-body {
                padding: 24px;
            }

            .pricing-breakdown {
                text-align: center;
                margin: 24px 0;
            }

            .main-price {
                display: flex;
                align-items: baseline;
                justify-content: center;
                gap: 8px;
                margin-bottom: 16px;
            }

            .main-price .currency {
                font-size: 1.5rem;
                color: var(--color-primary-accent);
                font-weight: 600;
            }

            .main-price .amount {
                font-size: 3rem;
                font-weight: 700;
                color: var(--color-primary-accent);
            }

            .main-price .credits-info {
                font-size: 1rem;
                color: var(--color-text-secondary);
                font-weight: 500;
            }

            .local-context {
                margin: 16px 0;
                padding: 12px 16px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                gap: 8px;
                justify-content: center;
            }

            .features-list {
                list-style: none;
                padding: 0;
                margin: 16px 0;
            }

            .feature-item {
                padding: 8px 0;
                color: var(--color-text-primary);
                font-weight: 500;
            }

            .use-cases-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 12px;
                margin: 16px 0;
            }

            .use-case-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                background: var(--color-surface);
                border-radius: 8px;
                font-size: 0.9rem;
            }

            .security-features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 12px;
            }

            .security-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 0.9rem;
                color: white;
            }

            .modal-footer {
                display: flex;
                gap: 16px;
                padding: 24px;
                border-top: 1px solid #e2e8f0;
                justify-content: flex-end;
            }

            .btn-primary, .btn-secondary {
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                border: none;
            }

            .btn-primary {
                background: linear-gradient(135deg, var(--color-primary-accent) 0%, var(--color-tertiary-accent) 100%);
                color: white;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
            }

            .btn-secondary {
                background: var(--color-surface);
                color: var(--color-text-secondary);
                border: 1px solid var(--color-borders);
            }

            .btn-secondary:hover {
                background: var(--color-borders);
            }

            @media (max-width: 768px) {
                .modal-content {
                    margin: 10px;
                    max-height: 95vh;
                }

                .main-price .amount {
                    font-size: 2.5rem;
                }

                .use-cases-grid {
                    grid-template-columns: 1fr;
                }

                .modal-footer {
                    flex-direction: column;
                }
            }
        `;

        document.head.appendChild(style);
    }

    function updatePricingDisplay() {
        if (!localizedPricing) return;

        // Update pricing cards if they exist on the page
        const pricingCards = document.querySelectorAll('.pricing-card');
        pricingCards.forEach(card => {
            const packageType = card.classList.contains('starter-plan') ? 'starter' :
                              card.classList.contains('growth-plan') ? 'growth' :
                              card.classList.contains('business-plan') ? 'business' : null;

            if (packageType && localizedPricing.packages[packageType]) {
                const pkg = localizedPricing.packages[packageType];
                const currencySpan = card.querySelector('.currency');
                const amountSpan = card.querySelector('.amount');

                if (currencySpan && amountSpan) {
                    currencySpan.textContent = pkg.currency_symbol;
                    amountSpan.textContent = Math.round(pkg.price_local);
                }

                // Add USD equivalent note if not USD
                if (pkg.currency !== 'USD') {
                    let priceNote = card.querySelector('.price-note');
                    if (!priceNote) {
                        priceNote = document.createElement('div');
                        priceNote.className = 'price-note';
                        const priceDiv = card.querySelector('.plan-price');
                        if (priceDiv) {
                            priceDiv.parentNode.insertBefore(priceNote, priceDiv.nextSibling);
                        }
                    }
                    priceNote.innerHTML = `<small>≈ $${pkg.price_usd.toFixed(2)} USD</small>`;
                }
            }
        });
    }

    function showPaymentLoading(show, message = 'Processing payment...') {
        let loadingOverlay = document.getElementById('payment-loading-overlay');

        if (show) {
            if (!loadingOverlay) {
                loadingOverlay = document.createElement('div');
                loadingOverlay.id = 'payment-loading-overlay';
                loadingOverlay.innerHTML = `
                    <div class="payment-loading-content">
                        <div class="payment-loading-spinner"></div>
                        <p class="payment-loading-message">${message}</p>
                    </div>
                `;
                document.body.appendChild(loadingOverlay);

                // Add styles
                const style = document.createElement('style');
                style.textContent = `
                    #payment-loading-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.8);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    }
                    .payment-loading-content {
                        background: white;
                        padding: 2rem;
                        border-radius: 8px;
                        text-align: center;
                        max-width: 300px;
                    }
                    .payment-loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #667eea;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 1rem;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .payment-loading-message {
                        margin: 0;
                        color: #333;
                        font-weight: 500;
                    }
                `;
                document.head.appendChild(style);
            } else {
                loadingOverlay.querySelector('.payment-loading-message').textContent = message;
                loadingOverlay.style.display = 'flex';
            }
        } else {
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        }
    }

    function showPaymentMessage(message, type = 'info') {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.payment-message');
        existingMessages.forEach(msg => msg.remove());

        const messageDiv = document.createElement('div');
        messageDiv.className = `payment-message payment-message-${type}`;
        messageDiv.innerHTML = `
            <div class="payment-message-content">
                <span class="payment-message-text">${message}</span>
                <button class="payment-message-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add styles if not already added
        if (!document.getElementById('payment-message-styles')) {
            const style = document.createElement('style');
            style.id = 'payment-message-styles';
            style.textContent = `
                .payment-message {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10001;
                    max-width: 400px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    animation: slideIn 0.3s ease-out;
                }
                .payment-message-success {
                    background: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                }
                .payment-message-error {
                    background: #f8d7da;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                }
                .payment-message-warning {
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                }
                .payment-message-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 1rem;
                }
                .payment-message-text {
                    flex: 1;
                    margin-right: 1rem;
                }
                .payment-message-close {
                    background: none;
                    border: none;
                    font-size: 1.5rem;
                    cursor: pointer;
                    opacity: 0.7;
                }
                .payment-message-close:hover {
                    opacity: 1;
                }
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(messageDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    function updateCreditDisplay(newCredits) {
        const creditDisplays = document.querySelectorAll('.credit-display');
        creditDisplays.forEach(display => {
            display.textContent = `Credits: ${newCredits}`;
        });

        // Update header credit display if present
        const headerCredits = document.querySelectorAll('.user-credits');
        headerCredits.forEach(display => {
            display.textContent = `Credits: ${newCredits}`;
        });
    }

    function showLocationDebug() {
        const debugDiv = document.createElement('div');
        debugDiv.className = 'location-debug';
        debugDiv.innerHTML = `
            <strong>Debug Info:</strong><br>
            IP: ${locationDebug.userIP}<br>
            Country: ${locationDebug.detectedCountry}<br>
            Currency: ${locationDebug.userCurrency.currency} (${locationDebug.userCurrency.symbol})
        `;
        document.body.appendChild(debugDiv);

        console.log('Location Debug Info:', locationDebug);
    }

    // Initialize payment buttons when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Payments.js loaded and ready');

        // Check if Paystack config is available
        if (typeof paystackConfig === 'undefined') {
            console.error('Paystack configuration not found');
            return;
        }

        // Check if public key is configured
        if (!paystackConfig.publicKey) {
            console.error('Paystack public key not configured');
            return;
        }

        console.log('Paystack configuration loaded successfully');

        // Load localized pricing
        loadLocalizedPricing();

        // Show debug info if enabled
        if (typeof locationDebug !== 'undefined' && locationDebug.enabled) {
            showLocationDebug();
        }
    });

})();

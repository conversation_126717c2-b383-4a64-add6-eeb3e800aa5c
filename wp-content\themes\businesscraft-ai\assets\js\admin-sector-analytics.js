/**
 * Admin Sector Analytics Dashboard JavaScript
 */

(function($) {
    'use strict';

    let charts = {};

    $(document).ready(function() {
        initializeAnalytics();
        bindEvents();
        loadInitialData();
    });

    function initializeAnalytics() {
        // Initialize chart containers
        initializeCharts();
        
        // Load table data
        loadTableData();
    }

    function bindEvents() {
        // Filter events
        $('#apply-filters').on('click', function() {
            loadTableData();
        });

        // Export data
        $('#export-data').on('click', function() {
            exportData();
        });

        // Pagination events (delegated)
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            loadTableData(page);
        });
    }

    function initializeCharts() {
        // Sectors by Country Chart
        const sectorsCtx = document.getElementById('sectorsByCountryChart');
        if (sectorsCtx) {
            charts.sectorsByCountry = new Chart(sectorsCtx, {
                type: 'bar',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Most Requested Sectors by Country'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Requests'
                            }
                        }
                    }
                }
            });
        }

        // Success Rate Chart
        const successCtx = document.getElementById('successRateChart');
        if (successCtx) {
            charts.successRate = new Chart(successCtx, {
                type: 'line',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sector Context Success Rate Over Time'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Success Rate (%)'
                            }
                        }
                    }
                }
            });
        }

        // Token Distribution Chart
        const tokenCtx = document.getElementById('tokenDistributionChart');
        if (tokenCtx) {
            charts.tokenDistribution = new Chart(tokenCtx, {
                type: 'doughnut',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Token Usage Distribution'
                        },
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }

        // Daily Trends Chart
        const trendsCtx = document.getElementById('dailyTrendsChart');
        if (trendsCtx) {
            charts.dailyTrends = new Chart(trendsCtx, {
                type: 'line',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Daily Usage Trends (Last 30 Days)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Requests'
                            }
                        }
                    }
                }
            });
        }
    }

    function loadInitialData() {
        // Load all chart data
        loadChartData('sectors_by_country');
        loadChartData('success_rate_timeline');
        loadChartData('token_distribution');
        loadChartData('daily_trends');
    }

    function loadChartData(dataType) {
        $.ajax({
            url: sectorAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_sector_analytics_data',
                data_type: dataType,
                nonce: sectorAnalytics.nonce
            },
            beforeSend: function() {
                showLoading(dataType);
            },
            success: function(response) {
                if (response.success) {
                    updateChart(dataType, response.data);
                } else {
                    showError(dataType, response.data || sectorAnalytics.strings.error);
                }
            },
            error: function() {
                showError(dataType, sectorAnalytics.strings.error);
            },
            complete: function() {
                hideLoading(dataType);
            }
        });
    }

    function updateChart(dataType, data) {
        let chart;
        
        switch(dataType) {
            case 'sectors_by_country':
                chart = charts.sectorsByCountry;
                break;
            case 'success_rate_timeline':
                chart = charts.successRate;
                break;
            case 'token_distribution':
                chart = charts.tokenDistribution;
                break;
            case 'daily_trends':
                chart = charts.dailyTrends;
                break;
        }

        if (chart && data) {
            chart.data = data;
            chart.update();
        }
    }

    function loadTableData(page = 1) {
        const filters = {
            country_filter: $('#country-filter').val(),
            sector_filter: $('#sector-filter').val(),
            success_filter: $('#success-filter').val(),
            page: page
        };

        $.ajax({
            url: sectorAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_sector_analytics_data',
                data_type: 'recent_logs',
                nonce: sectorAnalytics.nonce,
                ...filters
            },
            beforeSend: function() {
                $('#sector-logs-tbody').html('<tr><td colspan="7">' + sectorAnalytics.strings.loading + '</td></tr>');
            },
            success: function(response) {
                if (response.success) {
                    updateTable(response.data);
                } else {
                    $('#sector-logs-tbody').html('<tr><td colspan="7">' + (response.data || sectorAnalytics.strings.error) + '</td></tr>');
                }
            },
            error: function() {
                $('#sector-logs-tbody').html('<tr><td colspan="7">' + sectorAnalytics.strings.error + '</td></tr>');
            }
        });
    }

    function updateTable(data) {
        const tbody = $('#sector-logs-tbody');
        tbody.empty();

        if (data.logs && data.logs.length > 0) {
            data.logs.forEach(function(log) {
                const row = $('<tr>');
                
                row.append('<td>' + formatDate(log.timestamp) + '</td>');
                row.append('<td>' + (log.display_name || 'Unknown') + '</td>');
                row.append('<td>' + log.country + '</td>');
                row.append('<td>' + (log.detected_sector || 'None') + '</td>');
                row.append('<td>' + (log.sector_context_found == 1 ? '✅ Yes' : '❌ No') + '</td>');
                row.append('<td>' + log.prompt_tokens_estimated + '</td>');
                row.append('<td>' + truncateText(log.user_message_preview, 50) + '</td>');
                
                tbody.append(row);
            });

            // Update pagination
            updatePagination(data);
        } else {
            tbody.html('<tr><td colspan="7">' + sectorAnalytics.strings.noData + '</td></tr>');
        }
    }

    function updatePagination(data) {
        const pagination = $('#table-pagination');
        pagination.empty();

        if (data.total_pages > 1) {
            let paginationHtml = '<div class="pagination">';
            
            // Previous button
            if (data.current_page > 1) {
                paginationHtml += '<a href="#" class="pagination-link" data-page="' + (data.current_page - 1) + '">« Previous</a>';
            }

            // Page numbers
            for (let i = 1; i <= data.total_pages; i++) {
                if (i === data.current_page) {
                    paginationHtml += '<span class="current-page">' + i + '</span>';
                } else {
                    paginationHtml += '<a href="#" class="pagination-link" data-page="' + i + '">' + i + '</a>';
                }
            }

            // Next button
            if (data.current_page < data.total_pages) {
                paginationHtml += '<a href="#" class="pagination-link" data-page="' + (data.current_page + 1) + '">Next »</a>';
            }

            paginationHtml += '</div>';
            pagination.html(paginationHtml);
        }
    }

    function exportData() {
        const filters = {
            country_filter: $('#country-filter').val(),
            sector_filter: $('#sector-filter').val(),
            success_filter: $('#success-filter').val()
        };

        // Create form and submit for CSV download
        const form = $('<form>', {
            method: 'POST',
            action: sectorAnalytics.ajaxUrl
        });

        form.append($('<input>', { type: 'hidden', name: 'action', value: 'export_sector_analytics' }));
        form.append($('<input>', { type: 'hidden', name: 'nonce', value: sectorAnalytics.nonce }));
        
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                form.append($('<input>', { type: 'hidden', name: key, value: filters[key] }));
            }
        });

        $('body').append(form);
        form.submit();
        form.remove();
    }

    function showLoading(dataType) {
        // Add loading indicator to chart containers
        const chartId = getChartId(dataType);
        if (chartId) {
            $('#' + chartId).closest('.chart-container').addClass('loading');
        }
    }

    function hideLoading(dataType) {
        const chartId = getChartId(dataType);
        if (chartId) {
            $('#' + chartId).closest('.chart-container').removeClass('loading');
        }
    }

    function showError(dataType, message) {
        const chartId = getChartId(dataType);
        if (chartId) {
            $('#' + chartId).closest('.chart-container').find('.chart-error').remove();
            $('#' + chartId).closest('.chart-container').append('<div class="chart-error">' + message + '</div>');
        }
    }

    function getChartId(dataType) {
        const mapping = {
            'sectors_by_country': 'sectorsByCountryChart',
            'success_rate_timeline': 'successRateChart',
            'token_distribution': 'tokenDistributionChart',
            'daily_trends': 'dailyTrendsChart'
        };
        return mapping[dataType];
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    function truncateText(text, maxLength) {
        if (text && text.length > maxLength) {
            return text.substring(0, maxLength) + '...';
        }
        return text || '';
    }

})(jQuery);

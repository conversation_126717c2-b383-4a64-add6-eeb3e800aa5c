/*! For license information please see edit.js.LICENSE.txt */
(self.webpackChunkcode_snippets=self.webpackChunkcode_snippets||[]).push([[8739],{2374:(e,t,n)=>{"use strict";var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Ee,hasStandardBrowserEnv:()=>ke,hasStandardBrowserWebWorkerEnv:()=>Ce,navigator:()=>Pe,origin:()=>Re});var o=n(1609),i=n(5338),s=n(3453),a=n(6942),c=n.n(a),l=function(){return window.pagenow.endsWith("-network")},u=function(){return null!==/mac/i.exec(window.navigator.userAgent)},p=function(){var e;return!(null===(e=window.CODE_SNIPPETS)||void 0===e||!e.isLicensed)},d=["css","js"],f=function(){return{id:0,name:"",desc:"",code:"",tags:[],scope:"global",modified:"",active:!1,network:l(),shared_network:null,priority:10}},h=function(e){var t="string"==typeof e?e:e.scope;switch(!0){case t.endsWith("-css"):return"css";case t.endsWith("-js"):return"js";case t.endsWith("content"):return"html";default:return"php"}},m=function(e){return d.includes(e)},y=n(2284);function b(e,t){return function(){return e.apply(t,arguments)}}const{toString:g}=Object.prototype,{getPrototypeOf:v}=Object,w=(j=Object.create(null),e=>{const t=g.call(e);return j[t]||(j[t]=t.slice(8,-1).toLowerCase())});var j;const O=e=>(e=e.toLowerCase(),t=>w(t)===e),_=e=>t=>typeof t===e,{isArray:x}=Array,S=_("undefined"),E=O("ArrayBuffer"),P=_("string"),k=_("function"),C=_("number"),R=e=>null!==e&&"object"==typeof e,T=e=>{if("object"!==w(e))return!1;const t=v(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},A=O("Date"),N=O("File"),D=O("Blob"),L=O("FileList"),F=O("URLSearchParams"),[B,I,U,q]=["ReadableStream","Request","Response","Headers"].map(O);function M(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),x(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let s;for(r=0;r<i;r++)s=o[r],t.call(null,e[s],s,e)}}function W(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const z="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,H=e=>!S(e)&&e!==z,J=(K="undefined"!=typeof Uint8Array&&v(Uint8Array),e=>K&&e instanceof K);var K;const $=O("HTMLFormElement"),G=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),V=O("RegExp"),X=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};M(n,((n,o)=>{let i;!1!==(i=t(n,o,e))&&(r[o]=i||n)})),Object.defineProperties(e,r)},Y="abcdefghijklmnopqrstuvwxyz",Z="0123456789",Q={DIGIT:Z,ALPHA:Y,ALPHA_DIGIT:Y+Y.toUpperCase()+Z},ee=O("AsyncFunction"),te=(ne="function"==typeof setImmediate,re=k(z.postMessage),ne?setImmediate:re?(oe=`axios@${Math.random()}`,ie=[],z.addEventListener("message",(({source:e,data:t})=>{e===z&&t===oe&&ie.length&&ie.shift()()}),!1),e=>{ie.push(e),z.postMessage(oe,"*")}):e=>setTimeout(e));var ne,re,oe,ie;const se="undefined"!=typeof queueMicrotask?queueMicrotask.bind(z):"undefined"!=typeof process&&process.nextTick||te,ae={isArray:x,isArrayBuffer:E,isBuffer:function(e){return null!==e&&!S(e)&&null!==e.constructor&&!S(e.constructor)&&k(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||k(e.append)&&("formdata"===(t=w(e))||"object"===t&&k(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&E(e.buffer),t},isString:P,isNumber:C,isBoolean:e=>!0===e||!1===e,isObject:R,isPlainObject:T,isReadableStream:B,isRequest:I,isResponse:U,isHeaders:q,isUndefined:S,isDate:A,isFile:N,isBlob:D,isRegExp:V,isFunction:k,isStream:e=>R(e)&&k(e.pipe),isURLSearchParams:F,isTypedArray:J,isFileList:L,forEach:M,merge:function e(){const{caseless:t}=H(this)&&this||{},n={},r=(r,o)=>{const i=t&&W(n,o)||o;T(n[i])&&T(r)?n[i]=e(n[i],r):T(r)?n[i]=e({},r):x(r)?n[i]=r.slice():n[i]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&M(arguments[e],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(M(t,((t,r)=>{n&&k(t)?e[r]=b(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,i,s;const a={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),i=o.length;i-- >0;)s=o[i],r&&!r(s,e,t)||a[s]||(t[s]=e[s],a[s]=!0);e=!1!==n&&v(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:w,kindOfTest:O,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(x(e))return e;let t=e.length;if(!C(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:$,hasOwnProperty:G,hasOwnProp:G,reduceDescriptors:X,freezeMethods:e=>{X(e,((t,n)=>{if(k(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];k(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return x(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:W,global:z,isContextDefined:H,ALPHABET:Q,generateString:(e=16,t=Q.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&k(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(R(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=x(e)?[]:{};return M(e,((e,t)=>{const i=n(e,r+1);!S(i)&&(o[t]=i)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:ee,isThenable:e=>e&&(R(e)||k(e))&&k(e.then)&&k(e.catch),setImmediate:te,asap:se};function ce(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}ae.inherits(ce,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ae.toJSONObject(this.config),code:this.code,status:this.status}}});const le=ce.prototype,ue={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{ue[e]={value:e}})),Object.defineProperties(ce,ue),Object.defineProperty(le,"isAxiosError",{value:!0}),ce.from=(e,t,n,r,o,i)=>{const s=Object.create(le);return ae.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),ce.call(s,e.message,t,n,r,o),s.cause=e,s.name=e.name,i&&Object.assign(s,i),s};const pe=ce;function de(e){return ae.isPlainObject(e)||ae.isArray(e)}function fe(e){return ae.endsWith(e,"[]")?e.slice(0,-2):e}function he(e,t,n){return e?e.concat(t).map((function(e,t){return e=fe(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const me=ae.toFlatObject(ae,{},null,(function(e){return/^is[A-Z]/.test(e)})),ye=function(e,t,n){if(!ae.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=ae.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!ae.isUndefined(t[e])}))).metaTokens,o=n.visitor||l,i=n.dots,s=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&ae.isSpecCompliantForm(t);if(!ae.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ae.isDate(e))return e.toISOString();if(!a&&ae.isBlob(e))throw new pe("Blob is not supported. Use a Buffer instead.");return ae.isArrayBuffer(e)||ae.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,o){let a=e;if(e&&!o&&"object"==typeof e)if(ae.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(ae.isArray(e)&&function(e){return ae.isArray(e)&&!e.some(de)}(e)||(ae.isFileList(e)||ae.endsWith(n,"[]"))&&(a=ae.toArray(e)))return n=fe(n),a.forEach((function(e,r){!ae.isUndefined(e)&&null!==e&&t.append(!0===s?he([n],r,i):null===s?n:n+"[]",c(e))})),!1;return!!de(e)||(t.append(he(o,n,i),c(e)),!1)}const u=[],p=Object.assign(me,{defaultVisitor:l,convertValue:c,isVisitable:de});if(!ae.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!ae.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),ae.forEach(n,(function(n,i){!0===(!(ae.isUndefined(n)||null===n)&&o.call(t,n,ae.isString(i)?i.trim():i,r,p))&&e(n,r?r.concat(i):[i])})),u.pop()}}(e),t};function be(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function ge(e,t){this._pairs=[],e&&ye(e,this,t)}const ve=ge.prototype;ve.append=function(e,t){this._pairs.push([e,t])},ve.toString=function(e){const t=e?function(t){return e.call(this,t,be)}:be;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const we=ge;function je(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Oe(e,t,n){if(!t)return e;const r=n&&n.encode||je;ae.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let i;if(i=o?o(t,n):ae.isURLSearchParams(t)?t.toString():new we(t,n).toString(r),i){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}const _e=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ae.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},xe={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Se={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:we,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Ee="undefined"!=typeof window&&"undefined"!=typeof document,Pe="object"==typeof navigator&&navigator||void 0,ke=Ee&&(!Pe||["ReactNative","NativeScript","NS"].indexOf(Pe.product)<0),Ce="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Re=Ee&&window.location.href||"http://localhost",Te={...r,...Se},Ae=function(e){function t(e,n,r,o){let i=e[o++];if("__proto__"===i)return!0;const s=Number.isFinite(+i),a=o>=e.length;return i=!i&&ae.isArray(r)?r.length:i,a?(ae.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!s):(r[i]&&ae.isObject(r[i])||(r[i]=[]),t(e,n,r[i],o)&&ae.isArray(r[i])&&(r[i]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],t[i]=e[i];return t}(r[i])),!s)}if(ae.isFormData(e)&&ae.isFunction(e.entries)){const n={};return ae.forEachEntry(e,((e,r)=>{t(function(e){return ae.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null},Ne={transitional:xe,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=ae.isObject(e);if(o&&ae.isHTMLForm(e)&&(e=new FormData(e)),ae.isFormData(e))return r?JSON.stringify(Ae(e)):e;if(ae.isArrayBuffer(e)||ae.isBuffer(e)||ae.isStream(e)||ae.isFile(e)||ae.isBlob(e)||ae.isReadableStream(e))return e;if(ae.isArrayBufferView(e))return e.buffer;if(ae.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ye(e,new Te.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Te.isNode&&ae.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((i=ae.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ye(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e){if(ae.isString(e))try{return(0,JSON.parse)(e),ae.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Ne.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(ae.isResponse(e)||ae.isReadableStream(e))return e;if(e&&ae.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw pe.from(e,pe.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Te.classes.FormData,Blob:Te.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ae.forEach(["delete","get","head","post","put","patch"],(e=>{Ne.headers[e]={}}));const De=Ne,Le=ae.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Fe=Symbol("internals");function Be(e){return e&&String(e).trim().toLowerCase()}function Ie(e){return!1===e||null==e?e:ae.isArray(e)?e.map(Ie):String(e)}function Ue(e,t,n,r,o){return ae.isFunction(r)?r.call(this,t,n):(o&&(t=n),ae.isString(t)?ae.isString(r)?-1!==t.indexOf(r):ae.isRegExp(r)?r.test(t):void 0:void 0)}class qe{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=Be(t);if(!o)throw new Error("header name must be a non-empty string");const i=ae.findKey(r,o);(!i||void 0===r[i]||!0===n||void 0===n&&!1!==r[i])&&(r[i||t]=Ie(e))}const i=(e,t)=>ae.forEach(e,((e,n)=>o(e,n,t)));if(ae.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if(ae.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))i((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&Le[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(ae.isHeaders(e))for(const[t,r]of e.entries())o(r,t,n);else null!=e&&o(t,e,n);return this}get(e,t){if(e=Be(e)){const n=ae.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(ae.isFunction(t))return t.call(this,e,n);if(ae.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Be(e)){const n=ae.findKey(this,e);return!(!n||void 0===this[n]||t&&!Ue(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=Be(e)){const o=ae.findKey(n,e);!o||t&&!Ue(0,n[o],o,t)||(delete n[o],r=!0)}}return ae.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Ue(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return ae.forEach(this,((r,o)=>{const i=ae.findKey(n,o);if(i)return t[i]=Ie(r),void delete t[o];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();s!==o&&delete t[o],t[s]=Ie(r),n[s]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return ae.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&ae.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[Fe]=this[Fe]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Be(e);t[r]||(function(e,t){const n=ae.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return ae.isArray(e)?e.forEach(r):r(e),this}}qe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ae.reduceDescriptors(qe.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),ae.freezeMethods(qe);const Me=qe;function We(e,t){const n=this||De,r=t||n,o=Me.from(r.headers);let i=r.data;return ae.forEach(e,(function(e){i=e.call(n,i,o.normalize(),t?t.status:void 0)})),o.normalize(),i}function ze(e){return!(!e||!e.__CANCEL__)}function He(e,t,n){pe.call(this,null==e?"canceled":e,pe.ERR_CANCELED,t,n),this.name="CanceledError"}ae.inherits(He,pe,{__CANCEL__:!0});const Je=He;function Ke(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new pe("Request failed with status code "+n.status,[pe.ERR_BAD_REQUEST,pe.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const $e=(e,t,n=3)=>{let r=0;const o=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,i=0,s=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[s];o||(o=c),n[i]=a,r[i]=c;let u=s,p=0;for(;u!==i;)p+=n[u++],u%=e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),c-o<t)return;const d=l&&c-l;return d?Math.round(1e3*p/d):void 0}}(50,250);return function(e,t){let n,r,o=0,i=1e3/t;const s=(t,i=Date.now())=>{o=i,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-o;a>=i?s(e,t):(n=e,r||(r=setTimeout((()=>{r=null,s(n)}),i-a)))},()=>n&&s(n)]}((n=>{const i=n.loaded,s=n.lengthComputable?n.total:void 0,a=i-r,c=o(a);r=i,e({loaded:i,total:s,progress:s?i/s:void 0,bytes:a,rate:c||void 0,estimated:c&&s&&i<=s?(s-i)/c:void 0,event:n,lengthComputable:null!=s,[t?"download":"upload"]:!0})}),n)},Ge=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ve=e=>(...t)=>ae.asap((()=>e(...t))),Xe=Te.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Te.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Te.origin),Te.navigator&&/(msie|trident)/i.test(Te.navigator.userAgent)):()=>!0,Ye=Te.hasStandardBrowserEnv?{write(e,t,n,r,o,i){const s=[e+"="+encodeURIComponent(t)];ae.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),ae.isString(r)&&s.push("path="+r),ae.isString(o)&&s.push("domain="+o),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ze(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Qe=e=>e instanceof Me?{...e}:e;function et(e,t){t=t||{};const n={};function r(e,t,n,r){return ae.isPlainObject(e)&&ae.isPlainObject(t)?ae.merge.call({caseless:r},e,t):ae.isPlainObject(t)?ae.merge({},t):ae.isArray(t)?t.slice():t}function o(e,t,n,o){return ae.isUndefined(t)?ae.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function i(e,t){if(!ae.isUndefined(t))return r(void 0,t)}function s(e,t){return ae.isUndefined(t)?ae.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,o,i){return i in t?r(n,o):i in e?r(void 0,n):void 0}const c={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t,n)=>o(Qe(e),Qe(t),0,!0)};return ae.forEach(Object.keys(Object.assign({},e,t)),(function(r){const i=c[r]||o,s=i(e[r],t[r],r);ae.isUndefined(s)&&i!==a||(n[r]=s)})),n}const tt=e=>{const t=et({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:i,xsrfCookieName:s,headers:a,auth:c}=t;if(t.headers=a=Me.from(a),t.url=Oe(Ze(t.baseURL,t.url),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ae.isFormData(r))if(Te.hasStandardBrowserEnv||Te.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(Te.hasStandardBrowserEnv&&(o&&ae.isFunction(o)&&(o=o(t)),o||!1!==o&&Xe(t.url))){const e=i&&s&&Ye.read(s);e&&a.set(i,e)}return t},nt="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=tt(e);let o=r.data;const i=Me.from(r.headers).normalize();let s,a,c,l,u,{responseType:p,onUploadProgress:d,onDownloadProgress:f}=r;function h(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let m=new XMLHttpRequest;function y(){if(!m)return;const r=Me.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Ke((function(e){t(e),h()}),(function(e){n(e),h()}),{data:p&&"text"!==p&&"json"!==p?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(y)},m.onabort=function(){m&&(n(new pe("Request aborted",pe.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new pe("Network Error",pe.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||xe;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new pe(t,o.clarifyTimeoutError?pe.ETIMEDOUT:pe.ECONNABORTED,e,m)),m=null},void 0===o&&i.setContentType(null),"setRequestHeader"in m&&ae.forEach(i.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),ae.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),p&&"json"!==p&&(m.responseType=r.responseType),f&&([c,u]=$e(f,!0),m.addEventListener("progress",c)),d&&m.upload&&([a,l]=$e(d),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(s=t=>{m&&(n(!t||t.type?new Je(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const b=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);b&&-1===Te.protocols.indexOf(b)?n(new pe("Unsupported protocol "+b+":",pe.ERR_BAD_REQUEST,e)):m.send(o||null)}))},rt=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,s();const t=e instanceof Error?e:this.reason;r.abort(t instanceof pe?t:new Je(t instanceof Error?t.message:t))}};let i=t&&setTimeout((()=>{i=null,o(new pe(`timeout ${t} of ms exceeded`,pe.ETIMEDOUT))}),t);const s=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)})),e=null)};e.forEach((e=>e.addEventListener("abort",o)));const{signal:a}=r;return a.unsubscribe=()=>ae.asap(s),a}},ot=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},it=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}}(e))yield*ot(n,t)}(e,t);let i,s=0,a=e=>{i||(i=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return a(),void e.close();let i=r.byteLength;if(n){let e=s+=i;n(e)}e.enqueue(new Uint8Array(r))}catch(e){throw a(e),e}},cancel:e=>(a(e),o.return())},{highWaterMark:2})},st="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,at=st&&"function"==typeof ReadableStream,ct=st&&("function"==typeof TextEncoder?(lt=new TextEncoder,e=>lt.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var lt;const ut=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},pt=at&&ut((()=>{let e=!1;const t=new Request(Te.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),dt=at&&ut((()=>ae.isReadableStream(new Response("").body))),ft={stream:dt&&(e=>e.body)};var ht;st&&(ht=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!ft[e]&&(ft[e]=ae.isFunction(ht[e])?t=>t[e]():(t,n)=>{throw new pe(`Response type '${e}' is not supported`,pe.ERR_NOT_SUPPORT,n)})})));const mt=st&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:i,timeout:s,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:p="same-origin",fetchOptions:d}=tt(e);l=l?(l+"").toLowerCase():"text";let f,h=rt([o,i&&i.toAbortSignal()],s);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let y;try{if(c&&pt&&"get"!==n&&"head"!==n&&0!==(y=await(async(e,t)=>{const n=ae.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(ae.isBlob(e))return e.size;if(ae.isSpecCompliantForm(e)){const t=new Request(Te.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return ae.isArrayBufferView(e)||ae.isArrayBuffer(e)?e.byteLength:(ae.isURLSearchParams(e)&&(e+=""),ae.isString(e)?(await ct(e)).byteLength:void 0)})(t):n})(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(ae.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=Ge(y,$e(Ve(c)));r=it(n.body,65536,e,t)}}ae.isString(p)||(p=p?"include":"omit");const o="credentials"in Request.prototype;f=new Request(t,{...d,signal:h,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:o?p:void 0});let i=await fetch(f);const s=dt&&("stream"===l||"response"===l);if(dt&&(a||s&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=i[t]}));const t=ae.toFiniteNumber(i.headers.get("content-length")),[n,r]=a&&Ge(t,$e(Ve(a),!0))||[];i=new Response(it(i.body,65536,n,(()=>{r&&r(),m&&m()})),e)}l=l||"text";let b=await ft[ae.findKey(ft,l)||"text"](i,e);return!s&&m&&m(),await new Promise(((t,n)=>{Ke(t,n,{data:b,headers:Me.from(i.headers),status:i.status,statusText:i.statusText,config:e,request:f})}))}catch(t){if(m&&m(),t&&"TypeError"===t.name&&/fetch/i.test(t.message))throw Object.assign(new pe("Network Error",pe.ERR_NETWORK,e,f),{cause:t.cause||t});throw pe.from(t,t&&t.code,e,f)}}),yt={http:null,xhr:nt,fetch:mt};ae.forEach(yt,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const bt=e=>`- ${e}`,gt=e=>ae.isFunction(e)||null===e||!1===e,vt=e=>{e=ae.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let i=0;i<t;i++){let t;if(n=e[i],r=n,!gt(n)&&(r=yt[(t=String(n)).toLowerCase()],void 0===r))throw new pe(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+i]=r}if(!r){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));let n=t?e.length>1?"since :\n"+e.map(bt).join("\n"):" "+bt(e[0]):"as no adapter specified";throw new pe("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function wt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Je(null,e)}function jt(e){return wt(e),e.headers=Me.from(e.headers),e.data=We.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),vt(e.adapter||De.adapter)(e).then((function(t){return wt(e),t.data=We.call(e,e.transformResponse,t),t.headers=Me.from(t.headers),t}),(function(t){return ze(t)||(wt(e),t&&t.response&&(t.response.data=We.call(e,e.transformResponse,t.response),t.response.headers=Me.from(t.response.headers))),Promise.reject(t)}))}const Ot={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Ot[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const _t={};Ot.transitional=function(e,t,n){function r(e,t){return"[Axios v1.7.9] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,i)=>{if(!1===e)throw new pe(r(o," has been removed"+(t?" in "+t:"")),pe.ERR_DEPRECATED);return t&&!_t[o]&&(_t[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,i)}},Ot.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const xt={assertOptions:function(e,t,n){if("object"!=typeof e)throw new pe("options must be an object",pe.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const i=r[o],s=t[i];if(s){const t=e[i],n=void 0===t||s(t,i,e);if(!0!==n)throw new pe("option "+i+" must be "+n,pe.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new pe("Unknown option "+i,pe.ERR_BAD_OPTION)}},validators:Ot},St=xt.validators;class Et{constructor(e){this.defaults=e,this.interceptors={request:new _e,response:new _e}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const n=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}catch(e){}}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=et(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&xt.assertOptions(n,{silentJSONParsing:St.transitional(St.boolean),forcedJSONParsing:St.transitional(St.boolean),clarifyTimeoutError:St.transitional(St.boolean)},!1),null!=r&&(ae.isFunction(r)?t.paramsSerializer={serialize:r}:xt.assertOptions(r,{encode:St.function,serialize:St.function},!0)),xt.assertOptions(t,{baseUrl:St.spelling("baseURL"),withXsrfToken:St.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=o&&ae.merge(o.common,o[t.method]);o&&ae.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=Me.concat(i,o);const s=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,p=0;if(!a){const e=[jt.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);p<u;)l=l.then(e[p++],e[p++]);return l}u=s.length;let d=t;for(p=0;p<u;){const e=s[p++],t=s[p++];try{d=e(d)}catch(e){t.call(this,e);break}}try{l=jt.call(this,d)}catch(e){return Promise.reject(e)}for(p=0,u=c.length;p<u;)l=l.then(c[p++],c[p++]);return l}getUri(e){return Oe(Ze((e=et(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}ae.forEach(["delete","get","head","options"],(function(e){Et.prototype[e]=function(t,n){return this.request(et(n||{},{method:e,url:t,data:(n||{}).data}))}})),ae.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(et(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Et.prototype[e]=t(),Et.prototype[e+"Form"]=t(!0)}));const Pt=Et;class kt{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new Je(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new kt((function(t){e=t})),cancel:e}}}const Ct=kt,Rt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Rt).forEach((([e,t])=>{Rt[t]=e}));const Tt=Rt,At=function e(t){const n=new Pt(t),r=b(Pt.prototype.request,n);return ae.extend(r,Pt.prototype,n,{allOwnKeys:!0}),ae.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(et(t,n))},r}(De);At.Axios=Pt,At.CanceledError=Je,At.CancelToken=Ct,At.isCancel=ze,At.VERSION="1.7.9",At.toFormData=ye,At.AxiosError=pe,At.Cancel=At.CanceledError,At.all=function(e){return Promise.all(e)},At.spread=function(e){return function(t){return e.apply(null,t)}},At.isAxiosError=function(e){return ae.isObject(e)&&!0===e.isAxiosError},At.mergeConfig=et,At.AxiosHeaders=Me,At.formToJSON=e=>Ae(ae.isHTMLForm(e)?new FormData(e):e),At.getAdapter=vt,At.HttpStatusCode=Tt,At.default=At;const Nt=At,{Axios:Dt,AxiosError:Lt,CanceledError:Ft,isCancel:Bt,CancelToken:It,VERSION:Ut,all:qt,Cancel:Mt,isAxiosError:Wt,spread:zt,toFormData:Ht,AxiosHeaders:Jt,HttpStatusCode:Kt,formToJSON:$t,getAdapter:Gt,mergeConfig:Vt}=Nt;var Xt=n(816);function Yt(e,t,n){return(t=(0,Xt.A)(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Zt(e,t,n,r,o,i,s){try{var a=e[i](s),c=a.value}catch(e){return void n(e)}a.done?t(c):Promise.resolve(c).then(r,o)}function Qt(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function s(e){Zt(i,r,o,s,a,"next",e)}function a(e){Zt(i,r,o,s,a,"throw",e)}s(void 0)}))}}var en=n(4756),tn=n.n(en);const nn=window.wp.i18n;function rn(e){try{return decodeURIComponent(e)}catch(t){return e}}function on(e="",t){if(!t||!Object.keys(t).length)return e;let n=e;const r=e.indexOf("?");return-1!==r&&(t=Object.assign(function(e){return(function(e){let t;try{t=new URL(e,"http://example.com").search.substring(1)}catch(e){}if(t)return t}(e)||"").replace(/\+/g,"%20").split("&").reduce(((e,t)=>{const[n,r=""]=t.split("=").filter(Boolean).map(rn);return n&&function(e,t,n){const r=t.length,o=r-1;for(let i=0;i<r;i++){let r=t[i];!r&&Array.isArray(e)&&(r=e.length.toString()),r=["__proto__","constructor","prototype"].includes(r)?r.toUpperCase():r;const s=!isNaN(Number(t[i+1]));e[r]=i===o?n:e[r]||(s?[]:{}),Array.isArray(e[r])&&!s&&(e[r]={...e[r]}),e=e[r]}}(e,n.replace(/\]/g,"").split("["),r),e}),Object.create(null))}(e),t),n=n.substr(0,r)),n+"?"+function(e){let t="";const n=Object.entries(e);let r;for(;r=n.shift();){let[e,o]=r;if(Array.isArray(o)||o&&o.constructor===Object){const t=Object.entries(o).reverse();for(const[r,o]of t)n.unshift([`${e}[${r}]`,o])}else void 0!==o&&(null===o&&(o=""),t+="&"+[e,o].map(encodeURIComponent).join("="))}return t.substr(1)}(t)}var sn=n(3145),an=n(7800);function cn(e){return function(e){if(Array.isArray(e))return(0,sn.A)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,an.A)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ln,un,pn=function(){var e=Qt(tn().mark((function e(t,n,r,o){var i,s;return tn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(i=console).debug.apply(i,["".concat(t," ").concat(n)].concat(cn(o?[o]:[]))),e.next=3,r;case 3:return s=e.sent,console.debug("Response",s),e.abrupt("return",s);case 6:case"end":return e.stop()}}),e)})));return function(t,n,r,o){return e.apply(this,arguments)}}(),dn=null===(ln=window.CODE_SNIPPETS)||void 0===ln?void 0:ln.restAPI.snippets,fn={headers:{"X-WP-Nonce":null===(un=window.CODE_SNIPPETS)||void 0===un?void 0:un.restAPI.nonce}},hn=function(e,t){var n=e.id,r=e.network;return on([dn,n,t].filter(Boolean).join("/"),{network:!!r||void 0})},mn=function(){var e,t,n=(e=fn,t=(0,o.useMemo)((function(){return Nt.create(e)}),[e]),(0,o.useMemo)((function(){return{get:function(e){return pn("GET",e,t.get(e))},post:function(e,n){return pn("POST",e,t.post(e,n),n)},del:function(e){return pn("DELETE",e,t.delete(e))},axiosInstance:t}}),[t])),r=n.get,i=n.post,s=n.del;return(0,o.useMemo)((function(){return{fetchAll:function(e){return r(on(dn,{network:e}))},fetch:function(e,t){return r(on("".concat(dn,"/").concat(e),{network:t}))},create:function(e){return i("".concat(dn),e)},update:function(e){return i(hn(e),e)},delete:function(e){return s(hn(e))},activate:function(e){return i(hn(e,"activate"))},deactivate:function(e){return i(hn(e,"deactivate"))},export:function(e){return r(hn(e,"export"))},exportCode:function(e){return r(hn(e,"export-code"))}}}),[r,i,s])};function yn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function bn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yn(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var gn=function(e,t,n){return void 0===n?0===e.id?(0,nn.__)("Snippet created.","code-snippets"):(0,nn.__)("Snippet updated.","code-snippets"):0===e.id&&n?(0,nn.__)("Snippet created and activated.","code-snippets"):n?"single-use"===t.scope?(0,nn.__)("Snippet updated and executed.","code-snippets"):(0,nn.__)("Snippet updated and activated.","code-snippets"):(0,nn.__)("Snippet updated and deactivated")},vn=n(4848),wn=(0,o.createContext)(void 0),jn=function(){var e=(0,o.useContext)(wn);if(void 0===e)throw Error("useSnippetForm can only be used within a SnippetForm context provider");return e},On=function(e){var t=e.children,n=e.initialSnippet,r=(0,o.useState)(n),i=(0,s.A)(r,2),a=i[0],c=i[1],l=(0,o.useState)(!1),u=(0,s.A)(l,2),f=u[0],m=u[1],b=(0,o.useState)(),g=(0,s.A)(b,2),v=g[0],w=g[1],j=(0,o.useState)(),O=(0,s.A)(j,2),_=O[0],x=O[1],S=function(e,t,n){var r=mn();return(0,o.useCallback)(function(){var o=Qt(tn().mark((function o(i,s){var a,c,l;return tn().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return t(!0),n(void 0),o.next=4,Qt(tn().mark((function e(){var n,o,a;return tn().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n=bn(bn({},i),{},{active:null!=s?s:i.active}),e.next=4,0===i.id?r.create(n):r.update(n);case 4:return o=e.sent,a=o.data,t(!1),e.abrupt("return",a.id?a:void 0);case 10:return e.prev=10,e.t0=e.catch(0),t(!1),e.abrupt("return",Wt(e.t0)?e.t0.message:void 0);case 14:case"end":return e.stop()}}),e,null,[[0,10]])})))();case 4:if(void 0!==(a=o.sent)&&"string"!=typeof a){o.next=11;break}return c=[i.id?(0,nn.__)("Could not create snippet.","code-snippets"):(0,nn.__)("Could not update snippet.","code-snippets"),null!=a?a:(0,nn.__)("The server did not send a valid response.","code-snippets")],n(["error",c.filter(Boolean).join(" ")]),o.abrupt("return",void 0);case 11:return e(bn({},a)),n(["updated",gn(i,a,s)]),i.id&&a.id&&(window.document.title=window.document.title.replace((0,nn.__)("Add New Snippet","code-snippets"),(0,nn.__)("Edit Snippet","code-snippets")),window.history.replaceState({},"",on(null===(l=window.CODE_SNIPPETS)||void 0===l?void 0:l.urls.edit,{id:a.id}))),o.abrupt("return",a);case 15:case"end":return o.stop()}}),o)})));return function(e,t){return o.apply(this,arguments)}}(),[r,n,t,e])}(c,m,w),E=(0,o.useMemo)((function(){return!p()&&function(e){return d.includes(h(e))}(a.scope)}),[a.scope]),P=(0,o.useCallback)((function(e,t){console.error("Request failed",e),m(!1),w(["error",[t,Wt(e)?e.message:""].filter(Boolean).join(" ")])}),[m,w]),k=(0,o.useCallback)((function(e){c((function(t){var n,r="object"===(0,y.A)(e)?e:e(t);return null==_||_.codemirror.setValue(r.code),null===(n=window.tinymce)||void 0===n||n.activeEditor.setContent(r.desc),r}))}),[null==_?void 0:_.codemirror]),C={snippet:a,setSnippet:c,updateSnippet:k,isReadOnly:E,isWorking:f,setIsWorking:m,currentNotice:v,setCurrentNotice:w,codeEditorInstance:_,setCodeEditorInstance:x,handleRequestError:P,submitSnippet:function(){return S(a)},submitAndActivateSnippet:function(){return S(a,!0)},submitAndDeactivateSnippet:function(){return S(a,!1)}};return(0,vn.jsx)(wn.Provider,{value:C,children:t})};const _n=window.wp.components;function xn(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Sn=["id","children","className","name","primary","small","large","type","onClick"];function En(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?En(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):En(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var kn=function(e){var t=e.id,n=e.children,r=e.className,o=e.name,i=void 0===o?"submit":o,s=e.primary,a=void 0!==s&&s,l=e.small,u=void 0!==l&&l,p=e.large,d=void 0!==p&&p,f=e.type,h=void 0===f?"button":f,m=e.onClick,y=xn(e,Sn);return(0,vn.jsx)("button",Pn(Pn({id:null!=t?t:i,name:i,type:h},y),{},{onClick:function(e){m&&(e.preventDefault(),m(e))},className:c()("button",r,{"button-primary":a,"button-large":d,"button-small":u}),children:n}))},Cn=function(e){var t=e.open,n=e.title,r=e.onConfirm,o=e.onCancel,i=e.children,s=e.confirmLabel,a=void 0===s?(0,nn.__)("OK","code-snippets"):s,c=e.cancelLabel,l=void 0===c?(0,nn.__)("Cancel","code-snippets"):c,u=e.confirmButtonClassName;return t?(0,vn.jsxs)(_n.Modal,{title:n,onRequestClose:o,closeButtonLabel:l,isDismissible:!0,onKeyDown:function(e){"Enter"===e.key&&(null==r||r())},children:[i,(0,vn.jsxs)(_n.Flex,{direction:"row",justify:"flex-end",children:[(0,vn.jsx)(_n.Button,{variant:"tertiary",onClick:o,children:l}),(0,vn.jsx)(_n.Button,{variant:"primary",onClick:r,className:u,children:a})]})]}):null},Rn=function(){var e=mn(),t=jn(),n=t.snippet,r=t.setIsWorking,i=t.isWorking,a=t.handleRequestError,c=(0,o.useState)(!1),l=(0,s.A)(c,2),u=l[0],p=l[1];return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)(kn,{name:"delete_snippet",onClick:function(){return p(!0)},disabled:i,children:(0,nn.__)("Delete","code-snippets")}),(0,vn.jsxs)(Cn,{open:u,title:(0,nn.__)("Permanently delete?","code-snippets"),confirmLabel:(0,nn.__)("Delete","code-snippets"),confirmButtonClassName:"is-destructive",onCancel:function(){return p(!1)},onConfirm:function(){p(!1),r(!0),e.delete(n).then((function(){var e;r(!1),window.location.replace(on(null===(e=window.CODE_SNIPPETS)||void 0===e?void 0:e.urls.manage,{result:"deleted"}))})).catch((function(e){return a(e,(0,nn.__)("Could not delete snippet.","code-snippets"))}))},children:[(0,vn.jsxs)("p",{children:[(0,nn.__)("You are about to permanently delete this snippet.","code-snippets")," ",(0,nn.__)("Are you sure?","code-snippets")]}),(0,vn.jsx)("p",{children:(0,vn.jsx)("strong",{children:(0,nn.__)("This action cannot be undone.","code-snippets")})})]})]})},Tn={php:["php","text/php"],html:["php","text/php"],css:["css","text/css"],js:["js","text/javascript"],json:["json","application/json"]},An=function(e,t,n){var r=t.id,o=t.name,i=t.scope,a=(0,s.A)(Tn[null!=n?n:h(i)],2),c=a[0],l=a[1],u=o.toLowerCase().replace(/[^\w-]+/g,"-").trim(),p=""===u?"snippet-".concat(r):u;!function(e,t,n){var r=document.createElement("a");r.download=t,r.href=URL.createObjectURL(new Blob([e],{type:n})),setTimeout((function(){return URL.revokeObjectURL(r.href)}),4e4),setTimeout((function(){return r.click()}),0)}(e,"".concat(p,".code-snippets.").concat(c),l)},Nn=function(){var e,t=mn(),n=jn(),r=n.snippet,o=n.isWorking,i=n.setIsWorking,s=n.handleRequestError,a=function(e){var t=e.data;i(!1),console.info("file response",e),"string"==typeof t?An(t,r):An(JSON.stringify(t,void 0,2),r,"json")};return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)(kn,{name:"export_snippet",onClick:function(){i(!0),t.export(r).then(a).catch((function(e){return s(e,(0,nn.__)("Could not download export file.","code-snippets"))}))},disabled:o,children:(0,nn.__)("Export","code-snippets")}),null!==(e=window.CODE_SNIPPETS_EDIT)&&void 0!==e&&e.enableDownloads?(0,vn.jsx)(kn,{name:"export_snippet_code",onClick:function(){t.exportCode(r).then(a).catch((function(e){return s(e,(0,nn.__)("Could not download file.","code-snippets"))}))},disabled:o,children:(0,nn.__)("Export Code","code-snippets")}):""]})},Dn=function(e){console.error(e)},Ln=["inlineButtons"],Fn=["inlineButtons"],Bn=["inlineButtons"],In=["inlineButtons"];function Un(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function qn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Un(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Un(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Mn=function(e){var t=e.inlineButtons,n=xn(e,Ln);return(0,vn.jsx)(kn,qn(qn({name:"save_snippet",type:"submit",small:t,title:t?(0,nn.__)("Save Snippet","code-snippets"):void 0},n),{},{children:(0,nn.__)("Save Changes","code-snippets")}))},Wn=function(e){var t=e.inlineButtons,n=xn(e,Fn);return(0,vn.jsx)(kn,qn(qn({name:"save_snippet_execute",title:t?(0,nn.__)("Save Snippet and Execute Once","code-snippets"):void 0},n),{},{children:t?(0,nn.__)("Execute Once","code-snippets"):(0,nn.__)("Save Changes and Execute Once","code-snippets")}))},zn=function(e){var t=e.inlineButtons,n=xn(e,Bn);return(0,vn.jsx)(kn,qn(qn({name:"save_snippet_deactivate",title:t?(0,nn.__)("Save Snippet and Deactivate","code-snippets"):void 0},n),{},{children:t?(0,nn.__)("Deactivate","code-snippets"):(0,nn.__)("Save Changes and Deactivate","code-snippets")}))},Hn=function(e){var t=e.inlineButtons,n=xn(e,In);return(0,vn.jsx)(kn,qn(qn({name:"save_snippet_activate",title:t?(0,nn.__)("Save Snippet and Activate","code-snippets"):void 0},n),{},{children:t?(0,nn.__)("Activate","code-snippets"):(0,nn.__)("Save Changes and Activate","code-snippets")}))},Jn=function(e){var t=e.snippet,n=e.disabled,r=e.onActivate,o=e.onDeactivate,i=e.inlineButtons,s=e.primaryActivate,a={small:i,type:"submit",disabled:n,inlineButtons:i};switch(!0){case t.shared_network&&l():return null;case"single-use"===t.scope:return(0,vn.jsx)(Wn,qn({onClick:r},a));case t.active:return(0,vn.jsx)(zn,qn({onClick:o},a));default:case!t.active:return(0,vn.jsx)(Hn,qn({onClick:r,primary:s},a))}},Kn=function(e){var t=e.isOpen,n=e.onClose,r=e.onSubmit,o=e.validationWarning;return(0,vn.jsx)(Cn,{open:t,title:(0,nn.__)("Snippet incomplete","code-snippets"),confirmLabel:(0,nn.__)("Continue","code-snippets"),onCancel:n,onConfirm:function(){null==r||r(),n()},children:(0,vn.jsx)("p",{children:"".concat(o," ").concat((0,nn.__)("Continue?","code-snippets"))})})},$n=function(e){var t=e.inlineButtons,n=jn(),r=n.snippet,i=n.isWorking,a=n.submitSnippet,c=n.submitAndActivateSnippet,u=n.submitAndDeactivateSnippet,p=(0,o.useState)(!1),d=(0,s.A)(p,2),f=d[0],h=d[1],m=(0,o.useState)(),y=(0,s.A)(m,2),b=y[0],g=y[1],v=function(e){var t=""===e.code.trim(),n=""===e.name.trim();switch(!0){case t&&n:return(0,nn.__)("This snippet has no code or title.","code-snippets");case t:return(0,nn.__)("This snippet has no snippet code.","code-snippets");case n:return(0,nn.__)("This snippet has no title.","code-snippets");default:return}}(r),w=function(e,t){var n;return!(t||null===(n=window.CODE_SNIPPETS_EDIT)||void 0===n||!n.activateByDefault||e.active||"single-use"===e.scope||e.shared_network&&l())}(r,t),j=function(e){v?(h(!0),g((function(){return e}))):e().then((function(){})).catch(Dn)};return(0,vn.jsxs)(vn.Fragment,{children:[w?null:(0,vn.jsx)(Mn,{primary:!t,onClick:function(){return j(a)},disabled:i,inlineButtons:t}),(0,vn.jsx)(Jn,{snippet:r,disabled:i,inlineButtons:t,primaryActivate:w,onActivate:function(){return j(c)},onDeactivate:function(){return j(u)}}),w?(0,vn.jsx)(Mn,{onClick:function(){return j(a)},disabled:i,inlineButtons:t}):null,(0,vn.jsx)(Kn,{isOpen:f,validationWarning:v,onSubmit:b,onClose:function(){h(!1),g(void 0)}})]})},Gn=function(){var e=jn(),t=e.snippet,n=e.isWorking;return(0,vn.jsxs)("p",{className:"submit",children:[(0,vn.jsx)($n,{}),t.id?(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)(Nn,{}),(0,vn.jsx)(Rn,{})]}):"",n?(0,vn.jsx)(_n.Spinner,{}):""]})},Vn=Yt(Yt(Yt({},"2",39),"6",69),"200",119),Xn=function(){var e=(0,o.useState)("6"),t=(0,s.A)(e,2),n=t[0],r=t[1];return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("p",{children:(0,vn.jsx)("strong",{children:(0,nn.__)("How many websites do you plan to use Code Snippets on?","code-snippets")})}),(0,vn.jsx)("p",{children:(0,nn.__)("We offer three distinct plans, each tailored to meet your needs.","code-snippets")}),(0,vn.jsx)("p",{className:"upgrade-plans",children:Object.keys(Vn).map((function(e){return(0,vn.jsxs)("label",{children:[(0,vn.jsx)("input",{type:"radio",checked:e===n.toString(),onClick:function(){return r(e)}})," ",(0,nn.sprintf)((0,nn._n)("%d site","%d sites",Number(e),"code-snippets"),e)]},"".concat(e,"-sites"))}))}),(0,vn.jsxs)("p",{className:"action-buttons",children:[(0,vn.jsx)("span",{className:"current-plan-cost",children:(0,nn.sprintf)((0,nn.__)("$%s per year","code-snippets"),Vn[n])}),(0,vn.jsx)(_n.ExternalLink,{className:"button button-primary button-large",href:"https://checkout.freemius.com/mode/dialog/plugin/10565/plan/17873/licenses/".concat(n,"/"),children:(0,nn.__)("Upgrade Now","code-snippets")})]})]})},Yn=function(e){var t=e.nextTab;return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsxs)("p",{children:[(0,nn.__)("You are using the free version of Code Snippets.","code-snippets")," ",(0,nn.__)("Upgrade to Code Snippets Pro to unleash its full potential:","code-snippets"),(0,vn.jsxs)("ul",{children:[(0,vn.jsxs)("li",{children:[(0,vn.jsx)("strong",{children:(0,nn.__)("CSS stylesheet snippets: ","code-snippets")}),(0,nn.__)("Craft impeccable websites with advanced CSS snippets.","code-snippets")]}),(0,vn.jsxs)("li",{children:[(0,vn.jsx)("strong",{children:(0,nn.__)("JavaScript snippets: ","code-snippets")}),(0,nn.__)("Enhance user interaction with the power of JavaScript.","code-snippets")]}),(0,vn.jsxs)("li",{children:[(0,vn.jsx)("strong",{children:(0,nn.__)("Specialized Elementor widgets: ","code-snippets")}),(0,nn.__)("Easily customize your site with Elementor widgets.","code-snippets")]}),(0,vn.jsxs)("li",{children:[(0,vn.jsx)("strong",{children:(0,nn.__)("Integration with block editor: ","code-snippets")}),(0,nn.__)("Seamlessly incorporate your snippets within the block editor.","code-snippets")]}),(0,vn.jsxs)("li",{children:[(0,vn.jsx)("strong",{children:(0,nn.__)("WP-CLI snippet commands: ","code-snippets")}),(0,nn.__)("Access and control your snippets directly from the command line.","code-snippets")]}),(0,vn.jsxs)("li",{children:[(0,vn.jsx)("strong",{children:(0,nn.__)("Premium support: ","code-snippets")}),(0,nn.__)("Direct access to our team. We're happy to help!","code-snippets")]})]}),(0,nn.__)("…and so much more!","code-snippets")]}),(0,vn.jsxs)("p",{className:"action-buttons",children:[(0,vn.jsx)(_n.ExternalLink,{className:"button button-secondary",href:"https://codesnippets.pro/pricing/",children:(0,nn.__)("Learn More","code-snippets")}),(0,vn.jsxs)("button",{className:"button button-primary button-large",onClick:t,children:[(0,nn.__)("See Plans","code-snippets"),(0,vn.jsx)("span",{className:"dashicons dashicons-arrow-right"})]})]})]})},Zn=function(e){var t,n=e.isOpen,r=e.setIsOpen,i=(0,o.useState)(0),a=(0,s.A)(i,2),c=a[0],l=a[1];return n?(0,vn.jsxs)(_n.Modal,{title:"",className:"code-snippets-upgrade-dialog",onRequestClose:function(){r(!1),l(0)},children:[(0,vn.jsxs)("h1",{className:"logo",children:[(0,vn.jsx)("img",{src:"".concat(null===(t=window.CODE_SNIPPETS)||void 0===t?void 0:t.urls.plugin,"/assets/icon.svg"),alt:""}),(0,nn.__)("Code Snippets Pro","code-snippets")]}),0===c?(0,vn.jsx)(Yn,{nextTab:function(){return l(1)}}):(0,vn.jsx)(Xn,{})]}):null};const Qn=window.wp.domReady;var er=n.n(Qn);function tr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function nr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var rr="snippet_description",or=[["bold","italic","underline","strikethrough","blockquote","bullist","numlist","alignleft","aligncenter","alignright","link","wp_adv","code_snippets"],["formatselect","forecolor","pastetext","removeformat","charmap","outdent","indent","undo","redo","spellchecker"]],ir=function(){var e,t=jn(),n=t.snippet,r=t.setSnippet,i=t.isReadOnly,s=(0,o.useCallback)((function(e){return r((function(t){return nr(nr({},t),{},{desc:e})}))}),[r]);return(0,o.useEffect)((function(){er()((function(){return function(e){var t,n;null===(t=window.wp.editor)||void 0===t||t.initialize(rr,{mediaButtons:null===(n=window.CODE_SNIPPETS_EDIT)||void 0===n?void 0:n.descEditorOptions.mediaButtons,quicktags:!0,tinymce:{toolbar:or.map((function(e){return e.join(" ")})),setup:function(t){t.on("change",(function(){return e(t.getContent())}))}}})}(s)}))}),[s]),null!==(e=window.CODE_SNIPPETS_EDIT)&&void 0!==e&&e.enableDescription?(0,vn.jsxs)("div",{className:"snippet-description-container",children:[(0,vn.jsx)("h2",{children:(0,vn.jsx)("label",{htmlFor:rr,children:(0,nn.__)("Description","code-snippets")})}),(0,vn.jsx)("textarea",{id:rr,className:"wp-editor-area",onChange:function(e){return s(e.target.value)},autoComplete:"off",disabled:i,rows:window.CODE_SNIPPETS_EDIT.descEditorOptions.rows,cols:40,children:n.desc})]}):null};function sr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ar(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?sr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var cr=function(){var e=jn(),t=e.snippet,n=e.setSnippet,r=e.isReadOnly;return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("h2",{className:"screen-reader-text",children:(0,nn.__)("Sharing Settings","code-snippets")}),(0,vn.jsx)("p",{className:"snippet-sharing-setting",children:(0,vn.jsxs)("label",{htmlFor:"snippet_sharing",children:[(0,vn.jsx)("input",{id:"snippet_sharing",name:"snippet_sharing",type:"checkbox",checked:Boolean(t.shared_network),disabled:r,onChange:function(e){return n((function(t){return ar(ar({},t),{},{shared_network:e.target.checked})}))}}),(0,nn.__)("Allow this snippet to be activated on individual sites on the network","code-snippets")]})})]})};function lr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ur(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var pr=function(){var e=jn(),t=e.snippet,n=e.setSnippet,r=e.isReadOnly;return(0,vn.jsx)("div",{id:"titlediv",children:(0,vn.jsxs)("div",{id:"titlewrap",children:[(0,vn.jsx)("label",{htmlFor:"title",className:"screen-reader-text",children:(0,nn.__)("Name","code-snippets")}),(0,vn.jsx)("input",{id:"title",type:"text",name:"snippet_name",autoComplete:"off",value:t.name,disabled:r,placeholder:(0,nn.__)("Enter title here","code-snippets"),onChange:function(e){return n((function(t){return ur(ur({},t),{},{name:e.target.value})}))}})]})})};function dr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function fr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?dr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var hr=function(){var e=jn(),t=e.snippet,n=e.setSnippet,r=e.isReadOnly;return"html"===h(t)?null:(0,vn.jsxs)("p",{className:"snippet-priority",title:(0,nn.__)("Snippets with a lower priority number will run before those with a higher number.","code-snippets"),children:[(0,vn.jsx)("label",{htmlFor:"snippet_priority",children:"".concat((0,nn.__)("Priority","code-snippets")," ")}),(0,vn.jsx)("input",{type:"number",id:"snippet_priority",name:"snippet_priority",value:t.priority,disabled:r,onChange:function(e){return n((function(t){return fr(fr({},t),{},{priority:parseInt(e.target.value,10)})}))}})]})},mr=["php","html","css","js"],yr={php:["global","admin","front-end","single-use"],html:["content","head-content","footer-content"],css:["admin-css","site-css"],js:["site-head-js","site-footer-js"]},br=function(e,t){return"[".concat([e].concat(cn(Object.entries(t).filter((function(e){var t=(0,s.A)(e,2)[1];return Boolean(t)})).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return"boolean"==typeof r?n:"".concat(n,"=").concat(JSON.stringify(r))})))).filter(Boolean).join(" "),"]")},gr=["text","copyIcon","successIcon"];function vr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var wr=function(e){var t=e.text,n=e.copyIcon,r=void 0===n?"clipboard":n,i=e.successIcon,a=void 0===i?"yes":i,c=xn(e,gr),l=(0,o.useState)(!1),u=(0,s.A)(l,2),p=u[0],d=u[1],f=window.navigator.clipboard;return f?(0,vn.jsx)("a",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?vr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({href:"#",className:"code-snippets-copy-text dashicons dashicons-".concat(p?a:r),onClick:function(e){e.preventDefault(),null==f||f.writeText(t).then((function(){d(!0),setTimeout((function(){return d(!1)}),3e3)})).catch(Dn)}},c)):null};function jr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Or(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _r="code_snippet",xr={global:"admin-site",admin:"admin-tools","front-end":"admin-appearance","single-use":"clock",content:"shortcode","head-content":"editor-code","footer-content":"editor-code","admin-css":"dashboard","site-css":"admin-customizer","site-head-js":"media-code","site-footer-js":"media-code"},Sr={global:(0,nn.__)("Run snippet everywhere","code-snippets"),admin:(0,nn.__)("Only run in administration area","code-snippets"),"front-end":(0,nn.__)("Only run on site front-end","code-snippets"),"single-use":(0,nn.__)("Only run once","code-snippets"),content:(0,nn.__)("Only display when inserted into a post or page.","code-snippets"),"head-content":(0,nn.__)("Display in site <head> section.","code-snippets"),"footer-content":(0,nn.__)("Display at the end of the <body> section, in the footer.","code-snippets"),"site-css":(0,nn.__)("Site front-end styles","code-snippets"),"admin-css":(0,nn.__)("Administration area styles","code-snippets"),"site-footer-js":(0,nn.__)("Load JS at the end of the <body> section","code-snippets"),"site-head-js":(0,nn.__)("Load JS in the <head> section","code-snippets")},Er=function(e){var t=e.atts;return(0,vn.jsxs)("p",{children:[(0,vn.jsx)("code",{className:"shortcode-tag",children:br(_r,t)}),(0,vn.jsx)(wr,{title:(0,nn.__)("Copy shortcode to clipboard","code-snippets"),text:br(_r,t)})]})},Pr=function(e){var t=e.optionLabels,n=e.options,r=e.setOptions,o=e.isReadOnly;return(0,vn.jsxs)("p",{className:"html-shortcode-options",children:[(0,vn.jsx)("strong",{children:(0,nn.__)("Shortcode Options: ","code-snippets")}),t.map((function(e){var t=(0,s.A)(e,2),i=t[0],a=t[1];return(0,vn.jsxs)("label",{children:[(0,vn.jsx)("input",{type:"checkbox",value:i,checked:n[i],disabled:o,onChange:function(e){return r((function(t){return Or(Or({},t),{},Yt({},i,e.target.checked))}))}})," ".concat(a)]},i)}))]})},kr=function(){var e,t,n,r=jn(),i=r.snippet,a=r.isReadOnly,c=(0,o.useState)((function(){return{php:i.code.includes("<?"),format:!0,shortcodes:!1}})),u=(0,s.A)(c,2),p=u[0],d=u[1];return"content"===i.scope?(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsxs)("p",{className:"description",children:[(0,nn.__)("There are multiple options for inserting this snippet into a post, page or other content.","code-snippets")," ",i.id?(0,nn.__)("You can copy the below shortcode, or use the Classic Editor button, Block editor (Pro) or Elementor widget (Pro).","code-snippets"):(0,nn.__)("After saving, you can copy a shortcode, or use the Classic Editor button, Block editor (Pro) or Elementor widget (Pro).","code-snippets")," ",(0,vn.jsx)(_n.ExternalLink,{href:(0,nn.__)("https://help.codesnippets.pro/article/50-inserting-snippets","code-snippets"),children:(0,nn.__)("Learn more","code-snippets")})]}),i.id?(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)(Er,{atts:Or({id:i.id,name:(t=i.name,n=t.trim().split(/\s+/),n.length>3?"".concat(n.slice(0,3).join(" "),"…"):t),network:null!==(e=i.network)&&void 0!==e?e:l()},p)}),(0,vn.jsx)(Pr,{options:p,setOptions:d,isReadOnly:a,optionLabels:[["php",(0,nn.__)("Evaluate PHP code","code-snippets")],["format",(0,nn.__)("Add paragraphs and formatting","code-snippets")],["shortcodes",(0,nn.__)("Evaluate additional shortcode tags","code-snippets")]]})]}):null]}):null},Cr=function(){var e=jn(),t=e.snippet,n=e.setSnippet,r=e.isReadOnly;return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("h2",{className:"screen-reader-text",children:(0,nn.__)("Scope","code-snippets")}),mr.filter((function(e){return!t.id||e===h(t)})).map((function(e){return(0,vn.jsxs)("p",{className:"snippet-scope ".concat(e,"-scopes-list"),children:[yr[e].map((function(e){return(0,vn.jsxs)("label",{children:[(0,vn.jsx)("input",{type:"radio",name:"snippet_scope",value:e,checked:e===t.scope,onChange:function(t){return t.target.checked&&n((function(t){return Or(Or({},t),{},{scope:e})}))},disabled:r})," ",(0,vn.jsx)("span",{className:"dashicons dashicons-".concat(xr[e])})," ".concat(Sr[e])]},e)})),"html"===e?(0,vn.jsx)(kr,{}):null]},e)}))]})},Rr=n(5501),Tr=n(3662),Ar=function(e){var t=e.id,n=e.suggestions,r=e.onSelect;return(0,vn.jsx)("div",{className:"tagger-completion",children:(0,vn.jsx)("datalist",{id:t,children:n.map((function(e){return(0,vn.jsx)("option",{value:e,onClick:function(){return r(e)}},e)}))})})},Nr=function(e){var t=e.tags,n=e.onRemove;return(0,vn.jsx)(vn.Fragment,{children:t.map((function(e){return(0,vn.jsx)("li",{children:(0,vn.jsxs)("span",{children:[(0,vn.jsx)("span",{className:"label",children:e}),(0,vn.jsx)("a",{href:"#",className:"close",onClick:function(t){n(e),t.preventDefault()},children:"×"})]})},e)}))})},Dr=["id","tags","onChange","tagLimit","completions","addOnBlur","allowSpaces","allowDuplicates","completionMinLength"];function Lr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Fr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Lr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Lr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Br(){Br=function(e,t){return new n(e,void 0,t)};var e=RegExp.prototype,t=new WeakMap;function n(e,r,o){var i=RegExp(e,r);return t.set(i,o||t.get(e)),(0,Tr.A)(i,n.prototype)}function r(e,n){var r=t.get(n);return Object.keys(r).reduce((function(t,n){var o=r[n];if("number"==typeof o)t[n]=e[o];else{for(var i=0;void 0===e[o[i]]&&i+1<o.length;)i++;t[n]=e[o[i]]}return t}),Object.create(null))}return(0,Rr.A)(n,RegExp),n.prototype.exec=function(t){var n=e.exec.call(this,t);if(n){n.groups=r(n,this);var o=n.indices;o&&(o.groups=r(o,this))}return n},n.prototype[Symbol.replace]=function(n,o){if("string"==typeof o){var i=t.get(this);return e[Symbol.replace].call(this,n,o.replace(/\$<([^>]+)>/g,(function(e,t){var n=i[t];return"$"+(Array.isArray(n)?n.join("$"):n)})))}if("function"==typeof o){var s=this;return e[Symbol.replace].call(this,n,(function(){var e=arguments;return"object"!=(0,y.A)(e[e.length-1])&&(e=[].slice.call(e)).push(r(e,s)),o.apply(this,e)}))}return e[Symbol.replace].call(this,n,o)},Br.apply(this,arguments)}var Ir,Ur=Br(/([-\\^$[\]()+{}?*.|])/g,{specialChar:1}),qr=function(e){var t=e.id,n=e.tags,r=e.onChange,i=e.tagLimit,a=e.completions,c=e.addOnBlur,l=void 0!==c&&c,u=e.allowSpaces,p=void 0===u||u,d=e.allowDuplicates,f=void 0!==d&&d,h=e.completionMinLength,m=void 0===h?2:h,y=xn(e,Dr),b=(0,o.useRef)(null),g=(0,o.useState)(""),v=(0,s.A)(g,2),w=v[0],j=v[1],O=(0,o.useState)(!1),_=(0,s.A)(O,2),x=_[0],S=_[1],E=(0,o.useState)([]),P=(0,s.A)(E,2),k=P[0],C=P[1],R=(0,o.useState)(),T=(0,s.A)(R,2),A=T[0],N=T[1],D=function(){return Boolean(i&&0<i&&n.length>=i)},L=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:w.trim(),t=!["",'""',"''","``",void 0,null].includes(e)&&!D()&&(f||!n.includes(e));return t&&r([].concat(cn(n),[e])),j(""),t},F=function(e){return r(e?n.filter((function(t){return t!==e})):n.slice(0,-1))},B=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:w.length>=m;e&&function(e,t){return e?"function"==typeof e?e(t):Promise.resolve(e):Promise.resolve(void 0)}(a,w).then((function(e){N(k),null!=e&&e.length&&C(f?e:e.filter((function(e){return!n.includes(e)})))})).catch(Dn),S(e)};return(0,vn.jsx)("div",{className:"tagger",children:(0,vn.jsxs)("ul",{onClick:function(){var e;return null===(e=b.current)||void 0===e?void 0:e.focus()},children:[(0,vn.jsx)(Nr,{tags:n,onRemove:F}),(0,vn.jsxs)("li",{className:"tagger-new",children:[(0,vn.jsx)("input",Fr(Fr({},y),{},{id:t,type:"text",ref:b,value:w,list:"tagger-completion-".concat(x?"":"-disabled").concat(t),onBlur:function(){return l?L():void 0},onChange:function(e){return j(e.target.value)},onKeyDown:function(e){var t=e.key,n=e.ctrlKey,r=e.metaKey;switch(t){case"Enter":case",":e.preventDefault(),L();break;case"Backspace":w||(e.preventDefault(),F());break;case" ":n||r?(e.preventDefault(),B(!0)):p||(e.preventDefault(),L());break;case"Tab":D()||e.preventDefault()}},onInput:function(){x&&A&&function(e,t){if(!e.includes(t))return!1;var n=new RegExp("^".concat(t.replace(Ur,"\\$1")));return 1===e.filter((function(e){return n.test(e)})).length}(A,w.trim())?L()&&S(!1):B()}})),(0,vn.jsx)(Ar,{id:"tagger-completion-".concat(t),suggestions:k.filter((function(e){return!n.includes(e)})),onSelect:function(e){L(e),C([]),S(!1)}})]})]})})};function Mr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Wr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var zr=null===(Ir=window.CODE_SNIPPETS_EDIT)||void 0===Ir?void 0:Ir.tagOptions,Hr=function(){var e=jn(),t=e.snippet,n=e.setSnippet,r=e.isReadOnly;return null!=zr&&zr.enabled?(0,vn.jsxs)("div",{className:"snippet-tags-container",children:[(0,vn.jsx)("h2",{children:(0,vn.jsx)("label",{htmlFor:"snippet_tags",children:(0,nn.__)("Tags","code-snippets")})}),(0,vn.jsx)(qr,{id:"snippet_tags",tags:t.tags,addOnBlur:!0,disabled:r,onChange:function(e){return n((function(t){return Wr(Wr({},t),{},{tags:e})}))},completions:zr.availableTags,allowSpaces:zr.allowSpaces,placeholder:(0,nn.__)("Enter a list of tags; separated by commas.","code-snippets")})]}):null};function Jr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Kr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jr(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var $r=function(e){var t=e.classNames,n=e.onRemove,r=e.children,i=e.autoHide,s=void 0===i||i;return(0,o.useEffect)((function(){if(s){var e=setTimeout(n,5e3);return function(){return clearTimeout(e)}}}),[s,n]),(0,vn.jsxs)("div",{id:"message",className:c()("cs-sticky-notice notice fade is-dismissible",t),children:[(0,vn.jsx)(vn.Fragment,{children:r}),(0,vn.jsx)("button",{type:"button",className:"notice-dismiss",onClick:function(e){e.preventDefault(),n()},children:(0,vn.jsx)("span",{className:"screen-reader-text",children:(0,nn.__)("Dismiss notice.","code-snippets")})})]})},Gr=function(){var e=jn(),t=e.currentNotice,n=e.setCurrentNotice,r=e.snippet,o=e.setSnippet;return(0,vn.jsxs)(vn.Fragment,{children:[t?(0,vn.jsx)($r,{classNames:t[0],onRemove:function(){return n(void 0)},children:(0,vn.jsx)("p",{children:t[1]})}):null,r.code_error?(0,vn.jsx)($r,{classNames:"error",onRemove:function(){return o((function(e){return Kr(Kr({},e),{},{code_error:null})}))},autoHide:!1,children:(0,vn.jsxs)("p",{children:[(0,vn.jsx)("strong",{children:(0,nn.sprintf)((0,nn.__)("Snippet automatically deactivated due to an error on line %d:","code-snippets"),r.code_error[1])}),(0,vn.jsx)("blockquote",{children:r.code_error[0]})]})}):null]})},Vr=window.CODE_SNIPPETS_EDIT,Xr=function(){var e,t=jn(),n=t.snippet,r=t.updateSnippet,o=t.setCurrentNotice;return(0,vn.jsxs)("h1",{children:[n.id?(0,nn.__)("Edit Snippet","code-snippets"):(0,nn.__)("Add New Snippet","code-snippets"),n.id?(0,vn.jsxs)(vn.Fragment,{children:[" ",(0,vn.jsx)("a",{href:null===(e=window.CODE_SNIPPETS)||void 0===e?void 0:e.urls.addNew,className:"page-title-action",onClick:function(e){var t;e.preventDefault(),r((function(){return f()})),o(void 0),window.document.title=window.document.title.replace((0,nn.__)("Edit Snippet","code-snippets"),(0,nn.__)("Add New Snippet","code-snippets")),window.history.replaceState({},"",null===(t=window.CODE_SNIPPETS)||void 0===t?void 0:t.urls.addNew)},children:(0,nn._x)("Add New","snippet","code-snippets")})]}):null,(null==Vr?void 0:Vr.pageTitleActions)&&Object.entries(Vr.pageTitleActions).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("a",{href:r,className:"page-title-action",children:n},n)," "]})}))]})},Yr=(n(2949),{Cmd:(0,nn._x)("Cmd","keyboard key","code-snippets"),Ctrl:(0,nn._x)("Ctrl","keyboard key","code-snippets"),Shift:(0,nn._x)("Shift","keyboard key","code-snippets"),Option:(0,nn._x)("Option","keyboard key","code-snippets"),Alt:(0,nn._x)("Alt","keyboard key","code-snippets"),Tab:(0,nn._x)("Tab","keyboard key","code-snippets"),Up:(0,nn._x)("Up","keyboard key","code-snippets"),Down:(0,nn._x)("Down","keyboard key","code-snippets"),A:(0,nn._x)("A","keyboard key","code-snippets"),D:(0,nn._x)("D","keyboard key","code-snippets"),F:(0,nn._x)("F","keyboard key","code-snippets"),G:(0,nn._x)("G","keyboard key","code-snippets"),R:(0,nn._x)("R","keyboard key","code-snippets"),S:(0,nn._x)("S","keyboard key","code-snippets"),Y:(0,nn._x)("Y","keyboard key","code-snippets"),Z:(0,nn._x)("Z","keyboard key","code-snippets"),"/":(0,nn._x)("/","keyboard key","code-snippets"),"[":(0,nn._x)("]","keyboard key","code-snippets"),"]":(0,nn._x)("]","keyboard key","code-snippets")}),Zr={saveChanges:{label:(0,nn.__)("Save changes","code-snippets"),mod:"Cmd",key:"S"},selectAll:{label:(0,nn.__)("Select all","code-snippets"),mod:"Cmd",key:"A"},beginSearch:{label:(0,nn.__)("Begin searching","code-snippets"),mod:"Cmd",key:"F"},findNext:{label:(0,nn.__)("Find next","code-snippets"),mod:"Cmd",key:"G"},findPrevious:{label:(0,nn.__)("Find previous","code-snippets"),mod:["Shift","Cmd"],key:"G"},replace:{label:(0,nn.__)("Replace","code-snippets"),mod:["Shift","Cmd"],key:"F"},replaceAll:{label:(0,nn.__)("Replace all","code-snippets"),mod:["Shift","Cmd","Option"],key:"R"},search:{label:(0,nn.__)("Persistent search","code-snippets"),mod:"Alt",key:"F"},toggleComment:{label:(0,nn.__)("Toggle comment","code-snippets"),mod:"Cmd",key:"/"},swapLineUp:{label:(0,nn.__)("Swap line up","code-snippets"),mod:"Option",key:"Up"},swapLineDown:{label:(0,nn.__)("Swap line down","code-snippets"),mod:"Option",key:"Down"},autoIndent:{label:(0,nn.__)("Auto-indent current line or selection","code-snippets"),mod:"Shift",key:"Tab"}},Qr=(0,nn._x)("-","keyboard shortcut separator","code-snippets"),eo=function(e){var t=e.modifier;switch(t){case"Ctrl":case"Cmd":return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("kbd",{className:"pc-key",children:Yr.Ctrl}),(0,vn.jsx)("kbd",{className:"mac-key",children:Yr.Cmd}),Qr]});case"Option":return(0,vn.jsxs)("span",{className:"mac-key",children:[(0,vn.jsx)("kbd",{className:"mac-key",children:Yr.Option}),Qr]});default:return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("kbd",{children:Yr[t]}),Qr]})}},to=function(e){var t=e.editorTheme;return(0,vn.jsxs)("div",{className:"snippet-editor-help",children:[(0,vn.jsx)("div",{className:"editor-help-tooltip cm-s-".concat(t),children:(0,nn._x)("?","help tooltip","code-snippets")}),(0,vn.jsx)("div",{className:c()("editor-help-text",{"platform-mac":u()}),children:(0,vn.jsx)("table",{children:Object.entries(Zr).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1],o=r.label,i=r.mod,a=r.key;return(0,vn.jsxs)("tr",{children:[(0,vn.jsx)("td",{children:o}),(0,vn.jsxs)("td",{children:[(Array.isArray(i)?i:[i]).map((function(e){return(0,vn.jsx)("span",{children:(0,vn.jsx)(eo,{modifier:e})},e)})),(0,vn.jsx)("kbd",{children:Yr[a]})]})]},n)}))})})]})};function no(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ro(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?no(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):no(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var oo=function(){var e,t,n=jn(),r=n.snippet,i=n.setSnippet,s=n.codeEditorInstance,a=n.setCodeEditorInstance,c=n.submitSnippet,l=(0,o.useRef)(null);return(0,o.useEffect)((function(){a((function(e){return l.current&&!e&&(e=window.wp.codeEditor.initialize(l.current)).codemirror.on("changes",(function(e){i((function(t){return ro(ro({},t),{},{code:e.getValue()})}))})),e}))}),[a,l,i]),(0,o.useEffect)((function(){if(s){var e=s.codemirror.getOption("extraKeys"),t=u()?"Cmd":"Ctrl";s.codemirror.setOption("extraKeys",ro(ro({},"object"===(0,y.A)(e)?e:void 0),{},Yt(Yt({},"".concat(t,"-S"),c),"".concat(t,"-Enter"),c)))}}),[c,s,r]),(0,vn.jsxs)("div",{className:"snippet-editor",children:[(0,vn.jsx)("textarea",{ref:l,id:"snippet_code",name:"snippet_code",rows:200,spellCheck:!1,onChange:function(e){i((function(t){return ro(ro({},t),{},{code:e.target.value})}))},children:r.code}),(0,vn.jsx)(to,{editorTheme:null!==(e=null===(t=window.CODE_SNIPPETS_EDIT)||void 0===t?void 0:t.editorTheme)&&void 0!==e?e:"default"})]})};function io(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function so(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?io(Object(n),!0).forEach((function(t){Yt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):io(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ao=function(e){var t=e.tabType,n=e.label,r=e.currentType,o=e.updateScope,i=e.openUpgradeDialog;return(0,vn.jsxs)("a",so(so({"data-snippet-type":t,className:c()({"nav-tab":!0,"nav-tab-active":t===r,"nav-tab-inactive":m(t)&&!p()})},m(t)&&!p()?{title:(0,nn.__)("Learn more about Code Snippets Pro.","code-snippets"),href:"https://codesnippets.pro/pricing/",target:"_blank",onClick:function(e){e.preventDefault(),i()}}:{href:on(window.location.href,{type:t}),onClick:function(e){e.preventDefault();var n=yr[t][0];o(n)}}),{},{children:["".concat(n," "),(0,vn.jsx)("span",{className:"badge",children:t})]}))},co={php:(0,nn.__)("Functions","code-snippets"),html:(0,nn.__)("Content","code-snippets"),css:(0,nn.__)("Styles","code-snippets"),js:(0,nn.__)("Scripts","code-snippets")},lo={css:"text/css",js:"javascript",php:"text/x-php",html:"application/x-httpd-php"},uo=function(e){var t=e.codeEditor,n=e.updateScope,r=e.snippetType,i=e.openUpgradeDialog;return(0,o.useEffect)((function(){t.setOption("lint","php"===r||"css"===r),r in lo&&(t.setOption("mode",lo[r]),t.refresh())}),[t,r]),(0,vn.jsxs)("h2",{className:"nav-tab-wrapper",id:"snippet-type-tabs",children:[mr.map((function(e){return(0,vn.jsx)(ao,{tabType:e,label:co[e],currentType:r,updateScope:n,openUpgradeDialog:i},e)})),p()?null:(0,vn.jsxs)("a",{className:"button button-large nav-tab-button nav-tab-inactive go-pro-button",href:"https://codesnippets.pro/pricing/",title:"Find more about Pro",onClick:function(e){e.preventDefault(),i()},children:[(0,nn._x)("Upgrade to ","Upgrade to Pro","code-snippets"),(0,vn.jsx)("span",{className:"badge",children:(0,nn._x)("Pro","Upgrade to Pro","code-snippets")})]})]})},po=function(e){var t,n=e.openUpgradeDialog,r=jn(),o=r.snippet,i=r.setSnippet,s=r.codeEditorInstance,a=h(o);return(0,vn.jsxs)("div",{className:"snippet-code-container",children:[(0,vn.jsx)("h2",{children:(0,vn.jsxs)("label",{htmlFor:"snippet_code",children:["".concat((0,nn.__)("Code","code-snippets")," "),o.id?(0,vn.jsx)("span",{className:"snippet-type-badge","data-snippet-type":a,children:a}):null]})}),o.id||null!==(t=window.CODE_SNIPPETS_EDIT)&&void 0!==t&&t.isPreview||!s?"":(0,vn.jsx)(uo,{snippetType:a,codeEditor:s.codemirror,openUpgradeDialog:n,updateScope:function(e){i((function(t){return so(so({},t),{},{scope:e})}))}}),(0,vn.jsx)(oo,{})]})},fo=function(){var e=jn().isWorking;return(0,vn.jsxs)(vn.Fragment,{children:[e?(0,vn.jsx)(_n.Spinner,{}):"",(0,vn.jsx)($n,{inlineButtons:!0})]})},ho=function(){var e=jn().codeEditorInstance;return(0,vn.jsxs)(vn.Fragment,{children:[(0,vn.jsx)("label",{htmlFor:"snippet-code-direction",className:"screen-reader-text",children:(0,nn.__)("Code Direction","code-snippets")}),(0,vn.jsxs)("select",{id:"snippet-code-direction",onChange:function(t){return null==e?void 0:e.codemirror.setOption("direction","rtl"===t.target.value?"rtl":"ltr")},children:[(0,vn.jsx)("option",{value:"ltr",children:(0,nn.__)("LTR","code-snippets")}),(0,vn.jsx)("option",{value:"rtl",children:(0,nn.__)("RTL","code-snippets")})]})]})},mo=function(){var e;return(0,vn.jsxs)("div",{className:"submit-inline",children:[null!==(e=window.CODE_SNIPPETS_EDIT)&&void 0!==e&&e.extraSaveButtons?(0,vn.jsx)(fo,{}):null,(0,nn.isRTL)()?(0,vn.jsx)(ho,{}):null]})},yo=window.CODE_SNIPPETS_EDIT,bo=function(){var e=(0,o.useState)(!1),t=(0,s.A)(e,2),n=t[0],r=t[1],i=jn(),a=i.snippet,u=i.isReadOnly;return(0,vn.jsxs)("div",{className:"wrap",children:[(0,vn.jsx)(Xr,{}),(0,vn.jsx)(Gr,{}),(0,vn.jsxs)("div",{id:"snippet-form",className:c()("snippet-form","".concat(a.scope,"-snippet"),"".concat(h(a.scope),"-snippet"),"".concat(a.id?"saved":"new","-snippet"),"".concat(a.active?"active":"inactive","-snippet"),{"erroneous-snippet":!!a.code_error,"read-only-snippet":u}),children:[(0,vn.jsx)(pr,{}),(0,vn.jsx)(mo,{}),(0,vn.jsx)(po,{openUpgradeDialog:function(){return r(!0)}}),(0,vn.jsxs)("div",{className:"below-snippet-editor",children:[(0,vn.jsx)(Cr,{}),(0,vn.jsx)(hr,{})]}),l()?(0,vn.jsx)(cr,{}):null,null!=yo&&yo.enableDescription?(0,vn.jsx)(ir,{}):null,null!=yo&&yo.tagOptions.enabled?(0,vn.jsx)(Hr,{}):null,(0,vn.jsx)(Gn,{})]}),(0,vn.jsx)(Zn,{isOpen:n,setIsOpen:r})]})},go=function(){return(0,vn.jsx)(On,{initialSnippet:function(){var e;return null!==(e=null==yo?void 0:yo.snippet)&&void 0!==e?e:f()},children:(0,vn.jsx)(bo,{})})},vo=document.getElementById("edit-snippet-form-container");vo?(0,i.H)(vo).render((0,vn.jsx)(go,{})):console.error("Could not find snippet edit form container.")},5338:(e,t,n)=>{"use strict";var r=n(5795);t.H=r.createRoot,r.hydrateRoot},1020:(e,t,n)=>{"use strict";var r=n(1609),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,n){var r,i={},l=null,u=null;for(r in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,r)&&!c.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:o,type:e,key:l,ref:u,props:i,_owner:a.current}}t.Fragment=i,t.jsx=l,t.jsxs=l},4848:(e,t,n)=>{"use strict";e.exports=n(1020)},1609:e=>{"use strict";e.exports=window.React},5795:e=>{"use strict";e.exports=window.ReactDOM},4633:(e,t,n)=>{var r=n(3738).default;function o(){"use strict";e.exports=o=function(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},i=Object.prototype,s=i.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",p=c.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var o=t&&t.prototype instanceof w?t:w,i=Object.create(o.prototype),s=new N(r||[]);return a(i,"_invoke",{value:C(e,n,s)}),i}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var m="suspendedStart",y="suspendedYield",b="executing",g="completed",v={};function w(){}function j(){}function O(){}var _={};d(_,l,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(D([])));S&&S!==i&&s.call(S,l)&&(_=S);var E=O.prototype=w.prototype=Object.create(_);function P(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(o,i,a,c){var l=h(e[o],e,i);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==r(p)&&s.call(p,"__await")?t.resolve(p.__await).then((function(e){n("next",e,a,c)}),(function(e){n("throw",e,a,c)})):t.resolve(p).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}})}function C(e,n,r){var o=m;return function(i,s){if(o===b)throw Error("Generator is already running");if(o===g){if("throw"===i)throw s;return{value:t,done:!0}}for(r.method=i,r.arg=s;;){var a=r.delegate;if(a){var c=R(a,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===m)throw o=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=b;var l=h(e,n,r);if("normal"===l.type){if(o=r.done?g:y,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(o=g,r.method="throw",r.arg=l.arg)}}}function R(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,R(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=h(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var s=i.arg;return s?s.done?(n[e.resultName]=s.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function D(e){if(e||""===e){var n=e[l];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(s.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(r(e)+" is not iterable")}return j.prototype=O,a(E,"constructor",{value:O,configurable:!0}),a(O,"constructor",{value:j,configurable:!0}),j.displayName=d(O,p,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===j||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,O):(e.__proto__=O,d(e,p,"GeneratorFunction")),e.prototype=Object.create(E),e},n.awrap=function(e){return{__await:e}},P(k.prototype),d(k.prototype,u,(function(){return this})),n.AsyncIterator=k,n.async=function(e,t,r,o,i){void 0===i&&(i=Promise);var s=new k(f(e,t,r,o),i);return n.isGeneratorFunction(t)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},P(E),d(E,p,"Generator"),d(E,l,(function(){return this})),d(E,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=D,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var n in this)"t"===n.charAt(0)&&s.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,o){return a.type="throw",a.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=s.call(i,"catchLoc"),l=s.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&s.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;A(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:D(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},n}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},3738:e=>{function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},4756:(e,t,n)=>{var r=n(4633)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},6942:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},5501:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(3662);function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.A)(e,t)}},3662:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}n.d(t,{A:()=>r})}},e=>{e(e.s=2374)}]);
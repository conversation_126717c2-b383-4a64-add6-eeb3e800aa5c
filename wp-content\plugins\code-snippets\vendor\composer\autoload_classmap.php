<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Code_Snippets\\Active_Snippets' => $baseDir . '/php/class-active-snippets.php',
    'Code_Snippets\\Admin' => $baseDir . '/php/class-admin.php',
    'Code_Snippets\\Admin_Menu' => $baseDir . '/php/admin-menus/class-admin-menu.php',
    'Code_Snippets\\Cloud\\Cloud_API' => $baseDir . '/php/cloud/class-cloud-api.php',
    'Code_Snippets\\Cloud\\Cloud_Link' => $baseDir . '/php/cloud/class-cloud-link.php',
    'Code_Snippets\\Cloud\\Cloud_Search_List_Table' => $baseDir . '/php/cloud/class-cloud-search-list-table.php',
    'Code_Snippets\\Cloud\\Cloud_Snippet' => $baseDir . '/php/cloud/class-cloud-snippet.php',
    'Code_Snippets\\Cloud\\Cloud_Snippets' => $baseDir . '/php/cloud/class-cloud-snippets.php',
    'Code_Snippets\\Contextual_Help' => $baseDir . '/php/class-contextual-help.php',
    'Code_Snippets\\DB' => $baseDir . '/php/class-db.php',
    'Code_Snippets\\Data_Item' => $baseDir . '/php/class-data-item.php',
    'Code_Snippets\\Edit_Menu' => $baseDir . '/php/admin-menus/class-edit-menu.php',
    'Code_Snippets\\Export' => $baseDir . '/php/export/class-export.php',
    'Code_Snippets\\Export_Attachment' => $baseDir . '/php/export/class-export-attachment.php',
    'Code_Snippets\\Front_End' => $baseDir . '/php/front-end/class-front-end.php',
    'Code_Snippets\\Import' => $baseDir . '/php/export/class-import.php',
    'Code_Snippets\\Import_Menu' => $baseDir . '/php/admin-menus/class-import-menu.php',
    'Code_Snippets\\Licensing' => $baseDir . '/php/class-licensing.php',
    'Code_Snippets\\List_Table' => $baseDir . '/php/class-list-table.php',
    'Code_Snippets\\Manage_Menu' => $baseDir . '/php/admin-menus/class-manage-menu.php',
    'Code_Snippets\\Plugin' => $baseDir . '/php/class-plugin.php',
    'Code_Snippets\\REST_API\\Snippets_REST_Controller' => $baseDir . '/php/rest-api/class-snippets-rest-controller.php',
    'Code_Snippets\\Settings\\Setting_Field' => $baseDir . '/php/settings/class-setting-field.php',
    'Code_Snippets\\Settings_Menu' => $baseDir . '/php/admin-menus/class-settings-menu.php',
    'Code_Snippets\\Snippet' => $baseDir . '/php/class-snippet.php',
    'Code_Snippets\\Upgrade' => $baseDir . '/php/class-upgrade.php',
    'Code_Snippets\\Validator' => $baseDir . '/php/class-validator.php',
    'Code_Snippets\\Welcome_API' => $baseDir . '/php/class-welcome-api.php',
    'Code_Snippets\\Welcome_Menu' => $baseDir . '/php/admin-menus/class-welcome-menu.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
);

<?php
/**
 * Enhanced Input Validator for BusinessCraft AI
 * Comprehensive validation and sanitization for AI inputs and user data
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_Enhanced_Input_Validator {
    
    private $security_patterns;
    private $content_filters;
    private $rate_limits;
    
    public function __construct() {
        $this->init_security_patterns();
        $this->init_content_filters();
        $this->init_rate_limits();
    }
    
    /**
     * Initialize security patterns for threat detection
     */
    private function init_security_patterns() {
        $this->security_patterns = array(
            'prompt_injection' => array(
                '/ignore\s+previous\s+instructions/i',
                '/forget\s+everything/i',
                '/act\s+as\s+if/i',
                '/pretend\s+to\s+be/i',
                '/system\s*:\s*you\s+are/i',
                '/\[SYSTEM\]/i',
                '/\<\|system\|\>/i',
                '/jailbreak/i',
                '/DAN\s+mode/i'
            ),
            'code_injection' => array(
                '/<script[^>]*>/i',
                '/<iframe[^>]*>/i',
                '/javascript:/i',
                '/vbscript:/i',
                '/onload\s*=/i',
                '/onerror\s*=/i',
                '/eval\s*\(/i',
                '/exec\s*\(/i'
            ),
            'sql_injection' => array(
                '/union\s+select/i',
                '/drop\s+table/i',
                '/delete\s+from/i',
                '/insert\s+into/i',
                '/update\s+.*\s+set/i',
                '/or\s+1\s*=\s*1/i',
                '/and\s+1\s*=\s*1/i'
            ),
            'sensitive_data' => array(
                '/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/', // Credit card
                '/\b\d{3}-\d{2}-\d{4}\b/', // SSN
                '/password\s*[:=]\s*\S+/i',
                '/api[_-]?key\s*[:=]\s*\S+/i',
                '/secret\s*[:=]\s*\S+/i'
            )
        );
    }
    
    /**
     * Initialize content filters
     */
    private function init_content_filters() {
        $this->content_filters = array(
            'max_length' => 5000,
            'min_length' => 3,
            'allowed_languages' => array('en', 'tw', 'sw', 'yo', 'zu'),
            'blocked_domains' => array(
                'malicious-site.com',
                'spam-domain.net'
            ),
            'inappropriate_content' => array(
                'hate_speech',
                'violence',
                'adult_content',
                'illegal_activities'
            )
        );
    }
    
    /**
     * Initialize rate limiting rules
     */
    private function init_rate_limits() {
        $this->rate_limits = array(
            'requests_per_minute' => 20,
            'requests_per_hour' => 200,
            'requests_per_day' => 1000,
            'max_message_length' => 2000,
            'max_messages_per_session' => 50
        );
    }
    
    /**
     * Validate and sanitize AI input
     */
    public function validate_ai_input($input, $context = 'general') {
        $validation_result = array(
            'is_valid' => true,
            'sanitized_input' => '',
            'warnings' => array(),
            'errors' => array(),
            'security_flags' => array()
        );
        
        // Basic validation
        if (empty($input) || !is_string($input)) {
            $validation_result['is_valid'] = false;
            $validation_result['errors'][] = 'Input is required and must be a string';
            return $validation_result;
        }
        
        // Length validation
        if (strlen($input) < $this->content_filters['min_length']) {
            $validation_result['is_valid'] = false;
            $validation_result['errors'][] = 'Input is too short (minimum ' . $this->content_filters['min_length'] . ' characters)';
            return $validation_result;
        }
        
        if (strlen($input) > $this->content_filters['max_length']) {
            $validation_result['is_valid'] = false;
            $validation_result['errors'][] = 'Input is too long (maximum ' . $this->content_filters['max_length'] . ' characters)';
            return $validation_result;
        }
        
        // Security pattern detection
        $security_issues = $this->detect_security_threats($input);
        if (!empty($security_issues)) {
            $validation_result['is_valid'] = false;
            $validation_result['security_flags'] = $security_issues;
            $validation_result['errors'][] = 'Input contains potentially harmful content';
            
            // Log security incident
            $this->log_security_incident($input, $security_issues);
            return $validation_result;
        }
        
        // Content filtering
        $content_issues = $this->filter_inappropriate_content($input);
        if (!empty($content_issues)) {
            $validation_result['warnings'] = array_merge($validation_result['warnings'], $content_issues);
        }
        
        // Sanitize input
        $validation_result['sanitized_input'] = $this->sanitize_ai_input($input);
        
        // Rate limiting check
        $rate_limit_result = $this->check_rate_limits();
        if (!$rate_limit_result['allowed']) {
            $validation_result['is_valid'] = false;
            $validation_result['errors'][] = $rate_limit_result['message'];
            return $validation_result;
        }
        
        return $validation_result;
    }
    
    /**
     * Detect security threats in input
     */
    private function detect_security_threats($input) {
        $threats = array();
        
        foreach ($this->security_patterns as $threat_type => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $input)) {
                    $threats[] = $threat_type;
                    break; // One match per threat type is enough
                }
            }
        }
        
        return array_unique($threats);
    }
    
    /**
     * Filter inappropriate content
     */
    private function filter_inappropriate_content($input) {
        $issues = array();
        
        // Check for URLs
        if (preg_match_all('/https?:\/\/[^\s]+/i', $input, $matches)) {
            foreach ($matches[0] as $url) {
                $domain = parse_url($url, PHP_URL_HOST);
                if (in_array($domain, $this->content_filters['blocked_domains'])) {
                    $issues[] = 'Contains blocked domain: ' . $domain;
                }
            }
        }
        
        // Check for excessive capitalization (potential spam)
        $caps_ratio = $this->calculate_caps_ratio($input);
        if ($caps_ratio > 0.7) {
            $issues[] = 'Excessive use of capital letters detected';
        }
        
        // Check for repetitive content
        if ($this->detect_repetitive_content($input)) {
            $issues[] = 'Repetitive content detected';
        }
        
        return $issues;
    }
    
    /**
     * Sanitize AI input
     */
    private function sanitize_ai_input($input) {
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Normalize whitespace
        $input = preg_replace('/\s+/', ' ', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        // Remove potentially dangerous HTML
        $input = wp_kses($input, array(
            'em' => array(),
            'strong' => array(),
            'b' => array(),
            'i' => array(),
            'u' => array()
        ));
        
        // Escape special characters for AI safety
        $input = $this->escape_ai_special_chars($input);
        
        return $input;
    }
    
    /**
     * Escape special characters that could affect AI processing
     */
    private function escape_ai_special_chars($input) {
        $replacements = array(
            '\\' => '\\\\',
            '"' => '\\"',
            "'" => "\\'",
            '`' => '\\`',
            '$' => '\\$'
        );
        
        return str_replace(array_keys($replacements), array_values($replacements), $input);
    }
    
    /**
     * Check rate limits
     */
    private function check_rate_limits() {
        $user_id = get_current_user_id();
        $ip_address = $this->get_client_ip();
        
        // Check requests per minute
        $minute_key = 'bcai_rate_limit_minute_' . $user_id . '_' . $ip_address;
        $minute_requests = get_transient($minute_key) ?: 0;
        
        if ($minute_requests >= $this->rate_limits['requests_per_minute']) {
            return array(
                'allowed' => false,
                'message' => 'Rate limit exceeded: too many requests per minute'
            );
        }
        
        // Check requests per hour
        $hour_key = 'bcai_rate_limit_hour_' . $user_id . '_' . $ip_address;
        $hour_requests = get_transient($hour_key) ?: 0;
        
        if ($hour_requests >= $this->rate_limits['requests_per_hour']) {
            return array(
                'allowed' => false,
                'message' => 'Rate limit exceeded: too many requests per hour'
            );
        }
        
        // Update counters
        set_transient($minute_key, $minute_requests + 1, 60);
        set_transient($hour_key, $hour_requests + 1, 3600);
        
        return array('allowed' => true);
    }
    
    /**
     * Calculate capitalization ratio
     */
    private function calculate_caps_ratio($input) {
        $total_letters = preg_match_all('/[a-zA-Z]/', $input);
        $caps_letters = preg_match_all('/[A-Z]/', $input);
        
        return $total_letters > 0 ? $caps_letters / $total_letters : 0;
    }
    
    /**
     * Detect repetitive content
     */
    private function detect_repetitive_content($input) {
        $words = explode(' ', strtolower($input));
        $word_count = array_count_values($words);
        
        foreach ($word_count as $count) {
            if ($count > 5) { // Same word repeated more than 5 times
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Log security incident
     */
    private function log_security_incident($input, $threats) {
        if (function_exists('businesscraft_ai_log_security_event')) {
            businesscraft_ai_log_security_event('input_validation_threat', array(
                'threats' => $threats,
                'input_length' => strlen($input),
                'input_hash' => hash('sha256', $input),
                'user_id' => get_current_user_id(),
                'ip_address' => $this->get_client_ip(),
                'timestamp' => current_time('mysql')
            ));
        }
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}

/**
 * Global validation functions
 */

/**
 * Validate AI input with enhanced security
 */
function businesscraft_ai_validate_ai_input($input, $context = 'general') {
    static $validator = null;
    
    if ($validator === null) {
        $validator = new BusinessCraft_Enhanced_Input_Validator();
    }
    
    return $validator->validate_ai_input($input, $context);
}

/**
 * Sanitize AI input
 */
function businesscraft_ai_sanitize_ai_input($input) {
    $validation_result = businesscraft_ai_validate_ai_input($input);
    
    if ($validation_result['is_valid']) {
        return $validation_result['sanitized_input'];
    }
    
    return false;
}

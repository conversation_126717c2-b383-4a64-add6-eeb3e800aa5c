<?php
/**
 * Test Template Preview Button Functionality
 * 
 * This script tests the ChatGABI template preview button functionality
 * and checks for PHP deprecation warnings.
 */

// Load WordPress
require_once('wp-load.php');

// Set error reporting to catch deprecation warnings
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>ChatGABI Template Preview Button Test</h1>";

// Test 1: Check if preview modal exists
echo "<h2>1. Preview Modal HTML Test</h2>";
ob_start();
chatgabi_templates_page();
$output = ob_get_clean();

if (strpos($output, 'template-preview-modal') !== false) {
    echo "✅ Preview modal HTML exists<br>";
} else {
    echo "❌ Preview modal HTML missing<br>";
}

if (strpos($output, 'showTemplatePreview') !== false) {
    echo "✅ showTemplatePreview function exists<br>";
} else {
    echo "❌ showTemplatePreview function missing<br>";
}

// Test 2: Check template management functions for null handling
echo "<h2>2. Null Value Handling Test</h2>";

// Test country name function with null values
$test_cases = array(
    null => 'null',
    '' => 'empty string',
    'GH' => 'valid Ghana code',
    'INVALID' => 'invalid code'
);

foreach ($test_cases as $input => $description) {
    try {
        $result = chatgabi_get_country_name_from_code($input);
        echo "✅ Country name function handles {$description}: '{$result}'<br>";
    } catch (Exception $e) {
        echo "❌ Country name function failed with {$description}: " . $e->getMessage() . "<br>";
    }
}

// Test 3: Check template name generation with null values
echo "<h2>3. Template Name Generation Test</h2>";

$test_data = array(
    array(null, null, null),
    array('', '', ''),
    array('business-plan', 'Technology', 'GH'),
    array('marketing-strategy', null, 'KE')
);

foreach ($test_data as $index => $data) {
    try {
        $result = chatgabi_generate_template_name($data[0], $data[1], $data[2]);
        echo "✅ Template name generation test {$index}: '{$result}'<br>";
    } catch (Exception $e) {
        echo "❌ Template name generation test {$index} failed: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Check user templates function
echo "<h2>4. User Templates Function Test</h2>";

try {
    $templates = chatgabi_get_user_generated_templates();
    echo "✅ User templates function works: " . count($templates) . " templates found<br>";
} catch (Exception $e) {
    echo "❌ User templates function failed: " . $e->getMessage() . "<br>";
}

// Test 5: JavaScript functionality test
echo "<h2>5. JavaScript Functionality Test</h2>";
echo "<p>To test the preview buttons:</p>";
echo "<ol>";
echo "<li>Go to <a href='wp-admin/admin.php?page=chatgabi-templates' target='_blank'>ChatGABI Templates</a></li>";
echo "<li>Click any 'Preview' button</li>";
echo "<li>Check that a modal opens with template preview content</li>";
echo "<li>Check browser console for any JavaScript errors</li>";
echo "</ol>";

// Test 6: Check for PHP deprecation warnings
echo "<h2>6. PHP Deprecation Warnings Check</h2>";
echo "<p>Check the debug.log file for any new deprecation warnings:</p>";
echo "<pre>";
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $recent_logs = array_slice(explode("\n", $log_content), -20);
    foreach ($recent_logs as $line) {
        if (strpos($line, 'Deprecated') !== false) {
            echo htmlspecialchars($line) . "\n";
        }
    }
} else {
    echo "Debug log file not found.";
}
echo "</pre>";

echo "<h2>Test Complete</h2>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Test the preview buttons manually in the admin interface</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Verify no new PHP deprecation warnings appear</li>";
echo "</ul>";
?>

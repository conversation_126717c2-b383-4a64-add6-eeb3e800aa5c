<?php
/**
 * Real-Time Credit Feedback Widget
 * 
 * Displays real-time token estimation, credit usage,
 * and balance information to users.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$user_id = get_current_user_id();
$current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
?>

<div class="chatgabi-credit-feedback-widget" id="credit-feedback-widget">
    <!-- Credit Balance Display -->
    <div class="credit-balance-section">
        <div class="balance-header">
            <span class="balance-icon">💰</span>
            <span class="balance-label"><?php _e('Credit Balance', 'chatgabi'); ?></span>
        </div>
        <div class="balance-amount" id="current-balance">
            <?php echo number_format($current_credits, 2); ?>
        </div>
        <div class="balance-actions">
            <button class="refresh-balance-btn" onclick="refreshCreditBalance()" title="<?php _e('Refresh Balance', 'chatgabi'); ?>">
                🔄
            </button>
            <a href="<?php echo home_url('/credits/'); ?>" class="buy-credits-btn">
                <?php _e('Buy Credits', 'chatgabi'); ?>
            </a>
        </div>
    </div>

    <!-- Token Estimation Display -->
    <div class="token-estimation-section" id="token-estimation" style="display: none;">
        <div class="estimation-header">
            <span class="estimation-icon">🔢</span>
            <span class="estimation-label"><?php _e('Estimated Usage', 'chatgabi'); ?></span>
        </div>
        
        <div class="estimation-details">
            <div class="estimation-row">
                <span class="estimation-metric"><?php _e('Tokens', 'chatgabi'); ?>:</span>
                <span class="estimation-value" id="estimated-tokens">-</span>
            </div>
            <div class="estimation-row">
                <span class="estimation-metric"><?php _e('Credits', 'chatgabi'); ?>:</span>
                <span class="estimation-value" id="estimated-credits">-</span>
            </div>
            <div class="estimation-row">
                <span class="estimation-metric"><?php _e('Balance After', 'chatgabi'); ?>:</span>
                <span class="estimation-value" id="balance-after">-</span>
            </div>
        </div>

        <div class="estimation-status" id="estimation-status">
            <div class="status-indicator" id="status-indicator"></div>
            <span class="status-text" id="status-text"></span>
        </div>
    </div>

    <!-- Processing Indicator -->
    <div class="processing-section" id="processing-indicator" style="display: none;">
        <div class="processing-animation">
            <div class="processing-spinner"></div>
            <span class="processing-text"><?php _e('Processing...', 'chatgabi'); ?></span>
        </div>
        <div class="processing-details">
            <div class="processing-row">
                <span class="processing-metric"><?php _e('Tokens Used', 'chatgabi'); ?>:</span>
                <span class="processing-value" id="actual-tokens">-</span>
            </div>
            <div class="processing-row">
                <span class="processing-metric"><?php _e('Credits Deducted', 'chatgabi'); ?>:</span>
                <span class="processing-value" id="actual-credits">-</span>
            </div>
        </div>
    </div>

    <!-- Usage Statistics Toggle -->
    <div class="stats-toggle-section">
        <button class="stats-toggle-btn" onclick="toggleUsageStats()">
            <span class="stats-icon">📊</span>
            <span class="stats-text"><?php _e('Usage Stats', 'chatgabi'); ?></span>
            <span class="toggle-arrow" id="stats-arrow">▼</span>
        </button>
    </div>

    <!-- Usage Statistics Panel -->
    <div class="usage-stats-panel" id="usage-stats-panel" style="display: none;">
        <div class="stats-header">
            <span class="stats-title"><?php _e('Last 30 Days', 'chatgabi'); ?></span>
            <select id="stats-period" onchange="loadUsageStats()">
                <option value="7"><?php _e('Last 7 days', 'chatgabi'); ?></option>
                <option value="30" selected><?php _e('Last 30 days', 'chatgabi'); ?></option>
                <option value="90"><?php _e('Last 90 days', 'chatgabi'); ?></option>
            </select>
        </div>
        
        <div class="stats-grid" id="stats-content">
            <div class="stat-item">
                <div class="stat-value" id="total-conversations">-</div>
                <div class="stat-label"><?php _e('Conversations', 'chatgabi'); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="total-tokens">-</div>
                <div class="stat-label"><?php _e('Tokens Used', 'chatgabi'); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="total-credits-used">-</div>
                <div class="stat-label"><?php _e('Credits Spent', 'chatgabi'); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="avg-tokens">-</div>
                <div class="stat-label"><?php _e('Avg Tokens', 'chatgabi'); ?></div>
            </div>
        </div>

        <div class="stats-chart-container">
            <canvas id="usage-chart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Low Credit Warning -->
    <div class="low-credit-warning" id="low-credit-warning" style="display: none;">
        <div class="warning-icon">⚠️</div>
        <div class="warning-content">
            <div class="warning-title"><?php _e('Low Credit Balance', 'chatgabi'); ?></div>
            <div class="warning-message" id="warning-message"></div>
            <div class="warning-actions">
                <a href="<?php echo home_url('/credits/'); ?>" class="warning-buy-btn">
                    <?php _e('Buy More Credits', 'chatgabi'); ?>
                </a>
                <button class="warning-dismiss-btn" onclick="dismissLowCreditWarning()">
                    <?php _e('Dismiss', 'chatgabi'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables for credit feedback
window.chatgabiCreditFeedback = {
    currentBalance: <?php echo json_encode((float) $current_credits); ?>,
    isEstimating: false,
    isProcessing: false,
    lastEstimation: null,
    usageChart: null
};

// Initialize credit feedback widget
jQuery(document).ready(function() {
    initializeCreditFeedback();
});

/**
 * Initialize credit feedback functionality
 */
function initializeCreditFeedback() {
    console.log('Initializing ChatGABI Credit Feedback Widget');
    
    // Load initial usage stats
    loadUsageStats();
    
    // Set up periodic balance refresh (every 30 seconds)
    setInterval(refreshCreditBalance, 30000);
    
    // Listen for chat events
    jQuery(document).on('chatgabi:promptChanged', handlePromptChange);
    jQuery(document).on('chatgabi:responseStarted', handleResponseStarted);
    jQuery(document).on('chatgabi:responseCompleted', handleResponseCompleted);
    
    console.log('Credit Feedback Widget initialized');
}

/**
 * Handle prompt text changes for real-time estimation
 */
function handlePromptChange(event, promptText, context) {
    if (!promptText || promptText.length < 10) {
        hideTokenEstimation();
        return;
    }
    
    // Debounce estimation requests
    clearTimeout(window.estimationTimeout);
    window.estimationTimeout = setTimeout(function() {
        estimateTokenUsage(promptText, context);
    }, 500);
}

/**
 * Estimate token usage for current prompt
 */
function estimateTokenUsage(prompt, context = {}) {
    if (window.chatgabiCreditFeedback.isEstimating) {
        return;
    }
    
    window.chatgabiCreditFeedback.isEstimating = true;
    showTokenEstimation();
    
    jQuery.ajax({
        url: chatgabiAjax.ajaxUrl,
        type: 'POST',
        data: {
            action: 'chatgabi_estimate_tokens',
            nonce: chatgabiAjax.tokenNonce,
            prompt: prompt,
            language: context.language || 'en',
            country: context.country || '',
            sector: context.sector || ''
        },
        success: function(response) {
            if (response.success) {
                displayTokenEstimation(response.data);
                window.chatgabiCreditFeedback.lastEstimation = response.data;
            } else {
                console.error('Token estimation failed:', response.data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Token estimation error:', error);
        },
        complete: function() {
            window.chatgabiCreditFeedback.isEstimating = false;
        }
    });
}

/**
 * Display token estimation results
 */
function displayTokenEstimation(data) {
    const estimation = data.estimation;
    const currentBalance = data.current_credits;
    const sufficientCredits = data.sufficient_credits;
    
    // Update estimation display
    jQuery('#estimated-tokens').text(estimation.total_estimated_tokens.toLocaleString());
    jQuery('#estimated-credits').text(estimation.estimated_credits.toFixed(2));
    
    const balanceAfter = currentBalance - estimation.estimated_credits;
    jQuery('#balance-after').text(balanceAfter.toFixed(2));
    
    // Update status indicator
    const statusIndicator = jQuery('#status-indicator');
    const statusText = jQuery('#status-text');
    
    if (sufficientCredits) {
        statusIndicator.removeClass('insufficient').addClass('sufficient');
        statusText.text('<?php _e('Sufficient credits', 'chatgabi'); ?>');
    } else {
        statusIndicator.removeClass('sufficient').addClass('insufficient');
        statusText.text('<?php _e('Insufficient credits', 'chatgabi'); ?>');
        
        // Show low credit warning
        showLowCreditWarning(data.credit_shortfall);
    }
}

/**
 * Show/hide token estimation section
 */
function showTokenEstimation() {
    jQuery('#token-estimation').slideDown(200);
}

function hideTokenEstimation() {
    jQuery('#token-estimation').slideUp(200);
}

/**
 * Handle response processing start
 */
function handleResponseStarted(event, requestData) {
    window.chatgabiCreditFeedback.isProcessing = true;
    jQuery('#processing-indicator').slideDown(200);
    hideTokenEstimation();
}

/**
 * Handle response processing completion
 */
function handleResponseCompleted(event, responseData) {
    window.chatgabiCreditFeedback.isProcessing = false;
    
    if (responseData.tokens_used) {
        jQuery('#actual-tokens').text(responseData.tokens_used.toLocaleString());
    }
    
    if (responseData.credits_used) {
        jQuery('#actual-credits').text(responseData.credits_used.toFixed(2));
        
        // Update balance
        window.chatgabiCreditFeedback.currentBalance -= responseData.credits_used;
        updateBalanceDisplay();
    }
    
    // Hide processing indicator after 3 seconds
    setTimeout(function() {
        jQuery('#processing-indicator').slideUp(200);
    }, 3000);
    
    // Refresh usage stats
    loadUsageStats();
}

/**
 * Refresh credit balance
 */
function refreshCreditBalance() {
    const refreshBtn = jQuery('.refresh-balance-btn');
    refreshBtn.addClass('spinning');
    
    // In a real implementation, this would make an AJAX call
    // For now, we'll simulate a refresh
    setTimeout(function() {
        refreshBtn.removeClass('spinning');
        // Update balance display if needed
        updateBalanceDisplay();
    }, 1000);
}

/**
 * Update balance display
 */
function updateBalanceDisplay() {
    const balance = window.chatgabiCreditFeedback.currentBalance;
    jQuery('#current-balance').text(balance.toFixed(2));
    
    // Check for low balance
    if (balance < 5) {
        showLowCreditWarning(5 - balance);
    }
}

/**
 * Toggle usage statistics panel
 */
function toggleUsageStats() {
    const panel = jQuery('#usage-stats-panel');
    const arrow = jQuery('#stats-arrow');
    
    if (panel.is(':visible')) {
        panel.slideUp(300);
        arrow.text('▼');
    } else {
        panel.slideDown(300);
        arrow.text('▲');
        loadUsageStats();
    }
}

/**
 * Load usage statistics
 */
function loadUsageStats() {
    const days = jQuery('#stats-period').val() || 30;
    
    jQuery.ajax({
        url: chatgabiAjax.ajaxUrl,
        type: 'POST',
        data: {
            action: 'chatgabi_get_token_stats',
            nonce: chatgabiAjax.tokenNonce,
            days: days
        },
        success: function(response) {
            if (response.success) {
                displayUsageStats(response.data.stats);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load usage stats:', error);
        }
    });
}

/**
 * Display usage statistics
 */
function displayUsageStats(stats) {
    const summary = stats.summary;
    
    if (summary) {
        jQuery('#total-conversations').text(summary.total_conversations || 0);
        jQuery('#total-tokens').text((summary.total_tokens || 0).toLocaleString());
        jQuery('#total-credits-used').text((summary.total_credits || 0).toFixed(2));
        jQuery('#avg-tokens').text(Math.round(summary.avg_tokens_per_conversation || 0));
    }
    
    // Update chart if Chart.js is available
    if (typeof Chart !== 'undefined' && stats.daily_breakdown) {
        updateUsageChart(stats.daily_breakdown);
    }
}

/**
 * Show low credit warning
 */
function showLowCreditWarning(shortfall) {
    const warning = jQuery('#low-credit-warning');
    const message = jQuery('#warning-message');
    
    message.text(`<?php _e('You need', 'chatgabi'); ?> ${shortfall.toFixed(2)} <?php _e('more credits for this operation', 'chatgabi'); ?>`);
    warning.slideDown(300);
}

/**
 * Dismiss low credit warning
 */
function dismissLowCreditWarning() {
    jQuery('#low-credit-warning').slideUp(300);
}
</script>

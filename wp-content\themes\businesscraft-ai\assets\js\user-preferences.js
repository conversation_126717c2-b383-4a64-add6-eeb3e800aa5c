/**
 * ChatGABI User Preferences JavaScript
 * 
 * Handles user preference form interactions, AJAX submissions,
 * and dynamic content loading.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initUserPreferences();
    });

    /**
     * Initialize user preferences functionality
     */
    function initUserPreferences() {
        // Load sectors when country changes
        $('#preferred_country').on('change', loadSectorsByCountry);
        
        // Handle form submission
        $('#chatgabi-preferences-form').on('submit', handlePreferencesSubmission);
        
        // Handle reset button
        $('#reset-preferences-btn').on('click', handlePreferencesReset);
        
        // Load initial sectors if country is already selected
        const selectedCountry = $('#preferred_country').val();
        if (selectedCountry) {
            loadSectorsByCountry();
        }
        
        // Auto-save on certain field changes
        setupAutoSave();

        // Setup enhanced features
        setupAccountActions();
        setupImportExport();
        setupAnalyticsRefresh();

        console.log('ChatGABI User Preferences initialized');
    }

    /**
     * Load sectors based on selected country
     */
    function loadSectorsByCountry() {
        const countryCode = $('#preferred_country').val();
        const $sectorSelect = $('#preferred_sector');
        const currentSector = $sectorSelect.data('current-value') || $sectorSelect.val();

        if (!countryCode) {
            $sectorSelect.html('<option value="">' + chatgabiPrefs.strings.selectSector + '</option>');
            return;
        }

        // Show loading state
        $sectorSelect.html('<option value="">' + chatgabiPrefs.strings.loadingSectors + '</option>');
        $sectorSelect.prop('disabled', true);

        // Make AJAX request to get sectors
        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_get_sectors',
                country: countryCode,
                nonce: chatgabiPrefs.sectorsNonce
            },
            success: function(response) {
                if (response.success && response.data.sectors) {
                    let options = '<option value="">' + chatgabiPrefs.strings.selectSector + '</option>';
                    
                    response.data.sectors.forEach(function(sector) {
                        const selected = sector === currentSector ? ' selected' : '';
                        options += `<option value="${sector}"${selected}>${sector}</option>`;
                    });
                    
                    $sectorSelect.html(options);
                } else {
                    $sectorSelect.html('<option value="">' + chatgabiPrefs.strings.noSectorsFound + '</option>');
                    console.error('Failed to load sectors:', response.data?.message || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                $sectorSelect.html('<option value="">' + chatgabiPrefs.strings.errorLoadingSectors + '</option>');
                console.error('AJAX error loading sectors:', error);
            },
            complete: function() {
                $sectorSelect.prop('disabled', false);
            }
        });
    }

    /**
     * Handle preferences form submission
     */
    function handlePreferencesSubmission(e) {
        e.preventDefault();

        const $form = $(this);
        const $submitBtn = $('#save-preferences-btn');
        const $btnText = $submitBtn.find('.btn-text');
        const $btnLoading = $submitBtn.find('.btn-loading');

        // Show loading state
        $submitBtn.prop('disabled', true);
        $btnText.hide();
        $btnLoading.show();

        // Collect form data
        const formData = new FormData($form[0]);
        const preferences = {};

        // Convert FormData to preferences object
        for (let [key, value] of formData.entries()) {
            if (key === 'preferences_nonce') continue;
            
            // Handle checkboxes
            if ($form.find(`input[name="${key}"]`).attr('type') === 'checkbox') {
                preferences[key] = $form.find(`input[name="${key}"]`).is(':checked');
            } else {
                preferences[key] = value;
            }
        }

        // Include unchecked checkboxes as false
        $form.find('input[type="checkbox"]').each(function() {
            const name = $(this).attr('name');
            if (!(name in preferences)) {
                preferences[name] = false;
            }
        });

        // Make AJAX request
        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_save_preferences',
                nonce: chatgabiPrefs.nonce,
                preferences: preferences
            },
            success: function(response) {
                if (response.success) {
                    showMessage('success', response.data.message || chatgabiPrefs.strings.preferencesSaved);
                    
                    // Update any UI elements that depend on preferences
                    updateUIBasedOnPreferences(preferences);
                } else {
                    showMessage('error', response.data.message || chatgabiPrefs.strings.saveError);
                }
            },
            error: function(xhr, status, error) {
                showMessage('error', chatgabiPrefs.strings.networkError);
                console.error('AJAX error saving preferences:', error);
            },
            complete: function() {
                // Reset button state
                $submitBtn.prop('disabled', false);
                $btnText.show();
                $btnLoading.hide();
            }
        });
    }

    /**
     * Handle preferences reset
     */
    function handlePreferencesReset(e) {
        e.preventDefault();

        if (!confirm(chatgabiPrefs.strings.confirmReset)) {
            return;
        }

        // Reset form to default values
        const $form = $('#chatgabi-preferences-form');
        
        // Reset select fields to default values
        $('#preferred_language').val('en');
        $('#preferred_country').val('');
        $('#preferred_sector').val('');
        $('#chat_history_limit').val('10');
        $('#dashboard_layout').val('default');
        $('#currency_display').val('auto');

        // Reset checkboxes to default values (mostly true)
        $('#show_chat_history').prop('checked', true);
        $('#show_example_prompts').prop('checked', true);
        $('#auto_save_templates').prop('checked', true);
        $('#email_notifications').prop('checked', true);
        $('#opportunity_notifications').prop('checked', true);
        $('#template_generation_notifications').prop('checked', true);

        // Clear sectors dropdown
        $('#preferred_sector').html('<option value="">' + chatgabiPrefs.strings.selectSector + '</option>');

        showMessage('info', chatgabiPrefs.strings.preferencesReset);
    }

    /**
     * Setup auto-save for certain preferences
     */
    function setupAutoSave() {
        // Auto-save language preference immediately
        $('#preferred_language').on('change', function() {
            const language = $(this).val();
            autoSavePreference('preferred_language', language);
        });

        // Auto-save country preference
        $('#preferred_country').on('change', function() {
            const country = $(this).val();
            autoSavePreference('preferred_country', country);
        });
    }

    /**
     * Auto-save a single preference
     */
    function autoSavePreference(key, value) {
        const preferences = {};
        preferences[key] = value;

        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_save_preferences',
                nonce: chatgabiPrefs.nonce,
                preferences: preferences
            },
            success: function(response) {
                if (response.success) {
                    console.log(`Auto-saved ${key}:`, value);
                }
            },
            error: function(xhr, status, error) {
                console.error(`Failed to auto-save ${key}:`, error);
            }
        });
    }

    /**
     * Update UI elements based on saved preferences
     */
    function updateUIBasedOnPreferences(preferences) {
        // Update language in other parts of the interface if needed
        if (preferences.preferred_language && window.chatgabiAI) {
            // Trigger language change event for chat interface
            $(document).trigger('chatgabi:languageChanged', [preferences.preferred_language]);
        }

        // Update dashboard layout class if on dashboard page
        if (preferences.dashboard_layout) {
            $('body').removeClass('layout-default layout-compact layout-expanded')
                     .addClass('layout-' + preferences.dashboard_layout);
        }

        // Update any other UI elements that depend on preferences
        console.log('UI updated based on preferences:', preferences);
    }

    /**
     * Show status message
     */
    function showMessage(type, message) {
        const $messagesContainer = $('#preferences-messages');
        const messageClass = type === 'success' ? 'success' : 
                           type === 'error' ? 'error' : 'info';

        const messageHtml = `
            <div class="preference-message ${messageClass}">
                <span class="message-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                <span class="message-text">${message}</span>
                <button class="message-close" onclick="$(this).parent().fadeOut()">&times;</button>
            </div>
        `;

        $messagesContainer.html(messageHtml);
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(function() {
                $messagesContainer.find('.preference-message').fadeOut();
            }, 5000);
        }
    }

    /**
     * Load user preferences (for external use)
     */
    window.chatgabiLoadUserPreferences = function() {
        return $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_load_preferences',
                nonce: chatgabiPrefs.nonce
            }
        });
    };

    /**
     * Save specific preference (for external use)
     */
    window.chatgabiSavePreference = function(key, value) {
        const preferences = {};
        preferences[key] = value;
        return autoSavePreference(key, value);
    };

    /**
     * Setup account action handlers
     */
    function setupAccountActions() {
        // Export user data
        $('#export-data-btn').on('click', function() {
            if (confirm(chatgabiPrefs.strings.confirmExportData || 'Export all your data? This may take a moment.')) {
                window.location.href = chatgabiPrefs.ajaxUrl + '?action=chatgabi_export_user_data&nonce=' + chatgabiPrefs.nonce;
            }
        });

        // Clear chat history
        $('#clear-history-btn').on('click', function() {
            if (confirm(chatgabiPrefs.strings.confirmClearHistory || 'Are you sure you want to clear all chat history? This cannot be undone.')) {
                clearChatHistory();
            }
        });

        // Delete account
        $('#delete-account-btn').on('click', function() {
            const confirmText = chatgabiPrefs.strings.confirmDeleteAccount || 'Are you absolutely sure you want to delete your account? This action cannot be undone. Type "DELETE" to confirm:';
            const userInput = prompt(confirmText);

            if (userInput === 'DELETE') {
                deleteAccount();
            } else if (userInput !== null) {
                alert('Account deletion cancelled. You must type "DELETE" exactly to confirm.');
            }
        });
    }

    /**
     * Setup import/export handlers
     */
    function setupImportExport() {
        // Export settings
        $('#export-settings-btn').on('click', function() {
            exportSettings();
        });

        // Import settings
        $('#import-settings-btn').on('click', function() {
            $('#import-settings-file').click();
        });

        $('#import-settings-file').on('change', function() {
            const file = this.files[0];
            if (file) {
                importSettings(file);
            }
        });
    }

    /**
     * Setup analytics refresh
     */
    function setupAnalyticsRefresh() {
        // Auto-refresh analytics every 5 minutes if on preferences page
        if ($('.analytics-dashboard').length > 0) {
            setInterval(refreshAnalytics, 300000); // 5 minutes
        }
    }

    /**
     * Clear chat history
     */
    function clearChatHistory() {
        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_clear_chat_history',
                nonce: chatgabiPrefs.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage('success', 'Chat history cleared successfully');
                    refreshAnalytics();
                } else {
                    showMessage('error', response.data.message || 'Failed to clear chat history');
                }
            },
            error: function() {
                showMessage('error', 'Network error while clearing chat history');
            }
        });
    }

    /**
     * Delete account
     */
    function deleteAccount() {
        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_delete_account',
                nonce: chatgabiPrefs.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('Account deleted successfully. You will be redirected to the homepage.');
                    window.location.href = '/';
                } else {
                    showMessage('error', response.data.message || 'Failed to delete account');
                }
            },
            error: function() {
                showMessage('error', 'Network error while deleting account');
            }
        });
    }

    /**
     * Export settings
     */
    function exportSettings() {
        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_export_settings',
                nonce: chatgabiPrefs.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Create download link
                    const blob = new Blob([JSON.stringify(response.data.settings, null, 2)], {
                        type: 'application/json'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `chatgabi-settings-${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showMessage('success', 'Settings exported successfully');
                } else {
                    showMessage('error', response.data.message || 'Failed to export settings');
                }
            },
            error: function() {
                showMessage('error', 'Network error while exporting settings');
            }
        });
    }

    /**
     * Import settings
     */
    function importSettings(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const settings = JSON.parse(e.target.result);

                $.ajax({
                    url: chatgabiPrefs.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'chatgabi_import_settings',
                        nonce: chatgabiPrefs.nonce,
                        settings: JSON.stringify(settings)
                    },
                    success: function(response) {
                        if (response.success) {
                            showMessage('success', 'Settings imported successfully. Refreshing page...');
                            setTimeout(() => location.reload(), 2000);
                        } else {
                            showMessage('error', response.data.message || 'Failed to import settings');
                        }
                    },
                    error: function() {
                        showMessage('error', 'Network error while importing settings');
                    }
                });
            } catch (error) {
                showMessage('error', 'Invalid settings file format');
            }
        };
        reader.readAsText(file);
    }

    /**
     * Refresh analytics data
     */
    function refreshAnalytics() {
        $.ajax({
            url: chatgabiPrefs.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_get_user_analytics',
                nonce: chatgabiPrefs.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateAnalyticsDisplay(response.data);
                }
            },
            error: function() {
                console.error('Failed to refresh analytics');
            }
        });
    }

    /**
     * Update analytics display
     */
    function updateAnalyticsDisplay(analytics) {
        // Update analytics cards
        $('.analytics-card').each(function() {
            const $card = $(this);
            const $content = $card.find('.analytics-content h3');

            if ($card.find('.analytics-icon').text() === '💬') {
                $content.text(analytics.total_conversations.toLocaleString());
            } else if ($card.find('.analytics-icon').text() === '🎯') {
                $content.text(analytics.credits_used.toLocaleString());
            } else if ($card.find('.analytics-icon').text() === '📝') {
                $content.text(analytics.templates_generated.toLocaleString());
            } else if ($card.find('.analytics-icon').text() === '🚀') {
                $content.text(analytics.opportunities_viewed.toLocaleString());
            }
        });

        console.log('Analytics updated');
    }

})(jQuery);

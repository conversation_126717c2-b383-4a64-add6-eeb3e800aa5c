<?php
/**
 * Token Optimizer for BusinessCraft AI
 * Reduces OpenAI API costs through intelligent token management
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_Token_Optimizer {

    private $context_cache;
    private $prompt_templates;
    private $token_limits;

    public function __construct() {
        $this->init_optimizer();
    }

    /**
     * Initialize token optimization settings
     */
    private function init_optimizer() {
        // ENFORCE 400-TOKEN LIMIT COMPLIANCE - Updated for audit requirements
        $this->token_limits = array(
            'gpt-3.5-turbo' => array(
                'max_tokens' => 4096,
                'optimal_prompt' => 1500,
                'optimal_response' => 400  // Enforced 400-token limit
            ),
            'gpt-4' => array(
                'max_tokens' => 8192,
                'optimal_prompt' => 2000,
                'optimal_response' => 400  // Enforced 400-token limit
            ),
            'gpt-4-turbo' => array(
                'max_tokens' => 128000,
                'optimal_prompt' => 3000,
                'optimal_response' => 400  // Enforced 400-token limit
            )
        );

        $this->prompt_templates = array(
            'business_analysis' => 'Analyze {business_type} in {country}. Focus on: {key_points}. Provide actionable insights.',
            'market_research' => 'Research {industry} market in {country}. Key areas: {focus_areas}. Include local context.',
            'financial_planning' => 'Create financial plan for {business_type} in {country}. Consider: {factors}.',
            'general_business' => 'Business advice for {country} entrepreneur. Context: {business_context}.'
        );
    }

    /**
     * Optimize prompt for token efficiency
     */
    public function optimize_prompt($message, $context_data, $model = 'gpt-3.5-turbo') {
        $request_type = $this->classify_request($message);
        $optimized_context = $this->optimize_context($context_data, $request_type, $model);

        // Use cached context if available
        $cache_key = $this->generate_context_cache_key($context_data, $request_type);
        $cached_context = $this->get_cached_context($cache_key);

        if ($cached_context) {
            $context_prompt = $cached_context;
            $this->log_token_event('context_cache_hit', array('cache_key' => $cache_key));
        } else {
            $context_prompt = $this->build_optimized_context($optimized_context, $request_type);
            $this->cache_context($cache_key, $context_prompt);
            $this->log_token_event('context_cache_miss', array('cache_key' => $cache_key));
        }

        // Build efficient prompt
        $optimized_prompt = $this->build_efficient_prompt($message, $context_prompt, $request_type);

        // Validate token count
        $token_count = $this->estimate_tokens($optimized_prompt);
        $limit = $this->token_limits[$model]['optimal_prompt'];

        if ($token_count > $limit) {
            $optimized_prompt = $this->compress_prompt($optimized_prompt, $limit);
            $this->log_token_event('prompt_compressed', array(
                'original_tokens' => $token_count,
                'compressed_tokens' => $this->estimate_tokens($optimized_prompt),
                'model' => $model
            ));
        }

        return $optimized_prompt;
    }

    /**
     * Classify request type for targeted optimization
     */
    private function classify_request($message) {
        $keywords = array(
            'market_analysis' => array('market', 'analysis', 'research', 'competition', 'competitor'),
            'financial_planning' => array('financial', 'budget', 'cost', 'revenue', 'profit', 'funding'),
            'business_strategy' => array('strategy', 'plan', 'growth', 'expansion', 'development'),
            'marketing' => array('marketing', 'promotion', 'advertising', 'brand', 'customer'),
            'operations' => array('operations', 'process', 'efficiency', 'workflow', 'management')
        );

        $message_lower = strtolower($message);
        $scores = array();

        foreach ($keywords as $type => $words) {
            $score = 0;
            foreach ($words as $word) {
                if (strpos($message_lower, $word) !== false) {
                    $score++;
                }
            }
            $scores[$type] = $score;
        }

        $max_score = max($scores);
        if ($max_score > 0) {
            return array_search($max_score, $scores);
        }

        return 'general_business';
    }

    /**
     * Optimize context data based on request type
     */
    private function optimize_context($context_data, $request_type, $model) {
        $priority_map = array(
            'market_analysis' => array('market_characteristics', 'regulatory_environment', 'opportunities'),
            'financial_planning' => array('regulatory_environment', 'payment_preferences', 'business_challenges'),
            'business_strategy' => array('business_culture', 'market_characteristics', 'opportunities'),
            'marketing' => array('communication_style', 'market_characteristics', 'networking_culture'),
            'operations' => array('business_culture', 'regulatory_environment', 'business_challenges'),
            'general_business' => array('business_culture', 'communication_style', 'market_characteristics')
        );

        $priorities = $priority_map[$request_type] ?? $priority_map['general_business'];
        $optimized = array();

        // Include only relevant context elements
        foreach ($priorities as $key) {
            if (isset($context_data[$key])) {
                $optimized[$key] = $context_data[$key];
            }
        }

        // Add model-specific optimizations
        if ($model === 'gpt-3.5-turbo') {
            // More aggressive optimization for smaller context window
            $optimized = array_slice($optimized, 0, 3, true);
        }

        return $optimized;
    }

    /**
     * Build optimized context prompt
     */
    private function build_optimized_context($context_data, $request_type) {
        $template = $this->prompt_templates[$request_type] ?? $this->prompt_templates['general_business'];

        // Create concise context
        $context_parts = array();
        foreach ($context_data as $key => $value) {
            if (is_string($value) && strlen($value) > 100) {
                // Truncate long context values
                $value = substr($value, 0, 100) . '...';
            }
            $context_parts[] = ucfirst(str_replace('_', ' ', $key)) . ': ' . $value;
        }

        return implode('. ', $context_parts);
    }

    /**
     * Build efficient prompt structure
     */
    private function build_efficient_prompt($message, $context, $request_type) {
        $prompt = "Context: {$context}\n\n";
        $prompt .= "Request: {$message}\n\n";
        $prompt .= "Provide a concise, actionable response focused on African business context.";

        return $prompt;
    }

    /**
     * Estimate token count (approximate)
     */
    private function estimate_tokens($text) {
        // Rough estimation: 1 token ≈ 4 characters for English
        // Adjust for African languages which may have different token ratios
        return ceil(strlen($text) / 4);
    }

    /**
     * Compress prompt to fit token limit
     */
    private function compress_prompt($prompt, $token_limit) {
        $target_chars = $token_limit * 4; // Approximate character limit

        if (strlen($prompt) <= $target_chars) {
            return $prompt;
        }

        // Split into sections and prioritize
        $sections = explode("\n\n", $prompt);
        $compressed = array();
        $current_length = 0;

        foreach ($sections as $section) {
            if ($current_length + strlen($section) <= $target_chars) {
                $compressed[] = $section;
                $current_length += strlen($section);
            } else {
                // Truncate the section if it's the last one that fits
                $remaining_chars = $target_chars - $current_length;
                if ($remaining_chars > 50) { // Only add if meaningful content can fit
                    $compressed[] = substr($section, 0, $remaining_chars - 3) . '...';
                }
                break;
            }
        }

        return implode("\n\n", $compressed);
    }

    /**
     * Generate cache key for context
     */
    private function generate_context_cache_key($context_data, $request_type) {
        $key_data = array(
            'type' => $request_type,
            'country' => $context_data['country'] ?? 'unknown',
            'business_type' => $context_data['business_type'] ?? 'sme',
            'industry' => $context_data['industry'] ?? 'general'
        );

        return 'bc_context_' . md5(serialize($key_data));
    }

    /**
     * Get cached context
     */
    private function get_cached_context($cache_key) {
        return get_transient($cache_key);
    }

    /**
     * Cache context for reuse
     */
    private function cache_context($cache_key, $context, $expiry = 3600) {
        set_transient($cache_key, $context, $expiry);
    }

    /**
     * Log token optimization events
     */
    private function log_token_event($event_type, $data = array()) {
        if (function_exists('businesscraft_ai_log_analytics')) {
            businesscraft_ai_log_analytics(
                get_current_user_id(),
                'token_optimization',
                array_merge(array('event' => $event_type), $data)
            );
        }
    }

    /**
     * Get model limits for optimization
     */
    public function get_model_limits($model) {
        return $this->token_limits[$model] ?? $this->token_limits['gpt-3.5-turbo'];
    }

    /**
     * Get token usage statistics
     */
    public function get_token_stats($user_id = null, $days = 30) {
        global $wpdb;

        $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
        $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';

        $where_clause = "WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)";
        $params = array($days);

        if ($user_id) {
            $where_clause .= " AND a.user_id = %d";
            $params[] = $user_id;
        }

        $query = "
            SELECT
                COUNT(*) as total_requests,
                AVG(c.tokens_used) as avg_tokens_per_request,
                SUM(c.tokens_used) as total_tokens,
                AVG(c.credits_used) as avg_credits_per_request,
                SUM(c.credits_used) as total_credits
            FROM {$analytics_table} a
            LEFT JOIN {$chat_logs_table} c ON a.user_id = c.user_id
                AND DATE(a.created_at) = DATE(c.created_at)
            {$where_clause}
        ";

        return $wpdb->get_row($wpdb->prepare($query, $params));
    }

    /**
     * Get optimization metrics for analytics
     */
    public function get_optimization_metrics($days = 30) {
        global $wpdb;

        $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';

        // Get cache hit rate
        $cache_hits = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) FROM {$analytics_table}
            WHERE event_type = 'token_optimization'
            AND JSON_EXTRACT(event_data, '$.event') = 'context_cache_hit'
            AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
        ", $days));

        $cache_misses = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) FROM {$analytics_table}
            WHERE event_type = 'token_optimization'
            AND JSON_EXTRACT(event_data, '$.event') = 'context_cache_miss'
            AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
        ", $days));

        $compressions = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) FROM {$analytics_table}
            WHERE event_type = 'token_optimization'
            AND JSON_EXTRACT(event_data, '$.event') = 'prompt_compressed'
            AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
        ", $days));

        $total_cache_events = $cache_hits + $cache_misses;
        $cache_hit_rate = $total_cache_events > 0 ? ($cache_hits / $total_cache_events) * 100 : 0;

        return array(
            'cache_hit_rate' => round($cache_hit_rate, 2),
            'cache_hits' => $cache_hits,
            'cache_misses' => $cache_misses,
            'prompt_compressions' => $compressions,
            'total_optimizations' => $total_cache_events + $compressions
        );
    }
}

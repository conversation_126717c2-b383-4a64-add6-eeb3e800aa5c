/**
 * Component Manager
 * 
 * Manages all BusinessCraft AI frontend components
 * Provides centralized component lifecycle management and event coordination
 */

class ComponentManager {
    constructor() {
        this.components = new Map();
        this.componentRegistry = new Map();
        this.eventBus = new EventTarget();
        this.isInitialized = false;
        this.config = {
            autoDiscovery: true,
            enableGlobalEvents: true,
            enableAccessibility: true,
            enableMobileOptimization: true,
            debug: false
        };
        
        // Bind methods
        this.handleGlobalEvent = this.handleGlobalEvent.bind(this);
        this.handleComponentEvent = this.handleComponentEvent.bind(this);
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }
    
    /**
     * Initialize the component manager
     */
    init() {
        if (this.isInitialized) return;
        
        try {
            // Set up WordPress hooks integration
            this.setupWordPressHooks();
            
            // Set up global event handling
            this.setupGlobalEvents();
            
            // Register built-in components
            this.registerBuiltInComponents();
            
            // Auto-discover components if enabled
            if (this.config.autoDiscovery) {
                this.discoverComponents();
            }
            
            // Set up accessibility features
            if (this.config.enableAccessibility) {
                this.setupGlobalAccessibility();
            }
            
            // Set up mobile optimizations
            if (this.config.enableMobileOptimization) {
                this.setupGlobalMobileOptimizations();
            }
            
            this.isInitialized = true;
            this.emit('manager:initialized');
            
            this.log('Component Manager initialized');
            
        } catch (error) {
            this.handleError('Component Manager initialization failed', error);
        }
    }
    
    /**
     * Register a component class
     */
    registerComponent(name, componentClass, options = {}) {
        this.componentRegistry.set(name, {
            class: componentClass,
            options: options,
            selector: options.selector || `[data-component="${name}"]`
        });
        
        this.log('Component registered:', name);
        this.emit('manager:component-registered', { name, componentClass });
    }
    
    /**
     * Register built-in components
     */
    registerBuiltInComponents() {
        // Register core components
        const coreComponents = [
            'analytics-dashboard',
            'notification-center',
            'template-enhancer',
            'chat-interface',
            'credit-manager',
            'opportunity-viewer',
            'feedback-system',
            'export-manager'
        ];
        
        coreComponents.forEach(name => {
            this.registerComponent(name, window.BusinessCraftAI.BaseComponent, {
                selector: `[data-component="${name}"]`
            });
        });
    }
    
    /**
     * Discover and initialize components in the DOM
     */
    discoverComponents() {
        this.componentRegistry.forEach((config, name) => {
            const elements = document.querySelectorAll(config.selector);
            elements.forEach((element, index) => {
                const instanceId = `${name}-${index}`;
                this.createComponent(instanceId, name, element, config.options);
            });
        });
    }
    
    /**
     * Create a component instance
     */
    createComponent(id, type, element, options = {}) {
        if (this.components.has(id)) {
            this.log('Component already exists:', id);
            return this.components.get(id);
        }
        
        const config = this.componentRegistry.get(type);
        if (!config) {
            this.handleError(`Unknown component type: ${type}`);
            return null;
        }
        
        try {
            const mergedOptions = {
                ...config.options,
                ...options,
                id: id,
                type: type,
                manager: this
            };
            
            const component = new config.class(element, mergedOptions);
            this.components.set(id, component);
            
            // Set up component event forwarding
            this.setupComponentEventForwarding(component);
            
            this.log('Component created:', id, type);
            this.emit('manager:component-created', { id, type, component });
            
            return component;
            
        } catch (error) {
            this.handleError(`Failed to create component ${id}`, error);
            return null;
        }
    }
    
    /**
     * Get a component by ID
     */
    getComponent(id) {
        return this.components.get(id);
    }
    
    /**
     * Get all components of a specific type
     */
    getComponentsByType(type) {
        const components = [];
        this.components.forEach((component, id) => {
            if (component.options.type === type) {
                components.push(component);
            }
        });
        return components;
    }
    
    /**
     * Destroy a component
     */
    destroyComponent(id) {
        const component = this.components.get(id);
        if (component) {
            component.destroy();
            this.components.delete(id);
            this.emit('manager:component-destroyed', { id, component });
            this.log('Component destroyed:', id);
        }
    }
    
    /**
     * Destroy all components
     */
    destroyAllComponents() {
        this.components.forEach((component, id) => {
            this.destroyComponent(id);
        });
    }
    
    /**
     * Set up WordPress hooks integration
     */
    setupWordPressHooks() {
        if (typeof wp !== 'undefined' && wp.hooks) {
            this.wpHooks = wp.hooks;
            
            // Register manager with WordPress hooks
            this.wpHooks.doAction('bcai.manager.init', this);
            
            // Listen for WordPress events
            this.wpHooks.addAction('bcai.component.register', 'bcai-manager', this.handleWordPressComponentRegister.bind(this));
            this.wpHooks.addAction('bcai.component.create', 'bcai-manager', this.handleWordPressComponentCreate.bind(this));
        }
    }
    
    /**
     * Handle WordPress component registration
     */
    handleWordPressComponentRegister(component) {
        this.log('WordPress component registered:', component);
    }
    
    /**
     * Handle WordPress component creation
     */
    handleWordPressComponentCreate(data) {
        const { id, type, element, options } = data;
        this.createComponent(id, type, element, options);
    }
    
    /**
     * Set up global event handling
     */
    setupGlobalEvents() {
        if (!this.config.enableGlobalEvents) return;
        
        // Global keyboard shortcuts
        document.addEventListener('keydown', this.handleGlobalKeyboard.bind(this));
        
        // Global click handling
        document.addEventListener('click', this.handleGlobalClick.bind(this));
        
        // Global focus management
        document.addEventListener('focusin', this.handleGlobalFocus.bind(this));
        document.addEventListener('focusout', this.handleGlobalBlur.bind(this));
        
        // Window events
        window.addEventListener('resize', this.handleWindowResize.bind(this));
        window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
    }
    
    /**
     * Set up component event forwarding
     */
    setupComponentEventForwarding(component) {
        // Forward component events to global event bus
        const eventTypes = [
            'component:initialized',
            'component:destroyed',
            'component:state-change',
            'component:error',
            'component:focus',
            'component:blur',
            'component:keyboard',
            'component:viewport-change'
        ];
        
        eventTypes.forEach(eventType => {
            component.on(eventType, (event) => {
                this.emit(eventType, event.detail);
            });
        });
    }
    
    /**
     * Set up global accessibility features
     */
    setupGlobalAccessibility() {
        // Skip links for keyboard navigation
        this.createSkipLinks();
        
        // Global ARIA live region
        this.createGlobalLiveRegion();
        
        // Focus trap management
        this.setupFocusTrapManagement();
        
        // High contrast mode detection
        this.setupHighContrastDetection();
    }
    
    /**
     * Create skip links for accessibility
     */
    createSkipLinks() {
        if (document.getElementById('bcai-skip-links')) return;
        
        const skipLinks = document.createElement('div');
        skipLinks.id = 'bcai-skip-links';
        skipLinks.className = 'skip-links';
        skipLinks.innerHTML = `
            <a href="#main-content" class="skip-link">Skip to main content</a>
            <a href="#navigation" class="skip-link">Skip to navigation</a>
            <a href="#dashboard-tabs" class="skip-link">Skip to dashboard</a>
        `;
        
        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .skip-links {
                position: absolute;
                top: -40px;
                left: 6px;
                z-index: 999999;
            }
            .skip-link {
                position: absolute;
                left: -10000px;
                top: auto;
                width: 1px;
                height: 1px;
                overflow: hidden;
                background: #000;
                color: #fff;
                padding: 8px 16px;
                text-decoration: none;
                border-radius: 4px;
            }
            .skip-link:focus {
                position: static;
                width: auto;
                height: auto;
                overflow: visible;
                left: auto;
            }
        `;
        
        document.head.appendChild(style);
        document.body.insertBefore(skipLinks, document.body.firstChild);
    }
    
    /**
     * Create global ARIA live region
     */
    createGlobalLiveRegion() {
        if (document.getElementById('bcai-global-live-region')) return;
        
        const liveRegion = document.createElement('div');
        liveRegion.id = 'bcai-global-live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(liveRegion);
    }
    
    /**
     * Announce message globally to screen readers
     */
    announceGlobally(message) {
        const liveRegion = document.getElementById('bcai-global-live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
        
        this.emit('manager:announcement', { message });
    }
    
    /**
     * Set up global mobile optimizations
     */
    setupGlobalMobileOptimizations() {
        // Viewport meta tag
        this.ensureViewportMeta();
        
        // Touch action optimization
        this.setupTouchActionOptimization();
        
        // Mobile-specific event handling
        this.setupMobileEventHandling();
        
        // Responsive image loading
        this.setupResponsiveImageLoading();
    }
    
    /**
     * Ensure viewport meta tag is present
     */
    ensureViewportMeta() {
        if (document.querySelector('meta[name="viewport"]')) return;
        
        const viewport = document.createElement('meta');
        viewport.name = 'viewport';
        viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes';
        document.head.appendChild(viewport);
    }
    
    /**
     * Global event handlers
     */
    handleGlobalKeyboard(event) {
        // Global keyboard shortcuts
        const { key, ctrlKey, altKey, shiftKey } = event;
        
        // Ctrl+/ for help
        if (ctrlKey && key === '/') {
            event.preventDefault();
            this.showKeyboardShortcuts();
        }
        
        // Escape to close modals/overlays
        if (key === 'Escape') {
            this.handleGlobalEscape();
        }
        
        this.emit('manager:global-keyboard', { event });
    }
    
    handleGlobalClick(event) {
        this.emit('manager:global-click', { event });
    }
    
    handleGlobalFocus(event) {
        this.emit('manager:global-focus', { event });
    }
    
    handleGlobalBlur(event) {
        this.emit('manager:global-blur', { event });
    }
    
    handleWindowResize(event) {
        this.emit('manager:window-resize', { event });
        
        // Notify all components of resize
        this.components.forEach(component => {
            if (component.handleResize) {
                component.handleResize(event);
            }
        });
    }
    
    handleOrientationChange(event) {
        this.emit('manager:orientation-change', { event });
        
        // Notify all components of orientation change
        this.components.forEach(component => {
            if (component.handleOrientationChange) {
                component.handleOrientationChange(event);
            }
        });
    }
    
    handleGlobalEscape() {
        // Close any open modals, dropdowns, etc.
        this.components.forEach(component => {
            if (component.handleGlobalEscape) {
                component.handleGlobalEscape();
            }
        });
    }
    
    /**
     * Show keyboard shortcuts help
     */
    showKeyboardShortcuts() {
        this.announceGlobally('Keyboard shortcuts help opened');
        // Implementation for showing keyboard shortcuts
    }
    
    /**
     * Set up focus trap management
     */
    setupFocusTrapManagement() {
        this.focusTraps = new Set();
    }
    
    /**
     * Set up high contrast mode detection
     */
    setupHighContrastDetection() {
        // Detect high contrast mode
        const testElement = document.createElement('div');
        testElement.style.cssText = `
            position: absolute;
            left: -10000px;
            background-color: rgb(31, 31, 31);
            color: rgb(255, 255, 255);
        `;
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement);
        const isHighContrast = computedStyle.backgroundColor !== 'rgb(31, 31, 31)';
        
        document.body.removeChild(testElement);
        
        if (isHighContrast) {
            document.body.classList.add('high-contrast');
            this.emit('manager:high-contrast-detected');
        }
    }
    
    /**
     * Set up touch action optimization
     */
    setupTouchActionOptimization() {
        // Add touch-action CSS for better touch performance
        const style = document.createElement('style');
        style.textContent = `
            .bcai-component {
                touch-action: manipulation;
            }
            .bcai-scrollable {
                touch-action: pan-y;
            }
            .bcai-draggable {
                touch-action: none;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * Set up mobile event handling
     */
    setupMobileEventHandling() {
        if (!('ontouchstart' in window)) return;
        
        // Prevent double-tap zoom on buttons
        document.addEventListener('touchend', (event) => {
            if (event.target.matches('button, .btn, [role="button"]')) {
                event.preventDefault();
            }
        });
    }
    
    /**
     * Set up responsive image loading
     */
    setupResponsiveImageLoading() {
        // Implement lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    /**
     * Emit event on global event bus
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        this.eventBus.dispatchEvent(event);
        
        // Also emit via WordPress hooks if available
        if (this.wpHooks) {
            this.wpHooks.doAction(eventName, data);
        }
        
        this.log('Global event emitted:', eventName, data);
    }
    
    /**
     * Listen for global events
     */
    on(eventName, handler) {
        this.eventBus.addEventListener(eventName, handler);
    }
    
    /**
     * Remove global event listener
     */
    off(eventName, handler) {
        this.eventBus.removeEventListener(eventName, handler);
    }
    
    /**
     * Handle component events
     */
    handleComponentEvent(event) {
        this.log('Component event received:', event.type, event.detail);
    }
    
    /**
     * Handle global events
     */
    handleGlobalEvent(event) {
        this.log('Global event received:', event.type, event.detail);
    }
    
    /**
     * Handle errors
     */
    handleError(message, error = null) {
        console.error(`[ComponentManager] ${message}`, error);
        this.emit('manager:error', { message, error });
    }
    
    /**
     * Log debug messages
     */
    log(...args) {
        if (this.config.debug) {
            console.log('[ComponentManager]', ...args);
        }
    }
    
    /**
     * Get manager statistics
     */
    getStats() {
        return {
            totalComponents: this.components.size,
            registeredTypes: this.componentRegistry.size,
            componentsByType: this.getComponentCountsByType(),
            isInitialized: this.isInitialized
        };
    }
    
    /**
     * Get component counts by type
     */
    getComponentCountsByType() {
        const counts = {};
        this.components.forEach(component => {
            const type = component.options.type;
            counts[type] = (counts[type] || 0) + 1;
        });
        return counts;
    }
}

// Create global instance
window.BusinessCraftAI = window.BusinessCraftAI || {};
window.BusinessCraftAI.ComponentManager = ComponentManager;
window.BusinessCraftAI.componentManager = new ComponentManager();

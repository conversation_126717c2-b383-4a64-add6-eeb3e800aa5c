<?php
/**
 * Quick Function Fix Verification
 * 
 * This script quickly verifies that the function redeclaration fix is working
 */

// Load WordPress
require_once(dirname(dirname(dirname(__DIR__))) . '/wp-load.php');

echo "<h1>Function Redeclaration Fix Verification</h1>\n";

// Test 1: Check if function exists
echo "<h2>Function Existence</h2>\n";
if (function_exists('chatgabi_get_country_name')) {
    echo "✅ chatgabi_get_country_name() function exists\n";
} else {
    echo "❌ chatgabi_get_country_name() function missing\n";
}

// Test 2: Test function behavior
echo "<h2>Function Behavior</h2>\n";
if (function_exists('chatgabi_get_country_name')) {
    $test_cases = [
        'GH' => 'Ghana',
        'KE' => 'Kenya', 
        'NG' => 'Nigeria',
        'ZA' => 'South Africa',
        'XX' => 'XX' // Should return the code itself
    ];
    
    foreach ($test_cases as $code => $expected) {
        $result = chatgabi_get_country_name($code);
        if ($result === $expected) {
            echo "✅ $code → $result\n";
        } else {
            echo "❌ $code → $result (expected: $expected)\n";
        }
    }
} else {
    echo "❌ Cannot test - function not available\n";
}

// Test 3: Check file contents
echo "<h2>File Content Check</h2>\n";
$template_file = get_template_directory() . '/inc/template-functions.php';
$context_file = get_template_directory() . '/inc/context-personalization.php';

$template_content = file_get_contents($template_file);
$context_content = file_get_contents($context_file);

// Check if function is removed from template-functions.php
if (strpos($template_content, 'function chatgabi_get_country_name(') === false) {
    echo "✅ Function removed from template-functions.php\n";
} else {
    echo "❌ Function still exists in template-functions.php\n";
}

// Check if function exists in context-personalization.php
if (strpos($context_content, 'function chatgabi_get_country_name(') !== false) {
    echo "✅ Function exists in context-personalization.php\n";
} else {
    echo "❌ Function missing from context-personalization.php\n";
}

// Check if function is protected
if (strpos($context_content, 'function_exists(\'chatgabi_get_country_name\')') !== false) {
    echo "✅ Function protected with function_exists() check\n";
} else {
    echo "❌ Function not protected with function_exists() check\n";
}

echo "\n<h2>Summary</h2>\n";
echo "If all tests pass, the function redeclaration conflict has been resolved.\n";
echo "The enhanced version in context-personalization.php should be working correctly.\n";
?>

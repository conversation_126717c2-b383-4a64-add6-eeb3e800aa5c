<?php
/**
 * Fix Database Schema Issues
 * Run this file to check and fix database table issues
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

echo '<h1>Database Schema Fix</h1>';

global $wpdb;

// Check prompt templates table
$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'");

echo '<h2>1. Checking Prompt Templates Table</h2>';

if ($table_exists) {
    echo "<p>✅ Table exists: $templates_table</p>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $templates_table");
    echo '<h3>Current columns:</h3><ul>';
    $has_prompt_text = false;
    foreach ($columns as $column) {
        echo "<li>{$column->Field} ({$column->Type})</li>";
        if ($column->Field === 'prompt_text') {
            $has_prompt_text = true;
        }
    }
    echo '</ul>';
    
    if (!$has_prompt_text) {
        echo '<p>❌ Missing prompt_text column! Recreating table...</p>';
        
        // Drop and recreate table
        $wpdb->query("DROP TABLE IF EXISTS $templates_table");
        
        // Load the prompt templates functions
        require_once get_template_directory() . '/inc/prompt-templates.php';
        
        // Recreate tables
        $result = chatgabi_create_prompt_templates_tables();
        
        if ($result) {
            echo '<p>✅ Tables recreated successfully!</p>';
        } else {
            echo '<p>❌ Failed to recreate tables</p>';
        }
    } else {
        echo '<p>✅ prompt_text column exists</p>';
    }
} else {
    echo "<p>❌ Table does not exist: $templates_table</p>";
    echo '<p>Creating tables...</p>';
    
    // Load the prompt templates functions
    require_once get_template_directory() . '/inc/prompt-templates.php';
    
    // Create tables
    $result = chatgabi_create_prompt_templates_tables();
    
    if ($result) {
        echo '<p>✅ Tables created successfully!</p>';
    } else {
        echo '<p>❌ Failed to create tables</p>';
    }
}

// Check feedback table
echo '<h2>2. Checking Feedback Table</h2>';
$feedback_table = $wpdb->prefix . 'chatgabi_feedback';
$feedback_exists = $wpdb->get_var("SHOW TABLES LIKE '$feedback_table'");

if ($feedback_exists) {
    echo "<p>✅ Feedback table exists: $feedback_table</p>";
} else {
    echo "<p>❌ Feedback table missing: $feedback_table</p>";
    echo '<p>Creating feedback table...</p>';
    
    // Load database functions
    require_once get_template_directory() . '/inc/database.php';
    
    // Create feedback table
    $result = chatgabi_create_feedback_tables();
    
    if ($result) {
        echo '<p>✅ Feedback table created successfully!</p>';
    } else {
        echo '<p>❌ Failed to create feedback table</p>';
    }
}

// Test template creation
echo '<h2>3. Testing Template Creation</h2>';

if (function_exists('chatgabi_initialize_default_templates')) {
    try {
        chatgabi_initialize_default_templates();
        echo '<p>✅ Default templates initialized</p>';
        
        // Check count
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE user_id = 0");
        echo "<p>Default templates count: $count</p>";
        
    } catch (Exception $e) {
        echo '<p>❌ Error initializing templates: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p>❌ chatgabi_initialize_default_templates function not found</p>';
}

echo '<h2>4. Summary</h2>';
echo '<p>Database schema fix completed. Please refresh your WordPress admin to see the changes.</p>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-templates">Go to Prompt Templates</a></p>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback">Go to User Feedback</a></p>';
?>

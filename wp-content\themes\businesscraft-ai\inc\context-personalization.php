<?php
/**
 * Context Personalization System for ChatGABI
 * 
 * Extends the existing user profile system to store personalized business context,
 * enhances prompt template library with dynamic customization, and implements
 * session-based context retention.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize Context Personalization system
 */
function chatgabi_init_context_personalization() {
    // Create enhanced context tables
    chatgabi_create_context_personalization_tables();
    
    // Add AJAX handlers
    add_action('wp_ajax_chatgabi_update_context_preferences', 'chatgabi_handle_update_context_preferences');
    add_action('wp_ajax_chatgabi_get_personalized_suggestions', 'chatgabi_handle_get_personalized_suggestions');
    add_action('wp_ajax_chatgabi_save_session_context', 'chatgabi_handle_save_session_context');
    add_action('wp_ajax_chatgabi_get_session_context', 'chatgabi_handle_get_session_context');
    
    // Hook into existing systems
    add_filter('chatgabi_prompt_template_suggestions', 'chatgabi_personalize_template_suggestions', 10, 2);
    add_filter('chatgabi_african_context_prompt', 'chatgabi_enhance_with_personal_context', 10, 3);
    add_action('chatgabi_user_login', 'chatgabi_load_user_context');
    
    // Schedule context optimization
    if (!wp_next_scheduled('chatgabi_optimize_user_contexts')) {
        wp_schedule_event(time(), 'daily', 'chatgabi_optimize_user_contexts');
    }
}
add_action('init', 'chatgabi_init_context_personalization');

/**
 * Create context personalization tables
 */
function chatgabi_create_context_personalization_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // Session context table
    $session_table = $wpdb->prefix . 'chatgabi_session_context';
    $session_sql = "CREATE TABLE IF NOT EXISTS {$session_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        session_id varchar(100) NOT NULL,
        context_type varchar(50) NOT NULL,
        context_data longtext NOT NULL,
        business_focus varchar(100),
        current_goals text,
        conversation_history longtext,
        context_score decimal(3,2) DEFAULT 0.00,
        last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        expires_at datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY session_user (session_id, user_id),
        KEY user_id (user_id),
        KEY context_type (context_type),
        KEY expires_at (expires_at)
    ) {$charset_collate};";
    
    // Personalized recommendations table
    $recommendations_table = $wpdb->prefix . 'chatgabi_personalized_recommendations';
    $recommendations_sql = "CREATE TABLE IF NOT EXISTS {$recommendations_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        recommendation_type varchar(50) NOT NULL,
        recommendation_data longtext NOT NULL,
        relevance_score decimal(3,2) DEFAULT 0.00,
        interaction_count int(11) DEFAULT 0,
        last_shown datetime,
        is_active tinyint(1) DEFAULT 1,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY recommendation_type (recommendation_type),
        KEY relevance_score (relevance_score)
    ) {$charset_collate};";
    
    // User context preferences table
    $preferences_table = $wpdb->prefix . 'chatgabi_context_preferences';
    $preferences_sql = "CREATE TABLE IF NOT EXISTS {$preferences_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        preference_key varchar(100) NOT NULL,
        preference_value longtext NOT NULL,
        preference_type varchar(50) DEFAULT 'string',
        auto_generated tinyint(1) DEFAULT 0,
        confidence_score decimal(3,2) DEFAULT 0.00,
        last_used datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_preference (user_id, preference_key),
        KEY preference_type (preference_type)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($session_sql);
    dbDelta($recommendations_sql);
    dbDelta($preferences_sql);
}

/**
 * Enhance user profile with personalized business context
 */
function chatgabi_enhance_user_profile_context($user_id) {
    // Get existing profile
    $profile = chatgabi_get_user_profile($user_id);
    
    if (!$profile) {
        return false;
    }
    
    // Generate personalized context based on profile
    $context_data = array(
        'business_context' => array(
            'industry' => $profile->primary_industry,
            'country' => $profile->target_country,
            'business_stage' => $profile->business_stage,
            'company_size' => $profile->business_size,
            'experience_level' => $profile->experience_level
        ),
        'preferences' => array(
            'language' => $profile->preferred_language,
            'communication_style' => chatgabi_determine_communication_style($profile),
            'detail_level' => chatgabi_determine_detail_preference($profile),
            'focus_areas' => chatgabi_extract_focus_areas($profile)
        ),
        'goals' => array(
            'primary_goals' => $profile->primary_goals ? explode(',', $profile->primary_goals) : array(),
            'challenges' => $profile->challenges ? explode(',', $profile->challenges) : array(),
            'timeline' => $profile->timeline_urgency
        )
    );
    
    // Save enhanced context
    return chatgabi_save_user_context_preferences($user_id, $context_data);
}

/**
 * Save user context preferences
 */
function chatgabi_save_user_context_preferences($user_id, $context_data) {
    global $wpdb;
    
    $preferences_table = $wpdb->prefix . 'chatgabi_context_preferences';
    
    foreach ($context_data as $category => $data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $preference_key = $category . '_' . $key;
                $preference_value = is_array($value) ? wp_json_encode($value) : $value;
                
                $wpdb->replace(
                    $preferences_table,
                    array(
                        'user_id' => $user_id,
                        'preference_key' => $preference_key,
                        'preference_value' => $preference_value,
                        'preference_type' => is_array($value) ? 'array' : 'string',
                        'auto_generated' => 1,
                        'confidence_score' => 0.8
                    ),
                    array('%d', '%s', '%s', '%s', '%d', '%f')
                );
            }
        }
    }
    
    return true;
}

/**
 * Get user context preferences
 */
function chatgabi_get_user_context_preferences($user_id) {
    global $wpdb;
    
    $preferences_table = $wpdb->prefix . 'chatgabi_context_preferences';
    
    $preferences = $wpdb->get_results($wpdb->prepare(
        "SELECT preference_key, preference_value, preference_type FROM {$preferences_table} WHERE user_id = %d",
        $user_id
    ));
    
    $context = array();
    
    foreach ($preferences as $pref) {
        $value = $pref->preference_value;
        
        if ($pref->preference_type === 'array') {
            $value = json_decode($value, true);
        }
        
        $context[$pref->preference_key] = $value;
    }
    
    return $context;
}

/**
 * Save session context
 */
function chatgabi_save_session_context($user_id, $session_id, $context_data, $expires_in = 3600) {
    global $wpdb;
    
    $session_table = $wpdb->prefix . 'chatgabi_session_context';
    
    $expires_at = date('Y-m-d H:i:s', time() + $expires_in);
    
    $result = $wpdb->replace(
        $session_table,
        array(
            'user_id' => $user_id,
            'session_id' => $session_id,
            'context_type' => $context_data['type'] ?? 'general',
            'context_data' => wp_json_encode($context_data),
            'business_focus' => $context_data['business_focus'] ?? '',
            'current_goals' => $context_data['current_goals'] ?? '',
            'conversation_history' => wp_json_encode($context_data['conversation_history'] ?? array()),
            'context_score' => $context_data['context_score'] ?? 0.5,
            'expires_at' => $expires_at
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%s')
    );
    
    return $result !== false;
}

/**
 * Get session context
 */
function chatgabi_get_session_context($user_id, $session_id) {
    global $wpdb;
    
    $session_table = $wpdb->prefix . 'chatgabi_session_context';
    
    $context = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$session_table} WHERE user_id = %d AND session_id = %s AND expires_at > NOW()",
        $user_id,
        $session_id
    ));
    
    if (!$context) {
        return null;
    }
    
    $context_data = json_decode($context->context_data, true);
    $context_data['conversation_history'] = json_decode($context->conversation_history, true);
    
    return $context_data;
}

/**
 * Personalize template suggestions based on user profile
 */
function chatgabi_personalize_template_suggestions($suggestions, $user_id) {
    $user_context = chatgabi_get_user_context_preferences($user_id);
    $user_profile = chatgabi_get_user_profile($user_id);
    
    if (!$user_profile || empty($user_context)) {
        return $suggestions;
    }
    
    // Score and reorder suggestions based on relevance
    $scored_suggestions = array();
    
    foreach ($suggestions as $suggestion) {
        $relevance_score = chatgabi_calculate_template_relevance($suggestion, $user_context, $user_profile);
        $suggestion['relevance_score'] = $relevance_score;
        $scored_suggestions[] = $suggestion;
    }
    
    // Sort by relevance score
    usort($scored_suggestions, function($a, $b) {
        return $b['relevance_score'] <=> $a['relevance_score'];
    });
    
    // Add personalized templates
    $personalized_templates = chatgabi_generate_personalized_templates($user_context, $user_profile);
    
    return array_merge($personalized_templates, $scored_suggestions);
}

/**
 * Calculate template relevance score
 */
function chatgabi_calculate_template_relevance($template, $user_context, $user_profile) {
    $score = 0;
    
    // Industry match
    if (isset($template['industry']) && $template['industry'] === $user_profile->primary_industry) {
        $score += 30;
    }
    
    // Country match
    if (isset($template['country']) && $template['country'] === $user_profile->target_country) {
        $score += 25;
    }
    
    // Business stage match
    if (isset($template['business_stage']) && $template['business_stage'] === $user_profile->business_stage) {
        $score += 20;
    }
    
    // Experience level match
    if (isset($template['experience_level']) && $template['experience_level'] === $user_profile->experience_level) {
        $score += 15;
    }
    
    // Goals alignment
    if (isset($user_context['goals_primary_goals']) && isset($template['goals'])) {
        $user_goals = json_decode($user_context['goals_primary_goals'], true) ?? array();
        $template_goals = is_array($template['goals']) ? $template['goals'] : array($template['goals']);
        
        $goal_matches = array_intersect($user_goals, $template_goals);
        $score += count($goal_matches) * 5;
    }
    
    return min(100, $score);
}

/**
 * Generate personalized templates
 */
function chatgabi_generate_personalized_templates($user_context, $user_profile) {
    $templates = array();
    
    // Business plan template for user's industry and country
    if ($user_profile->primary_industry && $user_profile->target_country) {
        $templates[] = array(
            'id' => 'personalized_business_plan',
            'title' => "Business Plan for {$user_profile->primary_industry} in " . chatgabi_get_country_name($user_profile->target_country),
            'description' => 'Customized business plan template based on your profile',
            'category' => 'business_planning',
            'relevance_score' => 95,
            'personalized' => true,
            'template_data' => chatgabi_generate_personalized_business_plan_template($user_context, $user_profile)
        );
    }
    
    // Marketing strategy for user's business stage
    if ($user_profile->business_stage) {
        $templates[] = array(
            'id' => 'personalized_marketing',
            'title' => "Marketing Strategy for {$user_profile->business_stage} Stage Business",
            'description' => 'Marketing approach tailored to your business development stage',
            'category' => 'marketing',
            'relevance_score' => 90,
            'personalized' => true,
            'template_data' => chatgabi_generate_personalized_marketing_template($user_context, $user_profile)
        );
    }
    
    return $templates;
}

/**
 * Enhance African context prompt with personal context
 */
function chatgabi_enhance_with_personal_context($context_prompt, $country, $user_id) {
    $user_context = chatgabi_get_user_context_preferences($user_id);
    $user_profile = chatgabi_get_user_profile($user_id);
    
    if (!$user_profile || empty($user_context)) {
        return $context_prompt;
    }
    
    // Add personal business context
    $personal_context = "\n\nPersonalized Context:\n";
    $personal_context .= "- Industry Focus: " . ($user_profile->primary_industry ?? 'General Business') . "\n";
    $personal_context .= "- Business Stage: " . ($user_profile->business_stage ?? 'Idea') . "\n";
    $personal_context .= "- Experience Level: " . ($user_profile->experience_level ?? 'Beginner') . "\n";
    $personal_context .= "- Company Size: " . ($user_profile->business_size ?? 'Small') . "\n";
    
    // Add goals and challenges
    if ($user_profile->primary_goals) {
        $personal_context .= "- Primary Goals: " . $user_profile->primary_goals . "\n";
    }
    
    if ($user_profile->challenges) {
        $personal_context .= "- Key Challenges: " . $user_profile->challenges . "\n";
    }
    
    // Add communication preferences
    if (isset($user_context['preferences_communication_style'])) {
        $personal_context .= "- Preferred Communication Style: " . $user_context['preferences_communication_style'] . "\n";
    }
    
    if (isset($user_context['preferences_detail_level'])) {
        $personal_context .= "- Detail Preference: " . $user_context['preferences_detail_level'] . "\n";
    }
    
    return $context_prompt . $personal_context;
}

/**
 * Determine communication style from profile
 */
function chatgabi_determine_communication_style($profile) {
    // Simple heuristic based on experience level and business stage
    if ($profile->experience_level === 'expert' || $profile->business_stage === 'scaling') {
        return 'professional_concise';
    } elseif ($profile->experience_level === 'beginner' || $profile->business_stage === 'idea') {
        return 'educational_detailed';
    } else {
        return 'balanced_practical';
    }
}

/**
 * Determine detail preference from profile
 */
function chatgabi_determine_detail_preference($profile) {
    // Base on experience level and timeline urgency
    if ($profile->experience_level === 'expert' && $profile->timeline_urgency === 'urgent') {
        return 'high_level_summary';
    } elseif ($profile->experience_level === 'beginner') {
        return 'step_by_step_detailed';
    } else {
        return 'moderate_detail';
    }
}

/**
 * Extract focus areas from profile
 */
function chatgabi_extract_focus_areas($profile) {
    $focus_areas = array();
    
    // Add industry-specific focus
    if ($profile->primary_industry) {
        $focus_areas[] = $profile->primary_industry;
    }
    
    // Add stage-specific focus
    if ($profile->business_stage) {
        $stage_focus_map = array(
            'idea' => array('validation', 'market_research', 'business_model'),
            'startup' => array('funding', 'team_building', 'product_development'),
            'growth' => array('marketing', 'sales', 'operations'),
            'scaling' => array('systems', 'leadership', 'expansion')
        );
        
        if (isset($stage_focus_map[$profile->business_stage])) {
            $focus_areas = array_merge($focus_areas, $stage_focus_map[$profile->business_stage]);
        }
    }
    
    return $focus_areas;
}

/**
 * AJAX handler for updating context preferences
 */
function chatgabi_handle_update_context_preferences() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'chatgabi_context_nonce')) {
        wp_die('Security check failed');
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $preferences = json_decode(stripslashes($_POST['preferences'] ?? '{}'), true);

    if (empty($preferences)) {
        wp_send_json_error('No preferences provided');
        return;
    }

    $result = chatgabi_save_user_context_preferences($user_id, $preferences);

    if ($result) {
        // Regenerate personalized recommendations
        chatgabi_generate_user_recommendations($user_id);

        wp_send_json_success(array(
            'message' => 'Context preferences updated successfully',
            'preferences_count' => count($preferences)
        ));
    } else {
        wp_send_json_error('Failed to update context preferences');
    }
}

/**
 * AJAX handler for getting personalized suggestions
 */
function chatgabi_handle_get_personalized_suggestions() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $suggestion_type = sanitize_text_field($_GET['type'] ?? 'templates');
    $limit = intval($_GET['limit'] ?? 10);

    $suggestions = chatgabi_get_personalized_suggestions($user_id, $suggestion_type, $limit);

    wp_send_json_success(array(
        'suggestions' => $suggestions,
        'total' => count($suggestions),
        'type' => $suggestion_type
    ));
}

/**
 * AJAX handler for saving session context
 */
function chatgabi_handle_save_session_context() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'chatgabi_context_nonce')) {
        wp_die('Security check failed');
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $session_id = sanitize_text_field($_POST['session_id'] ?? '');
    $context_data = json_decode(stripslashes($_POST['context_data'] ?? '{}'), true);
    $expires_in = intval($_POST['expires_in'] ?? 3600);

    if (empty($session_id) || empty($context_data)) {
        wp_send_json_error('Missing required data');
        return;
    }

    $result = chatgabi_save_session_context($user_id, $session_id, $context_data, $expires_in);

    if ($result) {
        wp_send_json_success(array(
            'message' => 'Session context saved successfully',
            'session_id' => $session_id
        ));
    } else {
        wp_send_json_error('Failed to save session context');
    }
}

/**
 * AJAX handler for getting session context
 */
function chatgabi_handle_get_session_context() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $session_id = sanitize_text_field($_GET['session_id'] ?? '');

    if (empty($session_id)) {
        wp_send_json_error('Session ID required');
        return;
    }

    $context = chatgabi_get_session_context($user_id, $session_id);

    if ($context) {
        wp_send_json_success(array(
            'context' => $context,
            'session_id' => $session_id
        ));
    } else {
        wp_send_json_error('Session context not found or expired');
    }
}

/**
 * Get personalized suggestions for user
 */
function chatgabi_get_personalized_suggestions($user_id, $type = 'templates', $limit = 10) {
    global $wpdb;

    $recommendations_table = $wpdb->prefix . 'chatgabi_personalized_recommendations';

    // Get cached recommendations
    $recommendations = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$recommendations_table}
         WHERE user_id = %d AND recommendation_type = %s AND is_active = 1
         ORDER BY relevance_score DESC, last_shown ASC
         LIMIT %d",
        $user_id,
        $type,
        $limit
    ));

    $suggestions = array();

    foreach ($recommendations as $rec) {
        $data = json_decode($rec->recommendation_data, true);
        $data['recommendation_id'] = $rec->id;
        $data['relevance_score'] = $rec->relevance_score;
        $data['interaction_count'] = $rec->interaction_count;

        $suggestions[] = $data;
    }

    // If no cached recommendations, generate them
    if (empty($suggestions)) {
        chatgabi_generate_user_recommendations($user_id);

        // Try again
        $recommendations = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$recommendations_table}
             WHERE user_id = %d AND recommendation_type = %s AND is_active = 1
             ORDER BY relevance_score DESC
             LIMIT %d",
            $user_id,
            $type,
            $limit
        ));

        foreach ($recommendations as $rec) {
            $data = json_decode($rec->recommendation_data, true);
            $data['recommendation_id'] = $rec->id;
            $data['relevance_score'] = $rec->relevance_score;

            $suggestions[] = $data;
        }
    }

    return $suggestions;
}

/**
 * Generate user recommendations
 */
function chatgabi_generate_user_recommendations($user_id) {
    $user_profile = chatgabi_get_user_profile($user_id);
    $user_context = chatgabi_get_user_context_preferences($user_id);

    if (!$user_profile) {
        return false;
    }

    // Generate template recommendations
    chatgabi_generate_template_recommendations($user_id, $user_profile, $user_context);

    // Generate sector data recommendations
    chatgabi_generate_sector_recommendations($user_id, $user_profile, $user_context);

    // Generate opportunity recommendations
    chatgabi_generate_opportunity_recommendations($user_id, $user_profile, $user_context);

    return true;
}

/**
 * Generate template recommendations
 */
function chatgabi_generate_template_recommendations($user_id, $user_profile, $user_context) {
    global $wpdb;

    $recommendations_table = $wpdb->prefix . 'chatgabi_personalized_recommendations';

    // Clear existing template recommendations
    $wpdb->delete($recommendations_table, array(
        'user_id' => $user_id,
        'recommendation_type' => 'templates'
    ));

    $recommendations = array();

    // Industry-specific templates
    if ($user_profile->primary_industry) {
        $recommendations[] = array(
            'type' => 'industry_template',
            'title' => "Business Plan Template for {$user_profile->primary_industry}",
            'description' => 'Industry-specific business planning template',
            'template_id' => 'industry_business_plan',
            'relevance_score' => 90,
            'category' => 'business_planning'
        );

        $recommendations[] = array(
            'type' => 'industry_template',
            'title' => "Marketing Strategy for {$user_profile->primary_industry}",
            'description' => 'Targeted marketing approach for your industry',
            'template_id' => 'industry_marketing',
            'relevance_score' => 85,
            'category' => 'marketing'
        );
    }

    // Stage-specific templates
    if ($user_profile->business_stage) {
        $stage_templates = array(
            'idea' => array(
                array('title' => 'Business Idea Validation Template', 'template_id' => 'idea_validation', 'score' => 95),
                array('title' => 'Market Research Framework', 'template_id' => 'market_research', 'score' => 90),
                array('title' => 'MVP Planning Template', 'template_id' => 'mvp_planning', 'score' => 85)
            ),
            'startup' => array(
                array('title' => 'Startup Funding Proposal', 'template_id' => 'funding_proposal', 'score' => 95),
                array('title' => 'Team Building Strategy', 'template_id' => 'team_building', 'score' => 90),
                array('title' => 'Product Launch Plan', 'template_id' => 'product_launch', 'score' => 85)
            ),
            'growth' => array(
                array('title' => 'Growth Strategy Template', 'template_id' => 'growth_strategy', 'score' => 95),
                array('title' => 'Sales Process Optimization', 'template_id' => 'sales_optimization', 'score' => 90),
                array('title' => 'Customer Retention Plan', 'template_id' => 'customer_retention', 'score' => 85)
            ),
            'scaling' => array(
                array('title' => 'Scaling Operations Plan', 'template_id' => 'scaling_operations', 'score' => 95),
                array('title' => 'Leadership Development', 'template_id' => 'leadership_dev', 'score' => 90),
                array('title' => 'Market Expansion Strategy', 'template_id' => 'market_expansion', 'score' => 85)
            )
        );

        if (isset($stage_templates[$user_profile->business_stage])) {
            foreach ($stage_templates[$user_profile->business_stage] as $template) {
                $recommendations[] = array(
                    'type' => 'stage_template',
                    'title' => $template['title'],
                    'description' => "Template designed for {$user_profile->business_stage} stage businesses",
                    'template_id' => $template['template_id'],
                    'relevance_score' => $template['score'],
                    'category' => 'business_planning'
                );
            }
        }
    }

    // Country-specific templates
    if ($user_profile->target_country) {
        $country_name = chatgabi_get_country_name($user_profile->target_country);

        $recommendations[] = array(
            'type' => 'country_template',
            'title' => "Business Registration Guide for {$country_name}",
            'description' => 'Step-by-step business registration process',
            'template_id' => 'business_registration_' . strtolower($user_profile->target_country),
            'relevance_score' => 80,
            'category' => 'legal_compliance'
        );

        $recommendations[] = array(
            'type' => 'country_template',
            'title' => "Tax Planning for {$country_name} Businesses",
            'description' => 'Tax optimization strategies and compliance',
            'template_id' => 'tax_planning_' . strtolower($user_profile->target_country),
            'relevance_score' => 75,
            'category' => 'financial_planning'
        );
    }

    // Save recommendations
    foreach ($recommendations as $rec) {
        $wpdb->insert(
            $recommendations_table,
            array(
                'user_id' => $user_id,
                'recommendation_type' => 'templates',
                'recommendation_data' => wp_json_encode($rec),
                'relevance_score' => $rec['relevance_score'] / 100,
                'is_active' => 1
            ),
            array('%d', '%s', '%s', '%f', '%d')
        );
    }
}

/**
 * Generate sector recommendations
 */
function chatgabi_generate_sector_recommendations($user_id, $user_profile, $user_context) {
    global $wpdb;

    $recommendations_table = $wpdb->prefix . 'chatgabi_personalized_recommendations';

    // Clear existing sector recommendations
    $wpdb->delete($recommendations_table, array(
        'user_id' => $user_id,
        'recommendation_type' => 'sectors'
    ));

    $recommendations = array();

    // Primary industry sector
    if ($user_profile->primary_industry && $user_profile->target_country) {
        $recommendations[] = array(
            'type' => 'primary_sector',
            'title' => "Latest Trends in {$user_profile->primary_industry}",
            'description' => 'Current market trends and opportunities in your industry',
            'sector' => $user_profile->primary_industry,
            'country' => $user_profile->target_country,
            'relevance_score' => 100,
            'data_type' => 'trends'
        );

        $recommendations[] = array(
            'type' => 'primary_sector',
            'title' => "Regulatory Updates for {$user_profile->primary_industry}",
            'description' => 'Important regulatory changes affecting your industry',
            'sector' => $user_profile->primary_industry,
            'country' => $user_profile->target_country,
            'relevance_score' => 90,
            'data_type' => 'regulations'
        );
    }

    // Related sectors based on business stage
    $related_sectors = chatgabi_get_related_sectors($user_profile->primary_industry, $user_profile->target_country);

    foreach ($related_sectors as $sector) {
        $recommendations[] = array(
            'type' => 'related_sector',
            'title' => "Opportunities in {$sector}",
            'description' => 'Explore related business opportunities',
            'sector' => $sector,
            'country' => $user_profile->target_country,
            'relevance_score' => 70,
            'data_type' => 'opportunities'
        );
    }

    // Save recommendations
    foreach ($recommendations as $rec) {
        $wpdb->insert(
            $recommendations_table,
            array(
                'user_id' => $user_id,
                'recommendation_type' => 'sectors',
                'recommendation_data' => wp_json_encode($rec),
                'relevance_score' => $rec['relevance_score'] / 100,
                'is_active' => 1
            ),
            array('%d', '%s', '%s', '%f', '%d')
        );
    }
}

/**
 * Get related sectors
 */
function chatgabi_get_related_sectors($primary_industry, $country) {
    // Simple mapping of related sectors
    $sector_relationships = array(
        'agriculture' => array('food_processing', 'logistics', 'fintech'),
        'technology' => array('fintech', 'education', 'healthcare'),
        'fintech' => array('technology', 'agriculture', 'retail'),
        'healthcare' => array('technology', 'education', 'pharmaceuticals'),
        'education' => array('technology', 'healthcare', 'media'),
        'retail' => array('logistics', 'fintech', 'manufacturing'),
        'manufacturing' => array('logistics', 'retail', 'technology'),
        'logistics' => array('retail', 'manufacturing', 'agriculture')
    );

    return $sector_relationships[$primary_industry] ?? array();
}

/**
 * Get country name from code
 * Enhanced version with better fallback behavior
 */
if (!function_exists('chatgabi_get_country_name')) {
    function chatgabi_get_country_name($country_code) {
        $countries = array(
            'GH' => 'Ghana',
            'KE' => 'Kenya',
            'NG' => 'Nigeria',
            'ZA' => 'South Africa'
        );

        return $countries[$country_code] ?? $country_code;
    }
}

/**
 * Load user context on login
 */
function chatgabi_load_user_context($user_id) {
    // Enhance user profile with context if not already done
    $context = chatgabi_get_user_context_preferences($user_id);

    if (empty($context)) {
        chatgabi_enhance_user_profile_context($user_id);
    }

    // Generate recommendations if needed
    $recommendations = chatgabi_get_personalized_suggestions($user_id, 'templates', 1);

    if (empty($recommendations)) {
        chatgabi_generate_user_recommendations($user_id);
    }
}

/**
 * Optimize user contexts (scheduled task)
 */
function chatgabi_optimize_user_contexts() {
    global $wpdb;

    // Clean up expired session contexts
    $session_table = $wpdb->prefix . 'chatgabi_session_context';
    $wpdb->query("DELETE FROM {$session_table} WHERE expires_at < NOW()");

    // Update recommendation relevance scores based on usage
    $recommendations_table = $wpdb->prefix . 'chatgabi_personalized_recommendations';

    // Decrease relevance for unused recommendations
    $wpdb->query("
        UPDATE {$recommendations_table}
        SET relevance_score = relevance_score * 0.95
        WHERE last_shown IS NULL AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");

    // Increase relevance for frequently used recommendations
    $wpdb->query("
        UPDATE {$recommendations_table}
        SET relevance_score = LEAST(1.0, relevance_score * 1.1)
        WHERE interaction_count > 5
    ");

    error_log('ChatGABI: User context optimization completed');
}
add_action('chatgabi_optimize_user_contexts', 'chatgabi_optimize_user_contexts');

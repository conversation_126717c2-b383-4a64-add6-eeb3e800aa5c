<?php
/**
 * Credit Purchase AJAX Handlers for BusinessCraft AI
 * 
 * Handles credit package loading and payment initiation
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize credit purchase handlers
 */
function businesscraft_ai_init_credit_purchase_handlers() {
    // Add AJAX handlers
    add_action('wp_ajax_businesscraft_ai_get_credit_packages', 'businesscraft_ai_get_credit_packages');
    add_action('wp_ajax_businesscraft_ai_initiate_payment', 'businesscraft_ai_initiate_payment');
    add_action('wp_ajax_businesscraft_ai_verify_payment', 'businesscraft_ai_verify_payment');
    
    // Add webhook handler for Paystack
    add_action('wp_ajax_nopriv_businesscraft_ai_paystack_webhook', 'businesscraft_ai_handle_paystack_webhook');
    add_action('wp_ajax_businesscraft_ai_paystack_webhook', 'businesscraft_ai_handle_paystack_webhook');
}
add_action('init', 'businesscraft_ai_init_credit_purchase_handlers');

/**
 * Get localized credit packages
 */
function businesscraft_ai_get_credit_packages() {
    // Verify nonce - accept multiple nonce types for compatibility
    $nonce_valid = wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce') ||
                   wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_export_nonce') ||
                   wp_verify_nonce($_POST['nonce'], 'wp_rest');

    if (!$nonce_valid) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    try {
        // Get localized packages
        $packages = businesscraft_ai_get_localized_package_pricing();
        $user_currency = businesscraft_ai_get_user_currency();
        
        wp_send_json_success(array(
            'packages' => $packages,
            'currency' => $user_currency,
            'user_country' => businesscraft_ai_get_user_country()
        ));
        
    } catch (Exception $e) {
        wp_send_json_error('Failed to load packages: ' . $e->getMessage());
    }
}

/**
 * Initiate payment process
 */
function businesscraft_ai_initiate_payment() {
    // Verify nonce - accept both export nonce and credit purchase nonce for compatibility
    $nonce_valid = wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_export_nonce') ||
                   wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce') ||
                   wp_verify_nonce($_POST['nonce'], 'wp_rest');

    if (!$nonce_valid) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $package = sanitize_text_field($_POST['package']);
    $email = sanitize_email($_POST['email']);
    
    // Validate inputs
    if (empty($package) || empty($email)) {
        wp_send_json_error('Missing required fields');
        return;
    }
    
    // Validate email belongs to current user
    $current_user = wp_get_current_user();

    // Enhanced email validation with better error messaging
    if (empty($current_user->user_email)) {
        error_log('BusinessCraft AI: User has no email address set');
        wp_send_json_error('User email not configured. Please update your profile.');
        return;
    }

    // Check if email matches current user's email
    if ($current_user->user_email !== $email) {
        error_log('BusinessCraft AI: Email mismatch - User email: ' . $current_user->user_email . ', Provided: ' . $email);

        // For testing purposes, allow admin users to use any valid email
        if (current_user_can('manage_options') && is_email($email)) {
            error_log('BusinessCraft AI: Admin user using different email for testing: ' . $email);
            // Allow admin to proceed with different email for testing
        } else {
            wp_send_json_error(sprintf(
                'Email mismatch. Please use your registered email address: %s',
                $current_user->user_email
            ));
            return;
        }
    }
    
    try {
        // Log the payment attempt
        error_log('BusinessCraft AI: AJAX Payment initiation started for user ' . $user_id . ', package: ' . $package);

        // Check if Paystack functions are available
        if (!function_exists('businesscraft_ai_initiate_paystack_payment')) {
            error_log('BusinessCraft AI: Paystack function not found');
            wp_send_json_error('Payment system not available');
            return;
        }

        // Check if Paystack is configured
        $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ?
                     BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY :
                     get_option('businesscraft_ai_paystack_secret_key');

        if (empty($secret_key)) {
            error_log('BusinessCraft AI: Paystack secret key not configured');
            wp_send_json_error('Payment system not configured');
            return;
        }

        // Validate package
        $valid_packages = array('starter', 'growth', 'business');
        if (!in_array($package, $valid_packages)) {
            error_log('BusinessCraft AI: Invalid package: ' . $package);
            wp_send_json_error('Invalid package selected');
            return;
        }

        // Initiate Paystack payment
        $payment_result = businesscraft_ai_initiate_paystack_payment($email, $package, $user_id);

        error_log('BusinessCraft AI: Payment result: ' . json_encode($payment_result));

        if (is_array($payment_result) && isset($payment_result['status'])) {
            if ($payment_result['status'] === 'success') {
                wp_send_json_success($payment_result['data']);
            } else {
                wp_send_json_error($payment_result['message'] ?? 'Payment initialization failed');
            }
        } else {
            // Handle case where payment_result is not in expected format
            wp_send_json_error('Unexpected payment response format');
        }

    } catch (Exception $e) {
        error_log('BusinessCraft AI Payment Error: ' . $e->getMessage());
        error_log('BusinessCraft AI Payment Error Trace: ' . $e->getTraceAsString());
        wp_send_json_error('Payment initialization failed: ' . $e->getMessage());
    }
}

/**
 * Verify payment status
 */
function businesscraft_ai_verify_payment() {
    // Verify nonce - accept multiple nonce types for compatibility
    $nonce_valid = wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce') ||
                   wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_export_nonce') ||
                   wp_verify_nonce($_POST['nonce'], 'wp_rest');

    if (!$nonce_valid) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $reference = sanitize_text_field($_POST['reference']);
    
    if (empty($reference)) {
        wp_send_json_error('Missing payment reference');
        return;
    }
    
    try {
        // Verify payment with Paystack
        $verification_result = businesscraft_ai_verify_paystack_payment($reference);
        
        if (is_wp_error($verification_result)) {
            wp_send_json_error($verification_result->get_error_message());
            return;
        }
        
        // Process successful payment
        if ($verification_result['status'] === 'success') {
            $credits_added = businesscraft_ai_process_successful_payment($verification_result, $user_id);
            
            wp_send_json_success(array(
                'message' => 'Payment verified successfully',
                'credits_added' => $credits_added,
                'new_balance' => get_user_meta($user_id, 'businesscraft_credits', true)
            ));
        } else {
            wp_send_json_error('Payment verification failed');
        }
        
    } catch (Exception $e) {
        error_log('BusinessCraft AI Payment Verification Error: ' . $e->getMessage());
        wp_send_json_error('Payment verification failed: ' . $e->getMessage());
    }
}

/**
 * Handle Paystack webhook
 */
function businesscraft_ai_handle_paystack_webhook() {
    // Get the payload
    $payload = file_get_contents('php://input');
    $signature = $_SERVER['HTTP_X_PAYSTACK_SIGNATURE'] ?? '';
    
    // Verify webhook signature
    if (!businesscraft_ai_verify_paystack_signature($payload, $signature)) {
        http_response_code(400);
        exit('Invalid signature');
    }
    
    $event = json_decode($payload, true);
    
    if (!$event || !isset($event['event'])) {
        http_response_code(400);
        exit('Invalid payload');
    }
    
    // Handle different event types
    switch ($event['event']) {
        case 'charge.success':
            businesscraft_ai_handle_successful_payment($event['data']);
            break;
            
        case 'charge.failed':
            businesscraft_ai_handle_failed_payment($event['data']);
            break;
            
        default:
            // Log unknown event
            error_log('BusinessCraft AI: Unknown Paystack event: ' . $event['event']);
            break;
    }
    
    http_response_code(200);
    exit('OK');
}

/**
 * Handle successful payment from webhook
 */
function businesscraft_ai_handle_successful_payment($payment_data) {
    $reference = $payment_data['reference'];
    $metadata = $payment_data['metadata'];
    
    if (!isset($metadata['user_id']) || !isset($metadata['credits'])) {
        error_log('BusinessCraft AI: Invalid payment metadata');
        return;
    }
    
    $user_id = intval($metadata['user_id']);
    $credits = intval($metadata['credits']);
    $package = $metadata['package'] ?? 'unknown';
    
    // Check if payment already processed
    global $wpdb;
    $existing_payment = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM {$wpdb->prefix}businesscraft_ai_credit_transactions 
         WHERE reference = %s AND status = 'completed'",
        $reference
    ));
    
    if ($existing_payment) {
        error_log('BusinessCraft AI: Payment already processed: ' . $reference);
        return;
    }
    
    // Add credits to user account
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    $new_credits = $current_credits + $credits;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);
    
    // Log the transaction
    businesscraft_ai_log_credit_transaction($user_id, $credits, 'purchase', $reference, array(
        'package' => $package,
        'amount' => $payment_data['amount'] / 100, // Convert from kobo/pesewas
        'currency' => $payment_data['currency'],
        'payment_method' => 'paystack'
    ));
    
    // Send confirmation email
    businesscraft_ai_send_purchase_confirmation_email($user_id, $credits, $package);
    
    error_log("BusinessCraft AI: Successfully processed payment {$reference} for user {$user_id}, added {$credits} credits");
}

/**
 * Handle failed payment from webhook
 */
function businesscraft_ai_handle_failed_payment($payment_data) {
    $reference = $payment_data['reference'];
    $metadata = $payment_data['metadata'];
    
    if (isset($metadata['user_id'])) {
        $user_id = intval($metadata['user_id']);
        
        // Log failed transaction
        businesscraft_ai_log_credit_transaction($user_id, 0, 'failed', $reference, array(
            'failure_reason' => $payment_data['gateway_response'] ?? 'Unknown error',
            'amount' => $payment_data['amount'] / 100,
            'currency' => $payment_data['currency']
        ));
        
        error_log("BusinessCraft AI: Payment failed {$reference} for user {$user_id}");
    }
}

/**
 * Process successful payment
 */
function businesscraft_ai_process_successful_payment($payment_data, $user_id) {
    $metadata = $payment_data['metadata'];
    $credits = intval($metadata['credits']);
    $package = $metadata['package'] ?? 'unknown';
    $reference = $payment_data['reference'];
    
    // Add credits to user account
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    $new_credits = $current_credits + $credits;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);
    
    // Log the transaction
    businesscraft_ai_log_credit_transaction($user_id, $credits, 'purchase', $reference, array(
        'package' => $package,
        'amount' => $payment_data['amount'] / 100,
        'currency' => $payment_data['currency'],
        'payment_method' => 'paystack'
    ));
    
    return $credits;
}

/**
 * Log credit transaction
 */
function businesscraft_ai_log_credit_transaction($user_id, $credits, $type, $reference, $metadata = array()) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_credit_transactions';
    
    // Create table if it doesn't exist
    businesscraft_ai_create_credit_transactions_table();
    
    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'credits' => $credits,
            'transaction_type' => $type,
            'reference' => $reference,
            'metadata' => json_encode($metadata),
            'status' => $type === 'failed' ? 'failed' : 'completed',
            'created_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s', '%s', '%s')
    );
}

/**
 * Create credit transactions table
 */
function businesscraft_ai_create_credit_transactions_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_credit_transactions';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        credits int(11) NOT NULL,
        transaction_type varchar(20) NOT NULL,
        reference varchar(100),
        metadata longtext,
        status varchar(20) NOT NULL DEFAULT 'pending',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY transaction_type (transaction_type),
        KEY reference (reference),
        KEY created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Send purchase confirmation email
 */
function businesscraft_ai_send_purchase_confirmation_email($user_id, $credits, $package) {
    $user = get_userdata($user_id);
    if (!$user) {
        return;
    }
    
    $subject = __('Credit Purchase Confirmation - BusinessCraft AI', 'businesscraft-ai');
    
    $message = sprintf(
        __('Dear %s,

Thank you for your purchase! Your credits have been successfully added to your account.

Purchase Details:
- Package: %s
- Credits Added: %s
- New Balance: %s

You can now use these credits to access our AI-powered business tools.

Best regards,
The BusinessCraft AI Team', 'businesscraft-ai'),
        $user->display_name,
        ucfirst($package),
        number_format($credits),
        number_format(get_user_meta($user_id, 'businesscraft_credits', true))
    );
    
    wp_mail($user->user_email, $subject, $message);
}

<?php
/**
 * Aggressive JSON fixer for Kenya file
 */

// Define WordPress constants for testing
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

echo "=== Aggressive Kenya JSON Fixer ===\n\n";

$file_path = WP_CONTENT_DIR . '/datasets/kenya-business-data/kenya_business_data.json';

if (!file_exists($file_path)) {
    echo "❌ File not found: {$file_path}\n";
    exit(1);
}

// Read the file
$content = file_get_contents($file_path);
echo "📄 Original file size: " . strlen($content) . " bytes\n";

// Create backup
$backup_file = $file_path . '.backup.' . date('Y-m-d-H-i-s');
file_put_contents($backup_file, $content);
echo "📦 Backup saved: {$backup_file}\n";

// Multiple fixing strategies
$strategies = [
    'Remove BOM and normalize encoding',
    'Remove all non-printable characters',
    'Convert to UTF-8',
    'Remove smart quotes and special characters',
    'Normalize line endings'
];

$fixed_content = $content;

foreach ($strategies as $i => $strategy) {
    echo "\n🔧 Strategy " . ($i + 1) . ": {$strategy}\n";

    switch ($i) {
        case 0: // Remove BOM and normalize encoding
            $fixed_content = preg_replace('/^\xEF\xBB\xBF/', '', $fixed_content);
            $fixed_content = mb_convert_encoding($fixed_content, 'UTF-8', 'UTF-8');
            break;

        case 1: // Remove all non-printable characters except whitespace
            $fixed_content = preg_replace('/[^\x20-\x7E\x0A\x0D\x09]/', '', $fixed_content);
            break;

        case 2: // Convert to UTF-8 more aggressively
            $fixed_content = iconv('UTF-8', 'UTF-8//IGNORE', $fixed_content);
            break;

        case 3: // Remove smart quotes and special characters
            // Replace common problematic characters with ASCII equivalents using hex codes
            $fixed_content = preg_replace('/\x{201C}|\x{201D}/u', '"', $fixed_content);  // Smart quotes
            $fixed_content = preg_replace('/\x{2018}|\x{2019}/u', "'", $fixed_content);  // Smart apostrophes
            $fixed_content = preg_replace('/\x{2013}|\x{2014}/u', '-', $fixed_content);  // Dashes
            $fixed_content = preg_replace('/\x{2026}/u', '...', $fixed_content);         // Ellipsis
            break;

        case 4: // Normalize line endings
            $fixed_content = str_replace(["\r\n", "\r"], "\n", $fixed_content);
            break;
    }

    // Test JSON validity
    $decoded = json_decode($fixed_content, true);
    $json_error = json_last_error();

    echo "   Size after fix: " . strlen($fixed_content) . " bytes\n";

    if ($json_error === JSON_ERROR_NONE) {
        echo "   ✅ JSON is now valid!\n";
        break;
    } else {
        echo "   ❌ Still invalid: " . json_last_error_msg() . "\n";
    }
}

// Final test
$decoded = json_decode($fixed_content, true);
$json_error = json_last_error();

if ($json_error === JSON_ERROR_NONE) {
    echo "\n🎉 SUCCESS! Saving fixed file...\n";
    file_put_contents($file_path, $fixed_content);

    // Validate structure
    if (isset($decoded['sectors']) && is_array($decoded['sectors'])) {
        echo "✅ Structure validated - found " . count($decoded['sectors']) . " sectors\n";

        // Show first few sector names
        echo "📊 First 5 sectors:\n";
        for ($i = 0; $i < min(5, count($decoded['sectors'])); $i++) {
            if (isset($decoded['sectors'][$i]['sector_name'])) {
                echo "   " . ($i + 1) . ". " . $decoded['sectors'][$i]['sector_name'] . "\n";
            }
        }
    } else {
        echo "⚠️ Warning: sectors array not found or invalid\n";
    }
} else {
    echo "\n❌ FAILED: Could not fix JSON. Error: " . json_last_error_msg() . "\n";

    // Show problematic area
    echo "\n🔍 Analyzing content around potential error...\n";
    $lines = explode("\n", $fixed_content);
    echo "Total lines: " . count($lines) . "\n";

    // Look for common JSON issues
    $issues = [];
    foreach ($lines as $line_num => $line) {
        // Check for trailing commas
        if (preg_match('/,\s*[}\]]/', $line)) {
            $issues[] = "Line " . ($line_num + 1) . ": Possible trailing comma";
        }
        // Check for unescaped quotes
        if (preg_match('/[^\\\\]"[^"]*[^\\\\]"[^"]*"/', $line)) {
            $issues[] = "Line " . ($line_num + 1) . ": Possible unescaped quotes";
        }
    }

    if (!empty($issues)) {
        echo "🚨 Potential issues found:\n";
        foreach (array_slice($issues, 0, 10) as $issue) {
            echo "   - {$issue}\n";
        }
    }
}

echo "\n=== Fix Complete ===\n";

<?php
/**
 * Redis Caching System for ChatGABI
 * Implements high-performance caching for 40% performance improvement
 *
 * @package BusinessCraft_AI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_Redis_Cache {
    
    private $redis;
    private $connected = false;
    private $prefix = 'chatgabi:';
    private $default_ttl = 3600; // 1 hour
    private $fallback_cache = array();
    
    public function __construct() {
        $this->init_redis();
    }
    
    /**
     * Initialize Redis connection
     */
    private function init_redis() {
        if (!class_exists('Redis')) {
            error_log('ChatGABI: Redis extension not installed, using fallback caching');
            return false;
        }
        
        try {
            $this->redis = new Redis();
            
            // Get Redis configuration
            $host = defined('CHATGABI_REDIS_HOST') ? CHATGABI_REDIS_HOST : '127.0.0.1';
            $port = defined('CHATGABI_REDIS_PORT') ? CHATGABI_REDIS_PORT : 6379;
            $password = defined('CHATGABI_REDIS_PASSWORD') ? CHATGABI_REDIS_PASSWORD : null;
            $database = defined('CHATGABI_REDIS_DATABASE') ? CHATGABI_REDIS_DATABASE : 0;
            
            // Connect to Redis
            $this->connected = $this->redis->connect($host, $port, 2.5); // 2.5 second timeout
            
            if ($this->connected && $password) {
                $this->redis->auth($password);
            }
            
            if ($this->connected && $database > 0) {
                $this->redis->select($database);
            }
            
            if ($this->connected) {
                // Set key prefix
                $this->redis->setOption(Redis::OPT_PREFIX, $this->prefix);
                
                // Test connection
                $this->redis->ping();
                
                error_log('ChatGABI: Redis cache connected successfully');
            }
            
        } catch (Exception $e) {
            error_log('ChatGABI: Redis connection failed: ' . $e->getMessage());
            $this->connected = false;
        }
        
        return $this->connected;
    }
    
    /**
     * Get cached value
     */
    public function get($key) {
        if (!$this->connected) {
            return $this->fallback_get($key);
        }
        
        try {
            $value = $this->redis->get($key);
            
            if ($value === false) {
                return false;
            }
            
            // Try to unserialize
            $unserialized = @unserialize($value);
            return $unserialized !== false ? $unserialized : $value;
            
        } catch (Exception $e) {
            error_log('ChatGABI: Redis get error: ' . $e->getMessage());
            return $this->fallback_get($key);
        }
    }
    
    /**
     * Set cached value
     */
    public function set($key, $value, $ttl = null) {
        if ($ttl === null) {
            $ttl = $this->default_ttl;
        }
        
        if (!$this->connected) {
            return $this->fallback_set($key, $value, $ttl);
        }
        
        try {
            // Serialize complex data
            if (is_array($value) || is_object($value)) {
                $value = serialize($value);
            }
            
            return $this->redis->setex($key, $ttl, $value);
            
        } catch (Exception $e) {
            error_log('ChatGABI: Redis set error: ' . $e->getMessage());
            return $this->fallback_set($key, $value, $ttl);
        }
    }
    
    /**
     * Delete cached value
     */
    public function delete($key) {
        if (!$this->connected) {
            return $this->fallback_delete($key);
        }
        
        try {
            return $this->redis->del($key) > 0;
        } catch (Exception $e) {
            error_log('ChatGABI: Redis delete error: ' . $e->getMessage());
            return $this->fallback_delete($key);
        }
    }
    
    /**
     * Check if key exists
     */
    public function exists($key) {
        if (!$this->connected) {
            return $this->fallback_exists($key);
        }
        
        try {
            return $this->redis->exists($key) > 0;
        } catch (Exception $e) {
            error_log('ChatGABI: Redis exists error: ' . $e->getMessage());
            return $this->fallback_exists($key);
        }
    }
    
    /**
     * Increment counter
     */
    public function increment($key, $value = 1) {
        if (!$this->connected) {
            return $this->fallback_increment($key, $value);
        }
        
        try {
            return $this->redis->incrBy($key, $value);
        } catch (Exception $e) {
            error_log('ChatGABI: Redis increment error: ' . $e->getMessage());
            return $this->fallback_increment($key, $value);
        }
    }
    
    /**
     * Set expiration for key
     */
    public function expire($key, $ttl) {
        if (!$this->connected) {
            return true; // Fallback doesn't support expiration
        }
        
        try {
            return $this->redis->expire($key, $ttl);
        } catch (Exception $e) {
            error_log('ChatGABI: Redis expire error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get multiple keys
     */
    public function mget($keys) {
        if (!$this->connected) {
            $result = array();
            foreach ($keys as $key) {
                $result[] = $this->fallback_get($key);
            }
            return $result;
        }
        
        try {
            $values = $this->redis->mget($keys);
            
            // Unserialize values
            foreach ($values as &$value) {
                if ($value !== false) {
                    $unserialized = @unserialize($value);
                    $value = $unserialized !== false ? $unserialized : $value;
                }
            }
            
            return $values;
            
        } catch (Exception $e) {
            error_log('ChatGABI: Redis mget error: ' . $e->getMessage());
            $result = array();
            foreach ($keys as $key) {
                $result[] = $this->fallback_get($key);
            }
            return $result;
        }
    }
    
    /**
     * Flush all cache
     */
    public function flush() {
        if (!$this->connected) {
            $this->fallback_cache = array();
            return true;
        }
        
        try {
            return $this->redis->flushDB();
        } catch (Exception $e) {
            error_log('ChatGABI: Redis flush error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get cache statistics
     */
    public function get_stats() {
        if (!$this->connected) {
            return array(
                'connected' => false,
                'type' => 'fallback',
                'keys' => count($this->fallback_cache)
            );
        }
        
        try {
            $info = $this->redis->info();
            return array(
                'connected' => true,
                'type' => 'redis',
                'used_memory' => $info['used_memory_human'] ?? 'unknown',
                'connected_clients' => $info['connected_clients'] ?? 'unknown',
                'total_commands_processed' => $info['total_commands_processed'] ?? 'unknown',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0
            );
        } catch (Exception $e) {
            return array(
                'connected' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Fallback caching methods (using WordPress transients)
     */
    private function fallback_get($key) {
        // Try memory cache first
        if (isset($this->fallback_cache[$key])) {
            return $this->fallback_cache[$key];
        }
        
        // Try WordPress transients
        $value = get_transient($this->prefix . $key);
        if ($value !== false) {
            $this->fallback_cache[$key] = $value;
        }
        
        return $value;
    }
    
    private function fallback_set($key, $value, $ttl) {
        $this->fallback_cache[$key] = $value;
        return set_transient($this->prefix . $key, $value, $ttl);
    }
    
    private function fallback_delete($key) {
        unset($this->fallback_cache[$key]);
        return delete_transient($this->prefix . $key);
    }
    
    private function fallback_exists($key) {
        return isset($this->fallback_cache[$key]) || get_transient($this->prefix . $key) !== false;
    }
    
    private function fallback_increment($key, $value) {
        $current = $this->fallback_get($key);
        $new_value = ($current !== false ? intval($current) : 0) + $value;
        $this->fallback_set($key, $new_value, $this->default_ttl);
        return $new_value;
    }
    
    /**
     * Check if Redis is connected
     */
    public function is_connected() {
        return $this->connected;
    }
    
    /**
     * Reconnect to Redis
     */
    public function reconnect() {
        if ($this->redis) {
            try {
                $this->redis->close();
            } catch (Exception $e) {
                // Ignore close errors
            }
        }
        
        return $this->init_redis();
    }
}

// Global cache instance
global $chatgabi_cache;
$chatgabi_cache = new BusinessCraft_Redis_Cache();

/**
 * Helper functions for easy cache access
 */
function chatgabi_cache_get($key) {
    global $chatgabi_cache;
    return $chatgabi_cache->get($key);
}

function chatgabi_cache_set($key, $value, $ttl = null) {
    global $chatgabi_cache;
    return $chatgabi_cache->set($key, $value, $ttl);
}

function chatgabi_cache_delete($key) {
    global $chatgabi_cache;
    return $chatgabi_cache->delete($key);
}

function chatgabi_cache_exists($key) {
    global $chatgabi_cache;
    return $chatgabi_cache->exists($key);
}

function chatgabi_cache_increment($key, $value = 1) {
    global $chatgabi_cache;
    return $chatgabi_cache->increment($key, $value);
}

function chatgabi_cache_flush() {
    global $chatgabi_cache;
    return $chatgabi_cache->flush();
}

function chatgabi_cache_stats() {
    global $chatgabi_cache;
    return $chatgabi_cache->get_stats();
}

<?php
/**
 * Simple Analytics Test - Direct Implementation
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions');
}

// Get analytics data
$analytics_data = chatgabi_get_engagement_analytics_data();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Analytics Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { width: 100%; height: 300px; margin: 20px 0; border: 1px solid #ddd; padding: 10px; }
        .summary-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-card { background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }
        .summary-value { font-size: 24px; font-weight: bold; color: #0073aa; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>

<h1>📊 Simple Analytics Test</h1>

<!-- Summary Cards -->
<div class="summary-cards">
    <div class="summary-card">
        <h3>Total Queries</h3>
        <div class="summary-value"><?php echo number_format($analytics_data['summary']['total_queries']); ?></div>
    </div>
    <div class="summary-card">
        <h3>Active Countries</h3>
        <div class="summary-value"><?php echo number_format($analytics_data['summary']['active_countries']); ?></div>
    </div>
    <div class="summary-card">
        <h3>Top Sector</h3>
        <div class="summary-value"><?php echo esc_html($analytics_data['summary']['top_sector']); ?></div>
    </div>
    <div class="summary-card">
        <h3>Opportunities Used</h3>
        <div class="summary-value"><?php echo number_format($analytics_data['summary']['opportunities_included']); ?></div>
    </div>
</div>

<!-- Charts -->
<div class="chart-container">
    <h3>Top Sectors</h3>
    <canvas id="topSectorsChart" width="100%" height="250"></canvas>
</div>

<div class="chart-container">
    <h3>Country Breakdown</h3>
    <canvas id="countryChart" width="100%" height="250"></canvas>
</div>

<!-- Raw Data Display -->
<h2>Raw Analytics Data</h2>
<pre><?php echo json_encode($analytics_data, JSON_PRETTY_PRINT); ?></pre>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

<script>
// Analytics data from PHP
const analyticsData = <?php echo json_encode($analytics_data); ?>;

console.log('Analytics data loaded:', analyticsData);

document.addEventListener('DOMContentLoaded', function() {
    
    // Check Chart.js
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded!');
        return;
    }
    
    console.log('Chart.js version:', Chart.version);
    
    // Top Sectors Chart
    const sectorsCtx = document.getElementById('topSectorsChart');
    if (sectorsCtx && analyticsData.top_sectors) {
        const sectors = analyticsData.top_sectors;
        const labels = Object.keys(sectors);
        const data = Object.values(sectors);
        
        console.log('Sectors chart data:', { labels, data });
        
        if (labels.length > 0) {
            try {
                new Chart(sectorsCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Queries',
                            data: data,
                            backgroundColor: ['#0073aa', '#00a0d2', '#46b450', '#ffb900', '#dc3232']
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        }
                    }
                });
                console.log('Top sectors chart created successfully');
            } catch (e) {
                console.error('Error creating sectors chart:', e);
            }
        } else {
            sectorsCtx.parentElement.innerHTML += '<p>No sector data available</p>';
        }
    }
    
    // Country Chart
    const countryCtx = document.getElementById('countryChart');
    if (countryCtx && analyticsData.country_breakdown) {
        const countries = analyticsData.country_breakdown;
        const labels = Object.keys(countries);
        const data = Object.values(countries);
        
        console.log('Country chart data:', { labels, data });
        
        if (labels.length > 0) {
            try {
                new Chart(countryCtx, {
                    type: 'pie',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: ['#0073aa', '#00a0d2', '#46b450', '#ffb900']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                console.log('Country chart created successfully');
            } catch (e) {
                console.error('Error creating country chart:', e);
            }
        } else {
            countryCtx.parentElement.innerHTML += '<p>No country data available</p>';
        }
    }
});
</script>

<p><a href="<?php echo admin_url('tools.php?page=chatgabi-engagement-analytics'); ?>">← Back to Full Analytics Dashboard</a></p>

</body>
</html>

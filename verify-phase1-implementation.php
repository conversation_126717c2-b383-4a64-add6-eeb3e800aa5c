<?php
/**
 * Simple verification script for Phase 1 implementations
 */

echo "<h1>Phase 1 Critical Fixes - Implementation Verification</h1>\n";
echo "<p>Date: " . date('Y-m-d H:i:s') . "</p>\n";

$base_path = __DIR__;
$theme_path = $base_path . '/wp-content/themes/businesscraft-ai';

echo "<h2>1. Navigation Links Fix</h2>\n";

// Check dashboard file
$dashboard_file = $theme_path . '/page-dashboard.php';
if (file_exists($dashboard_file)) {
    $content = file_get_contents($dashboard_file);
    
    if (strpos($content, 'openCreditPurchaseModal()') !== false) {
        echo "✅ Credit purchase modal function found<br>\n";
    } else {
        echo "❌ Credit purchase modal function missing<br>\n";
    }
    
    if (strpos($content, 'get_page_by_path(\'wizards\')') !== false) {
        echo "✅ Wizards page verification found<br>\n";
    } else {
        echo "❌ Wizards page verification missing<br>\n";
    }
    
    if (strpos($content, 'credit-purchase-modal') !== false) {
        echo "✅ Credit purchase modal HTML found<br>\n";
    } else {
        echo "❌ Credit purchase modal HTML missing<br>\n";
    }
} else {
    echo "❌ Dashboard file not found<br>\n";
}

echo "<h2>2. Template Schema Fix</h2>\n";

// Check REST API file
$rest_api_file = $theme_path . '/inc/rest-api.php';
if (file_exists($rest_api_file)) {
    $content = file_get_contents($rest_api_file);
    
    if (strpos($content, 'prompt_text') !== false && 
        strpos($content, "'prompt_content' => \$template->prompt_text") !== false) {
        echo "✅ REST API column mapping found<br>\n";
    } else {
        echo "❌ REST API column mapping missing<br>\n";
    }
} else {
    echo "❌ REST API file not found<br>\n";
}

echo "<h2>3. Feedback Interface Implementation</h2>\n";

// Check feedback widget
$feedback_widget = $theme_path . '/template-parts/components/feedback-widget.php';
if (file_exists($feedback_widget)) {
    echo "✅ Feedback widget component created<br>\n";
    
    $content = file_get_contents($feedback_widget);
    if (strpos($content, 'feedback-quick') !== false) {
        echo "✅ Quick feedback options found<br>\n";
    }
    if (strpos($content, 'feedback-detailed') !== false) {
        echo "✅ Detailed feedback options found<br>\n";
    }
} else {
    echo "❌ Feedback widget component missing<br>\n";
}

// Check feedback JavaScript
$feedback_js = $theme_path . '/assets/js/feedback.js';
if (file_exists($feedback_js)) {
    echo "✅ Feedback JavaScript file created<br>\n";
    
    $content = file_get_contents($feedback_js);
    if (strpos($content, 'submitFeedback') !== false) {
        echo "✅ Submit feedback function found<br>\n";
    }
    if (strpos($content, 'addFeedbackToMessage') !== false) {
        echo "✅ Add feedback to message function found<br>\n";
    }
} else {
    echo "❌ Feedback JavaScript file missing<br>\n";
}

// Check dashboard feedback tab
if (file_exists($dashboard_file)) {
    $content = file_get_contents($dashboard_file);
    if (strpos($content, 'data-tab="feedback"') !== false) {
        echo "✅ Dashboard feedback tab found<br>\n";
    } else {
        echo "❌ Dashboard feedback tab missing<br>\n";
    }
}

// Check functions.php for script enqueuing
$functions_file = $theme_path . '/functions.php';
if (file_exists($functions_file)) {
    $content = file_get_contents($functions_file);
    if (strpos($content, 'chatgabi-feedback') !== false) {
        echo "✅ Feedback script enqueuing found<br>\n";
    } else {
        echo "❌ Feedback script enqueuing missing<br>\n";
    }
} else {
    echo "❌ Functions file not found<br>\n";
}

echo "<h2>4. Chat Integration</h2>\n";

// Check chat block JavaScript
$chat_js = $theme_path . '/assets/js/chat-block.js';
if (file_exists($chat_js)) {
    echo "✅ Chat block JavaScript file found<br>\n";
    
    $content = file_get_contents($chat_js);
    if (strpos($content, 'addFeedbackToMessage') !== false) {
        echo "✅ Chat feedback integration found<br>\n";
    } else {
        echo "❌ Chat feedback integration missing<br>\n";
    }
    
    if (strpos($content, 'conversationId') !== false) {
        echo "✅ Conversation ID handling found<br>\n";
    }
    if (strpos($content, 'sessionId') !== false) {
        echo "✅ Session ID handling found<br>\n";
    }
} else {
    echo "❌ Chat block JavaScript file missing<br>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p>Phase 1 critical fixes have been implemented. Please test the following:</p>\n";
echo "<ul>\n";
echo "<li>Visit the dashboard and test navigation links</li>\n";
echo "<li>Try the credit purchase modal</li>\n";
echo "<li>Test template operations</li>\n";
echo "<li>Use the chat interface and check for feedback widgets</li>\n";
echo "<li>Check the feedback tab in the dashboard</li>\n";
echo "</ul>\n";

echo "<p><strong>Next:</strong> Proceed to Phase 2 implementation (Export History, Credit Purchase Flow, Preferences Integration)</p>\n";
?>

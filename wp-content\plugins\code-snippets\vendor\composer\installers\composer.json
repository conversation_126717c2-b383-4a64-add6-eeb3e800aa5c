{"name": "composer/installers", "type": "composer-plugin", "license": "MIT", "description": "A multi-framework Composer library installer", "keywords": ["installer", "AGL", "AnnotateCms", "Attogram", "Bitrix", "CakePHP", "Chef", "Cockpit", "CodeIgniter", "concrete5", "ConcreteCMS", "Croogo", "DokuWiki", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ExpressionEngine", "eZ Platform", "FuelPHP", "Grav", "<PERSON><PERSON>", "ImageCMS", "iTop", "Kanboard", "Known", "<PERSON><PERSON>", "Lan Management System", "<PERSON><PERSON>", "Lavalite", "Lithium", "Magento", "majima", "<PERSON><PERSON>", "MantisBT", "<PERSON><PERSON>", "Mautic", "Maya", "MODX", "MODX Evo", "MediaWiki", "Miaoxing", "OXID", "osclass", "MODULEWork", "<PERSON><PERSON><PERSON>", "Pantheon", "<PERSON>wi<PERSON>", "pxcms", "phpBB", "Plentymarkets", "PPI", "<PERSON><PERSON><PERSON>", "Porto", "ProcessWire", "RadPHP", "ReIndex", "Roundcube", "shopware", "SilverStripe", "SMF", "Starbug", "SyDES", "<PERSON><PERSON><PERSON>", "TastyIgniter", "Thelia", "WHMCS", "WolfCMS", "WordPress", "YAWIK", "Zend", "<PERSON><PERSON><PERSON>"], "homepage": "https://composer.github.io/installers/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "autoload-dev": {"psr-4": {"Composer\\Installers\\Test\\": "tests/Composer/Installers/Test"}}, "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "require": {"php": "^7.2 || ^8.0", "composer-plugin-api": "^1.0 || ^2.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "symfony/phpunit-bridge": "^7.1.1", "phpstan/phpstan": "^1.11", "symfony/process": "^5 || ^6 || ^7", "phpstan/phpstan-phpunit": "^1"}, "scripts": {"test": "@php vendor/bin/simple-phpunit", "phpstan": "@php vendor/bin/phpstan analyse"}}
<?php
/**
 * Test file for ChatGABI Templates Interface
 * 
 * This file can be used to test the templates interface implementation
 * Run this by visiting: /wp-content/themes/businesscraft-ai/test-templates-interface.php
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_die('Please log in to test the templates interface.');
}

// Test template categories function
echo "<h2>Testing Template Categories</h2>";
if (function_exists('chatgabi_get_template_categories')) {
    $categories = chatgabi_get_template_categories();
    echo "<p>Found " . count($categories) . " categories:</p>";
    echo "<ul>";
    foreach ($categories as $category) {
        echo "<li>{$category['icon']} {$category['name']} - {$category['description']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Function chatgabi_get_template_categories() not found!</p>";
}

// Test database tables
echo "<h2>Testing Database Tables</h2>";
global $wpdb;

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

$templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table;
$categories_exists = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table;

echo "<p>Templates table exists: " . ($templates_exists ? "✅ Yes" : "❌ No") . "</p>";
echo "<p>Categories table exists: " . ($categories_exists ? "✅ Yes" : "❌ No") . "</p>";

if ($templates_exists) {
    $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
    echo "<p>Templates in database: $template_count</p>";
}

if ($categories_exists) {
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");
    echo "<p>Categories in database: $category_count</p>";
}

// Test REST API endpoints
echo "<h2>Testing REST API Endpoints</h2>";
$rest_base = rest_url('chatgabi/v1/');
echo "<p>REST API Base URL: <a href='$rest_base' target='_blank'>$rest_base</a></p>";

$endpoints = array(
    'templates' => 'Get all templates',
    'template-categories' => 'Get template categories',
    'templates/suggestions' => 'Get AI suggestions (requires credits)',
    'templates/enhance' => 'Enhance template with AI (requires credits)'
);

echo "<ul>";
foreach ($endpoints as $endpoint => $description) {
    $url = $rest_base . $endpoint;
    echo "<li><a href='$url' target='_blank'>$endpoint</a> - $description</li>";
}
echo "</ul>";

// Test user context
echo "<h2>Testing User Context</h2>";
$user_id = get_current_user_id();
$user_profile = get_user_meta($user_id, 'bcai_profile_type', true) ?: 'sme';
$user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
$user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
$user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
$user_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;

echo "<ul>";
echo "<li>User ID: $user_id</li>";
echo "<li>Profile Type: $user_profile</li>";
echo "<li>Industry: " . ($user_industry ?: 'Not set') . "</li>";
echo "<li>Country: $user_country</li>";
echo "<li>Language: $user_language</li>";
echo "<li>Credits: $user_credits</li>";
echo "</ul>";

// Test African Context Engine
echo "<h2>Testing African Context Engine</h2>";
if (class_exists('BusinessCraft_African_Context_Engine')) {
    $context_engine = new BusinessCraft_African_Context_Engine();
    $country_context = $context_engine->get_country_context($user_country);
    echo "<p>✅ African Context Engine loaded</p>";
    echo "<p>Country context for $user_country:</p>";
    echo "<ul>";
    echo "<li>Name: {$country_context['name']}</li>";
    echo "<li>Business Culture: {$country_context['business_culture']}</li>";
    echo "<li>Market Characteristics: {$country_context['market_characteristics']}</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ African Context Engine not found!</p>";
}

// Test OpenAI integration
echo "<h2>Testing OpenAI Integration</h2>";
if (function_exists('businesscraft_ai_process_openai_request')) {
    echo "<p>✅ OpenAI integration function found</p>";
    
    // Check if API key is configured
    $api_key = defined('OPENAI_API_KEY') ? 'Configured' : 'Not configured';
    echo "<p>API Key: $api_key</p>";
} else {
    echo "<p style='color: red;'>❌ OpenAI integration function not found!</p>";
}

// Test file existence
echo "<h2>Testing File Existence</h2>";
$files_to_check = array(
    'page-templates.php' => 'Main template page',
    'assets/css/templates.css' => 'Template styles',
    'assets/js/templates-interface.js' => 'Template JavaScript',
    'inc/template-functions.php' => 'Template functions',
    'inc/rest-api.php' => 'REST API endpoints'
);

foreach ($files_to_check as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    $exists = file_exists($file_path);
    echo "<p>" . ($exists ? "✅" : "❌") . " $file - $description</p>";
}

// Test page template
echo "<h2>Testing Page Template</h2>";
$templates_page = get_page_by_path('templates');
if ($templates_page) {
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    echo "<p>✅ Templates page exists (ID: {$templates_page->ID})</p>";
    echo "<p>Page template: " . ($page_template ?: 'default') . "</p>";
    echo "<p><a href='" . get_permalink($templates_page->ID) . "' target='_blank'>View Templates Page</a></p>";
} else {
    echo "<p style='color: red;'>❌ Templates page not found!</p>";
}

echo "<h2>Implementation Status</h2>";
echo "<p><strong>✅ Completed Components:</strong></p>";
echo "<ul>";
echo "<li>Template page structure (page-templates.php)</li>";
echo "<li>REST API endpoints for template management</li>";
echo "<li>AI-powered enhancement and suggestions</li>";
echo "<li>Template categories system</li>";
echo "<li>User context integration</li>";
echo "<li>CSS styling for responsive design</li>";
echo "<li>JavaScript interface with AJAX functionality</li>";
echo "<li>Integration with existing credit system</li>";
echo "<li>African Context Engine integration</li>";
echo "<li>Multi-language support infrastructure</li>";
echo "</ul>";

echo "<p><strong>🔧 Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Visit the <a href='" . home_url('/templates') . "' target='_blank'>Templates Page</a> to test the interface</li>";
echo "<li>Ensure database tables are created by visiting any page (triggers init hooks)</li>";
echo "<li>Test template creation, editing, and AI enhancement features</li>";
echo "<li>Verify credit system integration works correctly</li>";
echo "<li>Test mobile responsiveness and user experience</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Test completed at " . current_time('Y-m-d H:i:s') . "</em></p>";
?>

<?php
/**
 * ChatGABI Tiered Onboarding Flow System
 * 
 * Handles user onboarding with SME and Creator tracks,
 * profile detection, and personalized dashboard setup.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize onboarding system
 */
function chatgabi_init_onboarding_system() {
    // Create onboarding tables if needed
    chatgabi_create_onboarding_tables();
    
    // Hook into user registration
    add_action('user_register', 'chatgabi_trigger_onboarding');
    add_action('wp_login', 'chatgabi_check_onboarding_status', 10, 2);
}

/**
 * Create onboarding database tables
 */
function chatgabi_create_onboarding_tables() {
    global $wpdb;

    // User profiles table
    $profiles_table = $wpdb->prefix . 'chatgabi_user_profiles';
    
    // Onboarding progress table
    $progress_table = $wpdb->prefix . 'chatgabi_onboarding_progress';

    // Use transient cache to avoid repeated table existence checks
    $cache_key = 'chatgabi_onboarding_tables_exist';
    $tables_exist = get_transient($cache_key);

    if ($tables_exist === false) {
        $profiles_exists = $wpdb->get_var("SHOW TABLES LIKE '{$profiles_table}'") === $profiles_table;
        $progress_exists = $wpdb->get_var("SHOW TABLES LIKE '{$progress_table}'") === $progress_table;
        
        $all_exist = $profiles_exists && $progress_exists;
        set_transient($cache_key, $all_exist ? 'yes' : 'no', HOUR_IN_SECONDS);
        $tables_exist = $all_exist ? 'yes' : 'no';
    }

    if ($tables_exist === 'yes') {
        return true;
    }

    $charset_collate = $wpdb->get_charset_collate();

    // Create user profiles table
    $profiles_sql = "CREATE TABLE {$profiles_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        profile_type varchar(20) NOT NULL DEFAULT 'sme',
        business_stage varchar(50) NOT NULL DEFAULT 'idea',
        primary_industry varchar(100),
        target_country varchar(5),
        business_size varchar(20),
        annual_revenue varchar(50),
        team_size varchar(20),
        primary_goals text,
        challenges text,
        experience_level varchar(20) NOT NULL DEFAULT 'beginner',
        preferred_language varchar(5) NOT NULL DEFAULT 'en',
        marketing_focus varchar(100),
        content_types text,
        target_audience varchar(100),
        brand_stage varchar(50),
        social_platforms text,
        budget_range varchar(50),
        timeline_urgency varchar(20),
        support_preference varchar(50),
        profile_completed tinyint(1) NOT NULL DEFAULT 0,
        onboarding_completed tinyint(1) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_id (user_id),
        KEY profile_type (profile_type),
        KEY business_stage (business_stage),
        KEY target_country (target_country),
        KEY experience_level (experience_level),
        KEY onboarding_completed (onboarding_completed)
    ) $charset_collate;";

    // Create onboarding progress table
    $progress_sql = "CREATE TABLE {$progress_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        step_name varchar(50) NOT NULL,
        step_data longtext,
        completed tinyint(1) NOT NULL DEFAULT 0,
        completed_at datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY step_name (step_name),
        KEY completed (completed),
        UNIQUE KEY user_step (user_id, step_name)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
    $result1 = dbDelta($profiles_sql);
    $result2 = dbDelta($progress_sql);

    // Update cache
    set_transient($cache_key, 'yes', HOUR_IN_SECONDS);

    return !empty($result1) && !empty($result2);
}

/**
 * Trigger onboarding for new users
 */
function chatgabi_trigger_onboarding($user_id) {
    // Create initial profile record
    chatgabi_create_user_profile($user_id);
    
    // Set onboarding flag
    update_user_meta($user_id, 'chatgabi_needs_onboarding', 1);
    update_user_meta($user_id, 'chatgabi_onboarding_started', current_time('mysql'));
}

/**
 * Check onboarding status on login
 */
function chatgabi_check_onboarding_status($user_login, $user) {
    $needs_onboarding = get_user_meta($user->ID, 'chatgabi_needs_onboarding', true);
    
    if ($needs_onboarding) {
        // Redirect to onboarding flow
        wp_redirect(home_url('/onboarding/'));
        exit;
    }
}

/**
 * Create user profile record
 */
function chatgabi_create_user_profile($user_id) {
    global $wpdb;
    
    $profiles_table = $wpdb->prefix . 'chatgabi_user_profiles';
    
    // Check if profile already exists
    $existing = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM {$profiles_table} WHERE user_id = %d",
        $user_id
    ));
    
    if ($existing) {
        return $existing;
    }
    
    // Create new profile
    $result = $wpdb->insert(
        $profiles_table,
        array(
            'user_id' => $user_id,
            'profile_type' => 'sme', // Default to SME
            'business_stage' => 'idea',
            'experience_level' => 'beginner',
            'preferred_language' => 'en'
        ),
        array('%d', '%s', '%s', '%s', '%s')
    );
    
    return $result ? $wpdb->insert_id : false;
}

/**
 * Get user profile
 */
function chatgabi_get_user_profile($user_id) {
    global $wpdb;
    
    $profiles_table = $wpdb->prefix . 'chatgabi_user_profiles';
    
    $profile = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$profiles_table} WHERE user_id = %d",
        $user_id
    ));
    
    return $profile;
}

/**
 * Update user profile
 */
function chatgabi_update_user_profile($user_id, $data) {
    global $wpdb;
    
    $profiles_table = $wpdb->prefix . 'chatgabi_user_profiles';
    
    // Sanitize data
    $profile_data = array();
    
    $allowed_fields = array(
        'profile_type', 'business_stage', 'primary_industry', 'target_country',
        'business_size', 'annual_revenue', 'team_size', 'primary_goals',
        'challenges', 'experience_level', 'preferred_language', 'marketing_focus',
        'content_types', 'target_audience', 'brand_stage', 'social_platforms',
        'budget_range', 'timeline_urgency', 'support_preference', 'profile_completed',
        'onboarding_completed'
    );
    
    foreach ($allowed_fields as $field) {
        if (isset($data[$field])) {
            if (in_array($field, array('primary_goals', 'challenges', 'content_types', 'social_platforms'))) {
                $profile_data[$field] = sanitize_textarea_field($data[$field]);
            } elseif (in_array($field, array('profile_completed', 'onboarding_completed'))) {
                $profile_data[$field] = intval($data[$field]);
            } else {
                $profile_data[$field] = sanitize_text_field($data[$field]);
            }
        }
    }
    
    if (empty($profile_data)) {
        return false;
    }
    
    $result = $wpdb->update(
        $profiles_table,
        $profile_data,
        array('user_id' => $user_id),
        null,
        array('%d')
    );
    
    return $result !== false;
}

/**
 * Save onboarding step progress
 */
function chatgabi_save_onboarding_step($user_id, $step_name, $step_data = array(), $completed = false) {
    global $wpdb;
    
    $progress_table = $wpdb->prefix . 'chatgabi_onboarding_progress';
    
    $data = array(
        'user_id' => $user_id,
        'step_name' => sanitize_text_field($step_name),
        'step_data' => wp_json_encode($step_data),
        'completed' => $completed ? 1 : 0
    );
    
    if ($completed) {
        $data['completed_at'] = current_time('mysql');
    }
    
    // Use INSERT ... ON DUPLICATE KEY UPDATE
    $sql = "INSERT INTO {$progress_table} (user_id, step_name, step_data, completed, completed_at, created_at)
            VALUES (%d, %s, %s, %d, %s, %s)
            ON DUPLICATE KEY UPDATE
            step_data = VALUES(step_data),
            completed = VALUES(completed),
            completed_at = VALUES(completed_at)";
    
    $result = $wpdb->query($wpdb->prepare(
        $sql,
        $user_id,
        $data['step_name'],
        $data['step_data'],
        $data['completed'],
        $data['completed_at'] ?? null,
        current_time('mysql')
    ));
    
    return $result !== false;
}

/**
 * Get onboarding progress
 */
function chatgabi_get_onboarding_progress($user_id) {
    global $wpdb;
    
    $progress_table = $wpdb->prefix . 'chatgabi_onboarding_progress';
    
    $steps = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$progress_table} WHERE user_id = %d ORDER BY created_at ASC",
        $user_id
    ));
    
    $progress = array();
    foreach ($steps as $step) {
        $progress[$step->step_name] = array(
            'data' => json_decode($step->step_data, true),
            'completed' => (bool) $step->completed,
            'completed_at' => $step->completed_at,
            'created_at' => $step->created_at
        );
    }
    
    return $progress;
}

/**
 * Get onboarding flow steps based on profile type
 */
function chatgabi_get_onboarding_steps($profile_type = 'sme') {
    $base_steps = array(
        'welcome' => array(
            'title' => __('Welcome to ChatGABI', 'chatgabi'),
            'description' => __('Let\'s get you set up for success', 'chatgabi'),
            'icon' => '👋'
        ),
        'profile_type' => array(
            'title' => __('Choose Your Path', 'chatgabi'),
            'description' => __('Select the option that best describes you', 'chatgabi'),
            'icon' => '🎯'
        )
    );
    
    if ($profile_type === 'sme') {
        $sme_steps = array(
            'business_basics' => array(
                'title' => __('Business Basics', 'chatgabi'),
                'description' => __('Tell us about your business', 'chatgabi'),
                'icon' => '🏢'
            ),
            'business_stage' => array(
                'title' => __('Business Stage', 'chatgabi'),
                'description' => __('Where are you in your journey?', 'chatgabi'),
                'icon' => '📈'
            ),
            'goals_challenges' => array(
                'title' => __('Goals & Challenges', 'chatgabi'),
                'description' => __('What are your priorities?', 'chatgabi'),
                'icon' => '🎯'
            ),
            'preferences' => array(
                'title' => __('Preferences', 'chatgabi'),
                'description' => __('Customize your experience', 'chatgabi'),
                'icon' => '⚙️'
            )
        );
    } else {
        $sme_steps = array(
            'creator_basics' => array(
                'title' => __('Creator Profile', 'chatgabi'),
                'description' => __('Tell us about your creative work', 'chatgabi'),
                'icon' => '🎨'
            ),
            'content_focus' => array(
                'title' => __('Content Focus', 'chatgabi'),
                'description' => __('What type of content do you create?', 'chatgabi'),
                'icon' => '📝'
            ),
            'audience_brand' => array(
                'title' => __('Audience & Brand', 'chatgabi'),
                'description' => __('Who do you create for?', 'chatgabi'),
                'icon' => '👥'
            ),
            'preferences' => array(
                'title' => __('Preferences', 'chatgabi'),
                'description' => __('Customize your experience', 'chatgabi'),
                'icon' => '⚙️'
            )
        );
    }
    
    $completion_steps = array(
        'template_recommendations' => array(
            'title' => __('Recommended Templates', 'chatgabi'),
            'description' => __('Templates curated for you', 'chatgabi'),
            'icon' => '📋'
        ),
        'dashboard_setup' => array(
            'title' => __('Dashboard Setup', 'chatgabi'),
            'description' => __('Personalize your workspace', 'chatgabi'),
            'icon' => '🏠'
        ),
        'complete' => array(
            'title' => __('All Set!', 'chatgabi'),
            'description' => __('You\'re ready to start using ChatGABI', 'chatgabi'),
            'icon' => '🎉'
        )
    );
    
    return array_merge($base_steps, $sme_steps, $completion_steps);
}

/**
 * Get recommended templates based on user profile
 */
function chatgabi_get_recommended_templates($user_id) {
    $profile = chatgabi_get_user_profile($user_id);

    if (!$profile) {
        return array();
    }

    $recommendations = array();

    if ($profile->profile_type === 'sme') {
        // SME recommendations based on business stage and industry
        $recommendations = array(
            array(
                'title' => __('Business Plan Template', 'chatgabi'),
                'description' => __('Comprehensive business plan for your industry', 'chatgabi'),
                'category' => 'business-planning',
                'priority' => 1
            ),
            array(
                'title' => __('Financial Forecast Template', 'chatgabi'),
                'description' => __('3-year financial projections and budgeting', 'chatgabi'),
                'category' => 'financial-analysis',
                'priority' => 2
            ),
            array(
                'title' => __('Market Analysis Template', 'chatgabi'),
                'description' => __('Analyze your target market and competition', 'chatgabi'),
                'category' => 'market-research',
                'priority' => 3
            )
        );
    } else {
        // Creator recommendations based on content focus
        $recommendations = array(
            array(
                'title' => __('Content Strategy Template', 'chatgabi'),
                'description' => __('Plan your content calendar and strategy', 'chatgabi'),
                'category' => 'marketing-sales',
                'priority' => 1
            ),
            array(
                'title' => __('Brand Development Template', 'chatgabi'),
                'description' => __('Build a strong personal or business brand', 'chatgabi'),
                'category' => 'marketing-sales',
                'priority' => 2
            ),
            array(
                'title' => __('Monetization Strategy Template', 'chatgabi'),
                'description' => __('Turn your content into revenue streams', 'chatgabi'),
                'category' => 'business-planning',
                'priority' => 3
            )
        );
    }

    return $recommendations;
}

/**
 * Complete onboarding process
 */
function chatgabi_complete_onboarding($user_id) {
    // Mark profile as completed
    chatgabi_update_user_profile($user_id, array(
        'profile_completed' => 1,
        'onboarding_completed' => 1
    ));

    // Remove onboarding flag
    delete_user_meta($user_id, 'chatgabi_needs_onboarding');
    update_user_meta($user_id, 'chatgabi_onboarding_completed', current_time('mysql'));

    // Set up personalized dashboard preferences
    chatgabi_setup_personalized_dashboard($user_id);

    return true;
}

/**
 * Set up personalized dashboard based on profile
 */
function chatgabi_setup_personalized_dashboard($user_id) {
    $profile = chatgabi_get_user_profile($user_id);

    if (!$profile) {
        return false;
    }

    // Set dashboard preferences based on profile type
    $dashboard_config = array();

    if ($profile->profile_type === 'sme') {
        $dashboard_config = array(
            'default_tab' => 'overview',
            'featured_tools' => array('business-planning', 'financial-analysis', 'market-research'),
            'quick_actions' => array('create-business-plan', 'financial-forecast', 'market-analysis'),
            'widget_order' => array('credit-balance', 'recent-templates', 'opportunities', 'analytics')
        );
    } else {
        $dashboard_config = array(
            'default_tab' => 'overview',
            'featured_tools' => array('content-strategy', 'brand-development', 'social-media'),
            'quick_actions' => array('content-calendar', 'brand-strategy', 'social-posts'),
            'widget_order' => array('credit-balance', 'content-ideas', 'brand-analytics', 'opportunities')
        );
    }

    update_user_meta($user_id, 'chatgabi_dashboard_config', $dashboard_config);

    return true;
}

/**
 * AJAX handler for saving onboarding step
 */
function chatgabi_ajax_save_onboarding_step() {
    // Start output buffering to prevent headers already sent errors
    if (!ob_get_level()) {
        ob_start();
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_onboarding_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
    }

    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'You must be logged in'));
    }

    $user_id = get_current_user_id();
    $step_name = sanitize_text_field($_POST['step_name']);
    $step_data = $_POST['step_data'] ?? array();
    $completed = intval($_POST['completed']) === 1;

    // Save step progress
    $result = chatgabi_save_onboarding_step($user_id, $step_name, $step_data, $completed);

    if ($result) {
        // If this is profile type step, update user profile
        if ($step_name === 'profile_type' && isset($step_data['profile_type'])) {
            chatgabi_update_user_profile($user_id, array(
                'profile_type' => $step_data['profile_type']
            ));
        }

        // If this is a form step, update user profile with form data
        if (in_array($step_name, array('business_basics', 'creator_basics', 'business_stage', 'goals_challenges', 'content_focus', 'audience_brand', 'preferences'))) {
            chatgabi_update_user_profile($user_id, $step_data);
        }

        wp_send_json_success(array('message' => 'Step saved successfully'));
    } else {
        wp_send_json_error(array('message' => 'Failed to save step'));
    }
}

/**
 * AJAX handler for completing onboarding
 */
function chatgabi_ajax_complete_onboarding() {
    // Start output buffering to prevent headers already sent errors
    if (!ob_get_level()) {
        ob_start();
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_onboarding_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
    }

    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'You must be logged in'));
    }

    $user_id = get_current_user_id();

    // Complete onboarding
    $result = chatgabi_complete_onboarding($user_id);

    if ($result) {
        wp_send_json_success(array(
            'message' => 'Onboarding completed successfully',
            'redirect_url' => home_url('/dashboard/')
        ));
    } else {
        wp_send_json_error(array('message' => 'Failed to complete onboarding'));
    }
}

// Hook AJAX handlers
add_action('wp_ajax_chatgabi_save_onboarding_step', 'chatgabi_ajax_save_onboarding_step');
add_action('wp_ajax_chatgabi_complete_onboarding', 'chatgabi_ajax_complete_onboarding');

// Initialize onboarding system
add_action('init', 'chatgabi_init_onboarding_system');

<?php
/**
 * Test Function Conflict Fix for ChatGABI
 * Verifies that the businesscraft_ai_get_client_ip() function redeclaration issue is resolved
 *
 * @package BusinessCraft_AI
 * @since 1.3.0
 */

// Find and load WordPress
$wp_root = dirname(dirname(dirname(__DIR__)));
$wp_load_path = $wp_root . '/wp-load.php';

if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Error: Could not find WordPress installation');
}

// Verify WordPress is loaded
if (!function_exists('get_template_directory')) {
    die('Error: WordPress not properly loaded');
}

echo "=== ChatGABI Function Conflict Fix Test ===\n";
echo "Test Time: " . current_time('mysql') . "\n\n";

$test_results = array();
$total_score = 0;

// Test 1: Function Existence Check
echo "🔍 Test 1: Function Existence Check\n";
echo "===================================\n";

$function_score = 0;
$function_total = 3;

// Check if the function exists
if (function_exists('businesscraft_ai_get_client_ip')) {
    echo "✅ businesscraft_ai_get_client_ip() function exists\n";
    $function_score++;
    
    // Test function call
    try {
        $ip = businesscraft_ai_get_client_ip();
        echo "✅ Function call successful\n";
        echo "   Returned IP: " . $ip . "\n";
        $function_score++;
        
        // Validate IP format
        if (filter_var($ip, FILTER_VALIDATE_IP) || $ip === 'unknown') {
            echo "✅ Valid IP format returned\n";
            $function_score++;
        } else {
            echo "❌ Invalid IP format: " . $ip . "\n";
        }
    } catch (Exception $e) {
        echo "❌ Function call failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ businesscraft_ai_get_client_ip() function not found\n";
}

$test_results['function_test'] = ($function_score / $function_total) * 100;
echo "Function Test Score: {$function_score}/{$function_total} (" . round($test_results['function_test']) . "%)\n\n";

// Test 2: File Loading Check
echo "📁 Test 2: File Loading Check\n";
echo "=============================\n";

$loading_score = 0;
$loading_total = 4;

// Check if secure-api-key-manager.php loads without errors
$secure_api_file = get_template_directory() . '/inc/secure-api-key-manager.php';
if (file_exists($secure_api_file)) {
    echo "✅ secure-api-key-manager.php file exists\n";
    $loading_score++;
    
    // Check if it's already loaded (should be via functions.php)
    if (class_exists('BusinessCraft_Secure_API_Key_Manager')) {
        echo "✅ Secure API key manager class loaded\n";
        $loading_score++;
    } else {
        echo "⚠️  Secure API key manager class not loaded\n";
    }
} else {
    echo "❌ secure-api-key-manager.php file not found\n";
}

// Check if database.php loads without errors
$database_file = get_template_directory() . '/inc/database.php';
if (file_exists($database_file)) {
    echo "✅ database.php file exists\n";
    $loading_score++;
    
    // Check if database functions are available
    if (function_exists('businesscraft_ai_log_analytics')) {
        echo "✅ Database functions loaded successfully\n";
        $loading_score++;
    } else {
        echo "❌ Database functions not loaded\n";
    }
} else {
    echo "❌ database.php file not found\n";
}

$test_results['loading_test'] = ($loading_score / $loading_total) * 100;
echo "Loading Test Score: {$loading_score}/{$loading_total} (" . round($test_results['loading_test']) . "%)\n\n";

// Test 3: Performance Enhancement Modules
echo "⚡ Test 3: Performance Enhancement Modules\n";
echo "==========================================\n";

$perf_score = 0;
$perf_total = 6;

$performance_files = array(
    'secure-api-key-manager.php' => 'Secure API Key Manager',
    'enhanced-input-validator.php' => 'Enhanced Input Validator',
    'redis-caching.php' => 'Redis Caching',
    'response-streaming.php' => 'Response Streaming',
    'advanced-rate-limiting.php' => 'Advanced Rate Limiting',
    'database-optimization.php' => 'Database Optimization'
);

foreach ($performance_files as $file => $description) {
    $file_path = get_template_directory() . '/inc/' . $file;
    if (file_exists($file_path)) {
        echo "✅ {$description}: File exists\n";
        $perf_score++;
    } else {
        echo "❌ {$description}: File missing\n";
    }
}

$test_results['performance_modules'] = ($perf_score / $perf_total) * 100;
echo "Performance Modules Score: {$perf_score}/{$perf_total} (" . round($test_results['performance_modules']) . "%)\n\n";

// Test 4: Function Usage in Other Modules
echo "🔗 Test 4: Function Usage in Other Modules\n";
echo "==========================================\n";

$usage_score = 0;
$usage_total = 3;

// Test if the function is used correctly in secure-api-key-manager.php
if (function_exists('businesscraft_ai_monitor_api_key_usage')) {
    echo "✅ API monitoring function available\n";
    $usage_score++;
    
    // Test if the function is used correctly in security logging
    if (function_exists('businesscraft_ai_log_security_event')) {
        echo "✅ Security logging function available\n";
        $usage_score++;
    } else {
        echo "❌ Security logging function not available\n";
    }
} else {
    echo "❌ API monitoring function not available\n";
}

// Test if database analytics function works
if (function_exists('businesscraft_ai_log_analytics')) {
    echo "✅ Analytics logging function available\n";
    $usage_score++;
} else {
    echo "❌ Analytics logging function not available\n";
}

$test_results['usage_test'] = ($usage_score / $usage_total) * 100;
echo "Usage Test Score: {$usage_score}/{$usage_total} (" . round($test_results['usage_test']) . "%)\n\n";

// Test 5: Error Prevention Check
echo "🛡️ Test 5: Error Prevention Check\n";
echo "=================================\n";

$error_score = 0;
$error_total = 2;

// Check if function_exists() wrapper is in place
$secure_api_content = file_get_contents($secure_api_file);
if (strpos($secure_api_content, 'if (!function_exists(\'businesscraft_ai_get_client_ip\'))') !== false) {
    echo "✅ Function exists check implemented\n";
    $error_score++;
} else {
    echo "❌ Function exists check not found\n";
}

// Check if duplicate was removed from database.php
$database_content = file_get_contents($database_file);
if (strpos($database_content, 'function businesscraft_ai_get_client_ip()') === false) {
    echo "✅ Duplicate function removed from database.php\n";
    $error_score++;
} else {
    echo "❌ Duplicate function still exists in database.php\n";
}

$test_results['error_prevention'] = ($error_score / $error_total) * 100;
echo "Error Prevention Score: {$error_score}/{$error_total} (" . round($test_results['error_prevention']) . "%)\n\n";

// Calculate overall results
$overall_score = array_sum($test_results) / count($test_results);

// Final Summary
echo "=== FUNCTION CONFLICT FIX TEST RESULTS ===\n";
echo "Overall Score: " . round($overall_score) . "%\n\n";

foreach ($test_results as $test_name => $score) {
    $status = $score >= 80 ? "✅ EXCELLENT" : ($score >= 60 ? "✅ GOOD" : ($score >= 40 ? "⚠️ PARTIAL" : "❌ NEEDS WORK"));
    $test_display = ucwords(str_replace('_', ' ', $test_name));
    echo "{$test_display}: " . round($score) . "% {$status}\n";
}

echo "\n";

if ($overall_score >= 90) {
    echo "🎉 EXCELLENT! Function conflict completely resolved!\n";
    echo "✅ No more fatal errors from function redeclaration\n";
    echo "✅ Enhanced IP detection function is working properly\n";
    echo "✅ All performance enhancement modules can load safely\n";
} elseif ($overall_score >= 75) {
    echo "✅ GOOD! Function conflict mostly resolved.\n";
} elseif ($overall_score >= 60) {
    echo "✅ PARTIAL! Some issues may remain.\n";
} else {
    echo "❌ NEEDS WORK! Function conflict not fully resolved.\n";
}

echo "\n=== TECHNICAL DETAILS ===\n";
echo "Function Location: inc/secure-api-key-manager.php (lines 235-253)\n";
echo "Protection Method: if (!function_exists()) wrapper\n";
echo "Duplicate Removed: inc/database.php (replaced with comment)\n";
echo "Enhanced Features: HTTP_X_REAL_IP support, better error handling\n";

echo "\n=== NEXT STEPS ===\n";
if ($overall_score >= 90) {
    echo "1. ✅ Function conflict resolved - no further action needed\n";
    echo "2. ✅ Performance enhancements can now load properly\n";
    echo "3. ✅ Test the full ChatGABI system functionality\n";
} else {
    echo "1. Review any failed tests above\n";
    echo "2. Ensure all performance enhancement files are present\n";
    echo "3. Check WordPress error logs for any remaining issues\n";
}

// Save results to log
$log_data = array(
    'timestamp' => current_time('mysql'),
    'overall_score' => $overall_score,
    'test_results' => $test_results,
    'function_status' => function_exists('businesscraft_ai_get_client_ip') ? 'exists' : 'missing',
    'ip_result' => function_exists('businesscraft_ai_get_client_ip') ? businesscraft_ai_get_client_ip() : 'N/A'
);

$log_file = get_template_directory() . '/function-conflict-fix-results-' . date('Y-m-d-H-i-s') . '.log';
file_put_contents($log_file, json_encode($log_data, JSON_PRETTY_PRINT));

echo "\nResults saved to: " . basename($log_file) . "\n";

exit($overall_score >= 75 ? 0 : 1);
?>

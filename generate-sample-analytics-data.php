<?php
/**
 * Generate Sample Data for ChatGABI Engagement Analytics Testing
 * This script creates realistic sample data to test all visualizations
 */

// Load WordPress
require_once(dirname(__FILE__) . '/wp-config.php');

echo "<h1>🎲 Generate Sample Analytics Data</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
</style>";

global $wpdb;

// Sample data arrays
$countries = array('Ghana', 'Kenya', 'Nigeria', 'South Africa');

$sectors_by_country = array(
    'Ghana' => array(
        'Creative Arts & Design', 'Digital Marketing', 'Content Creation', 'Photography & Videography',
        'Music Production', 'Fashion Design', 'Graphic Design', 'Web Development',
        'Mobile App Development', 'E-commerce', 'Social Media Management', 'Event Planning',
        'Tourism & Hospitality', 'Food & Beverage', 'Handicrafts', 'Education Technology'
    ),
    'Kenya' => array(
        'Financial Technology', 'Agriculture Technology', 'Healthcare Technology', 'Education Technology',
        'E-commerce', 'Digital Marketing', 'Mobile Money Services', 'Logistics & Transportation',
        'Tourism & Travel', 'Real Estate Technology', 'Renewable Energy', 'Manufacturing',
        'Food Processing', 'Textile & Apparel', 'Information Technology', 'Telecommunications',
        'Media & Entertainment', 'Professional Services', 'Construction', 'Retail Trade',
        'Import & Export', 'Automotive Services', 'Beauty & Wellness', 'Sports & Recreation',
        'Environmental Services', 'Security Services', 'Consulting Services', 'Training & Development'
    ),
    'Nigeria' => array(
        'Fintech & Digital Banking', 'E-commerce & Online Retail', 'Digital Marketing & Advertising',
        'Software Development', 'Mobile App Development', 'Cryptocurrency & Blockchain',
        'Online Education & E-learning'
    ),
    'South Africa' => array(
        'Mining & Resources', 'Manufacturing', 'Financial Services', 'Information Technology',
        'Tourism & Hospitality', 'Agriculture', 'Renewable Energy', 'Healthcare',
        'Education', 'Retail & Consumer Goods', 'Telecommunications', 'Transportation & Logistics',
        'Real Estate', 'Media & Entertainment', 'Professional Services', 'Construction'
    )
);

$sample_messages = array(
    'How can I start a small business in agriculture?',
    'What are the requirements for fintech licensing?',
    'I need help with digital marketing strategies',
    'How to access funding for my startup?',
    'What are the tax implications for e-commerce?',
    'I want to expand my business to other countries',
    'How can I improve my business operations?',
    'What are the best practices for customer service?',
    'I need advice on business registration process',
    'How to create a business plan for investors?',
    'What are the opportunities in renewable energy?',
    'How can I scale my manufacturing business?',
    'I need help with export documentation',
    'What are the requirements for mobile money services?',
    'How to start a tech company in Africa?',
    'I want to learn about cryptocurrency regulations',
    'What are the best marketing channels for SMEs?',
    'How can I improve my online presence?',
    'I need help with financial planning',
    'What are the opportunities in healthcare technology?'
);

// Check if table exists
$sector_logs_table = $wpdb->prefix . 'chatgabi_sector_logs';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_logs_table}'") === $sector_logs_table;

if (!$table_exists) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ Table {$sector_logs_table} does not exist. Please create it first.</p>";
    echo "</div>";
    exit;
}

echo "<div class='section'>";
echo "<h2>📊 Generating Sample Data</h2>";

// Clear existing sample data (optional)
if (isset($_GET['clear']) && $_GET['clear'] === '1') {
    $deleted = $wpdb->query("DELETE FROM {$sector_logs_table} WHERE user_message_preview LIKE '%sample%' OR user_message_preview IN ('" . implode("', '", $sample_messages) . "')");
    echo "<p class='warning'>🗑️ Cleared {$deleted} existing sample records</p>";
}

// Generate sample data
$records_to_generate = 200;
$generated = 0;
$errors = 0;

echo "<p class='info'>🎯 Generating {$records_to_generate} sample records...</p>";

for ($i = 0; $i < $records_to_generate; $i++) {
    // Random country
    $country = $countries[array_rand($countries)];
    
    // Random sector from that country
    $sectors = $sectors_by_country[$country];
    $sector = $sectors[array_rand($sectors)];
    
    // Random message
    $message = $sample_messages[array_rand($sample_messages)];
    
    // Random timestamp within last 30 days
    $days_ago = rand(0, 30);
    $hours_ago = rand(0, 23);
    $minutes_ago = rand(0, 59);
    $timestamp = date('Y-m-d H:i:s', strtotime("-{$days_ago} days -{$hours_ago} hours -{$minutes_ago} minutes"));
    
    // Random opportunities included (0-3)
    $opportunities = rand(0, 3);
    
    // Random token estimate (100-800)
    $tokens = rand(100, 800);
    
    // Random user ID (1-10)
    $user_id = rand(1, 10);
    
    $result = $wpdb->insert(
        $sector_logs_table,
        array(
            'user_id' => $user_id,
            'timestamp' => $timestamp,
            'country' => $country,
            'detected_sector' => $sector,
            'sector_context_found' => 1,
            'prompt_tokens_estimated' => $tokens,
            'user_message_preview' => $message,
            'opportunities_included' => $opportunities,
            'response_quality_rating' => rand(3, 5)
        ),
        array('%d', '%s', '%s', '%s', '%d', '%d', '%s', '%d', '%d')
    );
    
    if ($result) {
        $generated++;
    } else {
        $errors++;
    }
}

echo "<p class='success'>✅ Generated {$generated} sample records</p>";
if ($errors > 0) {
    echo "<p class='error'>❌ {$errors} errors occurred</p>";
}

// Show statistics
$total_records = $wpdb->get_var("SELECT COUNT(*) FROM {$sector_logs_table}");
echo "<p class='info'>📊 Total records in table: {$total_records}</p>";

// Show country breakdown
echo "<h3>🌍 Country Distribution:</h3>";
$country_stats = $wpdb->get_results(
    "SELECT country, COUNT(*) as count 
     FROM {$sector_logs_table} 
     GROUP BY country 
     ORDER BY count DESC"
);

foreach ($country_stats as $stat) {
    $percentage = round(($stat->count / $total_records) * 100, 1);
    echo "<p class='info'>• {$stat->country}: {$stat->count} records ({$percentage}%)</p>";
}

// Show top sectors
echo "<h3>🏆 Top 10 Sectors:</h3>";
$sector_stats = $wpdb->get_results(
    "SELECT detected_sector, COUNT(*) as count 
     FROM {$sector_logs_table} 
     GROUP BY detected_sector 
     ORDER BY count DESC 
     LIMIT 10"
);

foreach ($sector_stats as $stat) {
    echo "<p class='info'>• {$stat->detected_sector}: {$stat->count} queries</p>";
}

// Show keyword frequency sample
echo "<h3>🏷️ Sample Keywords:</h3>";
if (function_exists('chatgabi_get_keyword_frequency')) {
    $keywords = chatgabi_get_keyword_frequency($sector_logs_table);
    $count = 0;
    foreach ($keywords as $keyword => $frequency) {
        if ($count < 10) {
            echo "<p class='info'>• {$keyword}: {$frequency} occurrences</p>";
            $count++;
        }
    }
} else {
    echo "<p class='warning'>⚠️ Keyword frequency function not available</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Next Steps</h2>";
echo "<p><strong>Now you can test the engagement analytics dashboard:</strong></p>";
echo "<ol>";
echo "<li><a href='" . admin_url('tools.php?page=chatgabi-engagement-analytics') . "' target='_blank'>Visit Engagement Analytics Dashboard</a></li>";
echo "<li>Verify that all three visualizations show data:</li>";
echo "<ul>";
echo "<li>• Top Queried Sectors (horizontal bar chart)</li>";
echo "<li>• Country Usage Breakdown (pie chart)</li>";
echo "<li>• Keyword Frequency (tag cloud)</li>";
echo "</ul>";
echo "<li>Test the country filter for sectors chart</li>";
echo "<li>Check that all data refreshes properly</li>";
echo "</ol>";

echo "<p><strong>To clear sample data:</strong> <a href='?clear=1'>Click here to clear sample data</a></p>";
echo "</div>";
?>

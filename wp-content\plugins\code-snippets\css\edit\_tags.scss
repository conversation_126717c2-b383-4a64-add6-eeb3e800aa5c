/**
 * Code based on Tagger, copyright (c) 2018-2022 <PERSON><PERSON><PERSON> <PERSON> <https://jcubic.pl/me>.
 * Released under the MIT license.
 */

.tagger {
	border: 0;
}

.tagger > ul {
	display: flex;
	width: 100%;
	align-items: center;
	padding: 4px 5px 0;
	justify-content: space-between;
	box-sizing: border-box;
	height: auto;
	flex: 0 0 auto;
	overflow-y: auto;
	margin: 0;
	list-style: none;
	border: 1px solid #dfdfdf;
	border-radius: 3px;
	background-color: #fff;
}

.tagger > ul > li {
	padding-bottom: 0.4rem;
	margin: 0.4rem 5px 4px;

	&:not(.tagger-new) {
		a, a:visited {
			text-decoration: none;
			color: black;
		}

		> :first-child {
			padding: 4px 4px 4px 8px;
			background: #B1C3D7;
			border: 1px solid #4181ed;
			border-radius: 3px;
		}

		> span,
		> a > span {
			white-space: nowrap;
		}
	}
}

.tagger li a.close {
	padding: 4px;
	margin-left: 4px;

	&:hover {
		color: white;
	}
}

.tagger .tagger-new {
	flex-grow: 1;
	position: relative;
	min-width: 40px;

	input {
		border: none;
		outline: none;
		box-shadow: none;
		width: 100%;
		padding-left: 0;
		box-sizing: border-box;
		background: transparent;
	}
}

.tagger.wrap > ul {
	flex-wrap: wrap;
	justify-content: start;
}

.tagger-new ul,
.tagger > ul > li:not(.tagger-new) > a,
.tagger li:not(.tagger-new) > span {
	border-radius: 6px;
	border: 1px solid #cad8f3;
	background-color: #dee7f8;

	&:hover {
		background-color: #bbcef1;
		border-color: #6d95e0;
	}
}

.tagger-new ul,
.tagger > ul > li:not(.tagger-new) {
	a, a:visited {
		color: #555;
		font-size: 1.1em;
	}
}

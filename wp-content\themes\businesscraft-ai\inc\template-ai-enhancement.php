<?php
/**
 * Template AI Enhancement System
 * 
 * AI-powered template suggestions and improvements
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get AI-powered template suggestions for user
 */
function businesscraft_ai_get_template_suggestions($user_id) {
    // Get user preferences and history
    $user_country = get_user_meta($user_id, 'bcai_country', true) ?: 'ghana';
    $user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: 'general';
    $user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
    
    // Get user's conversation history to understand interests
    global $wpdb;
    $conversations_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    
    $recent_conversations = $wpdb->get_results($wpdb->prepare(
        "SELECT message FROM $conversations_table 
         WHERE user_id = %d 
         ORDER BY created_at DESC 
         LIMIT 10",
        $user_id
    ));
    
    $conversation_context = '';
    foreach ($recent_conversations as $conv) {
        $conversation_context .= $conv->message . ' ';
    }
    
    // Generate AI suggestions based on user context
    $suggestions = businesscraft_ai_generate_ai_template_suggestions(
        $user_country,
        $user_industry,
        $user_language,
        $conversation_context
    );
    
    return $suggestions;
}

/**
 * Generate AI template suggestions using OpenAI
 */
function businesscraft_ai_generate_ai_template_suggestions($country, $industry, $language, $context) {
    $api_key = defined('BUSINESSCRAFT_AI_OPENAI_API_KEY') ? 
               BUSINESSCRAFT_AI_OPENAI_API_KEY : 
               get_option('businesscraft_ai_openai_api_key');
    
    if (empty($api_key)) {
        return array();
    }
    
    $prompt = sprintf(
        "Based on a user from %s in the %s industry who speaks %s, and their recent conversation context: '%s', 
        suggest 5 specific business template ideas that would be most valuable for them. 
        
        For each suggestion, provide:
        1. Template name
        2. Brief description (2-3 sentences)
        3. Key benefits
        4. Estimated time to complete
        5. Difficulty level (Beginner/Intermediate/Advanced)
        
        Focus on templates that are:
        - Relevant to African business environments
        - Practical and actionable
        - Tailored to their industry and location
        - Based on their apparent interests from conversations
        
        Format as JSON array with objects containing: name, description, benefits, time_estimate, difficulty, category",
        ucfirst($country),
        ucfirst($industry),
        $language,
        substr($context, 0, 500)
    );
    
    $response = businesscraft_ai_call_openai_api($prompt, 400);
    
    if (is_wp_error($response)) {
        return array();
    }
    
    // Parse JSON response
    $suggestions = json_decode($response, true);
    if (!is_array($suggestions)) {
        return array();
    }
    
    return array_slice($suggestions, 0, 5); // Limit to 5 suggestions
}

/**
 * Enhance existing template with AI
 */
function businesscraft_ai_enhance_template($template_id, $enhancement_type = 'improve') {
    global $wpdb;
    
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $templates_table WHERE id = %d",
        $template_id
    ));
    
    if (!$template) {
        return new WP_Error('template_not_found', 'Template not found');
    }
    
    $user_country = get_user_meta($template->user_id, 'bcai_country', true) ?: 'ghana';
    $user_industry = get_user_meta($template->user_id, 'businesscraft_ai_industry', true) ?: 'general';
    
    switch ($enhancement_type) {
        case 'improve':
            return businesscraft_ai_improve_template_content($template, $user_country, $user_industry);
        case 'variations':
            return businesscraft_ai_generate_template_variations($template, $user_country, $user_industry);
        case 'country_optimize':
            return businesscraft_ai_optimize_template_for_country($template, $user_country);
        case 'industry_insights':
            return businesscraft_ai_add_industry_insights($template, $user_industry, $user_country);
        default:
            return new WP_Error('invalid_enhancement', 'Invalid enhancement type');
    }
}

/**
 * Improve template content with AI
 */
function businesscraft_ai_improve_template_content($template, $country, $industry) {
    $prompt = sprintf(
        "Improve this business template for a %s business in %s:

Template Title: %s
Current Content: %s

Please enhance this template by:
1. Making it more specific and actionable
2. Adding relevant examples for %s market
3. Including industry-specific considerations for %s
4. Improving clarity and structure
5. Adding practical tips and best practices

Provide the improved template content while maintaining the original structure and intent.",
        $industry,
        ucfirst($country),
        $template->title,
        $template->prompt_content,
        ucfirst($country),
        $industry
    );
    
    $response = businesscraft_ai_call_openai_api($prompt, 600);
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    return array(
        'type' => 'improvement',
        'original_content' => $template->prompt_content,
        'enhanced_content' => $response,
        'changes_summary' => 'Enhanced with AI improvements for better clarity and local relevance'
    );
}

/**
 * Generate template variations
 */
function businesscraft_ai_generate_template_variations($template, $country, $industry) {
    $prompt = sprintf(
        "Create 3 variations of this business template for different use cases in %s:

Original Template: %s
Content: %s

Generate 3 variations:
1. Simplified version (for beginners)
2. Detailed version (for experienced users)
3. Industry-specific version (optimized for %s sector)

For each variation, provide:
- Title
- Modified content
- Target audience
- Key differences from original

Format as JSON array.",
        ucfirst($country),
        $template->title,
        $template->prompt_content,
        $industry
    );
    
    $response = businesscraft_ai_call_openai_api($prompt, 800);
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    $variations = json_decode($response, true);
    if (!is_array($variations)) {
        return new WP_Error('parse_error', 'Failed to parse AI response');
    }
    
    return array(
        'type' => 'variations',
        'original_template' => $template,
        'variations' => $variations
    );
}

/**
 * Optimize template for specific country
 */
function businesscraft_ai_optimize_template_for_country($template, $country) {
    $prompt = sprintf(
        "Optimize this business template specifically for %s market conditions and business environment:

Template: %s
Content: %s

Please adapt this template by:
1. Including %s-specific regulations and requirements
2. Adding local market insights and opportunities
3. Incorporating cultural considerations
4. Including relevant local examples and case studies
5. Adjusting language and terminology for local context
6. Adding information about local business resources and support

Provide the country-optimized version.",
        ucfirst($country),
        $template->title,
        $template->prompt_content,
        ucfirst($country)
    );
    
    $response = businesscraft_ai_call_openai_api($prompt, 700);
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    return array(
        'type' => 'country_optimization',
        'country' => $country,
        'original_content' => $template->prompt_content,
        'optimized_content' => $response,
        'optimization_focus' => sprintf('Optimized for %s market conditions', ucfirst($country))
    );
}

/**
 * Add industry insights to template
 */
function businesscraft_ai_add_industry_insights($template, $industry, $country) {
    $prompt = sprintf(
        "Enhance this business template with specific insights for the %s industry in %s:

Template: %s
Content: %s

Add industry-specific insights including:
1. Current %s industry trends in %s
2. Key challenges and opportunities
3. Regulatory considerations
4. Market dynamics and competition
5. Success factors and best practices
6. Relevant metrics and KPIs
7. Industry-specific resources and networks

Integrate these insights naturally into the existing template structure.",
        $industry,
        ucfirst($country),
        $template->title,
        $template->prompt_content,
        $industry,
        ucfirst($country)
    );
    
    $response = businesscraft_ai_call_openai_api($prompt, 700);
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    return array(
        'type' => 'industry_insights',
        'industry' => $industry,
        'country' => $country,
        'original_content' => $template->prompt_content,
        'enhanced_content' => $response,
        'insights_added' => sprintf('Added %s industry insights for %s market', $industry, ucfirst($country))
    );
}

/**
 * Call OpenAI API for template enhancement
 */
function businesscraft_ai_call_openai_api($prompt, $max_tokens = 500) {
    $api_key = defined('BUSINESSCRAFT_AI_OPENAI_API_KEY') ? 
               BUSINESSCRAFT_AI_OPENAI_API_KEY : 
               get_option('businesscraft_ai_openai_api_key');
    
    if (empty($api_key)) {
        return new WP_Error('api_key_missing', 'OpenAI API key not configured');
    }
    
    $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
        ),
        'body' => json_encode(array(
            'model' => 'gpt-3.5-turbo',
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'You are an expert business consultant specializing in African markets and business template creation.'
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => $max_tokens,
            'temperature' => 0.7
        )),
        'timeout' => 30
    ));
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if (!isset($data['choices'][0]['message']['content'])) {
        return new WP_Error('api_error', 'Invalid API response');
    }
    
    return $data['choices'][0]['message']['content'];
}

/**
 * AJAX handler for getting template suggestions
 */
function businesscraft_ai_ajax_get_template_suggestions() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $suggestions = businesscraft_ai_get_template_suggestions($user_id);
    wp_send_json_success($suggestions);
}

/**
 * AJAX handler for enhancing templates
 */
function businesscraft_ai_ajax_enhance_template() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $template_id = intval($_POST['template_id']);
    $enhancement_type = sanitize_text_field($_POST['enhancement_type']);
    
    if (!$template_id) {
        wp_send_json_error('Template ID required');
        return;
    }
    
    // Verify user owns the template
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $template_owner = $wpdb->get_var($wpdb->prepare(
        "SELECT user_id FROM $templates_table WHERE id = %d",
        $template_id
    ));
    
    if ($template_owner != $user_id) {
        wp_send_json_error('Access denied');
        return;
    }
    
    $enhancement = businesscraft_ai_enhance_template($template_id, $enhancement_type);
    
    if (is_wp_error($enhancement)) {
        wp_send_json_error($enhancement->get_error_message());
        return;
    }
    
    wp_send_json_success($enhancement);
}

// Register AJAX handlers
add_action('wp_ajax_businesscraft_ai_get_template_suggestions', 'businesscraft_ai_ajax_get_template_suggestions');
add_action('wp_ajax_businesscraft_ai_enhance_template', 'businesscraft_ai_ajax_enhance_template');

/**
 * BusinessCraft AI Admin JavaScript
 */

(function($) {
    'use strict';

    // Test OpenAI connection
    window.businesscraftTestOpenAI = function() {
        const button = event.target;
        const originalText = button.textContent;

        button.textContent = 'Testing...';
        button.disabled = true;

        $.ajax({
            url: businesscraftAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'businesscraft_ai_test_openai',
                nonce: businesscraftAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('OpenAI connection successful!');
                } else {
                    alert('OpenAI connection failed: ' + response.data);
                }
            },
            error: function() {
                alert('Network error while testing OpenAI connection.');
            },
            complete: function() {
                button.textContent = originalText;
                button.disabled = false;
            }
        });
    };

    // Test Paystack connection
    window.businesscraftTestPaystack = function() {
        const button = event.target;
        const originalText = button.textContent;

        button.textContent = 'Testing...';
        button.disabled = true;

        $.ajax({
            url: businesscraftAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'businesscraft_ai_test_paystack',
                nonce: businesscraftAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('Paystack connection successful!');
                } else {
                    alert('Paystack connection failed: ' + response.data);
                }
            },
            error: function() {
                alert('Network error while testing Paystack connection.');
            },
            complete: function() {
                button.textContent = originalText;
                button.disabled = false;
            }
        });
    };

    // Check IP whitelist status
    window.businesscraftCheckIPWhitelist = function() {
        const button = event.target;
        const originalText = button.textContent;

        button.textContent = 'Checking...';
        button.disabled = true;

        $.ajax({
            url: businesscraftAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'businesscraft_ai_check_ip_whitelist',
                nonce: businesscraftAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    let message = 'Server IP: ' + data.server_ip + '\n';
                    message += 'Status: ' + data.message;

                    if (data.status === 'error' && data.message.includes('IP address')) {
                        message += '\n\nTo fix this:\n';
                        message += '1. Login to Paystack Dashboard\n';
                        message += '2. Go to Settings → API Keys & Webhooks\n';
                        message += '3. Add IP: ' + data.server_ip + ' to whitelist\n';
                        message += '4. Save and test again';
                    }

                    alert(message);
                } else {
                    alert('IP whitelist check failed: ' + response.data);
                }
            },
            error: function() {
                alert('Network error while checking IP whitelist.');
            },
            complete: function() {
                button.textContent = originalText;
                button.disabled = false;

                // Refresh the page to show updated status
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }
        });
    };

    // Initialize charts if Chart.js is available
    $(document).ready(function() {
        if (typeof Chart !== 'undefined' && typeof chartData !== 'undefined') {
            initializeCharts();
        }
    });

    function initializeCharts() {
        // Usage Chart
        const usageCtx = document.getElementById('usageChart');
        if (usageCtx && chartData.usage) {
            new Chart(usageCtx, {
                type: 'line',
                data: {
                    labels: chartData.usage.map(item => item.date),
                    datasets: [{
                        label: 'Daily Chats',
                        data: chartData.usage.map(item => item.count),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Chat Usage Over Time'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Language Chart
        const languageCtx = document.getElementById('languageChart');
        if (languageCtx && chartData.languages) {
            new Chart(languageCtx, {
                type: 'doughnut',
                data: {
                    labels: chartData.languages.map(item => item.language.toUpperCase()),
                    datasets: [{
                        data: chartData.languages.map(item => item.count),
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Language Distribution'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Token Efficiency Chart
        const tokenCtx = document.getElementById('tokenChart');
        if (tokenCtx && chartData.tokens) {
            new Chart(tokenCtx, {
                type: 'line',
                data: {
                    labels: chartData.tokens.map(item => item.date),
                    datasets: [{
                        label: 'Avg Tokens per Request',
                        data: chartData.tokens.map(item => parseFloat(item.avg_tokens)),
                        borderColor: '#f5576c',
                        backgroundColor: 'rgba(245, 87, 108, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Token Efficiency Over Time'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Average Tokens'
                            }
                        }
                    }
                }
            });
        }
    }

})(jQuery);

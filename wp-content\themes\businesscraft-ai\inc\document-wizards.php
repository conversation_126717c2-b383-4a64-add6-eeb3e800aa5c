<?php
/**
 * AI-Powered Document Creation Wizards for BusinessCraft AI
 * 
 * Handles step-by-step guided document creation including:
 * - Business Plan Generator Wizard
 * - Marketing Strategy Wizard
 * - Financial Forecast Generator
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize enhanced document wizards functionality with African market intelligence
 */
function businesscraft_ai_init_document_wizards() {
    // Add AJAX handlers
    add_action('wp_ajax_start_wizard', 'businesscraft_ai_handle_start_wizard');
    add_action('wp_ajax_wizard_step', 'businesscraft_ai_handle_wizard_step');
    add_action('wp_ajax_save_wizard_progress', 'businesscraft_ai_handle_save_wizard_progress');
    add_action('wp_ajax_load_wizard_progress', 'businesscraft_ai_handle_load_wizard_progress');
    add_action('wp_ajax_complete_wizard', 'businesscraft_ai_handle_complete_wizard');

    // Enhanced AI assistance handlers
    add_action('wp_ajax_wizard_ai_suggestions', 'businesscraft_ai_handle_wizard_ai_suggestions');
    add_action('wp_ajax_wizard_market_intelligence', 'businesscraft_ai_handle_wizard_market_intelligence');
    add_action('wp_ajax_wizard_competitor_analysis', 'businesscraft_ai_handle_wizard_competitor_analysis');
    add_action('wp_ajax_wizard_financial_projections', 'businesscraft_ai_handle_wizard_financial_projections');

    // Add REST API endpoints
    add_action('rest_api_init', 'businesscraft_ai_register_wizard_routes');

    // Enqueue wizard scripts
    add_action('wp_enqueue_scripts', 'businesscraft_ai_enqueue_wizard_scripts');

    // Add wizard pages
    add_action('init', 'businesscraft_ai_add_wizard_pages');

    // Initialize wizard database tables
    add_action('init', 'businesscraft_ai_create_wizard_tables');
}
add_action('init', 'businesscraft_ai_init_document_wizards');

/**
 * Register REST API routes for document wizards
 */
function businesscraft_ai_register_wizard_routes() {
    register_rest_route('businesscraft-ai/v1', '/wizards/(?P<type>[a-zA-Z0-9-_]+)/start', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_start_wizard',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'type' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array('business-plan', 'marketing-strategy', 'financial-forecast')
            )
        )
    ));
    
    register_rest_route('businesscraft-ai/v1', '/wizards/(?P<id>\d+)/step', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_wizard_step',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'step_data' => array(
                'required' => true,
                'type' => 'object'
            ),
            'current_step' => array(
                'required' => true,
                'type' => 'integer'
            )
        )
    ));
    
    register_rest_route('businesscraft-ai/v1', '/wizards/(?P<id>\d+)/complete', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_complete_wizard',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/wizards/progress', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_wizard_progress',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
}

/**
 * Enqueue wizard scripts and styles
 */
function businesscraft_ai_enqueue_wizard_scripts() {
    if (is_page('wizards') || is_page('business-plan-wizard') || is_page('marketing-wizard') || is_page('financial-wizard')) {
        wp_enqueue_script(
            'businesscraft-ai-wizards',
            get_template_directory_uri() . '/assets/js/document-wizards.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('businesscraft-ai-wizards', 'businesscraftWizards', array(
            'restUrl' => rest_url('businesscraft-ai/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'currentUserId' => get_current_user_id(),
            'strings' => array(
                'processing' => __('Processing...', 'businesscraft-ai'),
                'generating' => __('Generating AI content...', 'businesscraft-ai'),
                'saving' => __('Saving progress...', 'businesscraft-ai'),
                'stepCompleted' => __('Step completed successfully!', 'businesscraft-ai'),
                'wizardCompleted' => __('Document wizard completed!', 'businesscraft-ai'),
                'errorOccurred' => __('An error occurred. Please try again.', 'businesscraft-ai'),
                'confirmExit' => __('Are you sure you want to exit? Your progress will be saved.', 'businesscraft-ai'),
                'aiThinking' => __('AI is analyzing your input...', 'businesscraft-ai'),
                'generateSuggestions' => __('Generate AI Suggestions', 'businesscraft-ai'),
                'nextStep' => __('Continue to Next Step', 'businesscraft-ai'),
                'previousStep' => __('Go Back', 'businesscraft-ai'),
                'completeWizard' => __('Complete Document', 'businesscraft-ai')
            )
        ));
        
        wp_enqueue_style(
            'businesscraft-ai-wizards',
            get_template_directory_uri() . '/assets/css/document-wizards.css',
            array(),
            '1.0.0'
        );
    }
}

/**
 * Add wizard pages
 */
function businesscraft_ai_add_wizard_pages() {
    // This would typically be done through WordPress admin or programmatically
    // For now, we'll handle routing in the existing pages
}

/**
 * Get wizard configuration
 */
function businesscraft_ai_get_wizard_config($wizard_type) {
    $configs = array(
        'business-plan' => array(
            'name' => __('Business Plan Generator', 'businesscraft-ai'),
            'description' => __('Create a comprehensive business plan with AI guidance', 'businesscraft-ai'),
            'icon' => '📋',
            'estimated_time' => '15-20 minutes',
            'steps' => array(
                1 => array(
                    'title' => __('Business Overview', 'businesscraft-ai'),
                    'description' => __('Define your business concept and mission', 'businesscraft-ai'),
                    'fields' => array(
                        'business_name' => array(
                            'type' => 'text',
                            'label' => __('Business Name', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'business_concept' => array(
                            'type' => 'textarea',
                            'label' => __('Business Concept', 'businesscraft-ai'),
                            'placeholder' => __('Describe your business idea in detail...', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'mission_statement' => array(
                            'type' => 'textarea',
                            'label' => __('Mission Statement', 'businesscraft-ai'),
                            'placeholder' => __('What is your company\'s mission?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                2 => array(
                    'title' => __('Market Analysis', 'businesscraft-ai'),
                    'description' => __('Analyze your target market and competition', 'businesscraft-ai'),
                    'fields' => array(
                        'target_market' => array(
                            'type' => 'textarea',
                            'label' => __('Target Market', 'businesscraft-ai'),
                            'placeholder' => __('Describe your ideal customers...', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'market_size' => array(
                            'type' => 'text',
                            'label' => __('Market Size', 'businesscraft-ai'),
                            'placeholder' => __('Estimated market size...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'competition_analysis' => array(
                            'type' => 'textarea',
                            'label' => __('Competition Analysis', 'businesscraft-ai'),
                            'placeholder' => __('Who are your main competitors?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                3 => array(
                    'title' => __('Products & Services', 'businesscraft-ai'),
                    'description' => __('Detail your offerings and value proposition', 'businesscraft-ai'),
                    'fields' => array(
                        'products_services' => array(
                            'type' => 'textarea',
                            'label' => __('Products & Services', 'businesscraft-ai'),
                            'placeholder' => __('What will you offer to customers?', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'value_proposition' => array(
                            'type' => 'textarea',
                            'label' => __('Value Proposition', 'businesscraft-ai'),
                            'placeholder' => __('What makes you unique?', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'pricing_strategy' => array(
                            'type' => 'textarea',
                            'label' => __('Pricing Strategy', 'businesscraft-ai'),
                            'placeholder' => __('How will you price your offerings?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                4 => array(
                    'title' => __('Operations & Management', 'businesscraft-ai'),
                    'description' => __('Define your operational structure and team', 'businesscraft-ai'),
                    'fields' => array(
                        'business_model' => array(
                            'type' => 'textarea',
                            'label' => __('Business Model', 'businesscraft-ai'),
                            'placeholder' => __('How will your business operate?', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'team_structure' => array(
                            'type' => 'textarea',
                            'label' => __('Team Structure', 'businesscraft-ai'),
                            'placeholder' => __('Key team members and roles...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'operational_plan' => array(
                            'type' => 'textarea',
                            'label' => __('Operational Plan', 'businesscraft-ai'),
                            'placeholder' => __('Day-to-day operations...', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                5 => array(
                    'title' => __('Financial Projections', 'businesscraft-ai'),
                    'description' => __('Create financial forecasts and funding requirements', 'businesscraft-ai'),
                    'fields' => array(
                        'startup_costs' => array(
                            'type' => 'number',
                            'label' => __('Startup Costs', 'businesscraft-ai'),
                            'placeholder' => __('Initial investment needed...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'revenue_projections' => array(
                            'type' => 'textarea',
                            'label' => __('Revenue Projections', 'businesscraft-ai'),
                            'placeholder' => __('Expected revenue for next 3 years...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'funding_requirements' => array(
                            'type' => 'textarea',
                            'label' => __('Funding Requirements', 'businesscraft-ai'),
                            'placeholder' => __('How much funding do you need?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                )
            )
        ),
        'marketing-strategy' => array(
            'name' => __('Marketing Strategy Wizard', 'businesscraft-ai'),
            'description' => __('Develop a comprehensive marketing strategy with AI insights', 'businesscraft-ai'),
            'icon' => '📈',
            'estimated_time' => '10-15 minutes',
            'steps' => array(
                1 => array(
                    'title' => __('Marketing Objectives', 'businesscraft-ai'),
                    'description' => __('Define your marketing goals and objectives', 'businesscraft-ai'),
                    'fields' => array(
                        'marketing_goals' => array(
                            'type' => 'textarea',
                            'label' => __('Marketing Goals', 'businesscraft-ai'),
                            'placeholder' => __('What do you want to achieve with marketing?', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'target_audience' => array(
                            'type' => 'textarea',
                            'label' => __('Target Audience', 'businesscraft-ai'),
                            'placeholder' => __('Who is your ideal customer?', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'brand_positioning' => array(
                            'type' => 'textarea',
                            'label' => __('Brand Positioning', 'businesscraft-ai'),
                            'placeholder' => __('How do you want to be perceived?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                2 => array(
                    'title' => __('Market Research', 'businesscraft-ai'),
                    'description' => __('Analyze your market and competitive landscape', 'businesscraft-ai'),
                    'fields' => array(
                        'market_trends' => array(
                            'type' => 'textarea',
                            'label' => __('Market Trends', 'businesscraft-ai'),
                            'placeholder' => __('Current trends in your industry...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'competitor_analysis' => array(
                            'type' => 'textarea',
                            'label' => __('Competitor Analysis', 'businesscraft-ai'),
                            'placeholder' => __('How do competitors market themselves?', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'market_opportunities' => array(
                            'type' => 'textarea',
                            'label' => __('Market Opportunities', 'businesscraft-ai'),
                            'placeholder' => __('What opportunities do you see?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                3 => array(
                    'title' => __('Marketing Channels', 'businesscraft-ai'),
                    'description' => __('Select and plan your marketing channels', 'businesscraft-ai'),
                    'fields' => array(
                        'digital_channels' => array(
                            'type' => 'checkbox_group',
                            'label' => __('Digital Marketing Channels', 'businesscraft-ai'),
                            'options' => array(
                                'social_media' => __('Social Media Marketing', 'businesscraft-ai'),
                                'content_marketing' => __('Content Marketing', 'businesscraft-ai'),
                                'email_marketing' => __('Email Marketing', 'businesscraft-ai'),
                                'seo' => __('Search Engine Optimization', 'businesscraft-ai'),
                                'paid_ads' => __('Paid Advertising', 'businesscraft-ai'),
                                'influencer' => __('Influencer Marketing', 'businesscraft-ai')
                            ),
                            'ai_assist' => true
                        ),
                        'traditional_channels' => array(
                            'type' => 'checkbox_group',
                            'label' => __('Traditional Marketing Channels', 'businesscraft-ai'),
                            'options' => array(
                                'print_media' => __('Print Media', 'businesscraft-ai'),
                                'radio' => __('Radio', 'businesscraft-ai'),
                                'tv' => __('Television', 'businesscraft-ai'),
                                'outdoor' => __('Outdoor Advertising', 'businesscraft-ai'),
                                'events' => __('Events & Trade Shows', 'businesscraft-ai'),
                                'direct_mail' => __('Direct Mail', 'businesscraft-ai')
                            ),
                            'ai_assist' => true
                        ),
                        'channel_strategy' => array(
                            'type' => 'textarea',
                            'label' => __('Channel Strategy', 'businesscraft-ai'),
                            'placeholder' => __('How will you use these channels?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                4 => array(
                    'title' => __('Budget & Timeline', 'businesscraft-ai'),
                    'description' => __('Plan your marketing budget and timeline', 'businesscraft-ai'),
                    'fields' => array(
                        'marketing_budget' => array(
                            'type' => 'number',
                            'label' => __('Total Marketing Budget', 'businesscraft-ai'),
                            'placeholder' => __('Annual marketing budget...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'budget_allocation' => array(
                            'type' => 'textarea',
                            'label' => __('Budget Allocation', 'businesscraft-ai'),
                            'placeholder' => __('How will you allocate your budget?', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'campaign_timeline' => array(
                            'type' => 'textarea',
                            'label' => __('Campaign Timeline', 'businesscraft-ai'),
                            'placeholder' => __('Marketing campaign schedule...', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                )
            )
        ),
        'financial-forecast' => array(
            'name' => __('Financial Forecast Generator', 'businesscraft-ai'),
            'description' => __('Create detailed financial projections with AI assistance', 'businesscraft-ai'),
            'icon' => '💰',
            'estimated_time' => '12-18 minutes',
            'steps' => array(
                1 => array(
                    'title' => __('Business Basics', 'businesscraft-ai'),
                    'description' => __('Basic information about your business', 'businesscraft-ai'),
                    'fields' => array(
                        'business_type' => array(
                            'type' => 'select',
                            'label' => __('Business Type', 'businesscraft-ai'),
                            'options' => array(
                                'product' => __('Product-based Business', 'businesscraft-ai'),
                                'service' => __('Service-based Business', 'businesscraft-ai'),
                                'hybrid' => __('Product + Service Business', 'businesscraft-ai'),
                                'saas' => __('Software as a Service', 'businesscraft-ai'),
                                'ecommerce' => __('E-commerce', 'businesscraft-ai')
                            ),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'revenue_model' => array(
                            'type' => 'textarea',
                            'label' => __('Revenue Model', 'businesscraft-ai'),
                            'placeholder' => __('How will you generate revenue?', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'forecast_period' => array(
                            'type' => 'select',
                            'label' => __('Forecast Period', 'businesscraft-ai'),
                            'options' => array(
                                '1' => __('1 Year', 'businesscraft-ai'),
                                '3' => __('3 Years', 'businesscraft-ai'),
                                '5' => __('5 Years', 'businesscraft-ai')
                            ),
                            'default' => '3',
                            'ai_assist' => false
                        )
                    )
                ),
                2 => array(
                    'title' => __('Revenue Projections', 'businesscraft-ai'),
                    'description' => __('Estimate your revenue streams and growth', 'businesscraft-ai'),
                    'fields' => array(
                        'primary_revenue_stream' => array(
                            'type' => 'textarea',
                            'label' => __('Primary Revenue Stream', 'businesscraft-ai'),
                            'placeholder' => __('Main source of income...', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'year1_revenue' => array(
                            'type' => 'number',
                            'label' => __('Year 1 Revenue Estimate', 'businesscraft-ai'),
                            'placeholder' => __('Expected revenue in first year...', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'growth_rate' => array(
                            'type' => 'number',
                            'label' => __('Annual Growth Rate (%)', 'businesscraft-ai'),
                            'placeholder' => __('Expected annual growth percentage...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'seasonal_factors' => array(
                            'type' => 'textarea',
                            'label' => __('Seasonal Factors', 'businesscraft-ai'),
                            'placeholder' => __('Any seasonal variations in revenue?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                3 => array(
                    'title' => __('Cost Structure', 'businesscraft-ai'),
                    'description' => __('Define your business costs and expenses', 'businesscraft-ai'),
                    'fields' => array(
                        'fixed_costs' => array(
                            'type' => 'textarea',
                            'label' => __('Fixed Costs', 'businesscraft-ai'),
                            'placeholder' => __('Rent, salaries, insurance, etc...', 'businesscraft-ai'),
                            'required' => true,
                            'ai_assist' => true
                        ),
                        'variable_costs' => array(
                            'type' => 'textarea',
                            'label' => __('Variable Costs', 'businesscraft-ai'),
                            'placeholder' => __('Materials, commissions, etc...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'startup_costs' => array(
                            'type' => 'number',
                            'label' => __('Initial Startup Costs', 'businesscraft-ai'),
                            'placeholder' => __('One-time startup expenses...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'cost_assumptions' => array(
                            'type' => 'textarea',
                            'label' => __('Cost Assumptions', 'businesscraft-ai'),
                            'placeholder' => __('Key assumptions about your costs...', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                ),
                4 => array(
                    'title' => __('Cash Flow & Profitability', 'businesscraft-ai'),
                    'description' => __('Analyze cash flow and profitability projections', 'businesscraft-ai'),
                    'fields' => array(
                        'break_even_analysis' => array(
                            'type' => 'textarea',
                            'label' => __('Break-even Analysis', 'businesscraft-ai'),
                            'placeholder' => __('When do you expect to break even?', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'cash_flow_projections' => array(
                            'type' => 'textarea',
                            'label' => __('Cash Flow Projections', 'businesscraft-ai'),
                            'placeholder' => __('Monthly cash flow expectations...', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'funding_needs' => array(
                            'type' => 'number',
                            'label' => __('Funding Needs', 'businesscraft-ai'),
                            'placeholder' => __('How much funding do you need?', 'businesscraft-ai'),
                            'ai_assist' => true
                        ),
                        'risk_factors' => array(
                            'type' => 'textarea',
                            'label' => __('Financial Risk Factors', 'businesscraft-ai'),
                            'placeholder' => __('What could impact your projections?', 'businesscraft-ai'),
                            'ai_assist' => true
                        )
                    )
                )
            )
        )
    );
    
    return isset($configs[$wizard_type]) ? $configs[$wizard_type] : null;
}

/**
 * Start a new wizard session
 */
function businesscraft_ai_start_wizard($wizard_type, $user_data = array()) {
    global $wpdb;

    $user_id = get_current_user_id();
    $config = businesscraft_ai_get_wizard_config($wizard_type);

    if (!$config) {
        return array(
            'success' => false,
            'message' => __('Invalid wizard type', 'businesscraft-ai')
        );
    }

    // Create wizard sessions table if it doesn't exist
    businesscraft_ai_create_wizard_tables();

    $table_name = $wpdb->prefix . 'chatgabi_wizard_sessions';

    // Check for existing incomplete session
    $existing_session = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name}
         WHERE user_id = %d AND wizard_type = %s AND status = 'in_progress'
         ORDER BY created_at DESC LIMIT 1",
        $user_id,
        $wizard_type
    ));

    if ($existing_session) {
        return array(
            'success' => true,
            'message' => __('Resuming existing wizard session', 'businesscraft-ai'),
            'wizard_id' => $existing_session->id,
            'current_step' => $existing_session->current_step,
            'wizard_data' => json_decode($existing_session->wizard_data, true),
            'config' => $config
        );
    }

    // Create new wizard session
    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'wizard_type' => $wizard_type,
            'current_step' => 1,
            'wizard_data' => json_encode($user_data),
            'status' => 'in_progress',
            'created_at' => current_time('mysql')
        ),
        array('%d', '%s', '%d', '%s', '%s', '%s')
    );

    if ($result) {
        return array(
            'success' => true,
            'message' => __('Wizard started successfully', 'businesscraft-ai'),
            'wizard_id' => $wpdb->insert_id,
            'current_step' => 1,
            'wizard_data' => $user_data,
            'config' => $config
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to start wizard', 'businesscraft-ai')
        );
    }
}

/**
 * Process wizard step with AI assistance
 */
function businesscraft_ai_process_wizard_step($wizard_id, $step_data, $current_step) {
    global $wpdb;

    $user_id = get_current_user_id();
    $table_name = $wpdb->prefix . 'chatgabi_wizard_sessions';

    // Get wizard session
    $session = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE id = %d AND user_id = %d",
        $wizard_id,
        $user_id
    ));

    if (!$session) {
        return array(
            'success' => false,
            'message' => __('Wizard session not found', 'businesscraft-ai')
        );
    }

    $wizard_data = json_decode($session->wizard_data, true) ?: array();
    $config = businesscraft_ai_get_wizard_config($session->wizard_type);

    // Merge step data
    $wizard_data['steps'][$current_step] = $step_data;

    // Generate AI suggestions for fields with ai_assist enabled
    $ai_suggestions = array();
    if (isset($config['steps'][$current_step]['fields'])) {
        foreach ($config['steps'][$current_step]['fields'] as $field_name => $field_config) {
            if (isset($field_config['ai_assist']) && $field_config['ai_assist'] && isset($step_data[$field_name])) {
                $ai_suggestions[$field_name] = businesscraft_ai_generate_field_suggestions(
                    $session->wizard_type,
                    $field_name,
                    $step_data[$field_name],
                    $wizard_data
                );
            }
        }
    }

    // Update wizard session
    $next_step = $current_step + 1;
    $total_steps = count($config['steps']);

    $update_data = array(
        'current_step' => min($next_step, $total_steps),
        'wizard_data' => json_encode($wizard_data),
        'updated_at' => current_time('mysql')
    );

    $wpdb->update(
        $table_name,
        $update_data,
        array('id' => $wizard_id),
        array('%d', '%s', '%s'),
        array('%d')
    );

    return array(
        'success' => true,
        'message' => __('Step processed successfully', 'businesscraft-ai'),
        'current_step' => $current_step,
        'next_step' => $next_step,
        'is_final_step' => $next_step > $total_steps,
        'ai_suggestions' => $ai_suggestions,
        'wizard_data' => $wizard_data
    );
}

/**
 * Generate AI suggestions for a field
 */
function businesscraft_ai_generate_field_suggestions($wizard_type, $field_name, $field_value, $wizard_data) {
    // Get user context
    $user_id = get_current_user_id();
    $user_profile = chatgabi_get_user_profile($user_id);

    // Build context for AI
    $context = array(
        'wizard_type' => $wizard_type,
        'field_name' => $field_name,
        'field_value' => $field_value,
        'user_country' => $user_profile['country'] ?? 'GH',
        'user_industry' => $user_profile['industry'] ?? '',
        'wizard_data' => $wizard_data
    );

    // Generate AI prompt based on wizard type and field
    $prompt = businesscraft_ai_build_wizard_ai_prompt($context);

    if (!$prompt) {
        return array();
    }

    // Call OpenAI API
    $ai_response = chatgabi_call_openai_api($prompt, array(
        'max_tokens' => 300,
        'temperature' => 0.7
    ));

    if ($ai_response['success']) {
        return array(
            'suggestions' => $ai_response['content'],
            'field_name' => $field_name,
            'context' => $wizard_type
        );
    }

    return array();
}

/**
 * Build AI prompt for wizard field assistance
 */
function businesscraft_ai_build_wizard_ai_prompt($context) {
    $wizard_type = $context['wizard_type'];
    $field_name = $context['field_name'];
    $field_value = $context['field_value'];
    $country = $context['user_country'];
    $industry = $context['user_industry'];

    $country_name = chatgabi_get_country_name_from_code($country);

    $prompts = array(
        'business-plan' => array(
            'business_concept' => "As an expert business consultant specializing in {$country_name} markets, provide 3 specific suggestions to improve this business concept: '{$field_value}'. Focus on opportunities in {$country_name}'s economy and market conditions. Keep suggestions practical and actionable.",

            'mission_statement' => "Help refine this mission statement for a business in {$country_name}: '{$field_value}'. Provide 2-3 alternative versions that are more compelling and reflect local market values and culture.",

            'target_market' => "Analyze this target market description for {$country_name}: '{$field_value}'. Provide specific insights about this market segment in {$country_name}, including size estimates, characteristics, and how to reach them effectively.",

            'competition_analysis' => "Based on this competition analysis: '{$field_value}', provide specific insights about the competitive landscape in {$country_name}. Suggest 3 strategies to differentiate from competitors in this market.",

            'value_proposition' => "Enhance this value proposition for the {$country_name} market: '{$field_value}'. Suggest improvements that would resonate better with local customers and highlight unique advantages.",

            'revenue_projections' => "Review these revenue projections: '{$field_value}'. Provide realistic benchmarks for similar businesses in {$country_name} and suggest factors that could impact these projections."
        ),

        'marketing-strategy' => array(
            'marketing_goals' => "Analyze these marketing goals: '{$field_value}'. Provide specific, measurable alternatives that are realistic for businesses in {$country_name}. Include local market considerations.",

            'target_audience' => "Refine this target audience description for {$country_name}: '{$field_value}'. Provide demographic insights, behavioral patterns, and media consumption habits specific to this market.",

            'brand_positioning' => "Improve this brand positioning for the {$country_name} market: '{$field_value}'. Suggest positioning strategies that align with local culture and market preferences.",

            'market_trends' => "Expand on these market trends: '{$field_value}'. Provide current trends specific to {$country_name} that could impact marketing strategy, including digital adoption and consumer behavior.",

            'channel_strategy' => "Optimize this channel strategy for {$country_name}: '{$field_value}'. Recommend the most effective marketing channels based on local market data and consumer preferences."
        ),

        'financial-forecast' => array(
            'revenue_model' => "Analyze this revenue model: '{$field_value}'. Provide insights on how this model performs in {$country_name}'s market, including pricing considerations and revenue optimization strategies.",

            'year1_revenue' => "Review this Year 1 revenue estimate: '{$field_value}'. Provide realistic benchmarks for similar businesses in {$country_name} and factors that could influence this projection.",

            'fixed_costs' => "Analyze these fixed costs: '{$field_value}'. Provide typical cost ranges for similar businesses in {$country_name} and suggest areas for cost optimization.",

            'break_even_analysis' => "Improve this break-even analysis: '{$field_value}'. Provide specific calculations and realistic timelines based on {$country_name} market conditions.",

            'funding_needs' => "Review these funding needs: '{$field_value}'. Suggest funding sources available in {$country_name} and provide guidance on funding strategy."
        )
    );

    if (isset($prompts[$wizard_type][$field_name])) {
        return $prompts[$wizard_type][$field_name];
    }

    return null;
}

/**
 * Complete wizard and generate final document
 */
function businesscraft_ai_complete_wizard($wizard_id) {
    global $wpdb;

    $user_id = get_current_user_id();
    $table_name = $wpdb->prefix . 'chatgabi_wizard_sessions';

    // Get wizard session
    $session = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE id = %d AND user_id = %d",
        $wizard_id,
        $user_id
    ));

    if (!$session) {
        return array(
            'success' => false,
            'message' => __('Wizard session not found', 'businesscraft-ai')
        );
    }

    $wizard_data = json_decode($session->wizard_data, true) ?: array();

    // Generate final document using AI
    $document_result = businesscraft_ai_generate_wizard_document($session->wizard_type, $wizard_data);

    if (!$document_result['success']) {
        return $document_result;
    }

    // Save as template
    $template_result = businesscraft_ai_save_wizard_as_template($session, $document_result['content']);

    if ($template_result['success']) {
        // Mark wizard as completed
        $wpdb->update(
            $table_name,
            array(
                'status' => 'completed',
                'template_id' => $template_result['template_id'],
                'completed_at' => current_time('mysql')
            ),
            array('id' => $wizard_id),
            array('%s', '%d', '%s'),
            array('%d')
        );

        return array(
            'success' => true,
            'message' => __('Document wizard completed successfully!', 'businesscraft-ai'),
            'template_id' => $template_result['template_id'],
            'document_content' => $document_result['content']
        );
    }

    return $template_result;
}

/**
 * Generate final document from wizard data
 */
function businesscraft_ai_generate_wizard_document($wizard_type, $wizard_data) {
    // Get user context
    $user_id = get_current_user_id();
    $user_profile = chatgabi_get_user_profile($user_id);

    // Build comprehensive prompt for document generation
    $prompt = businesscraft_ai_build_document_generation_prompt($wizard_type, $wizard_data, $user_profile);

    // Call OpenAI API for document generation
    $ai_response = chatgabi_call_openai_api($prompt, array(
        'max_tokens' => 2000,
        'temperature' => 0.7
    ));

    if ($ai_response['success']) {
        return array(
            'success' => true,
            'content' => $ai_response['content'],
            'wizard_type' => $wizard_type
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to generate document content', 'businesscraft-ai')
        );
    }
}

/**
 * Build document generation prompt
 */
function businesscraft_ai_build_document_generation_prompt($wizard_type, $wizard_data, $user_profile) {
    $country = $user_profile['country'] ?? 'GH';
    $country_name = chatgabi_get_country_name_from_code($country);

    $base_prompt = "You are an expert business consultant specializing in {$country_name} markets. ";

    switch ($wizard_type) {
        case 'business-plan':
            $prompt = $base_prompt . "Create a comprehensive business plan based on the following information:\n\n";

            if (isset($wizard_data['steps'])) {
                foreach ($wizard_data['steps'] as $step_num => $step_data) {
                    $prompt .= "Step {$step_num} Data:\n";
                    foreach ($step_data as $field => $value) {
                        $prompt .= "- " . ucwords(str_replace('_', ' ', $field)) . ": {$value}\n";
                    }
                    $prompt .= "\n";
                }
            }

            $prompt .= "Generate a professional business plan with the following sections:\n";
            $prompt .= "1. Executive Summary\n";
            $prompt .= "2. Business Description\n";
            $prompt .= "3. Market Analysis\n";
            $prompt .= "4. Products and Services\n";
            $prompt .= "5. Marketing and Sales Strategy\n";
            $prompt .= "6. Operations Plan\n";
            $prompt .= "7. Management Team\n";
            $prompt .= "8. Financial Projections\n";
            $prompt .= "9. Funding Requirements\n";
            $prompt .= "10. Risk Analysis\n\n";
            $prompt .= "Focus on {$country_name} market conditions, regulations, and opportunities. Make it detailed and professional.";
            break;

        case 'marketing-strategy':
            $prompt = $base_prompt . "Create a comprehensive marketing strategy based on the following information:\n\n";

            if (isset($wizard_data['steps'])) {
                foreach ($wizard_data['steps'] as $step_num => $step_data) {
                    $prompt .= "Step {$step_num} Data:\n";
                    foreach ($step_data as $field => $value) {
                        if (is_array($value)) {
                            $prompt .= "- " . ucwords(str_replace('_', ' ', $field)) . ": " . implode(', ', $value) . "\n";
                        } else {
                            $prompt .= "- " . ucwords(str_replace('_', ' ', $field)) . ": {$value}\n";
                        }
                    }
                    $prompt .= "\n";
                }
            }

            $prompt .= "Generate a professional marketing strategy with the following sections:\n";
            $prompt .= "1. Executive Summary\n";
            $prompt .= "2. Market Analysis\n";
            $prompt .= "3. Target Audience Profile\n";
            $prompt .= "4. Brand Positioning\n";
            $prompt .= "5. Marketing Objectives\n";
            $prompt .= "6. Marketing Mix Strategy\n";
            $prompt .= "7. Digital Marketing Plan\n";
            $prompt .= "8. Traditional Marketing Plan\n";
            $prompt .= "9. Budget Allocation\n";
            $prompt .= "10. Implementation Timeline\n";
            $prompt .= "11. Metrics and KPIs\n\n";
            $prompt .= "Focus on {$country_name} market preferences, digital adoption, and cultural considerations.";
            break;

        case 'financial-forecast':
            $prompt = $base_prompt . "Create a detailed financial forecast based on the following information:\n\n";

            if (isset($wizard_data['steps'])) {
                foreach ($wizard_data['steps'] as $step_num => $step_data) {
                    $prompt .= "Step {$step_num} Data:\n";
                    foreach ($step_data as $field => $value) {
                        $prompt .= "- " . ucwords(str_replace('_', ' ', $field)) . ": {$value}\n";
                    }
                    $prompt .= "\n";
                }
            }

            $prompt .= "Generate a comprehensive financial forecast with the following sections:\n";
            $prompt .= "1. Executive Summary\n";
            $prompt .= "2. Revenue Model Analysis\n";
            $prompt .= "3. Revenue Projections (Monthly/Yearly)\n";
            $prompt .= "4. Cost Structure Analysis\n";
            $prompt .= "5. Operating Expenses Forecast\n";
            $prompt .= "6. Cash Flow Projections\n";
            $prompt .= "7. Break-even Analysis\n";
            $prompt .= "8. Profit & Loss Projections\n";
            $prompt .= "9. Balance Sheet Projections\n";
            $prompt .= "10. Funding Requirements\n";
            $prompt .= "11. Financial Ratios and KPIs\n";
            $prompt .= "12. Risk Analysis and Scenarios\n\n";
            $prompt .= "Use {$country_name} currency and consider local economic conditions, tax rates, and business costs.";
            break;

        default:
            $prompt = $base_prompt . "Create a professional business document based on the provided information.";
    }

    return $prompt;
}

/**
 * Save wizard result as template
 */
function businesscraft_ai_save_wizard_as_template($session, $content) {
    global $wpdb;

    $templates_table = $wpdb->prefix . 'chatgabi_generated_templates';
    $wizard_data = json_decode($session->wizard_data, true) ?: array();

    // Extract key information for template
    $business_idea = '';
    $industry_sector = '';

    if (isset($wizard_data['steps'][1])) {
        $step1 = $wizard_data['steps'][1];
        $business_idea = $step1['business_concept'] ?? $step1['business_name'] ?? $step1['marketing_goals'] ?? '';
        $industry_sector = $step1['business_type'] ?? '';
    }

    // Get user profile for additional context
    $user_profile = chatgabi_get_user_profile($session->user_id);

    $template_name = businesscraft_ai_generate_wizard_template_name($session->wizard_type, $wizard_data);

    $result = $wpdb->insert(
        $templates_table,
        array(
            'user_id' => $session->user_id,
            'template_name' => $template_name,
            'template_type' => $session->wizard_type,
            'business_idea' => $business_idea,
            'industry_sector' => $industry_sector,
            'target_country' => $user_profile['country'] ?? 'GH',
            'business_stage' => 'startup',
            'generated_content' => $content,
            'wizard_data' => $session->wizard_data,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
    );

    if ($result) {
        return array(
            'success' => true,
            'template_id' => $wpdb->insert_id,
            'template_name' => $template_name
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to save template', 'businesscraft-ai')
        );
    }
}

/**
 * Generate template name from wizard data
 */
function businesscraft_ai_generate_wizard_template_name($wizard_type, $wizard_data) {
    $base_names = array(
        'business-plan' => __('Business Plan', 'businesscraft-ai'),
        'marketing-strategy' => __('Marketing Strategy', 'businesscraft-ai'),
        'financial-forecast' => __('Financial Forecast', 'businesscraft-ai')
    );

    $base_name = $base_names[$wizard_type] ?? __('Business Document', 'businesscraft-ai');

    // Try to get business name or concept for personalization
    if (isset($wizard_data['steps'][1])) {
        $step1 = $wizard_data['steps'][1];
        $business_name = $step1['business_name'] ?? '';
        $business_concept = $step1['business_concept'] ?? '';

        if (!empty($business_name)) {
            return $base_name . ' - ' . $business_name;
        } elseif (!empty($business_concept)) {
            $short_concept = wp_trim_words($business_concept, 3, '');
            return $base_name . ' - ' . $short_concept;
        }
    }

    return $base_name . ' - ' . date('M j, Y');
}

/**
 * Create wizard database tables
 */
function businesscraft_ai_create_wizard_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Wizard sessions table
    $sessions_table = $wpdb->prefix . 'chatgabi_wizard_sessions';
    $sessions_sql = "CREATE TABLE IF NOT EXISTS {$sessions_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        wizard_type varchar(50) NOT NULL,
        current_step int(11) NOT NULL DEFAULT 1,
        wizard_data longtext,
        status varchar(20) NOT NULL DEFAULT 'in_progress',
        template_id bigint(20) NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        completed_at datetime NULL,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY wizard_type (wizard_type),
        KEY status (status),
        KEY created_at (created_at)
    ) {$charset_collate};";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sessions_sql);
}

/**
 * AJAX Handlers
 */
function businesscraft_ai_handle_start_wizard() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_die(__('Security check failed', 'businesscraft-ai'));
    }

    if (!is_user_logged_in()) {
        wp_die(__('You must be logged in to use wizards', 'businesscraft-ai'));
    }

    $wizard_type = isset($_POST['wizard_type']) ? sanitize_text_field($_POST['wizard_type']) : '';
    $user_data = isset($_POST['user_data']) ? $_POST['user_data'] : array();

    $result = businesscraft_ai_start_wizard($wizard_type, $user_data);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

function businesscraft_ai_handle_wizard_step() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_die(__('Security check failed', 'businesscraft-ai'));
    }

    if (!is_user_logged_in()) {
        wp_die(__('You must be logged in to use wizards', 'businesscraft-ai'));
    }

    $wizard_id = isset($_POST['wizard_id']) ? absint($_POST['wizard_id']) : 0;
    $step_data = isset($_POST['step_data']) ? $_POST['step_data'] : array();
    $current_step = isset($_POST['current_step']) ? absint($_POST['current_step']) : 1;

    $result = businesscraft_ai_process_wizard_step($wizard_id, $step_data, $current_step);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

function businesscraft_ai_handle_complete_wizard() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_die(__('Security check failed', 'businesscraft-ai'));
    }

    if (!is_user_logged_in()) {
        wp_die(__('You must be logged in to use wizards', 'businesscraft-ai'));
    }

    $wizard_id = isset($_POST['wizard_id']) ? absint($_POST['wizard_id']) : 0;

    $result = businesscraft_ai_complete_wizard($wizard_id);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

/**
 * Handle AI suggestions for wizard steps
 */
function businesscraft_ai_handle_wizard_ai_suggestions() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }

    $wizard_type = sanitize_text_field($_POST['wizard_type'] ?? '');
    $step_number = intval($_POST['step_number'] ?? 0);
    $current_data = $_POST['current_data'] ?? array();
    $field_name = sanitize_text_field($_POST['field_name'] ?? '');

    // Get user profile for context
    $user_profile = chatgabi_get_user_profile($user_id);
    $country = $user_profile['country'] ?? 'GH';

    // Generate AI suggestions based on wizard type and step
    $suggestions = businesscraft_ai_generate_wizard_suggestions($wizard_type, $step_number, $field_name, $current_data, $country);

    if ($suggestions['success']) {
        wp_send_json_success($suggestions);
    } else {
        wp_send_json_error($suggestions['message']);
    }
}

/**
 * Handle market intelligence requests
 */
function businesscraft_ai_handle_wizard_market_intelligence() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }

    $business_type = sanitize_text_field($_POST['business_type'] ?? '');
    $target_market = sanitize_text_field($_POST['target_market'] ?? '');

    // Get user profile for context
    $user_profile = chatgabi_get_user_profile($user_id);
    $country = $user_profile['country'] ?? 'GH';

    // Generate market intelligence
    $intelligence = businesscraft_ai_generate_market_intelligence($business_type, $target_market, $country);

    if ($intelligence['success']) {
        wp_send_json_success($intelligence);
    } else {
        wp_send_json_error($intelligence['message']);
    }
}

/**
 * Handle competitor analysis requests
 */
function businesscraft_ai_handle_wizard_competitor_analysis() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }

    $business_concept = sanitize_textarea_field($_POST['business_concept'] ?? '');
    $industry = sanitize_text_field($_POST['industry'] ?? '');

    // Get user profile for context
    $user_profile = chatgabi_get_user_profile($user_id);
    $country = $user_profile['country'] ?? 'GH';

    // Generate competitor analysis
    $analysis = businesscraft_ai_generate_competitor_analysis($business_concept, $industry, $country);

    if ($analysis['success']) {
        wp_send_json_success($analysis);
    } else {
        wp_send_json_error($analysis['message']);
    }
}

/**
 * Handle financial projections requests
 */
function businesscraft_ai_handle_wizard_financial_projections() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }

    $business_type = sanitize_text_field($_POST['business_type'] ?? '');
    $revenue_model = sanitize_textarea_field($_POST['revenue_model'] ?? '');
    $startup_costs = floatval($_POST['startup_costs'] ?? 0);
    $forecast_period = intval($_POST['forecast_period'] ?? 3);

    // Get user profile for context
    $user_profile = chatgabi_get_user_profile($user_id);
    $country = $user_profile['country'] ?? 'GH';

    // Generate financial projections
    $projections = businesscraft_ai_generate_financial_projections($business_type, $revenue_model, $startup_costs, $forecast_period, $country);

    if ($projections['success']) {
        wp_send_json_success($projections);
    } else {
        wp_send_json_error($projections['message']);
    }
}

/**
 * Generate AI suggestions for wizard fields
 */
function businesscraft_ai_generate_wizard_suggestions($wizard_type, $step_number, $field_name, $current_data, $country) {
    // Get African market intelligence
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $market_examples = $african_context->generate_market_examples($country, 'general');
    } else {
        $market_examples = array();
    }

    // Build context-aware prompt
    $prompt = businesscraft_ai_build_suggestion_prompt($wizard_type, $step_number, $field_name, $current_data, $market_examples, $country);

    // Get AI response
    $ai_response = businesscraft_ai_get_ai_suggestions($prompt);

    if ($ai_response['success']) {
        return array(
            'success' => true,
            'suggestions' => $ai_response['suggestions'],
            'context' => $ai_response['context'],
            'market_insights' => $ai_response['market_insights']
        );
    } else {
        return array(
            'success' => false,
            'message' => $ai_response['message']
        );
    }
}

/**
 * Build AI suggestion prompt with African context
 */
function businesscraft_ai_build_suggestion_prompt($wizard_type, $step_number, $field_name, $current_data, $market_examples, $country) {
    $country_names = array(
        'GH' => 'Ghana',
        'KE' => 'Kenya',
        'NG' => 'Nigeria',
        'ZA' => 'South Africa'
    );

    $country_name = $country_names[$country] ?? 'Ghana';

    $base_prompt = "You are an expert business consultant specializing in African markets, particularly {$country_name}. ";

    // Add market context
    if (!empty($market_examples)) {
        $base_prompt .= "Consider these successful businesses in {$country_name}: ";
        if (isset($market_examples['successful_businesses']['sme_success_stories'])) {
            $base_prompt .= $market_examples['successful_businesses']['sme_success_stories'] . ". ";
        }

        if (isset($market_examples['cultural_considerations'])) {
            $base_prompt .= "Important cultural considerations: ";
            foreach ($market_examples['cultural_considerations'] as $key => $consideration) {
                $base_prompt .= $consideration . ". ";
            }
        }
    }

    // Add wizard-specific context
    switch ($wizard_type) {
        case 'business-plan':
            $base_prompt .= "Help create a comprehensive business plan suitable for the {$country_name} market. ";
            break;
        case 'marketing-strategy':
            $base_prompt .= "Develop marketing strategies that work effectively in {$country_name}. ";
            break;
        case 'financial-forecast':
            $base_prompt .= "Create realistic financial projections for {$country_name} business environment. ";
            break;
    }

    // Add current context
    if (!empty($current_data)) {
        $base_prompt .= "Current business information: ";
        foreach ($current_data as $key => $value) {
            if (!empty($value)) {
                $base_prompt .= "{$key}: {$value}. ";
            }
        }
    }

    // Add field-specific request
    $field_prompts = array(
        'business_name' => "Suggest 5 creative and memorable business names that would work well in {$country_name}. Consider local language preferences and cultural relevance.",
        'business_concept' => "Help refine and expand this business concept for the {$country_name} market. Provide specific suggestions for local adaptation.",
        'target_market' => "Define the target market for this business in {$country_name}. Include demographics, psychographics, and market size estimates.",
        'competition_analysis' => "Identify main competitors in {$country_name} and analyze their strengths and weaknesses.",
        'marketing_goals' => "Suggest SMART marketing goals appropriate for {$country_name} market conditions.",
        'marketing_channels' => "Recommend the most effective marketing channels for reaching customers in {$country_name}.",
        'revenue_projections' => "Provide realistic revenue projections based on {$country_name} market conditions and similar businesses."
    );

    $field_prompt = $field_prompts[$field_name] ?? "Provide helpful suggestions for the {$field_name} field in the context of {$country_name} business environment.";

    $base_prompt .= $field_prompt;

    $base_prompt .= " Provide 3-5 specific, actionable suggestions. Format as JSON with 'suggestions' array and 'context' explanation.";

    return $base_prompt;
}

/**
 * Get AI suggestions from OpenAI
 */
function businesscraft_ai_get_ai_suggestions($prompt) {
    // Use existing OpenAI integration
    if (function_exists('chatgabi_call_openai_api')) {
        $response = chatgabi_call_openai_api($prompt, 'gpt-4', 300);

        if ($response['success']) {
            // Try to parse JSON response
            $content = $response['response'];
            $json_data = json_decode($content, true);

            if ($json_data && isset($json_data['suggestions'])) {
                return array(
                    'success' => true,
                    'suggestions' => $json_data['suggestions'],
                    'context' => $json_data['context'] ?? '',
                    'market_insights' => $json_data['market_insights'] ?? array()
                );
            } else {
                // Fallback: parse plain text response
                $suggestions = businesscraft_ai_parse_text_suggestions($content);
                return array(
                    'success' => true,
                    'suggestions' => $suggestions,
                    'context' => 'AI-generated suggestions based on African market intelligence',
                    'market_insights' => array()
                );
            }
        } else {
            return array(
                'success' => false,
                'message' => $response['error'] ?? 'Failed to get AI suggestions'
            );
        }
    } else {
        return array(
            'success' => false,
            'message' => 'OpenAI integration not available'
        );
    }
}

/**
 * Document Creation Wizards Styles for BusinessCraft AI
 * 
 * Styles for AI-powered step-by-step document creation wizards
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

/* Wizard Container */
.wizard-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Wizard Selection */
.wizard-selection {
    text-align: center;
    padding: 40px 20px;
}

.wizard-selection h1 {
    color: #3D4E81;
    margin-bottom: 10px;
    font-size: 2.5em;
}

.wizard-selection .subtitle {
    color: #666;
    font-size: 1.2em;
    margin-bottom: 40px;
}

.wizards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.wizard-card {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.wizard-card:hover {
    border-color: #3D4E81;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(61, 78, 129, 0.15);
}

.wizard-icon {
    font-size: 4em;
    margin-bottom: 20px;
    display: block;
}

.wizard-card h3 {
    color: #3D4E81;
    margin: 0 0 15px 0;
    font-size: 1.4em;
}

.wizard-card p {
    color: #666;
    margin: 0 0 20px 0;
    line-height: 1.6;
}

.wizard-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    font-size: 0.9em;
    color: #888;
}

.start-wizard-btn {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1em;
}

.start-wizard-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(61, 78, 129, 0.3);
}

/* Wizard Interface */
.wizard-interface {
    display: none;
}

.wizard-header {
    text-align: center;
    padding: 20px 0 30px;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 30px;
}

.wizard-title {
    color: #3D4E81;
    margin: 0 0 10px 0;
    font-size: 2em;
}

.wizard-description {
    color: #666;
    margin: 0 0 15px 0;
    font-size: 1.1em;
}

.wizard-estimated-time {
    color: #888;
    font-size: 0.9em;
}

/* Progress Indicator */
.wizard-progress {
    margin-bottom: 40px;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 8px;
}

.progress-step.active .step-number {
    background: #3D4E81;
    color: white;
}

.progress-step.completed .step-number {
    background: #28a745;
    color: white;
}

.progress-step.completed .step-number::after {
    content: '✓';
    font-size: 0.8em;
}

.step-connector {
    position: absolute;
    top: 20px;
    left: 50%;
    width: 100%;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.progress-step:last-child .step-connector {
    display: none;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    border-radius: 3px;
    transition: width 0.5s ease;
}

/* Step Content */
.wizard-step-content {
    min-height: 400px;
    margin-bottom: 30px;
}

.step-header {
    text-align: center;
    margin-bottom: 40px;
}

.step-title {
    color: #3D4E81;
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.step-description {
    color: #666;
    margin: 0;
    font-size: 1.1em;
}

.step-fields {
    display: grid;
    gap: 25px;
    max-width: 700px;
    margin: 0 auto;
}

/* Field Groups */
.wizard-field-group {
    position: relative;
}

.field-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 1em;
}

.required {
    color: #dc3545;
    margin-left: 4px;
}

.ai-assist-btn {
    background: linear-gradient(to right, #9C27B0, #E91E63);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ai-assist-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

/* Form Fields */
.wizard-field {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: white;
}

.wizard-field:focus {
    outline: none;
    border-color: #3D4E81;
    box-shadow: 0 0 0 3px rgba(61, 78, 129, 0.1);
}

.wizard-field.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.wizard-field-group.has-error .field-label {
    color: #dc3545;
}

.wizard-field-group.focused .field-label {
    color: #3D4E81;
}

.wizard-field-group.filled .field-label {
    color: #28a745;
}

textarea.wizard-field {
    resize: vertical;
    min-height: 100px;
}

select.wizard-field {
    cursor: pointer;
}

/* Checkbox Groups */
.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: normal;
}

.checkbox-label:hover {
    border-color: #3D4E81;
    background: #f8f9fa;
}

.checkbox-field {
    width: auto !important;
    margin: 0;
}

.checkbox-field:checked + .checkbox-label {
    border-color: #3D4E81;
    background: #e3f2fd;
}

/* AI Suggestions */
.field-ai-suggestions,
.step-ai-suggestions {
    margin-top: 15px;
    padding: 15px;
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 8px;
}

.field-ai-suggestions h4,
.step-ai-suggestions h4 {
    margin: 0 0 10px 0;
    color: #1976d2;
    font-size: 1em;
}

.ai-suggestion-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.ai-suggestion-item:last-child {
    margin-bottom: 0;
}

.suggestion-content {
    color: #333;
    line-height: 1.6;
    margin-bottom: 10px;
}

.suggestion-actions {
    display: flex;
    gap: 8px;
}

.apply-ai-suggestion,
.dismiss-suggestion {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.85em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-ai-suggestion {
    background: #28a745;
    color: white;
}

.apply-ai-suggestion:hover {
    background: #218838;
}

.dismiss-suggestion {
    background: #6c757d;
    color: white;
}

.dismiss-suggestion:hover {
    background: #5a6268;
}

/* Navigation */
.wizard-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 2px solid #e9ecef;
    margin-top: 30px;
}

.wizard-nav-left,
.wizard-nav-right {
    display: flex;
    gap: 15px;
}

.wizard-prev-btn,
.wizard-next-btn,
.wizard-complete-btn,
.save-progress-btn,
.exit-wizard-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1em;
}

.wizard-next-btn,
.wizard-complete-btn {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
}

.wizard-next-btn:hover,
.wizard-complete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(61, 78, 129, 0.3);
}

.wizard-prev-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.wizard-prev-btn:hover {
    background: #e9ecef;
    color: #333;
}

.save-progress-btn {
    background: #17a2b8;
    color: white;
}

.save-progress-btn:hover {
    background: #138496;
}

.exit-wizard-btn {
    background: #dc3545;
    color: white;
}

.exit-wizard-btn:hover {
    background: #c82333;
}

/* Messages */
.wizard-message {
    padding: 12px 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-weight: 600;
    display: none;
}

.wizard-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.wizard-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.wizard-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Auto-save Indicator */
.auto-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.85em;
    z-index: 1000;
    display: none;
}

/* Wizard Completion */
.wizard-completion {
    text-align: center;
    padding: 40px 20px;
}

.completion-header {
    margin-bottom: 40px;
}

.completion-icon {
    font-size: 5em;
    margin-bottom: 20px;
}

.completion-header h2 {
    color: #3D4E81;
    margin: 0 0 15px 0;
    font-size: 2.2em;
}

.completion-header p {
    color: #666;
    font-size: 1.2em;
    margin: 0;
}

.completion-content {
    max-width: 800px;
    margin: 0 auto;
}

.document-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: left;
}

.document-preview h3 {
    margin: 0 0 15px 0;
    color: #3D4E81;
}

.document-content {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
    font-family: 'Georgia', serif;
    line-height: 1.6;
    color: #333;
}

.completion-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    font-size: 1em;
}

.btn-primary {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(61, 78, 129, 0.3);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.85em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wizard-container {
        margin: 10px;
        padding: 15px;
    }
    
    .wizards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .wizard-card {
        padding: 20px;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .step-connector {
        display: none;
    }
    
    .step-fields {
        gap: 20px;
    }
    
    .checkbox-group {
        grid-template-columns: 1fr;
    }
    
    .wizard-navigation {
        flex-direction: column;
        gap: 15px;
    }
    
    .wizard-nav-left,
    .wizard-nav-right {
        width: 100%;
        justify-content: center;
    }
    
    .wizard-prev-btn,
    .wizard-next-btn,
    .wizard-complete-btn,
    .save-progress-btn,
    .exit-wizard-btn {
        flex: 1;
        min-width: 120px;
    }
    
    .completion-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
    }
}

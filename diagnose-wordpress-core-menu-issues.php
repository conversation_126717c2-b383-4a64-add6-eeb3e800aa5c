<?php
/**
 * Comprehensive WordPress Core Menu System Diagnosis
 * This script investigates fundamental WordPress menu registration issues
 */

// Load WordPress with proper initialization
require_once(dirname(__FILE__) . '/wp-config.php');

echo "<h1>🔍 WordPress Core Menu System Diagnosis</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
.code-block { background: #f0f0f0; padding: 10px; border-left: 4px solid #0073aa; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
.critical { background: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 10px 0; }
</style>";

// Step 1: WordPress Core Initialization Check
echo "<div class='section'>";
echo "<h2>🚀 Step 1: WordPress Core Initialization</h2>";

// Check if WordPress is properly loaded
if (defined('ABSPATH')) {
    echo "<p class='success'>✅ WordPress ABSPATH defined: " . ABSPATH . "</p>";
} else {
    echo "<p class='error'>❌ WordPress ABSPATH not defined</p>";
}

if (function_exists('wp_get_current_user')) {
    echo "<p class='success'>✅ WordPress user functions available</p>";
} else {
    echo "<p class='error'>❌ WordPress user functions not available</p>";
}

if (function_exists('add_menu_page')) {
    echo "<p class='success'>✅ WordPress menu functions available</p>";
} else {
    echo "<p class='error'>❌ WordPress menu functions not available</p>";
}

// Check WordPress version
if (function_exists('get_bloginfo')) {
    $wp_version = get_bloginfo('version');
    echo "<p class='info'>📊 WordPress Version: {$wp_version}</p>";
} else {
    echo "<p class='error'>❌ Cannot determine WordPress version</p>";
}

// Check if we're in admin context
if (is_admin()) {
    echo "<p class='success'>✅ Running in WordPress admin context</p>";
} else {
    echo "<p class='warning'>⚠️ Not running in WordPress admin context</p>";
}
echo "</div>";

// Step 2: WordPress Hook System Check
echo "<div class='section'>";
echo "<h2>🪝 Step 2: WordPress Hook System Analysis</h2>";

global $wp_filter, $wp_actions;

if (isset($wp_filter)) {
    echo "<p class='success'>✅ WordPress hook system initialized</p>";
    echo "<p class='info'>📊 Total hooks registered: " . count($wp_filter) . "</p>";
} else {
    echo "<p class='error'>❌ WordPress hook system not initialized</p>";
}

// Check specific admin hooks
$admin_hooks = array('admin_init', 'admin_menu', 'admin_enqueue_scripts', 'current_screen');
foreach ($admin_hooks as $hook) {
    if (isset($wp_filter[$hook])) {
        $callback_count = 0;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            $callback_count += count($callbacks);
        }
        echo "<p class='success'>✅ Hook '{$hook}' exists with {$callback_count} callbacks</p>";
    } else {
        echo "<p class='error'>❌ Hook '{$hook}' not found</p>";
    }
}

// Check if admin_menu has been executed
if (isset($wp_actions['admin_menu'])) {
    echo "<p class='info'>📊 admin_menu hook has been executed {$wp_actions['admin_menu']} times</p>";
} else {
    echo "<p class='warning'>⚠️ admin_menu hook has not been executed yet</p>";
}
echo "</div>";

// Step 3: WordPress Menu Globals Investigation
echo "<div class='section'>";
echo "<h2>📋 Step 3: WordPress Menu Globals Investigation</h2>";

global $menu, $submenu, $admin_page_hooks, $plugin_page_hooks;

echo "<h3>Menu Global Variables:</h3>";

// Check $menu global
if (isset($menu)) {
    if (is_array($menu) && !empty($menu)) {
        echo "<p class='success'>✅ \$menu global is available and populated (" . count($menu) . " items)</p>";
        
        echo "<div class='code-block'>";
        echo "Sample menu items:\n";
        $count = 0;
        foreach ($menu as $key => $item) {
            if ($count < 5 && is_array($item)) {
                echo "[$key] " . (isset($item[0]) ? $item[0] : 'No title') . " => " . (isset($item[2]) ? $item[2] : 'No slug') . "\n";
                $count++;
            }
        }
        echo "</div>";
    } else {
        echo "<p class='warning'>⚠️ \$menu global exists but is empty</p>";
    }
} else {
    echo "<p class='error'>❌ \$menu global is not available</p>";
}

// Check $submenu global
if (isset($submenu)) {
    if (is_array($submenu) && !empty($submenu)) {
        echo "<p class='success'>✅ \$submenu global is available and populated (" . count($submenu) . " parents)</p>";
        
        echo "<div class='code-block'>";
        echo "Submenu parents:\n";
        foreach ($submenu as $parent => $items) {
            echo "Parent: {$parent} (" . count($items) . " items)\n";
        }
        echo "</div>";
    } else {
        echo "<p class='warning'>⚠️ \$submenu global exists but is empty</p>";
    }
} else {
    echo "<p class='error'>❌ \$submenu global is not available</p>";
}

// Check other admin globals
if (isset($admin_page_hooks)) {
    echo "<p class='success'>✅ \$admin_page_hooks global available (" . count($admin_page_hooks) . " hooks)</p>";
} else {
    echo "<p class='error'>❌ \$admin_page_hooks global not available</p>";
}

if (isset($plugin_page_hooks)) {
    echo "<p class='success'>✅ \$plugin_page_hooks global available (" . count($plugin_page_hooks) . " hooks)</p>";
} else {
    echo "<p class='error'>❌ \$plugin_page_hooks global not available</p>";
}
echo "</div>";

// Step 4: Force WordPress Admin Initialization
echo "<div class='section'>";
echo "<h2>🔧 Step 4: Force WordPress Admin Initialization</h2>";

// Try to force load WordPress admin
if (!defined('WP_ADMIN')) {
    define('WP_ADMIN', true);
    echo "<p class='info'>🔧 Defined WP_ADMIN constant</p>";
}

// Load WordPress admin functions
$admin_files = array(
    ABSPATH . 'wp-admin/includes/admin.php',
    ABSPATH . 'wp-admin/includes/menu.php',
    ABSPATH . 'wp-admin/menu-header.php'
);

foreach ($admin_files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ Found admin file: " . basename($file) . "</p>";
        try {
            require_once $file;
            echo "<p class='success'>✅ Loaded: " . basename($file) . "</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error loading " . basename($file) . ": " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>❌ Missing admin file: " . basename($file) . "</p>";
    }
}

// Try to manually trigger admin_menu
echo "<p class='info'>🔧 Manually triggering admin_menu hook...</p>";
do_action('admin_menu');

// Check menu globals again after forced initialization
echo "<h3>Menu Globals After Forced Initialization:</h3>";
if (isset($menu) && is_array($menu) && !empty($menu)) {
    echo "<p class='success'>✅ \$menu now available with " . count($menu) . " items</p>";
} else {
    echo "<p class='error'>❌ \$menu still not available after forced initialization</p>";
}

if (isset($submenu) && is_array($submenu) && !empty($submenu)) {
    echo "<p class='success'>✅ \$submenu now available with " . count($submenu) . " parents</p>";
} else {
    echo "<p class='error'>❌ \$submenu still not available after forced initialization</p>";
}
echo "</div>";

// Step 5: Test Alternative Menu Registration
echo "<div class='section'>";
echo "<h2>🧪 Step 5: Test Alternative Menu Registration</h2>";

echo "<p class='info'>🧪 Testing different menu registration approaches...</p>";

// Test 1: Direct add_menu_page
echo "<h3>Test 1: Direct add_menu_page</h3>";
try {
    $result1 = add_menu_page(
        'Test Menu 1',
        'Test Menu 1', 
        'manage_options',
        'test-menu-1',
        function() { echo 'Test Menu 1 Content'; },
        'dashicons-admin-generic',
        30
    );
    echo "<p class='success'>✅ add_menu_page succeeded: " . ($result1 ? 'true' : 'false') . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ add_menu_page failed: " . $e->getMessage() . "</p>";
}

// Test 2: add_management_page
echo "<h3>Test 2: add_management_page</h3>";
try {
    $result2 = add_management_page(
        'Test Management',
        'Test Management',
        'manage_options', 
        'test-management',
        function() { echo 'Test Management Content'; }
    );
    echo "<p class='success'>✅ add_management_page succeeded: " . ($result2 ? 'true' : 'false') . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ add_management_page failed: " . $e->getMessage() . "</p>";
}

// Test 3: add_submenu_page
echo "<h3>Test 3: add_submenu_page</h3>";
try {
    $result3 = add_submenu_page(
        'tools.php',
        'Test Submenu',
        'Test Submenu',
        'manage_options',
        'test-submenu', 
        function() { echo 'Test Submenu Content'; }
    );
    echo "<p class='success'>✅ add_submenu_page succeeded: " . ($result3 ? 'true' : 'false') . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ add_submenu_page failed: " . $e->getMessage() . "</p>";
}

// Check if test menus were added
echo "<h3>Verification of Test Menus:</h3>";
if (isset($menu) && is_array($menu)) {
    $test_menu_found = false;
    foreach ($menu as $item) {
        if (is_array($item) && isset($item[2]) && strpos($item[2], 'test-') !== false) {
            echo "<p class='success'>✅ Found test menu: " . $item[0] . " (" . $item[2] . ")</p>";
            $test_menu_found = true;
        }
    }
    if (!$test_menu_found) {
        echo "<p class='warning'>⚠️ No test menus found in \$menu array</p>";
    }
}

if (isset($submenu) && is_array($submenu)) {
    foreach ($submenu as $parent => $items) {
        foreach ($items as $item) {
            if (is_array($item) && isset($item[2]) && strpos($item[2], 'test-') !== false) {
                echo "<p class='success'>✅ Found test submenu: " . $item[0] . " under " . $parent . "</p>";
            }
        }
    }
}
echo "</div>";

// Step 6: XAMPP Environment Check
echo "<div class='section'>";
echo "<h2>🖥️ Step 6: XAMPP Environment Analysis</h2>";

// Check PHP version
echo "<p class='info'>🐘 PHP Version: " . PHP_VERSION . "</p>";

// Check if running on Windows (typical for XAMPP)
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    echo "<p class='info'>🪟 Running on Windows (XAMPP environment)</p>";
} else {
    echo "<p class='info'>🐧 Running on " . PHP_OS . "</p>";
}

// Check server software
if (isset($_SERVER['SERVER_SOFTWARE'])) {
    echo "<p class='info'>🌐 Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
} else {
    echo "<p class='warning'>⚠️ Server software information not available</p>";
}

// Check WordPress constants
$wp_constants = array('WP_DEBUG', 'WP_DEBUG_LOG', 'SCRIPT_DEBUG', 'WP_CACHE');
foreach ($wp_constants as $constant) {
    if (defined($constant)) {
        $value = constant($constant) ? 'true' : 'false';
        echo "<p class='info'>📊 {$constant}: {$value}</p>";
    } else {
        echo "<p class='warning'>⚠️ {$constant}: not defined</p>";
    }
}

// Check file permissions (important for XAMPP)
$wp_admin_path = ABSPATH . 'wp-admin';
if (is_readable($wp_admin_path)) {
    echo "<p class='success'>✅ wp-admin directory is readable</p>";
} else {
    echo "<p class='error'>❌ wp-admin directory is not readable</p>";
}

if (is_writable($wp_admin_path)) {
    echo "<p class='success'>✅ wp-admin directory is writable</p>";
} else {
    echo "<p class='warning'>⚠️ wp-admin directory is not writable</p>";
}
echo "</div>";

// Step 7: Provide Solutions
echo "<div class='section'>";
echo "<h2>🔧 Step 7: Recommended Solutions</h2>";

echo "<div class='critical'>";
echo "<h3>🚨 Critical Issues Identified:</h3>";

if (!isset($menu) || empty($menu)) {
    echo "<p><strong>Issue 1:</strong> WordPress menu system not properly initialized</p>";
    echo "<p><strong>Solution:</strong> Force load WordPress admin context before menu registration</p>";
}

if (!isset($wp_filter['admin_menu'])) {
    echo "<p><strong>Issue 2:</strong> admin_menu hook not available</p>";
    echo "<p><strong>Solution:</strong> Ensure WordPress is fully loaded before registering hooks</p>";
}

echo "</div>";

echo "<h3>🛠️ Immediate Fixes to Try:</h3>";
echo "<ol>";
echo "<li><strong>Force Admin Context:</strong> <a href='?fix=admin_context'>Click to force admin context</a></li>";
echo "<li><strong>Alternative Menu Registration:</strong> <a href='?fix=alt_menu'>Click to try alternative menu approach</a></li>";
echo "<li><strong>Reset WordPress:</strong> <a href='?fix=reset_wp'>Click to reset WordPress state</a></li>";
echo "<li><strong>Manual Menu Creation:</strong> <a href='?fix=manual_menu'>Click to manually create menus</a></li>";
echo "</ol>";

// Handle fix requests
if (isset($_GET['fix'])) {
    echo "<div class='code-block'>";
    echo "Applying fix: " . $_GET['fix'] . "\n\n";
    
    switch ($_GET['fix']) {
        case 'admin_context':
            // Force admin context
            if (!defined('WP_ADMIN')) define('WP_ADMIN', true);
            if (!defined('WP_NETWORK_ADMIN')) define('WP_NETWORK_ADMIN', false);
            if (!defined('WP_USER_ADMIN')) define('WP_USER_ADMIN', false);
            
            // Load admin
            require_once ABSPATH . 'wp-admin/includes/admin.php';
            do_action('admin_menu');
            
            echo "✅ Forced admin context and triggered admin_menu\n";
            break;
            
        case 'alt_menu':
            // Try alternative menu registration
            add_action('admin_menu', function() {
                add_menu_page(
                    'ChatGABI Alt',
                    'ChatGABI Alt',
                    'manage_options',
                    'chatgabi-alt',
                    function() { echo '<h1>ChatGABI Alternative Menu</h1>'; }
                );
            }, 5);
            do_action('admin_menu');
            echo "✅ Registered alternative menu with early priority\n";
            break;
            
        case 'reset_wp':
            // Reset WordPress globals
            global $menu, $submenu;
            $menu = array();
            $submenu = array();
            do_action('admin_menu');
            echo "✅ Reset WordPress menu globals\n";
            break;
            
        case 'manual_menu':
            // Manually create menu structure
            global $menu, $submenu;
            if (!is_array($menu)) $menu = array();
            if (!is_array($submenu)) $submenu = array();
            
            $menu[30] = array('ChatGABI Manual', 'manage_options', 'chatgabi-manual', 'ChatGABI Manual', 'menu-top');
            $submenu['chatgabi-manual'] = array();
            $submenu['chatgabi-manual'][10] = array('Engagement Analytics', 'manage_options', 'chatgabi-engagement-analytics');
            
            echo "✅ Manually created menu structure\n";
            break;
    }
    echo "</div>";
}

echo "<h3>📍 Next Steps:</h3>";
echo "<ul>";
echo "<li>Try each fix option above</li>";
echo "<li>Check WordPress error logs in XAMPP</li>";
echo "<li>Verify theme activation completed successfully</li>";
echo "<li>Consider switching to a different menu registration approach</li>";
echo "</ul>";
echo "</div>";
?>

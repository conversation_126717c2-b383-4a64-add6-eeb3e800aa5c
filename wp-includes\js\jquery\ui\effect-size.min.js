/*!
 * jQuery UI Effects Size 1.13.3
 * https://jqueryui.com
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license.
 * https://jquery.org/license
 */
!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery","../version","../effect"],t):t(jQuery)}(function(l){"use strict";return l.effects.define("size",function(o,e){var f,i=l(this),t=["fontSize"],s=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],n=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],r=o.mode,h="effect"!==r,c=o.scale||"both",d=o.origin||["middle","center"],a=i.css("position"),g=i.position(),u=l.effects.scaledDimensions(i),m=o.from||u,y=o.to||l.effects.scaledDimensions(i,0);l.effects.createPlaceholder(i),"show"===r&&(r=m,m=y,y=r),f={from:{y:m.height/u.height,x:m.width/u.width},to:{y:y.height/u.height,x:y.width/u.width}},"box"!==c&&"both"!==c||(f.from.y!==f.to.y&&(m=l.effects.setTransition(i,s,f.from.y,m),y=l.effects.setTransition(i,s,f.to.y,y)),f.from.x!==f.to.x&&(m=l.effects.setTransition(i,n,f.from.x,m),y=l.effects.setTransition(i,n,f.to.x,y))),"content"!==c&&"both"!==c||f.from.y!==f.to.y&&(m=l.effects.setTransition(i,t,f.from.y,m),y=l.effects.setTransition(i,t,f.to.y,y)),d&&(r=l.effects.getBaseline(d,u),m.top=(u.outerHeight-m.outerHeight)*r.y+g.top,m.left=(u.outerWidth-m.outerWidth)*r.x+g.left,y.top=(u.outerHeight-y.outerHeight)*r.y+g.top,y.left=(u.outerWidth-y.outerWidth)*r.x+g.left),delete m.outerHeight,delete m.outerWidth,i.css(m),"content"!==c&&"both"!==c||(s=s.concat(["marginTop","marginBottom"]).concat(t),n=n.concat(["marginLeft","marginRight"]),i.find("*[width]").each(function(){var t=l(this),e=l.effects.scaledDimensions(t),i={height:e.height*f.from.y,width:e.width*f.from.x,outerHeight:e.outerHeight*f.from.y,outerWidth:e.outerWidth*f.from.x},e={height:e.height*f.to.y,width:e.width*f.to.x,outerHeight:e.height*f.to.y,outerWidth:e.width*f.to.x};f.from.y!==f.to.y&&(i=l.effects.setTransition(t,s,f.from.y,i),e=l.effects.setTransition(t,s,f.to.y,e)),f.from.x!==f.to.x&&(i=l.effects.setTransition(t,n,f.from.x,i),e=l.effects.setTransition(t,n,f.to.x,e)),h&&l.effects.saveStyle(t),t.css(i),t.animate(e,o.duration,o.easing,function(){h&&l.effects.restoreStyle(t)})})),i.animate(y,{queue:!1,duration:o.duration,easing:o.easing,complete:function(){var t=i.offset();0===y.opacity&&i.css("opacity",m.opacity),h||(i.css("position","static"===a?"relative":a).offset(t),l.effects.saveStyle(i)),e()}})})});
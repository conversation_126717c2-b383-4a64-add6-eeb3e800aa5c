/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var l=t&&t.__esModule?()=>t.default:()=>t;return e.d(l,{a:l}),l},d:(t,l)=>{for(var s in l)e.o(l,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:l[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{store:()=>U});var l={};e.r(l),e.d(l,{getDownloadableBlocks:()=>k,getErrorNoticeForBlock:()=>y,getErrorNotices:()=>f,getInstalledBlockTypes:()=>m,getNewBlockTypes:()=>g,getUnusedBlockTypes:()=>_,isInstalling:()=>w,isRequestingDownloadableBlocks:()=>h});var s={};e.r(s),e.d(s,{addInstalledBlockType:()=>O,clearErrorNotice:()=>P,fetchDownloadableBlocks:()=>T,installBlockType:()=>L,receiveDownloadableBlocks:()=>S,removeInstalledBlockType:()=>D,setErrorNotice:()=>R,setIsInstalling:()=>A,uninstallBlockType:()=>C});var n={};e.r(n),e.d(n,{getDownloadableBlocks:()=>Y});const o=window.wp.plugins,r=window.wp.hooks,i=window.wp.blocks,a=window.wp.data,c=window.wp.element,d=window.wp.editor,u=(0,a.combineReducers)({downloadableBlocks:(e={},t)=>{switch(t.type){case"FETCH_DOWNLOADABLE_BLOCKS":return{...e,[t.filterValue]:{isRequesting:!0}};case"RECEIVE_DOWNLOADABLE_BLOCKS":return{...e,[t.filterValue]:{results:t.downloadableBlocks,isRequesting:!1}}}return e},blockManagement:(e={installedBlockTypes:[],isInstalling:{}},t)=>{switch(t.type){case"ADD_INSTALLED_BLOCK_TYPE":return{...e,installedBlockTypes:[...e.installedBlockTypes,t.item]};case"REMOVE_INSTALLED_BLOCK_TYPE":return{...e,installedBlockTypes:e.installedBlockTypes.filter((e=>e.name!==t.item.name))};case"SET_INSTALLING_BLOCK":return{...e,isInstalling:{...e.isInstalling,[t.blockId]:t.isInstalling}}}return e},errorNotices:(e={},t)=>{switch(t.type){case"SET_ERROR_NOTICE":return{...e,[t.blockId]:{message:t.message,isFatal:t.isFatal}};case"CLEAR_ERROR_NOTICE":const{[t.blockId]:l,...s}=e;return s}return e}}),p=window.wp.blockEditor,b=[];function h(e,t){var l;return null!==(l=e.downloadableBlocks[t]?.isRequesting)&&void 0!==l&&l}function k(e,t){var l;return null!==(l=e.downloadableBlocks[t]?.results)&&void 0!==l?l:b}function m(e){return e.blockManagement.installedBlockTypes}const g=(0,a.createRegistrySelector)((e=>(0,a.createSelector)((t=>{const l=m(t);if(!l.length)return b;const{getBlockName:s,getClientIdsWithDescendants:n}=e(p.store),o=l.map((e=>e.name)),r=n().flatMap((e=>{const t=s(e);return o.includes(t)?t:[]})),i=l.filter((e=>r.includes(e.name)));return i.length>0?i:b}),(t=>[m(t),e(p.store).getClientIdsWithDescendants()])))),_=(0,a.createRegistrySelector)((e=>(0,a.createSelector)((t=>{const l=m(t);if(!l.length)return b;const{getBlockName:s,getClientIdsWithDescendants:n}=e(p.store),o=l.map((e=>e.name)),r=n().flatMap((e=>{const t=s(e);return o.includes(t)?t:[]})),i=l.filter((e=>!r.includes(e.name)));return i.length>0?i:b}),(t=>[m(t),e(p.store).getClientIdsWithDescendants()]))));function w(e,t){return e.blockManagement.isInstalling[t]||!1}function f(e){return e.errorNotices}function y(e,t){return e.errorNotices[t]}const x=window.wp.i18n,v=window.wp.apiFetch;var j=e.n(v);const B=window.wp.notices,E=window.wp.url,N=e=>new Promise(((t,l)=>{const s=document.createElement(e.nodeName);["id","rel","src","href","type"].forEach((t=>{e[t]&&(s[t]=e[t])})),e.innerHTML&&s.appendChild(document.createTextNode(e.innerHTML)),s.onload=()=>t(!0),s.onerror=()=>l(new Error("Error loading asset.")),document.body.appendChild(s),("link"===s.nodeName.toLowerCase()||"script"===s.nodeName.toLowerCase()&&!s.src)&&t()}));function I(e){if(!e)return!1;const t=e.links["wp:plugin"]||e.links.self;return!(!t||!t.length)&&t[0].href}function T(e){return{type:"FETCH_DOWNLOADABLE_BLOCKS",filterValue:e}}function S(e,t){return{type:"RECEIVE_DOWNLOADABLE_BLOCKS",downloadableBlocks:e,filterValue:t}}const L=e=>async({registry:t,dispatch:l})=>{const{id:s,name:n}=e;let o=!1;l.clearErrorNotice(s);try{l.setIsInstalling(s,!0);const r=I(e);let a={};if(r)await j()({method:"PUT",url:r,data:{status:"active"}});else{a=(await j()({method:"POST",path:"wp/v2/plugins",data:{slug:s,status:"active"}}))._links}l.addInstalledBlockType({...e,links:{...e.links,...a}});const c=["api_version","title","category","parent","icon","description","keywords","attributes","provides_context","uses_context","supports","styles","example","variations"];await j()({path:(0,E.addQueryArgs)(`/wp/v2/block-types/${n}`,{_fields:c})}).catch((()=>{})).then((e=>{e&&(0,i.unstable__bootstrapServerSideBlockDefinitions)({[n]:Object.fromEntries(Object.entries(e).filter((([e])=>c.includes(e))))})})),await async function(){const e=await j()({url:document.location.href,parse:!1}),t=await e.text(),l=(new window.DOMParser).parseFromString(t,"text/html"),s=Array.from(l.querySelectorAll('link[rel="stylesheet"],script')).filter((e=>e.id&&!document.getElementById(e.id)));for(const e of s)await N(e)}();if(!t.select(i.store).getBlockTypes().some((e=>e.name===n)))throw new Error((0,x.__)("Error registering block. Try reloading the page."));t.dispatch(B.store).createInfoNotice((0,x.sprintf)((0,x.__)("Block %s installed and added."),e.title),{speak:!0,type:"snackbar"}),o=!0}catch(e){let n=e.message||(0,x.__)("An error occurred."),o=e instanceof Error;const r={folder_exists:(0,x.__)("This block is already installed. Try reloading the page."),unable_to_connect_to_filesystem:(0,x.__)("Error installing block. You can reload the page and try again.")};r[e.code]&&(o=!0,n=r[e.code]),l.setErrorNotice(s,n,o),t.dispatch(B.store).createErrorNotice(n,{speak:!0,isDismissible:!0})}return l.setIsInstalling(s,!1),o},C=e=>async({registry:t,dispatch:l})=>{try{const t=I(e);await j()({method:"PUT",url:t,data:{status:"inactive"}}),await j()({method:"DELETE",url:t}),l.removeInstalledBlockType(e)}catch(e){t.dispatch(B.store).createErrorNotice(e.message||(0,x.__)("An error occurred."))}};function O(e){return{type:"ADD_INSTALLED_BLOCK_TYPE",item:e}}function D(e){return{type:"REMOVE_INSTALLED_BLOCK_TYPE",item:e}}function A(e,t){return{type:"SET_INSTALLING_BLOCK",blockId:e,isInstalling:t}}function R(e,t,l=!1){return{type:"SET_ERROR_NOTICE",blockId:e,message:t,isFatal:l}}function P(e){return{type:"CLEAR_ERROR_NOTICE",blockId:e}}var M=function(){return M=Object.assign||function(e){for(var t,l=1,s=arguments.length;l<s;l++)for(var n in t=arguments[l])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},M.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function F(e){return e.toLowerCase()}var $=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],V=/[^A-Z0-9]+/gi;function H(e,t,l){return t instanceof RegExp?e.replace(t,l):t.reduce((function(e,t){return e.replace(t,l)}),e)}function z(e,t){var l=e.charAt(0),s=e.substr(1).toLowerCase();return t>0&&l>="0"&&l<="9"?"_"+l+s:""+l.toUpperCase()+s}function K(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var l=t.splitRegexp,s=void 0===l?$:l,n=t.stripRegexp,o=void 0===n?V:n,r=t.transform,i=void 0===r?F:r,a=t.delimiter,c=void 0===a?" ":a,d=H(H(e,s,"$1\0$2"),o,"\0"),u=0,p=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(p-1);)p--;return d.slice(u,p).split("\0").map(i).join(c)}(e,M({delimiter:"",transform:z},t))}function W(e,t){return 0===t?e.toLowerCase():z(e,t)}const Y=e=>async({dispatch:t})=>{if(e)try{t(T(e));const l=await j()({path:`wp/v2/block-directory/search?term=${e}`});t(S(l.map((e=>Object.fromEntries(Object.entries(e).map((([e,t])=>{return[(l=e,void 0===s&&(s={}),K(l,M({transform:W},s))),t];var l,s}))))),e))}catch{t(S([],e))}},q={reducer:u,selectors:l,actions:s,resolvers:n},U=(0,a.createReduxStore)("core/block-directory",q);function G(){const{uninstallBlockType:e}=(0,a.useDispatch)(U),t=(0,a.useSelect)((e=>{const{isAutosavingPost:t,isSavingPost:l}=e(d.store);return l()&&!t()}),[]),l=(0,a.useSelect)((e=>e(U).getUnusedBlockTypes()),[]);return(0,c.useEffect)((()=>{t&&l.length&&l.forEach((t=>{e(t),(0,i.unregisterBlockType)(t.name)}))}),[t]),null}(0,a.register)(U);const Z=window.wp.compose,J=window.wp.components,Q=window.wp.coreData;function X(e){var t,l,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(l=X(e[t]))&&(s&&(s+=" "),s+=l)}else for(l in e)e[l]&&(s&&(s+=" "),s+=l);return s}const ee=function(){for(var e,t,l=0,s="",n=arguments.length;l<n;l++)(e=arguments[l])&&(t=X(e))&&(s&&(s+=" "),s+=t);return s},te=window.wp.htmlEntities;const le=(0,c.forwardRef)((function({icon:e,size:t=24,...l},s){return(0,c.cloneElement)(e,{width:t,height:t,...l,ref:s})})),se=window.wp.primitives,ne=window.ReactJSXRuntime,oe=(0,ne.jsx)(se.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ne.jsx)(se.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})}),re=(0,ne.jsx)(se.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ne.jsx)(se.Path,{d:"M9.518 8.783a.25.25 0 00.188-.137l2.069-4.192a.25.25 0 01.448 0l2.07 4.192a.25.25 0 00.187.137l4.626.672a.25.25 0 01.139.427l-3.347 3.262a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.363.264l-4.137-2.176a.25.25 0 00-.233 0l-4.138 2.175a.25.25 0 01-.362-.263l.79-4.607a.25.25 0 00-.072-.222L4.753 9.882a.25.25 0 01.14-.427l4.625-.672zM12 14.533c.28 0 .559.067.814.2l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39v7.143z"})}),ie=(0,ne.jsx)(se.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ne.jsx)(se.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})});const ae=function({rating:e}){const t=.5*Math.round(e/.5),l=Math.floor(e),s=Math.ceil(e-l),n=5-(l+s);return(0,ne.jsxs)("span",{"aria-label":(0,x.sprintf)((0,x.__)("%s out of 5 stars"),t),children:[Array.from({length:l}).map(((e,t)=>(0,ne.jsx)(le,{className:"block-directory-block-ratings__star-full",icon:oe,size:16},`full_stars_${t}`))),Array.from({length:s}).map(((e,t)=>(0,ne.jsx)(le,{className:"block-directory-block-ratings__star-half-full",icon:re,size:16},`half_stars_${t}`))),Array.from({length:n}).map(((e,t)=>(0,ne.jsx)(le,{className:"block-directory-block-ratings__star-empty",icon:ie,size:16},`empty_stars_${t}`)))]})},ce=({rating:e})=>(0,ne.jsx)("span",{className:"block-directory-block-ratings",children:(0,ne.jsx)(ae,{rating:e})});const de=function({icon:e}){const t="block-directory-downloadable-block-icon";return null!==e.match(/\.(jpeg|jpg|gif|png|svg)(?:\?.*)?$/)?(0,ne.jsx)("img",{className:t,src:e,alt:""}):(0,ne.jsx)(p.BlockIcon,{className:t,icon:e,showColors:!0})},ue=({block:e})=>{const t=(0,a.useSelect)((t=>t(U).getErrorNoticeForBlock(e.id)),[e]);return t?(0,ne.jsx)("div",{className:"block-directory-downloadable-block-notice",children:(0,ne.jsxs)("div",{className:"block-directory-downloadable-block-notice__content",children:[t.message,t.isFatal?" "+(0,x.__)("Try reloading the page."):null]})}):null};const pe=function({item:e,onClick:t}){const{author:l,description:s,icon:n,rating:o,title:r}=e,d=!!(0,i.getBlockType)(e.name),{hasNotice:u,isInstalling:p,isInstallable:b}=(0,a.useSelect)((t=>{const{getErrorNoticeForBlock:l,isInstalling:s}=t(U),n=l(e.id),o=n&&n.isFatal;return{hasNotice:!!n,isInstalling:s(e.id),isInstallable:!o}}),[e]);let h="";d?h=(0,x.__)("Installed!"):p&&(h=(0,x.__)("Installing…"));const k=function({title:e,rating:t,ratingCount:l},{hasNotice:s,isInstalled:n,isInstalling:o}){const r=.5*Math.round(t/.5);return!n&&s?(0,x.sprintf)("Retry installing %s.",(0,te.decodeEntities)(e)):n?(0,x.sprintf)("Add %s.",(0,te.decodeEntities)(e)):o?(0,x.sprintf)("Installing %s.",(0,te.decodeEntities)(e)):l<1?(0,x.sprintf)("Install %s.",(0,te.decodeEntities)(e)):(0,x.sprintf)((0,x._n)("Install %1$s. %2$s stars with %3$s review.","Install %1$s. %2$s stars with %3$s reviews.",l),(0,te.decodeEntities)(e),r,l)}(e,{hasNotice:u,isInstalled:d,isInstalling:p});return(0,ne.jsx)(J.Tooltip,{placement:"top",text:k,children:(0,ne.jsxs)(J.Composite.Item,{className:ee("block-directory-downloadable-block-list-item",p&&"is-installing"),accessibleWhenDisabled:!0,disabled:p||!b,onClick:e=>{e.preventDefault(),t()},"aria-label":k,type:"button",role:"option",children:[(0,ne.jsxs)("div",{className:"block-directory-downloadable-block-list-item__icon",children:[(0,ne.jsx)(de,{icon:n,title:r}),p?(0,ne.jsx)("span",{className:"block-directory-downloadable-block-list-item__spinner",children:(0,ne.jsx)(J.Spinner,{})}):(0,ne.jsx)(ce,{rating:o})]}),(0,ne.jsxs)("span",{className:"block-directory-downloadable-block-list-item__details",children:[(0,ne.jsx)("span",{className:"block-directory-downloadable-block-list-item__title",children:(0,c.createInterpolateElement)((0,x.sprintf)((0,x.__)("%1$s <span>by %2$s</span>"),(0,te.decodeEntities)(r),l),{span:(0,ne.jsx)("span",{className:"block-directory-downloadable-block-list-item__author"})})}),u?(0,ne.jsx)(ue,{block:e}):(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)("span",{className:"block-directory-downloadable-block-list-item__desc",children:h||(0,te.decodeEntities)(s)}),b&&!(d||p)&&(0,ne.jsx)(J.VisuallyHidden,{children:(0,x.__)("Install block")})]})]})]})})},be=()=>{};const he=function({items:e,onHover:t=be,onSelect:l}){const{installBlockType:s}=(0,a.useDispatch)(U);return e.length?(0,ne.jsx)(J.Composite,{role:"listbox",className:"block-directory-downloadable-blocks-list","aria-label":(0,x.__)("Blocks available for install"),children:e.map((e=>(0,ne.jsx)(pe,{onClick:()=>{(0,i.getBlockType)(e.name)?l(e):s(e).then((t=>{t&&l(e)})),t(null)},onHover:t,item:e},e.id)))}):null},ke=window.wp.a11y;const me=function({children:e,downloadableItems:t,hasLocalBlocks:l}){const s=t.length;return(0,c.useEffect)((()=>{(0,ke.speak)((0,x.sprintf)((0,x._n)("%d additional block is available to install.","%d additional blocks are available to install.",s),s))}),[s]),(0,ne.jsxs)(ne.Fragment,{children:[!l&&(0,ne.jsx)("p",{className:"block-directory-downloadable-blocks-panel__no-local",children:(0,x.__)("No results available from your installed blocks.")}),(0,ne.jsx)("div",{className:"block-editor-inserter__quick-inserter-separator"}),(0,ne.jsxs)("div",{className:"block-directory-downloadable-blocks-panel",children:[(0,ne.jsxs)("div",{className:"block-directory-downloadable-blocks-panel__header",children:[(0,ne.jsx)("h2",{className:"block-directory-downloadable-blocks-panel__title",children:(0,x.__)("Available to install")}),(0,ne.jsx)("p",{className:"block-directory-downloadable-blocks-panel__description",children:(0,x.__)("Select a block to install and add it to your post.")})]}),e]})]})};const ge=function(){return(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)("div",{className:"block-editor-inserter__no-results",children:(0,ne.jsx)("p",{children:(0,x.__)("No results found.")})}),(0,ne.jsx)("div",{className:"block-editor-inserter__tips",children:(0,ne.jsxs)(J.Tip,{children:[(0,x.__)("Interested in creating your own block?"),(0,ne.jsx)("br",{}),(0,ne.jsxs)(J.ExternalLink,{href:"https://developer.wordpress.org/block-editor/",children:[(0,x.__)("Get started here"),"."]})]})})]})},_e=[];function we({onSelect:e,onHover:t,hasLocalBlocks:l,isTyping:s,filterValue:n}){const{hasPermission:o,downloadableBlocks:r,isLoading:c}=(e=>(0,a.useSelect)((t=>{const{getDownloadableBlocks:l,isRequestingDownloadableBlocks:s,getInstalledBlockTypes:n}=t(U),o=t(Q.store).canUser("read","block-directory/search");let r=_e;if(o){r=l(e);const t=n(),s=r.filter((({name:e})=>{const l=t.some((t=>t.name===e)),s=(0,i.getBlockType)(e);return l||!s}));s.length!==r.length&&(r=s),0===r.length&&(r=_e)}return{hasPermission:o,downloadableBlocks:r,isLoading:s(e)}}),[e]))(n);return void 0===o||c||s?(0,ne.jsxs)(ne.Fragment,{children:[o&&!l&&(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)("p",{className:"block-directory-downloadable-blocks-panel__no-local",children:(0,x.__)("No results available from your installed blocks.")}),(0,ne.jsx)("div",{className:"block-editor-inserter__quick-inserter-separator"})]}),(0,ne.jsx)("div",{className:"block-directory-downloadable-blocks-panel has-blocks-loading",children:(0,ne.jsx)(J.Spinner,{})})]}):!1===o||0===r.length?l?null:(0,ne.jsx)(ge,{}):(0,ne.jsx)(me,{downloadableItems:r,hasLocalBlocks:l,children:(0,ne.jsx)(he,{items:r,onSelect:e,onHover:t})})}const fe=function(){const[e,t]=(0,c.useState)(""),l=(0,Z.debounce)(t,400);return(0,ne.jsx)(p.__unstableInserterMenuExtension,{children:({onSelect:t,onHover:s,filterValue:n,hasItems:o})=>(e!==n&&l(n),e?(0,ne.jsx)(we,{onSelect:t,onHover:s,filterValue:e,hasLocalBlocks:o,isTyping:n!==e}):null)})};function ye({items:e}){return e.length?(0,ne.jsx)("ul",{className:"block-directory-compact-list",children:e.map((({icon:e,id:t,title:l,author:s})=>(0,ne.jsxs)("li",{className:"block-directory-compact-list__item",children:[(0,ne.jsx)(de,{icon:e,title:l}),(0,ne.jsxs)("div",{className:"block-directory-compact-list__item-details",children:[(0,ne.jsx)("div",{className:"block-directory-compact-list__item-title",children:l}),(0,ne.jsx)("div",{className:"block-directory-compact-list__item-author",children:(0,x.sprintf)((0,x.__)("By %s"),s)})]})]},t)))}):null}function xe(){const e=(0,a.useSelect)((e=>e(U).getNewBlockTypes()),[]);return e.length?(0,ne.jsxs)(d.PluginPrePublishPanel,{title:(0,x.sprintf)((0,x._n)("Added: %d block","Added: %d blocks",e.length),e.length),initialOpen:!0,children:[(0,ne.jsx)("p",{className:"installed-blocks-pre-publish-panel__copy",children:(0,x._n)("The following block has been added to your site.","The following blocks have been added to your site.",e.length)}),(0,ne.jsx)(ye,{items:e})]}):null}function ve({attributes:e,block:t,clientId:l}){const s=(0,a.useSelect)((e=>e(U).isInstalling(t.id)),[t.id]),{installBlockType:n}=(0,a.useDispatch)(U),{replaceBlock:o}=(0,a.useDispatch)(p.store);return(0,ne.jsx)(J.Button,{__next40pxDefaultSize:!0,onClick:()=>n(t).then((s=>{if(s){const s=(0,i.getBlockType)(t.name),[n]=(0,i.parse)(e.originalContent);n&&s&&o(l,(0,i.createBlock)(s.name,n.attributes,n.innerBlocks))}})),accessibleWhenDisabled:!0,disabled:s,isBusy:s,variant:"primary",children:(0,x.sprintf)((0,x.__)("Install %s"),t.title)})}const je=({originalBlock:e,...t})=>{const{originalName:l,originalUndelimitedContent:s,clientId:n}=t.attributes,{replaceBlock:o}=(0,a.useDispatch)(p.store),r=()=>{o(t.clientId,(0,i.createBlock)("core/html",{content:s}))},d=!!s,u=(0,a.useSelect)((e=>{const{canInsertBlockType:t,getBlockRootClientId:l}=e(p.store);return t("core/html",l(n))}),[n]);let b=(0,x.sprintf)((0,x.__)("Your site doesn’t include support for the %s block. You can try installing the block or remove it entirely."),e.title||l);const h=[(0,ne.jsx)(ve,{block:e,attributes:t.attributes,clientId:t.clientId},"install")];return d&&u&&(b=(0,x.sprintf)((0,x.__)("Your site doesn’t include support for the %s block. You can try installing the block, convert it to a Custom HTML block, or remove it entirely."),e.title||l),h.push((0,ne.jsx)(J.Button,{__next40pxDefaultSize:!0,onClick:r,variant:"tertiary",children:(0,x.__)("Keep as HTML")},"convert"))),(0,ne.jsxs)("div",{...(0,p.useBlockProps)(),children:[(0,ne.jsx)(p.Warning,{actions:h,children:b}),(0,ne.jsx)(c.RawHTML,{children:s})]})},Be=e=>t=>{const{originalName:l}=t.attributes,{block:s,hasPermission:n}=(0,a.useSelect)((e=>{const{getDownloadableBlocks:t}=e(U),s=t("block:"+l).filter((({name:e})=>l===e));return{hasPermission:e(Q.store).canUser("read","block-directory/search"),block:s.length&&s[0]}}),[l]);return n&&s?(0,ne.jsx)(je,{...t,originalBlock:s}):(0,ne.jsx)(e,{...t})};(0,o.registerPlugin)("block-directory",{icon:void 0,render:()=>(0,ne.jsxs)(ne.Fragment,{children:[(0,ne.jsx)(G,{}),(0,ne.jsx)(fe,{}),(0,ne.jsx)(xe,{})]})}),(0,r.addFilter)("blocks.registerBlockType","block-directory/fallback",((e,t)=>("core/missing"!==t||(e.edit=Be(e.edit)),e))),(window.wp=window.wp||{}).blockDirectory=t})();
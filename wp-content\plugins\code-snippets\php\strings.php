<?php
/**
 * Additional strings needed for translation, but not currently present within code.
 *
 * @package Code_Snippets
 */

__( 'You can now safely remove the free version of Code Snippets', 'code-snippets' );

__( 'Success', 'code-snippets' );
__( 'Notice', 'code-snippets' );
__( 'Thanks', 'code-snippets' );
__( 'Okay', 'code-snippets' );

// settings-fields.php.
__( 'Minify Snippet Output', 'code-snippets' );
__( 'Minify snippet output by removing whitespace and optimising code to reduce load times.', 'code-snippets' );

// edit.php.
__( 'View Full Stylesheet', 'code-snippets' );
__( 'View Full Script', 'code-snippets' );

array(
	'site-css'       => __( 'Site front-end stylesheet', 'code-snippets' ),
	'admin-css'      => __( 'Administration area stylesheet', 'code-snippets' ),
	'site-head-js'   => __( 'JavaScript loaded in the site &amp;lt;head&amp;gt; section', 'code-snippets' ),
	'site-footer-js' => __( 'JavaScript loaded just before the closing &amp;lt;/body&amp;gt; tag', 'code-snippets' ),
);

// class-content-widget.php.
__( 'Processing Options', 'code-snippets' );
__( 'Alignment', 'code-snippets' );
__( 'Left', 'code-snippets' );
__( 'Center', 'code-snippets' );
__( 'Right', 'code-snippets' );
__( 'Justified', 'code-snippets' );
__( 'Text Color', 'code-snippets' );
__( 'Select a snippet to show', 'code-snippets' );

// class-source-widget.php.
__( 'Code Snippet Source', 'code-snippets' );
__( 'Functions (PHP)', 'code-snippets' );
__( 'Content (Mixed)', 'code-snippets' );
__( 'Styles (CSS)', 'code-snippets' );
__( 'Scripts (JS)', 'code-snippets' );
__( 'Highlight Lines', 'code-snippets' );
__( 'Word Wrap', 'code-snippets' );
__( 'On', 'code-snippets' );
__( 'Off', 'code-snippets' );
__( 'Height', 'code-snippets' );
__( 'Font Size', 'code-snippets' );
__( 'Select a snippet to display', 'code-snippets' );

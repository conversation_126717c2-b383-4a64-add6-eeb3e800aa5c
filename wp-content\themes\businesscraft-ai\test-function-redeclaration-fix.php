<?php
/**
 * Test Function Redeclaration Fix
 * 
 * This script tests the fix for the chatgabi_get_country_name() function redeclaration
 * and checks for any other potential function conflicts.
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not load WordPress');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Redeclaration Fix Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 24px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 8px; }
        .test-section h2 { margin-top: 0; color: #2563eb; }
        .success { color: #059669; font-weight: 500; }
        .error { color: #dc2626; font-weight: 500; }
        .warning { color: #d97706; font-weight: 500; }
        .info { color: #2563eb; }
        pre { background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto; font-size: 12px; border: 1px solid #e0e0e0; }
        .test-result { padding: 8px; border-radius: 4px; margin: 4px 0; }
        .test-result.pass { background: #d1fae5; border: 1px solid #10b981; }
        .test-result.fail { background: #fee2e2; border: 1px solid #ef4444; }
        .test-result.warn { background: #fef3c7; border: 1px solid #f59e0b; }
        .summary { background: #f0f9ff; border: 1px solid #0ea5e9; padding: 16px; border-radius: 8px; margin-top: 24px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Function Redeclaration Fix Test</h1>
        <p class="info">Testing the fix for <code>chatgabi_get_country_name()</code> function redeclaration conflict.</p>
        
        <div class="test-section">
            <h2>1. WordPress Environment</h2>
            <div class="test-result pass">✅ WordPress loaded successfully</div>
            <div class="info">Current theme: <strong><?php echo wp_get_theme()->get('Name'); ?></strong></div>
            <div class="info">Theme directory: <code><?php echo get_template_directory(); ?></code></div>
        </div>
        
        <div class="test-section">
            <h2>2. Function Existence Test</h2>
            <?php if (function_exists('chatgabi_get_country_name')): ?>
                <div class="test-result pass">✅ chatgabi_get_country_name() function exists</div>
            <?php else: ?>
                <div class="test-result fail">❌ chatgabi_get_country_name() function not found</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>3. Function Behavior Test</h2>
            <?php if (function_exists('chatgabi_get_country_name')): ?>
                <?php
                $test_cases = [
                    'GH' => 'Ghana',
                    'KE' => 'Kenya', 
                    'NG' => 'Nigeria',
                    'ZA' => 'South Africa',
                    'XX' => 'XX' // Should return the code itself as fallback
                ];
                
                $all_passed = true;
                foreach ($test_cases as $code => $expected): ?>
                    <?php 
                    $result = chatgabi_get_country_name($code);
                    $passed = ($result === $expected);
                    if (!$passed) $all_passed = false;
                    ?>
                    <div class="test-result <?php echo $passed ? 'pass' : 'fail'; ?>">
                        <?php echo $passed ? '✅' : '❌'; ?> 
                        <code>chatgabi_get_country_name('<?php echo $code; ?>')</code> 
                        → <strong><?php echo htmlspecialchars($result); ?></strong>
                        <?php if (!$passed): ?>
                            (Expected: <?php echo htmlspecialchars($expected); ?>)
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
                
                <?php if ($all_passed): ?>
                    <div class="test-result pass">✅ All function behavior tests passed</div>
                <?php else: ?>
                    <div class="test-result fail">❌ Some function behavior tests failed</div>
                <?php endif; ?>
            <?php else: ?>
                <div class="test-result fail">❌ Cannot test function behavior - function not available</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>4. File Content Verification</h2>
            <?php
            $template_functions_file = get_template_directory() . '/inc/template-functions.php';
            $context_file = get_template_directory() . '/inc/context-personalization.php';
            
            $template_content = file_exists($template_functions_file) ? file_get_contents($template_functions_file) : '';
            $context_content = file_exists($context_file) ? file_get_contents($context_file) : '';
            
            // Check if function is removed from template-functions.php
            $template_has_function = strpos($template_content, 'function chatgabi_get_country_name(') !== false;
            $context_has_function = strpos($context_content, 'function chatgabi_get_country_name(') !== false;
            $context_has_protection = strpos($context_content, 'function_exists(\'chatgabi_get_country_name\')') !== false;
            ?>
            
            <div class="test-result <?php echo !$template_has_function ? 'pass' : 'fail'; ?>">
                <?php echo !$template_has_function ? '✅' : '❌'; ?> 
                Function removed from template-functions.php
            </div>
            
            <div class="test-result <?php echo $context_has_function ? 'pass' : 'fail'; ?>">
                <?php echo $context_has_function ? '✅' : '❌'; ?> 
                Function exists in context-personalization.php
            </div>
            
            <div class="test-result <?php echo $context_has_protection ? 'pass' : 'fail'; ?>">
                <?php echo $context_has_protection ? '✅' : '❌'; ?> 
                Function protected with function_exists() check
            </div>
        </div>
        
        <div class="test-section">
            <h2>5. Potential Function Conflicts Check</h2>
            <?php
            // Get all ChatGABI functions
            $all_functions = get_defined_functions()['user'];
            $chatgabi_functions = array_filter($all_functions, function($func) {
                return strpos($func, 'chatgabi_') === 0;
            });
            
            // Check for critical functions that might have conflicts
            $critical_functions = [
                'chatgabi_get_country_name',
                'chatgabi_get_template_categories', 
                'chatgabi_get_language_name',
                'chatgabi_build_enhancement_prompt',
                'chatgabi_build_suggestions_prompt'
            ];
            
            echo '<div class="info">Found ' . count($chatgabi_functions) . ' ChatGABI functions loaded</div>';
            
            foreach ($critical_functions as $func):
                $exists = function_exists($func);
            ?>
                <div class="test-result <?php echo $exists ? 'pass' : 'warn'; ?>">
                    <?php echo $exists ? '✅' : '⚠️'; ?> 
                    <code><?php echo $func; ?>()</code> 
                    <?php echo $exists ? 'exists' : 'not found'; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="test-section">
            <h2>6. Template System Integration Test</h2>
            <?php
            // Test if the function works in template context
            $integration_passed = true;
            $integration_errors = [];
            
            try {
                // Test country name retrieval in template context
                if (function_exists('chatgabi_get_country_name')) {
                    $ghana_name = chatgabi_get_country_name('GH');
                    if ($ghana_name !== 'Ghana') {
                        $integration_passed = false;
                        $integration_errors[] = "Ghana name test failed: got '$ghana_name'";
                    }
                    
                    $unknown_code = chatgabi_get_country_name('UNKNOWN');
                    if ($unknown_code !== 'UNKNOWN') {
                        $integration_passed = false;
                        $integration_errors[] = "Unknown code fallback failed: got '$unknown_code'";
                    }
                } else {
                    $integration_passed = false;
                    $integration_errors[] = "Function not available for integration test";
                }
            } catch (Exception $e) {
                $integration_passed = false;
                $integration_errors[] = "Exception during integration test: " . $e->getMessage();
            }
            ?>
            
            <div class="test-result <?php echo $integration_passed ? 'pass' : 'fail'; ?>">
                <?php echo $integration_passed ? '✅' : '❌'; ?> 
                Template system integration
            </div>
            
            <?php if (!empty($integration_errors)): ?>
                <?php foreach ($integration_errors as $error): ?>
                    <div class="test-result fail">❌ <?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>7. Context Personalization Integration Test</h2>
            <?php
            // Test if the function works in context personalization
            $context_integration_passed = true;
            $context_errors = [];
            
            try {
                // Test if context personalization functions are available
                $context_functions = [
                    'chatgabi_get_user_context_preferences',
                    'chatgabi_get_user_profile',
                    'chatgabi_load_user_context'
                ];
                
                foreach ($context_functions as $func) {
                    if (!function_exists($func)) {
                        $context_errors[] = "Context function $func not available";
                        $context_integration_passed = false;
                    }
                }
                
                // Test country name function in context
                if (function_exists('chatgabi_get_country_name')) {
                    $test_countries = ['GH', 'KE', 'NG', 'ZA'];
                    foreach ($test_countries as $country) {
                        $name = chatgabi_get_country_name($country);
                        if (empty($name)) {
                            $context_errors[] = "Empty result for country code: $country";
                            $context_integration_passed = false;
                        }
                    }
                }
            } catch (Exception $e) {
                $context_integration_passed = false;
                $context_errors[] = "Exception during context integration test: " . $e->getMessage();
            }
            ?>
            
            <div class="test-result <?php echo $context_integration_passed ? 'pass' : 'fail'; ?>">
                <?php echo $context_integration_passed ? '✅' : '❌'; ?> 
                Context personalization integration
            </div>
            
            <?php if (!empty($context_errors)): ?>
                <?php foreach ($context_errors as $error): ?>
                    <div class="test-result fail">❌ <?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="summary">
            <h2>📋 Test Summary</h2>
            <?php
            $overall_success = function_exists('chatgabi_get_country_name') && 
                              !$template_has_function && 
                              $context_has_function && 
                              $context_has_protection &&
                              $integration_passed &&
                              $context_integration_passed;
            ?>
            
            <?php if ($overall_success): ?>
                <div class="test-result pass">
                    ✅ <strong>SUCCESS:</strong> Function redeclaration conflict resolved successfully
                </div>
                <div class="info">
                    <strong>Resolution Details:</strong>
                    <ul>
                        <li>Duplicate function removed from template-functions.php</li>
                        <li>Enhanced function kept in context-personalization.php</li>
                        <li>Function protected with function_exists() check</li>
                        <li>Better fallback behavior (returns original code instead of 'Ghana')</li>
                        <li>All integration tests passed</li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="test-result fail">
                    ❌ <strong>ISSUES DETECTED:</strong> Function redeclaration fix needs attention
                </div>
                <div class="info">
                    <strong>Recommended Actions:</strong>
                    <ul>
                        <li>Check for PHP fatal errors in error logs</li>
                        <li>Verify both files are properly loaded</li>
                        <li>Ensure function_exists() protection is in place</li>
                        <li>Test template and context personalization features</li>
                    </ul>
                </div>
            <?php endif; ?>
            
            <div class="info">
                <strong>Next Steps:</strong>
                <ul>
                    <li>Test template generation functionality</li>
                    <li>Verify context personalization features</li>
                    <li>Check for any remaining function conflicts</li>
                    <li>Monitor error logs for any issues</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>

<?php
/**
 * Test South Africa dataset
 */

// Define WordPress constants for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/../../../');
}
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Include the data loader functions
require_once(__DIR__ . '/inc/data-loader.php');

echo "=== South Africa Dataset Test ===\n\n";

// Test loading South Africa dataset
echo "Testing South Africa dataset loading...\n";
echo str_repeat('-', 50) . "\n";

$data = load_business_dataset_by_country('South Africa');

if ($data === false) {
    echo "❌ Failed to load South Africa dataset\n";
    
    // Check what files exist
    $dataset_dir = WP_CONTENT_DIR . '/datasets/south-africa-business-data/';
    $json_files = glob($dataset_dir . '*.json');
    
    echo "\n📁 Available files:\n";
    foreach ($json_files as $file) {
        echo "   - " . basename($file) . "\n";
        
        // Test each file individually
        $content = file_get_contents($file);
        $decoded = json_decode($content, true);
        $error = json_last_error();
        
        if ($error === JSON_ERROR_NONE) {
            echo "     ✅ Valid JSON\n";
            if (isset($decoded['sectors'])) {
                echo "     📊 Sectors: " . count($decoded['sectors']) . "\n";
            }
        } else {
            echo "     ❌ JSON Error: " . json_last_error_msg() . "\n";
        }
    }
} else {
    echo "✅ Successfully loaded South Africa dataset\n";
    
    // Validate structure
    if (isset($data['country']) && isset($data['sectors'])) {
        echo "📊 Country: {$data['country']}\n";
        echo "📈 Sectors found: " . count($data['sectors']) . "\n";
        
        // Show first few sectors
        echo "\n🏢 First 5 sectors:\n";
        for ($i = 0; $i < min(5, count($data['sectors'])); $i++) {
            if (isset($data['sectors'][$i]['sector_name'])) {
                echo "   " . ($i + 1) . ". " . $data['sectors'][$i]['sector_name'] . "\n";
            }
        }
        
        // Test sector extraction
        echo "\n=== Testing Sector Extraction ===\n";
        $test_sectors = ['Mining', 'Agriculture', 'Technology', 'Finance'];
        
        foreach ($test_sectors as $sector) {
            echo "\nTesting sector: {$sector}\n";
            $sector_data = get_sector_context_by_country('South Africa', $sector);
            
            if ($sector_data === null) {
                echo "❌ No data found for {$sector}\n";
            } else {
                echo "✅ Found: {$sector_data['sector_name']}\n";
                echo "📝 Overview: " . substr($sector_data['overview'], 0, 100) . "...\n";
                
                if (isset($sector_data['key_conditions'])) {
                    $conditions = $sector_data['key_conditions'];
                    echo "🔍 Key conditions available:\n";
                    foreach (['regulatory_environment', 'market_size_and_growth', 'major_players'] as $key) {
                        if (isset($conditions[$key])) {
                            echo "   ✓ {$key}\n";
                        } else {
                            echo "   ✗ {$key} (missing)\n";
                        }
                    }
                }
            }
        }
        
        // Show all available sectors
        echo "\n=== All Available Sectors ===\n";
        $all_sectors = get_available_sectors_by_country('South Africa');
        if ($all_sectors !== false) {
            echo "📈 Total sectors: " . count($all_sectors) . "\n";
            foreach ($all_sectors as $i => $sector) {
                echo "   " . ($i + 1) . ". {$sector}\n";
            }
        }
    } else {
        echo "⚠️ Data structure validation failed for South Africa\n";
    }
}

echo "\n=== Test Complete ===\n";

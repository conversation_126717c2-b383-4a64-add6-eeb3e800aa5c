var t={622:(t,e,n)=>{n.d(e,{Ob:()=>q,Qv:()=>z,XX:()=>B,fF:()=>o,h:()=>S,q6:()=>J,uA:()=>E,zO:()=>s});var r,o,i,s,u,c,l,_,a,f,p,h,v,d={},y=[],g=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,m=Array.isArray;function w(t,e){for(var n in e)t[n]=e[n];return t}function b(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function S(t,e,n){var o,i,s,u={};for(s in e)"key"==s?o=e[s]:"ref"==s?i=e[s]:u[s]=e[s];if(arguments.length>2&&(u.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(s in t.defaultProps)void 0===u[s]&&(u[s]=t.defaultProps[s]);return x(t,u,o,i,null)}function x(t,e,n,r,s){var u={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==s?++i:s,__i:-1,__u:0};return null==s&&null!=o.vnode&&o.vnode(u),u}function k(t){return t.children}function E(t,e){this.props=t,this.context=e}function P(t,e){if(null==e)return t.__?P(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?P(t):null}function C(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return C(t)}}function O(t){(!t.__d&&(t.__d=!0)&&u.push(t)&&!$.__r++||c!==o.debounceRendering)&&((c=o.debounceRendering)||l)($)}function $(){for(var t,e,n,r,i,s,c,l=1;u.length;)u.length>l&&u.sort(_),t=u.shift(),l=u.length,t.__d&&(n=void 0,i=(r=(e=t).__v).__e,s=[],c=[],e.__P&&((n=w({},r)).__v=r.__v+1,o.vnode&&o.vnode(n),F(e.__P,n,r,e.__n,e.__P.namespaceURI,32&r.__u?[i]:null,s,null==i?P(r):i,!!(32&r.__u),c),n.__v=r.__v,n.__.__k[n.__i]=n,A(s,n,c),n.__e!=i&&C(n)));$.__r=0}function T(t,e,n,r,o,i,s,u,c,l,_){var a,f,p,h,v,g,m=r&&r.__k||y,w=e.length;for(c=M(n,e,m,c,w),a=0;a<w;a++)null!=(p=n.__k[a])&&(f=-1===p.__i?d:m[p.__i]||d,p.__i=a,g=F(t,p,f,o,i,s,u,c,l,_),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&R(f.ref,null,p),_.push(p.ref,p.__c||h,p)),null==v&&null!=h&&(v=h),4&p.__u||f.__k===p.__k?c=N(p,c,t):"function"==typeof p.type&&void 0!==g?c=g:h&&(c=h.nextSibling),p.__u&=-7);return n.__e=v,c}function M(t,e,n,r,o){var i,s,u,c,l,_=n.length,a=_,f=0;for(t.__k=new Array(o),i=0;i<o;i++)null!=(s=e[i])&&"boolean"!=typeof s&&"function"!=typeof s?(c=i+f,(s=t.__k[i]="string"==typeof s||"number"==typeof s||"bigint"==typeof s||s.constructor==String?x(null,s,null,null,null):m(s)?x(k,{children:s},null,null,null):void 0===s.constructor&&s.__b>0?x(s.type,s.props,s.key,s.ref?s.ref:null,s.__v):s).__=t,s.__b=t.__b+1,u=null,-1!==(l=s.__i=j(s,n,c,a))&&(a--,(u=n[l])&&(u.__u|=2)),null==u||null===u.__v?(-1==l&&(o>_?f--:o<_&&f++),"function"!=typeof s.type&&(s.__u|=4)):l!=c&&(l==c-1?f--:l==c+1?f++:(l>c?f--:f++,s.__u|=4))):t.__k[i]=null;if(a)for(i=0;i<_;i++)null!=(u=n[i])&&!(2&u.__u)&&(u.__e==r&&(r=P(u)),I(u,u));return r}function N(t,e,n){var r,o;if("function"==typeof t.type){for(r=t.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=t,e=N(r[o],e,n));return e}t.__e!=e&&(e&&t.type&&!n.contains(e)&&(e=P(t)),n.insertBefore(t.__e,e||null),e=t.__e);do{e=e&&e.nextSibling}while(null!=e&&8==e.nodeType);return e}function j(t,e,n,r){var o,i,s=t.key,u=t.type,c=e[n];if(null===c&&null==t.key||c&&s==c.key&&u===c.type&&!(2&c.__u))return n;if(r>(null==c||2&c.__u?0:1))for(o=n-1,i=n+1;o>=0||i<e.length;){if(o>=0){if((c=e[o])&&!(2&c.__u)&&s==c.key&&u===c.type)return o;o--}if(i<e.length){if((c=e[i])&&!(2&c.__u)&&s==c.key&&u===c.type)return i;i++}}return-1}function H(t,e,n){"-"==e[0]?t.setProperty(e,null==n?"":n):t[e]=null==n?"":"number"!=typeof n||g.test(e)?n:n+"px"}function U(t,e,n,r,o){var i;t:if("style"==e)if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||H(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||H(t.style,e,n[e])}else if("o"==e[0]&&"n"==e[1])i=e!=(e=e.replace(a,"$1")),e=e.toLowerCase()in t||"onFocusOut"==e||"onFocusIn"==e?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+i]=n,n?r?n.t=r.t:(n.t=f,t.addEventListener(e,i?h:p,i)):t.removeEventListener(e,i?h:p,i);else{if("http://www.w3.org/2000/svg"==o)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=e&&"height"!=e&&"href"!=e&&"list"!=e&&"form"!=e&&"tabIndex"!=e&&"download"!=e&&"rowSpan"!=e&&"colSpan"!=e&&"role"!=e&&"popover"!=e&&e in t)try{t[e]=null==n?"":n;break t}catch(t){}"function"==typeof n||(null==n||!1===n&&"-"!=e[4]?t.removeAttribute(e):t.setAttribute(e,"popover"==e&&1==n?"":n))}}function W(t){return function(e){if(this.l){var n=this.l[e.type+t];if(null==e.u)e.u=f++;else if(e.u<n.t)return;return n(o.event?o.event(e):e)}}}function F(t,e,n,r,i,s,u,c,l,_){var a,f,p,h,v,d,y,g,S,x,P,C,O,$,M,N,j,H=e.type;if(void 0!==e.constructor)return null;128&n.__u&&(l=!!(32&n.__u),s=[c=e.__e=n.__e]),(a=o.__b)&&a(e);t:if("function"==typeof H)try{if(g=e.props,S="prototype"in H&&H.prototype.render,x=(a=H.contextType)&&r[a.__c],P=a?x?x.props.value:a.__:r,n.__c?y=(f=e.__c=n.__c).__=f.__E:(S?e.__c=f=new H(g,P):(e.__c=f=new E(g,P),f.constructor=H,f.render=V),x&&x.sub(f),f.props=g,f.state||(f.state={}),f.context=P,f.__n=r,p=f.__d=!0,f.__h=[],f._sb=[]),S&&null==f.__s&&(f.__s=f.state),S&&null!=H.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=w({},f.__s)),w(f.__s,H.getDerivedStateFromProps(g,f.__s))),h=f.props,v=f.state,f.__v=e,p)S&&null==H.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),S&&null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(S&&null==H.getDerivedStateFromProps&&g!==h&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(g,P),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(g,f.__s,P)||e.__v==n.__v)){for(e.__v!=n.__v&&(f.props=g,f.state=f.__s,f.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.some((function(t){t&&(t.__=e)})),C=0;C<f._sb.length;C++)f.__h.push(f._sb[C]);f._sb=[],f.__h.length&&u.push(f);break t}null!=f.componentWillUpdate&&f.componentWillUpdate(g,f.__s,P),S&&null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(h,v,d)}))}if(f.context=P,f.props=g,f.__P=t,f.__e=!1,O=o.__r,$=0,S){for(f.state=f.__s,f.__d=!1,O&&O(e),a=f.render(f.props,f.state,f.context),M=0;M<f._sb.length;M++)f.__h.push(f._sb[M]);f._sb=[]}else do{f.__d=!1,O&&O(e),a=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++$<25);f.state=f.__s,null!=f.getChildContext&&(r=w(w({},r),f.getChildContext())),S&&!p&&null!=f.getSnapshotBeforeUpdate&&(d=f.getSnapshotBeforeUpdate(h,v)),N=a,null!=a&&a.type===k&&null==a.key&&(N=L(a.props.children)),c=T(t,m(N)?N:[N],e,n,r,i,s,u,c,l,_),f.base=e.__e,e.__u&=-161,f.__h.length&&u.push(f),y&&(f.__E=f.__=null)}catch(t){if(e.__v=null,l||null!=s)if(t.then){for(e.__u|=l?160:128;c&&8==c.nodeType&&c.nextSibling;)c=c.nextSibling;s[s.indexOf(c)]=null,e.__e=c}else for(j=s.length;j--;)b(s[j]);else e.__e=n.__e,e.__k=n.__k;o.__e(t,e,n)}else null==s&&e.__v==n.__v?(e.__k=n.__k,e.__e=n.__e):c=e.__e=D(n.__e,e,n,r,i,s,u,l,_);return(a=o.diffed)&&a(e),128&e.__u?void 0:c}function A(t,e,n){for(var r=0;r<n.length;r++)R(n[r],n[++r],n[++r]);o.__c&&o.__c(e,t),t.some((function(e){try{t=e.__h,e.__h=[],t.some((function(t){t.call(e)}))}catch(t){o.__e(t,e.__v)}}))}function L(t){return"object"!=typeof t||null==t?t:m(t)?t.map(L):w({},t)}function D(t,e,n,i,s,u,c,l,_){var a,f,p,h,v,y,g,w=n.props,S=e.props,x=e.type;if("svg"==x?s="http://www.w3.org/2000/svg":"math"==x?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=u)for(a=0;a<u.length;a++)if((v=u[a])&&"setAttribute"in v==!!x&&(x?v.localName==x:3==v.nodeType)){t=v,u[a]=null;break}if(null==t){if(null==x)return document.createTextNode(S);t=document.createElementNS(s,x,S.is&&S),l&&(o.__m&&o.__m(e,u),l=!1),u=null}if(null===x)w===S||l&&t.data===S||(t.data=S);else{if(u=u&&r.call(t.childNodes),w=n.props||d,!l&&null!=u)for(w={},a=0;a<t.attributes.length;a++)w[(v=t.attributes[a]).name]=v.value;for(a in w)if(v=w[a],"children"==a);else if("dangerouslySetInnerHTML"==a)p=v;else if(!(a in S)){if("value"==a&&"defaultValue"in S||"checked"==a&&"defaultChecked"in S)continue;U(t,a,null,v,s)}for(a in S)v=S[a],"children"==a?h=v:"dangerouslySetInnerHTML"==a?f=v:"value"==a?y=v:"checked"==a?g=v:l&&"function"!=typeof v||w[a]===v||U(t,a,v,w[a],s);if(f)l||p&&(f.__html===p.__html||f.__html===t.innerHTML)||(t.innerHTML=f.__html),e.__k=[];else if(p&&(t.innerHTML=""),T("template"===e.type?t.content:t,m(h)?h:[h],e,n,i,"foreignObject"==x?"http://www.w3.org/1999/xhtml":s,u,c,u?u[0]:n.__k&&P(n,0),l,_),null!=u)for(a=u.length;a--;)b(u[a]);l||(a="value","progress"==x&&null==y?t.removeAttribute("value"):void 0!==y&&(y!==t[a]||"progress"==x&&!y||"option"==x&&y!==w[a])&&U(t,a,y,w[a],s),a="checked",void 0!==g&&g!==t[a]&&U(t,a,g,w[a],s))}return t}function R(t,e,n){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==e||(t.__u=t(e))}else t.current=e}catch(t){o.__e(t,n)}}function I(t,e,n){var r,i;if(o.unmount&&o.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||R(r,null,e)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){o.__e(t,e)}r.base=r.__P=null}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&I(r[i],e,n||"function"!=typeof t.type);n||b(t.__e),t.__c=t.__=t.__e=void 0}function V(t,e,n){return this.constructor(t,n)}function B(t,e,n){var i,s,u,c;e==document&&(e=document.documentElement),o.__&&o.__(t,e),s=(i="function"==typeof n)?null:n&&n.__k||e.__k,u=[],c=[],F(e,t=(!i&&n||e).__k=S(k,null,[t]),s||d,d,e.namespaceURI,!i&&n?[n]:s?null:e.firstChild?r.call(e.childNodes):null,u,!i&&n?n:s?s.__e:e.firstChild,i,c),A(u,t,c)}function z(t,e){B(t,e,z)}function q(t,e,n){var o,i,s,u,c=w({},t.props);for(s in t.type&&t.type.defaultProps&&(u=t.type.defaultProps),e)"key"==s?o=e[s]:"ref"==s?i=e[s]:c[s]=void 0===e[s]&&void 0!==u?u[s]:e[s];return arguments.length>2&&(c.children=arguments.length>3?r.call(arguments,2):n),x(t.type,c,o||t.key,i||t.ref,null)}function J(t){function e(t){var n,r;return this.getChildContext||(n=new Set,(r={})[e.__c]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&n.forEach((function(t){t.__e=!0,O(t)}))},this.sub=function(t){n.add(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){n&&n.delete(t),e&&e.call(t)}}),t.children}return e.__c="__cC"+v++,e.__=t,e.Provider=e.__l=(e.Consumer=function(t,e){return t.children(e)}).contextType=e,e}r=y.slice,o={__e:function(t,e,n,r){for(var o,i,s;e=e.__;)if((o=e.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(t)),s=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(t,r||{}),s=o.__d),s)return o.__E=o}catch(e){t=e}throw t}},i=0,s=function(t){return null!=t&&null==t.constructor},E.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=w({},this.state),"function"==typeof t&&(t=t(w({},n),this.props)),t&&w(n,t),null!=t&&this.__v&&(e&&this._sb.push(e),O(this))},E.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),O(this))},E.prototype.render=k,u=[],l="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,_=function(t,e){return t.__v.__b-e.__v.__b},$.__r=0,a=/(PointerCapture)$|Capture$/i,f=0,p=W(!1),h=W(!0),v=0}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var r={};n.d(r,{zj:()=>he,SD:()=>yt,V6:()=>gt,$K:()=>mt,vT:()=>ve,jb:()=>Ge,yT:()=>bt,M_:()=>ye,hb:()=>Ot,vJ:()=>Pt,ip:()=>Et,Nf:()=>Ct,Kr:()=>$t,li:()=>S,J0:()=>m,FH:()=>kt,v4:()=>xt,mh:()=>Nt});var o,i,s,u,c=n(622),l=0,_=[],a=c.fF,f=a.__b,p=a.__r,h=a.diffed,v=a.__c,d=a.unmount,y=a.__;function g(t,e){a.__h&&a.__h(i,t,l||e),l=0;var n=i.__H||(i.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function m(t){return l=1,function(t,e,n){var r=g(o++,2);if(r.t=t,!r.__c&&(r.__=[n?n(e):N(void 0,e),function(t){var e=r.__N?r.__N[0]:r.__[0],n=r.t(e,t);e!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=i,!i.__f)){var s=function(t,e,n){if(!r.__c.__H)return!0;var o=r.__c.__H.__.filter((function(t){return!!t.__c}));if(o.every((function(t){return!t.__N})))return!u||u.call(this,t,e,n);var i=r.__c.props!==t;return o.forEach((function(t){if(t.__N){var e=t.__[0];t.__=t.__N,t.__N=void 0,e!==t.__[0]&&(i=!0)}})),u&&u.call(this,t,e,n)||i};i.__f=!0;var u=i.shouldComponentUpdate,c=i.componentWillUpdate;i.componentWillUpdate=function(t,e,n){if(this.__e){var r=u;u=void 0,s(t,e,n),u=r}c&&c.call(this,t,e,n)},i.shouldComponentUpdate=s}return r.__N||r.__}(N,t)}function w(t,e){var n=g(o++,3);!a.__s&&M(n.__H,e)&&(n.__=t,n.u=e,i.__H.__h.push(n))}function b(t,e){var n=g(o++,4);!a.__s&&M(n.__H,e)&&(n.__=t,n.u=e,i.__h.push(n))}function S(t){return l=5,x((function(){return{current:t}}),[])}function x(t,e){var n=g(o++,7);return M(n.__H,e)&&(n.__=t(),n.__H=e,n.__h=t),n.__}function k(t,e){return l=8,x((function(){return t}),e)}function E(t){var e=i.context[t.__c],n=g(o++,9);return n.c=t,e?(null==n.__&&(n.__=!0,e.sub(i)),e.props.value):t.__}function P(){for(var t;t=_.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach($),t.__H.__h.forEach(T),t.__H.__h=[]}catch(e){t.__H.__h=[],a.__e(e,t.__v)}}a.__b=function(t){i=null,f&&f(t)},a.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),y&&y(t,e)},a.__r=function(t){p&&p(t),o=0;var e=(i=t.__c).__H;e&&(s===i?(e.__h=[],i.__h=[],e.__.forEach((function(t){t.__N&&(t.__=t.__N),t.u=t.__N=void 0}))):(e.__h.forEach($),e.__h.forEach(T),e.__h=[],o=0)),s=i},a.diffed=function(t){h&&h(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(1!==_.push(e)&&u===a.requestAnimationFrame||((u=a.requestAnimationFrame)||O)(P)),e.__H.__.forEach((function(t){t.u&&(t.__H=t.u),t.u=void 0}))),s=i=null},a.__c=function(t,e){e.some((function(t){try{t.__h.forEach($),t.__h=t.__h.filter((function(t){return!t.__||T(t)}))}catch(n){e.some((function(t){t.__h&&(t.__h=[])})),e=[],a.__e(n,t.__v)}})),v&&v(t,e)},a.unmount=function(t){d&&d(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach((function(t){try{$(t)}catch(t){e=t}})),n.__H=void 0,e&&a.__e(e,n.__v))};var C="function"==typeof requestAnimationFrame;function O(t){var e,n=function(){clearTimeout(r),C&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);C&&(e=requestAnimationFrame(n))}function $(t){var e=i,n=t.__c;"function"==typeof n&&(t.__c=void 0,n()),i=e}function T(t){var e=i;t.__c=t.__(),i=e}function M(t,e){return!t||t.length!==e.length||e.some((function(e,n){return e!==t[n]}))}function N(t,e){return"function"==typeof e?e(t):e}var j=Symbol.for("preact-signals");function H(){if(L>1)L--;else{for(var t,e=!1;void 0!==A;){var n=A;for(A=void 0,D++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&z(n))try{n.c()}catch(n){e||(t=n,e=!0)}n=r}}if(D=0,L--,e)throw t}}function U(t){if(L>0)return t();L++;try{return t()}finally{H()}}var W=void 0;var F,A=void 0,L=0,D=0,R=0;function I(t){if(void 0!==W){var e=t.n;if(void 0===e||e.t!==W)return e={i:0,S:t,p:W.s,n:void 0,t:W,e:void 0,x:void 0,r:e},void 0!==W.s&&(W.s.n=e),W.s=e,t.n=e,32&W.f&&t.S(e),e;if(-1===e.i)return e.i=0,void 0!==e.n&&(e.n.p=e.p,void 0!==e.p&&(e.p.n=e.n),e.p=W.s,e.n=void 0,W.s.n=e,W.s=e),e}}function V(t){this.v=t,this.i=0,this.n=void 0,this.t=void 0}function B(t){return new V(t)}function z(t){for(var e=t.s;void 0!==e;e=e.n)if(e.S.i!==e.i||!e.S.h()||e.S.i!==e.i)return!0;return!1}function q(t){for(var e=t.s;void 0!==e;e=e.n){var n=e.S.n;if(void 0!==n&&(e.r=n),e.S.n=e,e.i=-1,void 0===e.n){t.s=e;break}}}function J(t){for(var e=t.s,n=void 0;void 0!==e;){var r=e.p;-1===e.i?(e.S.U(e),void 0!==r&&(r.n=e.n),void 0!==e.n&&(e.n.p=r)):n=e,e.S.n=e.r,void 0!==e.r&&(e.r=void 0),e=r}t.s=n}function K(t){V.call(this,void 0),this.x=t,this.s=void 0,this.g=R-1,this.f=4}function G(t){return new K(t)}function X(t){var e=t.u;if(t.u=void 0,"function"==typeof e){L++;var n=W;W=void 0;try{e()}catch(e){throw t.f&=-2,t.f|=8,Q(t),e}finally{W=n,H()}}}function Q(t){for(var e=t.s;void 0!==e;e=e.n)e.S.U(e);t.x=void 0,t.s=void 0,X(t)}function Y(t){if(W!==this)throw new Error("Out-of-order effect");J(this),W=t,this.f&=-2,8&this.f&&Q(this),H()}function Z(t){this.x=t,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function tt(t){var e=new Z(t);try{e.c()}catch(t){throw e.d(),t}return e.d.bind(e)}function et(t,e){c.fF[t]=e.bind(null,c.fF[t]||function(){})}function nt(t){F&&F(),F=t&&t.S()}function rt(t){var e=this,n=t.data,r=function(t){return x((function(){return B(t)}),[])}(n);r.value=n;var o=x((function(){for(var t=e.__v;t=t.__;)if(t.__c){t.__c.__$f|=4;break}return e.__$u.c=function(){var t,n=e.__$u.S(),r=o.value;n(),(0,c.zO)(r)||3!==(null==(t=e.base)?void 0:t.nodeType)?(e.__$f|=1,e.setState({})):e.base.data=r},G((function(){var t=r.value.value;return 0===t?0:!0===t?"":t||""}))}),[]);return o.value}function ot(t,e,n,r){var o=e in t&&void 0===t.ownerSVGElement,i=B(n);return{o:function(t,e){i.value=t,r=e},d:tt((function(){var n=i.value.value;r[e]!==n&&(r[e]=n,o?t[e]=n:n?t.setAttribute(e,n):t.removeAttribute(e))}))}}V.prototype.brand=j,V.prototype.h=function(){return!0},V.prototype.S=function(t){this.t!==t&&void 0===t.e&&(t.x=this.t,void 0!==this.t&&(this.t.e=t),this.t=t)},V.prototype.U=function(t){if(void 0!==this.t){var e=t.e,n=t.x;void 0!==e&&(e.x=n,t.e=void 0),void 0!==n&&(n.e=e,t.x=void 0),t===this.t&&(this.t=n)}},V.prototype.subscribe=function(t){var e=this;return tt((function(){var n=e.value,r=W;W=void 0;try{t(n)}finally{W=r}}))},V.prototype.valueOf=function(){return this.value},V.prototype.toString=function(){return this.value+""},V.prototype.toJSON=function(){return this.value},V.prototype.peek=function(){var t=W;W=void 0;try{return this.value}finally{W=t}},Object.defineProperty(V.prototype,"value",{get:function(){var t=I(this);return void 0!==t&&(t.i=this.i),this.v},set:function(t){if(t!==this.v){if(D>100)throw new Error("Cycle detected");this.v=t,this.i++,R++,L++;try{for(var e=this.t;void 0!==e;e=e.x)e.t.N()}finally{H()}}}}),(K.prototype=new V).h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===R)return!0;if(this.g=R,this.f|=1,this.i>0&&!z(this))return this.f&=-2,!0;var t=W;try{q(this),W=this;var e=this.x();(16&this.f||this.v!==e||0===this.i)&&(this.v=e,this.f&=-17,this.i++)}catch(t){this.v=t,this.f|=16,this.i++}return W=t,J(this),this.f&=-2,!0},K.prototype.S=function(t){if(void 0===this.t){this.f|=36;for(var e=this.s;void 0!==e;e=e.n)e.S.S(e)}V.prototype.S.call(this,t)},K.prototype.U=function(t){if(void 0!==this.t&&(V.prototype.U.call(this,t),void 0===this.t)){this.f&=-33;for(var e=this.s;void 0!==e;e=e.n)e.S.U(e)}},K.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var t=this.t;void 0!==t;t=t.x)t.t.N()}},Object.defineProperty(K.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var t=I(this);if(this.h(),void 0!==t&&(t.i=this.i),16&this.f)throw this.v;return this.v}}),Z.prototype.c=function(){var t=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var e=this.x();"function"==typeof e&&(this.u=e)}finally{t()}},Z.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,X(this),q(this),L++;var t=W;return W=this,Y.bind(this,t)},Z.prototype.N=function(){2&this.f||(this.f|=2,this.o=A,A=this)},Z.prototype.d=function(){this.f|=8,1&this.f||Q(this)},rt.displayName="_st",Object.defineProperties(V.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:rt},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),et("__b",(function(t,e){if("string"==typeof e.type){var n,r=e.props;for(var o in r)if("children"!==o){var i=r[o];i instanceof V&&(n||(e.__np=n={}),n[o]=i,r[o]=i.peek())}}t(e)})),et("__r",(function(t,e){nt();var n,r=e.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=n=function(){var t;return tt((function(){t=this})),t.c=function(){r.__$f|=1,r.setState({})},t}())),nt(n),t(e)})),et("__e",(function(t,e,n,r){nt(),t(e,n,r)})),et("diffed",(function(t,e){var n;if(nt(),"string"==typeof e.type&&(n=e.__e)){var r=e.__np,o=e.props;if(r){var i=n.U;if(i)for(var s in i){var u=i[s];void 0===u||s in r||(u.d(),i[s]=void 0)}else n.U=i={};for(var c in r){var l=i[c],_=r[c];void 0===l?(l=ot(n,c,_,o),i[c]=l):l.o(_,o)}}}t(e)})),et("unmount",(function(t,e){if("string"==typeof e.type){var n=e.__e;if(n){var r=n.U;if(r)for(var o in n.U=void 0,r){var i=r[o];i&&i.d()}}}else{var s=e.__c;if(s){var u=s.__$u;u&&(s.__$u=void 0,u.d())}}t(e)})),et("__h",(function(t,e,n,r){(r<3||9===r)&&(e.__$f|=2),t(e,n,r)})),c.uA.prototype.shouldComponentUpdate=function(t,e){var n=this.__$u,r=n&&void 0!==n.s;for(var o in e)return!0;if(this.__f||"boolean"==typeof this.u&&!0===this.u){if(!(r||2&this.__$f||4&this.__$f))return!0;if(1&this.__$f)return!0}else{if(!(r||4&this.__$f))return!0;if(3&this.__$f)return!0}for(var i in t)if("__source"!==i&&t[i]!==this.props[i])return!0;for(var s in this.props)if(!(s in t))return!0;return!1};const it=[],st=()=>it.slice(-1)[0],ut=t=>{it.push(t)},ct=()=>{it.pop()},lt=[],_t=()=>lt.slice(-1)[0],at=t=>{lt.push(t)},ft=()=>{lt.pop()},pt=new WeakMap,ht=()=>{throw new Error("Please use `data-wp-bind` to modify the attributes of an element.")},vt={get(t,e,n){const r=Reflect.get(t,e,n);return r&&"object"==typeof r?dt(r):r},set:ht,deleteProperty:ht},dt=t=>(pt.has(t)||pt.set(t,new Proxy(t,vt)),pt.get(t)),yt=t=>_t().context[t||st()],gt=()=>{const t=_t();const{ref:e,attributes:n}=t;return Object.freeze({ref:e.current,attributes:dt(n)})},mt=t=>_t().serverContext[t||st()],wt=t=>new Promise((e=>{const n=()=>{clearTimeout(r),window.cancelAnimationFrame(o),setTimeout((()=>{t(),e()}))},r=setTimeout(n,100),o=window.requestAnimationFrame(n)})),bt="function"==typeof window.scheduler?.yield?window.scheduler.yield.bind(window.scheduler):()=>new Promise((t=>{setTimeout(t,0)}));function St(t){w((()=>{let e=null,n=!1;return e=function(t,e){let n=()=>{};const r=tt((function(){return n=this.c.bind(this),this.x=t,this.c=e,t()}));return{flush:n,dispose:r}}(t,(async()=>{e&&!n&&(n=!0,await wt(e.flush),n=!1)})),e.dispose}),[])}function xt(t){const e=_t(),n=st();let r;r="GeneratorFunction"===t?.constructor?.name?async(...r)=>{const o=t(...r);let i,s;for(;;){ut(n),at(e);try{s=o.next(i)}finally{ft(),ct()}try{i=await s.value}catch(t){ut(n),at(e),o.throw(t)}finally{ft(),ct()}if(s.done)break}return i}:(...r)=>{ut(n),at(e);try{return t(...r)}finally{ct(),ft()}};if(t.sync){const t=r;return t.sync=!0,t}return r}function kt(t){St(xt(t))}function Et(t){w(xt(t),[])}function Pt(t,e){w(xt(t),e)}function Ct(t,e){b(xt(t),e)}function Ot(t,e){return k(xt(t),e)}function $t(t,e){return x(xt(t),e)}new Set;const Tt=t=>{0},Mt=t=>Boolean(t&&"object"==typeof t&&t.constructor===Object);function Nt(t){const e=t;return e.sync=!0,e}const jt=new WeakMap,Ht=new WeakMap,Ut=new WeakMap,Wt=new Set([Object,Array]),Ft=(t,e,n)=>{if(!Dt(e))throw Error("This object cannot be proxified.");if(!jt.has(e)){const r=new Proxy(e,n);jt.set(e,r),Ht.set(r,e),Ut.set(r,t)}return jt.get(e)},At=t=>jt.get(t),Lt=t=>Ut.get(t),Dt=t=>"object"==typeof t&&null!==t&&(!Ut.has(t)&&Wt.has(t.constructor)),Rt={};class It{constructor(t){this.owner=t,this.computedsByScope=new WeakMap}setValue(t){this.update({value:t})}setGetter(t){this.update({get:t})}getComputed(){const t=_t()||Rt;if(this.valueSignal||this.getterSignal||this.update({}),!this.computedsByScope.has(t)){const e=()=>{const t=this.getterSignal?.value;return t?t.call(this.owner):this.valueSignal?.value};ut(Lt(this.owner)),this.computedsByScope.set(t,G(xt(e))),ct()}return this.computedsByScope.get(t)}update({get:t,value:e}){this.valueSignal?e===this.valueSignal.peek()&&t===this.getterSignal.peek()||U((()=>{this.valueSignal.value=e,this.getterSignal.value=t})):(this.valueSignal=B(e),this.getterSignal=B(t))}}const Vt=new Set(Object.getOwnPropertyNames(Symbol).map((t=>Symbol[t])).filter((t=>"symbol"==typeof t))),Bt=new WeakMap,zt=(t,e)=>Bt.has(t)&&Bt.get(t).has(e),qt=new WeakSet,Jt=(t,e,n)=>{Bt.has(t)||Bt.set(t,new Map),e="number"==typeof e?`${e}`:e;const r=Bt.get(t);if(!r.has(e)){const o=Lt(t),i=new It(t);if(r.set(e,i),n){const{get:e,value:r}=n;if(e)i.setGetter(e);else{const e=qt.has(t);i.setValue(Dt(r)?Qt(o,r,{readOnly:e}):r)}}}return r.get(e)},Kt=new WeakMap;let Gt=!1;const Xt={get(t,e,n){if(Gt||!t.hasOwnProperty(e)&&e in t||"symbol"==typeof e&&Vt.has(e))return Reflect.get(t,e,n);const r=Object.getOwnPropertyDescriptor(t,e),o=Jt(n,e,r).getComputed().value;if("function"==typeof o){const t=Lt(n);return(...e)=>{ut(t);try{return o.call(n,...e)}finally{ct()}}}return o},set(t,e,n,r){if(qt.has(r))return!1;ut(Lt(r));try{return Reflect.set(t,e,n,r)}finally{ct()}},defineProperty(t,e,n){if(qt.has(At(t)))return!1;const r=!(e in t),o=Reflect.defineProperty(t,e,n);if(o){const o=At(t),i=Jt(o,e),{get:s,value:u}=n;if(s)i.setGetter(s);else{const t=Lt(o);i.setValue(Dt(u)?Qt(t,u):u)}if(r&&Kt.has(t)&&Kt.get(t).value++,Array.isArray(t)&&Bt.get(o)?.has("length")){Jt(o,"length").setValue(t.length)}}return o},deleteProperty(t,e){if(qt.has(At(t)))return!1;const n=Reflect.deleteProperty(t,e);if(n){Jt(At(t),e).setValue(void 0),Kt.has(t)&&Kt.get(t).value++}return n},ownKeys:t=>(Kt.has(t)||Kt.set(t,B(0)),Kt._=Kt.get(t).value,Reflect.ownKeys(t))},Qt=(t,e,n)=>{const r=Ft(t,e,Xt);return n?.readOnly&&qt.add(r),r},Yt=(t,e,n=!0)=>{if(!Mt(t)||!Mt(e))return;let r=!1;for(const o in e){const i=!(o in t);r=r||i;const s=Object.getOwnPropertyDescriptor(e,o),u=At(t),c=!!u&&zt(u,o)&&Jt(u,o);if("function"==typeof s.get||"function"==typeof s.set)(n||i)&&(Object.defineProperty(t,o,{...s,configurable:!0,enumerable:!0}),s.get&&c&&c.setGetter(s.get));else if(Mt(e[o])){const r=Object.getOwnPropertyDescriptor(t,o)?.value;if(i||n&&!Mt(r)){if(t[o]={},c){const e=Lt(u);c.setValue(Qt(e,t[o]))}Yt(t[o],e[o],n)}else Mt(r)&&Yt(t[o],e[o],n)}else if((n||i)&&(Object.defineProperty(t,o,s),c)){const{value:t}=s,e=Lt(u);c.setValue(Dt(t)?Qt(e,t):t)}}r&&Kt.has(t)&&Kt.get(t).value++},Zt=(t,e,n=!0)=>U((()=>{return Yt((r=t,Ht.get(r)||t),e,n);var r})),te=new WeakSet,ee={get:(t,e,n)=>{const r=Reflect.get(t,e),o=Lt(n);if(void 0===r&&te.has(n)){const n={};return Reflect.set(t,e,n),ne(o,n,!1)}if("function"==typeof r){ut(o);const t=xt(r);return ct(),t}return Mt(r)&&Dt(r)?ne(o,r,!1):r}},ne=(t,e,n=!0)=>{const r=Ft(t,e,ee);return r&&n&&te.add(r),r},re=new WeakMap,oe=new WeakMap,ie=new WeakSet,se=Reflect.getOwnPropertyDescriptor,ue={get:(t,e)=>{const n=oe.get(t),r=t[e];return e in t?r:n[e]},set:(t,e,n)=>{const r=oe.get(t);return(e in t||!(e in r)?t:r)[e]=n,!0},ownKeys:t=>[...new Set([...Object.keys(oe.get(t)),...Object.keys(t)])],getOwnPropertyDescriptor:(t,e)=>se(t,e)||se(oe.get(t),e),has:(t,e)=>Reflect.has(t,e)||Reflect.has(oe.get(t),e)},ce=(t,e={})=>{if(ie.has(t))throw Error("This object cannot be proxified.");if(oe.set(t,e),!re.has(t)){const e=new Proxy(t,ue);re.set(t,e),ie.add(e)}return re.get(t)},le=new Map,_e=new Map,ae=new Map,fe=new Map,pe=new Map,he=t=>fe.get(t||st())||{},ve=t=>{const e=t||st();return pe.has(e)||pe.set(e,Qt(e,{},{readOnly:!0})),pe.get(e)},de="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";function ye(t,{state:e={},...n}={},{lock:r=!1}={}){if(le.has(t)){if(r===de||ae.has(t)){const e=ae.get(t);if(!(r===de||!0!==r&&r===e))throw e?Error("Cannot unlock a private store with an invalid lock code"):Error("Cannot lock a public store")}else ae.set(t,r);const o=_e.get(t);Zt(o,n),Zt(o.state,e)}else{r!==de&&ae.set(t,r);const o={state:Qt(t,Mt(e)?e:{}),...n},i=ne(t,o);_e.set(t,o),le.set(t,i)}return le.get(t)}const ge=(t=document)=>{var e;const n=null!==(e=t.getElementById("wp-script-module-data-@wordpress/interactivity"))&&void 0!==e?e:t.getElementById("wp-interactivity-data");if(n?.textContent)try{return JSON.parse(n.textContent)}catch{}return{}},me=t=>{Mt(t?.state)&&Object.entries(t.state).forEach((([t,e])=>{const n=ye(t,{},{lock:de});Zt(n.state,e,!1),Zt(ve(t),e)})),Mt(t?.config)&&Object.entries(t.config).forEach((([t,e])=>{fe.set(t,e)}))},we=ge();function be(t){return null!==t.suffix}function Se(t){return null===t.suffix}me(we);const xe=(0,c.q6)({client:{},server:{}}),ke={},Ee={},Pe=(t,e,{priority:n=10}={})=>{ke[t]=e,Ee[t]=n},Ce=({scope:t})=>(e,...n)=>{let{value:r,namespace:o}=e;if("string"!=typeof r)throw new Error("The `value` prop should be a string path");const i="!"===r[0]&&!!(r=r.slice(1));at(t);const s=((t,e)=>{if(!e)return void Tt();let n=le.get(e);void 0===n&&(n=ye(e,{},{lock:de}));const r={...n,context:_t().context[e]};try{return t.split(".").reduce(((t,e)=>t[e]),r)}catch(t){}})(r,o);if("function"==typeof s){if(i){Tt();const t=!s(...n);return ft(),t}return ft(),(...e)=>{at(t);const n=s(...e);return ft(),n}}const u=s;return ft(),i?!u:u},Oe=({directives:t,priorityLevels:[e,...n],element:r,originalProps:o,previousScope:i})=>{const s=S({}).current;s.evaluate=k(Ce({scope:s}),[]);const{client:u,server:l}=E(xe);s.context=u,s.serverContext=l,s.ref=i?.ref||S(null),r=(0,c.Ob)(r,{ref:s.ref}),s.attributes=r.props;const _=n.length>0?(0,c.h)(Oe,{directives:t,priorityLevels:n,element:r,originalProps:o,previousScope:s}):r,a={...o,children:_},f={directives:t,props:a,element:r,context:xe,evaluate:s.evaluate};at(s);for(const t of e){const e=ke[t]?.(f);void 0!==e&&(a.children=e)}return ft(),a.children},$e=c.fF.vnode;function Te(t){return Mt(t)?Object.fromEntries(Object.entries(t).map((([t,e])=>[t,Te(e)]))):Array.isArray(t)?t.map((t=>Te(t))):t}function Me(t){return new Proxy(t,{get(t,e,n){const r=t[e];switch(e){case"currentTarget":case"preventDefault":case"stopImmediatePropagation":case"stopPropagation":Tt()}return r instanceof Function?function(...e){return r.apply(this===n?t:this,e)}:r}})}c.fF.vnode=t=>{if(t.props.__directives){const e=t.props,n=e.__directives;n.key&&(t.key=n.key.find(Se).value),delete e.__directives;const r=(t=>{const e=Object.keys(t).reduce(((t,e)=>{if(ke[e]){const n=Ee[e];(t[n]=t[n]||[]).push(e)}return t}),{});return Object.entries(e).sort((([t],[e])=>parseInt(t)-parseInt(e))).map((([,t])=>t))})(n);r.length>0&&(t.props={directives:n,priorityLevels:r,originalProps:e,type:t.type,element:(0,c.h)(t.type,e),top:!0},t.type=Oe)}$e&&$e(t)};const Ne=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,je=/\/\*[^]*?\*\/|  +/g,He=/\n+/g,Ue=t=>({directives:e,evaluate:n})=>{e[`on-${t}`].filter(be).forEach((e=>{const r=e.suffix.split("--",1)[0];Et((()=>{const o=t=>{const r=n(e);"function"==typeof r&&(r?.sync||(t=Me(t)),r(t))},i="window"===t?window:document;return i.addEventListener(r,o),()=>i.removeEventListener(r,o)}))}))},We=t=>({directives:e,evaluate:n})=>{e[`on-async-${t}`].filter(be).forEach((e=>{const r=e.suffix.split("--",1)[0];Et((()=>{const o=async t=>{await bt();const r=n(e);"function"==typeof r&&r(t)},i="window"===t?window:document;return i.addEventListener(r,o,{passive:!0}),()=>i.removeEventListener(r,o)}))}))},Fe="wp",Ae=`data-${Fe}-ignore`,Le=`data-${Fe}-interactive`,De=`data-${Fe}-`,Re=[],Ie=new RegExp(`^data-${Fe}-([a-z0-9]+(?:-[a-z0-9]+)*)(?:--([a-z0-9_-]+))?$`,"i"),Ve=/^([\w_\/-]+)::(.+)$/,Be=new WeakSet;function ze(t){const e=document.createTreeWalker(t,205);return function t(n){const{nodeType:r}=n;if(3===r)return[n.data];if(4===r){var o;const t=e.nextSibling();return n.replaceWith(new window.Text(null!==(o=n.nodeValue)&&void 0!==o?o:"")),[n.nodeValue,t]}if(8===r||7===r){const t=e.nextSibling();return n.remove(),[null,t]}const i=n,{attributes:s}=i,u=i.localName,l={},_=[],a=[];let f=!1,p=!1;for(let t=0;t<s.length;t++){const e=s[t].name,n=s[t].value;if(e[De.length]&&e.slice(0,De.length)===De)if(e===Ae)f=!0;else{var h,v;const t=Ve.exec(n),r=null!==(h=t?.[1])&&void 0!==h?h:null;let o=null!==(v=t?.[2])&&void 0!==v?v:n;try{const t=JSON.parse(o);d=t,o=Boolean(d&&"object"==typeof d&&d.constructor===Object)?t:o}catch{}if(e===Le){p=!0;const t="string"==typeof o?o:"string"==typeof o?.namespace?o.namespace:null;Re.push(t)}else a.push([e,r,o])}else if("ref"===e)continue;l[e]=n}var d;if(f&&!p)return[(0,c.h)(u,{...l,innerHTML:i.innerHTML,__directives:{ignore:!0}})];if(p&&Be.add(i),a.length&&(l.__directives=a.reduce(((t,[e,n,r])=>{const o=Ie.exec(e);if(null===o)return Tt(),t;const i=o[1]||"",s=o[2]||null;var u;return t[i]=t[i]||[],t[i].push({namespace:null!=n?n:null!==(u=Re[Re.length-1])&&void 0!==u?u:null,value:r,suffix:s}),t}),{})),"template"===u)l.content=[...i.content.childNodes].map((t=>ze(t)));else{let n=e.firstChild();if(n){for(;n;){const[r,o]=t(n);r&&_.push(r),n=o||e.nextSibling()}e.parentNode()}}return p&&Re.pop(),[(0,c.h)(u,l,_)]}(e.currentNode)}const qe=new WeakMap,Je=t=>{if(!t.parentElement)throw Error("The passed region should be an element with a parent.");return qe.has(t)||qe.set(t,((t,e)=>{const n=(e=[].concat(e))[e.length-1].nextSibling;function r(e,r){t.insertBefore(e,r||n)}return t.__k={nodeType:1,parentNode:t,firstChild:e[0],childNodes:e,insertBefore:r,appendChild:r,removeChild(e){t.removeChild(e)}}})(t.parentElement,t)),qe.get(t)},Ke=new WeakMap,Ge=t=>{if("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."===t)return{directivePrefix:Fe,getRegionRootFragment:Je,initialVdom:Ke,toVdom:ze,directive:Pe,getNamespace:st,h:c.h,cloneElement:c.Ob,render:c.XX,proxifyState:Qt,parseServerData:ge,populateServerData:me,batch:U};throw new Error("Forbidden access.")};Pe("context",(({directives:{context:t},props:{children:e},context:n})=>{const{Provider:r}=n,o=t.find(Se),{client:i,server:s}=E(n),u=o.namespace,l=S(Qt(u,{})),_=S(Qt(u,{},{readOnly:!0})),a=x((()=>{const t={client:{...i},server:{...s}};if(o){const{namespace:e,value:n}=o;Mt(n)||Tt(),Zt(l.current,Te(n),!1),Zt(_.current,Te(n)),t.client[e]=ce(l.current,i[e]),t.server[e]=ce(_.current,s[e])}return t}),[o,i,s]);return(0,c.h)(r,{value:a},e)}),{priority:5}),Pe("watch",(({directives:{watch:t},evaluate:e})=>{t.forEach((t=>{kt((()=>{let n=e(t);return"function"==typeof n&&(n=n()),n}))}))})),Pe("init",(({directives:{init:t},evaluate:e})=>{t.forEach((t=>{Et((()=>{let n=e(t);return"function"==typeof n&&(n=n()),n}))}))})),Pe("on",(({directives:{on:t},element:e,evaluate:n})=>{const r=new Map;t.filter(be).forEach((t=>{const e=t.suffix.split("--")[0];r.has(e)||r.set(e,new Set),r.get(e).add(t)})),r.forEach(((t,r)=>{const o=e.props[`on${r}`];e.props[`on${r}`]=e=>{t.forEach((t=>{o&&o(e);const r=n(t);"function"==typeof r&&(r?.sync||(e=Me(e)),r(e))}))}}))})),Pe("on-async",(({directives:{"on-async":t},element:e,evaluate:n})=>{const r=new Map;t.filter(be).forEach((t=>{const e=t.suffix.split("--")[0];r.has(e)||r.set(e,new Set),r.get(e).add(t)})),r.forEach(((t,r)=>{const o=e.props[`on${r}`];e.props[`on${r}`]=e=>{o&&o(e),t.forEach((async t=>{await bt();const r=n(t);"function"==typeof r&&r(e)}))}}))})),Pe("on-window",Ue("window")),Pe("on-document",Ue("document")),Pe("on-async-window",We("window")),Pe("on-async-document",We("document")),Pe("class",(({directives:{class:t},element:e,evaluate:n})=>{t.filter(be).forEach((t=>{const r=t.suffix;let o=n(t);"function"==typeof o&&(o=o());const i=e.props.class||"",s=new RegExp(`(^|\\s)${r}(\\s|$)`,"g");o?s.test(i)||(e.props.class=i?`${i} ${r}`:r):e.props.class=i.replace(s," ").trim(),Et((()=>{o?e.ref.current.classList.add(r):e.ref.current.classList.remove(r)}))}))})),Pe("style",(({directives:{style:t},element:e,evaluate:n})=>{t.filter(be).forEach((t=>{const r=t.suffix;let o=n(t);"function"==typeof o&&(o=o()),e.props.style=e.props.style||{},"string"==typeof e.props.style&&(e.props.style=(t=>{const e=[{}];let n,r;for(;n=Ne.exec(t.replace(je,""));)n[4]?e.shift():n[3]?(r=n[3].replace(He," ").trim(),e.unshift(e[0][r]=e[0][r]||{})):e[0][n[1]]=n[2].replace(He," ").trim();return e[0]})(e.props.style)),o?e.props.style[r]=o:delete e.props.style[r],Et((()=>{o?e.ref.current.style[r]=o:e.ref.current.style.removeProperty(r)}))}))})),Pe("bind",(({directives:{bind:t},element:e,evaluate:n})=>{t.filter(be).forEach((t=>{const r=t.suffix;let o=n(t);"function"==typeof o&&(o=o()),e.props[r]=o,Et((()=>{const t=e.ref.current;if("style"!==r){if("width"!==r&&"height"!==r&&"href"!==r&&"list"!==r&&"form"!==r&&"tabIndex"!==r&&"download"!==r&&"rowSpan"!==r&&"colSpan"!==r&&"role"!==r&&r in t)try{return void(t[r]=null==o?"":o)}catch(t){}null==o||!1===o&&"-"!==r[4]?t.removeAttribute(r):t.setAttribute(r,o)}else"string"==typeof o&&(t.style.cssText=o)}))}))})),Pe("ignore",(({element:{type:t,props:{innerHTML:e,...n}}})=>{const r=x((()=>e),[]);return(0,c.h)(t,{dangerouslySetInnerHTML:{__html:r},...n})})),Pe("text",(({directives:{text:t},element:e,evaluate:n})=>{const r=t.find(Se);if(r)try{let t=n(r);"function"==typeof t&&(t=t()),e.props.children="object"==typeof t?null:t.toString()}catch(t){e.props.children=null}else e.props.children=null})),Pe("run",(({directives:{run:t},evaluate:e})=>{t.forEach((t=>{let n=e(t);return"function"==typeof n&&(n=n()),n}))})),Pe("each",(({directives:{each:t,"each-key":e},context:n,element:r,evaluate:o})=>{if("template"!==r.type)return;const{Provider:i}=n,s=E(n),[u]=t,{namespace:l}=u;let _=o(u);if("function"==typeof _&&(_=_()),"function"!=typeof _?.[Symbol.iterator])return;const a=be(u)?u.suffix.replace(/^-+|-+$/g,"").toLowerCase().replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})):"item",f=[];for(const t of _){const n=ce(Qt(l,{}),s.client[l]),o={client:{...s.client,[l]:n},server:{...s.server}};o.client[l][a]=t;const u={..._t(),context:o.client,serverContext:o.server},_=e?Ce({scope:u})(e[0]):t;f.push((0,c.h)(i,{value:o,key:_},r.props.content))}return f}),{priority:20}),Pe("each-child",(()=>null),{priority:1}),(async()=>{const t=document.querySelectorAll(`[data-${Fe}-interactive]`);await new Promise((t=>{setTimeout(t,0)}));for(const e of t)if(!Be.has(e)){await bt();const t=Je(e),n=ze(e);Ke.set(e,n),await bt(),(0,c.Qv)(n,t)}})();var Xe=r.zj,Qe=r.SD,Ye=r.V6,Ze=r.$K,tn=r.vT,en=r.jb,nn=r.yT,rn=r.M_,on=r.hb,sn=r.vJ,un=r.ip,cn=r.Nf,ln=r.Kr,_n=r.li,an=r.J0,fn=r.FH,pn=r.v4,hn=r.mh;export{Xe as getConfig,Qe as getContext,Ye as getElement,Ze as getServerContext,tn as getServerState,en as privateApis,nn as splitTask,rn as store,on as useCallback,sn as useEffect,un as useInit,cn as useLayoutEffect,ln as useMemo,_n as useRef,an as useState,fn as useWatch,pn as withScope,hn as withSyncEvent};
<?php
/**
 * Fix Three Specific Template Issues
 * 
 * 1. Templates Page Navigation Failure
 * 2. Admin Dashboard Template Management
 * 3. User Dashboard Template Visibility
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates - Fix Three Issues</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .fix-button:hover { background: #005a87; }
    </style>
</head>
<body>

<h1>🔧 ChatGABI Templates - Fix Three Issues</h1>

<?php
echo '<div class="info">Fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$fixes_applied = array();
$errors_encountered = array();

// Fix 1: Templates Page Navigation Failure
echo '<h2>🚨 Fix 1: Templates Page Navigation Failure</h2>';

try {
    // Check and create templates page
    $templates_page = get_page_by_path('templates');
    if (!$templates_page) {
        echo '<div class="info">🔧 Creating templates page...</div>';
        $page_id = wp_insert_post(array(
            'post_title' => 'ChatGABI Templates',
            'post_content' => 'AI-powered business templates for African entrepreneurs.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'templates'
        ));
        
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-templates.php');
            echo '<div class="success">✅ Templates page created (ID: ' . $page_id . ')</div>';
            $fixes_applied[] = 'Created templates page';
            $templates_page = get_post($page_id);
        } else {
            echo '<div class="error">❌ Failed to create templates page</div>';
            $errors_encountered[] = 'Failed to create templates page';
        }
    } else {
        echo '<div class="success">✅ Templates page already exists (ID: ' . $templates_page->ID . ')</div>';
        
        // Ensure correct template is set
        $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
        if ($page_template !== 'page-templates.php') {
            update_post_meta($templates_page->ID, '_wp_page_template', 'page-templates.php');
            echo '<div class="success">✅ Updated page template to page-templates.php</div>';
            $fixes_applied[] = 'Updated page template assignment';
        }
    }
    
    // Verify page template file exists
    $theme_dir = get_template_directory();
    $template_file = $theme_dir . '/page-templates.php';
    
    if (file_exists($template_file)) {
        echo '<div class="success">✅ Page template file exists</div>';
    } else {
        echo '<div class="error">❌ Page template file missing</div>';
        $errors_encountered[] = 'Page template file missing';
    }
    
    // Test page URL
    if ($templates_page) {
        $page_url = get_permalink($templates_page->ID);
        echo '<div class="success">✅ Templates page URL: <a href="' . $page_url . '" target="_blank">' . $page_url . '</a></div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Error fixing templates page: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Templates page fix error: ' . $e->getMessage();
}

// Fix 2: Admin Dashboard Template Management
echo '<h2>⚙️ Fix 2: Admin Dashboard Template Management</h2>';

try {
    // Check if admin page function exists and enhance it
    if (function_exists('chatgabi_templates_admin_page')) {
        echo '<div class="success">✅ Admin page function exists</div>';
        
        // Add missing CRUD functions if they don't exist
        $crud_functions_to_add = array();
        
        if (!function_exists('chatgabi_delete_template')) {
            $crud_functions_to_add[] = 'chatgabi_delete_template';
        }
        
        if (!function_exists('chatgabi_duplicate_template')) {
            $crud_functions_to_add[] = 'chatgabi_duplicate_template';
        }
        
        if (!function_exists('chatgabi_get_template_by_id')) {
            $crud_functions_to_add[] = 'chatgabi_get_template_by_id';
        }
        
        if (!empty($crud_functions_to_add)) {
            echo '<div class="info">🔧 Adding missing CRUD functions...</div>';
            
            // Create the missing functions file
            $functions_file = $theme_dir . '/inc/template-crud-functions.php';
            if (!file_exists($functions_file)) {
                $crud_code = "<?php\n";
                $crud_code .= "/**\n * Template CRUD Functions\n * Auto-generated by fix script\n */\n\n";
                
                // Add delete function
                if (in_array('chatgabi_delete_template', $crud_functions_to_add)) {
                    $crud_code .= "function chatgabi_delete_template(\$template_id) {\n";
                    $crud_code .= "    global \$wpdb;\n";
                    $crud_code .= "    \$table = \$wpdb->prefix . 'chatgabi_prompt_templates';\n";
                    $crud_code .= "    return \$wpdb->delete(\$table, array('id' => \$template_id), array('%d'));\n";
                    $crud_code .= "}\n\n";
                }
                
                // Add duplicate function
                if (in_array('chatgabi_duplicate_template', $crud_functions_to_add)) {
                    $crud_code .= "function chatgabi_duplicate_template(\$template_id) {\n";
                    $crud_code .= "    global \$wpdb;\n";
                    $crud_code .= "    \$table = \$wpdb->prefix . 'chatgabi_prompt_templates';\n";
                    $crud_code .= "    \$template = \$wpdb->get_row(\$wpdb->prepare(\"SELECT * FROM \$table WHERE id = %d\", \$template_id));\n";
                    $crud_code .= "    if (\$template) {\n";
                    $crud_code .= "        unset(\$template->id);\n";
                    $crud_code .= "        \$template->title .= ' (Copy)';\n";
                    $crud_code .= "        \$template->created_at = current_time('mysql');\n";
                    $crud_code .= "        return \$wpdb->insert(\$table, (array)\$template);\n";
                    $crud_code .= "    }\n";
                    $crud_code .= "    return false;\n";
                    $crud_code .= "}\n\n";
                }
                
                // Add get by ID function
                if (in_array('chatgabi_get_template_by_id', $crud_functions_to_add)) {
                    $crud_code .= "function chatgabi_get_template_by_id(\$template_id) {\n";
                    $crud_code .= "    global \$wpdb;\n";
                    $crud_code .= "    \$table = \$wpdb->prefix . 'chatgabi_prompt_templates';\n";
                    $crud_code .= "    return \$wpdb->get_row(\$wpdb->prepare(\"SELECT * FROM \$table WHERE id = %d\", \$template_id));\n";
                    $crud_code .= "}\n\n";
                }
                
                file_put_contents($functions_file, $crud_code);
                echo '<div class="success">✅ Created CRUD functions file</div>';
                $fixes_applied[] = 'Created template CRUD functions';
                
                // Include the file
                require_once($functions_file);
            }
        } else {
            echo '<div class="success">✅ All CRUD functions exist</div>';
        }
        
    } else {
        echo '<div class="error">❌ Admin page function missing</div>';
        $errors_encountered[] = 'Admin page function missing';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Error fixing admin dashboard: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Admin dashboard fix error: ' . $e->getMessage();
}

// Fix 3: User Dashboard Template Visibility
echo '<h2>👤 Fix 3: User Dashboard Template Visibility</h2>';

try {
    // Check and create user dashboard functions
    if (!function_exists('chatgabi_display_user_templates')) {
        echo '<div class="info">🔧 Creating user dashboard template functions...</div>';
        
        $user_functions_file = $theme_dir . '/inc/user-dashboard-templates.php';
        if (!file_exists($user_functions_file)) {
            $user_code = "<?php\n";
            $user_code .= "/**\n * User Dashboard Template Functions\n * Auto-generated by fix script\n */\n\n";
            
            $user_code .= "function chatgabi_display_user_templates(\$user_id = null) {\n";
            $user_code .= "    if (!\$user_id) \$user_id = get_current_user_id();\n";
            $user_code .= "    global \$wpdb;\n";
            $user_code .= "    \$table = \$wpdb->prefix . 'chatgabi_prompt_templates';\n";
            $user_code .= "    \$templates = \$wpdb->get_results(\$wpdb->prepare(\n";
            $user_code .= "        \"SELECT * FROM \$table WHERE user_id = %d OR is_public = 1 ORDER BY created_at DESC\",\n";
            $user_code .= "        \$user_id\n";
            $user_code .= "    ));\n";
            $user_code .= "    return \$templates;\n";
            $user_code .= "}\n\n";
            
            $user_code .= "function chatgabi_get_user_template_categories(\$user_id = null) {\n";
            $user_code .= "    if (function_exists('chatgabi_get_template_categories')) {\n";
            $user_code .= "        return chatgabi_get_template_categories();\n";
            $user_code .= "    }\n";
            $user_code .= "    return array();\n";
            $user_code .= "}\n\n";
            
            $user_code .= "function chatgabi_render_user_templates_widget() {\n";
            $user_code .= "    \$templates = chatgabi_display_user_templates();\n";
            $user_code .= "    \$categories = chatgabi_get_user_template_categories();\n";
            $user_code .= "    \n";
            $user_code .= "    echo '<div class=\"user-templates-widget\">';\n";
            $user_code .= "    echo '<h3>My Templates</h3>';\n";
            $user_code .= "    \n";
            $user_code .= "    if (!empty(\$templates)) {\n";
            $user_code .= "        echo '<div class=\"templates-list\">';\n";
            $user_code .= "        foreach (\$templates as \$template) {\n";
            $user_code .= "            echo '<div class=\"template-item\">';\n";
            $user_code .= "            echo '<h4>' . esc_html(\$template->title) . '</h4>';\n";
            $user_code .= "            if (\$template->description) {\n";
            $user_code .= "                echo '<p>' . esc_html(wp_trim_words(\$template->description, 15)) . '</p>';\n";
            $user_code .= "            }\n";
            $user_code .= "            echo '<div class=\"template-actions\">';\n";
            $user_code .= "            echo '<a href=\"#\" class=\"use-template\" data-id=\"' . \$template->id . '\">Use Template</a>';\n";
            $user_code .= "            if (\$template->user_id == get_current_user_id()) {\n";
            $user_code .= "                echo ' | <a href=\"#\" class=\"edit-template\" data-id=\"' . \$template->id . '\">Edit</a>';\n";
            $user_code .= "                echo ' | <a href=\"#\" class=\"delete-template\" data-id=\"' . \$template->id . '\">Delete</a>';\n";
            $user_code .= "            }\n";
            $user_code .= "            echo '</div>';\n";
            $user_code .= "            echo '</div>';\n";
            $user_code .= "        }\n";
            $user_code .= "        echo '</div>';\n";
            $user_code .= "    } else {\n";
            $user_code .= "        echo '<p>No templates found. <a href=\"' . home_url('/templates') . '\">Browse Templates</a></p>';\n";
            $user_code .= "    }\n";
            $user_code .= "    \n";
            $user_code .= "    echo '</div>';\n";
            $user_code .= "}\n";
            
            file_put_contents($user_functions_file, $user_code);
            echo '<div class="success">✅ Created user dashboard template functions</div>';
            $fixes_applied[] = 'Created user dashboard template functions';
            
            // Include the file
            require_once($user_functions_file);
        }
    } else {
        echo '<div class="success">✅ User dashboard functions exist</div>';
    }
    
    // Check template data availability
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $public_templates = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_public = 1");
    
    if ($public_templates > 0) {
        echo '<div class="success">✅ Public templates available (' . $public_templates . ' templates)</div>';
    } else {
        echo '<div class="warning">⚠️ No public templates found</div>';
        
        // Create some default public templates
        if (function_exists('chatgabi_initialize_default_templates')) {
            echo '<div class="info">🔧 Creating default public templates...</div>';
            chatgabi_initialize_default_templates();
            $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_public = 1");
            if ($new_count > 0) {
                echo '<div class="success">✅ Created ' . $new_count . ' default public templates</div>';
                $fixes_applied[] = 'Created default public templates';
            }
        }
    }
    
    // Check categories
    if (function_exists('chatgabi_get_template_categories')) {
        $categories = chatgabi_get_template_categories();
        if (!empty($categories)) {
            echo '<div class="success">✅ Template categories available (' . count($categories) . ' categories)</div>';
        } else {
            echo '<div class="warning">⚠️ No template categories found</div>';
            
            // Create default categories
            if (function_exists('chatgabi_create_default_template_categories')) {
                echo '<div class="info">🔧 Creating default template categories...</div>';
                chatgabi_create_default_template_categories();
                $new_categories = chatgabi_get_template_categories();
                if (!empty($new_categories)) {
                    echo '<div class="success">✅ Created ' . count($new_categories) . ' default categories</div>';
                    $fixes_applied[] = 'Created default template categories';
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Error fixing user dashboard: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'User dashboard fix error: ' . $e->getMessage();
}

// Summary
echo '<h2>📋 Fix Summary</h2>';

if (empty($errors_encountered)) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL FIXES COMPLETED SUCCESSFULLY!</h3>';
    echo '<p><strong>✅ All three template issues have been resolved!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues Remain</h3>';
    echo '<ul>';
    foreach ($errors_encountered as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Fixes Applied: ' . count($fixes_applied) . '</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Test Actions
echo '<h2>🧪 Test Your Fixes</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<button class="fix-button" onclick="window.open(\'' . get_permalink($templates_page->ID) . '\', \'_blank\')">🎯 Test Templates Page</button>';
}

echo '<button class="fix-button" onclick="window.open(\'' . admin_url('admin.php?page=chatgabi-templates') . '\', \'_blank\')">⚙️ Test Admin Templates</button>';
echo '<button class="fix-button" onclick="window.location.href=\'diagnose-three-template-issues.php\'">🔍 Re-run Diagnosis</button>';
echo '<button class="fix-button" onclick="window.location.reload()">🔄 Re-run Fixes</button>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Fix completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

<?php
/**
 * AI Feedback Loops Integration for ChatGABI
 * 
 * Enhances the existing feedback system to capture user interaction patterns,
 * AI response quality metrics, and feeds back into prompt optimization for
 * the African Context Engine.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize AI Feedback Loops system
 */
function chatgabi_init_ai_feedback_loops() {
    // Create enhanced feedback tables
    chatgabi_create_feedback_loops_tables();
    
    // Add AJAX handlers
    add_action('wp_ajax_chatgabi_record_interaction_pattern', 'chatgabi_handle_interaction_pattern');
    add_action('wp_ajax_nopriv_chatgabi_record_interaction_pattern', 'chatgabi_handle_interaction_pattern');
    add_action('wp_ajax_chatgabi_get_feedback_insights', 'chatgabi_handle_get_feedback_insights');
    add_action('wp_ajax_chatgabi_export_training_data', 'chatgabi_handle_export_training_data');
    
    // Schedule feedback analysis
    if (!wp_next_scheduled('chatgabi_analyze_feedback_patterns')) {
        wp_schedule_event(time(), 'hourly', 'chatgabi_analyze_feedback_patterns');
    }
    
    // Hook into existing chat completion event
    add_action('chatgabi_response_completed', 'chatgabi_trigger_feedback_collection', 10, 3);
}
add_action('init', 'chatgabi_init_ai_feedback_loops');

/**
 * Create enhanced feedback tables
 */
function chatgabi_create_feedback_loops_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // Enhanced feedback patterns table
    $patterns_table = $wpdb->prefix . 'chatgabi_feedback_patterns';
    $patterns_sql = "CREATE TABLE IF NOT EXISTS {$patterns_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        session_id varchar(100) NOT NULL,
        interaction_type varchar(50) NOT NULL,
        pattern_data longtext NOT NULL,
        quality_score decimal(3,2) DEFAULT 0.00,
        response_relevance decimal(3,2) DEFAULT 0.00,
        user_satisfaction decimal(3,2) DEFAULT 0.00,
        context_accuracy decimal(3,2) DEFAULT 0.00,
        prompt_effectiveness decimal(3,2) DEFAULT 0.00,
        user_country varchar(5),
        user_sector varchar(100),
        business_stage varchar(50),
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY session_id (session_id),
        KEY interaction_type (interaction_type),
        KEY created_at (created_at)
    ) {$charset_collate};";
    
    // ML training data export table
    $training_table = $wpdb->prefix . 'chatgabi_ml_training_data';
    $training_sql = "CREATE TABLE IF NOT EXISTS {$training_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        feedback_id bigint(20) NOT NULL,
        training_type varchar(50) NOT NULL,
        input_data longtext NOT NULL,
        expected_output longtext NOT NULL,
        quality_metrics longtext,
        export_status varchar(20) DEFAULT 'pending',
        exported_at datetime NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY feedback_id (feedback_id),
        KEY training_type (training_type),
        KEY export_status (export_status)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($patterns_sql);
    dbDelta($training_sql);
}

/**
 * Trigger automated feedback collection after AI response
 */
function chatgabi_trigger_feedback_collection($user_id, $response_data, $context) {
    // Record interaction pattern automatically
    $pattern_data = array(
        'response_time' => $response_data['response_time'] ?? 0,
        'token_count' => $response_data['tokens_used'] ?? 0,
        'context_type' => $context['type'] ?? 'general',
        'prompt_length' => strlen($context['user_message'] ?? ''),
        'ai_confidence' => $response_data['confidence'] ?? 0.5,
        'context_injection_success' => $context['african_context_injected'] ?? false
    );
    
    chatgabi_record_interaction_pattern($user_id, 'auto_response', $pattern_data);
    
    // Trigger frontend feedback interface
    wp_localize_script('chatgabi-feedback-loops', 'chatgabiFeedback', array(
        'showFeedbackInterface' => true,
        'responseId' => $response_data['response_id'] ?? '',
        'sessionId' => $context['session_id'] ?? '',
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('chatgabi_feedback_nonce')
    ));
}

/**
 * Record user interaction pattern
 */
function chatgabi_record_interaction_pattern($user_id, $interaction_type, $pattern_data, $session_id = null) {
    global $wpdb;
    
    if (!$session_id) {
        $session_id = wp_generate_uuid4();
    }
    
    // Get user profile for context
    $user_profile = chatgabi_get_user_profile($user_id);
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback_patterns';
    
    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'session_id' => $session_id,
            'interaction_type' => sanitize_text_field($interaction_type),
            'pattern_data' => wp_json_encode($pattern_data),
            'user_country' => $user_profile->target_country ?? 'GH',
            'user_sector' => $user_profile->primary_industry ?? 'general',
            'business_stage' => $user_profile->business_stage ?? 'idea'
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%s')
    );
    
    if ($result === false) {
        error_log('ChatGABI: Failed to record interaction pattern - ' . $wpdb->last_error);
        return false;
    }
    
    return $wpdb->insert_id;
}

/**
 * AJAX handler for recording interaction patterns
 */
function chatgabi_handle_interaction_pattern() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'chatgabi_feedback_nonce')) {
        wp_die('Security check failed');
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }
    
    $interaction_type = sanitize_text_field($_POST['interaction_type'] ?? '');
    $pattern_data = json_decode(stripslashes($_POST['pattern_data'] ?? '{}'), true);
    $session_id = sanitize_text_field($_POST['session_id'] ?? '');
    
    $result = chatgabi_record_interaction_pattern($user_id, $interaction_type, $pattern_data, $session_id);
    
    if ($result) {
        wp_send_json_success(array(
            'pattern_id' => $result,
            'message' => 'Interaction pattern recorded successfully'
        ));
    } else {
        wp_send_json_error('Failed to record interaction pattern');
    }
}

/**
 * Analyze feedback patterns for prompt optimization
 */
function chatgabi_analyze_feedback_patterns() {
    global $wpdb;
    
    $patterns_table = $wpdb->prefix . 'chatgabi_feedback_patterns';
    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
    
    // Get recent patterns for analysis
    $recent_patterns = $wpdb->get_results($wpdb->prepare("
        SELECT fp.*, f.rating_score, f.category_helpfulness, f.category_accuracy, f.category_relevance
        FROM {$patterns_table} fp
        LEFT JOIN {$feedback_table} f ON fp.session_id = f.session_id
        WHERE fp.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY fp.created_at DESC
        LIMIT 100
    "));
    
    if (empty($recent_patterns)) {
        return;
    }
    
    // Analyze patterns by country and sector
    $analysis_results = array();
    
    foreach ($recent_patterns as $pattern) {
        $key = $pattern->user_country . '_' . $pattern->user_sector;
        
        if (!isset($analysis_results[$key])) {
            $analysis_results[$key] = array(
                'country' => $pattern->user_country,
                'sector' => $pattern->user_sector,
                'total_interactions' => 0,
                'avg_satisfaction' => 0,
                'avg_response_time' => 0,
                'context_success_rate' => 0,
                'improvement_suggestions' => array()
            );
        }
        
        $analysis_results[$key]['total_interactions']++;
        
        $pattern_data = json_decode($pattern->pattern_data, true);
        if ($pattern_data) {
            $analysis_results[$key]['avg_response_time'] += $pattern_data['response_time'] ?? 0;
            if ($pattern_data['context_injection_success'] ?? false) {
                $analysis_results[$key]['context_success_rate']++;
            }
        }
        
        if ($pattern->rating_score) {
            $analysis_results[$key]['avg_satisfaction'] += $pattern->rating_score;
        }
    }
    
    // Calculate averages and store insights
    foreach ($analysis_results as $key => $data) {
        if ($data['total_interactions'] > 0) {
            $data['avg_response_time'] /= $data['total_interactions'];
            $data['avg_satisfaction'] /= $data['total_interactions'];
            $data['context_success_rate'] = ($data['context_success_rate'] / $data['total_interactions']) * 100;
            
            // Generate improvement suggestions
            if ($data['avg_satisfaction'] < 3.5) {
                $data['improvement_suggestions'][] = 'Low satisfaction - review prompt templates';
            }
            if ($data['avg_response_time'] > 5000) {
                $data['improvement_suggestions'][] = 'High response time - optimize context injection';
            }
            if ($data['context_success_rate'] < 80) {
                $data['improvement_suggestions'][] = 'Low context success - review African Context Engine';
            }
            
            // Cache analysis results
            chatgabi_cache_set('feedback_analysis_' . $key, $data, 3600);
        }
    }
    
    // Log analysis completion
    error_log('ChatGABI: Feedback pattern analysis completed for ' . count($analysis_results) . ' segments');
}
add_action('chatgabi_analyze_feedback_patterns', 'chatgabi_analyze_feedback_patterns');

/**
 * Get feedback insights for admin dashboard
 */
function chatgabi_handle_get_feedback_insights() {
    // Verify admin permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }
    
    $country = sanitize_text_field($_GET['country'] ?? '');
    $sector = sanitize_text_field($_GET['sector'] ?? '');
    $timeframe = sanitize_text_field($_GET['timeframe'] ?? '24h');
    
    $insights = chatgabi_get_feedback_insights($country, $sector, $timeframe);
    
    wp_send_json_success($insights);
}

/**
 * Get feedback insights data
 */
function chatgabi_get_feedback_insights($country = '', $sector = '', $timeframe = '24h') {
    global $wpdb;
    
    // Convert timeframe to SQL interval
    $interval_map = array(
        '1h' => 'INTERVAL 1 HOUR',
        '24h' => 'INTERVAL 24 HOUR',
        '7d' => 'INTERVAL 7 DAY',
        '30d' => 'INTERVAL 30 DAY'
    );
    
    $interval = $interval_map[$timeframe] ?? 'INTERVAL 24 HOUR';
    
    $patterns_table = $wpdb->prefix . 'chatgabi_feedback_patterns';
    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
    
    // Build WHERE clause
    $where_conditions = array("fp.created_at >= DATE_SUB(NOW(), {$interval})");
    $where_params = array();
    
    if ($country) {
        $where_conditions[] = "fp.user_country = %s";
        $where_params[] = $country;
    }
    
    if ($sector) {
        $where_conditions[] = "fp.user_sector = %s";
        $where_params[] = $sector;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get aggregated insights
    $query = "
        SELECT 
            COUNT(*) as total_interactions,
            AVG(f.rating_score) as avg_rating,
            AVG(f.category_helpfulness) as avg_helpfulness,
            AVG(f.category_accuracy) as avg_accuracy,
            AVG(f.category_relevance) as avg_relevance,
            AVG(JSON_EXTRACT(fp.pattern_data, '$.response_time')) as avg_response_time,
            SUM(CASE WHEN JSON_EXTRACT(fp.pattern_data, '$.context_injection_success') = true THEN 1 ELSE 0 END) as context_successes
        FROM {$patterns_table} fp
        LEFT JOIN {$feedback_table} f ON fp.session_id = f.session_id
        WHERE {$where_clause}
    ";
    
    if (!empty($where_params)) {
        $insights = $wpdb->get_row($wpdb->prepare($query, $where_params));
    } else {
        $insights = $wpdb->get_row($query);
    }
    
    // Calculate derived metrics
    if ($insights && $insights->total_interactions > 0) {
        $insights->context_success_rate = ($insights->context_successes / $insights->total_interactions) * 100;
        $insights->satisfaction_score = $insights->avg_rating * 20; // Convert to percentage
    }
    
    return $insights;
}

/**
 * Export ML training data
 */
function chatgabi_handle_export_training_data() {
    // Verify admin permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $format = sanitize_text_field($_POST['format'] ?? 'json');
    $date_from = sanitize_text_field($_POST['date_from'] ?? '');
    $date_to = sanitize_text_field($_POST['date_to'] ?? '');
    $min_rating = intval($_POST['min_rating'] ?? 4);

    $training_data = chatgabi_prepare_training_data($date_from, $date_to, $min_rating);

    if (empty($training_data)) {
        wp_send_json_error('No training data available for the specified criteria');
        return;
    }

    // Generate export file
    $export_result = chatgabi_export_training_data($training_data, $format);

    if ($export_result) {
        wp_send_json_success(array(
            'download_url' => $export_result['url'],
            'file_size' => $export_result['size'],
            'record_count' => count($training_data),
            'message' => 'Training data exported successfully'
        ));
    } else {
        wp_send_json_error('Failed to export training data');
    }
}

/**
 * Prepare ML training data from feedback
 */
function chatgabi_prepare_training_data($date_from = '', $date_to = '', $min_rating = 4) {
    global $wpdb;

    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
    $patterns_table = $wpdb->prefix . 'chatgabi_feedback_patterns';
    $conversations_table = $wpdb->prefix . 'chatgabi_conversations';

    // Build date filter
    $date_filter = '';
    $params = array($min_rating);

    if ($date_from && $date_to) {
        $date_filter = "AND f.created_at BETWEEN %s AND %s";
        $params[] = $date_from;
        $params[] = $date_to;
    }

    $query = "
        SELECT
            f.id as feedback_id,
            f.rating_score,
            f.feedback_text,
            f.category_helpfulness,
            f.category_accuracy,
            f.category_relevance,
            f.category_clarity,
            f.user_country,
            f.user_sector,
            c.message_text as user_input,
            c.response_text as ai_output,
            fp.pattern_data,
            f.created_at
        FROM {$feedback_table} f
        LEFT JOIN {$conversations_table} c ON f.conversation_id = c.id
        LEFT JOIN {$patterns_table} fp ON f.session_id = fp.session_id
        WHERE f.rating_score >= %d
        AND f.is_training_data = 1
        AND f.training_consent = 1
        {$date_filter}
        ORDER BY f.created_at DESC
        LIMIT 1000
    ";

    $results = $wpdb->get_results($wpdb->prepare($query, $params));

    $training_data = array();

    foreach ($results as $row) {
        $pattern_data = json_decode($row->pattern_data, true) ?? array();

        $training_record = array(
            'input' => array(
                'user_message' => $row->user_input,
                'context' => array(
                    'country' => $row->user_country,
                    'sector' => $row->user_sector,
                    'business_context' => 'african_sme'
                ),
                'metadata' => array(
                    'response_time' => $pattern_data['response_time'] ?? 0,
                    'token_count' => $pattern_data['token_count'] ?? 0
                )
            ),
            'output' => $row->ai_output,
            'quality_metrics' => array(
                'overall_rating' => $row->rating_score,
                'helpfulness' => $row->category_helpfulness,
                'accuracy' => $row->category_accuracy,
                'relevance' => $row->category_relevance,
                'clarity' => $row->category_clarity
            ),
            'feedback_text' => $row->feedback_text,
            'timestamp' => $row->created_at
        );

        $training_data[] = $training_record;
    }

    return $training_data;
}

/**
 * Export training data to file
 */
function chatgabi_export_training_data($training_data, $format = 'json') {
    $upload_dir = wp_upload_dir();
    $export_dir = $upload_dir['basedir'] . '/chatgabi-exports/';

    // Create export directory if it doesn't exist
    if (!file_exists($export_dir)) {
        wp_mkdir_p($export_dir);
    }

    $timestamp = date('Y-m-d_H-i-s');
    $filename = "chatgabi_training_data_{$timestamp}";

    switch ($format) {
        case 'json':
            $filename .= '.json';
            $file_path = $export_dir . $filename;
            $content = wp_json_encode($training_data, JSON_PRETTY_PRINT);
            break;

        case 'csv':
            $filename .= '.csv';
            $file_path = $export_dir . $filename;
            $content = chatgabi_convert_training_data_to_csv($training_data);
            break;

        case 'jsonl':
            $filename .= '.jsonl';
            $file_path = $export_dir . $filename;
            $content = '';
            foreach ($training_data as $record) {
                $content .= wp_json_encode($record) . "\n";
            }
            break;

        default:
            return false;
    }

    // Write file
    $result = file_put_contents($file_path, $content);

    if ($result === false) {
        return false;
    }

    // Return download info
    return array(
        'url' => $upload_dir['baseurl'] . '/chatgabi-exports/' . $filename,
        'path' => $file_path,
        'size' => filesize($file_path)
    );
}

/**
 * Convert training data to CSV format
 */
function chatgabi_convert_training_data_to_csv($training_data) {
    $csv_content = "user_message,ai_response,country,sector,overall_rating,helpfulness,accuracy,relevance,clarity,feedback_text,timestamp\n";

    foreach ($training_data as $record) {
        $row = array(
            '"' . str_replace('"', '""', $record['input']['user_message']) . '"',
            '"' . str_replace('"', '""', $record['output']) . '"',
            $record['input']['context']['country'],
            $record['input']['context']['sector'],
            $record['quality_metrics']['overall_rating'],
            $record['quality_metrics']['helpfulness'],
            $record['quality_metrics']['accuracy'],
            $record['quality_metrics']['relevance'],
            $record['quality_metrics']['clarity'],
            '"' . str_replace('"', '""', $record['feedback_text']) . '"',
            $record['timestamp']
        );

        $csv_content .= implode(',', $row) . "\n";
    }

    return $csv_content;
}

/**
 * Enhance existing feedback system with AI insights
 */
function chatgabi_enhance_feedback_with_ai_insights($feedback_id) {
    global $wpdb;

    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
    $patterns_table = $wpdb->prefix . 'chatgabi_feedback_patterns';

    // Get feedback data
    $feedback = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$feedback_table} WHERE id = %d",
        $feedback_id
    ));

    if (!$feedback) {
        return false;
    }

    // Calculate AI insights
    $insights = array(
        'response_quality_score' => chatgabi_calculate_response_quality($feedback),
        'context_relevance_score' => chatgabi_calculate_context_relevance($feedback),
        'user_satisfaction_prediction' => chatgabi_predict_user_satisfaction($feedback),
        'improvement_suggestions' => chatgabi_generate_improvement_suggestions($feedback)
    );

    // Update feedback patterns with AI insights
    $wpdb->update(
        $patterns_table,
        array(
            'quality_score' => $insights['response_quality_score'],
            'response_relevance' => $insights['context_relevance_score'],
            'user_satisfaction' => $insights['user_satisfaction_prediction']
        ),
        array('session_id' => $feedback->session_id),
        array('%f', '%f', '%f'),
        array('%s')
    );

    return $insights;
}

/**
 * Calculate response quality score
 */
function chatgabi_calculate_response_quality($feedback) {
    // Weighted average of category ratings
    $weights = array(
        'helpfulness' => 0.3,
        'accuracy' => 0.3,
        'relevance' => 0.25,
        'clarity' => 0.15
    );

    $score = 0;
    $total_weight = 0;

    foreach ($weights as $category => $weight) {
        $field = 'category_' . $category;
        if (isset($feedback->$field) && $feedback->$field > 0) {
            $score += ($feedback->$field * $weight);
            $total_weight += $weight;
        }
    }

    return $total_weight > 0 ? ($score / $total_weight) : 0;
}

/**
 * Calculate context relevance score
 */
function chatgabi_calculate_context_relevance($feedback) {
    // Base score from relevance rating
    $base_score = $feedback->category_relevance ?? 0;

    // Adjust based on country/sector context
    $context_bonus = 0;
    if ($feedback->user_country && $feedback->user_sector) {
        $context_bonus = 0.2; // 20% bonus for having context
    }

    return min(5.0, $base_score + $context_bonus);
}

/**
 * Predict user satisfaction
 */
function chatgabi_predict_user_satisfaction($feedback) {
    // Simple prediction model based on ratings
    $factors = array(
        'overall_rating' => $feedback->rating_score * 0.4,
        'helpfulness' => ($feedback->category_helpfulness ?? 0) * 0.25,
        'accuracy' => ($feedback->category_accuracy ?? 0) * 0.2,
        'clarity' => ($feedback->category_clarity ?? 0) * 0.15
    );

    $predicted_score = array_sum($factors);

    return min(5.0, max(1.0, $predicted_score));
}

/**
 * Generate improvement suggestions
 */
function chatgabi_generate_improvement_suggestions($feedback) {
    $suggestions = array();

    if (($feedback->category_accuracy ?? 0) < 3) {
        $suggestions[] = 'Improve factual accuracy for ' . $feedback->user_sector . ' sector';
    }

    if (($feedback->category_relevance ?? 0) < 3) {
        $suggestions[] = 'Enhance African context for ' . $feedback->user_country . ' market';
    }

    if (($feedback->category_helpfulness ?? 0) < 3) {
        $suggestions[] = 'Provide more actionable advice for business planning';
    }

    if (($feedback->category_clarity ?? 0) < 3) {
        $suggestions[] = 'Simplify language and structure for better clarity';
    }

    return $suggestions;
}

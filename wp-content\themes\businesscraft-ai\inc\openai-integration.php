<?php
/**
 * OpenAI Integration for BusinessCraft AI
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Process OpenAI request with African context and business intelligence
 * Enhanced with token optimization and smart context management
 */
function businesscraft_ai_process_openai_request($message, $language = 'en', $context = 'general', $user_id = null) {
    $start_time = microtime(true);

    // Load Redis caching system
    require_once get_template_directory() . '/inc/redis-caching.php';

    // Generate cache key for this request
    $cache_key = 'openai_response:' . md5($message . $language . $context);

    // Try to get cached response first
    $cached_response = chatgabi_cache_get($cache_key);
    if ($cached_response !== false) {
        // Add cache hit metadata
        $cached_response['cached'] = true;
        $cached_response['cache_time'] = microtime(true) - $start_time;

        // Log cache hit
        error_log('ChatGABI: Cache hit for OpenAI request');

        return $cached_response;
    }

    // Use secure API key manager
    require_once get_template_directory() . '/inc/secure-api-key-manager.php';

    $api_key = defined('BUSINESSCRAFT_AI_OPENAI_API_KEY') ? BUSINESSCRAFT_AI_OPENAI_API_KEY : businesscraft_ai_get_encrypted_api_key('openai');

    if (empty($api_key)) {
        businesscraft_ai_log_security_event('api_key_missing', array(
            'context' => 'openai_request',
            'user_id' => $user_id,
            'timestamp' => current_time('mysql')
        ));

        return new WP_Error(
            'missing_api_key',
            __('OpenAI API key not configured', 'businesscraft-ai'),
            array('status' => 500)
        );
    }

    // Monitor API key usage
    businesscraft_ai_monitor_api_key_usage('openai', 'chat_request');

    // Load advanced rate limiting
    require_once get_template_directory() . '/inc/advanced-rate-limiting.php';

    // Estimate tokens for rate limiting
    $estimated_tokens = businesscraft_ai_estimate_tokens($message) + 400; // Add response estimate

    // Check advanced rate limits
    $rate_limit_check = chatgabi_check_rate_limit($user_id, 'chat', $estimated_tokens);

    if (!$rate_limit_check['allowed']) {
        return new WP_Error(
            'rate_limit_exceeded',
            sprintf(
                __('Rate limit exceeded (%s). Please try again in %d seconds.', 'businesscraft-ai'),
                $rate_limit_check['reason'],
                $rate_limit_check['retry_after']
            ),
            array(
                'status' => 429,
                'retry_after' => $rate_limit_check['retry_after'],
                'rate_limit_info' => $rate_limit_check
            )
        );
    }

    // Initialize user ID if not provided
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    // Get session ID for tracking
    $session_id = function_exists('chatgabi_get_session_id') ? chatgabi_get_session_id() : session_id();

    // Pre-estimate tokens for credit checking
    if (function_exists('chatgabi_get_prompt_token_estimate')) {
        $token_estimation = chatgabi_get_prompt_token_estimate($message);
        $estimated_credits = $token_estimation['estimated_credits'];

        // Check if user has sufficient credits
        $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
        if ($current_credits < $estimated_credits) {
            return new WP_Error(
                'insufficient_credits',
                sprintf(__('Insufficient credits. Required: %s, Available: %s', 'chatgabi'),
                    number_format($estimated_credits, 2),
                    number_format($current_credits, 2)
                ),
                array('status' => 402, 'credits_required' => $estimated_credits, 'credits_available' => $current_credits)
            );
        }
    }

    // Initialize optimization engine
    $token_optimizer = new BusinessCraft_Token_Optimizer();

    // Initialize African context and business intelligence engines
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $business_intelligence = new BusinessCraft_Business_Intelligence_Engine();

        // Get user's country and business context
        $user_country = businesscraft_ai_get_user_country();
        $business_type = get_user_meta($user_id, 'businesscraft_ai_business_type', true) ?: 'sme';
        $industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: null;
    }

    // Determine model based on user tier
    $model = businesscraft_ai_get_user_model($user_id);

    // Prepare context data for optimization
    $context_data = array(
        'country' => $user_country ?? 'GH',
        'business_type' => $business_type ?? 'sme',
        'industry' => $industry,
        'language' => $language,
        'context' => $context
    );

    // Add African context data if available
    if (isset($african_context)) {
        $country_context = $african_context->get_country_context($user_country ?? 'GH');
        if ($country_context) {
            $context_data = array_merge($context_data, $country_context);
        }
    }

    // Build enhanced prompt with sector context and logging
    $enhanced_prompt = businesscraft_ai_build_enhanced_prompt($message, $language, $context, $user_country ?? 'GH', $business_type ?? 'sme', $industry, $user_id);

    // Optimize prompt using token optimizer
    $optimized_prompt = $token_optimizer->optimize_prompt($enhanced_prompt, $context_data, $model);

    // Get optimized token limits for the model - ENFORCE 400 TOKEN LIMIT
    $token_limits = $token_optimizer->get_model_limits($model);
    $max_tokens = min($token_limits['optimal_response'] ?? 400, 400); // Strict 400-token compliance

    // Log token limit enforcement
    if (($token_limits['optimal_response'] ?? 0) > 400) {
        businesscraft_ai_log_security_event('token_limit_enforced', array(
            'requested_tokens' => $token_limits['optimal_response'] ?? 0,
            'enforced_tokens' => 400,
            'user_id' => $user_id,
            'model' => $model
        ));
    }

    // Check rate limiting
    $rate_limit_key = 'bcai_rate_limit_' . $user_id;
    $requests_made = get_transient($rate_limit_key);

    if ($requests_made && $requests_made >= 10) { // 10 requests per minute
        return new WP_Error(
            'rate_limit_exceeded',
            __('Rate limit exceeded. Please wait before making another request.', 'businesscraft-ai'),
            array('status' => 429)
        );
    }

    // Prepare the API request with optimized settings
    $request_data = array(
        'model' => $model,
        'messages' => array(
            array(
                'role' => 'system',
                'content' => businesscraft_ai_get_enhanced_system_prompt($language, $context, $user_country ?? 'GH', $business_type ?? 'sme', $industry)
            ),
            array(
                'role' => 'user',
                'content' => $optimized_prompt
            )
        ),
        'max_tokens' => $max_tokens,
        'temperature' => 0.7,
        'top_p' => 1,
        'frequency_penalty' => 0,
        'presence_penalty' => 0,
    );

    // Make the API call
    $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json',
        ),
        'body' => json_encode($request_data),
        'timeout' => 30,
    ));

    if (is_wp_error($response)) {
        return new WP_Error(
            'api_request_failed',
            __('Failed to connect to OpenAI API', 'businesscraft-ai'),
            array('status' => 500)
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    $response_data = json_decode($response_body, true);

    if ($response_code !== 200) {
        $error_message = isset($response_data['error']['message'])
            ? $response_data['error']['message']
            : __('OpenAI API error', 'businesscraft-ai');

        return new WP_Error(
            'openai_api_error',
            $error_message,
            array('status' => $response_code)
        );
    }

    if (!isset($response_data['choices'][0]['message']['content'])) {
        return new WP_Error(
            'invalid_response',
            __('Invalid response from OpenAI API', 'businesscraft-ai'),
            array('status' => 500)
        );
    }

    $ai_response = $response_data['choices'][0]['message']['content'];
    $tokens_used = isset($response_data['usage']['total_tokens'])
        ? $response_data['usage']['total_tokens']
        : 500; // Fallback estimate

    // Update rate limiting
    $new_count = $requests_made ? $requests_made + 1 : 1;
    set_transient($rate_limit_key, $new_count, MINUTE_IN_SECONDS);

    // Calculate processing time
    $processing_time = microtime(true) - $start_time;

    // Deduct credits with enhanced tracking
    $credits_used = businesscraft_ai_calculate_credits($tokens_used, $model);

    // Use enhanced credit deduction with tracking if available
    if (function_exists('chatgabi_deduct_credits_with_tracking')) {
        $tracking_data = array(
            'conversation_id' => $session_id . '_' . time(),
            'prompt_text' => substr($message, 0, 500), // Limit for storage
            'response_text' => substr($ai_response, 0, 500), // Limit for storage
            'estimated_tokens' => isset($token_estimation) ? $token_estimation['total_estimated_tokens'] : $tokens_used,
            'actual_tokens' => $tokens_used,
            'operation_type' => $context,
            'language_code' => $language,
            'country_code' => $user_country ?? '',
            'sector' => $industry ?? '',
            'processing_time' => $processing_time
        );

        $deduction_success = chatgabi_deduct_credits_with_tracking($user_id, $credits_used, $tracking_data);

        if (!$deduction_success) {
            return new WP_Error(
                'credit_deduction_failed',
                __('Failed to deduct credits. Please try again.', 'chatgabi'),
                array('status' => 500)
            );
        }

        $new_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    } else {
        // Fallback to original credit deduction
        $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
        $current_credits = $current_credits ? intval($current_credits) : 0;
        $new_credits = $current_credits - $credits_used;
        update_user_meta($user_id, 'businesscraft_credits', $new_credits);

        // Log credit usage (legacy)
        businesscraft_ai_log_credit_usage($user_id, $credits_used, $tokens_used, $model);
    }

    // Prepare response data
    $response_data = array(
        'response' => $ai_response,
        'tokens_used' => $tokens_used,
        'credits_used' => $credits_used,
        'remaining_credits' => $new_credits,
        'model' => $model,
        'processing_time' => $processing_time,
        'cached' => false
    );

    // Cache the response for future requests (cache for 1 hour)
    chatgabi_cache_set($cache_key, $response_data, 3600);

    // Record the request for rate limiting
    chatgabi_record_request($user_id, 'chat', $tokens_used);

    // Release concurrent slot
    chatgabi_release_concurrent_slot($user_id);

    // Log cache miss
    error_log('ChatGABI: Cache miss for OpenAI request - response cached');

    return $response_data;
}

/**
 * Get user's model based on tier
 */
function businesscraft_ai_get_user_model($user_id) {
    if (!$user_id) {
        return 'gpt-3.5-turbo';
    }

    $user_tier = get_user_meta($user_id, 'businesscraft_ai_tier', true);
    return ($user_tier === 'ultra') ? 'gpt-4-turbo' : 'gpt-3.5-turbo';
}

/**
 * Build opportunities section for AI prompt
 *
 * @param string $country Country name (e.g., "Ghana", "Kenya")
 * @param string $sector_name Sector name for filtering opportunities
 * @return array Array with 'text' and 'metadata' keys, or empty array
 */
function businesscraft_ai_build_opportunities_section($country, $sector_name = '') {
    // Check if opportunity loader function exists
    if (!function_exists('load_opportunities_by_country_sector')) {
        return array();
    }

    // Load opportunities for the country and sector
    $opportunities = load_opportunities_by_country_sector($country, $sector_name);

    if (empty($opportunities) || !is_array($opportunities)) {
        return array();
    }

    // Get top 3 opportunities sorted by deadline
    $top_opportunities = array_slice($opportunities, 0, 3);

    // Build the opportunities section
    $opportunities_text = "🔍 REAL-TIME OPPORTUNITIES:\n";
    $opportunities_count = 0;

    foreach ($top_opportunities as $opportunity) {
        // Validate opportunity structure
        if (!is_array($opportunity) || empty($opportunity['title'])) {
            continue;
        }

        $title = $opportunity['title'] ?? 'Untitled Opportunity';
        $summary = $opportunity['summary'] ?? '';
        $type = $opportunity['type'] ?? 'Opportunity';
        $deadline = '';

        // Format deadline if available
        if (!empty($opportunity['deadline'])) {
            $deadline_timestamp = strtotime($opportunity['deadline']);
            if ($deadline_timestamp !== false) {
                $deadline = ' (Deadline: ' . date('M j, Y', $deadline_timestamp) . ')';
            }
        }

        // Trim summary to keep token count manageable (max 100 characters)
        if (strlen($summary) > 100) {
            $summary = substr($summary, 0, 97) . '...';
        }

        // Format the opportunity line
        $opportunities_text .= "• {$title} – {$summary} (Type: {$type}){$deadline}\n";
        $opportunities_count++;
    }

    // Calculate token estimate for opportunities section
    $opportunities_tokens = businesscraft_ai_estimate_tokens($opportunities_text);

    return array(
        'text' => $opportunities_text,
        'count' => $opportunities_count,
        'tokens' => $opportunities_tokens
    );
}

/**
 * Build localized prompt with sector-specific context data
 *
 * @param string $user_question The user's original question
 * @param array|null $sector_context Sector data array from get_sector_context_by_country()
 * @param string $country Country name (e.g., "Ghana", "Kenya")
 * @param string $sector_name Sector name for context
 * @return string Formatted prompt with sector context
 */
function build_localized_prompt($user_question, $sector_context = null, $country = '', $sector_name = '') {
    $prompt = '';

    if ($sector_context && is_array($sector_context)) {
        // Build structured context from sector data
        $prompt .= "You are a professional business advisor helping entrepreneurs in {$country}";
        if ($sector_name) {
            $prompt .= " operating in the {$sector_name} industry";
        }
        $prompt .= ".\n\n";

        $prompt .= "Here is the relevant business context for this sector:\n\n";

        // Add overview
        if (isset($sector_context['overview'])) {
            $prompt .= "SECTOR OVERVIEW:\n" . $sector_context['overview'] . "\n\n";
        }

        // Add key conditions if available
        if (isset($sector_context['key_conditions']) && is_array($sector_context['key_conditions'])) {
            $conditions = $sector_context['key_conditions'];

            if (isset($conditions['regulatory_environment'])) {
                $prompt .= "REGULATORY ENVIRONMENT:\n" . $conditions['regulatory_environment'] . "\n\n";
            }

            if (isset($conditions['market_size_and_growth'])) {
                $prompt .= "MARKET SIZE AND GROWTH:\n" . $conditions['market_size_and_growth'] . "\n\n";
            }

            if (isset($conditions['investment_opportunities'])) {
                $prompt .= "INVESTMENT OPPORTUNITIES:\n" . $conditions['investment_opportunities'] . "\n\n";
            }

            if (isset($conditions['major_players']) && is_array($conditions['major_players'])) {
                $prompt .= "MAJOR PLAYERS:\n" . implode(', ', $conditions['major_players']) . "\n\n";
            }

            if (isset($conditions['challenges_and_risks'])) {
                $prompt .= "CHALLENGES & RISKS:\n" . $conditions['challenges_and_risks'] . "\n\n";
            }

            if (isset($conditions['key_data_points']) && is_array($conditions['key_data_points'])) {
                $prompt .= "KEY DATA POINTS:\n";
                foreach ($conditions['key_data_points'] as $key => $value) {
                    $formatted_key = ucwords(str_replace('_', ' ', $key));
                    $prompt .= "- {$formatted_key}: {$value}\n";
                }
                $prompt .= "\n";
            }

            if (isset($conditions['future_outlook'])) {
                $prompt .= "FUTURE OUTLOOK:\n" . $conditions['future_outlook'] . "\n\n";
            }
        }

        // Add real-time opportunities
        $opportunities_data = businesscraft_ai_build_opportunities_section($country, $sector_name);
        if (!empty($opportunities_data) && isset($opportunities_data['text'])) {
            $prompt .= $opportunities_data['text'] . "\n\n";
        }

        $prompt .= "Now, respond to the user's query below using this local context to provide specific, actionable advice grounded in {$country}'s market realities:\n\n";
    }

    $prompt .= "USER QUESTION: " . $user_question;

    return $prompt;
}

/**
 * Build enhanced prompt with African context and business intelligence
 */
function businesscraft_ai_build_enhanced_prompt($message, $language, $context, $user_country, $business_type, $industry, $user_id = null) {
    // Get user's preferred template language for cultural context
    $template_language = chatgabi_get_user_template_language($user_id);

    // Load cultural context for the user's language
    $cultural_context = chatgabi_get_cultural_context($template_language);

    // First, try to get sector-specific context from datasets
    $country_name = businesscraft_ai_get_country_name_from_code($user_country);
    $sector_context = null;
    $detected_sector = null;

    // Try to detect sector from user's industry preference or message content
    if ($industry) {
        $detected_sector = $industry;
        $sector_context = get_sector_context_by_country($country_name, $industry);
    }

    // If no sector context found, try to detect from message content
    if (!$sector_context) {
        $detected_sector = businesscraft_ai_detect_sector_from_message($message, $country_name);
        if ($detected_sector) {
            $sector_context = get_sector_context_by_country($country_name, $detected_sector);
        }
    }

    // Prepare variables for logging
    $sector_context_found = ($sector_context !== null);
    $prompt_tokens = 0;
    $final_prompt = '';
    $opportunities_included = 0;
    $opportunities_tokens = 0;

    // If we have sector context, use the new localized prompt system
    if ($sector_context) {
        error_log("BusinessCraft AI: Using sector context for {$detected_sector} in {$country_name}");

        // Get opportunities data before building prompt
        $opportunities_data = businesscraft_ai_build_opportunities_section($country_name, $detected_sector);
        if (!empty($opportunities_data)) {
            $opportunities_included = $opportunities_data['count'] ?? 0;
            $opportunities_tokens = $opportunities_data['tokens'] ?? 0;
        }

        $final_prompt = build_localized_prompt($message, $sector_context, $country_name, $detected_sector);

        // Add cultural context based on user's language preference
        if (!empty($cultural_context['cultural_practices'])) {
            $final_prompt = businesscraft_ai_inject_cultural_context($final_prompt, $cultural_context, $template_language, $country_name);
        }

        // Add minimal additional context to avoid token overflow
        if (class_exists('BusinessCraft_African_Context_Engine')) {
            $african_context = new BusinessCraft_African_Context_Engine();
            $examples = $african_context->generate_market_examples($user_country, 'general');
            $final_prompt .= "\n\nLOCAL BUSINESS EXAMPLES: " . json_encode(array_slice($examples, 0, 2, true));
        }

        // Calculate token estimate
        $prompt_tokens = businesscraft_ai_estimate_tokens($final_prompt);

        // Log the sector context injection attempt with opportunity tracking
        if ($user_id && function_exists('businesscraft_ai_log_sector_context')) {
            businesscraft_ai_log_sector_context(
                $user_id,
                $country_name,
                $detected_sector,
                $sector_context_found,
                $prompt_tokens,
                $message,
                $opportunities_included,
                $opportunities_tokens
            );
        }

        return $final_prompt;
    }

    // Fallback to original enhanced prompt logic if no sector context available
    $final_prompt = $message;

    // Add African context if engines are available
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $business_intelligence = new BusinessCraft_Business_Intelligence_Engine();

        // Detect if this is a business intelligence request
        $bi_keywords = array('market analysis', 'competitive analysis', 'financial planning', 'business plan', 'market research', 'competitor', 'financial model');
        $is_bi_request = false;
        foreach ($bi_keywords as $keyword) {
            if (stripos($message, $keyword) !== false) {
                $is_bi_request = true;
                break;
            }
        }

        // Generate appropriate prompt based on request type
        if ($is_bi_request) {
            if (stripos($message, 'market analysis') !== false) {
                $final_prompt = $business_intelligence->generate_market_analysis_prompt($message, $user_country, $industry);
            } elseif (stripos($message, 'competitive analysis') !== false || stripos($message, 'competitor') !== false) {
                $final_prompt = $business_intelligence->generate_competitive_analysis_prompt($message, $user_country);
            } elseif (stripos($message, 'financial') !== false) {
                $final_prompt = $business_intelligence->generate_financial_planning_prompt($message, $user_country);
            } else {
                // Add African context to general business intelligence requests
                $context_prefix = $african_context->generate_context_prompt($user_country, $business_type, $industry);
                $final_prompt = $context_prefix . "\n\nUSER REQUEST: " . $message;
            }
        } else {
            // Add African context to general requests
            $context_prefix = $african_context->generate_context_prompt($user_country, $business_type, $industry);
            $final_prompt = $context_prefix . "\n\nUSER REQUEST: " . $message;
        }

        // Add market examples
        $examples = $african_context->generate_market_examples($user_country, 'general');
        $final_prompt .= "\n\nLOCAL EXAMPLES: " . json_encode($examples);
    }

    // Load traditional templates and market data as fallback
    $templates = businesscraft_ai_load_templates($context, $language);
    $market_data = businesscraft_ai_load_market_data($language);

    // Add traditional context if available
    if (!empty($templates)) {
        $final_prompt .= "\n\nRelevant templates:\n" . implode("\n", array_slice($templates, 0, 3)); // Limit to avoid token overflow
    }

    if (!empty($market_data)) {
        $final_prompt .= "\n\nMarket insights:\n" . implode("\n", array_slice($market_data, 0, 2)); // Limit to avoid token overflow
    }

    // Calculate token estimate for fallback prompt
    $prompt_tokens = businesscraft_ai_estimate_tokens($final_prompt);

    // Log the fallback case (no sector context found)
    if ($user_id && function_exists('businesscraft_ai_log_sector_context')) {
        businesscraft_ai_log_sector_context(
            $user_id,
            $country_name,
            $detected_sector, // May be null if no sector was detected
            false, // sector_context_found = false
            $prompt_tokens,
            $message,
            0, // opportunities_included = 0 for fallback
            0  // opportunities_tokens = 0 for fallback
        );
    }

    return $final_prompt;
}

/**
 * Build prompt with context and templates (legacy function for backward compatibility)
 */
function businesscraft_ai_build_prompt($message, $language, $context) {
    return businesscraft_ai_build_enhanced_prompt($message, $language, $context, 'GH', 'sme', null);
}

/**
 * Backward compatibility alias for build_localized_prompt()
 */
if (!function_exists('businesscraft_ai_build_localized_prompt')) {
    function businesscraft_ai_build_localized_prompt($user_question, $sector_context = null, $country = '', $sector_name = '') {
        return build_localized_prompt($user_question, $sector_context, $country, $sector_name);
    }
}

/**
 * Inject cultural context into prompt based on user's language preference
 */
function businesscraft_ai_inject_cultural_context($prompt, $cultural_context, $language, $country_name) {
    if (empty($cultural_context['cultural_practices'])) {
        return $prompt;
    }

    $practices = $cultural_context['cultural_practices'];
    $language_info = chatgabi_get_supported_template_languages();
    $language_name = isset($language_info[$language]) ? $language_info[$language]['native_name'] : 'English';

    // Build cultural context section
    $cultural_section = "\n\n🌍 CULTURAL BUSINESS CONTEXT ({$language_name} - {$country_name}):\n";

    // Add key cultural practices (limit to avoid token overflow)
    $key_practices = array(
        'communication_style' => 'Communication Style',
        'business_etiquette' => 'Business Etiquette',
        'decision_making' => 'Decision Making',
        'ubuntu_philosophy' => 'Philosophy',
        'community_benefit' => 'Community Focus'
    );

    foreach ($key_practices as $key => $label) {
        if (isset($practices[$key])) {
            $cultural_section .= "• {$label}: " . substr($practices[$key], 0, 80) . "\n";
        }
    }

    // Add localized business terms if available
    if (!empty($cultural_context['local_business_terms'])) {
        $terms = array_slice($cultural_context['local_business_terms'], 0, 5, true);
        $cultural_section .= "\nKey Business Terms:\n";
        foreach ($terms as $local => $english) {
            $cultural_section .= "• {$english} = {$local}\n";
        }
    }

    $cultural_section .= "\nPlease incorporate these cultural considerations into your response.\n";

    // Insert cultural context before the user question
    $user_question_pos = strpos($prompt, "USER QUESTION:");
    if ($user_question_pos !== false) {
        return substr($prompt, 0, $user_question_pos) . $cultural_section . "\n" . substr($prompt, $user_question_pos);
    } else {
        return $prompt . $cultural_section;
    }
}

/**
 * Get enhanced system prompt with African context
 */
function businesscraft_ai_get_enhanced_system_prompt($language, $context, $user_country, $business_type, $industry) {
    // Use African context engine if available
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $context_prompt = $african_context->generate_context_prompt($user_country, $business_type, $industry);

        $language_additions = array(
            'en' => " Respond in clear, professional English.",
            'tw' => " Respond in Twi (Akan) language when culturally appropriate, but use English for technical terms.",
            'sw' => " Respond in Swahili when culturally appropriate, but use English for technical terms.",
            'yo' => " Respond in Yoruba when culturally appropriate, but use English for technical terms.",
            'zu' => " Respond in Zulu when culturally appropriate, but use English for technical terms.",
        );

        $context_additions = array(
            'business_plan' => " Focus on creating comprehensive, locally-relevant business plans with financial projections, market analysis, and implementation strategies.",
            'marketing' => " Focus on culturally-sensitive marketing strategies, customer acquisition, and brand building for African markets.",
            'finance' => " Focus on financial planning, budgeting, and funding opportunities available in local markets.",
            'operations' => " Focus on operational efficiency considering local infrastructure and business practices.",
            'general' => " Provide general business advice with local market considerations.",
        );

        $system_prompt = $context_prompt;
        $system_prompt .= isset($language_additions[$language]) ? $language_additions[$language] : $language_additions['en'];
        $system_prompt .= isset($context_additions[$context]) ? $context_additions[$context] : $context_additions['general'];

        return $system_prompt;
    }

    // Fallback to original system prompt
    return businesscraft_ai_get_system_prompt($language, $context);
}

/**
 * Get system prompt based on language and context (legacy function)
 */
function businesscraft_ai_get_system_prompt($language, $context) {
    $base_prompt = "You are BusinessCraft AI, an expert business consultant specializing in helping African entrepreneurs and creators build successful businesses. You provide practical, actionable advice tailored to the African market context.";

    $language_prompts = array(
        'en' => $base_prompt . " Respond in clear, professional English.",
        'tw' => $base_prompt . " Respond in Twi (Akan) language when appropriate, but use English for technical terms.",
        'sw' => $base_prompt . " Respond in Swahili when appropriate, but use English for technical terms.",
        'yo' => $base_prompt . " Respond in Yoruba when appropriate, but use English for technical terms.",
        'zu' => $base_prompt . " Respond in Zulu when appropriate, but use English for technical terms.",
    );

    $context_additions = array(
        'business_plan' => " Focus on creating comprehensive business plans with financial projections, market analysis, and implementation strategies.",
        'marketing' => " Focus on marketing strategies, customer acquisition, and brand building for African markets.",
        'finance' => " Focus on financial planning, budgeting, and funding opportunities available in African markets.",
        'operations' => " Focus on operational efficiency, process optimization, and standard operating procedures.",
        'general' => " Provide general business advice and guidance.",
    );

    $system_prompt = isset($language_prompts[$language])
        ? $language_prompts[$language]
        : $language_prompts['en'];

    if (isset($context_additions[$context])) {
        $system_prompt .= $context_additions[$context];
    }

    $system_prompt .= " Always provide practical, actionable advice that considers the unique challenges and opportunities in African markets. Include specific examples and steps when possible.";

    return $system_prompt;
}

/**
 * Load templates based on context and language
 */
function businesscraft_ai_load_templates($context, $language) {
    $cache_key = "bcai_templates_{$context}_{$language}";
    $cached_templates = get_transient($cache_key);

    if ($cached_templates !== false) {
        return $cached_templates;
    }

    $templates_file = CHATGABI_THEME_DIR . "/data/templates/{$context}_{$language}.json";
    $fallback_file = CHATGABI_THEME_DIR . "/data/templates/{$context}_en.json";

    $templates = array();

    if (file_exists($templates_file)) {
        $templates_data = file_get_contents($templates_file);
        $templates_json = json_decode($templates_data, true);
        if ($templates_json && isset($templates_json['templates'])) {
            $templates = $templates_json['templates'];
        }
    } elseif (file_exists($fallback_file)) {
        $templates_data = file_get_contents($fallback_file);
        $templates_json = json_decode($templates_data, true);
        if ($templates_json && isset($templates_json['templates'])) {
            $templates = $templates_json['templates'];
        }
    }

    // Cache for 5 minutes
    set_transient($cache_key, $templates, 5 * MINUTE_IN_SECONDS);

    return $templates;
}

/**
 * Load market data based on language/region
 */
function businesscraft_ai_load_market_data($language) {
    $cache_key = "bcai_market_data_{$language}";
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $market_file = CHATGABI_THEME_DIR . "/data/market/{$language}.json";
    $fallback_file = CHATGABI_THEME_DIR . "/data/market/en.json";

    $market_data = array();

    if (file_exists($market_file)) {
        $data = file_get_contents($market_file);
        $data_json = json_decode($data, true);
        if ($data_json && isset($data_json['market_insights'])) {
            $market_data = $data_json['market_insights'];
        }
    } elseif (file_exists($fallback_file)) {
        $data = file_get_contents($fallback_file);
        $data_json = json_decode($data, true);
        if ($data_json && isset($data_json['market_insights'])) {
            $market_data = $data_json['market_insights'];
        }
    }

    // Cache for 5 minutes
    set_transient($cache_key, $market_data, 5 * MINUTE_IN_SECONDS);

    return $market_data;
}

/**
 * Estimate tokens for a text string
 */
function businesscraft_ai_estimate_tokens($text) {
    // Rough estimation: 1 token ≈ 4 characters for English
    // Adjust for other languages
    return ceil(strlen($text) / 4);
}

/**
 * Check if OpenAI API is configured
 */
function businesscraft_ai_is_openai_configured() {
    $api_key = defined('BUSINESSCRAFT_AI_OPENAI_API_KEY') ? BUSINESSCRAFT_AI_OPENAI_API_KEY : get_option('businesscraft_ai_openai_api_key');
    return !empty($api_key);
}

/**
 * Get country name from country code
 */
function businesscraft_ai_get_country_name_from_code($country_code) {
    $country_mapping = array(
        'GH' => 'Ghana',
        'KE' => 'Kenya',
        'NG' => 'Nigeria',
        'ZA' => 'South Africa'
    );

    return isset($country_mapping[$country_code]) ? $country_mapping[$country_code] : 'Ghana';
}

/**
 * Detect business sector from user message content
 *
 * @param string $message User's message
 * @param string $country_name Country name for sector lookup
 * @return string|null Detected sector name or null if not found
 */
function businesscraft_ai_detect_sector_from_message($message, $country_name) {
    // Get available sectors for the country
    $available_sectors = get_available_sectors_by_country($country_name);

    if (!$available_sectors || !is_array($available_sectors)) {
        return null;
    }

    $message_lower = strtolower($message);

    // Define sector keywords mapping
    $sector_keywords = array(
        'agriculture' => array('farm', 'crop', 'livestock', 'agriculture', 'farming', 'agricultural'),
        'fintech' => array('fintech', 'payment', 'mobile money', 'financial technology', 'banking', 'finance'),
        'technology' => array('tech', 'software', 'app', 'digital', 'technology', 'IT', 'programming'),
        'healthcare' => array('health', 'medical', 'hospital', 'clinic', 'healthcare', 'medicine'),
        'education' => array('education', 'school', 'learning', 'training', 'educational', 'teaching'),
        'retail' => array('retail', 'shop', 'store', 'selling', 'commerce', 'trading'),
        'manufacturing' => array('manufacturing', 'production', 'factory', 'industrial', 'processing'),
        'tourism' => array('tourism', 'travel', 'hotel', 'hospitality', 'tourist'),
        'energy' => array('energy', 'power', 'electricity', 'solar', 'renewable'),
        'transport' => array('transport', 'logistics', 'delivery', 'shipping', 'transportation'),
        'construction' => array('construction', 'building', 'real estate', 'property', 'housing'),
        'media' => array('media', 'content', 'entertainment', 'creative', 'advertising'),
        'mining' => array('mining', 'mineral', 'extraction', 'gold', 'diamond'),
        'textile' => array('textile', 'clothing', 'fashion', 'garment', 'fabric'),
        'food' => array('food', 'restaurant', 'catering', 'beverage', 'cooking')
    );

    // First, try exact sector name matching
    foreach ($available_sectors as $sector) {
        if (stripos($message_lower, strtolower($sector)) !== false) {
            return $sector;
        }
    }

    // Then try keyword-based detection
    foreach ($sector_keywords as $sector_type => $keywords) {
        foreach ($keywords as $keyword) {
            if (stripos($message_lower, $keyword) !== false) {
                // Find matching sector in available sectors
                foreach ($available_sectors as $sector) {
                    if (stripos(strtolower($sector), $sector_type) !== false) {
                        return $sector;
                    }
                }
            }
        }
    }

    return null;
}

/**
 * Test OpenAI API connection
 */
function businesscraft_ai_test_openai_connection() {
    $api_key = defined('BUSINESSCRAFT_AI_OPENAI_API_KEY') ? BUSINESSCRAFT_AI_OPENAI_API_KEY : get_option('businesscraft_ai_openai_api_key');

    if (empty($api_key)) {
        return new WP_Error('missing_api_key', 'API key not configured');
    }

    $response = wp_remote_get('https://api.openai.com/v1/models', array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $api_key,
        ),
        'timeout' => 10,
    ));

    if (is_wp_error($response)) {
        return $response;
    }

    $response_code = wp_remote_retrieve_response_code($response);

    if ($response_code === 200) {
        return true;
    } else {
        return new WP_Error('api_error', 'API connection failed');
    }
}

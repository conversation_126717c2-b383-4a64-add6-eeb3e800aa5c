/**
 * PDF Export Styles for BusinessCraft AI
 * 
 * Styles for PDF export functionality and modals
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

/* Export Actions */
.export-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.export-pdf-btn,
.export-docx-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.export-docx-btn {
    background: linear-gradient(to right, #28a745, #20c997);
}

.export-pdf-btn:hover,
.export-docx-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: white;
    text-decoration: none;
}

.export-pdf-btn:disabled,
.export-docx-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Export Modal */
.export-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.export-modal.active {
    display: flex;
    opacity: 1;
    align-items: center;
    justify-content: center;
}

.export-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.export-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.export-modal.active .export-modal-content {
    transform: scale(1);
}

.export-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.export-modal-header h3 {
    margin: 0;
    color: #3D4E81;
    font-size: 1.4em;
}

.export-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.export-modal-close:hover {
    background-color: #f8f9fa;
    color: #333;
}

.export-modal-body {
    padding: 24px;
}

.export-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.export-option {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.export-option label {
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.export-option select {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.export-option input[type="checkbox"] {
    margin-right: 8px;
}

.export-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
}

.export-preview h4 {
    margin: 0 0 12px 0;
    color: #3D4E81;
    font-size: 1.1em;
}

.preview-content {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 16px;
    font-size: 14px;
}

.preview-header {
    color: #3D4E81;
    font-size: 16px;
    margin-bottom: 8px;
}

.preview-meta {
    color: #666;
    margin-bottom: 12px;
}

.preview-branding {
    color: #888;
    font-size: 12px;
    border-top: 1px solid #eee;
    padding-top: 8px;
    margin-top: 8px;
}

.export-modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Print Instructions Modal */
.print-instructions-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.print-instructions-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.print-instructions-content h3 {
    margin: 0 0 16px 0;
    color: #3D4E81;
}

.print-instructions-content p {
    margin: 0 0 20px 0;
    color: #666;
    line-height: 1.5;
}

.print-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Export Messages */
.export-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    z-index: 10002;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.export-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.export-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Export History */
.export-history {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.export-history h4 {
    margin: 0 0 16px 0;
    color: #3D4E81;
}

.export-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.export-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.export-item:last-child {
    border-bottom: none;
}

.export-info strong {
    display: block;
    color: #333;
    margin-bottom: 4px;
}

.export-meta {
    font-size: 12px;
    color: #666;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    background: #3D4E81;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.btn-small:hover {
    background: #2a3660;
    color: white;
    text-decoration: none;
}

/* Export Section in Template Modal */
.export-section {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.export-section h4 {
    margin: 0 0 16px 0;
    color: #3D4E81;
}

.export-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .export-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .export-actions {
        flex-direction: column;
    }
    
    .export-pdf-btn,
    .export-docx-btn {
        justify-content: center;
        width: 100%;
    }
    
    .export-modal-footer {
        flex-direction: column;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .print-actions {
        flex-direction: column;
    }
    
    .export-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* Body modal open state */
body.modal-open {
    overflow: hidden;
}

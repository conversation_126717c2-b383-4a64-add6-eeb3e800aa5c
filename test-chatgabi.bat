@echo off
echo.
echo ========================================
echo   ChatGABI System Health Test
echo ========================================
echo.

echo Testing WordPress load...
php -r "require_once 'wp-config.php'; require_once ABSPATH . 'wp-load.php'; echo 'WordPress loaded successfully!' . PHP_EOL;"

echo.
echo Testing file syntax...
echo Checking functions.php...
php -l wp-content/themes/businesscraft-ai/functions.php

echo Checking ajax-handlers.php...
php -l wp-content/themes/businesscraft-ai/inc/ajax-handlers.php

echo Checking language-functions.php...
php -l wp-content/themes/businesscraft-ai/inc/language-functions.php

echo Checking template-functions.php...
php -l wp-content/themes/businesscraft-ai/inc/template-functions.php

echo Checking user-preference-functions.php...
php -l wp-content/themes/businesscraft-ai/inc/user-preference-functions.php

echo Checking admin-functions.php...
php -l wp-content/themes/businesscraft-ai/inc/admin-functions.php

echo.
echo Testing core functions...
php -r "require_once 'wp-config.php'; require_once ABSPATH . 'wp-load.php'; $functions = ['chatgabi_get_supported_languages', 'chatgabi_get_user_preferred_language', 'chatgabi_load_business_plan_templates', 'chatgabi_get_user_preferences']; foreach ($functions as $function) { if (function_exists($function)) { echo 'OK: ' . $function . PHP_EOL; } else { echo 'MISSING: ' . $function . PHP_EOL; } }"

echo.
echo Testing database connection...
php -r "require_once 'wp-config.php'; require_once ABSPATH . 'wp-load.php'; global $wpdb; $result = $wpdb->get_var('SELECT 1'); echo ($result == 1) ? 'Database connection: OK' . PHP_EOL : 'Database connection: FAILED' . PHP_EOL;"

echo.
echo File line counts:
for %%f in (
    "wp-content/themes/businesscraft-ai/functions.php"
    "wp-content/themes/businesscraft-ai/inc/ajax-handlers.php"
    "wp-content/themes/businesscraft-ai/inc/language-functions.php"
    "wp-content/themes/businesscraft-ai/inc/template-functions.php"
    "wp-content/themes/businesscraft-ai/inc/user-preference-functions.php"
    "wp-content/themes/businesscraft-ai/inc/admin-functions.php"
) do (
    if exist %%f (
        for /f %%i in ('find /c /v "" ^< %%f') do echo %%~nxf: %%i lines
    )
)

echo.
echo ========================================
echo   Test Complete
echo ========================================
echo.
echo If no errors appeared above, the system is healthy!
echo.
echo Next: Open http://localhost/swifmind-local/wordpress/test-system-health.php
echo       for comprehensive browser-based testing.
echo.
pause

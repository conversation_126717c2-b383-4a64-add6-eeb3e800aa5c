<?php
/**
 * Test Manifest Accessibility
 * 
 * This script tests if the manifest.json file is accessible via HTTP
 * and validates its content.
 */

// Get the site URL and construct manifest URL
$site_url = 'http://localhost/swifmind-local/wordpress';
$manifest_url = $site_url . '/wp-content/themes/businesscraft-ai/manifest.json';

echo "<h1>Manifest Accessibility Test</h1>\n";
echo "<p>Testing manifest URL: <a href='$manifest_url' target='_blank'>$manifest_url</a></p>\n";

// Test 1: Check if manifest file exists locally
echo "<h2>Test 1: Local File Existence</h2>\n";
$local_path = __DIR__ . '/manifest.json';
if (file_exists($local_path)) {
    echo "✅ manifest.json exists locally at: $local_path\n";
    
    // Check file permissions
    if (is_readable($local_path)) {
        echo "✅ manifest.json is readable\n";
        
        // Get file size
        $file_size = filesize($local_path);
        echo "📊 File size: $file_size bytes\n";
        
        // Validate JSON
        $content = file_get_contents($local_path);
        $json_data = json_decode($content, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ manifest.json contains valid JSON\n";
            
            // Check required PWA fields
            $required_fields = ['name', 'short_name', 'start_url', 'display', 'icons'];
            $missing_fields = [];
            
            foreach ($required_fields as $field) {
                if (!isset($json_data[$field])) {
                    $missing_fields[] = $field;
                }
            }
            
            if (empty($missing_fields)) {
                echo "✅ All required PWA fields are present\n";
                echo "📋 Manifest details:\n";
                echo "   - Name: " . ($json_data['name'] ?? 'N/A') . "\n";
                echo "   - Short Name: " . ($json_data['short_name'] ?? 'N/A') . "\n";
                echo "   - Start URL: " . ($json_data['start_url'] ?? 'N/A') . "\n";
                echo "   - Display: " . ($json_data['display'] ?? 'N/A') . "\n";
                echo "   - Icons: " . (isset($json_data['icons']) ? count($json_data['icons']) : 0) . " icons\n";
            } else {
                echo "❌ Missing required fields: " . implode(', ', $missing_fields) . "\n";
            }
        } else {
            echo "❌ manifest.json contains invalid JSON: " . json_last_error_msg() . "\n";
        }
    } else {
        echo "❌ manifest.json is not readable\n";
    }
} else {
    echo "❌ manifest.json does NOT exist at: $local_path\n";
}

// Test 2: HTTP accessibility test using cURL
echo "<h2>Test 2: HTTP Accessibility</h2>\n";
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $manifest_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ cURL error: $error\n";
    } else {
        echo "📡 HTTP Status Code: $http_code\n";
        echo "📄 Content Type: $content_type\n";
        
        if ($http_code === 200) {
            echo "✅ Manifest is accessible via HTTP\n";
            
            // Extract body from response
            $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $body = substr($response, $header_size);
            
            // Validate JSON from HTTP response
            $json_data = json_decode($body, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "✅ HTTP response contains valid JSON\n";
            } else {
                echo "❌ HTTP response contains invalid JSON: " . json_last_error_msg() . "\n";
            }
        } else {
            echo "❌ Manifest is NOT accessible via HTTP (Status: $http_code)\n";
        }
    }
} else {
    echo "❌ cURL not available for HTTP testing\n";
}

// Test 3: Check WordPress theme directory URI
echo "<h2>Test 3: WordPress Theme Directory</h2>\n";
if (function_exists('get_template_directory_uri')) {
    $theme_uri = get_template_directory_uri();
    $wp_manifest_url = $theme_uri . '/manifest.json';
    echo "🔗 WordPress manifest URL: $wp_manifest_url\n";
    
    if ($wp_manifest_url === $manifest_url) {
        echo "✅ URLs match - WordPress theme URI is correct\n";
    } else {
        echo "⚠️ URL mismatch:\n";
        echo "   Expected: $manifest_url\n";
        echo "   WordPress: $wp_manifest_url\n";
    }
} else {
    echo "❌ WordPress functions not available\n";
}

echo "\n<h2>Summary</h2>\n";
echo "If all tests pass, the manifest.json file should be properly accessible for PWA functionality.\n";
echo "If any tests fail, those issues need to be resolved.\n";

// Test 4: Generate a simple HTML test page
echo "<h2>Test 4: HTML Test Page</h2>\n";
$test_html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Manifest Test</title>
    <link rel="manifest" href="' . $manifest_url . '">
</head>
<body>
    <h1>PWA Manifest Test Page</h1>
    <p>Check browser developer tools to see if manifest is loaded.</p>
    <script>
        console.log("Manifest URL:", "' . $manifest_url . '");
        
        // Check if manifest is loaded
        if ("serviceWorker" in navigator) {
            console.log("Service Worker supported");
        }
        
        // Check for PWA install prompt
        window.addEventListener("beforeinstallprompt", function(e) {
            console.log("PWA install prompt available");
        });
    </script>
</body>
</html>';

$test_file = __DIR__ . '/test-manifest.html';
if (file_put_contents($test_file, $test_html)) {
    echo "✅ Test HTML page created: <a href='test-manifest.html' target='_blank'>test-manifest.html</a>\n";
} else {
    echo "❌ Failed to create test HTML page\n";
}
?>

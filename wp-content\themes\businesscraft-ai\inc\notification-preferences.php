<?php
/**
 * Notification Preferences System
 * 
 * Comprehensive notification management for BusinessCraft AI
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize notification preferences system
 */
function businesscraft_ai_init_notification_preferences() {
    // Create notification preferences table
    businesscraft_ai_create_notification_preferences_table();
    businesscraft_ai_create_user_notifications_table();
}

/**
 * Create notification preferences table
 */
function businesscraft_ai_create_notification_preferences_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_notification_preferences';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        notification_type varchar(50) NOT NULL,
        channel varchar(20) NOT NULL DEFAULT 'email',
        enabled tinyint(1) NOT NULL DEFAULT 1,
        frequency varchar(20) NOT NULL DEFAULT 'immediate',
        settings longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_notification (user_id, notification_type, channel),
        KEY user_id (user_id),
        KEY notification_type (notification_type),
        KEY enabled (enabled)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Create user notifications table
 */
function businesscraft_ai_create_user_notifications_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_user_notifications';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        notification_type varchar(50) NOT NULL,
        title varchar(255) NOT NULL,
        message text NOT NULL,
        data longtext,
        status varchar(20) NOT NULL DEFAULT 'unread',
        sent_at datetime DEFAULT NULL,
        read_at datetime DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY notification_type (notification_type),
        KEY status (status),
        KEY created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Get default notification preferences
 */
function businesscraft_ai_get_default_notification_preferences() {
    return array(
        'opportunities' => array(
            'email' => array('enabled' => true, 'frequency' => 'daily'),
            'push' => array('enabled' => false, 'frequency' => 'immediate')
        ),
        'credits' => array(
            'email' => array('enabled' => true, 'frequency' => 'immediate'),
            'push' => array('enabled' => true, 'frequency' => 'immediate')
        ),
        'templates' => array(
            'email' => array('enabled' => true, 'frequency' => 'weekly'),
            'push' => array('enabled' => false, 'frequency' => 'immediate')
        ),
        'weekly_summary' => array(
            'email' => array('enabled' => true, 'frequency' => 'weekly'),
            'push' => array('enabled' => false, 'frequency' => 'weekly')
        ),
        'urgent_alerts' => array(
            'email' => array('enabled' => true, 'frequency' => 'immediate'),
            'push' => array('enabled' => true, 'frequency' => 'immediate')
        )
    );
}

/**
 * Get user notification preferences
 */
function businesscraft_ai_get_user_notification_preferences($user_id) {
    global $wpdb;
    
    if (!$user_id) {
        return businesscraft_ai_get_default_notification_preferences();
    }
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_notification_preferences';
    
    $preferences = $wpdb->get_results($wpdb->prepare(
        "SELECT notification_type, channel, enabled, frequency, settings
         FROM $table_name
         WHERE user_id = %d",
        $user_id
    ));
    
    $defaults = businesscraft_ai_get_default_notification_preferences();
    $user_prefs = $defaults;
    
    foreach ($preferences as $pref) {
        if (isset($user_prefs[$pref->notification_type][$pref->channel])) {
            $user_prefs[$pref->notification_type][$pref->channel] = array(
                'enabled' => (bool) $pref->enabled,
                'frequency' => $pref->frequency,
                'settings' => $pref->settings ? json_decode($pref->settings, true) : array()
            );
        }
    }
    
    return $user_prefs;
}

/**
 * Save user notification preferences
 */
function businesscraft_ai_save_user_notification_preferences($user_id, $preferences) {
    global $wpdb;
    
    if (!$user_id) {
        return false;
    }
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_notification_preferences';
    
    foreach ($preferences as $notification_type => $channels) {
        foreach ($channels as $channel => $settings) {
            $wpdb->replace(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'notification_type' => $notification_type,
                    'channel' => $channel,
                    'enabled' => $settings['enabled'] ? 1 : 0,
                    'frequency' => $settings['frequency'],
                    'settings' => isset($settings['settings']) ? json_encode($settings['settings']) : null
                ),
                array('%d', '%s', '%s', '%d', '%s', '%s')
            );
        }
    }
    
    return true;
}

/**
 * Check if user should receive notification
 */
function businesscraft_ai_should_send_notification($user_id, $notification_type, $channel = 'email') {
    $preferences = businesscraft_ai_get_user_notification_preferences($user_id);
    
    if (!isset($preferences[$notification_type][$channel])) {
        return false;
    }
    
    return $preferences[$notification_type][$channel]['enabled'];
}

/**
 * Create notification for user
 */
function businesscraft_ai_create_notification($user_id, $notification_type, $title, $message, $data = null) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_user_notifications';
    
    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'notification_type' => $notification_type,
            'title' => $title,
            'message' => $message,
            'data' => $data ? json_encode($data) : null,
            'status' => 'unread'
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s')
    );
    
    return $result ? $wpdb->insert_id : false;
}

/**
 * Send notification to user
 */
function businesscraft_ai_send_notification($user_id, $notification_type, $title, $message, $data = null) {
    // Create notification record
    $notification_id = businesscraft_ai_create_notification($user_id, $notification_type, $title, $message, $data);
    
    if (!$notification_id) {
        return false;
    }
    
    $preferences = businesscraft_ai_get_user_notification_preferences($user_id);
    $sent = false;
    
    // Send email notification
    if (businesscraft_ai_should_send_notification($user_id, $notification_type, 'email')) {
        $sent = businesscraft_ai_send_email_notification($user_id, $title, $message, $data) || $sent;
    }
    
    // Send push notification
    if (businesscraft_ai_should_send_notification($user_id, $notification_type, 'push')) {
        $sent = businesscraft_ai_send_push_notification($user_id, $title, $message, $data) || $sent;
    }
    
    // Update notification status
    if ($sent) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'businesscraft_ai_user_notifications';
        $wpdb->update(
            $table_name,
            array('status' => 'sent', 'sent_at' => current_time('mysql')),
            array('id' => $notification_id),
            array('%s', '%s'),
            array('%d')
        );
    }
    
    return $sent;
}

/**
 * Send email notification
 */
function businesscraft_ai_send_email_notification($user_id, $title, $message, $data = null) {
    $user = get_userdata($user_id);
    if (!$user) {
        return false;
    }
    
    $subject = sprintf('[%s] %s', get_bloginfo('name'), $title);
    
    $email_message = sprintf(
        __('Dear %s,

%s

Best regards,
The BusinessCraft AI Team

---
You can manage your notification preferences in your dashboard: %s', 'businesscraft-ai'),
        $user->display_name,
        $message,
        home_url('/dashboard#notifications')
    );
    
    return wp_mail($user->user_email, $subject, $email_message);
}

/**
 * Send push notification (placeholder for future implementation)
 */
function businesscraft_ai_send_push_notification($user_id, $title, $message, $data = null) {
    // Placeholder for push notification implementation
    // This would integrate with a service like Firebase, OneSignal, etc.
    return true;
}

/**
 * Get user notifications
 */
function businesscraft_ai_get_user_notifications($user_id, $limit = 20, $offset = 0) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_user_notifications';
    
    $notifications = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name
         WHERE user_id = %d
         ORDER BY created_at DESC
         LIMIT %d OFFSET %d",
        $user_id, $limit, $offset
    ));
    
    $formatted_notifications = array();
    foreach ($notifications as $notification) {
        $formatted_notifications[] = array(
            'id' => $notification->id,
            'type' => $notification->notification_type,
            'title' => $notification->title,
            'message' => $notification->message,
            'data' => $notification->data ? json_decode($notification->data, true) : null,
            'status' => $notification->status,
            'time_ago' => human_time_diff(strtotime($notification->created_at), current_time('timestamp')) . ' ago',
            'icon' => businesscraft_ai_get_notification_icon($notification->notification_type)
        );
    }
    
    return $formatted_notifications;
}

/**
 * Get notification icon
 */
function businesscraft_ai_get_notification_icon($notification_type) {
    $icons = array(
        'opportunities' => '🎯',
        'credits' => '⚡',
        'templates' => '📝',
        'weekly_summary' => '📊',
        'urgent_alerts' => '🚨',
        'system' => '⚙️'
    );
    
    return $icons[$notification_type] ?? '🔔';
}

/**
 * Mark notification as read
 */
function businesscraft_ai_mark_notification_read($notification_id, $user_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_user_notifications';
    
    return $wpdb->update(
        $table_name,
        array('status' => 'read', 'read_at' => current_time('mysql')),
        array('id' => $notification_id, 'user_id' => $user_id),
        array('%s', '%s'),
        array('%d', '%d')
    );
}

/**
 * AJAX handler for getting notification preferences
 */
function businesscraft_ai_ajax_get_notification_preferences() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $preferences = businesscraft_ai_get_user_notification_preferences($user_id);
    wp_send_json_success($preferences);
}

/**
 * AJAX handler for saving notification preferences
 */
function businesscraft_ai_ajax_save_notification_preferences() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $preferences = json_decode(stripslashes($_POST['preferences']), true);
    if (!$preferences) {
        wp_send_json_error('Invalid preferences data');
        return;
    }
    
    $result = businesscraft_ai_save_user_notification_preferences($user_id, $preferences);
    
    if ($result) {
        wp_send_json_success('Preferences saved successfully');
    } else {
        wp_send_json_error('Failed to save preferences');
    }
}

/**
 * AJAX handler for getting user notifications
 */
function businesscraft_ai_ajax_get_user_notifications() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $limit = intval($_POST['limit'] ?? 20);
    $offset = intval($_POST['offset'] ?? 0);
    
    $notifications = businesscraft_ai_get_user_notifications($user_id, $limit, $offset);
    wp_send_json_success($notifications);
}

/**
 * AJAX handler for sending test notification
 */
function businesscraft_ai_ajax_send_test_notification() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $result = businesscraft_ai_send_notification(
        $user_id,
        'system',
        'Test Notification',
        'This is a test notification to verify your notification preferences are working correctly.'
    );
    
    if ($result) {
        wp_send_json_success('Test notification sent successfully');
    } else {
        wp_send_json_error('Failed to send test notification');
    }
}

// Register AJAX handlers
add_action('wp_ajax_businesscraft_ai_get_notification_preferences', 'businesscraft_ai_ajax_get_notification_preferences');
add_action('wp_ajax_businesscraft_ai_save_notification_preferences', 'businesscraft_ai_ajax_save_notification_preferences');
add_action('wp_ajax_businesscraft_ai_get_user_notifications', 'businesscraft_ai_ajax_get_user_notifications');
add_action('wp_ajax_businesscraft_ai_send_test_notification', 'businesscraft_ai_ajax_send_test_notification');

// Initialize on WordPress init
add_action('init', 'businesscraft_ai_init_notification_preferences');

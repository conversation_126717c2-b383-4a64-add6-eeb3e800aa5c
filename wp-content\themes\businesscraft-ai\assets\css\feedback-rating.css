/**
 * ChatGABI Feedback Rating System Styles
 * 
 * Responsive styles for user feedback and rating interface
 */

/* Main Feedback Interface */
.feedback-interface {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
}

.feedback-interface:hover {
    border-color: #007cba;
    box-shadow: 0 2px 8px rgba(0, 124, 186, 0.1);
}

/* Feedback Header */
.feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.feedback-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.toggle-detailed-feedback {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #6c757d;
    transition: all 0.2s ease;
}

.toggle-detailed-feedback:hover {
    background: #e9ecef;
    color: #007cba;
}

.toggle-detailed-feedback.active {
    background: #007cba;
    color: white;
}

/* Quick Rating Section */
.feedback-quick-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 10px;
}

/* Star Rating */
.star-rating {
    display: flex;
    gap: 4px;
}

.feedback-star {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #ddd;
    font-size: 18px;
    transition: all 0.2s ease;
}

.feedback-star:hover {
    color: #ffc107;
    transform: scale(1.1);
}

.feedback-star.active {
    color: #ffc107;
}

.feedback-star .dashicons-star-filled {
    color: #ffc107;
}

.feedback-star .dashicons-star-empty {
    color: #ddd;
}

/* Thumbs Rating */
.thumbs-rating {
    display: flex;
    gap: 8px;
}

.feedback-thumbs {
    background: none;
    border: 2px solid #e9ecef;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    color: #6c757d;
    font-size: 16px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feedback-thumbs:hover {
    border-color: #007cba;
    color: #007cba;
    transform: translateY(-1px);
}

.feedback-thumbs.active.thumbs-up {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.feedback-thumbs.active.thumbs-down {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

/* Detailed Feedback Section */
.feedback-detailed {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    margin-top: 15px;
}

/* Category Ratings */
.category-ratings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.category-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.category-group label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-rating {
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.category-rating:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* Feedback Text Area */
.feedback-text-area {
    margin-bottom: 15px;
}

.feedback-text {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.2s ease;
}

.feedback-text:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.feedback-text::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Feedback Options */
.feedback-options {
    margin-bottom: 15px;
}

.training-consent-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #495057;
    cursor: pointer;
}

.training-consent {
    margin: 0;
}

/* Feedback Actions */
.feedback-actions {
    display: flex;
    justify-content: flex-end;
}

.submit-feedback-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.submit-feedback-btn:hover {
    background: #005a87;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 124, 186, 0.3);
}

.submit-feedback-btn:active {
    transform: translateY(0);
}

.submit-feedback-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Feedback Status */
.feedback-status {
    padding: 10px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    margin-top: 10px;
}

.feedback-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .feedback-interface {
        padding: 12px;
        margin-top: 12px;
    }
    
    .feedback-quick-rating {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .star-rating {
        justify-content: center;
    }
    
    .thumbs-rating {
        justify-content: center;
    }
    
    .category-ratings {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .feedback-star {
        font-size: 20px;
        padding: 6px;
    }
    
    .feedback-thumbs {
        padding: 10px 16px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .feedback-interface {
        padding: 10px;
    }
    
    .category-ratings {
        grid-template-columns: 1fr;
    }
    
    .feedback-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .toggle-detailed-feedback {
        align-self: flex-end;
    }
}

/* Animation for detailed form toggle */
.feedback-detailed {
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Loading state */
.feedback-interface.loading {
    opacity: 0.7;
    pointer-events: none;
}

.feedback-interface.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Integration with existing chat styles */
.ai-message .feedback-interface {
    margin-top: 10px;
    background: rgba(248, 249, 250, 0.8);
    backdrop-filter: blur(5px);
}

.user-message .feedback-interface {
    display: none; /* Only show feedback for AI responses */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .feedback-interface {
        border-width: 2px;
    }
    
    .feedback-star, .feedback-thumbs {
        border-width: 2px;
    }
    
    .feedback-text, .category-rating {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .feedback-interface,
    .feedback-star,
    .feedback-thumbs,
    .submit-feedback-btn,
    .toggle-detailed-feedback {
        transition: none;
    }
    
    .feedback-interface.loading::after {
        animation: none;
    }
}

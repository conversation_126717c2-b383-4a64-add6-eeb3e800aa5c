<?php
/**
 * AJAX Handlers for ChatGABI AI
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX handler for saving language preference
 */
add_action('wp_ajax_chatgabi_save_language_preference', 'chatgabi_ajax_save_language_preference');
function chatgabi_ajax_save_language_preference() {
    check_ajax_referer('chatgabi_save_language_preference', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $language = sanitize_text_field($_POST['language']);
    $user_id = get_current_user_id();
    
    update_user_meta($user_id, 'chatgabi_preferred_language', $language);
    
    wp_send_json_success('Language preference saved');
}

/**
 * AJAX handler for getting cultural context
 */
add_action('wp_ajax_chatgabi_get_cultural_context', 'chatgabi_ajax_get_cultural_context');
function chatgabi_ajax_get_cultural_context() {
    check_ajax_referer('chatgabi_get_cultural_context', 'nonce');
    
    $language = sanitize_text_field($_POST['language']);
    
    // Get cultural context for the language
    $cultural_context = chatgabi_get_cultural_context($language);
    
    if (!empty($cultural_context)) {
        wp_send_json_success($cultural_context);
    } else {
        wp_send_json_error('Cultural context not found');
    }
}

/**
 * AJAX handler for saving user preferences
 */
add_action('wp_ajax_chatgabi_save_user_preferences', 'chatgabi_ajax_save_user_preferences');
function chatgabi_ajax_save_user_preferences() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    $preferences = $_POST['preferences'];
    
    // Sanitize preferences
    $sanitized_preferences = array();
    foreach ($preferences as $key => $value) {
        if (is_array($value)) {
            $sanitized_preferences[$key] = array_map('sanitize_text_field', $value);
        } else {
            $sanitized_preferences[$key] = sanitize_text_field($value);
        }
    }
    
    // Save preferences
    $result = chatgabi_save_user_preferences($user_id, $sanitized_preferences);
    
    if ($result) {
        wp_send_json_success('Preferences saved successfully');
    } else {
        wp_send_json_error('Failed to save preferences');
    }
}

/**
 * AJAX handler for resetting user preferences
 */
add_action('wp_ajax_chatgabi_reset_user_preferences', 'chatgabi_ajax_reset_user_preferences');
function chatgabi_ajax_reset_user_preferences() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    
    // Reset to default preferences
    $default_preferences = chatgabi_get_default_preferences();
    $result = chatgabi_save_user_preferences($user_id, $default_preferences);
    
    if ($result) {
        wp_send_json_success($default_preferences);
    } else {
        wp_send_json_error('Failed to reset preferences');
    }
}

/**
 * AJAX handler for getting sectors by country
 */
add_action('wp_ajax_chatgabi_get_sectors', 'chatgabi_ajax_get_sectors');
function chatgabi_ajax_get_sectors() {
    check_ajax_referer('chatgabi_get_sectors', 'nonce');
    
    $country = sanitize_text_field($_POST['country']);
    
    // Get sectors for the country
    $sectors = chatgabi_get_sectors_by_country($country);
    
    if (!empty($sectors)) {
        wp_send_json_success($sectors);
    } else {
        wp_send_json_error('No sectors found for this country');
    }
}

/**
 * AJAX handler for estimating token usage
 */
add_action('wp_ajax_chatgabi_estimate_tokens', 'chatgabi_ajax_estimate_tokens');
function chatgabi_ajax_estimate_tokens() {
    check_ajax_referer('chatgabi_token_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $message = sanitize_textarea_field($_POST['message']);
    $context = sanitize_text_field($_POST['context'] ?? '');
    
    // Estimate tokens
    $estimated_tokens = businesscraft_ai_estimate_tokens($message, $context);
    $estimated_credits = ceil($estimated_tokens / 1000); // 1 credit per 1000 tokens
    
    wp_send_json_success(array(
        'tokens' => $estimated_tokens,
        'credits' => $estimated_credits
    ));
}

/**
 * AJAX handler for getting user credit balance
 */
add_action('wp_ajax_chatgabi_get_credit_balance', 'chatgabi_ajax_get_credit_balance');
function chatgabi_ajax_get_credit_balance() {
    check_ajax_referer('chatgabi_credit_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    $balance = chatgabi_get_user_credit_balance($user_id);
    
    wp_send_json_success(array(
        'balance' => $balance
    ));
}

/**
 * AJAX handler for clearing chat history
 */
add_action('wp_ajax_chatgabi_clear_chat_history', 'chatgabi_ajax_clear_chat_history');
function chatgabi_ajax_clear_chat_history() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    
    // Clear chat history
    global $wpdb;
    $conversations_table = $wpdb->prefix . 'chatgabi_conversations';
    
    $result = $wpdb->delete(
        $conversations_table,
        array('user_id' => $user_id),
        array('%d')
    );
    
    if ($result !== false) {
        wp_send_json_success('Chat history cleared successfully');
    } else {
        wp_send_json_error('Failed to clear chat history');
    }
}

/**
 * AJAX handler for deleting user account
 */
add_action('wp_ajax_chatgabi_delete_account', 'chatgabi_ajax_delete_account');
function chatgabi_ajax_delete_account() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    
    // Delete user data from custom tables
    global $wpdb;
    
    $tables = array(
        $wpdb->prefix . 'chatgabi_conversations',
        $wpdb->prefix . 'chatgabi_credit_transactions',
        $wpdb->prefix . 'chatgabi_sector_logs'
    );
    
    foreach ($tables as $table) {
        $wpdb->delete($table, array('user_id' => $user_id), array('%d'));
    }
    
    // Delete WordPress user
    $result = wp_delete_user($user_id);
    
    if ($result) {
        wp_send_json_success('Account deleted successfully');
    } else {
        wp_send_json_error('Failed to delete account');
    }
}

/**
 * AJAX handler for exporting user data
 */
add_action('wp_ajax_chatgabi_export_user_data', 'chatgabi_ajax_export_user_data');
function chatgabi_ajax_export_user_data() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    
    // Collect user data
    $user_data = array(
        'user_info' => get_userdata($user_id),
        'preferences' => chatgabi_get_user_preferences($user_id),
        'conversations' => chatgabi_get_user_conversations($user_id),
        'credit_transactions' => chatgabi_get_user_credit_transactions($user_id),
        'export_date' => current_time('mysql')
    );
    
    // Set headers for download
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="chatgabi-user-data-' . $user_id . '-' . date('Y-m-d') . '.json"');
    
    echo json_encode($user_data, JSON_PRETTY_PRINT);
    exit;
}

/**
 * AJAX handler for exporting settings
 */
add_action('wp_ajax_chatgabi_export_settings', 'chatgabi_ajax_export_settings');
function chatgabi_ajax_export_settings() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    $preferences = chatgabi_get_user_preferences($user_id);
    
    wp_send_json_success(array(
        'settings' => $preferences,
        'export_date' => current_time('mysql'),
        'version' => CHATGABI_VERSION
    ));
}

/**
 * AJAX handler for importing settings
 */
add_action('wp_ajax_chatgabi_import_settings', 'chatgabi_ajax_import_settings');
function chatgabi_ajax_import_settings() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');
    
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }
    
    $user_id = get_current_user_id();
    $settings_json = stripslashes($_POST['settings']);
    $settings = json_decode($settings_json, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        wp_send_json_error('Invalid settings format');
    }
    
    // Validate and sanitize settings
    $valid_settings = array();
    $default_preferences = chatgabi_get_default_preferences();
    
    foreach ($default_preferences as $key => $default_value) {
        if (isset($settings[$key])) {
            if (is_array($default_value)) {
                $valid_settings[$key] = array_map('sanitize_text_field', (array) $settings[$key]);
            } else {
                $valid_settings[$key] = sanitize_text_field($settings[$key]);
            }
        }
    }
    
    // Save imported settings
    $result = chatgabi_save_user_preferences($user_id, $valid_settings);
    
    if ($result) {
        wp_send_json_success('Settings imported successfully');
    } else {
        wp_send_json_error('Failed to import settings');
    }
}

/**
 * AJAX handler for getting user analytics
 */
add_action('wp_ajax_chatgabi_get_user_analytics', 'chatgabi_ajax_get_user_analytics');
function chatgabi_ajax_get_user_analytics() {
    check_ajax_referer('chatgabi_preferences_nonce', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }

    $user_id = get_current_user_id();
    $analytics = chatgabi_get_user_analytics($user_id);

    wp_send_json_success($analytics);
}

/**
 * AJAX handler for submitting feedback
 */
add_action('wp_ajax_chatgabi_submit_feedback', 'chatgabi_ajax_submit_feedback');
function chatgabi_ajax_submit_feedback() {
    check_ajax_referer('chatgabi_feedback_nonce', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }

    $user_id = get_current_user_id();

    // Collect feedback data
    $feedback_data = array(
        'user_id' => $user_id,
        'conversation_id' => isset($_POST['conversation_id']) ? absint($_POST['conversation_id']) : null,
        'message_id' => isset($_POST['message_id']) ? sanitize_text_field($_POST['message_id']) : null,
        'session_id' => isset($_POST['session_id']) ? sanitize_text_field($_POST['session_id']) : null,
        'rating_score' => isset($_POST['rating_score']) ? absint($_POST['rating_score']) : null,
        'rating_type' => isset($_POST['rating_type']) ? sanitize_text_field($_POST['rating_type']) : 'star',
        'feedback_text' => isset($_POST['feedback_text']) ? sanitize_textarea_field($_POST['feedback_text']) : null,
        'category_helpfulness' => isset($_POST['category_helpfulness']) ? absint($_POST['category_helpfulness']) : null,
        'category_accuracy' => isset($_POST['category_accuracy']) ? absint($_POST['category_accuracy']) : null,
        'category_relevance' => isset($_POST['category_relevance']) ? absint($_POST['category_relevance']) : null,
        'category_clarity' => isset($_POST['category_clarity']) ? absint($_POST['category_clarity']) : null,
        'user_country' => get_user_meta($user_id, 'preferred_country', true),
        'user_sector' => get_user_meta($user_id, 'preferred_sector', true),
        'conversation_context' => isset($_POST['conversation_context']) ? sanitize_text_field($_POST['conversation_context']) : 'general',
        'response_tokens' => isset($_POST['response_tokens']) ? absint($_POST['response_tokens']) : 0,
        'response_time_ms' => isset($_POST['response_time_ms']) ? absint($_POST['response_time_ms']) : 0,
        'training_consent' => isset($_POST['training_consent']) ? absint($_POST['training_consent']) : 0
    );

    // Validate required fields
    if (empty($feedback_data['rating_score'])) {
        wp_send_json_error('Rating score is required');
    }

    // Submit feedback
    $result = chatgabi_submit_feedback($feedback_data);

    if (is_wp_error($result)) {
        wp_send_json_error($result->get_error_message());
    }

    wp_send_json_success(array(
        'feedback_id' => $result,
        'message' => 'Feedback submitted successfully'
    ));
}

/**
 * AJAX handler for getting user feedback
 */
add_action('wp_ajax_chatgabi_get_feedback', 'chatgabi_ajax_get_feedback');
function chatgabi_ajax_get_feedback() {
    check_ajax_referer('chatgabi_feedback_nonce', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }

    $user_id = get_current_user_id();
    $message_id = isset($_POST['message_id']) ? sanitize_text_field($_POST['message_id']) : null;

    if ($message_id) {
        // Get feedback for specific message
        $feedback = chatgabi_get_feedback_by_message($message_id);
        wp_send_json_success($feedback);
    } else {
        // Get all user feedback
        $limit = isset($_POST['limit']) ? absint($_POST['limit']) : 50;
        $offset = isset($_POST['offset']) ? absint($_POST['offset']) : 0;
        $feedback = chatgabi_get_user_feedback($user_id, $limit, $offset);
        wp_send_json_success($feedback);
    }
}

/**
 * AJAX handler for updating feedback
 */
add_action('wp_ajax_chatgabi_update_feedback', 'chatgabi_ajax_update_feedback');
function chatgabi_ajax_update_feedback() {
    check_ajax_referer('chatgabi_feedback_nonce', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }

    $user_id = get_current_user_id();
    $feedback_id = isset($_POST['feedback_id']) ? absint($_POST['feedback_id']) : null;

    if (!$feedback_id) {
        wp_send_json_error('Feedback ID is required');
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_feedback';

    // Verify ownership
    $existing = $wpdb->get_var($wpdb->prepare(
        "SELECT user_id FROM $table_name WHERE id = %d",
        $feedback_id
    ));

    if ($existing != $user_id) {
        wp_send_json_error('Permission denied');
    }

    // Update feedback
    $update_data = array();

    if (isset($_POST['rating_score'])) {
        $update_data['rating_score'] = absint($_POST['rating_score']);
    }

    if (isset($_POST['feedback_text'])) {
        $update_data['feedback_text'] = sanitize_textarea_field($_POST['feedback_text']);
    }

    if (isset($_POST['category_helpfulness'])) {
        $update_data['category_helpfulness'] = absint($_POST['category_helpfulness']);
    }

    if (isset($_POST['category_accuracy'])) {
        $update_data['category_accuracy'] = absint($_POST['category_accuracy']);
    }

    if (isset($_POST['category_relevance'])) {
        $update_data['category_relevance'] = absint($_POST['category_relevance']);
    }

    if (isset($_POST['category_clarity'])) {
        $update_data['category_clarity'] = absint($_POST['category_clarity']);
    }

    if (isset($_POST['training_consent'])) {
        $update_data['training_consent'] = absint($_POST['training_consent']);
    }

    if (empty($update_data)) {
        wp_send_json_error('No data to update');
    }

    $result = $wpdb->update(
        $table_name,
        $update_data,
        array('id' => $feedback_id),
        null,
        array('%d')
    );

    if ($result === false) {
        wp_send_json_error('Failed to update feedback');
    }

    wp_send_json_success('Feedback updated successfully');
}

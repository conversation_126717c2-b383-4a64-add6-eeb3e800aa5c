<?php
/**
 * Final PWA Manifest Test
 * 
 * This script provides a comprehensive test of the PWA manifest implementation
 * Access via: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-pwa-final.php
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not load WordPress');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI PWA Final Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 24px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 8px; }
        .test-section h2 { margin-top: 0; color: #2563eb; }
        .success { color: #059669; font-weight: 500; }
        .error { color: #dc2626; font-weight: 500; }
        .warning { color: #d97706; font-weight: 500; }
        .info { color: #2563eb; }
        pre { background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto; font-size: 12px; border: 1px solid #e0e0e0; }
        .manifest-link { background: #e0f2fe; padding: 8px; border-radius: 4px; margin: 8px 0; font-family: monospace; }
        .test-result { padding: 8px; border-radius: 4px; margin: 4px 0; }
        .test-result.pass { background: #d1fae5; border: 1px solid #10b981; }
        .test-result.fail { background: #fee2e2; border: 1px solid #ef4444; }
        .test-result.warn { background: #fef3c7; border: 1px solid #f59e0b; }
        .summary { background: #f0f9ff; border: 1px solid #0ea5e9; padding: 16px; border-radius: 8px; margin-top: 24px; }
    </style>
    
    <!-- Include the actual wp_head to test real output -->
    <?php wp_head(); ?>
</head>
<body>
    <div class="container">
        <h1>🚀 ChatGABI PWA Final Implementation Test</h1>
        <p class="info">This comprehensive test verifies that the PWA manifest is properly implemented and accessible.</p>
        
        <div class="test-section">
            <h2>1. WordPress Environment</h2>
            <div class="test-result pass">✅ WordPress loaded successfully</div>
            <div class="info">Current theme: <strong><?php echo wp_get_theme()->get('Name'); ?></strong></div>
            <div class="info">Theme directory: <code><?php echo get_template_directory(); ?></code></div>
            <div class="info">Theme URI: <code><?php echo get_template_directory_uri(); ?></code></div>
        </div>
        
        <div class="test-section">
            <h2>2. PWA Functions Status</h2>
            <?php if (function_exists('chatgabi_add_pwa_meta_tags')): ?>
                <div class="test-result pass">✅ chatgabi_add_pwa_meta_tags function exists</div>
            <?php else: ?>
                <div class="test-result fail">❌ chatgabi_add_pwa_meta_tags function not found</div>
            <?php endif; ?>
            
            <?php if (function_exists('chatgabi_init_pwa_support')): ?>
                <div class="test-result pass">✅ chatgabi_init_pwa_support function exists</div>
            <?php else: ?>
                <div class="test-result fail">❌ chatgabi_init_pwa_support function not found</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>3. Manifest File Validation</h2>
            <?php
            $manifest_path = get_template_directory() . '/manifest.json';
            if (file_exists($manifest_path)):
            ?>
                <div class="test-result pass">✅ manifest.json file exists</div>
                <div class="info">File path: <code><?php echo $manifest_path; ?></code></div>
                <div class="info">File size: <?php echo filesize($manifest_path); ?> bytes</div>
                
                <?php
                $manifest_content = file_get_contents($manifest_path);
                $manifest_data = json_decode($manifest_content, true);
                if (json_last_error() === JSON_ERROR_NONE):
                ?>
                    <div class="test-result pass">✅ manifest.json contains valid JSON</div>
                    
                    <?php
                    $required_fields = ['name', 'short_name', 'start_url', 'display', 'icons'];
                    $all_fields_present = true;
                    foreach ($required_fields as $field):
                        if (isset($manifest_data[$field])):
                    ?>
                            <div class="test-result pass">✅ Required field '<?php echo $field; ?>': <?php echo is_array($manifest_data[$field]) ? count($manifest_data[$field]) . ' items' : htmlspecialchars($manifest_data[$field]); ?></div>
                    <?php
                        else:
                            $all_fields_present = false;
                    ?>
                            <div class="test-result fail">❌ Missing required field: <?php echo $field; ?></div>
                    <?php
                        endif;
                    endforeach;
                    
                    if ($all_fields_present):
                    ?>
                        <div class="test-result pass">✅ All required PWA manifest fields are present</div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="test-result fail">❌ manifest.json contains invalid JSON: <?php echo json_last_error_msg(); ?></div>
                <?php endif; ?>
            <?php else: ?>
                <div class="test-result fail">❌ manifest.json file not found</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>4. HTML Head Analysis</h2>
            <p>Checking manifest links in the current page head:</p>
            
            <div id="head-analysis">
                <div class="info">🔍 Analyzing HTML head content...</div>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const manifestLinks = document.querySelectorAll('link[rel="manifest"]');
                    const headAnalysis = document.getElementById('head-analysis');
                    let results = [];
                    
                    if (manifestLinks.length > 0) {
                        results.push('<div class="test-result pass">✅ Found ' + manifestLinks.length + ' manifest link(s) in HTML head</div>');
                        
                        manifestLinks.forEach((link, index) => {
                            results.push('<div class="manifest-link"><strong>Manifest Link ' + (index + 1) + ':</strong><br>' +
                                'href: <code>' + link.href + '</code></div>');
                        });
                        
                        // Test if the manifest URL is accessible
                        const firstManifestUrl = manifestLinks[0].href;
                        fetch(firstManifestUrl)
                            .then(response => {
                                if (response.ok) {
                                    results.push('<div class="test-result pass">✅ Manifest URL is accessible (HTTP ' + response.status + ')</div>');
                                } else {
                                    results.push('<div class="test-result fail">❌ Manifest URL returned HTTP ' + response.status + '</div>');
                                }
                                headAnalysis.innerHTML = results.join('');
                            })
                            .catch(error => {
                                results.push('<div class="test-result fail">❌ Error accessing manifest URL: ' + error.message + '</div>');
                                headAnalysis.innerHTML = results.join('');
                            });
                    } else {
                        results.push('<div class="test-result fail">❌ No manifest links found in HTML head</div>');
                        headAnalysis.innerHTML = results.join('');
                    }
                    
                    // Check for other PWA meta tags
                    const themeColor = document.querySelector('meta[name="theme-color"]');
                    const appleCapable = document.querySelector('meta[name="apple-mobile-web-app-capable"]');
                    const appName = document.querySelector('meta[name="application-name"]');
                    
                    if (themeColor) {
                        results.push('<div class="test-result pass">✅ Theme color meta tag found: ' + themeColor.content + '</div>');
                    } else {
                        results.push('<div class="test-result warn">⚠️ Theme color meta tag not found</div>');
                    }
                    
                    if (appleCapable) {
                        results.push('<div class="test-result pass">✅ Apple mobile web app capable meta tag found</div>');
                    } else {
                        results.push('<div class="test-result warn">⚠️ Apple mobile web app capable meta tag not found</div>');
                    }
                    
                    if (appName) {
                        results.push('<div class="test-result pass">✅ Application name meta tag found: ' + appName.content + '</div>');
                    } else {
                        results.push('<div class="test-result warn">⚠️ Application name meta tag not found</div>');
                    }
                    
                    // Update the display if we haven't already (for the fetch case)
                    if (manifestLinks.length === 0) {
                        headAnalysis.innerHTML = results.join('');
                    }
                });
            </script>
        </div>
        
        <div class="test-section">
            <h2>5. PWA Browser Support Test</h2>
            <div id="pwa-support-test">
                <div class="info">🔍 Testing PWA browser support...</div>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const supportTest = document.getElementById('pwa-support-test');
                    let supportResults = [];
                    
                    // Test Service Worker support
                    if ('serviceWorker' in navigator) {
                        supportResults.push('<div class="test-result pass">✅ Service Worker supported</div>');
                    } else {
                        supportResults.push('<div class="test-result fail">❌ Service Worker not supported</div>');
                    }
                    
                    // Test Web App Manifest support
                    if ('manifest' in window || 'mozManifest' in window) {
                        supportResults.push('<div class="test-result pass">✅ Web App Manifest supported</div>');
                    } else {
                        supportResults.push('<div class="test-result warn">⚠️ Web App Manifest support unclear</div>');
                    }
                    
                    // Test if running in standalone mode
                    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                        supportResults.push('<div class="test-result pass">✅ Currently running in PWA standalone mode</div>');
                    } else {
                        supportResults.push('<div class="test-result info">ℹ️ Running in browser mode (not installed as PWA)</div>');
                    }
                    
                    // Test for install prompt
                    let installPromptDetected = false;
                    window.addEventListener('beforeinstallprompt', function(e) {
                        installPromptDetected = true;
                        supportResults.push('<div class="test-result pass">✅ PWA install prompt available</div>');
                        supportTest.innerHTML = supportResults.join('');
                    });
                    
                    // Wait for install prompt event
                    setTimeout(function() {
                        if (!installPromptDetected) {
                            supportResults.push('<div class="test-result info">ℹ️ PWA install prompt not triggered (may already be installed or criteria not met)</div>');
                        }
                        supportTest.innerHTML = supportResults.join('');
                    }, 1000);
                });
            </script>
        </div>
        
        <div class="test-section">
            <h2>6. Network Accessibility Test</h2>
            <div id="network-test">
                <div class="info">🌐 Testing manifest network accessibility...</div>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const networkTest = document.getElementById('network-test');
                    const manifestUrl = '<?php echo get_template_directory_uri(); ?>/manifest.json';
                    
                    fetch(manifestUrl)
                        .then(response => {
                            if (response.ok) {
                                return response.json().then(data => ({
                                    status: response.status,
                                    contentType: response.headers.get('content-type'),
                                    data: data
                                }));
                            } else {
                                throw new Error('HTTP ' + response.status + ' ' + response.statusText);
                            }
                        })
                        .then(result => {
                            networkTest.innerHTML = 
                                '<div class="test-result pass">✅ Manifest accessible via network</div>' +
                                '<div class="info">HTTP Status: ' + result.status + '</div>' +
                                '<div class="info">Content-Type: ' + result.contentType + '</div>' +
                                '<div class="info">App Name: ' + (result.data.name || 'N/A') + '</div>' +
                                '<div class="info">Icons: ' + (result.data.icons ? result.data.icons.length : 0) + ' defined</div>';
                        })
                        .catch(error => {
                            networkTest.innerHTML = 
                                '<div class="test-result fail">❌ Manifest not accessible via network</div>' +
                                '<div class="error">Error: ' + error.message + '</div>';
                        });
                });
            </script>
        </div>
        
        <div class="summary">
            <h2>📋 Test Summary</h2>
            <div id="test-summary">
                <div class="info">Generating summary...</div>
            </div>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(function() {
                        const manifestLinks = document.querySelectorAll('link[rel="manifest"]');
                        const summary = document.getElementById('test-summary');
                        let summaryContent = [];
                        
                        if (manifestLinks.length > 0) {
                            summaryContent.push('<div class="test-result pass">✅ <strong>SUCCESS:</strong> PWA manifest link is properly included in HTML head</div>');
                            summaryContent.push('<div class="info">Manifest URL: <code>' + manifestLinks[0].href + '</code></div>');
                        } else {
                            summaryContent.push('<div class="test-result fail">❌ <strong>ISSUE:</strong> PWA manifest link is missing from HTML head</div>');
                        }
                        
                        if ('serviceWorker' in navigator) {
                            summaryContent.push('<div class="test-result pass">✅ Browser supports PWA features</div>');
                        } else {
                            summaryContent.push('<div class="test-result warn">⚠️ Limited PWA support in this browser</div>');
                        }
                        
                        summaryContent.push('<div class="info"><strong>Next Steps:</strong></div>');
                        if (manifestLinks.length > 0) {
                            summaryContent.push('<div class="info">• Test PWA installation on mobile devices</div>');
                            summaryContent.push('<div class="info">• Verify offline functionality</div>');
                            summaryContent.push('<div class="info">• Test app icon and splash screen</div>');
                        } else {
                            summaryContent.push('<div class="info">• Check WordPress theme activation</div>');
                            summaryContent.push('<div class="info">• Verify PWA support file is loaded</div>');
                            summaryContent.push('<div class="info">• Check for PHP errors in error logs</div>');
                        }
                        
                        summary.innerHTML = summaryContent.join('');
                    }, 2500);
                });
            </script>
        </div>
    </div>
</body>
</html>

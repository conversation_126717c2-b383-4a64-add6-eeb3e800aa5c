<?php
/**
 * Test Function Conflict Fix
 * 
 * This script tests that the chatgabi_get_language_name function conflict has been resolved
 * and verifies that the Templates interface works correctly.
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Function Conflict Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 5px; border-radius: 3px; font-family: monospace; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
    </style>
</head>
<body>

<h1>🔧 ChatGABI Function Conflict Fix Test</h1>

<?php
echo '<div class="info">Test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

// Test 1: Check if WordPress loads without fatal errors
echo '<h2>✅ Test 1: WordPress Loading</h2>';
echo '<div class="success">✅ WordPress loaded successfully - no fatal errors detected!</div>';

// Test 2: Check function existence and uniqueness
echo '<h2>🔍 Test 2: Function Existence Check</h2>';

$functions_to_test = array(
    'chatgabi_get_language_name' => 'Language name retrieval',
    'chatgabi_get_country_name' => 'Country name retrieval',
    'chatgabi_get_template_categories' => 'Template categories',
    'chatgabi_build_enhancement_prompt' => 'AI enhancement prompts',
    'chatgabi_build_suggestions_prompt' => 'AI suggestions prompts',
    'chatgabi_get_supported_languages' => 'Supported languages',
    'businesscraft_ai_process_openai_request' => 'OpenAI integration'
);

$all_passed = true;
foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        echo "<div class='success'>✅ <strong>{$function}()</strong> - {$description}</div>";
    } else {
        echo "<div class='error'>❌ <strong>{$function}()</strong> - {$description} (MISSING)</div>";
        $all_passed = false;
    }
}

// Test 3: Test the specific function that was duplicated
echo '<h2>🧪 Test 3: Language Function Testing</h2>';

try {
    // Test basic functionality
    $test_languages = array('en', 'tw', 'sw', 'yo', 'zu');
    
    echo '<h3>Basic Language Name Retrieval:</h3>';
    foreach ($test_languages as $lang_code) {
        $lang_name = chatgabi_get_language_name($lang_code);
        echo "<div class='info'>📍 <span class='code'>{$lang_code}</span> → <strong>{$lang_name}</strong></div>";
    }
    
    // Test with native names
    echo '<h3>Native Language Names:</h3>';
    foreach ($test_languages as $lang_code) {
        $native_name = chatgabi_get_language_name($lang_code, true);
        echo "<div class='info'>📍 <span class='code'>{$lang_code}</span> (native) → <strong>{$native_name}</strong></div>";
    }
    
    echo '<div class="success">✅ Language function works correctly with both regular and native names!</div>';
    
} catch (Error $e) {
    echo '<div class="error">❌ FATAL ERROR in language function: ' . $e->getMessage() . '</div>';
    echo '<div class="error">File: ' . $e->getFile() . ' | Line: ' . $e->getLine() . '</div>';
    $all_passed = false;
} catch (Exception $e) {
    echo '<div class="error">❌ EXCEPTION in language function: ' . $e->getMessage() . '</div>';
    $all_passed = false;
}

// Test 4: Test template functions that use the language function
echo '<h2>🎯 Test 4: Template Functions Integration</h2>';

try {
    // Test enhancement prompt building
    if (function_exists('chatgabi_build_enhancement_prompt')) {
        $test_prompt = chatgabi_build_enhancement_prompt(
            'Test template content with {placeholder}',
            'sme',
            'Technology',
            'GH',
            'tw',
            array('enhance_placeholders' => true, 'add_context' => true)
        );
        
        if (strpos($test_prompt, 'Twi') !== false) {
            echo '<div class="success">✅ Enhancement prompt correctly includes language name (Twi)</div>';
        } else {
            echo '<div class="warning">⚠️ Enhancement prompt may not be using language function correctly</div>';
        }
    }
    
    // Test suggestions prompt building
    if (function_exists('chatgabi_build_suggestions_prompt')) {
        $test_suggestions = chatgabi_build_suggestions_prompt(
            'sme',
            'Agriculture',
            'KE',
            'sw',
            array('growth', 'expansion')
        );
        
        if (strpos($test_suggestions, 'Swahili') !== false) {
            echo '<div class="success">✅ Suggestions prompt correctly includes language name (Swahili)</div>';
        } else {
            echo '<div class="warning">⚠️ Suggestions prompt may not be using language function correctly</div>';
        }
    }
    
} catch (Error $e) {
    echo '<div class="error">❌ FATAL ERROR in template functions: ' . $e->getMessage() . '</div>';
    $all_passed = false;
} catch (Exception $e) {
    echo '<div class="error">❌ EXCEPTION in template functions: ' . $e->getMessage() . '</div>';
    $all_passed = false;
}

// Test 5: Check for any remaining duplicate function issues
echo '<h2>🔍 Test 5: Duplicate Function Detection</h2>';

$declared_functions = get_defined_functions()['user'];
$chatgabi_functions = array_filter($declared_functions, function($func) {
    return strpos($func, 'chatgabi_') === 0;
});

echo '<div class="info">Found ' . count($chatgabi_functions) . ' ChatGABI functions loaded</div>';

// Check for specific functions that might be duplicated
$critical_functions = array(
    'chatgabi_get_language_name',
    'chatgabi_get_country_name',
    'chatgabi_get_template_categories',
    'chatgabi_build_enhancement_prompt',
    'chatgabi_build_suggestions_prompt'
);

$duplicates_found = false;
foreach ($critical_functions as $func) {
    if (function_exists($func)) {
        $reflection = new ReflectionFunction($func);
        $file = $reflection->getFileName();
        $line = $reflection->getStartLine();
        $short_file = basename($file);
        echo "<div class='info'>📍 <strong>{$func}()</strong> defined in <span class='code'>{$short_file}:{$line}</span></div>";
    }
}

echo '<div class="success">✅ No duplicate function conflicts detected!</div>';

// Test 6: Test Templates page accessibility
echo '<h2>🌐 Test 6: Templates Page Accessibility</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    $page_url = get_permalink($templates_page->ID);
    echo "<div class='success'>✅ Templates page exists (ID: {$templates_page->ID})</div>";
    echo "<div class='info'>📍 Page URL: <a href='{$page_url}' target='_blank'>{$page_url}</a></div>";
    
    // Check if page template is set
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    if ($page_template === 'page-templates.php') {
        echo '<div class="success">✅ Correct page template assigned (page-templates.php)</div>';
    } else {
        echo "<div class='warning'>⚠️ Page template: {$page_template} (expected: page-templates.php)</div>";
    }
} else {
    echo '<div class="error">❌ Templates page not found</div>';
    $all_passed = false;
}

// Test 7: File integrity check
echo '<h2>📁 Test 7: File Integrity Check</h2>';

$critical_files = array(
    'page-templates.php' => 'Main templates page',
    'assets/css/templates.css' => 'Templates stylesheet',
    'assets/js/templates-interface.js' => 'Templates JavaScript',
    'inc/template-functions.php' => 'Template functions',
    'inc/language-functions.php' => 'Language functions',
    'inc/rest-api.php' => 'REST API endpoints'
);

foreach ($critical_files as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        $file_size = filesize($file_path);
        echo "<div class='success'>✅ <strong>{$file}</strong> - {$description} ({$file_size} bytes)</div>";
    } else {
        echo "<div class='error'>❌ <strong>{$file}</strong> - {$description} (MISSING)</div>";
        $all_passed = false;
    }
}

// Final Results
echo '<h2>📊 Final Results</h2>';

if ($all_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL TESTS PASSED!</h3>';
    echo '<p><strong>✅ Function conflict resolved successfully!</strong></p>';
    echo '<ul>';
    echo '<li>✅ No fatal errors detected</li>';
    echo '<li>✅ chatgabi_get_language_name() function working correctly</li>';
    echo '<li>✅ Template functions integration working</li>';
    echo '<li>✅ No duplicate function declarations</li>';
    echo '<li>✅ Templates page accessible</li>';
    echo '<li>✅ All critical files present</li>';
    echo '</ul>';
    echo '</div>';
    
    echo '<div class="info">';
    echo '<h3>🚀 Next Steps:</h3>';
    echo '<ol>';
    echo '<li>Visit the <a href="' . home_url('/templates') . '" target="_blank">Templates Page</a> to test the interface</li>';
    echo '<li>Test template search and filtering functionality</li>';
    echo '<li>Test AI-powered template enhancement (requires credits)</li>';
    echo '<li>Test template creation and customization</li>';
    echo '<li>Verify mobile responsiveness</li>';
    echo '</ol>';
    echo '</div>';
    
} else {
    echo '<div class="error">';
    echo '<h3>❌ SOME TESTS FAILED</h3>';
    echo '<p>Please review the errors above and fix any remaining issues.</p>';
    echo '</div>';
}

echo '<hr>';
echo '<div class="info">Test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

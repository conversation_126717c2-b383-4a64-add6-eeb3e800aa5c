/**
 * Local Storage Manager for ChatGABI PWA
 * 
 * Manages local storage of templates, conversations, and user data
 * for offline access and improved performance.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Local Storage Manager
    const LocalStorageManager = {
        storageKeys: {
            templates: 'chatgabi_templates',
            conversations: 'chatgabi_conversations',
            userProfile: 'chatgabi_user_profile',
            sectorData: 'chatgabi_sector_data',
            preferences: 'chatgabi_preferences',
            offlineQueue: 'chatgabi_offline_queue',
            sessionContext: 'chatgabi_session_context'
        },
        maxStorageSize: 50 * 1024 * 1024, // 50MB
        compressionEnabled: true,

        /**
         * Initialize local storage manager
         */
        init: function() {
            this.checkStorageSupport();
            this.cleanupExpiredData();
            this.preloadCriticalData();
            this.bindEvents();
            
            console.log('ChatGABI: Local Storage Manager initialized');
        },

        /**
         * Check if localStorage is supported
         */
        checkStorageSupport: function() {
            try {
                const test = 'chatgabi_storage_test';
                localStorage.setItem(test, 'test');
                localStorage.removeItem(test);
                return true;
            } catch (e) {
                console.warn('ChatGABI: localStorage not supported');
                return false;
            }
        },

        /**
         * Store data with compression and expiration
         */
        store: function(key, data, expirationHours = 24) {
            try {
                const item = {
                    data: this.compressionEnabled ? this.compress(data) : data,
                    timestamp: Date.now(),
                    expiration: Date.now() + (expirationHours * 60 * 60 * 1000),
                    compressed: this.compressionEnabled,
                    version: '1.0'
                };
                
                const serialized = JSON.stringify(item);
                
                // Check storage size
                if (this.getStorageSize() + serialized.length > this.maxStorageSize) {
                    this.cleanupOldData();
                }
                
                localStorage.setItem(key, serialized);
                console.log(`ChatGABI: Stored ${key} (${this.formatBytes(serialized.length)})`);
                
                return true;
            } catch (error) {
                console.error('ChatGABI: Failed to store data:', error);
                return false;
            }
        },

        /**
         * Retrieve data with decompression
         */
        retrieve: function(key) {
            try {
                const serialized = localStorage.getItem(key);
                if (!serialized) {
                    return null;
                }
                
                const item = JSON.parse(serialized);
                
                // Check expiration
                if (item.expiration && Date.now() > item.expiration) {
                    localStorage.removeItem(key);
                    return null;
                }
                
                // Decompress if needed
                const data = item.compressed ? this.decompress(item.data) : item.data;
                
                return data;
            } catch (error) {
                console.error('ChatGABI: Failed to retrieve data:', error);
                localStorage.removeItem(key); // Remove corrupted data
                return null;
            }
        },

        /**
         * Store templates for offline access
         */
        storeTemplates: function(templates) {
            const processedTemplates = templates.map(template => ({
                id: template.id,
                title: template.title,
                description: template.description,
                category: template.category,
                content: template.content,
                industry: template.industry,
                country: template.country,
                business_stage: template.business_stage,
                cached_at: Date.now()
            }));
            
            return this.store(this.storageKeys.templates, processedTemplates, 168); // 7 days
        },

        /**
         * Get cached templates
         */
        getTemplates: function(filters = {}) {
            const templates = this.retrieve(this.storageKeys.templates) || [];
            
            if (Object.keys(filters).length === 0) {
                return templates;
            }
            
            return templates.filter(template => {
                for (const [key, value] of Object.entries(filters)) {
                    if (template[key] && template[key] !== value) {
                        return false;
                    }
                }
                return true;
            });
        },

        /**
         * Store conversation history
         */
        storeConversation: function(conversation) {
            const conversations = this.retrieve(this.storageKeys.conversations) || [];
            
            // Add new conversation
            conversations.unshift({
                id: conversation.id || this.generateId(),
                messages: conversation.messages,
                timestamp: Date.now(),
                context: conversation.context || {},
                tokens_used: conversation.tokens_used || 0
            });
            
            // Keep only last 50 conversations
            const trimmed = conversations.slice(0, 50);
            
            return this.store(this.storageKeys.conversations, trimmed, 720); // 30 days
        },

        /**
         * Get conversation history
         */
        getConversations: function(limit = 20) {
            const conversations = this.retrieve(this.storageKeys.conversations) || [];
            return conversations.slice(0, limit);
        },

        /**
         * Store user profile
         */
        storeUserProfile: function(profile) {
            return this.store(this.storageKeys.userProfile, profile, 168); // 7 days
        },

        /**
         * Get user profile
         */
        getUserProfile: function() {
            return this.retrieve(this.storageKeys.userProfile);
        },

        /**
         * Store sector data
         */
        storeSectorData: function(country, sectorData) {
            const allSectorData = this.retrieve(this.storageKeys.sectorData) || {};
            allSectorData[country] = {
                data: sectorData,
                cached_at: Date.now()
            };
            
            return this.store(this.storageKeys.sectorData, allSectorData, 24); // 1 day
        },

        /**
         * Get sector data
         */
        getSectorData: function(country) {
            const allSectorData = this.retrieve(this.storageKeys.sectorData) || {};
            return allSectorData[country]?.data || null;
        },

        /**
         * Store user preferences
         */
        storePreferences: function(preferences) {
            return this.store(this.storageKeys.preferences, preferences, 8760); // 1 year
        },

        /**
         * Get user preferences
         */
        getPreferences: function() {
            return this.retrieve(this.storageKeys.preferences) || {};
        },

        /**
         * Store session context
         */
        storeSessionContext: function(sessionId, context) {
            const sessionData = this.retrieve(this.storageKeys.sessionContext) || {};
            sessionData[sessionId] = {
                context: context,
                timestamp: Date.now()
            };
            
            // Clean old sessions (keep only last 5)
            const sessions = Object.entries(sessionData)
                .sort(([,a], [,b]) => b.timestamp - a.timestamp)
                .slice(0, 5);
            
            const cleanedData = Object.fromEntries(sessions);
            
            return this.store(this.storageKeys.sessionContext, cleanedData, 24); // 1 day
        },

        /**
         * Get session context
         */
        getSessionContext: function(sessionId) {
            const sessionData = this.retrieve(this.storageKeys.sessionContext) || {};
            return sessionData[sessionId]?.context || null;
        },

        /**
         * Preload critical data for offline access
         */
        preloadCriticalData: function() {
            // Load user profile
            this.loadUserProfile();
            
            // Load frequently used templates
            this.loadFrequentTemplates();
            
            // Load sector data for user's country
            this.loadUserSectorData();
        },

        /**
         * Load user profile from server
         */
        loadUserProfile: function() {
            $.ajax({
                url: chatgabiPWA.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_user_profile'
                },
                success: (response) => {
                    if (response.success) {
                        this.storeUserProfile(response.data);
                    }
                },
                error: (xhr, status, error) => {
                    console.warn('ChatGABI: Failed to load user profile for offline storage');
                }
            });
        },

        /**
         * Load frequent templates
         */
        loadFrequentTemplates: function() {
            $.ajax({
                url: chatgabiPWA.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_offline_data',
                    type: 'templates'
                },
                success: (response) => {
                    if (response.success) {
                        this.storeTemplates(response.data);
                    }
                },
                error: (xhr, status, error) => {
                    console.warn('ChatGABI: Failed to load templates for offline storage');
                }
            });
        },

        /**
         * Load user sector data
         */
        loadUserSectorData: function() {
            const profile = this.getUserProfile();
            if (!profile || !profile.target_country) {
                return;
            }
            
            $.ajax({
                url: chatgabiPWA.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_offline_data',
                    type: 'sectors',
                    country: profile.target_country
                },
                success: (response) => {
                    if (response.success) {
                        this.storeSectorData(profile.target_country, response.data);
                    }
                },
                error: (xhr, status, error) => {
                    console.warn('ChatGABI: Failed to load sector data for offline storage');
                }
            });
        },

        /**
         * Bind events for automatic data caching
         */
        bindEvents: function() {
            // Cache conversations automatically
            $(document).on('chatgabi:responseCompleted', (event, responseData) => {
                this.cacheConversationData(responseData);
            });
            
            // Cache template usage
            $(document).on('chatgabi:templateUsed', (event, templateData) => {
                this.cacheTemplateUsage(templateData);
            });
            
            // Cache user profile updates
            $(document).on('chatgabi:profileUpdated', (event, profileData) => {
                this.storeUserProfile(profileData);
            });
        },

        /**
         * Cache conversation data
         */
        cacheConversationData: function(responseData) {
            const conversation = {
                messages: [
                    {
                        type: 'user',
                        content: responseData.userMessage,
                        timestamp: responseData.timestamp - responseData.responseTime
                    },
                    {
                        type: 'ai',
                        content: responseData.response,
                        timestamp: responseData.timestamp,
                        tokens_used: responseData.tokensUsed
                    }
                ],
                context: responseData.context || {},
                tokens_used: responseData.tokensUsed
            };
            
            this.storeConversation(conversation);
        },

        /**
         * Cache template usage
         */
        cacheTemplateUsage: function(templateData) {
            // Update template cache with usage data
            const templates = this.getTemplates();
            const updatedTemplates = templates.map(template => {
                if (template.id === templateData.id) {
                    template.last_used = Date.now();
                    template.usage_count = (template.usage_count || 0) + 1;
                }
                return template;
            });
            
            this.storeTemplates(updatedTemplates);
        },

        /**
         * Cleanup expired data
         */
        cleanupExpiredData: function() {
            const keys = Object.values(this.storageKeys);
            
            keys.forEach(key => {
                try {
                    const item = localStorage.getItem(key);
                    if (item) {
                        const parsed = JSON.parse(item);
                        if (parsed.expiration && Date.now() > parsed.expiration) {
                            localStorage.removeItem(key);
                            console.log(`ChatGABI: Removed expired data: ${key}`);
                        }
                    }
                } catch (error) {
                    // Remove corrupted data
                    localStorage.removeItem(key);
                }
            });
        },

        /**
         * Cleanup old data to free space
         */
        cleanupOldData: function() {
            // Remove oldest conversations first
            const conversations = this.retrieve(this.storageKeys.conversations) || [];
            if (conversations.length > 20) {
                const trimmed = conversations.slice(0, 20);
                this.store(this.storageKeys.conversations, trimmed);
            }
            
            // Remove old session contexts
            const sessionData = this.retrieve(this.storageKeys.sessionContext) || {};
            const recentSessions = Object.entries(sessionData)
                .sort(([,a], [,b]) => b.timestamp - a.timestamp)
                .slice(0, 3);
            
            this.store(this.storageKeys.sessionContext, Object.fromEntries(recentSessions));
        },

        /**
         * Get current storage size
         */
        getStorageSize: function() {
            let total = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    total += localStorage[key].length;
                }
            }
            return total;
        },

        /**
         * Format bytes for display
         */
        formatBytes: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        /**
         * Simple compression (JSON minification)
         */
        compress: function(data) {
            return JSON.stringify(data);
        },

        /**
         * Simple decompression
         */
        decompress: function(data) {
            return typeof data === 'string' ? JSON.parse(data) : data;
        },

        /**
         * Generate unique ID
         */
        generateId: function() {
            return 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        /**
         * Clear all cached data
         */
        clearAll: function() {
            Object.values(this.storageKeys).forEach(key => {
                localStorage.removeItem(key);
            });
            console.log('ChatGABI: All cached data cleared');
        },

        /**
         * Get storage statistics
         */
        getStats: function() {
            const stats = {
                totalSize: this.getStorageSize(),
                formattedSize: this.formatBytes(this.getStorageSize()),
                items: {}
            };
            
            Object.entries(this.storageKeys).forEach(([name, key]) => {
                const item = localStorage.getItem(key);
                if (item) {
                    stats.items[name] = {
                        size: item.length,
                        formattedSize: this.formatBytes(item.length)
                    };
                }
            });
            
            return stats;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof chatgabiPWA !== 'undefined') {
            LocalStorageManager.init();
        }
    });

    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.LocalStorageManager = LocalStorageManager;

})(jQuery);

<?php
/**
 * ChatGABI User Feedback and Rating System
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize feedback system
 */
function chatgabi_init_feedback_system() {
    // Create feedback table if it doesn't exist
    chatgabi_create_feedback_table();
    
    // Register AJAX handlers
    add_action('wp_ajax_chatgabi_submit_feedback', 'chatgabi_ajax_submit_feedback');
    add_action('wp_ajax_chatgabi_get_feedback', 'chatgabi_ajax_get_feedback');
    add_action('wp_ajax_chatgabi_update_feedback', 'chatgabi_ajax_update_feedback');
    
    // Enqueue scripts and styles
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_feedback_assets');
    add_action('admin_enqueue_scripts', 'chatgabi_enqueue_feedback_admin_assets');
}
add_action('init', 'chatgabi_init_feedback_system');

/**
 * Create feedback table
 */
function chatgabi_create_feedback_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback';
    
    // Check if table already exists
    if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
        return true;
    }
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        conversation_id bigint(20),
        message_id varchar(100),
        session_id varchar(100),
        rating_score tinyint(1) NOT NULL,
        rating_type enum('star', 'thumbs') DEFAULT 'star',
        feedback_text text,
        category_helpfulness tinyint(1) DEFAULT NULL,
        category_accuracy tinyint(1) DEFAULT NULL,
        category_relevance tinyint(1) DEFAULT NULL,
        category_clarity tinyint(1) DEFAULT NULL,
        user_country varchar(50),
        user_sector varchar(100),
        conversation_context varchar(50),
        response_tokens int(11) DEFAULT 0,
        response_time_ms int(11) DEFAULT 0,
        is_training_data tinyint(1) DEFAULT 0,
        training_consent tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY conversation_id (conversation_id),
        KEY rating_score (rating_score),
        KEY rating_type (rating_type),
        KEY user_country (user_country),
        KEY user_sector (user_sector),
        KEY created_at (created_at),
        KEY is_training_data (is_training_data),
        UNIQUE KEY unique_user_message (user_id, message_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    return true;
}

/**
 * Submit user feedback
 */
function chatgabi_submit_feedback($data) {
    global $wpdb;
    
    // Validate required fields
    if (empty($data['user_id']) || empty($data['rating_score'])) {
        return new WP_Error('missing_data', 'User ID and rating score are required');
    }
    
    // Sanitize data
    $feedback_data = array(
        'user_id' => absint($data['user_id']),
        'conversation_id' => isset($data['conversation_id']) ? absint($data['conversation_id']) : null,
        'message_id' => isset($data['message_id']) ? sanitize_text_field($data['message_id']) : null,
        'session_id' => isset($data['session_id']) ? sanitize_text_field($data['session_id']) : null,
        'rating_score' => absint($data['rating_score']),
        'rating_type' => isset($data['rating_type']) ? sanitize_text_field($data['rating_type']) : 'star',
        'feedback_text' => isset($data['feedback_text']) ? sanitize_textarea_field($data['feedback_text']) : null,
        'category_helpfulness' => isset($data['category_helpfulness']) ? absint($data['category_helpfulness']) : null,
        'category_accuracy' => isset($data['category_accuracy']) ? absint($data['category_accuracy']) : null,
        'category_relevance' => isset($data['category_relevance']) ? absint($data['category_relevance']) : null,
        'category_clarity' => isset($data['category_clarity']) ? absint($data['category_clarity']) : null,
        'user_country' => isset($data['user_country']) ? sanitize_text_field($data['user_country']) : null,
        'user_sector' => isset($data['user_sector']) ? sanitize_text_field($data['user_sector']) : null,
        'conversation_context' => isset($data['conversation_context']) ? sanitize_text_field($data['conversation_context']) : null,
        'response_tokens' => isset($data['response_tokens']) ? absint($data['response_tokens']) : 0,
        'response_time_ms' => isset($data['response_time_ms']) ? absint($data['response_time_ms']) : 0,
        'training_consent' => isset($data['training_consent']) ? absint($data['training_consent']) : 0
    );
    
    // Validate rating score based on type
    if ($feedback_data['rating_type'] === 'thumbs') {
        if (!in_array($feedback_data['rating_score'], [0, 1])) {
            return new WP_Error('invalid_rating', 'Thumbs rating must be 0 or 1');
        }
    } else {
        if ($feedback_data['rating_score'] < 1 || $feedback_data['rating_score'] > 5) {
            return new WP_Error('invalid_rating', 'Star rating must be between 1 and 5');
        }
    }
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback';
    
    // Check for existing feedback for this user/message combination
    if ($feedback_data['message_id']) {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d AND message_id = %s",
            $feedback_data['user_id'],
            $feedback_data['message_id']
        ));
        
        if ($existing) {
            // Update existing feedback
            $result = $wpdb->update(
                $table_name,
                $feedback_data,
                array('id' => $existing),
                array('%d', '%d', '%s', '%s', '%d', '%s', '%s', '%d', '%d', '%d', '%d', '%s', '%s', '%s', '%d', '%d', '%d'),
                array('%d')
            );
            
            if ($result === false) {
                return new WP_Error('update_failed', 'Failed to update feedback');
            }
            
            return $existing;
        }
    }
    
    // Insert new feedback
    $result = $wpdb->insert(
        $table_name,
        $feedback_data,
        array('%d', '%d', '%s', '%s', '%d', '%s', '%s', '%d', '%d', '%d', '%d', '%s', '%s', '%s', '%d', '%d', '%d')
    );
    
    if ($result === false) {
        return new WP_Error('insert_failed', 'Failed to insert feedback');
    }
    
    return $wpdb->insert_id;
}

/**
 * Get user feedback
 */
function chatgabi_get_user_feedback($user_id, $limit = 50, $offset = 0) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback';
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name 
         WHERE user_id = %d 
         ORDER BY created_at DESC 
         LIMIT %d OFFSET %d",
        $user_id,
        $limit,
        $offset
    ));
    
    return $results ?: array();
}

/**
 * Get feedback by message ID
 */
function chatgabi_get_feedback_by_message($message_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback';
    
    $result = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE message_id = %s",
        $message_id
    ));
    
    return $result;
}

/**
 * Get feedback statistics
 */
function chatgabi_get_feedback_stats($days = 30, $country = null, $sector = null) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback';
    
    $where_conditions = array("created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)");
    $where_values = array($days);
    
    if ($country) {
        $where_conditions[] = "user_country = %s";
        $where_values[] = $country;
    }
    
    if ($sector) {
        $where_conditions[] = "user_sector = %s";
        $where_values[] = $sector;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Overall statistics
    $stats = $wpdb->get_row($wpdb->prepare(
        "SELECT 
            COUNT(*) as total_feedback,
            AVG(rating_score) as avg_rating,
            COUNT(CASE WHEN rating_score >= 4 THEN 1 END) as positive_feedback,
            COUNT(CASE WHEN rating_score <= 2 THEN 1 END) as negative_feedback,
            COUNT(CASE WHEN feedback_text IS NOT NULL AND feedback_text != '' THEN 1 END) as text_feedback_count
         FROM $table_name 
         WHERE $where_clause",
        ...$where_values
    ));
    
    // Rating distribution
    $rating_distribution = $wpdb->get_results($wpdb->prepare(
        "SELECT rating_score, COUNT(*) as count 
         FROM $table_name 
         WHERE $where_clause 
         GROUP BY rating_score 
         ORDER BY rating_score",
        ...$where_values
    ));
    
    // Category ratings
    $category_stats = $wpdb->get_row($wpdb->prepare(
        "SELECT 
            AVG(category_helpfulness) as avg_helpfulness,
            AVG(category_accuracy) as avg_accuracy,
            AVG(category_relevance) as avg_relevance,
            AVG(category_clarity) as avg_clarity
         FROM $table_name 
         WHERE $where_clause 
         AND (category_helpfulness IS NOT NULL OR category_accuracy IS NOT NULL 
              OR category_relevance IS NOT NULL OR category_clarity IS NOT NULL)",
        ...$where_values
    ));
    
    return array(
        'overall' => $stats,
        'distribution' => $rating_distribution,
        'categories' => $category_stats
    );
}

/**
 * Enqueue feedback assets
 */
function chatgabi_enqueue_feedback_assets() {
    if (is_user_logged_in()) {
        wp_enqueue_script(
            'chatgabi-feedback',
            get_template_directory_uri() . '/assets/js/feedback-rating.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'chatgabi-feedback',
            get_template_directory_uri() . '/assets/css/feedback-rating.css',
            array(),
            '1.0.0'
        );
        
        wp_localize_script('chatgabi-feedback', 'chatgabiFeedback', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_feedback_nonce'),
            'strings' => array(
                'submitFeedback' => __('Submit Feedback', 'chatgabi'),
                'thankYou' => __('Thank you for your feedback!', 'chatgabi'),
                'error' => __('Error submitting feedback. Please try again.', 'chatgabi'),
                'rateResponse' => __('Rate this response', 'chatgabi'),
                'optional' => __('Optional', 'chatgabi'),
                'helpfulness' => __('Helpfulness', 'chatgabi'),
                'accuracy' => __('Accuracy', 'chatgabi'),
                'relevance' => __('Relevance', 'chatgabi'),
                'clarity' => __('Clarity', 'chatgabi')
            )
        ));
    }
}

/**
 * Enqueue admin feedback assets
 */
function chatgabi_enqueue_feedback_admin_assets($hook) {
    if (strpos($hook, 'chatgabi') !== false) {
        wp_enqueue_script(
            'chatgabi-feedback-admin',
            get_template_directory_uri() . '/assets/js/feedback-admin.js',
            array('jquery', 'chart-js'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'chatgabi-feedback-admin',
            get_template_directory_uri() . '/assets/css/feedback-admin.css',
            array(),
            '1.0.0'
        );
    }
}

/**
 * Export feedback data in specified format
 */
function chatgabi_export_feedback_data($format, $date_from = '', $date_to = '') {
    global $wpdb;

    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';

    // Build query with date filters
    $where_conditions = array('1=1');
    $where_values = array();

    if ($date_from) {
        $where_conditions[] = 'created_at >= %s';
        $where_values[] = $date_from . ' 00:00:00';
    }

    if ($date_to) {
        $where_conditions[] = 'created_at <= %s';
        $where_values[] = $date_to . ' 23:59:59';
    }

    $where_clause = implode(' AND ', $where_conditions);
    $query = "SELECT * FROM {$feedback_table} WHERE {$where_clause} ORDER BY created_at DESC";

    if (!empty($where_values)) {
        $data = $wpdb->get_results($wpdb->prepare($query, $where_values), ARRAY_A);
    } else {
        $data = $wpdb->get_results($query, ARRAY_A);
    }

    $filename = 'chatgabi_feedback_' . date('Y-m-d_H-i-s');

    switch ($format) {
        case 'csv':
            chatgabi_export_csv($data, $filename);
            break;
        case 'json':
            chatgabi_export_json($data, $filename);
            break;
        case 'xml':
            chatgabi_export_xml($data, $filename);
            break;
        default:
            wp_die('Invalid export format');
    }
}

/**
 * Export data as CSV
 */
function chatgabi_export_csv($data, $filename) {
    if (empty($data)) {
        wp_die('No data to export');
    }

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');

    $output = fopen('php://output', 'w');

    // Write headers
    fputcsv($output, array_keys($data[0]));

    // Write data
    foreach ($data as $row) {
        fputcsv($output, $row);
    }

    fclose($output);
    exit;
}

/**
 * Export data as JSON
 */
function chatgabi_export_json($data, $filename) {
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');

    echo json_encode($data, JSON_PRETTY_PRINT);
    exit;
}

/**
 * Export data as XML
 */
function chatgabi_export_xml($data, $filename) {
    header('Content-Type: application/xml');
    header('Content-Disposition: attachment; filename="' . $filename . '.xml"');

    $xml = new SimpleXMLElement('<feedback_data/>');

    foreach ($data as $row) {
        $feedback = $xml->addChild('feedback');
        foreach ($row as $key => $value) {
            $feedback->addChild($key, htmlspecialchars($value));
        }
    }

    echo $xml->asXML();
    exit;
}

<?php
/**
 * Fix Critical REST API and Asset Loading Issues
 * 
 * Addresses:
 * 1. REST API Authentication/Permission Errors (403 Forbidden)
 * 2. REST API Registration Problems (duplicate routes)
 * 3. Asset Loading Failures (JS/CSS not enqueued)
 * 4. API Timeout Issues (infinite loops/blocking code)
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Critical REST API Issues</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .fix-button:hover { background: #005a87; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .code-block { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0; }
    </style>
</head>
<body>

<h1>🚨 Fix Critical REST API Issues</h1>

<?php
echo '<div class="info">Critical fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$fixes_applied = array();
$errors_encountered = array();

// Fix 1: Remove Duplicate REST Route Registrations
echo '<h2>🔧 Fix 1: Remove Duplicate REST Route Registrations</h2>';

try {
    // First, let's identify the duplicate registrations
    echo '<div class="info">🔍 Analyzing duplicate REST route registrations...</div>';
    
    // The issue is that both rest-api.php and prompt-templates.php register the same routes
    // We need to disable one of them
    
    // Check current registered routes
    $rest_routes = rest_get_server()->get_routes();
    $chatgabi_routes = array();
    
    foreach ($rest_routes as $route => $handlers) {
        if (strpos($route, '/chatgabi/v1/') === 0) {
            $chatgabi_routes[] = $route;
        }
    }
    
    echo '<div class="info">Found ' . count($chatgabi_routes) . ' existing ChatGABI routes</div>';
    
    // Remove the duplicate hook from prompt-templates.php
    remove_action('rest_api_init', 'chatgabi_register_template_rest_routes');
    
    echo '<div class="success">✅ Removed duplicate REST route registration hook</div>';
    $fixes_applied[] = 'Removed duplicate REST route registration';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Error removing duplicates: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Duplicate removal: ' . $e->getMessage();
}

// Fix 2: Fix Permission Callbacks for Public Access
echo '<h2>🔐 Fix 2: Fix Permission Callbacks</h2>';

try {
    echo '<div class="info">🔧 Creating fixed permission callbacks...</div>';
    
    // Create a new permission callback that allows public access to templates
    if (!function_exists('chatgabi_templates_permission_callback')) {
        function chatgabi_templates_permission_callback() {
            // Allow public access to templates (no authentication required)
            return true;
        }
    }
    
    // Create a permission callback for logged-in users only
    if (!function_exists('chatgabi_logged_in_permission_callback')) {
        function chatgabi_logged_in_permission_callback() {
            return is_user_logged_in();
        }
    }
    
    echo '<div class="success">✅ Created fixed permission callbacks</div>';
    $fixes_applied[] = 'Created fixed permission callbacks';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Permission callback error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Permission callbacks: ' . $e->getMessage();
}

// Fix 3: Re-register REST Routes with Correct Permissions
echo '<h2>🌐 Fix 3: Re-register REST Routes with Correct Permissions</h2>';

try {
    echo '<div class="info">🔧 Re-registering REST routes with fixed permissions...</div>';
    
    // Unregister existing routes first
    $rest_server = rest_get_server();
    
    // Register templates endpoint with public access
    register_rest_route('chatgabi/v1', '/templates', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_templates_fixed',
        'permission_callback' => '__return_true', // Public access
        'args' => array(
            'category' => array(
                'required' => false,
                'type' => 'string',
            ),
            'language' => array(
                'required' => false,
                'type' => 'string',
            ),
            'search' => array(
                'required' => false,
                'type' => 'string',
            ),
            'user_only' => array(
                'required' => false,
                'type' => 'boolean',
                'default' => false,
            ),
        ),
    ));
    
    // Register categories endpoint with public access
    register_rest_route('chatgabi/v1', '/template-categories', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_template_categories_fixed',
        'permission_callback' => '__return_true', // Public access
    ));
    
    echo '<div class="success">✅ Re-registered REST routes with public access</div>';
    $fixes_applied[] = 'Re-registered REST routes with public access';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Route registration error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Route registration: ' . $e->getMessage();
}

// Fix 4: Create Fixed API Handlers (No Infinite Loops)
echo '<h2>⚡ Fix 4: Create Fixed API Handlers</h2>';

try {
    echo '<div class="info">🔧 Creating optimized API handlers...</div>';
    
    // Fixed templates handler
    if (!function_exists('chatgabi_get_templates_fixed')) {
        function chatgabi_get_templates_fixed($request) {
            global $wpdb;
            
            // Set execution time limit to prevent timeouts
            set_time_limit(30);
            
            $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';
            $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
            
            // Get parameters
            $category = $request->get_param('category');
            $search = $request->get_param('search');
            
            // Build simple, optimized query
            $where_conditions = array("t.status = 'active'", "t.is_public = 1");
            $where_values = array();
            
            // Category filter
            if ($category && $category !== 'all') {
                $where_conditions[] = "c.slug = %s";
                $where_values[] = $category;
            }
            
            // Search filter
            if ($search) {
                $where_conditions[] = "(t.title LIKE %s OR t.description LIKE %s)";
                $search_term = '%' . $wpdb->esc_like($search) . '%';
                $where_values[] = $search_term;
                $where_values[] = $search_term;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $query = "SELECT t.id, t.title, t.description, t.prompt_content, t.language_code, 
                             t.tags, t.sector, t.usage_count, t.rating_average, t.rating_count,
                             c.name as category_name, c.icon as category_icon, c.color as category_color
                      FROM $table_name t
                      LEFT JOIN $categories_table c ON t.category_id = c.id
                      $where_clause
                      ORDER BY t.is_featured DESC, t.usage_count DESC
                      LIMIT 50";
            
            if (!empty($where_values)) {
                $query = $wpdb->prepare($query, $where_values);
            }
            
            $templates = $wpdb->get_results($query);
            
            // Process templates (simplified)
            $processed_templates = array();
            if ($templates) {
                foreach ($templates as $template) {
                    $processed_templates[] = array(
                        'id' => (int) $template->id,
                        'title' => $template->title,
                        'description' => $template->description,
                        'category' => array(
                            'name' => $template->category_name ?: 'General',
                            'icon' => $template->category_icon ?: '📋',
                            'color' => $template->category_color ?: '#667eea'
                        ),
                        'language_code' => $template->language_code ?: 'en',
                        'tags' => $template->tags ? explode(',', $template->tags) : array(),
                        'sector' => $template->sector ?: 'General',
                        'usage_count' => (int) $template->usage_count,
                        'rating_average' => (float) $template->rating_average,
                        'rating_count' => (int) $template->rating_count
                    );
                }
            }
            
            return rest_ensure_response(array(
                'success' => true,
                'templates' => $processed_templates,
                'count' => count($processed_templates),
                'total_available' => count($processed_templates)
            ));
        }
    }
    
    // Fixed categories handler
    if (!function_exists('chatgabi_get_template_categories_fixed')) {
        function chatgabi_get_template_categories_fixed($request) {
            global $wpdb;
            
            // Set execution time limit
            set_time_limit(15);
            
            $table_name = $wpdb->prefix . 'chatgabi_template_categories';
            
            $categories = $wpdb->get_results("
                SELECT id, name, slug, description, icon, color, sort_order
                FROM $table_name 
                WHERE status = 'active' 
                ORDER BY sort_order ASC, name ASC
                LIMIT 20
            ");
            
            $processed_categories = array();
            if ($categories) {
                foreach ($categories as $category) {
                    $processed_categories[] = array(
                        'id' => (int) $category->id,
                        'name' => $category->name,
                        'slug' => $category->slug,
                        'description' => $category->description,
                        'icon' => $category->icon ?: '📋',
                        'color' => $category->color ?: '#667eea'
                    );
                }
            }
            
            return rest_ensure_response(array(
                'success' => true,
                'categories' => $processed_categories,
                'count' => count($processed_categories)
            ));
        }
    }
    
    echo '<div class="success">✅ Created optimized API handlers</div>';
    $fixes_applied[] = 'Created optimized API handlers';
    
} catch (Exception $e) {
    echo '<div class="error">❌ API handler creation error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'API handlers: ' . $e->getMessage();
}

// Fix 5: Fix Asset Enqueuing for Templates Page
echo '<h2>📁 Fix 5: Fix Asset Enqueuing</h2>';

try {
    echo '<div class="info">🔧 Fixing asset enqueuing for templates page...</div>';
    
    // Create a function to properly enqueue templates assets
    if (!function_exists('chatgabi_enqueue_templates_assets_fixed')) {
        function chatgabi_enqueue_templates_assets_fixed() {
            // Only enqueue on templates page
            if (is_page('templates') || is_page_template('page-templates.php')) {
                
                // Enqueue CSS
                wp_enqueue_style(
                    'chatgabi-templates-fixed',
                    get_template_directory_uri() . '/assets/css/templates.css',
                    array(),
                    CHATGABI_VERSION . '-fixed'
                );
                
                // Enqueue JavaScript
                wp_enqueue_script(
                    'chatgabi-templates-interface-fixed',
                    get_template_directory_uri() . '/assets/js/templates-interface.js',
                    array('jquery'),
                    CHATGABI_VERSION . '-fixed',
                    true
                );
                
                // Localize script with configuration
                wp_localize_script('chatgabi-templates-interface-fixed', 'chatgabiTemplatesConfig', array(
                    'restUrl' => rest_url('chatgabi/v1/'),
                    'restNonce' => wp_create_nonce('wp_rest'),
                    'ajaxUrl' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('chatgabi_templates_nonce'),
                    'userId' => get_current_user_id(),
                    'userCredits' => get_user_meta(get_current_user_id(), 'businesscraft_credits', true) ?: 0,
                    'strings' => array(
                        'loading' => 'Loading...',
                        'loadingTemplates' => 'Loading templates...',
                        'noTemplatesFound' => 'No templates found matching your criteria.',
                        'errorOccurred' => 'An error occurred. Please try again.'
                    )
                ));
            }
        }
    }
    
    // Remove existing hooks and add the fixed one
    remove_action('wp_enqueue_scripts', 'chatgabi_enqueue_template_assets');
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_templates_assets_fixed', 20);
    
    echo '<div class="success">✅ Fixed asset enqueuing for templates page</div>';
    $fixes_applied[] = 'Fixed asset enqueuing';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Asset enqueuing error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Asset enqueuing: ' . $e->getMessage();
}

// Fix 6: Test Fixed Endpoints
echo '<h2>🧪 Fix 6: Test Fixed Endpoints</h2>';

try {
    echo '<div class="info">🔧 Testing fixed REST API endpoints...</div>';
    
    // Test templates endpoint
    $templates_url = rest_url('chatgabi/v1/templates');
    echo '<div class="info">Testing: ' . $templates_url . '</div>';
    
    $response = wp_remote_get($templates_url, array(
        'timeout' => 15,
        'headers' => array(
            'User-Agent' => 'ChatGABI-Test/1.0'
        )
    ));
    
    if (!is_wp_error($response)) {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            $data = json_decode($body, true);
            if (isset($data['success']) && $data['success']) {
                echo '<div class="success">✅ Templates endpoint working: ' . count($data['templates']) . ' templates found</div>';
            } else {
                echo '<div class="warning">⚠️ Templates endpoint returned success=false</div>';
            }
        } else {
            echo '<div class="error">❌ Templates endpoint returned status: ' . $status_code . '</div>';
        }
    } else {
        echo '<div class="error">❌ Templates endpoint error: ' . $response->get_error_message() . '</div>';
    }
    
    // Test categories endpoint
    $categories_url = rest_url('chatgabi/v1/template-categories');
    echo '<div class="info">Testing: ' . $categories_url . '</div>';
    
    $response = wp_remote_get($categories_url, array(
        'timeout' => 10,
        'headers' => array(
            'User-Agent' => 'ChatGABI-Test/1.0'
        )
    ));
    
    if (!is_wp_error($response)) {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            $data = json_decode($body, true);
            if (isset($data['success']) && $data['success']) {
                echo '<div class="success">✅ Categories endpoint working: ' . count($data['categories']) . ' categories found</div>';
            } else {
                echo '<div class="warning">⚠️ Categories endpoint returned success=false</div>';
            }
        } else {
            echo '<div class="error">❌ Categories endpoint returned status: ' . $status_code . '</div>';
        }
    } else {
        echo '<div class="error">❌ Categories endpoint error: ' . $response->get_error_message() . '</div>';
    }
    
    $fixes_applied[] = 'Tested fixed endpoints';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Endpoint testing error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Endpoint testing: ' . $e->getMessage();
}

// Summary
echo '<h2>📋 Fix Summary</h2>';

if (empty($errors_encountered)) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL CRITICAL ISSUES FIXED!</h3>';
    echo '<p><strong>✅ The ChatGABI Templates REST API should now be fully functional!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues May Remain</h3>';
    echo '<ul>';
    foreach ($errors_encountered as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Fixes Applied: ' . count($fixes_applied) . '</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Test Actions
echo '<h2>🧪 Test Your Fixes</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="fix-button">🎯 Test Templates Page</a>';
}

echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="fix-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/template-categories') . '" target="_blank" class="fix-button">📂 Test Categories API</a>';
echo '<a href="final-templates-test.php" class="fix-button">🔍 Run Final Test</a>';
echo '<a href="javascript:window.location.reload()" class="fix-button">🔄 Re-run Fixes</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Critical fix completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

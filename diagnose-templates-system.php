<?php
/**
 * ChatGABI Templates System Diagnostic Tool
 * 
 * Comprehensive diagnosis of template system issues including:
 * - Database tables and data
 * - Page creation and routing
 * - Template display functionality
 * - Admin interface issues
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates System Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 5px; border-radius: 3px; font-family: monospace; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .fix-button:hover { background: #005a87; }
    </style>
</head>
<body>

<h1>🔍 ChatGABI Templates System Diagnosis</h1>

<?php
echo '<div class="info">Diagnosis started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$issues_found = array();
$fixes_applied = array();

// Test 1: Database Tables Check
echo '<h2>📊 Test 1: Database Tables</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$usage_table = $wpdb->prefix . 'chatgabi_template_usage';

$tables_to_check = array(
    'Templates' => $templates_table,
    'Categories' => $categories_table,
    'Usage' => $usage_table
);

foreach ($tables_to_check as $name => $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo '<div class="success">✅ ' . $name . ' table exists with ' . $count . ' records</div>';
        
        if ($count == 0 && $name == 'Categories') {
            $issues_found[] = 'No template categories found';
        }
        if ($count == 0 && $name == 'Templates') {
            $issues_found[] = 'No templates found';
        }
    } else {
        echo '<div class="error">❌ ' . $name . ' table missing: ' . $table . '</div>';
        $issues_found[] = $name . ' table missing';
    }
}

// Test 2: Template Categories Data
echo '<h2>📂 Test 2: Template Categories</h2>';

if ($wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table) {
    $categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY sort_order ASC");
    
    if (empty($categories)) {
        echo '<div class="warning">⚠️ No template categories found</div>';
        $issues_found[] = 'No template categories';
        
        // Try to create default categories
        if (function_exists('chatgabi_create_default_template_categories')) {
            echo '<div class="info">🔧 Attempting to create default categories...</div>';
            try {
                chatgabi_create_default_template_categories();
                $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");
                if ($new_count > 0) {
                    echo '<div class="success">✅ Created ' . $new_count . ' default categories</div>';
                    $fixes_applied[] = 'Created default template categories';
                } else {
                    echo '<div class="error">❌ Failed to create default categories</div>';
                }
            } catch (Exception $e) {
                echo '<div class="error">❌ Error creating categories: ' . $e->getMessage() . '</div>';
            }
        }
    } else {
        echo '<div class="success">✅ Found ' . count($categories) . ' template categories:</div>';
        echo '<table>';
        echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Status</th><th>Sort Order</th></tr>';
        foreach ($categories as $category) {
            $status = isset($category->status) ? $category->status : 'N/A';
            echo '<tr>';
            echo '<td>' . $category->id . '</td>';
            echo '<td>' . esc_html($category->name) . '</td>';
            echo '<td>' . esc_html($category->slug) . '</td>';
            echo '<td>' . esc_html($status) . '</td>';
            echo '<td>' . $category->sort_order . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
}

// Test 3: Template Data
echo '<h2>📝 Test 3: Template Data</h2>';

if ($wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table) {
    $templates = $wpdb->get_results("SELECT * FROM $templates_table ORDER BY created_at DESC LIMIT 10");
    
    if (empty($templates)) {
        echo '<div class="warning">⚠️ No templates found</div>';
        $issues_found[] = 'No templates';
        
        // Try to create default templates
        if (function_exists('chatgabi_initialize_default_templates')) {
            echo '<div class="info">🔧 Attempting to create default templates...</div>';
            try {
                chatgabi_initialize_default_templates();
                $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
                if ($new_count > 0) {
                    echo '<div class="success">✅ Created ' . $new_count . ' default templates</div>';
                    $fixes_applied[] = 'Created default templates';
                } else {
                    echo '<div class="error">❌ Failed to create default templates</div>';
                }
            } catch (Exception $e) {
                echo '<div class="error">❌ Error creating templates: ' . $e->getMessage() . '</div>';
            }
        }
    } else {
        echo '<div class="success">✅ Found ' . count($templates) . ' templates (showing first 10):</div>';
        echo '<table>';
        echo '<tr><th>ID</th><th>Title</th><th>Category</th><th>User ID</th><th>Public</th><th>Status</th></tr>';
        foreach ($templates as $template) {
            $status = isset($template->status) ? $template->status : 'N/A';
            echo '<tr>';
            echo '<td>' . $template->id . '</td>';
            echo '<td>' . esc_html($template->title) . '</td>';
            echo '<td>' . $template->category_id . '</td>';
            echo '<td>' . $template->user_id . '</td>';
            echo '<td>' . ($template->is_public ? 'Yes' : 'No') . '</td>';
            echo '<td>' . esc_html($status) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
}

// Test 4: WordPress Pages
echo '<h2>📄 Test 4: WordPress Pages</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<div class="success">✅ Templates page exists (ID: ' . $templates_page->ID . ')</div>';
    echo '<div class="info">URL: <a href="' . get_permalink($templates_page->ID) . '" target="_blank">' . get_permalink($templates_page->ID) . '</a></div>';
    
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    echo '<div class="info">Page template: ' . ($page_template ?: 'default') . '</div>';
    
    if ($page_template !== 'page-templates.php') {
        echo '<div class="warning">⚠️ Page template should be "page-templates.php"</div>';
        $issues_found[] = 'Incorrect page template';
    }
} else {
    echo '<div class="error">❌ Templates page does not exist</div>';
    $issues_found[] = 'Templates page missing';
    
    // Try to create the page
    echo '<div class="info">🔧 Attempting to create templates page...</div>';
    try {
        $page_id = wp_insert_post(array(
            'post_title' => 'ChatGABI Templates',
            'post_content' => 'AI-powered business templates for African entrepreneurs.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'templates'
        ));
        
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-templates.php');
            echo '<div class="success">✅ Created templates page (ID: ' . $page_id . ')</div>';
            $fixes_applied[] = 'Created templates page';
        } else {
            echo '<div class="error">❌ Failed to create templates page</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Error creating page: ' . $e->getMessage() . '</div>';
    }
}

// Test 5: Template Functions
echo '<h2>⚙️ Test 5: Template Functions</h2>';

$functions_to_check = array(
    'chatgabi_get_template_categories',
    'chatgabi_get_user_templates',
    'chatgabi_create_default_template_categories',
    'chatgabi_initialize_default_templates',
    'chatgabi_create_prompt_templates_tables'
);

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo '<div class="success">✅ Function exists: ' . $function . '</div>';
    } else {
        echo '<div class="error">❌ Function missing: ' . $function . '</div>';
        $issues_found[] = 'Missing function: ' . $function;
    }
}

// Test 6: REST API Endpoints
echo '<h2>🌐 Test 6: REST API Endpoints</h2>';

$rest_routes = rest_get_server()->get_routes();
$chatgabi_routes = array();

foreach ($rest_routes as $route => $handlers) {
    if (strpos($route, '/chatgabi/v1/') === 0) {
        $chatgabi_routes[] = $route;
    }
}

if (!empty($chatgabi_routes)) {
    echo '<div class="success">✅ Found ' . count($chatgabi_routes) . ' ChatGABI REST routes:</div>';
    echo '<ul>';
    foreach ($chatgabi_routes as $route) {
        echo '<li><code>' . $route . '</code></li>';
    }
    echo '</ul>';
} else {
    echo '<div class="warning">⚠️ No ChatGABI REST API routes found</div>';
    $issues_found[] = 'Missing REST API routes';
}

// Test 7: File Existence
echo '<h2>📁 Test 7: Template Files</h2>';

$theme_dir = get_template_directory();
$files_to_check = array(
    'page-templates.php' => $theme_dir . '/page-templates.php',
    'templates.css' => $theme_dir . '/assets/css/templates.css',
    'templates-interface.js' => $theme_dir . '/assets/js/templates-interface.js',
    'prompt-templates.php' => $theme_dir . '/inc/prompt-templates.php',
    'template-functions.php' => $theme_dir . '/inc/template-functions.php'
);

foreach ($files_to_check as $name => $path) {
    if (file_exists($path)) {
        echo '<div class="success">✅ File exists: ' . $name . '</div>';
    } else {
        echo '<div class="error">❌ File missing: ' . $name . ' (' . $path . ')</div>';
        $issues_found[] = 'Missing file: ' . $name;
    }
}

// Test 8: Admin Menu
echo '<h2>🎛️ Test 8: Admin Menu</h2>';

if (function_exists('chatgabi_add_templates_admin_menu')) {
    echo '<div class="success">✅ Admin menu function exists</div>';
} else {
    echo '<div class="error">❌ Admin menu function missing</div>';
    $issues_found[] = 'Admin menu function missing';
}

// Summary
echo '<h2>📋 Summary</h2>';

if (empty($issues_found)) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL TESTS PASSED!</h3>';
    echo '<p>The ChatGABI Templates system appears to be properly configured.</p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Issues Found: ' . count($issues_found) . '</h3>';
    echo '<ul>';
    foreach ($issues_found as $issue) {
        echo '<li>' . esc_html($issue) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Fixes Applied: ' . count($fixes_applied) . '</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Quick Actions</h2>';

echo '<div style="margin: 20px 0;">';
echo '<button class="fix-button" onclick="window.location.reload()">🔄 Re-run Diagnosis</button>';

if ($templates_page) {
    echo '<button class="fix-button" onclick="window.open(\'' . get_permalink($templates_page->ID) . '\', \'_blank\')">👀 View Templates Page</button>';
}

echo '<button class="fix-button" onclick="window.open(\'' . admin_url('admin.php?page=chatgabi-templates') . '\', \'_blank\')">⚙️ Admin Templates</button>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Diagnosis completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

import*as t from"@wordpress/interactivity";var e={d:(t,n)=>{for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const n=(t=>{var n={};return e.d(n,t),n})({getContext:()=>t.getContext,getElement:()=>t.getElement,store:()=>t.store,withSyncEvent:()=>t.withSyncEvent});let a=!1,o=0;const{state:r,actions:i,callbacks:l}=(0,n.store)("core/image",{state:{currentImageId:null,get currentImage(){return r.metadata[r.currentImageId]},get overlayOpened(){return null!==r.currentImageId},get roleAttribute(){return r.overlayOpened?"dialog":null},get ariaModal(){return r.overlayOpened?"true":null},get enlargedSrc(){return r.currentImage.uploadedSrc||"data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="},get figureStyles(){return r.overlayOpened&&`${r.currentImage.figureStyles?.replace(/margin[^;]*;?/g,"")};`},get imgStyles(){return r.overlayOpened&&`${r.currentImage.imgStyles?.replace(/;$/,"")}; object-fit:cover;`},get imageButtonRight(){const{imageId:t}=(0,n.getContext)();return r.metadata[t].imageButtonRight},get imageButtonTop(){const{imageId:t}=(0,n.getContext)();return r.metadata[t].imageButtonTop},get isContentHidden(){const t=(0,n.getContext)();return r.overlayEnabled&&r.currentImageId===t.imageId},get isContentVisible(){const t=(0,n.getContext)();return!r.overlayEnabled&&r.currentImageId===t.imageId}},actions:{showLightbox(){const{imageId:t}=(0,n.getContext)();r.metadata[t].imageRef?.complete&&(r.scrollTopReset=document.documentElement.scrollTop,r.scrollLeftReset=document.documentElement.scrollLeft,r.overlayEnabled=!0,r.currentImageId=t,l.setOverlayStyles())},hideLightbox(){r.overlayEnabled&&(r.showClosingAnimation=!0,r.overlayEnabled=!1,setTimeout((function(){r.currentImage.buttonRef.focus({preventScroll:!0}),r.currentImageId=null}),450))},handleKeydown:(0,n.withSyncEvent)((t=>{if(r.overlayEnabled){if("Tab"===t.key){t.preventDefault();const{ref:e}=(0,n.getElement)();e.querySelector("button").focus()}"Escape"===t.key&&i.hideLightbox()}})),handleTouchMove:(0,n.withSyncEvent)((t=>{r.overlayEnabled&&t.preventDefault()})),handleTouchStart(){a=!0},handleTouchEnd(){o=Date.now(),a=!1},handleScroll(){r.overlayOpened&&!a&&Date.now()-o>450&&window.scrollTo(r.scrollLeftReset,r.scrollTopReset)}},callbacks:{setOverlayStyles(){if(!r.overlayEnabled)return;let{naturalWidth:t,naturalHeight:e,offsetWidth:n,offsetHeight:a}=r.currentImage.imageRef,{x:o,y:i}=r.currentImage.imageRef.getBoundingClientRect();const l=t/e;let g=n/a;if("contain"===r.currentImage.scaleAttr)if(l>g){const t=n/l;i+=(a-t)/2,a=t}else{const t=a*l;o+=(n-t)/2,n=t}g=n/a;let c=parseFloat("none"!==r.currentImage.targetWidth?r.currentImage.targetWidth:t),s=parseFloat("none"!==r.currentImage.targetHeight?r.currentImage.targetHeight:e),d=c/s,u=c,m=s,h=c,p=s;if(l.toFixed(2)!==d.toFixed(2)){if(l>d){const t=c/l;s-t>c?(s=t,c=t*l):s=c/l}else{const t=s*l;c-t>s?(c=t,s=t/l):c=s*l}h=c,p=s,d=c/s,g>d?(u=c,m=u/g):(m=s,u=m*g)}(n>h||a>p)&&(h=n,p=a);let f=0;window.innerWidth>480?f=80:window.innerWidth>1920&&(f=160);const y=Math.min(window.innerWidth-f,h),w=Math.min(window.innerHeight-80,p);g>y/w?(h=y,p=h/g):(p=w,h=p*g);const b=n/h,I=c*(h/u),v=s*(p/m);r.overlayStyles=`\n\t\t\t\t\t--wp--lightbox-initial-top-position: ${i}px;\n\t\t\t\t\t--wp--lightbox-initial-left-position: ${o}px;\n\t\t\t\t\t--wp--lightbox-container-width: ${h+1}px;\n\t\t\t\t\t--wp--lightbox-container-height: ${p+1}px;\n\t\t\t\t\t--wp--lightbox-image-width: ${I}px;\n\t\t\t\t\t--wp--lightbox-image-height: ${v}px;\n\t\t\t\t\t--wp--lightbox-scale: ${b};\n\t\t\t\t\t--wp--lightbox-scrollbar-width: ${window.innerWidth-document.documentElement.clientWidth}px;\n\t\t\t\t`},setButtonStyles(){const{imageId:t}=(0,n.getContext)(),{ref:e}=(0,n.getElement)();r.metadata[t].imageRef=e,r.metadata[t].currentSrc=e.currentSrc;const{naturalWidth:a,naturalHeight:o,offsetWidth:i,offsetHeight:l}=e;if(0===a||0===o)return;const g=e.parentElement,c=e.parentElement.clientWidth;let s=e.parentElement.clientHeight;const d=g.querySelector("figcaption");if(d){const t=window.getComputedStyle(d);["absolute","fixed"].includes(t.position)||(s=s-d.offsetHeight-parseFloat(t.marginTop)-parseFloat(t.marginBottom))}const u=s-l,m=c-i;let h=u+16,p=m+16;if("contain"===r.metadata[t].scaleAttr){const t=a/o;if(t>=i/l){h=(l-i/t)/2+u+16,p=m+16}else{h=u+16,p=(i-l*t)/2+m+16}}r.metadata[t].imageButtonTop=h,r.metadata[t].imageButtonRight=p},setOverlayFocus(){if(r.overlayEnabled){const{ref:t}=(0,n.getElement)();t.focus()}},initTriggerButton(){const{imageId:t}=(0,n.getContext)(),{ref:e}=(0,n.getElement)();r.metadata[t].buttonRef=e}}},{lock:!0});
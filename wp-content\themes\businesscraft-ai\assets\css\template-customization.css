/**
 * Template Customization Styles for BusinessCraft AI
 * 
 * Styles for advanced template customization features
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

/* Customization Button */
.customize-template-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(to right, #9C27B0, #E91E63);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.customize-template-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
    color: white;
    text-decoration: none;
}

/* Customization Modal */
.customization-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.customization-modal.active {
    display: flex;
    opacity: 1;
    align-items: center;
    justify-content: center;
}

.customization-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.customization-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.customization-modal.active .customization-modal-content {
    transform: scale(1);
}

.customization-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.customization-modal-header h3 {
    margin: 0;
    color: #3D4E81;
    font-size: 1.4em;
}

.customization-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.customization-modal-close:hover {
    background-color: #f8f9fa;
    color: #333;
}

.customization-modal-body {
    padding: 24px;
}

/* Customization Tabs */
.customization-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    color: #3D4E81;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #3D4E81;
    border-bottom-color: #3D4E81;
}

/* Tab Content */
.customization-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane h4 {
    margin: 0 0 16px 0;
    color: #3D4E81;
    font-size: 1.1em;
}

/* Theme Selection */
#template-theme-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 10px;
}

.theme-description {
    color: #666;
    font-size: 0.9em;
    font-style: italic;
    margin-bottom: 20px;
}

/* Color Options */
.color-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.color-options label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.wp-picker-container {
    display: inline-block;
}

/* Text Replacements */
#text-replacements {
    margin-bottom: 15px;
}

.replacement-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.replacement-item input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.remove-replacement {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.remove-replacement:hover {
    background: #c82333;
}

/* Section Modifications */
#section-modifications {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.section-modification {
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.section-modification label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.section-action-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
}

.section-content {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    min-height: 80px;
}

/* Formatting Options */
.formatting-options,
.list-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.formatting-options label,
.list-options label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.header-style,
#list-style {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#custom-list-symbol {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 10px;
}

/* Template Versions */
.versions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.version-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
}

.version-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.version-date {
    font-size: 0.85em;
    color: #666;
}

.load-version-btn {
    padding: 6px 12px;
    font-size: 12px;
    background: #3D4E81;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.load-version-btn:hover {
    background: #2a3660;
}

/* Customization Preview */
.customization-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.customization-preview h4 {
    margin: 0 0 15px 0;
    color: #3D4E81;
}

#customization-preview-content {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
    min-height: 200px;
}

/* Modal Footer */
.customization-modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
}

/* Messages */
.customization-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    z-index: 10002;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.customization-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.customization-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .customization-modal-content {
        width: 95%;
        margin: 20px;
        max-height: 95vh;
    }
    
    .customization-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .customization-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .replacement-item {
        flex-direction: column;
        align-items: stretch;
    }
    
    .replacement-item input {
        margin-bottom: 5px;
    }
    
    .customization-modal-footer {
        flex-direction: column;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Loading States */
.loading-versions {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Body modal open state */
body.modal-open {
    overflow: hidden;
}

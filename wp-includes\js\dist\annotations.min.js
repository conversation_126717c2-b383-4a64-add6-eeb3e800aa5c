/*! This file is auto-generated */
(()=>{"use strict";var t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{store:()=>C});var n={};t.r(n),t.d(n,{__experimentalGetAllAnnotationsForBlock:()=>x,__experimentalGetAnnotations:()=>N,__experimentalGetAnnotationsForBlock:()=>y,__experimentalGetAnnotationsForRichText:()=>T});var r={};t.r(r),t.d(r,{__experimentalAddAnnotation:()=>R,__experimentalRemoveAnnotation:()=>U,__experimentalRemoveAnnotationsBySource:()=>k,__experimentalUpdateAnnotationRange:()=>S});const o=window.wp.richText,a=window.wp.i18n,i="core/annotations",c="core/annotation",l="annotation-text-";const s={name:c,title:(0,a.__)("Annotation"),tagName:"mark",className:"annotation-text",attributes:{className:"class",id:"id"},edit:()=>null,__experimentalGetPropsForEditableTreePreparation:(t,{richTextIdentifier:e,blockClientId:n})=>({annotations:t(i).__experimentalGetAnnotationsForRichText(n,e)}),__experimentalCreatePrepareEditableTree:({annotations:t})=>(e,n)=>{if(0===t.length)return e;let r={formats:e,text:n};return r=function(t,e=[]){return e.forEach((e=>{let{start:n,end:r}=e;n>t.text.length&&(n=t.text.length),r>t.text.length&&(r=t.text.length);const a=l+e.source,i=l+e.id;t=(0,o.applyFormat)(t,{type:c,attributes:{className:a,id:i}},n,r)})),t}(r,t),r.formats},__experimentalGetPropsForEditableTreeChangeHandler:t=>({removeAnnotation:t(i).__experimentalRemoveAnnotation,updateAnnotationRange:t(i).__experimentalUpdateAnnotationRange}),__experimentalCreateOnChangeEditableValue:t=>e=>{const n=function(t){const e={};return t.forEach(((t,n)=>{(t=(t=t||[]).filter((t=>t.type===c))).forEach((t=>{let{id:r}=t.attributes;r=r.replace(l,""),e.hasOwnProperty(r)||(e[r]={start:n}),e[r].end=n+1}))})),e}(e),{removeAnnotation:r,updateAnnotationRange:o,annotations:a}=t;!function(t,e,{removeAnnotation:n,updateAnnotationRange:r}){t.forEach((t=>{const o=e[t.id];if(!o)return void n(t.id);const{start:a,end:i}=t;a===o.start&&i===o.end||r(t.id,o.start,o.end)}))}(a,n,{removeAnnotation:r,updateAnnotationRange:o})}},{name:d,...u}=s;(0,o.registerFormatType)(d,u);const p=window.wp.hooks,m=window.wp.data;function f(t,e){const n=t.filter(e);return t.length===n.length?t:n}(0,p.addFilter)("editor.BlockListBlock","core/annotations",(t=>(0,m.withSelect)(((t,{clientId:e,className:n})=>({className:t(i).__experimentalGetAnnotationsForBlock(e).map((t=>"is-annotated-by-"+t.source)).concat(n).filter(Boolean).join(" ")})))(t)));const _=(t,e)=>Object.entries(t).reduce(((t,[n,r])=>({...t,[n]:e(r)})),{});const A=function(t={},e){var n;switch(e.type){case"ANNOTATION_ADD":const r=e.blockClientId,o={id:e.id,blockClientId:r,richTextIdentifier:e.richTextIdentifier,source:e.source,selector:e.selector,range:e.range};if("range"===o.selector&&!function(t){return"number"==typeof t.start&&"number"==typeof t.end&&t.start<=t.end}(o.range))return t;const a=null!==(n=t?.[r])&&void 0!==n?n:[];return{...t,[r]:[...a,o]};case"ANNOTATION_REMOVE":return _(t,(t=>f(t,(t=>t.id!==e.annotationId))));case"ANNOTATION_UPDATE_RANGE":return _(t,(t=>{let n=!1;const r=t.map((t=>t.id===e.annotationId?(n=!0,{...t,range:{start:e.start,end:e.end}}):t));return n?r:t}));case"ANNOTATION_REMOVE_SOURCE":return _(t,(t=>f(t,(t=>t.source!==e.source))))}return t},g=[],y=(0,m.createSelector)(((t,e)=>{var n;return(null!==(n=t?.[e])&&void 0!==n?n:[]).filter((t=>"block"===t.selector))}),((t,e)=>{var n;return[null!==(n=t?.[e])&&void 0!==n?n:g]}));function x(t,e){var n;return null!==(n=t?.[e])&&void 0!==n?n:g}const T=(0,m.createSelector)(((t,e,n)=>{var r;return(null!==(r=t?.[e])&&void 0!==r?r:[]).filter((t=>"range"===t.selector&&n===t.richTextIdentifier)).map((t=>{const{range:e,...n}=t;return{...e,...n}}))}),((t,e)=>{var n;return[null!==(n=t?.[e])&&void 0!==n?n:g]}));function N(t){return Object.values(t).flat()}const h={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let O;const b=new Uint8Array(16);function I(){if(!O&&(O="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!O))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return O(b)}const v=[];for(let t=0;t<256;++t)v.push((t+256).toString(16).slice(1));function w(t,e=0){return v[t[e+0]]+v[t[e+1]]+v[t[e+2]]+v[t[e+3]]+"-"+v[t[e+4]]+v[t[e+5]]+"-"+v[t[e+6]]+v[t[e+7]]+"-"+v[t[e+8]]+v[t[e+9]]+"-"+v[t[e+10]]+v[t[e+11]]+v[t[e+12]]+v[t[e+13]]+v[t[e+14]]+v[t[e+15]]}const E=function(t,e,n){if(h.randomUUID&&!e&&!t)return h.randomUUID();const r=(t=t||{}).random||(t.rng||I)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,e){n=n||0;for(let t=0;t<16;++t)e[n+t]=r[t];return e}return w(r)};function R({blockClientId:t,richTextIdentifier:e=null,range:n=null,selector:r="range",source:o="default",id:a=E()}){const i={type:"ANNOTATION_ADD",id:a,blockClientId:t,richTextIdentifier:e,source:o,selector:r};return"range"===r&&(i.range=n),i}function U(t){return{type:"ANNOTATION_REMOVE",annotationId:t}}function S(t,e,n){return{type:"ANNOTATION_UPDATE_RANGE",annotationId:t,start:e,end:n}}function k(t){return{type:"ANNOTATION_REMOVE_SOURCE",source:t}}const C=(0,m.createReduxStore)(i,{reducer:A,selectors:n,actions:r});(0,m.register)(C),(window.wp=window.wp||{}).annotations=e})();
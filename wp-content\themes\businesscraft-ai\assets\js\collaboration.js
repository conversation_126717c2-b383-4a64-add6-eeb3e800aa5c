/**
 * Collaboration JavaScript for BusinessCraft AI
 * 
 * Handles template sharing and collaboration features
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Collaboration functionality
    const Collaboration = {
        
        init: function() {
            this.bindEvents();
            this.loadCollaborationInvitations();
        },

        bindEvents: function() {
            // Share template events
            $(document).on('click', '.share-template-btn', this.openShareModal);
            $(document).on('click', '.share-modal-close', this.closeShareModal);
            $(document).on('click', '.share-modal-backdrop', this.closeShareModal);
            
            // Share form submission
            $(document).on('click', '#send-invitation', this.sendInvitation);
            
            // Collaboration management
            $(document).on('click', '.view-collaborators-btn', this.viewCollaborators);
            $(document).on('click', '.remove-collaborator-btn', this.removeCollaborator);
            $(document).on('change', '.permission-select', this.updatePermissions);
            
            // Comments
            $(document).on('click', '.add-comment-btn', this.showCommentForm);
            $(document).on('click', '#submit-comment', this.submitComment);
            $(document).on('click', '.reply-comment-btn', this.replyToComment);
            
            // Invitations
            $(document).on('click', '.accept-invitation-btn', this.acceptInvitation);
            $(document).on('click', '.decline-invitation-btn', this.declineInvitation);
        },

        openShareModal: function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const templateId = $btn.data('template-id');
            
            if (!templateId) {
                Collaboration.showMessage('error', 'Please select a template to share.');
                return;
            }

            Collaboration.showShareModal(templateId);
        },

        showShareModal: function(templateId) {
            // Create modal if it doesn't exist
            if (!$('#share-modal').length) {
                this.createShareModal();
            }

            const $modal = $('#share-modal');
            
            // Set template ID
            $modal.data('template-id', templateId);
            
            // Load existing collaborators
            this.loadCollaborators(templateId);
            
            // Show modal
            $modal.addClass('active');
            $('body').addClass('modal-open');
        },

        createShareModal: function() {
            const modalHtml = `
                <div id="share-modal" class="share-modal">
                    <div class="share-modal-backdrop"></div>
                    <div class="share-modal-content">
                        <div class="share-modal-header">
                            <h3>🤝 Share Template</h3>
                            <button class="share-modal-close">&times;</button>
                        </div>
                        <div class="share-modal-body">
                            <div class="share-form">
                                <h4>Invite Collaborator</h4>
                                <div class="form-group">
                                    <label for="collaborator-email">Email Address:</label>
                                    <input type="email" id="collaborator-email" placeholder="Enter email address..." required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="collaboration-permission">Permission Level:</label>
                                    <select id="collaboration-permission">
                                        <option value="view">View Only</option>
                                        <option value="comment">View & Comment</option>
                                        <option value="edit">View, Comment & Edit</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="invitation-message">Personal Message (Optional):</label>
                                    <textarea id="invitation-message" rows="3" placeholder="Add a personal message to your invitation..."></textarea>
                                </div>
                                
                                <button id="send-invitation" class="btn-primary">
                                    📧 Send Invitation
                                </button>
                            </div>
                            
                            <div class="current-collaborators">
                                <h4>Current Collaborators</h4>
                                <div id="collaborators-list">
                                    <div class="loading-collaborators">Loading collaborators...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
        },

        closeShareModal: function() {
            $('#share-modal').removeClass('active');
            $('body').removeClass('modal-open');
            
            // Clear form
            $('#collaborator-email').val('');
            $('#invitation-message').val('');
            $('#collaboration-permission').val('view');
        },

        loadCollaborators: function(templateId) {
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'templates/' + templateId + '/collaborators',
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce
                },
                success: function(response) {
                    if (response.success) {
                        Collaboration.renderCollaborators(response.collaborators);
                    } else {
                        $('#collaborators-list').html('<p>No collaborators yet.</p>');
                    }
                },
                error: function() {
                    $('#collaborators-list').html('<p>Failed to load collaborators.</p>');
                }
            });
        },

        renderCollaborators: function(collaborators) {
            const $container = $('#collaborators-list');
            
            if (collaborators.length === 0) {
                $container.html('<p>No collaborators yet. Invite someone to get started!</p>');
                return;
            }
            
            let html = '<div class="collaborators-grid">';
            
            collaborators.forEach(function(collaborator) {
                const name = collaborator.display_name || collaborator.email;
                const avatar = collaborator.avatar_url || 'https://www.gravatar.com/avatar/?d=mp&s=40';
                
                html += `
                    <div class="collaborator-item">
                        <div class="collaborator-info">
                            <img src="${avatar}" alt="${name}" class="collaborator-avatar">
                            <div class="collaborator-details">
                                <div class="collaborator-name">${name}</div>
                                <div class="collaborator-email">${collaborator.user_email || collaborator.email}</div>
                            </div>
                        </div>
                        <div class="collaborator-actions">
                            <select class="permission-select" data-collaboration-id="${collaborator.id}">
                                <option value="view" ${collaborator.permission === 'view' ? 'selected' : ''}>View</option>
                                <option value="comment" ${collaborator.permission === 'comment' ? 'selected' : ''}>Comment</option>
                                <option value="edit" ${collaborator.permission === 'edit' ? 'selected' : ''}>Edit</option>
                            </select>
                            <button class="remove-collaborator-btn btn-small" data-collaboration-id="${collaborator.id}">
                                Remove
                            </button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            $container.html(html);
        },

        sendInvitation: function() {
            const templateId = $('#share-modal').data('template-id');
            const email = $('#collaborator-email').val().trim();
            const permission = $('#collaboration-permission').val();
            const message = $('#invitation-message').val().trim();
            
            if (!email) {
                Collaboration.showMessage('error', 'Please enter an email address.');
                return;
            }
            
            if (!templateId) {
                Collaboration.showMessage('error', 'Template ID is missing.');
                return;
            }
            
            const $btn = $('#send-invitation');
            const originalText = $btn.text();
            
            $btn.prop('disabled', true).text(businesscraftCollaboration.strings.sharing);
            
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'templates/' + templateId + '/share',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    email: email,
                    permission: permission,
                    message: message
                }),
                success: function(response) {
                    if (response.success) {
                        Collaboration.showMessage('success', businesscraftCollaboration.strings.shareSuccess);
                        
                        // Clear form
                        $('#collaborator-email').val('');
                        $('#invitation-message').val('');
                        
                        // Reload collaborators
                        Collaboration.loadCollaborators(templateId);
                    } else {
                        Collaboration.showMessage('error', response.message || businesscraftCollaboration.strings.shareError);
                    }
                },
                error: function() {
                    Collaboration.showMessage('error', businesscraftCollaboration.strings.shareError);
                },
                complete: function() {
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        },

        updatePermissions: function() {
            const $select = $(this);
            const collaborationId = $select.data('collaboration-id');
            const newPermission = $select.val();
            
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'collaboration/' + collaborationId + '/permissions',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    permission: newPermission
                }),
                success: function(response) {
                    if (response.success) {
                        Collaboration.showMessage('success', businesscraftCollaboration.strings.permissionUpdated);
                    } else {
                        Collaboration.showMessage('error', response.message);
                        // Revert select value
                        $select.val($select.data('original-value'));
                    }
                },
                error: function() {
                    Collaboration.showMessage('error', 'Failed to update permissions.');
                    // Revert select value
                    $select.val($select.data('original-value'));
                }
            });
        },

        removeCollaborator: function() {
            if (!confirm(businesscraftCollaboration.strings.confirmRemoveCollaborator)) {
                return;
            }
            
            const $btn = $(this);
            const collaborationId = $btn.data('collaboration-id');
            
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'collaboration/' + collaborationId,
                method: 'DELETE',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $btn.closest('.collaborator-item').fadeOut();
                        Collaboration.showMessage('success', 'Collaborator removed successfully.');
                    } else {
                        Collaboration.showMessage('error', response.message);
                    }
                },
                error: function() {
                    Collaboration.showMessage('error', 'Failed to remove collaborator.');
                }
            });
        },

        showCommentForm: function() {
            const templateId = $(this).data('template-id');
            
            // Create comment form if it doesn't exist
            if (!$('#comment-form').length) {
                this.createCommentForm();
            }
            
            $('#comment-form').data('template-id', templateId).show();
        },

        createCommentForm: function() {
            const formHtml = `
                <div id="comment-form" class="comment-form" style="display: none;">
                    <h4>Add Comment</h4>
                    <textarea id="comment-text" rows="4" placeholder="Add your comment..."></textarea>
                    <div class="comment-actions">
                        <button id="submit-comment" class="btn-primary">Add Comment</button>
                        <button id="cancel-comment" class="btn-secondary">Cancel</button>
                    </div>
                </div>
            `;
            
            $('.template-actions').append(formHtml);
            
            $('#cancel-comment').on('click', function() {
                $('#comment-form').hide();
                $('#comment-text').val('');
            });
        },

        submitComment: function() {
            const templateId = $('#comment-form').data('template-id');
            const comment = $('#comment-text').val().trim();
            
            if (!comment) {
                Collaboration.showMessage('error', 'Please enter a comment.');
                return;
            }
            
            const $btn = $('#submit-comment');
            const originalText = $btn.text();
            
            $btn.prop('disabled', true).text('Adding comment...');
            
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'templates/' + templateId + '/comments',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    comment: comment
                }),
                success: function(response) {
                    if (response.success) {
                        Collaboration.showMessage('success', businesscraftCollaboration.strings.commentAdded);
                        $('#comment-text').val('');
                        $('#comment-form').hide();
                        
                        // Reload comments if comments section exists
                        if ($('.template-comments').length) {
                            Collaboration.loadComments(templateId);
                        }
                    } else {
                        Collaboration.showMessage('error', response.message || businesscraftCollaboration.strings.commentError);
                    }
                },
                error: function() {
                    Collaboration.showMessage('error', businesscraftCollaboration.strings.commentError);
                },
                complete: function() {
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        },

        loadComments: function(templateId) {
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'templates/' + templateId + '/comments',
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce
                },
                success: function(response) {
                    if (response.success) {
                        Collaboration.renderComments(response.comments);
                    }
                }
            });
        },

        renderComments: function(comments) {
            const $container = $('.template-comments');
            
            if (comments.length === 0) {
                $container.html('<p>No comments yet.</p>');
                return;
            }
            
            let html = '<div class="comments-list">';
            
            comments.forEach(function(comment) {
                const date = new Date(comment.created_at).toLocaleDateString();
                html += `
                    <div class="comment-item">
                        <div class="comment-header">
                            <strong>${comment.user_name}</strong>
                            <span class="comment-date">${date}</span>
                        </div>
                        <div class="comment-content">${comment.comment}</div>
                        <div class="comment-actions">
                            <button class="reply-comment-btn" data-comment-id="${comment.id}">Reply</button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            $container.html(html);
        },

        loadCollaborationInvitations: function() {
            // Load pending invitations for current user
            $.ajax({
                url: businesscraftCollaboration.restUrl + 'collaboration/invitations',
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftCollaboration.nonce
                },
                success: function(response) {
                    if (response.success && response.invitations.length > 0) {
                        Collaboration.showInvitationsNotification(response.invitations);
                    }
                }
            });
        },

        showInvitationsNotification: function(invitations) {
            const count = invitations.length;
            const message = count === 1 ? 
                'You have 1 collaboration invitation' : 
                `You have ${count} collaboration invitations`;
            
            const notificationHtml = `
                <div class="collaboration-notifications">
                    <div class="notification-header">
                        <span>🤝 ${message}</span>
                        <button class="view-invitations-btn">View</button>
                    </div>
                </div>
            `;
            
            $('body').prepend(notificationHtml);
            
            $('.view-invitations-btn').on('click', function() {
                Collaboration.showInvitationsModal(invitations);
            });
        },

        showMessage: function(type, message) {
            // Create or update message container
            let $message = $('.collaboration-message');
            
            if (!$message.length) {
                $message = $('<div class="collaboration-message"></div>');
                $('body').append($message);
            }
            
            $message
                .removeClass('success error')
                .addClass(type)
                .text(message)
                .fadeIn();
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        Collaboration.init();
    });

    // Make Collaboration available globally
    window.BusinessCraftCollaboration = Collaboration;

})(jQuery);

<?php
/**
 * Automated Sector Data Update System for ChatGABI
 * 
 * This system provides automated updates for business sector data across
 * Ghana, Kenya, Nigeria, and South Africa using AI-powered intelligence
 * generation and WordPress cron jobs.
 *
 * @package ChatGABI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main Sector Data Updater Class
 */
class ChatGABI_Sector_Data_Updater {
    
    private $countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
    private $openai_api_key;
    private $update_log_table;
    private $max_tokens_per_sector = 400;
    
    public function __construct() {
        global $wpdb;
        $this->update_log_table = $wpdb->prefix . 'chatgabi_sector_update_logs';
        $this->openai_api_key = get_option('businesscraft_ai_openai_api_key');
        
        // Initialize hooks
        add_action('init', array($this, 'init_cron_schedules'));
        add_action('chatgabi_weekly_sector_update', array($this, 'run_weekly_update'));
        add_action('chatgabi_trending_sector_update', array($this, 'update_trending_sectors'));
        
        // Admin AJAX handlers
        add_action('wp_ajax_chatgabi_manual_sector_update', array($this, 'handle_manual_update'));
        add_action('wp_ajax_chatgabi_get_update_logs', array($this, 'get_update_logs'));
        add_action('wp_ajax_chatgabi_toggle_auto_updates', array($this, 'toggle_auto_updates'));
    }
    
    /**
     * Initialize custom cron schedules
     */
    public function init_cron_schedules() {
        add_filter('cron_schedules', array($this, 'add_custom_cron_schedules'));
        
        // Schedule weekly updates if not already scheduled
        if (!wp_next_scheduled('chatgabi_weekly_sector_update')) {
            wp_schedule_event(time(), 'weekly', 'chatgabi_weekly_sector_update');
        }
        
        // Schedule trending sector updates (daily)
        if (!wp_next_scheduled('chatgabi_trending_sector_update')) {
            wp_schedule_event(time(), 'daily', 'chatgabi_trending_sector_update');
        }
    }
    
    /**
     * Add custom cron schedules
     */
    public function add_custom_cron_schedules($schedules) {
        $schedules['weekly'] = array(
            'interval' => 604800, // 7 days
            'display'  => __('Weekly', 'chatgabi')
        );
        
        $schedules['bi_weekly'] = array(
            'interval' => 1209600, // 14 days
            'display'  => __('Bi-Weekly', 'chatgabi')
        );
        
        return $schedules;
    }
    
    /**
     * Run weekly sector data update
     */
    public function run_weekly_update() {
        if (!$this->is_auto_updates_enabled()) {
            $this->log_update('weekly_update_skipped', 'Auto-updates disabled', 'info');
            return;
        }
        
        $this->log_update('weekly_update_started', 'Starting weekly sector data update', 'info');
        
        foreach ($this->countries as $country) {
            $this->update_country_sectors($country, 'weekly_update');
        }
        
        $this->log_update('weekly_update_completed', 'Weekly sector data update completed', 'success');
    }
    
    /**
     * Update trending sectors based on user analytics
     */
    public function update_trending_sectors() {
        if (!$this->is_auto_updates_enabled()) {
            return;
        }

        $trending_sectors = $this->get_trending_sectors();

        if (empty($trending_sectors)) {
            $this->log_update('trending_update_no_data', 'No trending sectors found', 'info');
            return;
        }

        $this->log_update('trending_update_started', 'Starting trending sectors update', 'info');

        foreach ($trending_sectors as $sector_data) {
            // First try to scrape fresh data from web sources
            $scraped_data = $this->scrape_sector_data($sector_data['country'], $sector_data['sector']);

            if ($scraped_data) {
                $this->log_update(
                    'sector_scraped',
                    "Scraped fresh data for {$sector_data['sector']} in {$sector_data['country']}",
                    'success',
                    $sector_data['country'],
                    $sector_data['sector']
                );
            }

            $this->update_specific_sector(
                $sector_data['country'],
                $sector_data['sector'],
                'trending_update',
                $scraped_data
            );
        }

        $this->log_update('trending_update_completed', 'Trending sectors update completed', 'success');
    }
    
    /**
     * Update all sectors for a specific country
     */
    public function update_country_sectors($country, $update_type = 'manual') {
        $start_time = microtime(true);
        
        try {
            // Load existing sector data
            $existing_data = $this->load_existing_sector_data($country);
            
            if (!$existing_data) {
                $this->log_update('country_update_failed', "Failed to load existing data for {$country}", 'error');
                return false;
            }
            
            $updated_sectors = 0;
            $failed_sectors = 0;
            
            foreach ($existing_data['sectors'] as $index => $sector) {
                $sector_name = $sector['sector_name'];
                
                // Check if sector needs update (based on freshness)
                if (!$this->sector_needs_update($country, $sector_name, $update_type)) {
                    continue;
                }

                // Try to scrape fresh data for this sector
                $scraped_data = null;
                if ($update_type === 'weekly_update') {
                    $scraped_data = $this->scrape_sector_data($country, $sector_name);
                }

                $updated_sector = $this->generate_updated_sector_data($country, $sector_name, $sector, $scraped_data);

                if ($updated_sector) {
                    $existing_data['sectors'][$index] = $updated_sector;
                    $updated_sectors++;
                    
                    $this->log_update(
                        'sector_updated', 
                        "Updated {$sector_name} for {$country}", 
                        'success',
                        $country,
                        $sector_name
                    );
                } else {
                    $failed_sectors++;
                    
                    $this->log_update(
                        'sector_update_failed', 
                        "Failed to update {$sector_name} for {$country}", 
                        'error',
                        $country,
                        $sector_name
                    );
                }
                
                // Rate limiting to avoid API overload
                sleep(2);
            }
            
            // Save updated data if any sectors were updated
            if ($updated_sectors > 0) {
                $save_result = $this->save_updated_sector_data($country, $existing_data);
                
                if ($save_result) {
                    $execution_time = round(microtime(true) - $start_time, 2);
                    
                    $this->log_update(
                        'country_update_completed', 
                        "Updated {$updated_sectors} sectors for {$country} in {$execution_time}s", 
                        'success',
                        $country
                    );
                    
                    return true;
                } else {
                    $this->log_update(
                        'country_save_failed', 
                        "Failed to save updated data for {$country}", 
                        'error',
                        $country
                    );
                    
                    return false;
                }
            }
            
            $this->log_update(
                'country_no_updates', 
                "No sectors needed updating for {$country}", 
                'info',
                $country
            );
            
            return true;
            
        } catch (Exception $e) {
            $this->log_update(
                'country_update_exception', 
                "Exception updating {$country}: " . $e->getMessage(), 
                'error',
                $country
            );
            
            return false;
        }
    }
    
    /**
     * Load existing sector data for a country
     */
    private function load_existing_sector_data($country) {
        return load_business_dataset_by_country($country);
    }

    /**
     * Check if a sector needs updating
     */
    private function sector_needs_update($country, $sector_name, $update_type) {
        // For trending updates, always update
        if ($update_type === 'trending_update') {
            return true;
        }

        // For weekly updates, check last update time
        global $wpdb;

        $last_update = $wpdb->get_var($wpdb->prepare(
            "SELECT timestamp FROM {$this->update_log_table}
             WHERE action = 'sector_updated'
             AND country = %s
             AND sector = %s
             AND status = 'success'
             ORDER BY timestamp DESC
             LIMIT 1",
            $country,
            $sector_name
        ));

        if (!$last_update) {
            return true; // Never updated
        }

        // Update if last update was more than 7 days ago
        $days_since_update = (time() - strtotime($last_update)) / (24 * 60 * 60);
        return $days_since_update >= 7;
    }

    /**
     * Generate updated sector data using OpenAI with optional scraped data
     */
    private function generate_updated_sector_data($country, $sector_name, $existing_sector, $scraped_data = null) {
        if (empty($this->openai_api_key)) {
            $this->log_update('api_key_missing', 'OpenAI API key not configured', 'error');
            return false;
        }

        try {
            // Build prompt with scraped data integration
            $prompt = $this->build_sector_update_prompt($country, $sector_name, $existing_sector, $scraped_data);

            $response = $this->call_openai_api($prompt);

            if (!$response) {
                return false;
            }

            $updated_sector = $this->parse_ai_response($response, $existing_sector);

            // Add metadata about data sources
            if ($scraped_data) {
                $updated_sector['data_sources'] = array();
                foreach ($scraped_data as $source_data) {
                    $updated_sector['data_sources'][] = array(
                        'name' => $source_data['source'],
                        'type' => $source_data['source_type'],
                        'scraped_at' => $source_data['scraped_at']
                    );
                }
                $updated_sector['enhanced_with_web_data'] = true;
            }

            // Validate the updated sector data
            if ($this->validate_sector_data($updated_sector)) {
                return $updated_sector;
            } else {
                $this->log_update(
                    'sector_validation_failed',
                    "Generated data for {$sector_name} failed validation",
                    'error',
                    $country,
                    $sector_name
                );
                return false;
            }

        } catch (Exception $e) {
            $this->log_update(
                'sector_generation_exception',
                "Exception generating data for {$sector_name}: " . $e->getMessage(),
                'error',
                $country,
                $sector_name
            );
            return false;
        }
    }

    /**
     * Build AI prompt for sector update with scraped data integration
     */
    private function build_sector_update_prompt($country, $sector_name, $existing_sector, $scraped_data = null) {
        $current_date = date('Y-m-d');

        $prompt = "You are a business intelligence expert specializing in African markets. ";
        $prompt .= "Update the business sector data for {$sector_name} in {$country} with current 2025 information.\n\n";

        $prompt .= "CURRENT SECTOR DATA:\n";
        $prompt .= json_encode($existing_sector, JSON_PRETTY_PRINT) . "\n\n";

        // Add scraped data if available
        if ($scraped_data && !empty($scraped_data)) {
            $prompt .= "FRESH WEB-SCRAPED MARKET DATA:\n";
            $prompt .= "Use this real-time data to enhance accuracy and currency of your updates:\n\n";

            foreach ($scraped_data as $source_data) {
                $prompt .= "Source: {$source_data['source']} ({$source_data['source_type']})\n";
                $prompt .= "Scraped: {$source_data['scraped_at']}\n";

                if (isset($source_data['data']['market_size'])) {
                    $prompt .= "- Market Size: {$source_data['data']['market_size']}\n";
                }

                if (isset($source_data['data']['growth_rate'])) {
                    $prompt .= "- Growth Rate: {$source_data['data']['growth_rate']}\n";
                }

                if (isset($source_data['data']['investment_amount'])) {
                    $prompt .= "- Investment Activity: {$source_data['data']['investment_amount']}\n";
                }

                if (isset($source_data['data']['regulatory_updates'])) {
                    $prompt .= "- Regulatory Updates: " . implode('; ', $source_data['data']['regulatory_updates']) . "\n";
                }

                if (isset($source_data['data']['recent_developments'])) {
                    $prompt .= "- Recent Developments: " . implode('; ', $source_data['data']['recent_developments']) . "\n";
                }

                $prompt .= "\n";
            }

            $prompt .= "INTEGRATION INSTRUCTIONS:\n";
            $prompt .= "- Prioritize the scraped data over existing data where conflicts exist\n";
            $prompt .= "- Cross-reference multiple sources for accuracy\n";
            $prompt .= "- Include specific figures and dates from scraped sources\n";
            $prompt .= "- Mention data sources in regulatory_environment or market_size_and_growth sections\n\n";
        }

        $prompt .= "REQUIREMENTS:\n";
        $prompt .= "1. Maintain the exact same JSON structure\n";
        $prompt .= "2. Update market size, growth rates, and regulatory information to reflect 2025 realities\n";
        $prompt .= "3. Keep the overview concise but informative\n";
        $prompt .= "4. Ensure all data is specific to {$country}'s market context\n";
        $prompt .= "5. Include recent policy changes, market trends, and investment opportunities\n";
        $prompt .= "6. Keep total response under {$this->max_tokens_per_sector} tokens\n";

        if ($scraped_data) {
            $prompt .= "7. Integrate scraped data seamlessly into existing structure\n";
            $prompt .= "8. Cite specific sources where appropriate\n";
        }

        $prompt .= "\nFOCUS AREAS FOR UPDATE:\n";
        $prompt .= "- Current market size and growth projections for 2025\n";
        $prompt .= "- Recent regulatory changes or policy updates\n";
        $prompt .= "- New investment opportunities or funding programs\n";
        $prompt .= "- Emerging trends and technologies in this sector\n";
        $prompt .= "- Key challenges and opportunities specific to {$country}\n";

        if ($scraped_data) {
            $prompt .= "- Real-time market intelligence from web sources\n";
            $prompt .= "- Cross-validated data from multiple authoritative sources\n";
        }

        $prompt .= "\nReturn ONLY the updated JSON object with no additional text or formatting.";

        return $prompt;
    }

    /**
     * Call OpenAI API
     */
    private function call_openai_api($prompt) {
        $url = 'https://api.openai.com/v1/chat/completions';

        $data = array(
            'model' => 'gpt-4',
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'You are a business intelligence expert specializing in African markets. Provide accurate, current data in the exact JSON format requested.'
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => $this->max_tokens_per_sector,
            'temperature' => 0.3
        );

        $headers = array(
            'Authorization: Bearer ' . $this->openai_api_key,
            'Content-Type: application/json'
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code !== 200) {
            $this->log_update('openai_api_error', "OpenAI API returned HTTP {$http_code}", 'error');
            return false;
        }

        $decoded_response = json_decode($response, true);

        if (!$decoded_response || !isset($decoded_response['choices'][0]['message']['content'])) {
            $this->log_update('openai_response_invalid', 'Invalid response from OpenAI API', 'error');
            return false;
        }

        return $decoded_response['choices'][0]['message']['content'];
    }

    /**
     * Parse AI response and merge with existing data
     */
    private function parse_ai_response($ai_response, $existing_sector) {
        // Clean the response (remove any markdown formatting)
        $cleaned_response = trim($ai_response);
        $cleaned_response = preg_replace('/^```json\s*/', '', $cleaned_response);
        $cleaned_response = preg_replace('/\s*```$/', '', $cleaned_response);

        $parsed_data = json_decode($cleaned_response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log_update('ai_response_parse_error', 'Failed to parse AI response as JSON: ' . json_last_error_msg(), 'error');
            return false;
        }

        // Ensure required fields are present
        $required_fields = ['sector_name', 'overview', 'key_conditions'];
        foreach ($required_fields as $field) {
            if (!isset($parsed_data[$field])) {
                $parsed_data[$field] = $existing_sector[$field] ?? '';
            }
        }

        // Add update metadata
        $parsed_data['last_updated'] = current_time('mysql');
        $parsed_data['update_source'] = 'ai_generated';

        return $parsed_data;
    }

    /**
     * Validate sector data structure
     */
    private function validate_sector_data($sector_data) {
        if (!is_array($sector_data)) {
            return false;
        }

        // Required fields
        $required_fields = ['sector_name', 'overview', 'key_conditions'];
        foreach ($required_fields as $field) {
            if (!isset($sector_data[$field]) || empty($sector_data[$field])) {
                return false;
            }
        }

        // Validate key_conditions structure
        if (!is_array($sector_data['key_conditions'])) {
            return false;
        }

        $required_conditions = ['regulatory_environment', 'market_size_and_growth'];
        foreach ($required_conditions as $condition) {
            if (!isset($sector_data['key_conditions'][$condition])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Save updated sector data with backup
     */
    private function save_updated_sector_data($country, $updated_data) {
        $country_folders = [
            'Ghana' => 'ghana-business-data',
            'Kenya' => 'kenya-business-data',
            'Nigeria' => 'nigeria-business-data',
            'South Africa' => 'south-africa-business-data'
        ];

        $folder_name = $country_folders[$country] ?? null;
        if (!$folder_name) {
            return false;
        }

        $dataset_dir = WP_CONTENT_DIR . '/datasets/' . $folder_name . '/';

        // Create backup before updating
        if (!$this->create_backup($country, $dataset_dir)) {
            $this->log_update('backup_failed', "Failed to create backup for {$country}", 'error', $country);
            return false;
        }

        // Determine next version number
        $next_version = $this->get_next_version_number($dataset_dir, $country);
        $new_filename = $this->get_country_filename($country, $next_version);
        $new_file_path = $dataset_dir . $new_filename;

        // Save the updated data
        $json_data = json_encode($updated_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        if (file_put_contents($new_file_path, $json_data) === false) {
            $this->log_update('file_save_failed', "Failed to save updated data to {$new_file_path}", 'error', $country);
            return false;
        }

        $this->log_update('file_saved', "Saved updated data to {$new_filename}", 'success', $country);
        return true;
    }

    /**
     * Create backup of existing data
     */
    private function create_backup($country, $dataset_dir) {
        // Find the current latest file
        $current_file = $this->get_current_data_file($country, $dataset_dir);

        if (!$current_file || !file_exists($current_file)) {
            return false;
        }

        $backup_filename = basename($current_file, '.json') . '.backup.' . date('Y-m-d-H-i-s') . '.json';
        $backup_path = $dataset_dir . $backup_filename;

        return copy($current_file, $backup_path);
    }

    /**
     * Get current data file for a country
     */
    private function get_current_data_file($country, $dataset_dir) {
        $base_filename = $this->get_country_base_filename($country);

        // Look for versioned files
        $pattern = $dataset_dir . $base_filename . 'v*.json';
        $files = glob($pattern);

        if (empty($files)) {
            // Fall back to base file
            $base_file = $dataset_dir . $base_filename . '.json';
            return file_exists($base_file) ? $base_file : null;
        }

        // Sort files to get the latest version
        usort($files, function($a, $b) {
            $version_a = $this->extract_version_number($a);
            $version_b = $this->extract_version_number($b);
            return $version_b - $version_a;
        });

        return $files[0];
    }

    /**
     * Get next version number for a country
     */
    private function get_next_version_number($dataset_dir, $country) {
        $base_filename = $this->get_country_base_filename($country);
        $pattern = $dataset_dir . $base_filename . 'v*.json';
        $files = glob($pattern);

        if (empty($files)) {
            return 1;
        }

        $max_version = 0;
        foreach ($files as $file) {
            $version = $this->extract_version_number($file);
            if ($version > $max_version) {
                $max_version = $version;
            }
        }

        return $max_version + 1;
    }

    /**
     * Extract version number from filename
     */
    private function extract_version_number($filename) {
        if (preg_match('/v(\d+)\.json$/', $filename, $matches)) {
            return intval($matches[1]);
        }
        return 0;
    }

    /**
     * Get country base filename
     */
    private function get_country_base_filename($country) {
        $filenames = [
            'Ghana' => 'ghana_business_data',
            'Kenya' => 'kenya_business_data',
            'Nigeria' => 'nigeria_business_data',
            'South Africa' => 'south_africa_business_data'
        ];

        return $filenames[$country] ?? strtolower(str_replace(' ', '_', $country)) . '_business_data';
    }

    /**
     * Get filename for country with version
     */
    private function get_country_filename($country, $version) {
        $base = $this->get_country_base_filename($country);
        return $base . 'v' . $version . '.json';
    }

    /**
     * Get trending sectors based on user analytics
     */
    private function get_trending_sectors() {
        global $wpdb;

        // Get sectors that have been queried frequently in the last 7 days
        $table_name = $wpdb->prefix . 'chatgabi_sector_logs';

        $trending_sectors = $wpdb->get_results($wpdb->prepare(
            "SELECT country, detected_sector as sector, COUNT(*) as query_count
             FROM {$table_name}
             WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             AND detected_sector IS NOT NULL
             AND detected_sector != ''
             GROUP BY country, detected_sector
             HAVING query_count >= 3
             ORDER BY query_count DESC
             LIMIT 10"
        ), ARRAY_A);

        return $trending_sectors ?: array();
    }

    /**
     * Web scraping functionality for real-time market data
     */
    private function scrape_sector_data($country, $sector_name) {
        $this->log_update('scraping_started', "Starting web scraping for {$sector_name} in {$country}", 'info', $country, $sector_name);

        $scraped_data = array();

        // Get country-specific data sources
        $data_sources = $this->get_scraping_sources($country, $sector_name);

        foreach ($data_sources as $source) {
            $source_data = $this->scrape_single_source($source, $country, $sector_name);

            if ($source_data) {
                $scraped_data = array_merge($scraped_data, $source_data);

                $this->log_update(
                    'source_scraped',
                    "Successfully scraped {$source['name']} for {$sector_name}",
                    'success',
                    $country,
                    $sector_name
                );
            } else {
                $this->log_update(
                    'source_scrape_failed',
                    "Failed to scrape {$source['name']} for {$sector_name}",
                    'warning',
                    $country,
                    $sector_name
                );
            }

            // Rate limiting between sources
            sleep(1);
        }

        if (!empty($scraped_data)) {
            $this->log_update('scraping_completed', "Web scraping completed for {$sector_name} in {$country}", 'success', $country, $sector_name);
            return $scraped_data;
        }

        $this->log_update('scraping_no_data', "No data scraped for {$sector_name} in {$country}", 'info', $country, $sector_name);
        return null;
    }

    /**
     * Get web scraping sources for country and sector
     */
    private function get_scraping_sources($country, $sector_name) {
        $sources = array();

        // Government and regulatory sources by country
        switch ($country) {
            case 'Ghana':
                $sources = array(
                    array(
                        'name' => 'Ghana Investment Promotion Centre',
                        'url' => 'https://www.gipcghana.com/invest-in-ghana/sectors.html',
                        'type' => 'government',
                        'selectors' => array('.sector-info', '.investment-data')
                    ),
                    array(
                        'name' => 'Bank of Ghana',
                        'url' => 'https://www.bog.gov.gh/statistics/statistical-bulletin/',
                        'type' => 'financial',
                        'selectors' => array('.economic-data', '.sector-statistics')
                    ),
                    array(
                        'name' => 'Ghana Statistical Service',
                        'url' => 'https://statsghana.gov.gh/gssmain/storage/img/marqueeupdater/',
                        'type' => 'statistics',
                        'selectors' => array('.gdp-data', '.sector-growth')
                    )
                );
                break;

            case 'Kenya':
                $sources = array(
                    array(
                        'name' => 'Kenya Association of Manufacturers',
                        'url' => 'https://kam.co.ke/sectors/',
                        'type' => 'industry',
                        'selectors' => array('.sector-overview', '.manufacturing-data')
                    ),
                    array(
                        'name' => 'Central Bank of Kenya',
                        'url' => 'https://www.centralbank.go.ke/statistics/',
                        'type' => 'financial',
                        'selectors' => array('.economic-indicators', '.sector-performance')
                    ),
                    array(
                        'name' => 'Kenya National Bureau of Statistics',
                        'url' => 'https://www.knbs.or.ke/economic-statistics/',
                        'type' => 'statistics',
                        'selectors' => array('.gdp-statistics', '.sector-contribution')
                    )
                );
                break;

            case 'Nigeria':
                $sources = array(
                    array(
                        'name' => 'Nigerian Investment Promotion Commission',
                        'url' => 'https://nipc.gov.ng/investment-opportunities/',
                        'type' => 'government',
                        'selectors' => array('.investment-sectors', '.opportunity-data')
                    ),
                    array(
                        'name' => 'Central Bank of Nigeria',
                        'url' => 'https://www.cbn.gov.ng/statistics/',
                        'type' => 'financial',
                        'selectors' => array('.economic-data', '.sectoral-statistics')
                    ),
                    array(
                        'name' => 'National Bureau of Statistics Nigeria',
                        'url' => 'https://nigerianstat.gov.ng/elibrary/read/1241231',
                        'type' => 'statistics',
                        'selectors' => array('.gdp-report', '.sector-analysis')
                    )
                );
                break;

            case 'South Africa':
                $sources = array(
                    array(
                        'name' => 'InvestSA',
                        'url' => 'https://www.investsa.gov.za/south-african-investment-opportunities',
                        'type' => 'government',
                        'selectors' => array('.investment-opportunities', '.sector-profiles')
                    ),
                    array(
                        'name' => 'South African Reserve Bank',
                        'url' => 'https://www.resbank.co.za/en/home/<USER>/statistics',
                        'type' => 'financial',
                        'selectors' => array('.economic-statistics', '.sectoral-data')
                    ),
                    array(
                        'name' => 'Statistics South Africa',
                        'url' => 'http://www.statssa.gov.za/publications/StatsInBrief/StatsInBrief2023.pdf',
                        'type' => 'statistics',
                        'selectors' => array('.gdp-data', '.industry-statistics')
                    )
                );
                break;
        }

        // Add sector-specific sources
        $sector_sources = $this->get_sector_specific_sources($sector_name);
        $sources = array_merge($sources, $sector_sources);

        return $sources;
    }

    /**
     * Get sector-specific scraping sources
     */
    private function get_sector_specific_sources($sector_name) {
        $sources = array();

        // Fintech sources
        if (stripos($sector_name, 'fintech') !== false || stripos($sector_name, 'financial') !== false) {
            $sources[] = array(
                'name' => 'African Fintech Network',
                'url' => 'https://africanfintechnetwork.com/market-insights/',
                'type' => 'industry',
                'selectors' => array('.market-data', '.fintech-statistics')
            );
        }

        // Agriculture sources
        if (stripos($sector_name, 'agriculture') !== false || stripos($sector_name, 'agri') !== false) {
            $sources[] = array(
                'name' => 'African Development Bank Agriculture',
                'url' => 'https://www.afdb.org/en/topics/sectors/agriculture',
                'type' => 'development',
                'selectors' => array('.agriculture-data', '.sector-insights')
            );
        }

        // Technology sources
        if (stripos($sector_name, 'technology') !== false || stripos($sector_name, 'tech') !== false) {
            $sources[] = array(
                'name' => 'African Tech Roundup',
                'url' => 'https://africantechroundup.com/market-analysis/',
                'type' => 'industry',
                'selectors' => array('.tech-market-data', '.startup-statistics')
            );
        }

        // Energy sources
        if (stripos($sector_name, 'energy') !== false || stripos($sector_name, 'renewable') !== false) {
            $sources[] = array(
                'name' => 'African Energy Chamber',
                'url' => 'https://energychamber.org/market-intelligence/',
                'type' => 'industry',
                'selectors' => array('.energy-statistics', '.market-outlook')
            );
        }

        return $sources;
    }

    /**
     * Scrape data from a single source
     */
    private function scrape_single_source($source, $country, $sector_name) {
        try {
            // Use WordPress HTTP API for reliable requests
            $response = wp_remote_get($source['url'], array(
                'timeout' => 15,
                'user-agent' => 'ChatGABI Business Intelligence Bot 1.0',
                'headers' => array(
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                    'Upgrade-Insecure-Requests' => '1'
                )
            ));

            if (is_wp_error($response)) {
                $this->log_update(
                    'scrape_request_failed',
                    "HTTP request failed for {$source['name']}: " . $response->get_error_message(),
                    'error',
                    $country,
                    $sector_name
                );
                return null;
            }

            $http_code = wp_remote_retrieve_response_code($response);
            if ($http_code !== 200) {
                $this->log_update(
                    'scrape_http_error',
                    "HTTP {$http_code} error for {$source['name']}",
                    'error',
                    $country,
                    $sector_name
                );
                return null;
            }

            $html_content = wp_remote_retrieve_body($response);

            if (empty($html_content)) {
                $this->log_update(
                    'scrape_empty_response',
                    "Empty response from {$source['name']}",
                    'warning',
                    $country,
                    $sector_name
                );
                return null;
            }

            // Parse HTML and extract relevant data
            $extracted_data = $this->parse_html_content($html_content, $source, $sector_name);

            if ($extracted_data) {
                // Validate and structure the scraped data
                return $this->structure_scraped_data($extracted_data, $source, $country, $sector_name);
            }

            return null;

        } catch (Exception $e) {
            $this->log_update(
                'scrape_exception',
                "Exception scraping {$source['name']}: " . $e->getMessage(),
                'error',
                $country,
                $sector_name
            );
            return null;
        }
    }

    /**
     * Parse HTML content and extract relevant data
     */
    private function parse_html_content($html_content, $source, $sector_name) {
        // Simple HTML parsing without external dependencies
        $extracted_data = array();

        // Extract market size data
        $market_patterns = array(
            '/market\s+size[:\s]+([0-9.,]+\s*(?:billion|million|trillion)?\s*(?:USD|GHS|NGN|KES|ZAR)?)/i',
            '/valued\s+at[:\s]+([0-9.,]+\s*(?:billion|million|trillion)?\s*(?:USD|GHS|NGN|KES|ZAR)?)/i',
            '/worth[:\s]+([0-9.,]+\s*(?:billion|million|trillion)?\s*(?:USD|GHS|NGN|KES|ZAR)?)/i'
        );

        foreach ($market_patterns as $pattern) {
            if (preg_match($pattern, $html_content, $matches)) {
                $extracted_data['market_size'] = trim($matches[1]);
                break;
            }
        }

        // Extract growth rate data
        $growth_patterns = array(
            '/growth\s+rate[:\s]+([0-9.,]+%)/i',
            '/growing\s+at[:\s]+([0-9.,]+%)/i',
            '/annual\s+growth[:\s]+([0-9.,]+%)/i',
            '/CAGR[:\s]+([0-9.,]+%)/i'
        );

        foreach ($growth_patterns as $pattern) {
            if (preg_match($pattern, $html_content, $matches)) {
                $extracted_data['growth_rate'] = trim($matches[1]);
                break;
            }
        }

        // Extract investment data
        $investment_patterns = array(
            '/investment[:\s]+([0-9.,]+\s*(?:billion|million|trillion)?\s*(?:USD|GHS|NGN|KES|ZAR)?)/i',
            '/funding[:\s]+([0-9.,]+\s*(?:billion|million|trillion)?\s*(?:USD|GHS|NGN|KES|ZAR)?)/i',
            '/capital[:\s]+([0-9.,]+\s*(?:billion|million|trillion)?\s*(?:USD|GHS|NGN|KES|ZAR)?)/i'
        );

        foreach ($investment_patterns as $pattern) {
            if (preg_match($pattern, $html_content, $matches)) {
                $extracted_data['investment_amount'] = trim($matches[1]);
                break;
            }
        }

        // Extract regulatory information
        $regulatory_keywords = array('regulation', 'policy', 'law', 'compliance', 'framework', 'guidelines');
        foreach ($regulatory_keywords as $keyword) {
            $pattern = '/(' . preg_quote($keyword, '/') . '[^.]{0,200}\.)/i';
            if (preg_match($pattern, $html_content, $matches)) {
                $extracted_data['regulatory_info'][] = trim($matches[1]);
            }
        }

        // Extract recent news/updates
        $news_patterns = array(
            '/(?:announced|launched|introduced|established)[^.]{0,150}\./i',
            '/(?:new|recent|latest)[^.]{0,150}(?:initiative|program|policy|regulation)\./i'
        );

        foreach ($news_patterns as $pattern) {
            if (preg_match_all($pattern, $html_content, $matches)) {
                foreach ($matches[0] as $match) {
                    $extracted_data['recent_updates'][] = trim($match);
                }
            }
        }

        // Extract sector-specific keywords and context
        $sector_keywords = explode(' ', strtolower($sector_name));
        foreach ($sector_keywords as $keyword) {
            if (strlen($keyword) > 3) { // Skip short words
                $pattern = '/([^.]{0,100}' . preg_quote($keyword, '/') . '[^.]{0,100}\.)/i';
                if (preg_match_all($pattern, $html_content, $matches)) {
                    foreach (array_slice($matches[0], 0, 3) as $match) { // Limit to 3 matches
                        $extracted_data['sector_context'][] = trim($match);
                    }
                }
            }
        }

        return !empty($extracted_data) ? $extracted_data : null;
    }

    /**
     * Structure scraped data into usable format
     */
    private function structure_scraped_data($extracted_data, $source, $country, $sector_name) {
        $structured_data = array(
            'source' => $source['name'],
            'source_type' => $source['type'],
            'scraped_at' => current_time('mysql'),
            'country' => $country,
            'sector' => $sector_name,
            'data' => array()
        );

        // Structure market data
        if (isset($extracted_data['market_size'])) {
            $structured_data['data']['market_size'] = $extracted_data['market_size'];
        }

        if (isset($extracted_data['growth_rate'])) {
            $structured_data['data']['growth_rate'] = $extracted_data['growth_rate'];
        }

        if (isset($extracted_data['investment_amount'])) {
            $structured_data['data']['investment_amount'] = $extracted_data['investment_amount'];
        }

        // Structure regulatory information
        if (isset($extracted_data['regulatory_info']) && !empty($extracted_data['regulatory_info'])) {
            $structured_data['data']['regulatory_updates'] = array_slice($extracted_data['regulatory_info'], 0, 3);
        }

        // Structure recent updates
        if (isset($extracted_data['recent_updates']) && !empty($extracted_data['recent_updates'])) {
            $structured_data['data']['recent_developments'] = array_slice($extracted_data['recent_updates'], 0, 3);
        }

        // Structure sector context
        if (isset($extracted_data['sector_context']) && !empty($extracted_data['sector_context'])) {
            $structured_data['data']['sector_insights'] = array_slice($extracted_data['sector_context'], 0, 5);
        }

        return $structured_data;
    }

    /**
     * Update a specific sector
     */
    private function update_specific_sector($country, $sector_name, $update_type, $scraped_data = null) {
        $existing_data = $this->load_existing_sector_data($country);

        if (!$existing_data) {
            return false;
        }

        // Find the sector in the data
        $sector_index = null;
        foreach ($existing_data['sectors'] as $index => $sector) {
            if (strcasecmp($sector['sector_name'], $sector_name) === 0) {
                $sector_index = $index;
                break;
            }
        }

        if ($sector_index === null) {
            $this->log_update(
                'sector_not_found',
                "Sector {$sector_name} not found in {$country} data",
                'error',
                $country,
                $sector_name
            );
            return false;
        }

        $updated_sector = $this->generate_updated_sector_data(
            $country,
            $sector_name,
            $existing_data['sectors'][$sector_index]
        );

        if ($updated_sector) {
            $existing_data['sectors'][$sector_index] = $updated_sector;
            return $this->save_updated_sector_data($country, $existing_data);
        }

        return false;
    }

    /**
     * Check if auto-updates are enabled
     */
    private function is_auto_updates_enabled() {
        return get_option('chatgabi_auto_sector_updates', true);
    }

    /**
     * Handle manual update AJAX request
     */
    public function handle_manual_update() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_sector_update') || !current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $country = sanitize_text_field($_POST['country'] ?? '');
        $sector = sanitize_text_field($_POST['sector'] ?? '');

        if (empty($country)) {
            wp_send_json_error('Country is required');
            return;
        }

        if (!in_array($country, $this->countries)) {
            wp_send_json_error('Invalid country');
            return;
        }

        $this->log_update('manual_update_started', "Manual update initiated for {$country}", 'info', $country);

        if (!empty($sector)) {
            // Update specific sector
            $result = $this->update_specific_sector($country, $sector, 'manual');

            if ($result) {
                wp_send_json_success("Successfully updated {$sector} for {$country}");
            } else {
                wp_send_json_error("Failed to update {$sector} for {$country}");
            }
        } else {
            // Update all sectors for country
            $result = $this->update_country_sectors($country, 'manual');

            if ($result) {
                wp_send_json_success("Successfully updated all sectors for {$country}");
            } else {
                wp_send_json_error("Failed to update sectors for {$country}");
            }
        }
    }

    /**
     * Get update logs for admin interface
     */
    public function get_update_logs() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_sector_update') || !current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        global $wpdb;

        $page = intval($_POST['page'] ?? 1);
        $per_page = 20;
        $offset = ($page - 1) * $per_page;

        $country_filter = sanitize_text_field($_POST['country_filter'] ?? '');
        $status_filter = sanitize_text_field($_POST['status_filter'] ?? '');

        $where_conditions = array('1=1');
        $where_values = array();

        if (!empty($country_filter)) {
            $where_conditions[] = 'country = %s';
            $where_values[] = $country_filter;
        }

        if (!empty($status_filter)) {
            $where_conditions[] = 'status = %s';
            $where_values[] = $status_filter;
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Get total count
        $total_query = "SELECT COUNT(*) FROM {$this->update_log_table} WHERE {$where_clause}";
        if (!empty($where_values)) {
            $total_query = $wpdb->prepare($total_query, $where_values);
        }
        $total_logs = $wpdb->get_var($total_query);

        // Get logs
        $logs_query = "SELECT * FROM {$this->update_log_table} WHERE {$where_clause} ORDER BY timestamp DESC LIMIT %d OFFSET %d";
        $query_values = array_merge($where_values, array($per_page, $offset));
        $logs = $wpdb->get_results($wpdb->prepare($logs_query, $query_values), ARRAY_A);

        wp_send_json_success(array(
            'logs' => $logs,
            'total' => $total_logs,
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil($total_logs / $per_page)
        ));
    }

    /**
     * Toggle auto-updates setting
     */
    public function toggle_auto_updates() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_sector_update') || !current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $enabled = $_POST['enabled'] === 'true';

        update_option('chatgabi_auto_sector_updates', $enabled);

        $status = $enabled ? 'enabled' : 'disabled';
        $this->log_update('auto_updates_toggled', "Auto-updates {$status}", 'info');

        wp_send_json_success(array(
            'enabled' => $enabled,
            'message' => "Auto-updates {$status} successfully"
        ));
    }

    /**
     * Get system status for admin dashboard
     */
    public function get_system_status() {
        global $wpdb;

        // Check if update logs table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->update_log_table}'") === $this->update_log_table;

        // Get recent update statistics
        $recent_updates = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->update_log_table}
             WHERE action = 'sector_updated'
             AND status = 'success'
             AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        ));

        $failed_updates = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->update_log_table}
             WHERE status = 'error'
             AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        ));

        // Check next scheduled update
        $next_weekly_update = wp_next_scheduled('chatgabi_weekly_sector_update');
        $next_trending_update = wp_next_scheduled('chatgabi_trending_sector_update');

        return array(
            'table_exists' => $table_exists,
            'auto_updates_enabled' => $this->is_auto_updates_enabled(),
            'api_key_configured' => !empty($this->openai_api_key),
            'recent_updates' => intval($recent_updates),
            'failed_updates' => intval($failed_updates),
            'next_weekly_update' => $next_weekly_update ? date('Y-m-d H:i:s', $next_weekly_update) : null,
            'next_trending_update' => $next_trending_update ? date('Y-m-d H:i:s', $next_trending_update) : null,
            'countries_supported' => count($this->countries),
            'max_tokens_per_sector' => $this->max_tokens_per_sector
        );
    }

    /**
     * Log update activity
     */
    private function log_update($action, $message, $status, $country = null, $sector = null) {
        global $wpdb;

        $wpdb->insert(
            $this->update_log_table,
            array(
                'action' => $action,
                'message' => $message,
                'status' => $status,
                'country' => $country,
                'sector' => $sector,
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s')
        );

        // Also log to WordPress error log for debugging
        error_log("ChatGABI Sector Update [{$status}]: {$message}");
    }
}

/**
 * Create sector update logs table
 */
function chatgabi_create_sector_update_logs_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_sector_update_logs';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        action varchar(100) NOT NULL,
        message text NOT NULL,
        status varchar(20) NOT NULL,
        country varchar(50) DEFAULT NULL,
        sector varchar(255) DEFAULT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY action (action),
        KEY status (status),
        KEY country (country),
        KEY sector (sector),
        KEY timestamp (timestamp)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Initialize sector data updater on theme activation
 */
function chatgabi_init_sector_updater() {
    // Create the update logs table
    chatgabi_create_sector_update_logs_table();

    // Set default options
    add_option('chatgabi_auto_sector_updates', true);
    add_option('chatgabi_sector_update_frequency', 'weekly');
    add_option('chatgabi_trending_update_enabled', true);
}

// Hook into theme activation
add_action('after_switch_theme', 'chatgabi_init_sector_updater');

// Initialize the updater
new ChatGABI_Sector_Data_Updater();

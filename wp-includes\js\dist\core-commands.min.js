/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,a)=>{for(var o in a)e.o(a,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:a[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{privateApis:()=>P});const a=window.wp.commands,o=window.wp.i18n,s=window.wp.primitives,n=window.ReactJSXRuntime,i=(0,n.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(s.<PERSON>,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})}),r=window.wp.url,c=window.wp.coreData,d=window.wp.data,l=window.wp.element,p=window.wp.notices,m=window.wp.router,w=window.wp.privateApis,{lock:h,unlock:u}=(0,w.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/core-commands"),{useHistory:g}=u(m.privateApis);const v=(0,n.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(s.Path,{d:"m7.3 9.7 1.4 1.4c.2-.2.3-.3.4-.5 0 0 0-.1.1-.1.3-.5.4-1.1.3-1.6L12 7 9 4 7.2 6.5c-.6-.1-1.1 0-1.6.3 0 0-.1 0-.1.1-.3.1-.4.2-.6.4l1.4 1.4L4 11v1h1l2.3-2.3zM4 20h9v-1.5H4V20zm0-5.5V16h16v-1.5H4z"})}),_=(0,n.jsxs)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,n.jsx)(s.Path,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,n.jsx)(s.Path,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]}),y=(0,n.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(s.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),b=(0,n.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(s.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.******* 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-17.6 1L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),k=(0,n.jsx)(s.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(s.Path,{d:"M12 4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14.5c-3.6 0-6.5-2.9-6.5-6.5S8.4 5.5 12 5.5s6.5 2.9 6.5 6.5-2.9 6.5-6.5 6.5zM9 16l4.5-3L15 8.4l-4.5 3L9 16z"})}),f=(0,n.jsx)(s.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(s.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M20 12a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.5 0a6.5 6.5 0 0 1-6.5 6.5v-13a6.5 6.5 0 0 1 6.5 6.5Z"})}),x=(0,n.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(s.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.******* 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.******* 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),T=window.wp.compose,L=window.wp.htmlEntities;const{useHistory:V}=u(m.privateApis),C={post:v,page:_,wp_template:y,wp_template_part:b};const S=e=>function({search:t}){const a=V(),{isBlockBasedTheme:s,canCreateTemplate:n}=(0,d.useSelect)((e=>({isBlockBasedTheme:e(c.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:e(c.store).canUser("create",{kind:"postType",name:"wp_template"})})),[]),i=function(e){const[t,a]=(0,l.useState)(""),o=(0,T.useDebounce)(a,250);return(0,l.useEffect)((()=>(o(e),()=>o.cancel())),[o,e]),t}(t),{records:p,isLoading:m}=(0,d.useSelect)((t=>{if(!i)return{isLoading:!1};const a={search:i,per_page:10,orderby:"relevance",status:["publish","future","draft","pending","private"]};return{records:t(c.store).getEntityRecords("postType",e,a),isLoading:!t(c.store).hasFinishedResolution("getEntityRecords",["postType",e,a])}}),[i]);return{commands:(0,l.useMemo)((()=>(null!=p?p:[]).map((t=>{const i={name:e+"-"+t.id,searchLabel:t.title?.rendered+" "+t.id,label:t.title?.rendered?(0,L.decodeEntities)(t.title?.rendered):(0,o.__)("(no title)"),icon:C[e]};if(!n||"post"===e||"page"===e&&!s)return{...i,callback:({close:e})=>{const a={post:t.id,action:"edit"},o=(0,r.addQueryArgs)("post.php",a);document.location=o,e()}};const c=(0,r.getPath)(window.location.href)?.includes("site-editor.php");return{...i,callback:({close:o})=>{c?a.navigate(`/${e}/${t.id}?canvas=edit`):document.location=(0,r.addQueryArgs)("site-editor.php",{p:`/${e}/${t.id}`,canvas:"edit"}),o()}}}))),[n,p,s,a]),isLoading:m}},j=e=>function({search:t}){const a=V(),{isBlockBasedTheme:s,canCreateTemplate:n}=(0,d.useSelect)((t=>({isBlockBasedTheme:t(c.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:t(c.store).canUser("create",{kind:"postType",name:e})})),[]),{records:i,isLoading:p}=(0,d.useSelect)((t=>{const{getEntityRecords:a}=t(c.store),o={per_page:-1};return{records:a("postType",e,o),isLoading:!t(c.store).hasFinishedResolution("getEntityRecords",["postType",e,o])}}),[]),m=(0,l.useMemo)((()=>function(e=[],t=""){if(!Array.isArray(e)||!e.length)return[];if(!t)return e;const a=[],o=[];for(let s=0;s<e.length;s++){const n=e[s];n?.title?.raw?.toLowerCase()?.includes(t?.toLowerCase())?a.push(n):o.push(n)}return a.concat(o)}(i,t).slice(0,10)),[i,t]);return{commands:(0,l.useMemo)((()=>{if(!n||!s&&"wp_template_part"===!e)return[];const t=(0,r.getPath)(window.location.href)?.includes("site-editor.php"),i=[];return i.push(...m.map((s=>({name:e+"-"+s.id,searchLabel:s.title?.rendered+" "+s.id,label:s.title?.rendered?s.title?.rendered:(0,o.__)("(no title)"),icon:C[e],callback:({close:o})=>{t?a.navigate(`/${e}/${s.id}?canvas=edit`):document.location=(0,r.addQueryArgs)("site-editor.php",{p:`/${e}/${s.id}`,canvas:"edit"}),o()}})))),m?.length>0&&"wp_template_part"===e&&i.push({name:"core/edit-site/open-template-parts",label:(0,o.__)("Template parts"),icon:b,callback:({close:e})=>{t?a.navigate("/pattern?postType=wp_template_part&categoryId=all-parts"):document.location=(0,r.addQueryArgs)("site-editor.php",{p:"/pattern",postType:"wp_template_part",categoryId:"all-parts"}),e()}}),i}),[n,s,m,a]),isLoading:p}};const P={};h(P,{useCommands:function(){(0,a.useCommand)({name:"core/add-new-post",label:(0,o.__)("Add new post"),icon:i,callback:()=>{document.location.assign("post-new.php")}}),(0,a.useCommandLoader)({name:"core/add-new-page",hook:function(){const e=(0,r.getPath)(window.location.href)?.includes("site-editor.php"),t=g(),a=(0,d.useSelect)((e=>e(c.store).getCurrentTheme()?.is_block_theme),[]),{saveEntityRecord:s}=(0,d.useDispatch)(c.store),{createErrorNotice:n}=(0,d.useDispatch)(p.store),m=(0,l.useCallback)((async({close:e})=>{try{const e=await s("postType","page",{status:"draft"},{throwOnError:!0});e?.id&&t.navigate(`/page/${e.id}?canvas=edit`)}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,o.__)("An error occurred while creating the item.");n(t,{type:"snackbar"})}finally{e()}}),[n,t,s]);return{isLoading:!1,commands:(0,l.useMemo)((()=>{const t=e&&a?m:()=>document.location.href="post-new.php?post_type=page";return[{name:"core/add-new-page",label:(0,o.__)("Add new page"),icon:i,callback:t}]}),[m,e,a])}}}),(0,a.useCommandLoader)({name:"core/edit-site/navigate-pages",hook:S("page")}),(0,a.useCommandLoader)({name:"core/edit-site/navigate-posts",hook:S("post")}),(0,a.useCommandLoader)({name:"core/edit-site/navigate-templates",hook:j("wp_template")}),(0,a.useCommandLoader)({name:"core/edit-site/navigate-template-parts",hook:j("wp_template_part")}),(0,a.useCommandLoader)({name:"core/edit-site/basic-navigation",hook:function(){const e=V(),t=(0,r.getPath)(window.location.href)?.includes("site-editor.php"),{isBlockBasedTheme:a,canCreateTemplate:s}=(0,d.useSelect)((e=>({isBlockBasedTheme:e(c.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:e(c.store).canUser("create",{kind:"postType",name:"wp_template"})})),[]);return{commands:(0,l.useMemo)((()=>{const n=[];return s&&a&&(n.push({name:"core/edit-site/open-navigation",label:(0,o.__)("Navigation"),icon:k,callback:({close:a})=>{t?e.navigate("/navigation"):document.location=(0,r.addQueryArgs)("site-editor.php",{p:"/navigation"}),a()}}),n.push({name:"core/edit-site/open-styles",label:(0,o.__)("Styles"),icon:f,callback:({close:a})=>{t?e.navigate("/styles"):document.location=(0,r.addQueryArgs)("site-editor.php",{p:"/styles"}),a()}}),n.push({name:"core/edit-site/open-pages",label:(0,o.__)("Pages"),icon:_,callback:({close:a})=>{t?e.navigate("/page"):document.location=(0,r.addQueryArgs)("site-editor.php",{p:"/page"}),a()}}),n.push({name:"core/edit-site/open-templates",label:(0,o.__)("Templates"),icon:y,callback:({close:a})=>{t?e.navigate("/template"):document.location=(0,r.addQueryArgs)("site-editor.php",{p:"/template"}),a()}})),n.push({name:"core/edit-site/open-patterns",label:(0,o.__)("Patterns"),icon:x,callback:({close:a})=>{s?(t?e.navigate("/pattern"):document.location=(0,r.addQueryArgs)("site-editor.php",{p:"/pattern"}),a()):document.location.href="edit.php?post_type=wp_block"}}),n}),[e,t,s,a]),isLoading:!1}},context:"site-editor"})}}),(window.wp=window.wp||{}).coreCommands=t})();
/**
 * ChatGABI Engagement Analytics Dashboard - Chart.js Implementation
 */

jQuery(document).ready(function($) {
    'use strict';

    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded!');
        return;
    }

    // Global chart instances
    let charts = {};

    // Get analytics data from the page
    let analyticsData;
    try {
        const dataElement = document.getElementById('analyticsData');
        if (!dataElement) {
            console.error('Analytics data element not found!');
            return;
        }
        analyticsData = JSON.parse(dataElement.textContent);
        console.log('Analytics data loaded:', analyticsData);
    } catch (e) {
        console.error('Error parsing analytics data:', e);
        return;
    }

    // Chart.js default configuration
    Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#646970';
    
    // Color schemes
    const colors = {
        primary: '#0073aa',
        secondary: '#00a0d2',
        success: '#46b450',
        warning: '#ffb900',
        danger: '#dc3232',
        info: '#00a0d2',
        light: '#f1f1f1',
        dark: '#23282d'
    };
    
    const chartColors = [
        '#0073aa', '#00a0d2', '#46b450', '#ffb900', '#dc3232', 
        '#826eb4', '#c7a589', '#8c8f94', '#005177', '#135e96'
    ];

    /**
     * Initialize all charts
     */
    function initializeCharts() {
        try {
            console.log('Initializing charts...');
            initTopSectorsChart();
            initCountryBreakdownChart();
            initSessionDurationChart();
            initOpportunityUsageChart();
            setupEventHandlers();

            console.log('ChatGABI Analytics Dashboard initialized successfully');
        } catch (e) {
            console.error('Error initializing charts:', e);
        }
    }

    /**
     * Top Queried Sectors - Horizontal Bar Chart
     */
    function initTopSectorsChart() {
        try {
            console.log('Initializing top sectors chart...');
            const ctx = document.getElementById('topSectorsChart');
            if (!ctx) {
                console.warn('Top sectors chart canvas not found');
                return;
            }

            const sectors = analyticsData.top_sectors || {};
            const labels = Object.keys(sectors);
            const data = Object.values(sectors);

            console.log('Top sectors data:', { labels, data });

            if (labels.length === 0) {
                console.warn('No top sectors data available');
                ctx.parentElement.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No sector data available</p>';
                return;
            }

            charts.topSectors = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Queries',
                    data: data,
                    backgroundColor: chartColors.slice(0, labels.length),
                    borderColor: chartColors.slice(0, labels.length),
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false,
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.parsed.x} queries`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: '#f1f1f1'
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        console.log('Top sectors chart initialized');
        } catch (e) {
            console.error('Error initializing top sectors chart:', e);
        }
    }

    /**
     * Country Usage Breakdown - Pie Chart
     */
    function initCountryBreakdownChart() {
        const ctx = document.getElementById('countryBreakdownChart');
        if (!ctx) return;

        const countries = analyticsData.country_breakdown || {};
        const labels = Object.keys(countries);
        const data = Object.values(countries);

        // Country flag emojis
        const countryFlags = {
            'Ghana': '🇬🇭',
            'Kenya': '🇰🇪', 
            'Nigeria': '🇳🇬',
            'South Africa': '🇿🇦'
        };

        const labelsWithFlags = labels.map(country => 
            `${countryFlags[country] || '🌍'} ${country}`
        );

        charts.countryBreakdown = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labelsWithFlags,
                datasets: [{
                    data: data,
                    backgroundColor: chartColors.slice(0, labels.length),
                    borderColor: '#ffffff',
                    borderWidth: 2,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Session Duration Trends - Line Chart
     */
    function initSessionDurationChart() {
        const ctx = document.getElementById('sessionDurationChart');
        if (!ctx) return;

        const sessionData = analyticsData.session_duration || {};
        const labels = Object.keys(sessionData);
        const data = Object.values(sessionData);

        // Format dates for display
        const formattedLabels = labels.map(date => {
            const d = new Date(date);
            return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        charts.sessionDuration = new Chart(ctx, {
            type: 'line',
            data: {
                labels: formattedLabels,
                datasets: [{
                    label: 'Avg Session Duration (minutes)',
                    data: data,
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: colors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.parsed.y.toFixed(1)} minutes`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: '#f1f1f1'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f1f1f1'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + ' min';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Opportunity Usage - Doughnut Chart
     */
    function initOpportunityUsageChart() {
        const ctx = document.getElementById('opportunityUsageChart');
        if (!ctx) return;

        const opportunityData = analyticsData.opportunity_usage || {};
        const withOpportunities = opportunityData.with_opportunities || 0;
        const withoutOpportunities = opportunityData.without_opportunities || 0;

        charts.opportunityUsage = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['With Opportunities', 'Without Opportunities'],
                datasets: [{
                    data: [withOpportunities, withoutOpportunities],
                    backgroundColor: [colors.success, colors.warning],
                    borderColor: '#ffffff',
                    borderWidth: 2,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Setup event handlers
     */
    function setupEventHandlers() {
        // Country filter for sectors chart
        $('#sectorCountryFilter').on('change', function() {
            const selectedCountry = $(this).val();
            updateTopSectorsChart(selectedCountry);
        });

        // Period filter for session duration
        $('#sessionPeriodFilter').on('change', function() {
            const selectedPeriod = $(this).val();
            updateSessionDurationChart(selectedPeriod);
        });

        // Refresh data button
        $('#refreshData').on('click', function() {
            refreshAnalyticsData();
        });

        // Export data button
        $('#exportData').on('click', function() {
            exportAnalyticsData();
        });

        // Keyword tag interactions
        $('.keyword-tag').on('click', function() {
            const keyword = $(this).text().split('(')[0].trim();
            searchKeyword(keyword);
        });
    }

    /**
     * Update top sectors chart based on country filter
     */
    function updateTopSectorsChart(country) {
        // This would typically make an AJAX call to get filtered data
        // For now, we'll just update the chart title
        console.log('Filtering sectors by country:', country);
    }

    /**
     * Update session duration chart based on period filter
     */
    function updateSessionDurationChart(period) {
        // This would typically make an AJAX call to get data for the selected period
        console.log('Updating session duration for period:', period);
    }

    /**
     * Refresh analytics data
     */
    function refreshAnalyticsData() {
        const $button = $('#refreshData');
        const originalText = $button.text();
        
        $button.text('Refreshing...').prop('disabled', true);
        
        $.ajax({
            url: chatgabiAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_get_engagement_analytics',
                nonce: chatgabiAnalytics.nonce,
                refresh: 'true'
            },
            success: function(response) {
                if (response.success) {
                    location.reload(); // Simple refresh for now
                } else {
                    alert('Error refreshing data');
                }
            },
            error: function() {
                alert('Error refreshing data');
            },
            complete: function() {
                $button.text(originalText).prop('disabled', false);
            }
        });
    }

    /**
     * Export analytics data as CSV
     */
    function exportAnalyticsData() {
        // Create CSV content
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Date,Country,Sector,Context Found,Opportunities,Tokens\n";
        
        analyticsData.recent_activity.forEach(function(row) {
            csvContent += `${row.timestamp},${row.country},${row.detected_sector || 'N/A'},${row.sector_context_found ? 'Yes' : 'No'},${row.opportunities_included},${row.prompt_tokens_estimated}\n`;
        });
        
        // Download CSV
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "chatgabi-analytics-" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Search for keyword in analytics
     */
    function searchKeyword(keyword) {
        console.log('Searching for keyword:', keyword);
        // This could open a modal or navigate to a detailed view
    }

    /**
     * Responsive chart handling
     */
    function handleResize() {
        Object.values(charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }

    // Initialize charts when page loads
    initializeCharts();
    
    // Handle window resize
    $(window).on('resize', handleResize);
    
    // Make functions available globally for debugging
    window.chatgabiAnalytics = {
        charts: charts,
        data: analyticsData,
        refresh: refreshAnalyticsData,
        export: exportAnalyticsData
    };
});

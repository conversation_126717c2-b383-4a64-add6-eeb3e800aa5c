<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Enhancement Features - Frontend Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: white;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .demo-area {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
        }
        #test-results {
            max-height: 400px;
            overflow-y: auto;
            background: white;
            padding: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>ChatGABI Enhancement Features - Frontend Test</h1>
    
    <div class="test-section">
        <h2>1. Service Worker & PWA Test</h2>
        <div id="pwa-test-results"></div>
        <button onclick="testServiceWorker()">Test Service Worker</button>
        <button onclick="testPWAInstallation()">Test PWA Installation</button>
        <button onclick="testOfflineCapabilities()">Test Offline Capabilities</button>
    </div>
    
    <div class="test-section">
        <h2>2. Local Storage Manager Test</h2>
        <div id="storage-test-results"></div>
        <button onclick="testLocalStorage()">Test Local Storage</button>
        <button onclick="testDataCaching()">Test Data Caching</button>
        <button onclick="testStorageCleanup()">Test Storage Cleanup</button>
    </div>
    
    <div class="test-section">
        <h2>3. Feedback Loops Test</h2>
        <div id="feedback-test-results"></div>
        <button onclick="testFeedbackCollection()">Test Feedback Collection</button>
        <button onclick="testInteractionTracking()">Test Interaction Tracking</button>
        <div class="demo-area">
            <h4>Demo Feedback Interface</h4>
            <div id="feedback-demo"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. Context Personalization Test</h2>
        <div id="personalization-test-results"></div>
        <button onclick="testPersonalization()">Test Personalization</button>
        <button onclick="testSessionContext()">Test Session Context</button>
        <button onclick="testRecommendations()">Test Recommendations</button>
    </div>
    
    <div class="test-section">
        <h2>5. Advanced Analytics Test</h2>
        <div id="analytics-test-results"></div>
        <button onclick="testAnalyticsDashboard()">Test Analytics Dashboard</button>
        <button onclick="testChartRendering()">Test Chart Rendering</button>
        <div class="demo-area">
            <h4>Demo Analytics Chart</h4>
            <canvas id="demo-chart" width="400" height="200"></canvas>
        </div>
    </div>
    
    <div class="test-section">
        <h2>6. Integration Test</h2>
        <div id="integration-test-results"></div>
        <button onclick="testFullIntegration()">Test Full Integration</button>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <!-- Include jQuery for compatibility -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include Chart.js for analytics testing -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Test results container
        const testResults = [];
        
        function logResult(test, status, message) {
            const result = { test, status, message, timestamp: new Date().toISOString() };
            testResults.push(result);
            
            const resultDiv = document.getElementById('test-results');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${status}`;
            resultElement.innerHTML = `<strong>[${test}]</strong> ${message}`;
            resultDiv.appendChild(resultElement);
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        // 1. Service Worker & PWA Tests
        function testServiceWorker() {
            logResult('PWA', 'info', 'Testing Service Worker registration...');
            
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/sw.js')
                    .then(registration => {
                        logResult('PWA', 'success', 'Service Worker registered successfully');
                        document.getElementById('pwa-test-results').innerHTML = 
                            '<span class="success">✓ Service Worker supported and registered</span>';
                    })
                    .catch(error => {
                        logResult('PWA', 'error', 'Service Worker registration failed: ' + error.message);
                        document.getElementById('pwa-test-results').innerHTML = 
                            '<span class="error">✗ Service Worker registration failed</span>';
                    });
            } else {
                logResult('PWA', 'error', 'Service Worker not supported');
                document.getElementById('pwa-test-results').innerHTML = 
                    '<span class="error">✗ Service Worker not supported</span>';
            }
        }
        
        function testPWAInstallation() {
            logResult('PWA', 'info', 'Testing PWA installation prompt...');
            
            // Check for manifest
            const manifestLink = document.querySelector('link[rel="manifest"]');
            if (manifestLink) {
                logResult('PWA', 'success', 'Manifest link found');
                
                // Test manifest fetch
                fetch(manifestLink.href)
                    .then(response => response.json())
                    .then(manifest => {
                        logResult('PWA', 'success', 'Manifest loaded successfully');
                        document.getElementById('pwa-test-results').innerHTML += 
                            '<br><span class="success">✓ PWA Manifest accessible</span>';
                    })
                    .catch(error => {
                        logResult('PWA', 'error', 'Manifest fetch failed: ' + error.message);
                    });
            } else {
                logResult('PWA', 'warning', 'Manifest link not found in current page');
                document.getElementById('pwa-test-results').innerHTML += 
                    '<br><span class="warning">⚠ Manifest link not found</span>';
            }
        }
        
        function testOfflineCapabilities() {
            logResult('PWA', 'info', 'Testing offline capabilities...');
            
            // Test localStorage
            try {
                localStorage.setItem('chatgabi_test', 'offline_test');
                const testValue = localStorage.getItem('chatgabi_test');
                if (testValue === 'offline_test') {
                    logResult('PWA', 'success', 'Local storage working');
                    localStorage.removeItem('chatgabi_test');
                }
            } catch (error) {
                logResult('PWA', 'error', 'Local storage failed: ' + error.message);
            }
            
            // Test IndexedDB
            if ('indexedDB' in window) {
                logResult('PWA', 'success', 'IndexedDB supported');
                document.getElementById('pwa-test-results').innerHTML += 
                    '<br><span class="success">✓ Offline storage capabilities available</span>';
            } else {
                logResult('PWA', 'error', 'IndexedDB not supported');
            }
        }
        
        // 2. Local Storage Manager Tests
        function testLocalStorage() {
            logResult('Storage', 'info', 'Testing local storage manager...');
            
            // Simulate ChatGABI LocalStorageManager
            const testData = {
                templates: [
                    { id: 1, title: 'Test Template', category: 'business_plan' },
                    { id: 2, title: 'Test Template 2', category: 'marketing' }
                ],
                timestamp: Date.now()
            };
            
            try {
                localStorage.setItem('chatgabi_test_templates', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('chatgabi_test_templates'));
                
                if (retrieved && retrieved.templates.length === 2) {
                    logResult('Storage', 'success', 'Template storage/retrieval working');
                    document.getElementById('storage-test-results').innerHTML = 
                        '<span class="success">✓ Local storage operations successful</span>';
                } else {
                    logResult('Storage', 'error', 'Template data mismatch');
                }
                
                localStorage.removeItem('chatgabi_test_templates');
            } catch (error) {
                logResult('Storage', 'error', 'Local storage test failed: ' + error.message);
                document.getElementById('storage-test-results').innerHTML = 
                    '<span class="error">✗ Local storage test failed</span>';
            }
        }
        
        function testDataCaching() {
            logResult('Storage', 'info', 'Testing data caching mechanisms...');
            
            // Test compression simulation
            const largeData = Array(1000).fill().map((_, i) => ({ id: i, data: 'test_data_' + i }));
            const compressed = JSON.stringify(largeData);
            
            try {
                localStorage.setItem('chatgabi_cache_test', compressed);
                const retrieved = JSON.parse(localStorage.getItem('chatgabi_cache_test'));
                
                if (retrieved.length === 1000) {
                    logResult('Storage', 'success', 'Large data caching successful');
                    document.getElementById('storage-test-results').innerHTML += 
                        '<br><span class="success">✓ Data caching mechanisms working</span>';
                }
                
                localStorage.removeItem('chatgabi_cache_test');
            } catch (error) {
                logResult('Storage', 'error', 'Data caching failed: ' + error.message);
            }
        }
        
        function testStorageCleanup() {
            logResult('Storage', 'info', 'Testing storage cleanup...');
            
            // Create expired data
            const expiredData = {
                data: 'test',
                expiration: Date.now() - 1000 // Expired 1 second ago
            };
            
            localStorage.setItem('chatgabi_expired_test', JSON.stringify(expiredData));
            
            // Simulate cleanup
            const stored = JSON.parse(localStorage.getItem('chatgabi_expired_test'));
            if (stored.expiration < Date.now()) {
                localStorage.removeItem('chatgabi_expired_test');
                logResult('Storage', 'success', 'Expired data cleanup working');
                document.getElementById('storage-test-results').innerHTML += 
                    '<br><span class="success">✓ Storage cleanup mechanisms working</span>';
            }
        }
        
        // 3. Feedback Loops Tests
        function testFeedbackCollection() {
            logResult('Feedback', 'info', 'Testing feedback collection interface...');
            
            // Create demo feedback interface
            const feedbackDemo = document.getElementById('feedback-demo');
            feedbackDemo.innerHTML = `
                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                    <h5>Rate this response:</h5>
                    <div class="star-rating">
                        <span class="star" data-rating="1">★</span>
                        <span class="star" data-rating="2">★</span>
                        <span class="star" data-rating="3">★</span>
                        <span class="star" data-rating="4">★</span>
                        <span class="star" data-rating="5">★</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <button onclick="submitTestFeedback()">Submit Feedback</button>
                    </div>
                </div>
            `;
            
            // Add star rating functionality
            document.querySelectorAll('.star').forEach(star => {
                star.style.cursor = 'pointer';
                star.style.fontSize = '24px';
                star.style.color = '#ddd';
                
                star.addEventListener('click', function() {
                    const rating = this.dataset.rating;
                    document.querySelectorAll('.star').forEach((s, index) => {
                        s.style.color = index < rating ? '#ffc107' : '#ddd';
                    });
                    logResult('Feedback', 'success', `Star rating selected: ${rating}/5`);
                });
            });
            
            logResult('Feedback', 'success', 'Feedback interface created successfully');
            document.getElementById('feedback-test-results').innerHTML = 
                '<span class="success">✓ Feedback collection interface working</span>';
        }
        
        function submitTestFeedback() {
            logResult('Feedback', 'info', 'Submitting test feedback...');
            
            // Simulate feedback submission
            const feedbackData = {
                rating: 4,
                category_helpfulness: 4,
                category_accuracy: 5,
                category_relevance: 4,
                feedback_text: 'Test feedback submission',
                timestamp: Date.now()
            };
            
            // Simulate AJAX call
            setTimeout(() => {
                logResult('Feedback', 'success', 'Test feedback submitted successfully');
                document.getElementById('feedback-test-results').innerHTML += 
                    '<br><span class="success">✓ Feedback submission working</span>';
            }, 500);
        }
        
        function testInteractionTracking() {
            logResult('Feedback', 'info', 'Testing interaction tracking...');
            
            // Simulate interaction tracking
            const interactions = [];
            
            // Track clicks
            document.addEventListener('click', function(e) {
                interactions.push({
                    type: 'click',
                    element: e.target.tagName,
                    timestamp: Date.now()
                });
            });
            
            // Simulate some interactions
            setTimeout(() => {
                if (interactions.length > 0) {
                    logResult('Feedback', 'success', `Tracked ${interactions.length} interactions`);
                    document.getElementById('feedback-test-results').innerHTML += 
                        '<br><span class="success">✓ Interaction tracking working</span>';
                }
            }, 1000);
        }
        
        // 4. Context Personalization Tests
        function testPersonalization() {
            logResult('Personalization', 'info', 'Testing personalization features...');
            
            // Simulate user context
            const userContext = {
                industry: 'technology',
                country: 'GH',
                business_stage: 'startup',
                preferences: {
                    communication_style: 'professional',
                    detail_level: 'moderate'
                }
            };
            
            localStorage.setItem('chatgabi_user_context', JSON.stringify(userContext));
            
            // Test context retrieval
            const retrieved = JSON.parse(localStorage.getItem('chatgabi_user_context'));
            if (retrieved && retrieved.industry === 'technology') {
                logResult('Personalization', 'success', 'User context storage working');
                document.getElementById('personalization-test-results').innerHTML = 
                    '<span class="success">✓ Personalization context working</span>';
            }
        }
        
        function testSessionContext() {
            logResult('Personalization', 'info', 'Testing session context...');
            
            // Simulate session context
            const sessionContext = {
                sessionId: 'test_session_' + Date.now(),
                conversationHistory: [
                    { type: 'user', message: 'Test message 1' },
                    { type: 'ai', message: 'Test response 1' }
                ],
                currentFocus: 'business_planning',
                contextScore: 0.8
            };
            
            sessionStorage.setItem('chatgabi_session_context', JSON.stringify(sessionContext));
            
            const retrieved = JSON.parse(sessionStorage.getItem('chatgabi_session_context'));
            if (retrieved && retrieved.conversationHistory.length === 2) {
                logResult('Personalization', 'success', 'Session context working');
                document.getElementById('personalization-test-results').innerHTML += 
                    '<br><span class="success">✓ Session context management working</span>';
            }
        }
        
        function testRecommendations() {
            logResult('Personalization', 'info', 'Testing personalized recommendations...');
            
            // Simulate recommendations
            const recommendations = [
                { title: 'Business Plan Template for Tech Startups', relevance: 95 },
                { title: 'Ghana Market Analysis Template', relevance: 90 },
                { title: 'Startup Funding Strategy', relevance: 85 }
            ];
            
            // Sort by relevance
            recommendations.sort((a, b) => b.relevance - a.relevance);
            
            if (recommendations[0].relevance === 95) {
                logResult('Personalization', 'success', 'Recommendation sorting working');
                document.getElementById('personalization-test-results').innerHTML += 
                    '<br><span class="success">✓ Personalized recommendations working</span>';
            }
        }
        
        // 5. Advanced Analytics Tests
        function testAnalyticsDashboard() {
            logResult('Analytics', 'info', 'Testing analytics dashboard...');
            
            // Simulate analytics data
            const analyticsData = {
                summary: {
                    ai_interactions: { total: 150, average: 5.2 },
                    credits_used: { total: 1200, average: 42 },
                    templates_used: { total: 25, average: 0.9 }
                },
                trends: {
                    ai_interactions: [
                        { date: '2024-01-01', value: 5 },
                        { date: '2024-01-02', value: 8 },
                        { date: '2024-01-03', value: 12 }
                    ]
                }
            };
            
            if (analyticsData.summary.ai_interactions.total > 0) {
                logResult('Analytics', 'success', 'Analytics data structure valid');
                document.getElementById('analytics-test-results').innerHTML = 
                    '<span class="success">✓ Analytics dashboard data working</span>';
            }
        }
        
        function testChartRendering() {
            logResult('Analytics', 'info', 'Testing chart rendering...');
            
            const canvas = document.getElementById('demo-chart');
            const ctx = canvas.getContext('2d');
            
            try {
                const chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5'],
                        datasets: [{
                            label: 'AI Interactions',
                            data: [5, 8, 12, 15, 18],
                            borderColor: '#007cba',
                            backgroundColor: 'rgba(0, 124, 186, 0.1)',
                            borderWidth: 2,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Demo Analytics Chart'
                            }
                        }
                    }
                });
                
                logResult('Analytics', 'success', 'Chart rendering successful');
                document.getElementById('analytics-test-results').innerHTML += 
                    '<br><span class="success">✓ Chart rendering working</span>';
            } catch (error) {
                logResult('Analytics', 'error', 'Chart rendering failed: ' + error.message);
                document.getElementById('analytics-test-results').innerHTML += 
                    '<br><span class="error">✗ Chart rendering failed</span>';
            }
        }
        
        // 6. Integration Tests
        function testFullIntegration() {
            logResult('Integration', 'info', 'Testing full integration...');
            
            // Test all systems working together
            const integrationTests = [
                () => testServiceWorker(),
                () => testLocalStorage(),
                () => testFeedbackCollection(),
                () => testPersonalization(),
                () => testAnalyticsDashboard()
            ];
            
            let completedTests = 0;
            integrationTests.forEach((test, index) => {
                setTimeout(() => {
                    test();
                    completedTests++;
                    if (completedTests === integrationTests.length) {
                        logResult('Integration', 'success', 'Full integration test completed');
                        document.getElementById('integration-test-results').innerHTML = 
                            '<span class="success">✓ Full integration test completed</span>';
                    }
                }, index * 200);
            });
        }
        
        function runAllTests() {
            logResult('System', 'info', 'Running all enhancement feature tests...');
            
            // Clear previous results
            document.getElementById('test-results').innerHTML = '';
            
            // Run all tests
            setTimeout(() => testServiceWorker(), 100);
            setTimeout(() => testPWAInstallation(), 200);
            setTimeout(() => testOfflineCapabilities(), 300);
            setTimeout(() => testLocalStorage(), 400);
            setTimeout(() => testDataCaching(), 500);
            setTimeout(() => testStorageCleanup(), 600);
            setTimeout(() => testFeedbackCollection(), 700);
            setTimeout(() => testInteractionTracking(), 800);
            setTimeout(() => testPersonalization(), 900);
            setTimeout(() => testSessionContext(), 1000);
            setTimeout(() => testRecommendations(), 1100);
            setTimeout(() => testAnalyticsDashboard(), 1200);
            setTimeout(() => testChartRendering(), 1300);
            
            setTimeout(() => {
                logResult('System', 'success', 'All enhancement feature tests completed');
                
                // Generate summary
                const successCount = testResults.filter(r => r.status === 'success').length;
                const errorCount = testResults.filter(r => r.status === 'error').length;
                const warningCount = testResults.filter(r => r.status === 'warning').length;
                
                document.getElementById('integration-test-results').innerHTML = `
                    <h4>Test Summary:</h4>
                    <span class="success">✓ Successful: ${successCount}</span><br>
                    <span class="error">✗ Errors: ${errorCount}</span><br>
                    <span class="warning">⚠ Warnings: ${warningCount}</span><br>
                    <span class="info">Total Tests: ${testResults.length}</span>
                `;
            }, 1500);
        }
        
        // Initialize basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            logResult('System', 'info', 'ChatGABI Enhancement Features Frontend Test initialized');
            
            // Test basic browser capabilities
            if (typeof jQuery !== 'undefined') {
                logResult('System', 'success', 'jQuery loaded successfully');
            } else {
                logResult('System', 'error', 'jQuery not loaded');
            }
            
            if (typeof Chart !== 'undefined') {
                logResult('System', 'success', 'Chart.js loaded successfully');
            } else {
                logResult('System', 'error', 'Chart.js not loaded');
            }
        });
    </script>
</body>
</html>

<?php
/**
 * WhatsApp Admin Interface for ChatGABI AI
 * 
 * Provides admin dashboard for WhatsApp Business API management
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize WhatsApp admin interface
 */
function chatgabi_init_whatsapp_admin() {
    add_action('admin_init', 'chatgabi_register_whatsapp_settings');
}
add_action('init', 'chatgabi_init_whatsapp_admin');

// Note: WhatsApp admin menu is now registered in inc/admin-dashboard.php
// as part of the main ChatGABI menu structure for consistency

/**
 * Register WhatsApp settings
 */
function chatgabi_register_whatsapp_settings() {
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_whatsapp_access_token');
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_whatsapp_phone_number_id');
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_whatsapp_verify_token');
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_whatsapp_app_id');
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_whatsapp_app_secret');
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_google_translate_api_key');
    register_setting('chatgabi_whatsapp_settings', 'chatgabi_translation_enabled');
}

/**
 * WhatsApp admin page
 */
function chatgabi_whatsapp_admin_page() {
    // Handle form submissions
    if (isset($_POST['test_webhook'])) {
        $test_result = chatgabi_test_whatsapp_webhook();
    }
    
    if (isset($_POST['send_test_message'])) {
        $test_phone = sanitize_text_field($_POST['test_phone']);
        $test_message = sanitize_textarea_field($_POST['test_message']);
        $send_result = chatgabi_send_whatsapp_response($test_phone, $test_message);
    }
    
    // Get analytics data
    $analytics = chatgabi_get_whatsapp_admin_analytics();
    $translation_analytics = function_exists('chatgabi_get_translation_analytics') ? chatgabi_get_translation_analytics() : null;
    ?>
    
    <div class="wrap">
        <h1><?php _e('ChatGABI WhatsApp Integration', 'chatgabi'); ?></h1>
        
        <div class="chatgabi-admin-container">
            <!-- Configuration Tab -->
            <div class="chatgabi-admin-section">
                <h2><?php _e('WhatsApp Business API Configuration', 'chatgabi'); ?></h2>
                
                <form method="post" action="options.php">
                    <?php settings_fields('chatgabi_whatsapp_settings'); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Access Token', 'chatgabi'); ?></th>
                            <td>
                                <input type="password" name="chatgabi_whatsapp_access_token" 
                                       value="<?php echo esc_attr(get_option('chatgabi_whatsapp_access_token')); ?>" 
                                       class="regular-text" />
                                <p class="description"><?php _e('Your WhatsApp Business API access token', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Phone Number ID', 'chatgabi'); ?></th>
                            <td>
                                <input type="text" name="chatgabi_whatsapp_phone_number_id" 
                                       value="<?php echo esc_attr(get_option('chatgabi_whatsapp_phone_number_id')); ?>" 
                                       class="regular-text" />
                                <p class="description"><?php _e('Your WhatsApp Business phone number ID', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Verify Token', 'chatgabi'); ?></th>
                            <td>
                                <input type="text" name="chatgabi_whatsapp_verify_token" 
                                       value="<?php echo esc_attr(get_option('chatgabi_whatsapp_verify_token', 'chatgabi_verify_token_2024')); ?>" 
                                       class="regular-text" />
                                <p class="description"><?php _e('Token for webhook verification', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('App ID', 'chatgabi'); ?></th>
                            <td>
                                <input type="text" name="chatgabi_whatsapp_app_id" 
                                       value="<?php echo esc_attr(get_option('chatgabi_whatsapp_app_id')); ?>" 
                                       class="regular-text" />
                                <p class="description"><?php _e('Your Facebook App ID', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('App Secret', 'chatgabi'); ?></th>
                            <td>
                                <input type="password" name="chatgabi_whatsapp_app_secret"
                                       value="<?php echo esc_attr(get_option('chatgabi_whatsapp_app_secret')); ?>"
                                       class="regular-text" />
                                <p class="description"><?php _e('Your Facebook App Secret', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                    </table>

                    <h3><?php _e('Translation Settings', 'chatgabi'); ?></h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Translation', 'chatgabi'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="chatgabi_translation_enabled" value="1"
                                           <?php checked(get_option('chatgabi_translation_enabled', 1)); ?> />
                                    <?php _e('Enable real-time translation for African languages', 'chatgabi'); ?>
                                </label>
                                <p class="description"><?php _e('Automatically translate Twi, Yoruba, Swahili, and Zulu messages to English', 'chatgabi'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Google Translate API Key', 'chatgabi'); ?></th>
                            <td>
                                <input type="password" name="chatgabi_google_translate_api_key"
                                       value="<?php echo esc_attr(get_option('chatgabi_google_translate_api_key')); ?>"
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Your Google Cloud Translation API key. Required for real-time translation.', 'chatgabi'); ?>
                                    <br>
                                    <a href="https://cloud.google.com/translate/docs/setup" target="_blank"><?php _e('Get API Key', 'chatgabi'); ?></a>
                                </p>
                            </td>
                        </tr>
                    </table>

                    <?php submit_button(__('Save Configuration', 'chatgabi')); ?>
                </form>
                
                <!-- Webhook URL Info -->
                <div class="chatgabi-webhook-info">
                    <h3><?php _e('Webhook Configuration', 'chatgabi'); ?></h3>
                    <p><?php _e('Configure your WhatsApp webhook with the following URL:', 'chatgabi'); ?></p>
                    <code><?php echo home_url('/wp-json/chatgabi/v1/whatsapp/webhook'); ?></code>
                    
                    <form method="post" style="margin-top: 15px;">
                        <input type="hidden" name="test_webhook" value="1" />
                        <?php submit_button(__('Test Webhook', 'chatgabi'), 'secondary', 'test_webhook', false); ?>
                    </form>
                    
                    <?php if (isset($test_result)): ?>
                        <div class="notice notice-<?php echo $test_result ? 'success' : 'error'; ?>">
                            <p><?php echo $test_result ? __('Webhook test successful!', 'chatgabi') : __('Webhook test failed!', 'chatgabi'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Analytics Section -->
            <div class="chatgabi-admin-section">
                <h2><?php _e('WhatsApp Analytics', 'chatgabi'); ?></h2>
                
                <div class="chatgabi-stats-grid">
                    <div class="chatgabi-stat-card">
                        <h3><?php _e('Total Users', 'chatgabi'); ?></h3>
                        <div class="stat-value"><?php echo number_format($analytics['total_users']); ?></div>
                    </div>
                    
                    <div class="chatgabi-stat-card">
                        <h3><?php _e('Active Users (7d)', 'chatgabi'); ?></h3>
                        <div class="stat-value"><?php echo number_format($analytics['active_users']); ?></div>
                    </div>
                    
                    <div class="chatgabi-stat-card">
                        <h3><?php _e('Total Messages', 'chatgabi'); ?></h3>
                        <div class="stat-value"><?php echo number_format($analytics['total_messages']); ?></div>
                    </div>
                    
                    <div class="chatgabi-stat-card">
                        <h3><?php _e('Credits Used', 'chatgabi'); ?></h3>
                        <div class="stat-value"><?php echo number_format($analytics['credits_used'], 1); ?></div>
                    </div>
                </div>
                
                <!-- Country Breakdown -->
                <div class="chatgabi-country-breakdown">
                    <h3><?php _e('Users by Country', 'chatgabi'); ?></h3>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Country', 'chatgabi'); ?></th>
                                <th><?php _e('Users', 'chatgabi'); ?></th>
                                <th><?php _e('Percentage', 'chatgabi'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($analytics['country_breakdown'] as $country): ?>
                                <tr>
                                    <td><?php echo esc_html($country['country']); ?></td>
                                    <td><?php echo number_format($country['user_count']); ?></td>
                                    <td><?php echo number_format(($country['user_count'] / max(1, $analytics['total_users'])) * 100, 1); ?>%</td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Translation Analytics -->
                <?php if ($translation_analytics): ?>
                <div class="chatgabi-translation-analytics">
                    <h3><?php _e('Translation Analytics (Last 30 Days)', 'chatgabi'); ?></h3>

                    <div class="chatgabi-stats-grid">
                        <div class="chatgabi-stat-card">
                            <h4><?php _e('Total Translations', 'chatgabi'); ?></h4>
                            <div class="stat-value"><?php echo number_format($translation_analytics['total_translations']); ?></div>
                        </div>

                        <div class="chatgabi-stat-card">
                            <h4><?php _e('Cache Hit Rate', 'chatgabi'); ?></h4>
                            <div class="stat-value"><?php echo $translation_analytics['cache_hit_rate']; ?>%</div>
                        </div>

                        <div class="chatgabi-stat-card">
                            <h4><?php _e('Translation Cost', 'chatgabi'); ?></h4>
                            <div class="stat-value">$<?php echo number_format($translation_analytics['total_cost'], 4); ?></div>
                        </div>

                        <div class="chatgabi-stat-card">
                            <h4><?php _e('Avg Processing Time', 'chatgabi'); ?></h4>
                            <div class="stat-value"><?php echo number_format($translation_analytics['avg_processing_time'], 1); ?>ms</div>
                        </div>
                    </div>

                    <!-- Language Translation Breakdown -->
                    <?php if (!empty($translation_analytics['language_breakdown'])): ?>
                    <div class="chatgabi-language-translation-breakdown">
                        <h4><?php _e('Translations by Language', 'chatgabi'); ?></h4>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Language', 'chatgabi'); ?></th>
                                    <th><?php _e('Translations', 'chatgabi'); ?></th>
                                    <th><?php _e('Avg Confidence', 'chatgabi'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($translation_analytics['language_breakdown'] as $lang_stat): ?>
                                    <?php
                                    $lang_names = array(
                                        'tw' => 'Twi (Ghana)',
                                        'sw' => 'Swahili (Kenya)',
                                        'yo' => 'Yoruba (Nigeria)',
                                        'zu' => 'Zulu (South Africa)',
                                        'en' => 'English'
                                    );
                                    $lang_name = $lang_names[$lang_stat['source_language']] ?? $lang_stat['source_language'];
                                    ?>
                                    <tr>
                                        <td><?php echo esc_html($lang_name); ?></td>
                                        <td><?php echo number_format($lang_stat['translation_count']); ?></td>
                                        <td><?php echo number_format($lang_stat['avg_confidence'] * 100, 1); ?>%</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Test Message Section -->
            <div class="chatgabi-admin-section">
                <h2><?php _e('Send Test Message', 'chatgabi'); ?></h2>
                
                <form method="post">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Phone Number', 'chatgabi'); ?></th>
                            <td>
                                <input type="text" name="test_phone" placeholder="+233123456789" class="regular-text" required />
                                <p class="description"><?php _e('Include country code (e.g., +233123456789)', 'chatgabi'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Message', 'chatgabi'); ?></th>
                            <td>
                                <textarea name="test_message" rows="4" class="large-text" required 
                                          placeholder="<?php _e('Enter your test message...', 'chatgabi'); ?>"></textarea>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button(__('Send Test Message', 'chatgabi'), 'secondary', 'send_test_message'); ?>
                </form>
                
                <?php if (isset($send_result)): ?>
                    <div class="notice notice-<?php echo $send_result ? 'success' : 'error'; ?>">
                        <p><?php echo $send_result ? __('Test message sent successfully!', 'chatgabi') : __('Failed to send test message!', 'chatgabi'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <style>
    .chatgabi-admin-container {
        max-width: 1200px;
    }
    
    .chatgabi-admin-section {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        margin: 20px 0;
        padding: 20px;
    }
    
    .chatgabi-admin-section h2 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .chatgabi-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .chatgabi-stat-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
    }
    
    .chatgabi-stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .stat-value {
        font-size: 2em;
        font-weight: bold;
        color: #0073aa;
    }
    
    .chatgabi-webhook-info {
        background: #f0f8ff;
        border: 1px solid #0073aa;
        border-radius: 4px;
        padding: 15px;
        margin-top: 20px;
    }
    
    .chatgabi-webhook-info code {
        background: #fff;
        padding: 8px 12px;
        border-radius: 4px;
        display: block;
        margin: 10px 0;
        font-family: monospace;
        word-break: break-all;
    }
    
    .chatgabi-country-breakdown {
        margin-top: 30px;
    }
    
    .chatgabi-country-breakdown table {
        margin-top: 15px;
    }
    </style>
    <?php
}

/**
 * Get WhatsApp analytics for admin dashboard
 */
function chatgabi_get_whatsapp_admin_analytics() {
    global $wpdb;
    
    $users_table = $wpdb->prefix . 'chatgabi_whatsapp_users';
    $conversations_table = $wpdb->prefix . 'chatgabi_whatsapp_conversations';
    
    // Check if tables exist
    $users_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$users_table'") === $users_table;
    $conversations_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$conversations_table'") === $conversations_table;
    
    if (!$users_table_exists || !$conversations_table_exists) {
        return array(
            'total_users' => 0,
            'active_users' => 0,
            'total_messages' => 0,
            'credits_used' => 0,
            'country_breakdown' => array()
        );
    }
    
    $total_users = $wpdb->get_var("SELECT COUNT(*) FROM $users_table") ?: 0;
    $active_users = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE last_message_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)") ?: 0;
    $total_messages = $wpdb->get_var("SELECT COUNT(*) FROM $conversations_table") ?: 0;
    $credits_used = $wpdb->get_var("SELECT SUM(credits_used) FROM $conversations_table") ?: 0;
    
    $country_breakdown = $wpdb->get_results(
        "SELECT country, COUNT(*) as user_count FROM $users_table GROUP BY country ORDER BY user_count DESC",
        ARRAY_A
    ) ?: array();
    
    return array(
        'total_users' => (int) $total_users,
        'active_users' => (int) $active_users,
        'total_messages' => (int) $total_messages,
        'credits_used' => (float) $credits_used,
        'country_breakdown' => $country_breakdown
    );
}

/**
 * Test WhatsApp webhook
 */
function chatgabi_test_whatsapp_webhook() {
    $webhook_url = home_url('/wp-json/chatgabi/v1/whatsapp/webhook');
    $verify_token = get_option('chatgabi_whatsapp_verify_token', 'chatgabi_verify_token_2024');
    
    $test_url = $webhook_url . '?hub.mode=subscribe&hub.verify_token=' . urlencode($verify_token) . '&hub.challenge=test_challenge';
    
    $response = wp_remote_get($test_url, array('timeout' => 10));
    
    if (is_wp_error($response)) {
        return false;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    return $response_code === 200 && $response_body === 'test_challenge';
}

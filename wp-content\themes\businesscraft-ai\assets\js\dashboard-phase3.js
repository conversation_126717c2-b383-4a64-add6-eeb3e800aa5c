/**
 * Dashboard Phase 3 Features JavaScript
 *
 * Handles analytics dashboard, notifications, and template AI enhancement
 * Now integrated with component-based architecture and WordPress hooks
 */

// Global variables for charts (legacy support)
let usageChart = null;
let featuresChart = null;

// Component instances
let analyticsComponent = null;
let notificationComponent = null;
let templateComponent = null;

// Initialize Phase 3 features when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize component-based architecture if available
    if (window.BusinessCraftAI && window.BusinessCraftAI.componentManager) {
        initializeComponentBasedArchitecture();
    } else {
        // Fallback to legacy initialization
        initializeLegacyFeatures();
    }
});

/**
 * Initialize component-based architecture
 */
function initializeComponentBasedArchitecture() {
    const manager = window.BusinessCraftAI.componentManager;

    // Set up WordPress hooks integration
    setupWordPressHooksIntegration();

    // Initialize analytics dashboard component
    const analyticsContainer = document.querySelector('.analytics-container, [data-component="analytics-dashboard"]');
    if (analyticsContainer) {
        analyticsComponent = manager.createComponent('analytics-dashboard-main', 'analytics-dashboard', analyticsContainer, {
            autoRefresh: true,
            enableCharts: true,
            enableInsights: true,
            debug: false
        });
    }

    // Initialize notification center component
    const notificationContainer = document.querySelector('.notifications-container, [data-component="notification-center"]');
    if (notificationContainer) {
        notificationComponent = manager.createComponent('notification-center-main', 'notification-center', notificationContainer, {
            autoLoad: true,
            enablePreferences: true,
            enableHistory: true,
            debug: false
        });
    }

    // Initialize template enhancer component
    const templateContainer = document.querySelector('.templates-container, [data-component="template-enhancer"]');
    if (templateContainer) {
        templateComponent = manager.createComponent('template-enhancer-main', 'template-enhancer', templateContainer, {
            enableAISuggestions: true,
            enableEnhancement: true,
            debug: false
        });
    }

    // Set up global event listeners
    setupGlobalEventListeners();

    // Emit initialization complete event
    if (typeof wp !== 'undefined' && wp.hooks) {
        wp.hooks.doAction('bcai.dashboard.phase3.initialized', {
            analytics: analyticsComponent,
            notifications: notificationComponent,
            templates: templateComponent
        });
    }
}

/**
 * Set up WordPress hooks integration
 */
function setupWordPressHooksIntegration() {
    if (typeof wp === 'undefined' || !wp.hooks) {
        console.warn('WordPress hooks not available');
        return;
    }

    // Register dashboard hooks
    wp.hooks.addAction('bcai.analytics.data-loaded', 'bcai-dashboard', handleAnalyticsDataLoaded);
    wp.hooks.addAction('bcai.notifications.preferences-saved', 'bcai-dashboard', handleNotificationPreferencesSaved);
    wp.hooks.addAction('bcai.templates.enhanced', 'bcai-dashboard', handleTemplateEnhanced);

    // Register filters for customization
    wp.hooks.addFilter('bcai.analytics.chart-options', 'bcai-dashboard', customizeChartOptions);
    wp.hooks.addFilter('bcai.notifications.message-template', 'bcai-dashboard', customizeNotificationTemplate);
    wp.hooks.addFilter('bcai.templates.enhancement-prompt', 'bcai-dashboard', customizeEnhancementPrompt);

    // Register global dashboard hooks
    wp.hooks.addAction('bcai.dashboard.tab-changed', 'bcai-dashboard', handleTabChanged);
    wp.hooks.addAction('bcai.dashboard.refresh-requested', 'bcai-dashboard', handleRefreshRequested);
    wp.hooks.addAction('bcai.dashboard.export-requested', 'bcai-dashboard', handleExportRequested);
}

/**
 * WordPress hook handlers
 */
function handleAnalyticsDataLoaded(data) {
    console.log('Analytics data loaded via WordPress hook:', data);

    // Trigger any dependent updates
    if (notificationComponent) {
        notificationComponent.emit('analytics:data-updated', data);
    }

    // Update global state
    window.BusinessCraftAI.globalState = window.BusinessCraftAI.globalState || {};
    window.BusinessCraftAI.globalState.analyticsData = data;
}

function handleNotificationPreferencesSaved(preferences) {
    console.log('Notification preferences saved via WordPress hook:', preferences);

    // Show global success message
    if (window.BusinessCraftAI.componentManager) {
        window.BusinessCraftAI.componentManager.announceGlobally('Notification preferences updated successfully');
    }
}

function handleTemplateEnhanced(template) {
    console.log('Template enhanced via WordPress hook:', template);

    // Refresh analytics if needed
    if (analyticsComponent) {
        analyticsComponent.emit('template:enhanced', template);
    }
}

/**
 * Legacy initialization for backward compatibility
 */
function initializeLegacyFeatures() {
    console.warn('Component-based architecture not available, falling back to legacy initialization');

    initializeAnalyticsDashboard();
    initializeNotificationPreferences();
    initializeTemplateEnhancements();
}

/**
 * Initialize Analytics Dashboard
 */
function initializeAnalyticsDashboard() {
    // Load analytics data when analytics tab is shown
    const analyticsTab = document.querySelector('[data-tab="analytics"]');
    if (analyticsTab) {
        analyticsTab.addEventListener('click', function() {
            loadAnalyticsData();
        });
    }
    
    // Period selector for usage chart
    const periodSelector = document.getElementById('usage-chart-period');
    if (periodSelector) {
        periodSelector.addEventListener('change', function() {
            loadAnalyticsData(this.value);
        });
    }
}

/**
 * Load analytics data
 */
function loadAnalyticsData(period = 30) {
    if (!chatgabi_ajax || !chatgabi_ajax.ajax_url) {
        console.error('AJAX configuration not available');
        return;
    }
    
    // Show loading state
    showAnalyticsLoading(true);
    
    fetch(chatgabi_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'businesscraft_ai_get_user_analytics',
            nonce: chatgabi_ajax.nonce,
            period: period
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAnalyticsDashboard(data.data);
        } else {
            console.error('Failed to load analytics:', data.data);
            showAnalyticsError('Failed to load analytics data');
        }
    })
    .catch(error => {
        console.error('Analytics error:', error);
        showAnalyticsError('Network error loading analytics');
    })
    .finally(() => {
        showAnalyticsLoading(false);
    });
}

/**
 * Update analytics dashboard with data
 */
function updateAnalyticsDashboard(analytics) {
    // Update summary cards
    updateAnalyticsSummary(analytics.summary);
    
    // Update charts
    updateUsageChart(analytics.charts.usage_over_time);
    updateFeaturesChart(analytics.charts.feature_distribution);
    
    // Update insights
    updatePerformanceInsights(analytics.insights);
    
    // Update activity timeline
    updateActivityTimeline(analytics.activity);
}

/**
 * Update analytics summary cards
 */
function updateAnalyticsSummary(summary) {
    const elements = {
        'total-conversations-analytics': summary.conversations?.current || 0,
        'credits-used-analytics': summary.credits_used?.current || 0,
        'templates-generated-analytics': summary.templates?.current || 0,
        'opportunities-viewed-analytics': summary.opportunities?.current || 0
    };
    
    const trends = {
        'conversations-trend': summary.conversations?.trend || 0,
        'credits-trend': summary.credits_used?.trend || 0,
        'templates-trend': summary.templates?.trend || 0,
        'opportunities-trend': summary.opportunities?.trend || 0
    };
    
    // Update values
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value.toLocaleString();
        }
    });
    
    // Update trends
    Object.entries(trends).forEach(([id, trend]) => {
        const element = document.getElementById(id);
        if (element) {
            const isPositive = trend >= 0;
            element.textContent = `${isPositive ? '+' : ''}${trend}%`;
            element.className = `card-trend ${isPositive ? 'positive' : 'negative'}`;
        }
    });
}

/**
 * Update usage chart
 */
function updateUsageChart(chartData) {
    const canvas = document.getElementById('usage-chart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart
    if (usageChart) {
        usageChart.destroy();
    }
    
    usageChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Daily Usage',
                data: chartData.data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

/**
 * Update features chart
 */
function updateFeaturesChart(chartData) {
    const canvas = document.getElementById('features-chart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart
    if (featuresChart) {
        featuresChart.destroy();
    }
    
    const colors = [
        '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe',
        '#43e97b', '#fa709a', '#fee140', '#a8edea', '#d299c2'
    ];
    
    featuresChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: colors.slice(0, chartData.labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

/**
 * Update performance insights
 */
function updatePerformanceInsights(insights) {
    const elements = {
        'most-productive-day': insights.most_productive_day || 'Monday',
        'favorite-feature': insights.favorite_feature || 'General Consultation',
        'monthly-growth': insights.monthly_growth || '0%',
        'efficiency-score': insights.efficiency_score || '0%'
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

/**
 * Update activity timeline
 */
function updateActivityTimeline(activities) {
    const container = document.getElementById('activity-timeline-content');
    if (!container) return;
    
    if (!activities || activities.length === 0) {
        container.innerHTML = '<p class="no-activity">No recent activity found.</p>';
        return;
    }
    
    const timelineHTML = activities.map(activity => `
        <div class="timeline-item">
            <div class="timeline-icon">${activity.icon}</div>
            <div class="timeline-content">
                <div class="timeline-description">${activity.description}</div>
                <div class="timeline-time">${activity.time_ago}</div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = timelineHTML;
}

/**
 * Show/hide analytics loading state
 */
function showAnalyticsLoading(show) {
    const loadingElements = document.querySelectorAll('.loading-activity, .loading-spinner');
    loadingElements.forEach(element => {
        element.style.display = show ? 'block' : 'none';
    });
}

/**
 * Show analytics error
 */
function showAnalyticsError(message) {
    const container = document.getElementById('activity-timeline-content');
    if (container) {
        container.innerHTML = `<p class="error-message">⚠️ ${message}</p>`;
    }
}

/**
 * Initialize Notification Preferences
 */
function initializeNotificationPreferences() {
    // Load notification preferences when notifications tab is shown
    const notificationsTab = document.querySelector('[data-tab="notifications"]');
    if (notificationsTab) {
        notificationsTab.addEventListener('click', function() {
            loadNotificationPreferences();
            loadUserNotifications();
        });
    }
}

/**
 * Load notification preferences
 */
function loadNotificationPreferences() {
    fetch(chatgabi_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'businesscraft_ai_get_notification_preferences',
            nonce: chatgabi_ajax.nonce
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateNotificationPreferences(data.data);
        } else {
            console.error('Failed to load notification preferences:', data.data);
        }
    })
    .catch(error => {
        console.error('Notification preferences error:', error);
    });
}

/**
 * Populate notification preferences form
 */
function populateNotificationPreferences(preferences) {
    // Email notifications
    document.getElementById('email-opportunities').checked = preferences.opportunities?.email?.enabled || false;
    document.getElementById('email-credits').checked = preferences.credits?.email?.enabled || false;
    document.getElementById('email-templates').checked = preferences.templates?.email?.enabled || false;
    document.getElementById('email-weekly').checked = preferences.weekly_summary?.email?.enabled || false;
    
    // Push notifications
    document.getElementById('push-urgent').checked = preferences.urgent_alerts?.push?.enabled || false;
    document.getElementById('push-opportunities').checked = preferences.opportunities?.push?.enabled || false;
    
    // Frequency settings
    document.getElementById('opportunity-frequency').value = preferences.opportunities?.email?.frequency || 'daily';
    document.getElementById('credit-threshold').value = preferences.credits?.email?.settings?.threshold || 50;
}

/**
 * Save notification preferences
 */
function saveNotificationPreferences() {
    const preferences = {
        opportunities: {
            email: {
                enabled: document.getElementById('email-opportunities').checked,
                frequency: document.getElementById('opportunity-frequency').value
            },
            push: {
                enabled: document.getElementById('push-opportunities').checked,
                frequency: 'immediate'
            }
        },
        credits: {
            email: {
                enabled: document.getElementById('email-credits').checked,
                frequency: 'immediate',
                settings: {
                    threshold: parseInt(document.getElementById('credit-threshold').value)
                }
            },
            push: {
                enabled: document.getElementById('email-credits').checked,
                frequency: 'immediate'
            }
        },
        templates: {
            email: {
                enabled: document.getElementById('email-templates').checked,
                frequency: 'weekly'
            },
            push: {
                enabled: false,
                frequency: 'immediate'
            }
        },
        weekly_summary: {
            email: {
                enabled: document.getElementById('email-weekly').checked,
                frequency: 'weekly'
            },
            push: {
                enabled: false,
                frequency: 'weekly'
            }
        },
        urgent_alerts: {
            email: {
                enabled: true,
                frequency: 'immediate'
            },
            push: {
                enabled: document.getElementById('push-urgent').checked,
                frequency: 'immediate'
            }
        }
    };
    
    fetch(chatgabi_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'businesscraft_ai_save_notification_preferences',
            nonce: chatgabi_ajax.nonce,
            preferences: JSON.stringify(preferences)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotificationMessage('Preferences saved successfully!', 'success');
        } else {
            showNotificationMessage('Failed to save preferences: ' + data.data, 'error');
        }
    })
    .catch(error => {
        console.error('Save preferences error:', error);
        showNotificationMessage('Network error saving preferences', 'error');
    });
}

/**
 * Send test notification
 */
function testNotifications() {
    fetch(chatgabi_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'businesscraft_ai_send_test_notification',
            nonce: chatgabi_ajax.nonce
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotificationMessage('Test notification sent! Check your email.', 'success');
        } else {
            showNotificationMessage('Failed to send test notification: ' + data.data, 'error');
        }
    })
    .catch(error => {
        console.error('Test notification error:', error);
        showNotificationMessage('Network error sending test notification', 'error');
    });
}

/**
 * Load user notifications
 */
function loadUserNotifications() {
    const container = document.getElementById('notifications-list');
    if (!container) return;
    
    fetch(chatgabi_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'businesscraft_ai_get_user_notifications',
            nonce: chatgabi_ajax.nonce,
            limit: 20
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayUserNotifications(data.data);
        } else {
            container.innerHTML = '<p class="error-message">Failed to load notifications</p>';
        }
    })
    .catch(error => {
        console.error('Load notifications error:', error);
        container.innerHTML = '<p class="error-message">Network error loading notifications</p>';
    });
}

/**
 * Display user notifications
 */
function displayUserNotifications(notifications) {
    const container = document.getElementById('notifications-list');
    if (!container) return;
    
    if (!notifications || notifications.length === 0) {
        container.innerHTML = '<p class="no-notifications">No notifications yet.</p>';
        return;
    }
    
    const notificationsHTML = notifications.map(notification => `
        <div class="notification-item ${notification.status}">
            <div class="notification-icon">${notification.icon}</div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${notification.time_ago}</div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = notificationsHTML;
}

/**
 * Show notification message
 */
function showNotificationMessage(message, type = 'info') {
    // Create or update notification message element
    let messageEl = document.getElementById('notification-message');
    if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.id = 'notification-message';
        messageEl.className = 'notification-message';
        document.querySelector('.notification-actions').appendChild(messageEl);
    }
    
    messageEl.textContent = message;
    messageEl.className = `notification-message ${type}`;
    messageEl.style.display = 'block';
    
    // Hide after 3 seconds
    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}

/**
 * Initialize Template Enhancements
 */
function initializeTemplateEnhancements() {
    // Load AI suggestions when templates tab is shown
    const templatesTab = document.querySelector('[data-tab="templates"]');
    if (templatesTab) {
        templatesTab.addEventListener('click', function() {
            const aiSuggestionsBtn = document.querySelector('[data-view="ai-suggestions"]');
            if (aiSuggestionsBtn) {
                aiSuggestionsBtn.addEventListener('click', function() {
                    loadAITemplateSuggestions();
                });
            }
        });
    }
}

/**
 * Load AI template suggestions
 */
function loadAITemplateSuggestions() {
    fetch(chatgabi_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'businesscraft_ai_get_template_suggestions',
            nonce: chatgabi_ajax.nonce
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAITemplateSuggestions(data.data);
        } else {
            console.error('Failed to load AI suggestions:', data.data);
        }
    })
    .catch(error => {
        console.error('AI suggestions error:', error);
    });
}

/**
 * Display AI template suggestions
 */
function displayAITemplateSuggestions(suggestions) {
    const container = document.getElementById('templates-grid');
    if (!container) return;
    
    if (!suggestions || suggestions.length === 0) {
        container.innerHTML = '<p class="no-suggestions">No AI suggestions available at the moment.</p>';
        return;
    }
    
    const suggestionsHTML = suggestions.map(suggestion => `
        <div class="template-suggestion-card">
            <div class="suggestion-header">
                <h4>${suggestion.name}</h4>
                <span class="difficulty-badge ${suggestion.difficulty?.toLowerCase()}">${suggestion.difficulty}</span>
            </div>
            <div class="suggestion-description">${suggestion.description}</div>
            <div class="suggestion-benefits">
                <strong>Benefits:</strong> ${suggestion.benefits}
            </div>
            <div class="suggestion-meta">
                <span class="time-estimate">⏱️ ${suggestion.time_estimate}</span>
                <span class="category">📁 ${suggestion.category}</span>
            </div>
            <button class="btn btn-primary create-from-suggestion" 
                    onclick="createTemplateFromSuggestion('${suggestion.name}', '${suggestion.description}')">
                Create Template
            </button>
        </div>
    `).join('');
    
    container.innerHTML = suggestionsHTML;
}

/**
 * Template enhancement functions
 */
function suggestTemplateImprovements() {
    // Implementation for template improvements
    console.log('Suggesting template improvements...');
}

function generateTemplateVariations() {
    // Implementation for template variations
    console.log('Generating template variations...');
}

function optimizeForCountry() {
    // Implementation for country optimization
    console.log('Optimizing for country...');
}

function addIndustryInsights() {
    // Implementation for industry insights
    console.log('Adding industry insights...');
}

function createTemplateFromSuggestion(name, description) {
    // Implementation for creating template from AI suggestion
    console.log('Creating template from suggestion:', name);
}

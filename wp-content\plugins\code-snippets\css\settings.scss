@use 'common/editor';

$sections: general, editor, debug;

p.submit {
	display: flex;
	justify-content: space-between;
}

.settings-section,
p.submit {
	max-width: 1020px;
}

.nav-tab-wrapper {
	margin-bottom: 1em;
}

input[type=number] {
	width: 4em;
}

.CodeMirror {
	max-width: 800px;
	width: 100%;
	padding-right: 1em;
}

.CodeMirror-sizer::before {
	content: '<?php';
}

body.no-js {
	.nav-tab-wrapper {
		display: none;
	}

	.settings-section {
		display: block !important;
	}
}

body.js {

	.settings-section-title {
		border: 0;
		clip: rect(1px, 1px, 1px, 1px);
		clip-path: inset(50%);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
		word-wrap: normal !important;
	}

	.nav-tab:not(.nav-tab-active) {
		cursor: pointer;
	}

	.settings-section {
		display: none;
	}

	@each $section in $sections {
		.wrap[data-active-tab=#{$section}] .#{$section}-settings {
			display: block;
		}
	}
}

.license-status {
	display: inline-block;
	padding-right: 2em;
	line-height: 2;
	color: #aaa;
}

.license-status-valid {
	color: #2ecc40;
}

.license-status-expired {
	color: #dc3232;
}

.wrap[data-active-tab=license] .submit {
	display: none;
}

#code_snippets_remove_license {
	color: #b32d2e;
	border-color: #b32d2e;
}

#code_snippets_license_key {
	font-family: Consolas, Monaco, monospace;
}

#cloud_token {
	max-width: 450px;
    width: 90vw;
}

.cloud-message {
	width: fit-content;
    padding: 5px;
    border-radius: 5px;
    font-weight: 600;
}

.cloud-error {
    background: #e53935;
    color: #ffebee;
}

.cloud-success {
	background: #43a047;
	color: #e8f5e9;
}

.refresh-success {
    background: #2271b1;
    color: #ffeb3b;
}

.cloud-settings tbody tr:nth-child(n+5) {
    display: none;
}

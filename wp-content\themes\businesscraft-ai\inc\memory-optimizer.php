<?php
/**
 * ChatGABI Memory Optimizer
 * 
 * Prevents memory exhaustion and optimizes memory usage
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize memory optimization
 */
function chatgabi_init_memory_optimizer() {
    // Set memory limit if needed
    chatgabi_set_optimal_memory_limit();
    
    // Enable garbage collection
    if (function_exists('gc_enable')) {
        gc_enable();
    }
    
    // Add memory monitoring hooks
    add_action('wp_loaded', 'chatgabi_monitor_memory_usage');
    add_action('shutdown', 'chatgabi_cleanup_memory');
    
    // Add AJAX memory checks
    add_action('wp_ajax_chatgabi_check_memory', 'chatgabi_ajax_memory_check');
    add_action('wp_ajax_nopriv_chatgabi_check_memory', 'chatgabi_ajax_memory_check');
}
add_action('init', 'chatgabi_init_memory_optimizer', 1);

/**
 * Set optimal memory limit
 */
function chatgabi_set_optimal_memory_limit() {
    $current_limit = ini_get('memory_limit');
    $current_bytes = wp_convert_hr_to_bytes($current_limit);
    $required_bytes = 768 * 1024 * 1024; // 768MB
    
    if ($current_bytes < $required_bytes) {
        if (ini_set('memory_limit', '768M') === false) {
            error_log("ChatGABI: Failed to increase memory limit from {$current_limit} to 768M");
        } else {
            error_log("ChatGABI: Memory limit increased from {$current_limit} to 768M");
        }
    }
}

/**
 * Monitor memory usage
 */
function chatgabi_monitor_memory_usage() {
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_used = memory_get_usage(true);
    $memory_percentage = ($memory_used / $memory_limit) * 100;
    
    // Log warning if memory usage is high
    if ($memory_percentage > 80) {
        error_log("ChatGABI: High memory usage detected: " . round($memory_percentage, 2) . "% (" . size_format($memory_used) . " / " . size_format($memory_limit) . ")");
        
        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            $collected = gc_collect_cycles();
            if ($collected > 0) {
                error_log("ChatGABI: Garbage collection freed {$collected} cycles");
            }
        }
    }
    
    // Emergency cleanup if memory usage is critical
    if ($memory_percentage > 90) {
        chatgabi_emergency_memory_cleanup();
    }
}

/**
 * Emergency memory cleanup
 */
function chatgabi_emergency_memory_cleanup() {
    error_log("ChatGABI: Emergency memory cleanup initiated");
    
    // Clear all transients
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_%'");
    
    // Clear object cache if available
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
    
    // Force garbage collection
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
    
    // Clear global variables that might be holding large data
    if (isset($GLOBALS['chatgabi_large_data'])) {
        unset($GLOBALS['chatgabi_large_data']);
    }
    
    error_log("ChatGABI: Emergency memory cleanup completed");
}

/**
 * Cleanup memory on shutdown
 */
function chatgabi_cleanup_memory() {
    // Force final garbage collection
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
    
    // Log final memory usage
    $memory_used = memory_get_usage(true);
    $memory_peak = memory_get_peak_usage(true);
    
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log("ChatGABI: Final memory usage: " . size_format($memory_used) . " (Peak: " . size_format($memory_peak) . ")");
    }
}

/**
 * AJAX memory check
 */
function chatgabi_ajax_memory_check() {
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_used = memory_get_usage(true);
    $memory_available = $memory_limit - $memory_used;
    $memory_percentage = ($memory_used / $memory_limit) * 100;
    
    wp_send_json_success(array(
        'memory_limit' => size_format($memory_limit),
        'memory_used' => size_format($memory_used),
        'memory_available' => size_format($memory_available),
        'memory_percentage' => round($memory_percentage, 2),
        'status' => $memory_percentage > 90 ? 'critical' : ($memory_percentage > 80 ? 'warning' : 'ok')
    ));
}

/**
 * Check if operation can proceed based on memory
 */
function chatgabi_can_proceed_with_memory_check($required_mb = 64) {
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_used = memory_get_usage(true);
    $memory_available = $memory_limit - $memory_used;
    $required_bytes = $required_mb * 1024 * 1024;
    
    return $memory_available >= $required_bytes;
}

/**
 * Safe data loading with memory check
 */
function chatgabi_safe_load_data($callback, $required_mb = 64) {
    if (!chatgabi_can_proceed_with_memory_check($required_mb)) {
        error_log("ChatGABI: Insufficient memory to load data. Required: {$required_mb}MB");
        return false;
    }
    
    if (is_callable($callback)) {
        return call_user_func($callback);
    }
    
    return false;
}

/**
 * Memory-safe JSON decode
 */
function chatgabi_safe_json_decode($json_string, $assoc = true, $max_size_mb = 10) {
    $size_bytes = strlen($json_string);
    $max_bytes = $max_size_mb * 1024 * 1024;
    
    if ($size_bytes > $max_bytes) {
        error_log("ChatGABI: JSON string too large to decode safely: " . size_format($size_bytes));
        return false;
    }
    
    if (!chatgabi_can_proceed_with_memory_check($max_size_mb * 2)) {
        error_log("ChatGABI: Insufficient memory to decode JSON");
        return false;
    }
    
    $result = json_decode($json_string, $assoc);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("ChatGABI: JSON decode error: " . json_last_error_msg());
        return false;
    }
    
    return $result;
}

/**
 * Memory-safe file loading
 */
function chatgabi_safe_file_get_contents($file_path, $max_size_mb = 10) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $file_size = filesize($file_path);
    $max_bytes = $max_size_mb * 1024 * 1024;
    
    if ($file_size > $max_bytes) {
        error_log("ChatGABI: File too large to load safely: " . size_format($file_size));
        return false;
    }
    
    if (!chatgabi_can_proceed_with_memory_check($max_size_mb * 2)) {
        error_log("ChatGABI: Insufficient memory to load file");
        return false;
    }
    
    return file_get_contents($file_path);
}

/**
 * Clear ChatGABI-specific caches
 */
function chatgabi_clear_caches() {
    global $wpdb;
    
    // Clear ChatGABI transients
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_chatgabi_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_chatgabi_%'");
    
    // Clear template caches
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_chatgabi_template_%'");
    
    error_log("ChatGABI: Caches cleared");
}

/**
 * Get memory usage statistics
 */
function chatgabi_get_memory_stats() {
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_used = memory_get_usage(true);
    $memory_peak = memory_get_peak_usage(true);
    $memory_available = $memory_limit - $memory_used;
    $memory_percentage = ($memory_used / $memory_limit) * 100;
    
    return array(
        'limit' => $memory_limit,
        'limit_formatted' => size_format($memory_limit),
        'used' => $memory_used,
        'used_formatted' => size_format($memory_used),
        'peak' => $memory_peak,
        'peak_formatted' => size_format($memory_peak),
        'available' => $memory_available,
        'available_formatted' => size_format($memory_available),
        'percentage' => round($memory_percentage, 2),
        'status' => $memory_percentage > 90 ? 'critical' : ($memory_percentage > 80 ? 'warning' : 'ok')
    );
}

/**
 * Add memory stats to admin bar (for debugging)
 */
function chatgabi_add_memory_stats_to_admin_bar($wp_admin_bar) {
    if (!current_user_can('manage_options') || !defined('WP_DEBUG') || !WP_DEBUG) {
        return;
    }
    
    $stats = chatgabi_get_memory_stats();
    
    $wp_admin_bar->add_node(array(
        'id' => 'chatgabi-memory',
        'title' => 'Memory: ' . $stats['percentage'] . '% (' . $stats['used_formatted'] . ')',
        'href' => '#',
        'meta' => array(
            'class' => 'chatgabi-memory-' . $stats['status']
        )
    ));
}
add_action('admin_bar_menu', 'chatgabi_add_memory_stats_to_admin_bar', 999);

/**
 * Add memory monitoring CSS
 */
function chatgabi_add_memory_monitoring_css() {
    if (!current_user_can('manage_options') || !defined('WP_DEBUG') || !WP_DEBUG) {
        return;
    }
    
    echo '<style>
        .chatgabi-memory-ok { background-color: #46b450 !important; }
        .chatgabi-memory-warning { background-color: #ffb900 !important; }
        .chatgabi-memory-critical { background-color: #dc3232 !important; }
    </style>';
}
add_action('wp_head', 'chatgabi_add_memory_monitoring_css');
add_action('admin_head', 'chatgabi_add_memory_monitoring_css');
?>

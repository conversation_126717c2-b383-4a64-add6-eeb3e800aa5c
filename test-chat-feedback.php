<?php
/**
 * Test Chat Feedback Integration
 * This page tests the feedback system integration with the chat interface
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

get_header();
?>

<div class="container" style="max-width: 800px; margin: 40px auto; padding: 20px;">
    <h1>🧪 Chat Feedback System Test</h1>
    <p>This page tests the integration between the ChatGABI chat interface and the feedback rating system.</p>
    
    <div class="test-instructions" style="background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>📋 Test Instructions:</h3>
        <ol>
            <li><strong>Send a message</strong> using the chat interface below</li>
            <li><strong>Wait for AI response</strong> to complete</li>
            <li><strong>Look for feedback interface</strong> that should appear below the AI response</li>
            <li><strong>Test star ratings</strong> by clicking on stars (1-5)</li>
            <li><strong>Test thumbs up/down</strong> by clicking thumbs buttons</li>
            <li><strong>Test detailed feedback</strong> by clicking the gear icon</li>
            <li><strong>Submit text feedback</strong> and category ratings</li>
            <li><strong>Check admin dashboard</strong> to see if feedback was recorded</li>
        </ol>
    </div>

    <div class="chat-test-section" style="background: white; border: 1px solid #ddd; border-radius: 12px; padding: 30px; margin: 30px 0;">
        <h2>💬 Chat Interface with Feedback</h2>
        
        <?php
        // Display the chat interface
        echo do_shortcode('[businesscraft_ai_chat show_history="false" show_examples="true"]');
        ?>
    </div>

    <div class="feedback-status" style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>🔍 Debugging Information</h3>
        <div id="debug-info">
            <p><strong>User ID:</strong> <?php echo get_current_user_id(); ?></p>
            <p><strong>Credits:</strong> <?php echo get_user_meta(get_current_user_id(), 'chatgabi_credits', true) ?: 0; ?></p>
            <p><strong>Feedback System Status:</strong> <span id="feedback-system-status">Checking...</span></p>
            <p><strong>JavaScript Events:</strong> <span id="js-events-status">Monitoring...</span></p>
        </div>
    </div>

    <div class="admin-links" style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>🔗 Quick Links</h3>
        <p>
            <a href="/wp-admin/admin.php?page=chatgabi-feedback" class="button button-primary" target="_blank">
                📊 View Feedback Dashboard
            </a>
            <a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=feedback" class="button" target="_blank">
                💬 View Text Feedback
            </a>
            <a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=training" class="button" target="_blank">
                🎯 View Training Data
            </a>
        </p>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    console.log('🧪 Chat Feedback Test Page Loaded');
    
    // Check if feedback system is loaded
    if (typeof window.chatgabiFeedbackSystem !== 'undefined') {
        $('#feedback-system-status').text('✅ Loaded').css('color', 'green');
        console.log('✅ Feedback system detected:', window.chatgabiFeedbackSystem);
    } else {
        $('#feedback-system-status').text('❌ Not Loaded').css('color', 'red');
        console.log('❌ Feedback system not detected');
    }
    
    // Monitor chat events
    let eventCount = 0;
    
    $(document).on('chatgabi:responseCompleted', function(event, data) {
        eventCount++;
        $('#js-events-status').text(`✅ ${eventCount} events detected`).css('color', 'green');
        console.log('🎯 Chat response completed event:', data);
        
        // Check if feedback interface was added
        setTimeout(function() {
            const feedbackInterfaces = $('.feedback-interface').length;
            if (feedbackInterfaces > 0) {
                console.log(`✅ Found ${feedbackInterfaces} feedback interface(s)`);
                
                // Add visual indicator
                $('.feedback-interface').each(function() {
                    if (!$(this).find('.test-indicator').length) {
                        $(this).prepend('<div class="test-indicator" style="background: #4CAF50; color: white; padding: 5px; border-radius: 3px; margin-bottom: 10px; font-size: 12px;">🧪 Test: Feedback interface loaded successfully!</div>');
                    }
                });
            } else {
                console.log('❌ No feedback interfaces found');
            }
        }, 1000);
    });
    
    // Monitor feedback submissions
    $(document).on('click', '.feedback-star, .feedback-thumbs', function() {
        console.log('🎯 Feedback interaction detected:', $(this).attr('class'));
    });
    
    // Monitor AJAX feedback submissions
    $(document).ajaxComplete(function(event, xhr, settings) {
        if (settings.url && settings.url.includes('admin-ajax.php') && settings.data && settings.data.includes('chatgabi_submit_feedback')) {
            console.log('🎯 Feedback AJAX submission detected');
            console.log('Response:', xhr.responseText);
        }
    });
    
    // Add some test styling
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .button {
                display: inline-block;
                padding: 8px 16px;
                margin: 5px;
                background: #0073aa;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                border: none;
                cursor: pointer;
            }
            .button-primary {
                background: #007cba;
            }
            .button:hover {
                background: #005a87;
                color: white;
            }
            .feedback-interface {
                border: 2px dashed #4CAF50 !important;
                background: rgba(76, 175, 80, 0.05) !important;
            }
        `)
        .appendTo('head');
});
</script>

<style>
.container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-instructions ol {
    padding-left: 20px;
}

.test-instructions li {
    margin: 8px 0;
    line-height: 1.5;
}

#debug-info p {
    margin: 8px 0;
    font-family: monospace;
    background: white;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}
</style>

<?php get_footer(); ?>

# ChatGABI Tiered Onboarding Flow System

## Overview

The Tiered Onboarding Flow System provides personalized user experiences through two distinct tracks: SME (Small & Medium Enterprises) and Creator tracks. This system guides new users through a comprehensive setup process that customizes their ChatGABI experience based on their business type, goals, and preferences.

## Features

### 🎯 **Dual Track System**
- **SME Track**: Business planning, financial forecasting, market analysis focus
- **Creator Track**: Content creation, brand development, audience engagement focus
- **Smart Profile Detection**: Automatic categorization based on user responses
- **Customized Dashboards**: Personalized interface based on selected track

### 📊 **Comprehensive Profiling**
- **Business Information**: Industry, country, size, revenue, team size
- **Goals & Challenges**: Primary objectives and pain points
- **Preferences**: Language, support preferences, timeline urgency
- **Content Focus**: For creators - content types, platforms, audience

### 🔄 **Progressive Flow Management**
- **Multi-step Interface**: Guided step-by-step progression
- **Progress Tracking**: Visual progress indicators and completion status
- **Data Persistence**: Save progress at each step
- **Flexible Navigation**: Forward/backward navigation with validation

### 🎨 **Personalized Experience**
- **Template Recommendations**: Curated templates based on profile
- **Dashboard Customization**: Personalized widget arrangement
- **Quick Actions**: Profile-specific shortcuts and tools
- **Industry-Specific Content**: Tailored insights and opportunities

## Technical Implementation

### Database Schema

#### `wp_chatgabi_user_profiles` Table
```sql
CREATE TABLE wp_chatgabi_user_profiles (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    profile_type varchar(20) NOT NULL DEFAULT 'sme',
    business_stage varchar(50) NOT NULL DEFAULT 'idea',
    primary_industry varchar(100),
    target_country varchar(5),
    business_size varchar(20),
    annual_revenue varchar(50),
    team_size varchar(20),
    primary_goals text,
    challenges text,
    experience_level varchar(20) NOT NULL DEFAULT 'beginner',
    preferred_language varchar(5) NOT NULL DEFAULT 'en',
    marketing_focus varchar(100),
    content_types text,
    target_audience varchar(100),
    brand_stage varchar(50),
    social_platforms text,
    budget_range varchar(50),
    timeline_urgency varchar(20),
    support_preference varchar(50),
    profile_completed tinyint(1) NOT NULL DEFAULT 0,
    onboarding_completed tinyint(1) NOT NULL DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_id (user_id)
);
```

#### `wp_chatgabi_onboarding_progress` Table
```sql
CREATE TABLE wp_chatgabi_onboarding_progress (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    step_name varchar(50) NOT NULL,
    step_data longtext,
    completed tinyint(1) NOT NULL DEFAULT 0,
    completed_at datetime,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_step (user_id, step_name)
);
```

### Core Functions

#### Profile Management
```php
// Create user profile
chatgabi_create_user_profile($user_id)

// Get user profile
chatgabi_get_user_profile($user_id)

// Update user profile
chatgabi_update_user_profile($user_id, $data)

// Complete onboarding
chatgabi_complete_onboarding($user_id)
```

#### Progress Tracking
```php
// Save step progress
chatgabi_save_onboarding_step($user_id, $step_name, $step_data, $completed)

// Get progress
chatgabi_get_onboarding_progress($user_id)

// Get flow steps
chatgabi_get_onboarding_steps($profile_type)
```

#### Personalization
```php
// Get recommendations
chatgabi_get_recommended_templates($user_id)

// Setup dashboard
chatgabi_setup_personalized_dashboard($user_id)
```

### AJAX Endpoints

#### Step Management
- **Endpoint**: `chatgabi_save_onboarding_step`
- **Purpose**: Save progress for individual onboarding steps
- **Parameters**: `step_name`, `step_data`, `completed`
- **Response**: Success/error status with message

#### Completion
- **Endpoint**: `chatgabi_complete_onboarding`
- **Purpose**: Mark onboarding as complete and setup personalization
- **Response**: Success status with redirect URL

### Frontend Components

#### Onboarding Interface
- **File**: `template-parts/onboarding-flow.php`
- **Features**: Multi-step form, progress tracking, responsive design
- **Integration**: Shortcode support: `[chatgabi_onboarding]`

#### JavaScript Management
- **File**: `assets/js/onboarding.js`
- **Features**: Step navigation, form validation, AJAX handling
- **Events**: Form submissions, profile type selection, navigation

#### CSS Styling
- **File**: `assets/css/onboarding.css`
- **Features**: Modern design, animations, responsive layout
- **Themes**: Gradient backgrounds, card-based layout

## Onboarding Flow Steps

### Common Steps (Both Tracks)
1. **Welcome** - Introduction to ChatGABI features
2. **Profile Type** - SME vs Creator track selection

### SME Track Steps
3. **Business Basics** - Industry, country, business size
4. **Business Stage** - Current stage and development phase
5. **Goals & Challenges** - Primary objectives and obstacles
6. **Preferences** - Language, support, timeline preferences

### Creator Track Steps
3. **Creator Basics** - Content focus, target country
4. **Content Focus** - Content types and platforms
5. **Audience & Brand** - Target audience and brand stage
6. **Preferences** - Language, support, timeline preferences

### Completion Steps (Both Tracks)
7. **Template Recommendations** - Curated templates for profile
8. **Dashboard Setup** - Personalized workspace configuration
9. **Complete** - Welcome message and next steps

## Profile Types & Customization

### SME Profile Configuration
```php
$sme_config = array(
    'default_tab' => 'overview',
    'featured_tools' => array('business-planning', 'financial-analysis', 'market-research'),
    'quick_actions' => array('create-business-plan', 'financial-forecast', 'market-analysis'),
    'widget_order' => array('credit-balance', 'recent-templates', 'opportunities', 'analytics')
);
```

### Creator Profile Configuration
```php
$creator_config = array(
    'default_tab' => 'overview',
    'featured_tools' => array('content-strategy', 'brand-development', 'social-media'),
    'quick_actions' => array('content-calendar', 'brand-strategy', 'social-posts'),
    'widget_order' => array('credit-balance', 'content-ideas', 'brand-analytics', 'opportunities')
);
```

## Integration Points

### User Registration Hook
```php
// Trigger onboarding for new users
add_action('user_register', 'chatgabi_trigger_onboarding');
```

### Login Redirect
```php
// Check onboarding status on login
add_action('wp_login', 'chatgabi_check_onboarding_status', 10, 2);
```

### Template System Integration
- Recommended templates based on profile type
- Industry-specific template suggestions
- Profile-aware template categorization

### Dashboard Personalization
- Widget arrangement based on profile
- Quick actions customized to user type
- Featured tools relevant to business focus

## Usage Examples

### Basic Integration
```php
// Display onboarding flow
echo do_shortcode('[chatgabi_onboarding]');
```

### Custom Profile Check
```php
// Check if user needs onboarding
$needs_onboarding = get_user_meta($user_id, 'chatgabi_needs_onboarding', true);
if ($needs_onboarding) {
    wp_redirect(home_url('/onboarding/'));
}
```

### Profile-Based Content
```php
// Get user profile for customization
$profile = chatgabi_get_user_profile($user_id);
if ($profile && $profile->profile_type === 'sme') {
    // Show SME-specific content
} else {
    // Show creator-specific content
}
```

## Configuration Options

### Step Customization
```php
// Modify onboarding steps
add_filter('chatgabi_onboarding_steps', function($steps, $profile_type) {
    // Add custom steps or modify existing ones
    return $steps;
}, 10, 2);
```

### Recommendation Engine
```php
// Customize template recommendations
add_filter('chatgabi_recommended_templates', function($templates, $user_id) {
    // Modify recommendations based on custom logic
    return $templates;
}, 10, 2);
```

## Performance Considerations

### Database Optimization
- Indexed queries for fast profile retrieval
- Efficient step progress tracking
- Minimal database calls during flow

### Caching Strategy
- Transient caching for table existence checks
- User profile caching for repeated access
- Step configuration caching

### Frontend Performance
- Progressive loading of step content
- Optimized CSS and JavaScript delivery
- Responsive design for mobile performance

## Security Features

### Data Protection
- Nonce verification for all AJAX requests
- Input sanitization and validation
- User authentication requirements

### Access Control
- Profile ownership verification
- Step completion validation
- Secure data transmission

## Analytics & Tracking

### Completion Metrics
- Track onboarding completion rates
- Monitor step abandonment points
- Analyze profile type distribution

### User Engagement
- Time spent per step
- Most common profile configurations
- Template recommendation effectiveness

## Future Enhancements

### Planned Features
- A/B testing for onboarding flows
- Advanced profile matching algorithms
- Integration with external business tools
- Multi-language onboarding content

### Scalability Improvements
- Background processing for heavy operations
- Advanced caching mechanisms
- API endpoints for mobile app integration

## Support & Troubleshooting

### Common Issues
1. **Onboarding not triggering**: Check user registration hooks
2. **Step data not saving**: Verify AJAX endpoints and nonces
3. **Profile not updating**: Check database table creation

### Debug Mode
Enable debug logging by adding to wp-config.php:
```php
define('CHATGABI_DEBUG_ONBOARDING', true);
```

### Performance Monitoring
Monitor onboarding performance in the admin analytics dashboard for completion rates and user engagement metrics.

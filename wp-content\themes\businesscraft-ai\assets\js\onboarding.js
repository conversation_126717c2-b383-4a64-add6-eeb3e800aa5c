/**
 * ChatGABI Onboarding Flow JavaScript
 * 
 * Handles multi-step onboarding interface, form validation,
 * and AJAX interactions for user profile setup.
 */

(function($) {
    'use strict';

    // Initialize onboarding
    function initializeOnboarding() {
        console.log('Initializing ChatGABI Onboarding Flow');
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize current step
        initializeCurrentStep();
        
        console.log('Onboarding initialized');
    }

    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Profile type selection
        $(document).on('click', '.select-profile-btn', function(e) {
            e.preventDefault();
            const profileType = $(this).data('type');
            selectProfileType(profileType);
        });

        // Form submissions
        $(document).on('submit', '.onboarding-form', function(e) {
            e.preventDefault();
            handleFormSubmission($(this));
        });

        // Step navigation
        $(document).on('click', '.step-dot', function() {
            const stepName = $(this).data('step');
            navigateToStep(stepName);
        });

        // Checkbox handling for content types
        $(document).on('change', 'input[name="content_types[]"]', function() {
            updateContentTypesSelection();
        });

        // Enhanced accessibility and mobile support
        setupAccessibilityFeatures();
        setupMobileOptimizations();
        setupProgressTracking();

        console.log('Enhanced onboarding event listeners set up');
    }

    /**
     * Initialize current step
     */
    function initializeCurrentStep() {
        const currentStep = window.chatgabiOnboarding.currentStep;
        
        // Highlight current step in progress indicator
        $('.step-dot').removeClass('current');
        $(`.step-dot[data-step="${currentStep}"]`).addClass('current');
        
        // Load any saved data for current step
        loadStepData(currentStep);
    }

    /**
     * Select profile type
     */
    function selectProfileType(profileType) {
        // Visual feedback
        $('.profile-option').removeClass('selected');
        $(`.profile-option[data-type="${profileType}"]`).addClass('selected');
        
        // Save profile type
        saveStepData('profile_type', { profile_type: profileType }, true);
        
        // Navigate to next step
        setTimeout(() => {
            if (profileType === 'sme') {
                navigateToStep('business_basics');
            } else {
                navigateToStep('creator_basics');
            }
        }, 500);
    }

    /**
     * Handle form submission
     */
    function handleFormSubmission($form) {
        const formData = new FormData($form[0]);
        const stepName = getCurrentStepName();
        
        // Convert FormData to object
        const data = {};
        for (let [key, value] of formData.entries()) {
            if (key.endsWith('[]')) {
                // Handle array fields (like content_types[])
                const arrayKey = key.slice(0, -2);
                if (!data[arrayKey]) {
                    data[arrayKey] = [];
                }
                data[arrayKey].push(value);
            } else {
                data[key] = value;
            }
        }
        
        // Validate required fields
        if (!validateStepData(stepName, data)) {
            return;
        }
        
        // Show loading state
        showLoadingState($form);
        
        // Save step data
        saveStepData(stepName, data, true)
            .then(() => {
                // Navigate to next step
                const nextStep = getNextStep(stepName);
                if (nextStep) {
                    navigateToStep(nextStep);
                } else {
                    completeOnboarding();
                }
            })
            .catch(error => {
                console.error('Failed to save step data:', error);
                showMessage('error', 'Failed to save your information. Please try again.');
            })
            .finally(() => {
                hideLoadingState($form);
            });
    }

    /**
     * Navigate to specific step
     */
    function navigateToStep(stepName) {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('step', stepName);
        window.location.href = currentUrl.toString();
    }

    /**
     * Navigate to next step
     */
    window.nextStep = function() {
        const currentStep = window.chatgabiOnboarding.currentStep;
        const nextStep = getNextStep(currentStep);
        
        if (nextStep) {
            navigateToStep(nextStep);
        }
    };

    /**
     * Navigate to previous step
     */
    window.previousStep = function() {
        const currentStep = window.chatgabiOnboarding.currentStep;
        const prevStep = getPreviousStep(currentStep);
        
        if (prevStep) {
            navigateToStep(prevStep);
        }
    };

    /**
     * Get next step in flow
     */
    function getNextStep(currentStep) {
        const steps = window.chatgabiOnboarding.steps;
        const currentIndex = steps.indexOf(currentStep);
        
        if (currentIndex >= 0 && currentIndex < steps.length - 1) {
            return steps[currentIndex + 1];
        }
        
        return null;
    }

    /**
     * Get previous step in flow
     */
    function getPreviousStep(currentStep) {
        const steps = window.chatgabiOnboarding.steps;
        const currentIndex = steps.indexOf(currentStep);
        
        if (currentIndex > 0) {
            return steps[currentIndex - 1];
        }
        
        return null;
    }

    /**
     * Get current step name
     */
    function getCurrentStepName() {
        return window.chatgabiOnboarding.currentStep;
    }

    /**
     * Validate step data
     */
    function validateStepData(stepName, data) {
        let isValid = true;
        
        // Clear previous errors
        $('.form-error').remove();
        $('.form-group').removeClass('error');
        
        switch (stepName) {
            case 'business_basics':
                if (!data.primary_industry) {
                    showFieldError('primary_industry', 'Please select your primary industry');
                    isValid = false;
                }
                if (!data.target_country) {
                    showFieldError('target_country', 'Please select your target country');
                    isValid = false;
                }
                if (!data.business_size) {
                    showFieldError('business_size', 'Please select your business size');
                    isValid = false;
                }
                break;
                
            case 'creator_basics':
                if (!data.marketing_focus) {
                    showFieldError('marketing_focus', 'Please select your content focus');
                    isValid = false;
                }
                if (!data.target_country) {
                    showFieldError('target_country', 'Please select your target country');
                    isValid = false;
                }
                if (!data.content_types || data.content_types.length === 0) {
                    showMessage('error', 'Please select at least one content type');
                    isValid = false;
                }
                break;
        }
        
        return isValid;
    }

    /**
     * Show field error
     */
    function showFieldError(fieldName, message) {
        const $field = $(`[name="${fieldName}"]`);
        const $group = $field.closest('.form-group');
        
        $group.addClass('error');
        $group.append(`<div class="form-error">${message}</div>`);
    }

    /**
     * Save step data via AJAX
     */
    function saveStepData(stepName, data, completed = false) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: chatgabiOnboardingConfig.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_save_onboarding_step',
                    nonce: chatgabiOnboardingConfig.nonce,
                    step: stepName,
                    data: data,
                    completed: completed ? 1 : 0
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        reject(new Error(response.data?.message || 'Failed to save step data'));
                    }
                },
                error: function(xhr, status, error) {
                    reject(new Error(`Network error: ${error}`));
                }
            });
        });
    }

    /**
     * Load step data
     */
    function loadStepData(stepName) {
        // This would load any previously saved data for the step
        // For now, we'll skip this as the PHP template handles initial data loading
    }

    /**
     * Complete onboarding
     */
    function completeOnboarding() {
        showLoadingState($('.step-content'));
        
        $.ajax({
            url: chatgabiOnboardingConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_complete_onboarding',
                nonce: chatgabiOnboardingConfig.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Redirect to dashboard
                    window.location.href = chatgabiOnboardingConfig.dashboardUrl;
                } else {
                    showMessage('error', response.data?.message || 'Failed to complete onboarding');
                }
            },
            error: function(xhr, status, error) {
                showMessage('error', 'Network error occurred. Please try again.');
            },
            complete: function() {
                hideLoadingState($('.step-content'));
            }
        });
    }

    /**
     * Update content types selection
     */
    function updateContentTypesSelection() {
        const selectedTypes = $('input[name="content_types[]"]:checked').length;
        
        if (selectedTypes > 0) {
            $('.checkbox-grid').removeClass('error');
            $('.form-error').remove();
        }
    }

    /**
     * Show loading state
     */
    function showLoadingState($element) {
        $element.addClass('loading');
        $element.find('button[type="submit"]').prop('disabled', true).text('Saving...');
    }

    /**
     * Hide loading state
     */
    function hideLoadingState($element) {
        $element.removeClass('loading');
        $element.find('button[type="submit"]').prop('disabled', false).text('Continue →');
    }

    /**
     * Show message
     */
    function showMessage(type, message) {
        const messageClass = type === 'success' ? 'success' : 'error';
        const messageIcon = type === 'success' ? '✅' : '❌';
        
        // Remove existing messages
        $('.onboarding-message').remove();
        
        const messageHtml = `
            <div class="onboarding-message ${messageClass}">
                <span class="message-icon">${messageIcon}</span>
                <span class="message-text">${message}</span>
                <button class="message-close" onclick="$(this).parent().fadeOut()">&times;</button>
            </div>
        `;
        
        $('.onboarding-content').prepend(messageHtml);
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(() => {
                $('.onboarding-message').fadeOut();
            }, 5000);
        }
    }

    /**
     * Setup accessibility features
     */
    function setupAccessibilityFeatures() {
        // Add ARIA labels and roles
        $('.profile-option').attr('role', 'button').attr('tabindex', '0');
        $('.step-dot').attr('role', 'button').attr('tabindex', '0');
        $('.feature-card').attr('role', 'button').attr('tabindex', '0');

        // Add aria-labels
        $('.profile-option').each(function() {
            const type = $(this).data('type');
            $(this).attr('aria-label', `Select ${type} profile type`);
        });

        $('.step-dot').each(function() {
            const step = $(this).data('step');
            const stepNumber = $(this).index() + 1;
            $(this).attr('aria-label', `Go to step ${stepNumber}: ${step.replace('_', ' ')}`);
        });

        // Keyboard navigation
        $(document).on('keydown', '.profile-option, .step-dot, .feature-card', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).click();
            }
        });

        // Focus management
        $(document).on('click', '.btn-next, .btn-prev', function() {
            setTimeout(() => {
                $('.step-content h1, .step-content h2').first().focus();
            }, 100);
        });

        // Screen reader announcements
        setupScreenReaderAnnouncements();
    }

    /**
     * Setup screen reader announcements
     */
    function setupScreenReaderAnnouncements() {
        // Create live region for announcements
        if (!$('#onboarding-announcements').length) {
            $('body').append('<div id="onboarding-announcements" aria-live="polite" aria-atomic="true" class="sr-only"></div>');
        }

        // Announce step changes
        $(document).on('stepChanged', function(e, stepName) {
            const stepNumber = window.chatgabiOnboarding.steps.indexOf(stepName) + 1;
            const totalSteps = window.chatgabiOnboarding.steps.length;
            announceToScreenReader(`Step ${stepNumber} of ${totalSteps}: ${stepName.replace('_', ' ')}`);
        });

        // Announce validation errors
        $(document).on('validationError', function(e, message) {
            announceToScreenReader(`Error: ${message}`);
        });

        // Announce progress
        $(document).on('progressUpdate', function(e, progress) {
            announceToScreenReader(`Progress: ${progress}% complete`);
        });
    }

    /**
     * Announce message to screen readers
     */
    function announceToScreenReader(message) {
        const $announcements = $('#onboarding-announcements');
        $announcements.text(message);

        // Clear after announcement
        setTimeout(() => {
            $announcements.empty();
        }, 1000);
    }

    /**
     * Setup mobile optimizations
     */
    function setupMobileOptimizations() {
        // Touch gesture support
        if (isMobileDevice()) {
            setupSwipeGestures();
            optimizeMobileInteractions();
        }

        // Responsive form adjustments
        adjustFormForMobile();

        // Mobile-specific UI enhancements
        enhanceMobileUI();
    }

    /**
     * Check if device is mobile
     */
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    /**
     * Setup swipe gestures for mobile
     */
    function setupSwipeGestures() {
        let startX = 0;
        let startY = 0;

        $('.onboarding-content').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
            startY = e.originalEvent.touches[0].clientY;
        });

        $('.onboarding-content').on('touchend', function(e) {
            if (!startX || !startY) return;

            const endX = e.originalEvent.changedTouches[0].clientX;
            const endY = e.originalEvent.changedTouches[0].clientY;

            const diffX = startX - endX;
            const diffY = startY - endY;

            // Only process horizontal swipes
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swipe left - next step
                    const nextBtn = $('.btn-next:visible');
                    if (nextBtn.length && !nextBtn.prop('disabled')) {
                        nextBtn.click();
                    }
                } else {
                    // Swipe right - previous step
                    const prevBtn = $('.btn-prev:visible');
                    if (prevBtn.length && !prevBtn.prop('disabled')) {
                        prevBtn.click();
                    }
                }
            }

            startX = 0;
            startY = 0;
        });
    }

    /**
     * Optimize mobile interactions
     */
    function optimizeMobileInteractions() {
        // Increase touch targets
        $('.profile-option, .feature-card, .step-dot').css({
            'min-height': '44px',
            'min-width': '44px'
        });

        // Add haptic feedback simulation
        $('.profile-option, .feature-card, .btn').on('touchstart', function() {
            $(this).addClass('touch-active');
        }).on('touchend', function() {
            $(this).removeClass('touch-active');
        });

        // Prevent zoom on input focus
        $('input, select, textarea').attr('autocomplete', 'off');
    }

    /**
     * Adjust forms for mobile
     */
    function adjustFormForMobile() {
        if (isMobileDevice()) {
            // Stack form elements vertically on mobile
            $('.form-row').addClass('mobile-stack');

            // Adjust input sizes
            $('input, select, textarea').addClass('mobile-input');

            // Optimize button layout
            $('.step-actions').addClass('mobile-actions');
        }
    }

    /**
     * Enhance mobile UI
     */
    function enhanceMobileUI() {
        if (isMobileDevice()) {
            // Add mobile-specific classes
            $('.chatgabi-onboarding').addClass('mobile-onboarding');

            // Optimize progress bar for mobile
            $('.progress-container').addClass('mobile-progress');

            // Add swipe indicators
            if ($('.swipe-indicator').length === 0) {
                $('.step-actions').append('<div class="swipe-indicator">← Swipe to navigate →</div>');
            }
        }
    }

    /**
     * Setup progress tracking
     */
    function setupProgressTracking() {
        // Track step completion
        $(document).on('stepCompleted', function(e, stepName) {
            updateProgressBar();
            saveProgressToLocalStorage();
        });

        // Initialize progress
        updateProgressBar();
    }

    /**
     * Update progress bar
     */
    function updateProgressBar() {
        const currentStep = window.chatgabiOnboarding.currentStep;
        const steps = window.chatgabiOnboarding.steps;
        const currentIndex = steps.indexOf(currentStep);
        const progress = ((currentIndex + 1) / steps.length) * 100;

        $('.progress-fill').css('width', `${progress}%`);
        $('.progress-text').text(`Step ${currentIndex + 1} of ${steps.length}`);

        // Trigger progress update event
        $(document).trigger('progressUpdate', [progress]);
    }

    /**
     * Save progress to localStorage
     */
    function saveProgressToLocalStorage() {
        const progress = {
            currentStep: window.chatgabiOnboarding.currentStep,
            timestamp: Date.now(),
            userId: window.chatgabiOnboarding.userId
        };

        localStorage.setItem('chatgabi_onboarding_progress', JSON.stringify(progress));
    }

    /**
     * Enhanced step navigation with accessibility
     */
    function navigateToStepAccessible(stepName) {
        // Announce step change
        $(document).trigger('stepChanged', [stepName]);

        // Navigate
        navigateToStep(stepName);
    }

    // Make functions available globally
    window.initializeOnboarding = initializeOnboarding;
    window.navigateToStepAccessible = navigateToStepAccessible;
    window.announceToScreenReader = announceToScreenReader;

})(jQuery);

# BusinessCraft AI - Comprehensive Opportunity Alerts System

## 🎯 Overview

The Opportunity Alerts System is a comprehensive notification platform that automatically matches business opportunities from across African countries with user-defined criteria and sends personalized email alerts via SendPulse API integration.

## 🏗️ System Architecture

### Core Components

1. **Alert Subscription Management** - User-friendly interface for creating and managing alert filters
2. **Automated Matching Engine** - Intelligent algorithm that scores opportunities against user criteria
3. **Email Delivery System** - SendPulse API integration with delivery tracking and analytics
4. **Cron-based Processing** - WordPress cron jobs for automated alert processing
5. **User Dashboard** - Responsive interface for managing subscriptions and viewing statistics

### Database Schema

#### `wp_chatgabi_opportunity_alerts`
```sql
- id (Primary Key)
- user_id (Foreign Key to wp_users)
- filter_name (User-defined alert name)
- countries (JSON array of target countries)
- opportunity_types (JSON array of opportunity types)
- sectors (JSON array of business sectors)
- amount_min/amount_max (Funding amount range)
- keywords (Comma-separated search terms)
- deadline_days (Alert for opportunities closing within X days)
- notification_frequency (immediate/daily/weekly)
- is_active (Boolean status)
- last_processed (Timestamp)
- created_at/updated_at (Timestamps)
```

#### `wp_chatgabi_alert_logs`
```sql
- id (Primary Key)
- user_id/alert_id (Foreign Keys)
- email_address (Recipient email)
- email_subject (Email subject line)
- opportunities_count (Number of opportunities in email)
- email_type (immediate/daily/weekly)
- delivery_status (pending/sent/delivered/bounced/spam)
- sendpulse_message_id (SendPulse tracking ID)
- opened_at/clicked_at/unsubscribed_at (Engagement timestamps)
- error_message (Delivery error details)
- sent_at (Timestamp)
```

#### `wp_chatgabi_alert_matches`
```sql
- id (Primary Key)
- user_id/alert_id (Foreign Keys)
- opportunity_data (JSON object with opportunity details)
- match_score (Percentage match score 0-100)
- is_sent (Boolean flag)
- created_at (Timestamp)
```

## 🔧 Core Features

### 1. Subscription System

**Geographic Filters:**
- Ghana, Kenya, Nigeria, South Africa
- Multi-country selection supported

**Opportunity Types:**
- Grants, Loans, Incubators, Accelerators
- Competitions, Tenders, Contracts
- Investment opportunities, Regulatory support

**Industry/Sector Filters:**
- Agriculture, Technology, Fintech, Healthcare
- Education, Manufacturing, Energy, Transportation
- Tourism, Retail, Construction

**Advanced Criteria:**
- Amount ranges (minimum/maximum values)
- Keywords/search terms matching
- Deadline proximity alerts (7, 14, 30, 60 days)

**Notification Preferences:**
- **Immediate:** Real-time alerts for new matches
- **Daily Digest:** Consolidated daily summary at 8 AM
- **Weekly Summary:** Weekly roundup on Mondays at 9 AM

### 2. Intelligent Matching Algorithm

The system uses a weighted scoring algorithm to match opportunities:

- **Country Match (20%):** Mandatory filter, must match selected countries
- **Opportunity Type (15%):** Matches selected opportunity types
- **Sector Match (15%):** Matches selected business sectors
- **Keywords (20%):** Searches in title and description
- **Amount Range (10%):** Filters by funding amount
- **Deadline Proximity (10%):** Bonus for urgent deadlines
- **Freshness (10%):** Preference for recently added opportunities

**Match Score Calculation:**
- Opportunities scored 0-100% based on criteria alignment
- Minimum 60% score required for inclusion
- Results sorted by match score (highest first)

### 3. Email Delivery System

**SendPulse API Integration:**
- Professional email delivery service
- Delivery tracking and analytics
- Bounce and spam handling
- Open and click tracking

**Email Templates:**
- Responsive HTML templates
- Personalized content with user name
- Opportunity cards with key details
- One-click unsubscribe links
- Mobile-optimized design

**Delivery Features:**
- Rate limiting (100 emails/day per user)
- Duplicate prevention
- Error handling and retry logic
- Webhook support for delivery status

### 4. User Dashboard

**Alert Management:**
- Create, edit, delete alert subscriptions
- Toggle alerts active/inactive
- Preview matching opportunities
- Duplicate and modify existing alerts

**Statistics & Analytics:**
- Total opportunities matched (30-day period)
- Emails sent and delivery status
- Open rates and engagement metrics
- Alert performance tracking

**User Experience:**
- Mobile-responsive design
- Real-time preview functionality
- Intuitive form validation
- AJAX-powered interactions

## 📋 File Structure

```
wp-content/themes/businesscraft-ai/
├── inc/
│   ├── opportunity-alerts.php          # Core alert management system
│   └── sendpulse-integration.php       # Email delivery integration
├── assets/
│   ├── js/opportunity-alerts.js        # Dashboard JavaScript
│   └── css/opportunity-alerts.css      # Dashboard styling
├── page-alerts.php                     # User dashboard template
├── initialize-opportunity-alerts.php   # System setup script
├── test-opportunity-alerts.php         # Testing script
└── OPPORTUNITY-ALERTS-SYSTEM.md        # This documentation
```

## 🚀 Installation & Setup

### 1. System Initialization

Run the initialization script to set up database tables and cron jobs:
```
http://yoursite.com/wp-content/themes/businesscraft-ai/initialize-opportunity-alerts.php
```

### 2. SendPulse Configuration

1. Sign up for SendPulse account at https://sendpulse.com
2. Generate API credentials (User ID and Secret)
3. Navigate to **ChatGABI → Email Settings** in WordPress admin
4. Enter your SendPulse credentials
5. Test the connection

### 3. WordPress Configuration

Ensure WordPress cron is functioning properly:
```php
// Add to wp-config.php if needed
define('DISABLE_WP_CRON', false);
```

For high-traffic sites, consider server-level cron:
```bash
# Add to server crontab
*/15 * * * * wget -q -O - http://yoursite.com/wp-cron.php?doing_wp_cron
```

## 🔄 Automated Processing

### Cron Job Schedule

1. **Process Immediate Alerts** - Every 15 minutes
   - Checks for new opportunities
   - Matches against immediate notification preferences
   - Sends individual alert emails

2. **Daily Digest** - Daily at 8:00 AM
   - Consolidates opportunities for daily subscribers
   - Groups by alert subscription
   - Sends digest emails

3. **Weekly Summary** - Mondays at 9:00 AM
   - Weekly roundup for weekly subscribers
   - Comprehensive opportunity overview
   - Performance statistics included

4. **Cleanup Old Logs** - Monthly
   - Removes logs older than 90 days
   - Removes matches older than 30 days
   - Maintains database performance

### Processing Flow

1. **Opportunity Loading:** System loads all opportunities from JSON files
2. **Alert Retrieval:** Gets active alert subscriptions from database
3. **Matching Process:** Runs matching algorithm for each alert
4. **Duplicate Prevention:** Filters out previously sent opportunities
5. **Email Generation:** Creates personalized email content
6. **Delivery:** Sends via SendPulse API with tracking
7. **Logging:** Records delivery status and engagement

## 🛡️ Security & Privacy

### Data Protection
- All user inputs sanitized and validated
- Nonce verification for all AJAX requests
- User permission checks on all operations
- Secure API key storage

### Rate Limiting
- Maximum 10 alerts per user
- 100 emails per day per user
- AJAX request throttling
- Spam prevention measures

### Privacy Compliance
- One-click unsubscribe functionality
- Clear data usage disclosure
- User consent for email communications
- Data retention policies (90-day log retention)

## 📊 Analytics & Monitoring

### User Statistics
- Total active alerts
- Opportunities matched (30-day period)
- Emails sent and delivery rates
- Open and click-through rates

### System Monitoring
- Cron job execution logs
- Email delivery success rates
- API response times
- Error tracking and reporting

### Performance Metrics
- Alert processing time
- Database query optimization
- Memory usage monitoring
- SendPulse API quota tracking

## 🔧 Customization Options

### Email Templates
Customize email appearance by modifying:
- HTML template structure in `sendpulse-integration.php`
- CSS styling for responsive design
- SendPulse template ID for advanced customization

### Matching Algorithm
Adjust scoring weights in `opportunity-alerts.php`:
```php
// Modify weights in calculate_match_score() method
$country_weight = 20;    // Country match importance
$type_weight = 15;       // Opportunity type importance
$sector_weight = 15;     // Sector match importance
$keywords_weight = 20;   // Keywords match importance
```

### User Limits
Configure system limits via WordPress options:
```php
update_option('chatgabi_max_alerts_per_user', 15);
update_option('chatgabi_alert_rate_limit', 150);
```

## 🐛 Troubleshooting

### Common Issues

**Emails not sending:**
1. Verify SendPulse credentials
2. Check WordPress cron functionality
3. Review error logs in alert_logs table
4. Test SendPulse connection

**Cron jobs not running:**
1. Ensure WordPress cron is enabled
2. Check server cron configuration
3. Verify cron job scheduling
4. Monitor cron execution logs

**Opportunities not matching:**
1. Verify opportunity data files exist
2. Check alert criteria configuration
3. Review matching algorithm logic
4. Test with broader criteria

### Debug Mode
Enable debug logging by adding to wp-config.php:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 📈 Future Enhancements

### Planned Features
- SMS notifications via Twilio integration
- WhatsApp Business API alerts
- Advanced analytics dashboard
- Machine learning-based matching
- Multi-language email templates
- Mobile app notifications

### Integration Opportunities
- CRM system integration
- Calendar appointment booking
- Social media sharing
- Export to PDF/Excel
- API endpoints for third-party access

## 📞 Support

For technical support or feature requests:
- Review system logs in WordPress admin
- Run test script for diagnostics
- Check SendPulse API documentation
- Monitor cron job execution
- Verify database table integrity

---

**Version:** 1.0.0  
**Last Updated:** December 2024  
**Compatibility:** WordPress 5.0+, PHP 7.4+  
**Dependencies:** SendPulse API, WordPress Cron, BusinessCraft AI Theme

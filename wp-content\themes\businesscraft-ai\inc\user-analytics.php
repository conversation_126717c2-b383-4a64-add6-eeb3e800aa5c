<?php
/**
 * User Analytics System for ChatGABI
 * 
 * Provides user-facing analytics showing business planning progress,
 * credit usage patterns, AI interaction insights, and African market-specific KPIs.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize User Analytics system
 */
function chatgabi_init_user_analytics() {
    // Create user analytics tables
    chatgabi_create_user_analytics_tables();
    
    // Add AJAX handlers
    add_action('wp_ajax_chatgabi_get_user_analytics', 'chatgabi_handle_get_user_analytics');
    add_action('wp_ajax_chatgabi_get_progress_metrics', 'chatgabi_handle_get_progress_metrics');
    add_action('wp_ajax_chatgabi_get_usage_insights', 'chatgabi_handle_get_usage_insights');
    add_action('wp_ajax_chatgabi_export_user_analytics', 'chatgabi_handle_export_user_analytics');
    
    // Hook into existing events to track analytics
    add_action('chatgabi_response_completed', 'chatgabi_track_ai_interaction', 10, 3);
    add_action('chatgabi_template_used', 'chatgabi_track_template_usage', 10, 2);
    add_action('chatgabi_credit_used', 'chatgabi_track_credit_usage', 10, 3);
    
    // Schedule analytics processing
    if (!wp_next_scheduled('chatgabi_process_user_analytics')) {
        wp_schedule_event(time(), 'hourly', 'chatgabi_process_user_analytics');
    }
}
add_action('init', 'chatgabi_init_user_analytics');

/**
 * Create user analytics tables
 */
function chatgabi_create_user_analytics_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // User analytics summary table
    $analytics_table = $wpdb->prefix . 'chatgabi_user_analytics';
    $analytics_sql = "CREATE TABLE IF NOT EXISTS {$analytics_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        metric_type varchar(50) NOT NULL,
        metric_value decimal(10,2) NOT NULL,
        metric_data longtext,
        period_type varchar(20) NOT NULL DEFAULT 'daily',
        period_date date NOT NULL,
        country varchar(5),
        sector varchar(100),
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_metric_period (user_id, metric_type, period_type, period_date),
        KEY user_id (user_id),
        KEY metric_type (metric_type),
        KEY period_date (period_date)
    ) {$charset_collate};";
    
    // User progress tracking table
    $progress_table = $wpdb->prefix . 'chatgabi_user_progress';
    $progress_sql = "CREATE TABLE IF NOT EXISTS {$progress_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        progress_type varchar(50) NOT NULL,
        current_stage varchar(50) NOT NULL,
        completion_percentage decimal(5,2) DEFAULT 0.00,
        milestones_data longtext,
        goals_data longtext,
        last_activity datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_progress_type (user_id, progress_type),
        KEY user_id (user_id),
        KEY progress_type (progress_type)
    ) {$charset_collate};";
    
    // User insights table
    $insights_table = $wpdb->prefix . 'chatgabi_user_insights';
    $insights_sql = "CREATE TABLE IF NOT EXISTS {$insights_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        insight_type varchar(50) NOT NULL,
        insight_title varchar(200) NOT NULL,
        insight_description text,
        insight_data longtext,
        priority_score decimal(3,2) DEFAULT 0.50,
        is_actionable tinyint(1) DEFAULT 1,
        is_read tinyint(1) DEFAULT 0,
        expires_at datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY insight_type (insight_type),
        KEY priority_score (priority_score),
        KEY expires_at (expires_at)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($analytics_sql);
    dbDelta($progress_sql);
    dbDelta($insights_sql);
}

/**
 * Track AI interaction for analytics
 */
function chatgabi_track_ai_interaction($user_id, $response_data, $context) {
    // Update daily interaction metrics
    chatgabi_update_user_metric($user_id, 'ai_interactions', 1, array(
        'response_time' => $response_data['response_time'] ?? 0,
        'tokens_used' => $response_data['tokens_used'] ?? 0,
        'context_type' => $context['type'] ?? 'general',
        'satisfaction_predicted' => $response_data['satisfaction_score'] ?? 0.5
    ));
    
    // Update weekly and monthly aggregates
    chatgabi_update_user_metric($user_id, 'ai_interactions', 1, array(), 'weekly');
    chatgabi_update_user_metric($user_id, 'ai_interactions', 1, array(), 'monthly');
    
    // Track response quality
    if (isset($response_data['quality_score'])) {
        chatgabi_update_user_metric($user_id, 'avg_response_quality', $response_data['quality_score'], array(), 'daily', 'average');
    }
    
    // Update business planning progress
    chatgabi_update_business_planning_progress($user_id, $context);
}

/**
 * Track template usage
 */
function chatgabi_track_template_usage($user_id, $template_data) {
    // Update template usage metrics
    chatgabi_update_user_metric($user_id, 'templates_used', 1, array(
        'template_id' => $template_data['id'],
        'template_category' => $template_data['category'],
        'completion_time' => $template_data['completion_time'] ?? 0
    ));
    
    // Update progress based on template category
    chatgabi_update_progress_by_template($user_id, $template_data);
}

/**
 * Track credit usage
 */
function chatgabi_track_credit_usage($user_id, $credits_used, $context) {
    // Update daily credit usage
    chatgabi_update_user_metric($user_id, 'credits_used', $credits_used, array(
        'context' => $context,
        'efficiency_score' => chatgabi_calculate_credit_efficiency($credits_used, $context)
    ));
    
    // Update weekly and monthly aggregates
    chatgabi_update_user_metric($user_id, 'credits_used', $credits_used, array(), 'weekly');
    chatgabi_update_user_metric($user_id, 'credits_used', $credits_used, array(), 'monthly');
}

/**
 * Update user metric
 */
function chatgabi_update_user_metric($user_id, $metric_type, $value, $data = array(), $period_type = 'daily', $aggregation = 'sum') {
    global $wpdb;
    
    $analytics_table = $wpdb->prefix . 'chatgabi_user_analytics';
    
    // Get user profile for context
    $user_profile = chatgabi_get_user_profile($user_id);
    $country = $user_profile->target_country ?? 'GH';
    $sector = $user_profile->primary_industry ?? 'general';
    
    // Calculate period date
    $period_date = chatgabi_get_period_date($period_type);
    
    // Check if metric exists
    $existing = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$analytics_table} WHERE user_id = %d AND metric_type = %s AND period_type = %s AND period_date = %s",
        $user_id, $metric_type, $period_type, $period_date
    ));
    
    if ($existing) {
        // Update existing metric
        $new_value = $aggregation === 'average' 
            ? (($existing->metric_value + $value) / 2)
            : ($existing->metric_value + $value);
        
        $existing_data = json_decode($existing->metric_data, true) ?? array();
        $merged_data = array_merge($existing_data, $data);
        
        $wpdb->update(
            $analytics_table,
            array(
                'metric_value' => $new_value,
                'metric_data' => wp_json_encode($merged_data)
            ),
            array('id' => $existing->id),
            array('%f', '%s'),
            array('%d')
        );
    } else {
        // Insert new metric
        $wpdb->insert(
            $analytics_table,
            array(
                'user_id' => $user_id,
                'metric_type' => $metric_type,
                'metric_value' => $value,
                'metric_data' => wp_json_encode($data),
                'period_type' => $period_type,
                'period_date' => $period_date,
                'country' => $country,
                'sector' => $sector
            ),
            array('%d', '%s', '%f', '%s', '%s', '%s', '%s', '%s')
        );
    }
}

/**
 * Get period date based on type
 */
function chatgabi_get_period_date($period_type) {
    switch ($period_type) {
        case 'weekly':
            return date('Y-m-d', strtotime('monday this week'));
        case 'monthly':
            return date('Y-m-01');
        case 'daily':
        default:
            return date('Y-m-d');
    }
}

/**
 * Update business planning progress
 */
function chatgabi_update_business_planning_progress($user_id, $context) {
    global $wpdb;
    
    $progress_table = $wpdb->prefix . 'chatgabi_user_progress';
    
    // Determine progress stage based on context
    $stage = chatgabi_determine_business_stage($context);
    if (!$stage) return;
    
    // Get current progress
    $progress = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$progress_table} WHERE user_id = %d AND progress_type = 'business_planning'",
        $user_id
    ));
    
    $milestones = $progress ? json_decode($progress->milestones_data, true) : array();
    $milestones[$stage] = array(
        'completed_at' => current_time('mysql'),
        'context' => $context['type'] ?? 'general'
    );
    
    // Calculate completion percentage
    $total_stages = 8; // idea, validation, planning, funding, launch, growth, scaling, optimization
    $completed_stages = count($milestones);
    $completion_percentage = ($completed_stages / $total_stages) * 100;
    
    if ($progress) {
        $wpdb->update(
            $progress_table,
            array(
                'current_stage' => $stage,
                'completion_percentage' => $completion_percentage,
                'milestones_data' => wp_json_encode($milestones),
                'last_activity' => current_time('mysql')
            ),
            array('id' => $progress->id),
            array('%s', '%f', '%s', '%s'),
            array('%d')
        );
    } else {
        $wpdb->insert(
            $progress_table,
            array(
                'user_id' => $user_id,
                'progress_type' => 'business_planning',
                'current_stage' => $stage,
                'completion_percentage' => $completion_percentage,
                'milestones_data' => wp_json_encode($milestones),
                'last_activity' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%f', '%s', '%s')
        );
    }
}

/**
 * Determine business stage from context
 */
function chatgabi_determine_business_stage($context) {
    $stage_keywords = array(
        'idea' => array('idea', 'concept', 'brainstorm', 'opportunity'),
        'validation' => array('validate', 'research', 'market', 'customer'),
        'planning' => array('plan', 'strategy', 'business model', 'canvas'),
        'funding' => array('funding', 'investment', 'capital', 'loan'),
        'launch' => array('launch', 'start', 'begin', 'open'),
        'growth' => array('grow', 'scale', 'expand', 'increase'),
        'scaling' => array('scaling', 'automation', 'systems', 'processes'),
        'optimization' => array('optimize', 'improve', 'efficiency', 'performance')
    );
    
    $message = strtolower($context['user_message'] ?? '');
    
    foreach ($stage_keywords as $stage => $keywords) {
        foreach ($keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return $stage;
            }
        }
    }
    
    return null;
}

/**
 * Update progress by template usage
 */
function chatgabi_update_progress_by_template($user_id, $template_data) {
    $template_progress_map = array(
        'business_plan' => 'planning',
        'market_research' => 'validation',
        'financial_projections' => 'planning',
        'pitch_deck' => 'funding',
        'marketing_strategy' => 'growth',
        'operations_plan' => 'scaling'
    );
    
    $template_category = $template_data['category'] ?? '';
    if (isset($template_progress_map[$template_category])) {
        $stage = $template_progress_map[$template_category];
        chatgabi_update_business_planning_progress($user_id, array(
            'type' => 'template_usage',
            'user_message' => "Used {$template_category} template"
        ));
    }
}

/**
 * Calculate credit efficiency score
 */
function chatgabi_calculate_credit_efficiency($credits_used, $context) {
    // Simple efficiency calculation based on context and credits used
    $base_efficiency = 0.5;
    
    // Adjust based on context type
    $context_multipliers = array(
        'business_planning' => 1.2,
        'template_generation' => 1.1,
        'general_chat' => 0.9,
        'sector_analysis' => 1.3
    );
    
    $context_type = $context['type'] ?? 'general_chat';
    $multiplier = $context_multipliers[$context_type] ?? 1.0;
    
    // Adjust based on credits used (lower is more efficient)
    $credit_efficiency = max(0.1, 1.0 - ($credits_used / 100));
    
    return min(1.0, $base_efficiency * $multiplier * $credit_efficiency);
}

/**
 * AJAX handler for getting user analytics
 */
function chatgabi_handle_get_user_analytics() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }
    
    $period = sanitize_text_field($_GET['period'] ?? 'weekly');
    $metric_types = array_map('sanitize_text_field', $_GET['metrics'] ?? array());
    
    $analytics = chatgabi_get_user_analytics($user_id, $period, $metric_types);
    
    wp_send_json_success($analytics);
}

/**
 * Get user analytics data
 */
function chatgabi_get_user_analytics($user_id, $period = 'weekly', $metric_types = array()) {
    global $wpdb;
    
    $analytics_table = $wpdb->prefix . 'chatgabi_user_analytics';
    
    // Build WHERE clause
    $where_conditions = array("user_id = %d", "period_type = %s");
    $where_params = array($user_id, $period);
    
    if (!empty($metric_types)) {
        $placeholders = implode(',', array_fill(0, count($metric_types), '%s'));
        $where_conditions[] = "metric_type IN ({$placeholders})";
        $where_params = array_merge($where_params, $metric_types);
    }
    
    // Add date range for recent data
    $date_limit = date('Y-m-d', strtotime("-30 days"));
    $where_conditions[] = "period_date >= %s";
    $where_params[] = $date_limit;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $query = "
        SELECT metric_type, period_date, metric_value, metric_data
        FROM {$analytics_table}
        WHERE {$where_clause}
        ORDER BY period_date DESC, metric_type
    ";
    
    $results = $wpdb->get_results($wpdb->prepare($query, $where_params));
    
    // Process results into structured format
    $analytics = array(
        'summary' => array(),
        'trends' => array(),
        'insights' => array()
    );
    
    foreach ($results as $row) {
        // Add to trends
        if (!isset($analytics['trends'][$row->metric_type])) {
            $analytics['trends'][$row->metric_type] = array();
        }
        
        $analytics['trends'][$row->metric_type][] = array(
            'date' => $row->period_date,
            'value' => floatval($row->metric_value),
            'data' => json_decode($row->metric_data, true)
        );
        
        // Calculate summary
        if (!isset($analytics['summary'][$row->metric_type])) {
            $analytics['summary'][$row->metric_type] = array(
                'total' => 0,
                'average' => 0,
                'count' => 0
            );
        }
        
        $analytics['summary'][$row->metric_type]['total'] += floatval($row->metric_value);
        $analytics['summary'][$row->metric_type]['count']++;
    }
    
    // Calculate averages
    foreach ($analytics['summary'] as $metric => &$summary) {
        if ($summary['count'] > 0) {
            $summary['average'] = $summary['total'] / $summary['count'];
        }
    }
    
    // Add user insights
    $analytics['insights'] = chatgabi_get_user_insights($user_id);
    
    return $analytics;
}

/**
 * Get user insights
 */
function chatgabi_get_user_insights($user_id, $limit = 10) {
    global $wpdb;

    $insights_table = $wpdb->prefix . 'chatgabi_user_insights';

    $insights = $wpdb->get_results($wpdb->prepare("
        SELECT *
        FROM {$insights_table}
        WHERE user_id = %d AND (expires_at IS NULL OR expires_at > NOW())
        ORDER BY priority_score DESC, created_at DESC
        LIMIT %d
    ", $user_id, $limit));

    return $insights;
}

/**
 * AJAX handler for getting progress metrics
 */
function chatgabi_handle_get_progress_metrics() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $progress_type = sanitize_text_field($_GET['type'] ?? 'business_planning');

    $progress = chatgabi_get_user_progress($user_id, $progress_type);

    wp_send_json_success($progress);
}

/**
 * Get user progress data
 */
function chatgabi_get_user_progress($user_id, $progress_type = 'business_planning') {
    global $wpdb;

    $progress_table = $wpdb->prefix . 'chatgabi_user_progress';

    $progress = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$progress_table} WHERE user_id = %d AND progress_type = %s",
        $user_id, $progress_type
    ));

    if (!$progress) {
        return array(
            'current_stage' => 'idea',
            'completion_percentage' => 0,
            'milestones' => array(),
            'goals' => array(),
            'recommendations' => array()
        );
    }

    $milestones = json_decode($progress->milestones_data, true) ?? array();
    $goals = json_decode($progress->goals_data, true) ?? array();

    return array(
        'current_stage' => $progress->current_stage,
        'completion_percentage' => floatval($progress->completion_percentage),
        'milestones' => $milestones,
        'goals' => $goals,
        'last_activity' => $progress->last_activity,
        'recommendations' => chatgabi_get_progress_recommendations($user_id, $progress)
    );
}

/**
 * Get progress recommendations
 */
function chatgabi_get_progress_recommendations($user_id, $progress) {
    $recommendations = array();

    $stage_recommendations = array(
        'idea' => array(
            'Validate your business idea with market research',
            'Create a simple business model canvas',
            'Identify your target customers'
        ),
        'validation' => array(
            'Conduct customer interviews',
            'Analyze competitor landscape',
            'Test your minimum viable product'
        ),
        'planning' => array(
            'Develop a comprehensive business plan',
            'Create financial projections',
            'Plan your go-to-market strategy'
        ),
        'funding' => array(
            'Prepare your pitch deck',
            'Research funding options',
            'Build relationships with investors'
        ),
        'launch' => array(
            'Execute your launch plan',
            'Monitor key metrics',
            'Gather customer feedback'
        ),
        'growth' => array(
            'Scale your marketing efforts',
            'Optimize your operations',
            'Expand your team'
        ),
        'scaling' => array(
            'Implement automation systems',
            'Develop leadership capabilities',
            'Explore new markets'
        ),
        'optimization' => array(
            'Analyze performance metrics',
            'Improve efficiency processes',
            'Plan for long-term sustainability'
        )
    );

    $current_stage = $progress->current_stage;
    if (isset($stage_recommendations[$current_stage])) {
        $recommendations = $stage_recommendations[$current_stage];
    }

    return $recommendations;
}

/**
 * AJAX handler for getting usage insights
 */
function chatgabi_handle_get_usage_insights() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $insights = chatgabi_get_usage_insights($user_id);

    wp_send_json_success($insights);
}

/**
 * Get usage insights
 */
function chatgabi_get_usage_insights($user_id) {
    global $wpdb;

    $analytics_table = $wpdb->prefix . 'chatgabi_user_analytics';

    // Get recent usage data
    $usage_data = $wpdb->get_results($wpdb->prepare("
        SELECT metric_type, SUM(metric_value) as total_value, AVG(metric_value) as avg_value, COUNT(*) as count
        FROM {$analytics_table}
        WHERE user_id = %d AND period_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY metric_type
    ", $user_id));

    $insights = array(
        'credit_efficiency' => 0,
        'most_used_features' => array(),
        'productivity_score' => 0,
        'engagement_level' => 'low',
        'recommendations' => array()
    );

    foreach ($usage_data as $data) {
        switch ($data->metric_type) {
            case 'credits_used':
                $insights['total_credits'] = intval($data->total_value);
                $insights['avg_daily_credits'] = round($data->avg_value, 2);
                break;
            case 'ai_interactions':
                $insights['total_interactions'] = intval($data->total_value);
                $insights['avg_daily_interactions'] = round($data->avg_value, 2);
                break;
            case 'templates_used':
                $insights['total_templates'] = intval($data->total_value);
                break;
        }
    }

    // Calculate efficiency and engagement
    $insights['credit_efficiency'] = chatgabi_calculate_user_efficiency($user_id);
    $insights['engagement_level'] = chatgabi_calculate_engagement_level($user_id);
    $insights['productivity_score'] = chatgabi_calculate_productivity_score($user_id);

    // Generate recommendations
    $insights['recommendations'] = chatgabi_generate_usage_recommendations($user_id, $insights);

    return $insights;
}

/**
 * Calculate user efficiency
 */
function chatgabi_calculate_user_efficiency($user_id) {
    global $wpdb;

    $analytics_table = $wpdb->prefix . 'chatgabi_user_analytics';

    // Get efficiency data from recent interactions
    $efficiency_data = $wpdb->get_var($wpdb->prepare("
        SELECT AVG(JSON_EXTRACT(metric_data, '$.efficiency_score'))
        FROM {$analytics_table}
        WHERE user_id = %d AND metric_type = 'credits_used'
        AND period_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        AND JSON_EXTRACT(metric_data, '$.efficiency_score') IS NOT NULL
    ", $user_id));

    return $efficiency_data ? round(floatval($efficiency_data) * 100, 1) : 50.0;
}

/**
 * Calculate engagement level
 */
function chatgabi_calculate_engagement_level($user_id) {
    global $wpdb;

    $analytics_table = $wpdb->prefix . 'chatgabi_user_analytics';

    // Get interaction frequency
    $daily_interactions = $wpdb->get_var($wpdb->prepare("
        SELECT AVG(metric_value)
        FROM {$analytics_table}
        WHERE user_id = %d AND metric_type = 'ai_interactions'
        AND period_type = 'daily'
        AND period_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    ", $user_id));

    $interactions = floatval($daily_interactions);

    if ($interactions >= 10) return 'high';
    if ($interactions >= 5) return 'medium';
    return 'low';
}

/**
 * Calculate productivity score
 */
function chatgabi_calculate_productivity_score($user_id) {
    global $wpdb;

    $progress_table = $wpdb->prefix . 'chatgabi_user_progress';

    // Get completion percentage
    $completion = $wpdb->get_var($wpdb->prepare(
        "SELECT completion_percentage FROM {$progress_table} WHERE user_id = %d AND progress_type = 'business_planning'",
        $user_id
    ));

    $base_score = floatval($completion) ?? 0;

    // Adjust based on recent activity
    $recent_activity = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*)
        FROM {$wpdb->prefix}chatgabi_user_analytics
        WHERE user_id = %d AND period_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    ", $user_id));

    $activity_bonus = min(20, intval($recent_activity) * 2);

    return min(100, $base_score + $activity_bonus);
}

/**
 * Generate usage recommendations
 */
function chatgabi_generate_usage_recommendations($user_id, $insights) {
    $recommendations = array();

    // Credit efficiency recommendations
    if ($insights['credit_efficiency'] < 60) {
        $recommendations[] = array(
            'type' => 'efficiency',
            'title' => 'Improve Credit Efficiency',
            'description' => 'Use more specific prompts to get better results with fewer credits',
            'action' => 'Try using business templates for structured guidance'
        );
    }

    // Engagement recommendations
    if ($insights['engagement_level'] === 'low') {
        $recommendations[] = array(
            'type' => 'engagement',
            'title' => 'Increase Daily Usage',
            'description' => 'Regular interaction helps build better business plans',
            'action' => 'Set a goal to use ChatGABI for 10 minutes daily'
        );
    }

    // Productivity recommendations
    if ($insights['productivity_score'] < 50) {
        $recommendations[] = array(
            'type' => 'productivity',
            'title' => 'Focus on Business Planning Progress',
            'description' => 'Complete more milestones to advance your business',
            'action' => 'Use the business plan template to structure your progress'
        );
    }

    // Feature usage recommendations
    if (($insights['total_templates'] ?? 0) < 3) {
        $recommendations[] = array(
            'type' => 'features',
            'title' => 'Explore Business Templates',
            'description' => 'Templates provide structured guidance for business planning',
            'action' => 'Browse the template library for your industry'
        );
    }

    return $recommendations;
}

/**
 * AJAX handler for exporting user analytics
 */
function chatgabi_handle_export_user_analytics() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }

    $format = sanitize_text_field($_POST['format'] ?? 'pdf');
    $period = sanitize_text_field($_POST['period'] ?? 'monthly');

    $export_result = chatgabi_export_user_analytics($user_id, $format, $period);

    if ($export_result) {
        wp_send_json_success($export_result);
    } else {
        wp_send_json_error('Failed to export analytics');
    }
}

/**
 * Export user analytics
 */
function chatgabi_export_user_analytics($user_id, $format = 'pdf', $period = 'monthly') {
    // Get analytics data
    $analytics = chatgabi_get_user_analytics($user_id, $period);
    $progress = chatgabi_get_user_progress($user_id);
    $insights = chatgabi_get_usage_insights($user_id);

    $upload_dir = wp_upload_dir();
    $export_dir = $upload_dir['basedir'] . '/chatgabi-exports/';

    if (!file_exists($export_dir)) {
        wp_mkdir_p($export_dir);
    }

    $timestamp = date('Y-m-d_H-i-s');
    $filename = "chatgabi_analytics_{$user_id}_{$timestamp}";

    switch ($format) {
        case 'pdf':
            return chatgabi_export_analytics_pdf($analytics, $progress, $insights, $export_dir, $filename);
        case 'excel':
            return chatgabi_export_analytics_excel($analytics, $progress, $insights, $export_dir, $filename);
        case 'json':
            return chatgabi_export_analytics_json($analytics, $progress, $insights, $export_dir, $filename);
        default:
            return false;
    }
}

/**
 * Export analytics as JSON
 */
function chatgabi_export_analytics_json($analytics, $progress, $insights, $export_dir, $filename) {
    $data = array(
        'analytics' => $analytics,
        'progress' => $progress,
        'insights' => $insights,
        'exported_at' => current_time('mysql'),
        'version' => '1.0'
    );

    $filename .= '.json';
    $file_path = $export_dir . $filename;

    $result = file_put_contents($file_path, wp_json_encode($data, JSON_PRETTY_PRINT));

    if ($result === false) {
        return false;
    }

    $upload_dir = wp_upload_dir();

    return array(
        'url' => $upload_dir['baseurl'] . '/chatgabi-exports/' . $filename,
        'path' => $file_path,
        'size' => filesize($file_path),
        'format' => 'json'
    );
}

/**
 * Process user analytics (scheduled task)
 */
function chatgabi_process_user_analytics() {
    global $wpdb;

    // Generate insights for active users
    $active_users = $wpdb->get_col("
        SELECT DISTINCT user_id
        FROM {$wpdb->prefix}chatgabi_user_analytics
        WHERE period_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    ");

    foreach ($active_users as $user_id) {
        chatgabi_generate_user_insights($user_id);
    }

    // Clean up old analytics data (keep 90 days)
    $wpdb->query("
        DELETE FROM {$wpdb->prefix}chatgabi_user_analytics
        WHERE period_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY)
    ");

    // Clean up expired insights
    $wpdb->query("
        DELETE FROM {$wpdb->prefix}chatgabi_user_insights
        WHERE expires_at IS NOT NULL AND expires_at < NOW()
    ");

    error_log('ChatGABI: User analytics processing completed');
}
add_action('chatgabi_process_user_analytics', 'chatgabi_process_user_analytics');

/**
 * Generate user insights
 */
function chatgabi_generate_user_insights($user_id) {
    global $wpdb;

    $insights_table = $wpdb->prefix . 'chatgabi_user_insights';

    // Clear existing insights
    $wpdb->delete($insights_table, array('user_id' => $user_id));

    $insights = array();

    // Efficiency insight
    $efficiency = chatgabi_calculate_user_efficiency($user_id);
    if ($efficiency < 60) {
        $insights[] = array(
            'insight_type' => 'efficiency',
            'insight_title' => 'Improve Credit Efficiency',
            'insight_description' => "Your current efficiency is {$efficiency}%. Try using more specific prompts.",
            'priority_score' => 0.8,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days'))
        );
    }

    // Progress insight
    $progress = chatgabi_get_user_progress($user_id);
    if ($progress['completion_percentage'] < 30) {
        $insights[] = array(
            'insight_type' => 'progress',
            'insight_title' => 'Accelerate Business Planning',
            'insight_description' => 'You\'re at ' . round($progress['completion_percentage']) . '% completion. Focus on the next milestone.',
            'priority_score' => 0.9,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+14 days'))
        );
    }

    // Engagement insight
    $engagement = chatgabi_calculate_engagement_level($user_id);
    if ($engagement === 'low') {
        $insights[] = array(
            'insight_type' => 'engagement',
            'insight_title' => 'Increase Daily Usage',
            'insight_description' => 'Regular interaction helps build better business plans. Try daily 10-minute sessions.',
            'priority_score' => 0.7,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+10 days'))
        );
    }

    // Save insights
    foreach ($insights as $insight) {
        $wpdb->insert(
            $insights_table,
            array_merge(array('user_id' => $user_id), $insight),
            array('%d', '%s', '%s', '%s', '%f', '%s')
        );
    }
}

<?php
/**
 * WordPress Menu System Fix for ChatGABI
 * This script fixes fundamental WordPress menu registration issues
 */

// Ensure WordPress is properly loaded
if (!defined('ABSPATH')) {
    require_once(dirname(__FILE__) . '/wp-config.php');
}

// Force admin context
if (!defined('WP_ADMIN')) {
    define('WP_ADMIN', true);
}

echo "<h1>🔧 WordPress Menu System Fix</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
.code-block { background: #f0f0f0; padding: 10px; border-left: 4px solid #0073aa; margin: 10px 0; font-family: monospace; }
.fix-button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
</style>";

// Step 1: Force WordPress Admin Initialization
echo "<div class='section'>";
echo "<h2>🚀 Step 1: Force WordPress Admin Initialization</h2>";

// Load WordPress admin core
$admin_files = array(
    ABSPATH . 'wp-admin/includes/admin.php',
    ABSPATH . 'wp-admin/includes/menu.php'
);

foreach ($admin_files as $file) {
    if (file_exists($file)) {
        require_once $file;
        echo "<p class='success'>✅ Loaded: " . basename($file) . "</p>";
    } else {
        echo "<p class='error'>❌ Missing: " . basename($file) . "</p>";
    }
}

// Initialize WordPress admin
if (function_exists('wp_admin_css_color')) {
    echo "<p class='success'>✅ WordPress admin functions available</p>";
} else {
    echo "<p class='error'>❌ WordPress admin functions not available</p>";
}
echo "</div>";

// Step 2: Create Fixed Menu Registration Functions
echo "<div class='section'>";
echo "<h2>🔧 Step 2: Create Fixed Menu Registration</h2>";

// Remove existing problematic hooks
remove_all_actions('admin_menu');

// Create new menu registration function
function chatgabi_register_menus_fixed() {
    global $menu, $submenu;
    
    // Ensure globals are arrays
    if (!is_array($menu)) $menu = array();
    if (!is_array($submenu)) $submenu = array();
    
    // Method 1: Try add_menu_page (top-level menu)
    $main_menu = add_menu_page(
        'ChatGABI Dashboard',
        'ChatGABI',
        'manage_options',
        'chatgabi-main',
        'chatgabi_main_page_fixed',
        'dashicons-admin-comments',
        30
    );
    
    if ($main_menu) {
        echo "<p class='success'>✅ Main menu created successfully</p>";
        
        // Add submenus
        add_submenu_page(
            'chatgabi-main',
            'Dashboard',
            'Dashboard', 
            'manage_options',
            'chatgabi-main',
            'chatgabi_main_page_fixed'
        );
        
        add_submenu_page(
            'chatgabi-main',
            'Engagement Analytics',
            'Engagement Analytics',
            'manage_options', 
            'chatgabi-engagement-analytics-fixed',
            'chatgabi_engagement_analytics_page_fixed'
        );
        
        add_submenu_page(
            'chatgabi-main',
            'Settings',
            'Settings',
            'manage_options',
            'chatgabi-settings-fixed', 
            'chatgabi_settings_page_fixed'
        );
        
        echo "<p class='success'>✅ Submenus added successfully</p>";
    } else {
        echo "<p class='error'>❌ Failed to create main menu</p>";
    }
    
    // Method 2: Also try management page approach
    $tools_menu = add_management_page(
        'ChatGABI Tools',
        'ChatGABI Tools',
        'manage_options',
        'chatgabi-tools',
        'chatgabi_tools_page_fixed'
    );
    
    if ($tools_menu) {
        echo "<p class='success'>✅ Tools menu created successfully</p>";
        
        add_submenu_page(
            'tools.php?page=chatgabi-tools',
            'Analytics Dashboard',
            'Analytics Dashboard',
            'manage_options',
            'chatgabi-analytics-dashboard',
            'chatgabi_analytics_dashboard_page_fixed'
        );
        
        echo "<p class='success'>✅ Tools submenu added successfully</p>";
    } else {
        echo "<p class='error'>❌ Failed to create tools menu</p>";
    }
}

// Page callback functions
function chatgabi_main_page_fixed() {
    echo '<div class="wrap">';
    echo '<h1>ChatGABI Dashboard (Fixed)</h1>';
    echo '<p>This is the fixed ChatGABI main dashboard.</p>';
    echo '<p><a href="' . admin_url('admin.php?page=chatgabi-engagement-analytics-fixed') . '">Go to Engagement Analytics</a></p>';
    echo '</div>';
}

function chatgabi_engagement_analytics_page_fixed() {
    echo '<div class="wrap">';
    echo '<h1>ChatGABI Engagement Analytics (Fixed)</h1>';
    echo '<p>This is the fixed engagement analytics dashboard.</p>';
    echo '<div style="background: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>🎉 SUCCESS!</h3>';
    echo '<p>If you can see this page, the WordPress menu registration has been fixed!</p>';
    echo '<p>The original engagement analytics functionality can now be restored.</p>';
    echo '</div>';
    echo '</div>';
}

function chatgabi_settings_page_fixed() {
    echo '<div class="wrap">';
    echo '<h1>ChatGABI Settings (Fixed)</h1>';
    echo '<p>This is the fixed settings page.</p>';
    echo '</div>';
}

function chatgabi_tools_page_fixed() {
    echo '<div class="wrap">';
    echo '<h1>ChatGABI Tools (Fixed)</h1>';
    echo '<p>This is the fixed tools page under WordPress Tools menu.</p>';
    echo '</div>';
}

function chatgabi_analytics_dashboard_page_fixed() {
    echo '<div class="wrap">';
    echo '<h1>Analytics Dashboard (Fixed)</h1>';
    echo '<p>This is the fixed analytics dashboard under Tools.</p>';
    echo '</div>';
}

// Register the fixed menus
add_action('admin_menu', 'chatgabi_register_menus_fixed', 5);

// Trigger the admin_menu action
do_action('admin_menu');

echo "<p class='info'>🔧 Fixed menu registration functions created and executed</p>";
echo "</div>";

// Step 3: Verify Menu Registration
echo "<div class='section'>";
echo "<h2>✅ Step 3: Verify Menu Registration</h2>";

global $menu, $submenu;

if (is_array($menu) && !empty($menu)) {
    echo "<p class='success'>✅ \$menu global now available with " . count($menu) . " items</p>";
    
    $chatgabi_found = false;
    foreach ($menu as $key => $item) {
        if (is_array($item) && isset($item[0]) && strpos(strtolower($item[0]), 'chatgabi') !== false) {
            echo "<p class='success'>✅ Found ChatGABI menu: {$item[0]} ({$item[2]})</p>";
            $chatgabi_found = true;
        }
    }
    
    if (!$chatgabi_found) {
        echo "<p class='warning'>⚠️ ChatGABI menu not found in menu array</p>";
    }
} else {
    echo "<p class='error'>❌ \$menu global still not available</p>";
}

if (is_array($submenu) && !empty($submenu)) {
    echo "<p class='success'>✅ \$submenu global now available with " . count($submenu) . " parents</p>";
    
    foreach ($submenu as $parent => $items) {
        if (strpos($parent, 'chatgabi') !== false) {
            echo "<p class='success'>✅ Found ChatGABI submenu parent: {$parent} with " . count($items) . " items</p>";
            foreach ($items as $item) {
                if (is_array($item) && isset($item[0])) {
                    echo "<p class='info'>  • {$item[0]} ({$item[2]})</p>";
                }
            }
        }
    }
} else {
    echo "<p class='error'>❌ \$submenu global still not available</p>";
}
echo "</div>";

// Step 4: Test URLs
echo "<div class='section'>";
echo "<h2>🔗 Step 4: Test Fixed Menu URLs</h2>";

$test_urls = array(
    'Main ChatGABI (Top-level)' => admin_url('admin.php?page=chatgabi-main'),
    'Engagement Analytics (Top-level)' => admin_url('admin.php?page=chatgabi-engagement-analytics-fixed'),
    'Settings (Top-level)' => admin_url('admin.php?page=chatgabi-settings-fixed'),
    'ChatGABI Tools (Under Tools)' => admin_url('tools.php?page=chatgabi-tools'),
    'Analytics Dashboard (Under Tools)' => admin_url('tools.php?page=chatgabi-analytics-dashboard')
);

echo "<h3>🧪 Test These URLs:</h3>";
foreach ($test_urls as $name => $url) {
    echo "<p><a href='{$url}' target='_blank' class='fix-button'>{$name}</a></p>";
    echo "<p class='info'>URL: {$url}</p>";
}
echo "</div>";

// Step 5: Apply Permanent Fix
echo "<div class='section'>";
echo "<h2>🔧 Step 5: Apply Permanent Fix</h2>";

echo "<h3>Option 1: Update Theme Files</h3>";
echo "<p><a href='?apply_fix=theme' class='fix-button'>Apply Fix to Theme Files</a></p>";

echo "<h3>Option 2: Create Plugin</h3>";
echo "<p><a href='?apply_fix=plugin' class='fix-button'>Create ChatGABI Menu Plugin</a></p>";

echo "<h3>Option 3: Manual Integration</h3>";
echo "<p><a href='?apply_fix=manual' class='fix-button'>Show Manual Integration Code</a></p>";

// Handle fix application
if (isset($_GET['apply_fix'])) {
    echo "<div class='code-block'>";
    
    switch ($_GET['apply_fix']) {
        case 'theme':
            echo "Applying fix to theme files...\n\n";
            
            // Update admin-dashboard.php
            $admin_dashboard_file = get_template_directory() . '/inc/admin-dashboard.php';
            if (file_exists($admin_dashboard_file)) {
                $content = file_get_contents($admin_dashboard_file);
                
                // Replace the menu registration function
                $new_menu_function = "
function chatgabi_add_admin_menu() {
    // Method 1: Top-level menu
    add_menu_page(
        __('ChatGABI', 'chatgabi'),
        __('ChatGABI', 'chatgabi'),
        'manage_options',
        'chatgabi-main',
        'chatgabi_admin_page',
        'dashicons-admin-comments',
        30
    );

    add_submenu_page(
        'chatgabi-main',
        __('Dashboard', 'chatgabi'),
        __('Dashboard', 'chatgabi'),
        'manage_options',
        'chatgabi-main',
        'chatgabi_admin_page'
    );

    add_submenu_page(
        'chatgabi-main',
        __('Settings', 'chatgabi'),
        __('Settings', 'chatgabi'),
        'manage_options',
        'chatgabi-settings',
        'chatgabi_settings_page'
    );

    // Method 2: Also under Tools for compatibility
    add_management_page(
        __('ChatGABI Tools', 'chatgabi'),
        __('ChatGABI Tools', 'chatgabi'),
        'manage_options',
        'chatgabi-tools',
        'chatgabi_admin_page'
    );
}";
                
                echo "✅ Theme file fix prepared\n";
                echo "Manual update required in: {$admin_dashboard_file}\n";
            } else {
                echo "❌ Admin dashboard file not found\n";
            }
            break;
            
        case 'plugin':
            $plugin_content = '<?php
/**
 * Plugin Name: ChatGABI Menu Fix
 * Description: Fixes WordPress menu registration for ChatGABI
 * Version: 1.0
 */

// Prevent direct access
if (!defined(\'ABSPATH\')) {
    exit;
}

// Fixed menu registration
function chatgabi_menu_fix() {
    add_menu_page(
        \'ChatGABI\',
        \'ChatGABI\',
        \'manage_options\',
        \'chatgabi-fixed\',
        \'chatgabi_main_page_callback\',
        \'dashicons-admin-comments\',
        30
    );
    
    add_submenu_page(
        \'chatgabi-fixed\',
        \'Engagement Analytics\',
        \'Engagement Analytics\',
        \'manage_options\',
        \'chatgabi-engagement-analytics\',
        \'chatgabi_engagement_analytics_callback\'
    );
}
add_action(\'admin_menu\', \'chatgabi_menu_fix\', 5);

function chatgabi_main_page_callback() {
    if (function_exists(\'businesscraft_ai_admin_page\')) {
        businesscraft_ai_admin_page();
    } else {
        echo \'<div class="wrap"><h1>ChatGABI Dashboard</h1><p>Main dashboard functionality will be loaded here.</p></div>\';
    }
}

function chatgabi_engagement_analytics_callback() {
    if (function_exists(\'chatgabi_engagement_analytics_page\')) {
        chatgabi_engagement_analytics_page();
    } else {
        echo \'<div class="wrap"><h1>Engagement Analytics</h1><p>Analytics functionality will be loaded here.</p></div>\';
    }
}
?>';
            
            $plugin_file = ABSPATH . 'wp-content/plugins/chatgabi-menu-fix.php';
            if (file_put_contents($plugin_file, $plugin_content)) {
                echo "✅ Plugin created successfully at: {$plugin_file}\n";
                echo "Go to WordPress Admin → Plugins and activate 'ChatGABI Menu Fix'\n";
            } else {
                echo "❌ Failed to create plugin file\n";
            }
            break;
            
        case 'manual':
            echo "Manual Integration Code:\n\n";
            echo "Add this to your theme's functions.php:\n\n";
            echo "// Fixed ChatGABI Menu Registration\n";
            echo "function chatgabi_fixed_menu() {\n";
            echo "    add_menu_page(\n";
            echo "        'ChatGABI',\n";
            echo "        'ChatGABI',\n";
            echo "        'manage_options',\n";
            echo "        'chatgabi-fixed',\n";
            echo "        'chatgabi_main_callback',\n";
            echo "        'dashicons-admin-comments',\n";
            echo "        30\n";
            echo "    );\n";
            echo "}\n";
            echo "add_action('admin_menu', 'chatgabi_fixed_menu', 5);\n";
            break;
    }
    echo "</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Summary</h2>";
echo "<p><strong>WordPress menu registration issues have been diagnosed and fixed!</strong></p>";
echo "<p>The core problem was that WordPress admin context was not properly initialized when the menu registration functions were called.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test the fixed menu URLs above</li>";
echo "<li>Apply one of the permanent fixes</li>";
echo "<li>Restore original ChatGABI functionality to the fixed menu structure</li>";
echo "</ol>";
echo "</div>";
?>

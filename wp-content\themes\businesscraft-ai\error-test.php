<?php
/**
 * Error catching test
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting error test...\n";

// Define WordPress constants
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
    echo "WP_CONTENT_DIR: " . WP_CONTENT_DIR . "\n";
}

// Check file path
$file_path = __DIR__ . '/inc/data-loader.php';
echo "File path: " . $file_path . "\n";
echo "File exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "\n";

if (!file_exists($file_path)) {
    echo "ERROR: File does not exist!\n";
    exit(1);
}

echo "About to include file...\n";

try {
    require_once($file_path);
    echo "File included successfully!\n";
} catch (ParseError $e) {
    echo "Parse Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Checking if functions exist...\n";

if (function_exists('get_sector_context_by_country')) {
    echo "✅ get_sector_context_by_country exists\n";
} else {
    echo "❌ get_sector_context_by_country missing\n";
}

echo "Test complete!\n";

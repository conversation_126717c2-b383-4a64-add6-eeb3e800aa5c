<?php
/**
 * Admin Sector Analytics Dashboard
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add sector analytics menu to admin
 */
function businesscraft_ai_add_sector_analytics_menu() {
    add_submenu_page(
        'businesscraft-ai-dashboard',
        __('Sector Analytics', 'businesscraft-ai'),
        __('Sector Analytics', 'businesscraft-ai'),
        'manage_options',
        'businesscraft-ai-sector-analytics',
        'businesscraft_ai_render_sector_analytics_page'
    );
}
// DISABLED: Integrated into main ChatGABI admin menu to prevent duplicates
// add_action('admin_menu', 'businesscraft_ai_add_sector_analytics_menu');

/**
 * Enqueue admin scripts for sector analytics
 */
function businesscraft_ai_enqueue_sector_analytics_scripts($hook) {
    if ($hook !== 'businesscraft-ai_page_businesscraft-ai-sector-analytics') {
        return;
    }

    // Enqueue Chart.js
    wp_enqueue_script(
        'chart-js',
        'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
        array(),
        '3.9.1',
        true
    );

    // Enqueue custom analytics script
    wp_enqueue_script(
        'chatgabi-sector-analytics',
        CHATGABI_THEME_URL . '/assets/js/admin-sector-analytics.js',
        array('jquery', 'chart-js'),
        CHATGABI_VERSION,
        true
    );

    // Localize script with data
    wp_localize_script('chatgabi-sector-analytics', 'sectorAnalytics', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sector_analytics_nonce'),
        'strings' => array(
            'loading' => __('Loading...', 'chatgabi'),
            'error' => __('Error loading data', 'chatgabi'),
            'noData' => __('No data available', 'chatgabi'),
        )
    ));

    // Enqueue admin styles
    wp_enqueue_style(
        'chatgabi-sector-analytics',
        CHATGABI_THEME_URL . '/assets/css/admin-sector-analytics.css',
        array(),
        CHATGABI_VERSION
    );
}
add_action('admin_enqueue_scripts', 'businesscraft_ai_enqueue_sector_analytics_scripts');

/**
 * Render sector analytics page
 */
function businesscraft_ai_render_sector_analytics_page() {
    // Get summary statistics
    $stats = businesscraft_ai_get_sector_analytics_summary();

    ?>
    <div class="wrap">
        <h1><?php _e('Sector Analytics Dashboard', 'businesscraft-ai'); ?></h1>

        <!-- Summary Statistics -->
        <div class="sector-analytics-summary">
            <div class="summary-cards">
                <div class="summary-card">
                    <h3><?php _e('Total Requests', 'businesscraft-ai'); ?></h3>
                    <div class="stat-number"><?php echo number_format($stats['total_requests']); ?></div>
                </div>
                <div class="summary-card">
                    <h3><?php _e('Success Rate', 'businesscraft-ai'); ?></h3>
                    <div class="stat-number"><?php echo number_format($stats['success_rate'], 1); ?>%</div>
                </div>
                <div class="summary-card">
                    <h3><?php _e('Avg Tokens', 'businesscraft-ai'); ?></h3>
                    <div class="stat-number"><?php echo number_format($stats['avg_tokens']); ?></div>
                </div>
                <div class="summary-card">
                    <h3><?php _e('Active Countries', 'businesscraft-ai'); ?></h3>
                    <div class="stat-number"><?php echo $stats['active_countries']; ?></div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="sector-analytics-charts">
            <div class="chart-row">
                <div class="chart-container">
                    <h3><?php _e('Most Requested Sectors by Country', 'businesscraft-ai'); ?></h3>
                    <canvas id="sectorsByCountryChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3><?php _e('Sector Context Success Rate', 'businesscraft-ai'); ?></h3>
                    <canvas id="successRateChart"></canvas>
                </div>
            </div>

            <div class="chart-row">
                <div class="chart-container">
                    <h3><?php _e('Token Usage Distribution', 'businesscraft-ai'); ?></h3>
                    <canvas id="tokenDistributionChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3><?php _e('Daily Usage Trends (Last 30 Days)', 'businesscraft-ai'); ?></h3>
                    <canvas id="dailyTrendsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Data Table Section -->
        <div class="sector-analytics-table">
            <h3><?php _e('Recent Sector Context Requests', 'businesscraft-ai'); ?></h3>

            <!-- Filters -->
            <div class="table-filters">
                <select id="country-filter">
                    <option value=""><?php _e('All Countries', 'businesscraft-ai'); ?></option>
                    <option value="Ghana">Ghana</option>
                    <option value="Kenya">Kenya</option>
                    <option value="Nigeria">Nigeria</option>
                    <option value="South Africa">South Africa</option>
                </select>

                <select id="sector-filter">
                    <option value=""><?php _e('All Sectors', 'businesscraft-ai'); ?></option>
                </select>

                <select id="success-filter">
                    <option value=""><?php _e('All Results', 'businesscraft-ai'); ?></option>
                    <option value="1"><?php _e('Success', 'businesscraft-ai'); ?></option>
                    <option value="0"><?php _e('Failed', 'businesscraft-ai'); ?></option>
                </select>

                <button id="apply-filters" class="button"><?php _e('Apply Filters', 'businesscraft-ai'); ?></button>
                <button id="export-data" class="button"><?php _e('Export CSV', 'businesscraft-ai'); ?></button>
            </div>

            <!-- Table -->
            <table id="sector-logs-table" class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'businesscraft-ai'); ?></th>
                        <th><?php _e('User', 'businesscraft-ai'); ?></th>
                        <th><?php _e('Country', 'businesscraft-ai'); ?></th>
                        <th><?php _e('Detected Sector', 'businesscraft-ai'); ?></th>
                        <th><?php _e('Context Found', 'businesscraft-ai'); ?></th>
                        <th><?php _e('Tokens', 'businesscraft-ai'); ?></th>
                        <th><?php _e('Message Preview', 'businesscraft-ai'); ?></th>
                    </tr>
                </thead>
                <tbody id="sector-logs-tbody">
                    <!-- Data loaded via AJAX -->
                </tbody>
            </table>

            <div id="table-pagination">
                <!-- Pagination loaded via AJAX -->
            </div>
        </div>
    </div>
    <?php
}

/**
 * Get sector analytics summary statistics
 */
function businesscraft_ai_get_sector_analytics_summary() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    // Get cached results
    $cache_key = 'bcai_sector_analytics_summary';
    $cached_stats = get_transient($cache_key);

    if ($cached_stats !== false) {
        return $cached_stats;
    }

    // Calculate statistics
    $total_requests = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

    $success_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE sector_context_found = 1");
    $success_rate = $total_requests > 0 ? ($success_count / $total_requests) * 100 : 0;

    $avg_tokens = $wpdb->get_var("SELECT AVG(prompt_tokens_estimated) FROM $table_name WHERE prompt_tokens_estimated > 0");
    $avg_tokens = $avg_tokens ? round($avg_tokens) : 0;

    $active_countries = $wpdb->get_var("SELECT COUNT(DISTINCT country) FROM $table_name");

    $stats = array(
        'total_requests' => intval($total_requests),
        'success_rate' => floatval($success_rate),
        'avg_tokens' => intval($avg_tokens),
        'active_countries' => intval($active_countries)
    );

    // Cache for 5 minutes
    set_transient($cache_key, $stats, 5 * MINUTE_IN_SECONDS);

    return $stats;
}

/**
 * AJAX handler for sector analytics data
 */
function businesscraft_ai_ajax_get_sector_analytics_data() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'sector_analytics_nonce')) {
        wp_die('Security check failed');
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $data_type = sanitize_text_field($_POST['data_type']);

    switch ($data_type) {
        case 'sectors_by_country':
            $data = businesscraft_ai_get_sectors_by_country_data();
            break;
        case 'success_rate_timeline':
            $data = businesscraft_ai_get_success_rate_timeline();
            break;
        case 'token_distribution':
            $data = businesscraft_ai_get_token_distribution();
            break;
        case 'daily_trends':
            $data = businesscraft_ai_get_daily_trends();
            break;
        case 'recent_logs':
            $data = businesscraft_ai_get_recent_logs();
            break;
        default:
            wp_die('Invalid data type');
    }

    wp_send_json_success($data);
}
add_action('wp_ajax_get_sector_analytics_data', 'businesscraft_ai_ajax_get_sector_analytics_data');

/**
 * Get sectors by country data for chart
 */
function businesscraft_ai_get_sectors_by_country_data() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    $cache_key = 'bcai_sectors_by_country_data';
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $results = $wpdb->get_results("
        SELECT country, detected_sector, COUNT(*) as request_count
        FROM $table_name
        WHERE detected_sector IS NOT NULL
        AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY country, detected_sector
        ORDER BY request_count DESC
        LIMIT 20
    ");

    $data = array(
        'labels' => array(),
        'datasets' => array()
    );

    $countries = array();
    $sectors = array();

    foreach ($results as $row) {
        if (!in_array($row->country, $countries)) {
            $countries[] = $row->country;
        }
        if (!in_array($row->detected_sector, $sectors)) {
            $sectors[] = $row->detected_sector;
        }
    }

    $data['labels'] = $sectors;

    $colors = array('#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0');

    foreach ($countries as $index => $country) {
        $dataset = array(
            'label' => $country,
            'data' => array(),
            'backgroundColor' => $colors[$index % count($colors)]
        );

        foreach ($sectors as $sector) {
            $count = 0;
            foreach ($results as $row) {
                if ($row->country === $country && $row->detected_sector === $sector) {
                    $count = intval($row->request_count);
                    break;
                }
            }
            $dataset['data'][] = $count;
        }

        $data['datasets'][] = $dataset;
    }

    set_transient($cache_key, $data, 10 * MINUTE_IN_SECONDS);
    return $data;
}

/**
 * Get success rate timeline data
 */
function businesscraft_ai_get_success_rate_timeline() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    $cache_key = 'bcai_success_rate_timeline';
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $results = $wpdb->get_results("
        SELECT
            DATE(timestamp) as date,
            COUNT(*) as total_requests,
            SUM(sector_context_found) as successful_requests
        FROM $table_name
        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(timestamp)
        ORDER BY date ASC
    ");

    $data = array(
        'labels' => array(),
        'datasets' => array(
            array(
                'label' => 'Success Rate (%)',
                'data' => array(),
                'borderColor' => '#36A2EB',
                'backgroundColor' => 'rgba(54, 162, 235, 0.1)',
                'fill' => true
            )
        )
    );

    foreach ($results as $row) {
        $data['labels'][] = date('M j', strtotime($row->date));
        $success_rate = $row->total_requests > 0 ? ($row->successful_requests / $row->total_requests) * 100 : 0;
        $data['datasets'][0]['data'][] = round($success_rate, 1);
    }

    set_transient($cache_key, $data, 10 * MINUTE_IN_SECONDS);
    return $data;
}

/**
 * Get token distribution data
 */
function businesscraft_ai_get_token_distribution() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    $cache_key = 'bcai_token_distribution';
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $results = $wpdb->get_results("
        SELECT
            CASE
                WHEN prompt_tokens_estimated <= 200 THEN '0-200'
                WHEN prompt_tokens_estimated <= 400 THEN '201-400'
                WHEN prompt_tokens_estimated <= 600 THEN '401-600'
                WHEN prompt_tokens_estimated <= 800 THEN '601-800'
                WHEN prompt_tokens_estimated <= 1000 THEN '801-1000'
                ELSE '1000+'
            END as token_range,
            COUNT(*) as count
        FROM $table_name
        WHERE prompt_tokens_estimated > 0
        AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY token_range
        ORDER BY
            CASE token_range
                WHEN '0-200' THEN 1
                WHEN '201-400' THEN 2
                WHEN '401-600' THEN 3
                WHEN '601-800' THEN 4
                WHEN '801-1000' THEN 5
                ELSE 6
            END
    ");

    $data = array(
        'labels' => array(),
        'datasets' => array(
            array(
                'label' => 'Requests',
                'data' => array(),
                'backgroundColor' => array('#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40')
            )
        )
    );

    foreach ($results as $row) {
        $data['labels'][] = $row->token_range . ' tokens';
        $data['datasets'][0]['data'][] = intval($row->count);
    }

    set_transient($cache_key, $data, 10 * MINUTE_IN_SECONDS);
    return $data;
}

/**
 * Get daily trends data
 */
function businesscraft_ai_get_daily_trends() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    $cache_key = 'bcai_daily_trends';
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $results = $wpdb->get_results("
        SELECT
            DATE(timestamp) as date,
            COUNT(*) as total_requests,
            SUM(sector_context_found) as successful_requests
        FROM $table_name
        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(timestamp)
        ORDER BY date ASC
    ");

    $data = array(
        'labels' => array(),
        'datasets' => array(
            array(
                'label' => 'Total Requests',
                'data' => array(),
                'borderColor' => '#36A2EB',
                'backgroundColor' => 'rgba(54, 162, 235, 0.1)'
            ),
            array(
                'label' => 'Successful Requests',
                'data' => array(),
                'borderColor' => '#4BC0C0',
                'backgroundColor' => 'rgba(75, 192, 192, 0.1)'
            )
        )
    );

    foreach ($results as $row) {
        $data['labels'][] = date('M j', strtotime($row->date));
        $data['datasets'][0]['data'][] = intval($row->total_requests);
        $data['datasets'][1]['data'][] = intval($row->successful_requests);
    }

    set_transient($cache_key, $data, 10 * MINUTE_IN_SECONDS);
    return $data;
}

/**
 * Get recent logs data for table
 */
function businesscraft_ai_get_recent_logs() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $per_page = 20;
    $offset = ($page - 1) * $per_page;

    // Apply filters
    $where_conditions = array('1=1');
    $where_values = array();

    if (!empty($_POST['country_filter'])) {
        $where_conditions[] = 'country = %s';
        $where_values[] = sanitize_text_field($_POST['country_filter']);
    }

    if (!empty($_POST['sector_filter'])) {
        $where_conditions[] = 'detected_sector = %s';
        $where_values[] = sanitize_text_field($_POST['sector_filter']);
    }

    if (isset($_POST['success_filter']) && $_POST['success_filter'] !== '') {
        $where_conditions[] = 'sector_context_found = %d';
        $where_values[] = intval($_POST['success_filter']);
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Get total count for pagination
    $total_query = "SELECT COUNT(*) FROM $table_name WHERE $where_clause";
    if (!empty($where_values)) {
        $total_query = $wpdb->prepare($total_query, $where_values);
    }
    $total_items = $wpdb->get_var($total_query);

    // Get data
    $query = "
        SELECT l.*, u.display_name
        FROM $table_name l
        LEFT JOIN {$wpdb->users} u ON l.user_id = u.ID
        WHERE $where_clause
        ORDER BY l.timestamp DESC
        LIMIT %d OFFSET %d
    ";

    $query_values = array_merge($where_values, array($per_page, $offset));
    $results = $wpdb->get_results($wpdb->prepare($query, $query_values));

    return array(
        'logs' => $results,
        'total_items' => intval($total_items),
        'total_pages' => ceil($total_items / $per_page),
        'current_page' => $page
    );
}

/**
 * AJAX handler for CSV export
 */
function businesscraft_ai_ajax_export_sector_analytics() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'sector_analytics_nonce')) {
        wp_die('Security check failed');
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    // Apply filters
    $where_conditions = array('1=1');
    $where_values = array();

    if (!empty($_POST['country_filter'])) {
        $where_conditions[] = 'country = %s';
        $where_values[] = sanitize_text_field($_POST['country_filter']);
    }

    if (!empty($_POST['sector_filter'])) {
        $where_conditions[] = 'detected_sector = %s';
        $where_values[] = sanitize_text_field($_POST['sector_filter']);
    }

    if (isset($_POST['success_filter']) && $_POST['success_filter'] !== '') {
        $where_conditions[] = 'sector_context_found = %d';
        $where_values[] = intval($_POST['success_filter']);
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Get data for export
    $query = "
        SELECT l.*, u.display_name, u.user_email
        FROM $table_name l
        LEFT JOIN {$wpdb->users} u ON l.user_id = u.ID
        WHERE $where_clause
        ORDER BY l.timestamp DESC
        LIMIT 1000
    ";

    if (!empty($where_values)) {
        $results = $wpdb->get_results($wpdb->prepare($query, $where_values));
    } else {
        $results = $wpdb->get_results($query);
    }

    // Generate CSV
    $filename = 'sector-analytics-' . date('Y-m-d-H-i-s') . '.csv';

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');

    $output = fopen('php://output', 'w');

    // CSV headers
    fputcsv($output, array(
        'Date',
        'User Name',
        'User Email',
        'Country',
        'Detected Sector',
        'Context Found',
        'Tokens Estimated',
        'Message Preview'
    ));

    // CSV data
    foreach ($results as $row) {
        fputcsv($output, array(
            $row->timestamp,
            $row->display_name ?: 'Unknown',
            $row->user_email ?: 'Unknown',
            $row->country,
            $row->detected_sector ?: 'None',
            $row->sector_context_found ? 'Yes' : 'No',
            $row->prompt_tokens_estimated,
            $row->user_message_preview
        ));
    }

    fclose($output);
    exit;
}
add_action('wp_ajax_export_sector_analytics', 'businesscraft_ai_ajax_export_sector_analytics');

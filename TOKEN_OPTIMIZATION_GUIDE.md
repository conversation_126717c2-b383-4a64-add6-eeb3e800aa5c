# BusinessCraft AI Token Optimization Guide

## Overview

The Token Optimization system is designed to reduce OpenAI API costs while maintaining the quality of AI responses for African entrepreneurs. This system implements intelligent context management, prompt compression, and caching strategies specifically optimized for BusinessCraft AI's African-centric approach.

## Key Features

### 1. Smart Context Management
- **Dynamic Context Prioritization**: Selects only relevant African context based on request type
- **Context Relevance Scoring**: Prioritizes context elements that matter most for each query
- **Model-Specific Optimization**: Adjusts context size based on the OpenAI model being used

### 2. Intelligent Caching System
- **Context Caching**: Reuses frequently accessed African context data
- **Cache Hit Rate Tracking**: Monitors cache effectiveness for optimization
- **Automatic Cache Expiry**: Ensures context freshness while maximizing reuse

### 3. Prompt Compression
- **Automatic Compression**: Reduces prompt size when approaching token limits
- **Content Prioritization**: Preserves most important information during compression
- **Compression Analytics**: Tracks compression frequency and effectiveness

### 4. Token Usage Analytics
- **Real-time Monitoring**: Tracks token usage patterns across all requests
- **Cost Analysis**: Calculates actual API costs and savings from optimization
- **Performance Metrics**: Measures optimization effectiveness over time

## Implementation Details

### Token Optimizer Class (`BusinessCraft_Token_Optimizer`)

Located in `/inc/token-optimizer.php`, this class handles all optimization logic:

```php
// Initialize the optimizer
$token_optimizer = new BusinessCraft_Token_Optimizer();

// Optimize a prompt with context
$optimized_prompt = $token_optimizer->optimize_prompt($message, $context_data, $model);

// Get optimization metrics
$metrics = $token_optimizer->get_optimization_metrics(30);
```

### Key Methods

#### `optimize_prompt($message, $context_data, $model)`
- Classifies request type (market analysis, financial planning, etc.)
- Selects relevant African context elements
- Applies caching and compression as needed
- Returns optimized prompt ready for OpenAI API

#### `classify_request($message)`
- Analyzes user message to determine request type
- Returns classification for targeted optimization
- Supports: market_analysis, financial_planning, business_strategy, marketing, operations

#### `optimize_context($context_data, $request_type, $model)`
- Filters context data based on request relevance
- Applies model-specific size limits
- Prioritizes most important context elements

### Integration with African Context Engine

The optimizer works seamlessly with the existing African Context Engine:

```php
// Get African context data
$african_context = new BusinessCraft_African_Context_Engine();
$country_context = $african_context->get_country_context($user_country);

// Optimize with African context
$context_data = array_merge($base_context, $country_context);
$optimized_prompt = $token_optimizer->optimize_prompt($message, $context_data, $model);
```

## Performance Metrics

### Dashboard Analytics

The admin dashboard now includes token optimization metrics:

1. **Average Tokens per Request**: Tracks efficiency improvements
2. **Cache Hit Rate**: Measures caching effectiveness (target: >70%)
3. **Total Optimizations**: Shows system activity
4. **Token Efficiency Trends**: Visual chart of optimization over time

### Key Performance Indicators

- **Token Reduction**: Target 20-30% reduction in average tokens per request
- **Cache Hit Rate**: Target >70% for frequently used contexts
- **Response Quality**: Maintain >90% user satisfaction despite optimization
- **Cost Savings**: Track actual dollar savings from reduced API usage

## Configuration and Tuning

### Model-Specific Limits

The optimizer includes optimized limits for each OpenAI model:

```php
'gpt-3.5-turbo' => array(
    'max_tokens' => 4096,
    'optimal_prompt' => 2000,
    'optimal_response' => 800
),
'gpt-4' => array(
    'max_tokens' => 8192,
    'optimal_prompt' => 4000,
    'optimal_response' => 1200
),
'gpt-4-turbo' => array(
    'max_tokens' => 128000,
    'optimal_prompt' => 6000,
    'optimal_response' => 1500
)
```

### Context Priority Maps

Different request types prioritize different African context elements:

- **Market Analysis**: market_characteristics, regulatory_environment, opportunities
- **Financial Planning**: regulatory_environment, payment_preferences, business_challenges
- **Business Strategy**: business_culture, market_characteristics, opportunities
- **Marketing**: communication_style, market_characteristics, networking_culture

## Monitoring and Troubleshooting

### Analytics Events

The system logs detailed analytics for monitoring:

- `context_cache_hit`: Successful cache retrieval
- `context_cache_miss`: Cache miss requiring new context generation
- `prompt_compressed`: Prompt size reduction applied

### Performance Monitoring

Monitor these metrics in the admin dashboard:

1. **Cache Hit Rate**: Should be >70% for optimal performance
2. **Average Tokens**: Should show downward trend over time
3. **Compression Frequency**: High frequency may indicate need for better context selection

### Troubleshooting Common Issues

#### Low Cache Hit Rate (<50%)
- Check if context data is too variable
- Consider adjusting cache key generation
- Review context selection logic

#### High Compression Frequency (>20%)
- Review context prioritization rules
- Consider reducing default context size
- Check if model limits are appropriate

#### Quality Degradation
- Monitor user feedback and satisfaction scores
- Review compression algorithm effectiveness
- Adjust context priority weights

## Cost Impact Analysis

### Expected Savings

Based on implementation, expect:

- **20-30% reduction** in average tokens per request
- **$0.50-1.50 savings per 1000 requests** (depending on model)
- **15-25% overall API cost reduction** for typical usage patterns

### ROI Calculation

For a typical BusinessCraft AI deployment:
- 10,000 requests/month
- Average 1,500 tokens per request (before optimization)
- GPT-3.5-turbo pricing: $0.002/1K tokens

**Before Optimization**: 15M tokens/month = $30/month
**After Optimization**: 11M tokens/month = $22/month
**Monthly Savings**: $8/month (27% reduction)

## Future Enhancements

### Planned Improvements

1. **Machine Learning Optimization**: Use usage patterns to improve context selection
2. **User-Specific Caching**: Cache based on individual user preferences and history
3. **Dynamic Model Selection**: Automatically choose optimal model based on request complexity
4. **Advanced Compression**: Implement semantic compression techniques

### Integration Opportunities

1. **WhatsApp Business API**: Extend optimization to WhatsApp interactions
2. **Mobile App**: Apply optimization to mobile app API calls
3. **Batch Processing**: Optimize multiple requests together for better efficiency

## Best Practices

### For Developers

1. **Monitor Metrics**: Regularly check optimization effectiveness
2. **Test Changes**: Always test optimization changes with real user data
3. **Preserve Quality**: Never sacrifice response quality for token savings
4. **Document Changes**: Keep track of optimization rule modifications

### For Administrators

1. **Regular Review**: Check optimization metrics weekly
2. **User Feedback**: Monitor user satisfaction alongside cost savings
3. **Performance Tuning**: Adjust settings based on usage patterns
4. **Cost Tracking**: Monitor actual API cost reductions

## Conclusion

The Token Optimization system provides significant cost savings while maintaining the high-quality, African-centric responses that make BusinessCraft AI unique. By intelligently managing context, caching frequently used data, and compressing prompts when necessary, the system reduces operational costs while preserving the cultural relevance and business intelligence that users expect.

Regular monitoring and tuning ensure optimal performance and continued cost savings as usage patterns evolve.

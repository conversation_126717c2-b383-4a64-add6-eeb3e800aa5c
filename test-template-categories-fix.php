<?php
/**
 * Test Template Categories Function Conflict Fix
 * 
 * This script tests that the chatgabi_get_template_categories function conflict has been resolved
 * and verifies that both the Templates interface and prompt templates functionality work correctly.
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Template Categories Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 5px; border-radius: 3px; font-family: monospace; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>

<h1>🔧 ChatGABI Template Categories Function Fix Test</h1>

<?php
echo '<div class="info">Test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

$all_passed = true;

// Test 1: Check if WordPress loads without fatal errors
echo '<h2>✅ Test 1: WordPress Loading</h2>';
echo '<div class="success">✅ WordPress loaded successfully - no fatal errors detected!</div>';

// Test 2: Check function existence and uniqueness
echo '<h2>🔍 Test 2: Function Existence Check</h2>';

$functions_to_test = array(
    'chatgabi_get_template_categories' => 'Template categories retrieval',
    'chatgabi_get_default_template_categories' => 'Default template categories',
    'chatgabi_init_template_categories' => 'Template categories initialization',
    'chatgabi_init_prompt_templates' => 'Prompt templates initialization',
    'chatgabi_save_prompt_template' => 'Prompt template saving',
    'chatgabi_get_user_templates' => 'User templates retrieval'
);

foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        echo "<div class='success'>✅ <strong>{$function}()</strong> - {$description}</div>";
    } else {
        echo "<div class='error'>❌ <strong>{$function}()</strong> - {$description} (MISSING)</div>";
        $all_passed = false;
    }
}

// Test 3: Test the specific function that was duplicated
echo '<h2>🧪 Test 3: Template Categories Function Testing</h2>';

try {
    // Test basic functionality (backward compatibility)
    echo '<h3>Basic Template Categories Retrieval:</h3>';
    $categories_basic = chatgabi_get_template_categories();
    
    if (is_array($categories_basic) && !empty($categories_basic)) {
        echo "<div class='success'>✅ Basic call returned " . count($categories_basic) . " categories</div>";
        
        // Display categories in a table
        echo '<table>';
        echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Icon</th><th>Description</th></tr>';
        foreach (array_slice($categories_basic, 0, 3) as $category) {
            $cat_array = is_object($category) ? (array) $category : $category;
            echo '<tr>';
            echo '<td>' . ($cat_array['id'] ?? 'N/A') . '</td>';
            echo '<td>' . ($cat_array['name'] ?? 'N/A') . '</td>';
            echo '<td>' . ($cat_array['slug'] ?? 'N/A') . '</td>';
            echo '<td>' . ($cat_array['icon'] ?? 'N/A') . '</td>';
            echo '<td>' . substr($cat_array['description'] ?? 'N/A', 0, 50) . '...</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="error">❌ Basic call failed or returned empty result</div>';
        $all_passed = false;
    }
    
    // Test with counts
    echo '<h3>Template Categories with Counts:</h3>';
    $categories_with_counts = chatgabi_get_template_categories(true);
    
    if (is_array($categories_with_counts) && !empty($categories_with_counts)) {
        echo "<div class='success'>✅ With counts call returned " . count($categories_with_counts) . " categories</div>";
        
        // Check if template_count field exists
        $first_category = is_object($categories_with_counts[0]) ? (array) $categories_with_counts[0] : $categories_with_counts[0];
        if (isset($first_category['template_count'])) {
            echo '<div class="success">✅ Template counts included in results</div>';
        } else {
            echo '<div class="warning">⚠️ Template counts not found in results</div>';
        }
    } else {
        echo '<div class="error">❌ With counts call failed</div>';
        $all_passed = false;
    }
    
    // Test return format compatibility
    echo '<h3>Return Format Compatibility:</h3>';
    $categories_arrays = chatgabi_get_template_categories(false, true);
    $categories_objects = chatgabi_get_template_categories(false, false);
    
    if (is_array($categories_arrays) && !empty($categories_arrays)) {
        $first_array = $categories_arrays[0];
        if (is_array($first_array)) {
            echo '<div class="success">✅ Array format working correctly</div>';
        } else {
            echo '<div class="error">❌ Array format not working</div>';
            $all_passed = false;
        }
    }
    
    if (is_array($categories_objects) && !empty($categories_objects)) {
        $first_object = $categories_objects[0];
        if (is_object($first_object)) {
            echo '<div class="success">✅ Object format working correctly</div>';
        } else {
            echo '<div class="error">❌ Object format not working</div>';
            $all_passed = false;
        }
    }
    
} catch (Error $e) {
    echo '<div class="error">❌ FATAL ERROR in template categories function: ' . $e->getMessage() . '</div>';
    echo '<div class="error">File: ' . $e->getFile() . ' | Line: ' . $e->getLine() . '</div>';
    $all_passed = false;
} catch (Exception $e) {
    echo '<div class="error">❌ EXCEPTION in template categories function: ' . $e->getMessage() . '</div>';
    $all_passed = false;
}

// Test 4: Test database table existence
echo '<h2>🗄️ Test 4: Database Table Verification</h2>';

global $wpdb;
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

$categories_exists = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table;
$templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table;

echo "<p>Categories table exists: " . ($categories_exists ? "✅ Yes" : "❌ No") . "</p>";
echo "<p>Templates table exists: " . ($templates_exists ? "✅ Yes" : "❌ No") . "</p>";

if ($categories_exists) {
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");
    echo "<p>Categories in database: $category_count</p>";
    
    if ($category_count > 0) {
        echo '<div class="success">✅ Categories table populated</div>';
    } else {
        echo '<div class="warning">⚠️ Categories table empty - will use defaults</div>';
    }
}

if ($templates_exists) {
    $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
    echo "<p>Templates in database: $template_count</p>";
}

// Test 5: Test integration with Templates interface
echo '<h2>🎯 Test 5: Templates Interface Integration</h2>';

try {
    // Test page-templates.php compatibility
    $templates_page = get_page_by_path('templates');
    if ($templates_page) {
        echo '<div class="success">✅ Templates page exists</div>';
        
        // Simulate the call from page-templates.php
        $template_categories = function_exists('chatgabi_get_template_categories') ? 
            chatgabi_get_template_categories() : array();
            
        if (!empty($template_categories)) {
            echo '<div class="success">✅ Templates page can retrieve categories</div>';
        } else {
            echo '<div class="warning">⚠️ Templates page getting empty categories</div>';
        }
    } else {
        echo '<div class="warning">⚠️ Templates page not found</div>';
    }
    
    // Test REST API compatibility
    if (function_exists('chatgabi_get_template_categories_api')) {
        echo '<div class="success">✅ REST API function exists</div>';
    } else {
        echo '<div class="warning">⚠️ REST API function not found</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Integration test failed: ' . $e->getMessage() . '</div>';
    $all_passed = false;
}

// Test 6: Check for any remaining duplicate function issues
echo '<h2>🔍 Test 6: Duplicate Function Detection</h2>';

$declared_functions = get_defined_functions()['user'];
$chatgabi_functions = array_filter($declared_functions, function($func) {
    return strpos($func, 'chatgabi_') === 0;
});

echo '<div class="info">Found ' . count($chatgabi_functions) . ' ChatGABI functions loaded</div>';

// Check for the specific function that was duplicated
$template_functions = array_filter($chatgabi_functions, function($func) {
    return strpos($func, 'template') !== false;
});

echo '<div class="info">Found ' . count($template_functions) . ' template-related functions:</div>';
echo '<ul>';
foreach (array_slice($template_functions, 0, 10) as $func) {
    echo "<li><span class='code'>{$func}()</span></li>";
}
if (count($template_functions) > 10) {
    echo '<li>... and ' . (count($template_functions) - 10) . ' more</li>';
}
echo '</ul>';

// Verify the specific function location
if (function_exists('chatgabi_get_template_categories')) {
    $reflection = new ReflectionFunction('chatgabi_get_template_categories');
    $file = $reflection->getFileName();
    $line = $reflection->getStartLine();
    $short_file = basename($file);
    echo "<div class='info'>📍 <strong>chatgabi_get_template_categories()</strong> defined in <span class='code'>{$short_file}:{$line}</span></div>";
    
    if (strpos($file, 'prompt-templates.php') !== false) {
        echo '<div class="success">✅ Function correctly located in prompt-templates.php</div>';
    } else {
        echo '<div class="error">❌ Function in unexpected location</div>';
        $all_passed = false;
    }
}

echo '<div class="success">✅ No duplicate function conflicts detected!</div>';

// Final Results
echo '<h2>📊 Final Results</h2>';

if ($all_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL TESTS PASSED!</h3>';
    echo '<p><strong>✅ Template categories function conflict resolved successfully!</strong></p>';
    echo '<ul>';
    echo '<li>✅ No fatal errors detected</li>';
    echo '<li>✅ chatgabi_get_template_categories() function working correctly</li>';
    echo '<li>✅ Backward compatibility maintained</li>';
    echo '<li>✅ Enhanced functionality available</li>';
    echo '<li>✅ Database integration working</li>';
    echo '<li>✅ Templates interface compatibility verified</li>';
    echo '<li>✅ No duplicate function declarations</li>';
    echo '</ul>';
    echo '</div>';
    
    echo '<div class="info">';
    echo '<h3>🚀 Next Steps:</h3>';
    echo '<ol>';
    echo '<li>Visit the <a href="' . home_url('/templates') . '" target="_blank">Templates Page</a> to test the interface</li>';
    echo '<li>Test template category filtering and display</li>';
    echo '<li>Test prompt template creation and management</li>';
    echo '<li>Verify AI-powered template features work correctly</li>';
    echo '<li>Test mobile responsiveness and user experience</li>';
    echo '</ol>';
    echo '</div>';
    
} else {
    echo '<div class="error">';
    echo '<h3>❌ SOME TESTS FAILED</h3>';
    echo '<p>Please review the errors above and fix any remaining issues.</p>';
    echo '</div>';
}

echo '<hr>';
echo '<div class="info">Test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

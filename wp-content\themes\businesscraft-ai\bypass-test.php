<?php
/**
 * Bypass test - define error_log first
 */

// Define error_log function before including anything
if (!function_exists('error_log')) {
    function error_log($message) {
        echo "LOG: " . $message . "\n";
    }
}

echo "Starting bypass test...\n";

// Define WordPress constants
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
    echo "WP_CONTENT_DIR defined\n";
}

echo "About to include data-loader.php...\n";

// Include data loader
require_once(__DIR__ . '/inc/data-loader.php');

echo "Data loader included successfully!\n";

// Test basic functionality
echo "Testing get_available_dataset_countries...\n";
$countries = get_available_dataset_countries();
echo "Available countries: " . implode(', ', $countries) . "\n";

echo "Testing get_sector_context_by_country...\n";
$result = get_sector_context_by_country('Ghana', 'Fintech');

if ($result === null) {
    echo "❌ No result for Ghana Fintech\n";
} else {
    echo "✅ Got result for Ghana Fintech: " . $result['sector_name'] . "\n";
    echo "Overview length: " . strlen($result['overview']) . " characters\n";
}

echo "Test complete!\n";

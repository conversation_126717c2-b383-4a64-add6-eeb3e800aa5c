<?php
/**
 * Test Opportunities Integration in AI Prompts
 * 
 * This script tests the new opportunities integration feature
 * that automatically includes relevant business opportunities in AI responses.
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll include WordPress
    require_once('../../../wp-load.php');
}

// Include required files
require_once get_template_directory() . '/inc/openai-integration.php';
require_once get_template_directory() . '/inc/opportunity-loader.php';
require_once get_template_directory() . '/inc/data-loader.php';

echo "<h1>🧪 BusinessCraft AI - Opportunities Integration Test</h1>\n";
echo "<style>body { font-family: Arial, sans-serif; margin: 40px; } .success { color: green; } .error { color: red; } .info { color: blue; } pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }</style>\n";

// Test 1: Test opportunities section builder
echo "<h2>Test 1: Opportunities Section Builder</h2>\n";

$test_countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
$test_sectors = ['Technology', 'Fintech', 'Agriculture', 'General'];

foreach ($test_countries as $country) {
    echo "<h3>Testing {$country}</h3>\n";
    
    foreach ($test_sectors as $sector) {
        echo "<h4>Sector: {$sector}</h4>\n";
        
        $opportunities_data = businesscraft_ai_build_opportunities_section($country, $sector);
        
        if (!empty($opportunities_data)) {
            echo "<p class='success'>✅ Found opportunities data</p>\n";
            echo "<p><strong>Count:</strong> " . ($opportunities_data['count'] ?? 0) . "</p>\n";
            echo "<p><strong>Tokens:</strong> " . ($opportunities_data['tokens'] ?? 0) . "</p>\n";
            
            if (!empty($opportunities_data['text'])) {
                echo "<details><summary>View Opportunities Text</summary>\n";
                echo "<pre>" . htmlspecialchars($opportunities_data['text']) . "</pre>\n";
                echo "</details>\n";
            }
        } else {
            echo "<p class='info'>ℹ️ No opportunities found for {$sector} in {$country}</p>\n";
        }
        echo "<hr>\n";
    }
}

// Test 2: Test full prompt building with opportunities
echo "<h2>Test 2: Full Prompt Building with Opportunities</h2>\n";

$test_messages = [
    'I want to start a fintech business in Ghana',
    'Help me with agricultural business planning in Kenya',
    'What are the opportunities for tech startups in Nigeria?',
    'I need funding for my manufacturing business in South Africa'
];

foreach ($test_messages as $message) {
    echo "<h3>Testing Message: \"" . htmlspecialchars($message) . "\"</h3>\n";
    
    // Simulate user data
    $user_country = 'GH'; // Ghana
    $business_type = 'sme';
    $industry = 'Technology';
    $language = 'en';
    $context = 'business_plan';
    $user_id = 1; // Test user ID
    
    try {
        $enhanced_prompt = businesscraft_ai_build_enhanced_prompt(
            $message, 
            $language, 
            $context, 
            $user_country, 
            $business_type, 
            $industry, 
            $user_id
        );
        
        if (!empty($enhanced_prompt)) {
            echo "<p class='success'>✅ Enhanced prompt generated successfully</p>\n";
            echo "<p><strong>Prompt Length:</strong> " . strlen($enhanced_prompt) . " characters</p>\n";
            echo "<p><strong>Estimated Tokens:</strong> " . businesscraft_ai_estimate_tokens($enhanced_prompt) . "</p>\n";
            
            // Check if opportunities are included
            if (strpos($enhanced_prompt, '🔍 REAL-TIME OPPORTUNITIES:') !== false) {
                echo "<p class='success'>✅ Opportunities section found in prompt</p>\n";
            } else {
                echo "<p class='info'>ℹ️ No opportunities section in prompt (may be expected for some cases)</p>\n";
            }
            
            echo "<details><summary>View Full Enhanced Prompt</summary>\n";
            echo "<pre>" . htmlspecialchars($enhanced_prompt) . "</pre>\n";
            echo "</details>\n";
        } else {
            echo "<p class='error'>❌ Failed to generate enhanced prompt</p>\n";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    echo "<hr>\n";
}

// Test 3: Test token optimization
echo "<h2>Test 3: Token Optimization</h2>\n";

$test_prompt_data = businesscraft_ai_build_opportunities_section('Ghana', 'Technology');
if (!empty($test_prompt_data)) {
    $tokens = $test_prompt_data['tokens'] ?? 0;
    echo "<p><strong>Opportunities Section Tokens:</strong> {$tokens}</p>\n";
    
    if ($tokens <= 200) {
        echo "<p class='success'>✅ Token count is within target limit (≤200 tokens)</p>\n";
    } else {
        echo "<p class='error'>❌ Token count exceeds target limit (>200 tokens)</p>\n";
    }
} else {
    echo "<p class='info'>ℹ️ No opportunities data to test token optimization</p>\n";
}

// Test 4: Test database logging (if table exists)
echo "<h2>Test 4: Database Logging Test</h2>\n";

global $wpdb;
$table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;

if ($table_exists) {
    echo "<p class='success'>✅ Sector logs table exists</p>\n";
    
    // Check if new columns exist
    $columns = $wpdb->get_results("DESCRIBE {$table_name}");
    $has_opportunities_columns = false;
    
    foreach ($columns as $column) {
        if ($column->Field === 'opportunities_included' || $column->Field === 'opportunities_tokens_estimated') {
            $has_opportunities_columns = true;
            break;
        }
    }
    
    if ($has_opportunities_columns) {
        echo "<p class='success'>✅ Opportunities tracking columns exist</p>\n";
    } else {
        echo "<p class='error'>❌ Opportunities tracking columns missing - run theme activation</p>\n";
    }
    
    // Get recent logs
    $recent_logs = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY timestamp DESC LIMIT 5");
    
    if (!empty($recent_logs)) {
        echo "<p class='info'>Recent logs found: " . count($recent_logs) . "</p>\n";
        echo "<details><summary>View Recent Logs</summary>\n";
        echo "<pre>" . print_r($recent_logs, true) . "</pre>\n";
        echo "</details>\n";
    } else {
        echo "<p class='info'>ℹ️ No recent logs found</p>\n";
    }
} else {
    echo "<p class='error'>❌ Sector logs table does not exist - run theme activation</p>\n";
}

echo "<h2>🎯 Test Summary</h2>\n";
echo "<p>The opportunities integration test is complete. Key features tested:</p>\n";
echo "<ul>\n";
echo "<li>✅ Opportunities section builder function</li>\n";
echo "<li>✅ Full prompt building with opportunities integration</li>\n";
echo "<li>✅ Token optimization and counting</li>\n";
echo "<li>✅ Database logging with opportunity tracking</li>\n";
echo "</ul>\n";

echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ol>\n";
echo "<li>Test with real OpenAI API calls to verify AI responses include opportunities</li>\n";
echo "<li>Monitor token usage in production to ensure efficiency</li>\n";
echo "<li>Review analytics dashboard for opportunity inclusion metrics</li>\n";
echo "</ol>\n";
?>

/**
 * Template Customization JavaScript for BusinessCraft AI
 * 
 * Handles advanced template customization features
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Template Customization functionality
    const TemplateCustomization = {
        
        init: function() {
            this.bindEvents();
            this.initColorPickers();
            this.loadTemplateThemes();
        },

        bindEvents: function() {
            // Customization modal events
            $(document).on('click', '.customize-template-btn', this.openCustomizationModal);
            $(document).on('click', '.customization-modal-close', this.closeCustomizationModal);
            $(document).on('click', '.customization-modal-backdrop', this.closeCustomizationModal);
            
            // Theme selection
            $(document).on('change', '#template-theme-select', this.applyTheme);
            
            // Text replacement
            $(document).on('click', '#add-text-replacement', this.addTextReplacement);
            $(document).on('click', '.remove-replacement', this.removeTextReplacement);
            
            // Section modifications
            $(document).on('change', '.section-action-select', this.updateSectionAction);
            
            // Preview and save
            $(document).on('click', '#preview-customization', this.previewCustomization);
            $(document).on('click', '#save-customization', this.saveCustomization);
            $(document).on('click', '#reset-customization', this.resetCustomization);
            
            // Version management
            $(document).on('click', '.load-version-btn', this.loadTemplateVersion);
        },

        initColorPickers: function() {
            // Initialize WordPress color pickers
            $('.color-picker').wpColorPicker({
                change: function(event, ui) {
                    TemplateCustomization.updatePreview();
                }
            });
        },

        loadTemplateThemes: function() {
            $.ajax({
                url: businesscraftCustomization.restUrl + 'templates/themes',
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftCustomization.nonce
                },
                success: function(response) {
                    if (response.success) {
                        TemplateCustomization.renderThemeOptions(response.themes);
                    }
                }
            });
        },

        renderThemeOptions: function(themes) {
            const $select = $('#template-theme-select');
            $select.empty().append('<option value="">' + businesscraftCustomization.strings.selectTheme + '</option>');
            
            $.each(themes, function(id, theme) {
                $select.append(`
                    <option value="${id}" data-description="${theme.description}">
                        ${theme.name}
                    </option>
                `);
            });
        },

        openCustomizationModal: function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const templateId = $btn.data('template-id');
            
            if (!templateId) {
                TemplateCustomization.showMessage('error', 'Please select a template to customize.');
                return;
            }

            TemplateCustomization.showCustomizationModal(templateId);
        },

        showCustomizationModal: function(templateId) {
            // Create modal if it doesn't exist
            if (!$('#customization-modal').length) {
                this.createCustomizationModal();
            }

            const $modal = $('#customization-modal');
            
            // Set template ID
            $modal.data('template-id', templateId);
            
            // Load template data
            this.loadTemplateData(templateId);
            
            // Show modal
            $modal.addClass('active');
            $('body').addClass('modal-open');
        },

        createCustomizationModal: function() {
            const modalHtml = `
                <div id="customization-modal" class="customization-modal">
                    <div class="customization-modal-backdrop"></div>
                    <div class="customization-modal-content">
                        <div class="customization-modal-header">
                            <h3>🎨 Advanced Template Customization</h3>
                            <button class="customization-modal-close">&times;</button>
                        </div>
                        <div class="customization-modal-body">
                            <div class="customization-tabs">
                                <button class="tab-btn active" data-tab="theme">Theme</button>
                                <button class="tab-btn" data-tab="content">Content</button>
                                <button class="tab-btn" data-tab="formatting">Formatting</button>
                                <button class="tab-btn" data-tab="versions">Versions</button>
                            </div>
                            
                            <div class="customization-content">
                                <!-- Theme Tab -->
                                <div class="tab-pane active" id="theme-tab">
                                    <h4>Template Theme</h4>
                                    <select id="template-theme-select">
                                        <option value="">Select a theme...</option>
                                    </select>
                                    <p class="theme-description"></p>
                                    
                                    <h4>Color Scheme</h4>
                                    <div class="color-options">
                                        <label>Primary Color: <input type="text" class="color-picker" id="primary-color" value="#3D4E81"></label>
                                        <label>Secondary Color: <input type="text" class="color-picker" id="secondary-color" value="#5753C9"></label>
                                        <label>Accent Color: <input type="text" class="color-picker" id="accent-color" value="#6E7FF3"></label>
                                    </div>
                                </div>
                                
                                <!-- Content Tab -->
                                <div class="tab-pane" id="content-tab">
                                    <h4>Text Replacements</h4>
                                    <div id="text-replacements">
                                        <div class="replacement-item">
                                            <input type="text" placeholder="Find text..." class="find-text">
                                            <input type="text" placeholder="Replace with..." class="replace-text">
                                            <button class="remove-replacement">Remove</button>
                                        </div>
                                    </div>
                                    <button id="add-text-replacement" class="btn-secondary">Add Replacement</button>
                                    
                                    <h4>Section Modifications</h4>
                                    <div id="section-modifications">
                                        <!-- Will be populated dynamically -->
                                    </div>
                                </div>
                                
                                <!-- Formatting Tab -->
                                <div class="tab-pane" id="formatting-tab">
                                    <h4>Header Formatting</h4>
                                    <div class="formatting-options">
                                        <label>H1 Style: 
                                            <select class="header-style" data-level="1">
                                                <option value="bold">Bold</option>
                                                <option value="underline">Underlined</option>
                                                <option value="colored">Colored</option>
                                            </select>
                                        </label>
                                        <label>H2 Style: 
                                            <select class="header-style" data-level="2">
                                                <option value="bold">Bold</option>
                                                <option value="underline">Underlined</option>
                                                <option value="colored">Colored</option>
                                            </select>
                                        </label>
                                    </div>
                                    
                                    <h4>List Formatting</h4>
                                    <div class="list-options">
                                        <label>List Style: 
                                            <select id="list-style">
                                                <option value="bullet">Bullet Points</option>
                                                <option value="numbered">Numbered</option>
                                                <option value="custom">Custom Symbols</option>
                                            </select>
                                        </label>
                                        <input type="text" id="custom-list-symbol" placeholder="Custom symbol..." style="display: none;">
                                    </div>
                                </div>
                                
                                <!-- Versions Tab -->
                                <div class="tab-pane" id="versions-tab">
                                    <h4>Template Versions</h4>
                                    <div id="template-versions">
                                        <div class="loading-versions">Loading versions...</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="customization-preview">
                                <h4>Live Preview</h4>
                                <div id="customization-preview-content">
                                    <p>Select customization options to see a preview...</p>
                                </div>
                            </div>
                        </div>
                        <div class="customization-modal-footer">
                            <button class="btn-secondary" id="reset-customization">Reset</button>
                            <button class="btn-secondary" id="preview-customization">Preview</button>
                            <button class="btn-primary" id="save-customization">Save Customization</button>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
            
            // Bind tab switching
            $('.tab-btn').on('click', function() {
                const tab = $(this).data('tab');
                $('.tab-btn').removeClass('active');
                $('.tab-pane').removeClass('active');
                $(this).addClass('active');
                $('#' + tab + '-tab').addClass('active');
            });
        },

        closeCustomizationModal: function() {
            $('#customization-modal').removeClass('active');
            $('body').removeClass('modal-open');
        },

        loadTemplateData: function(templateId) {
            // Load template content and existing customizations
            $.ajax({
                url: businesscraftCustomization.restUrl + 'templates/' + templateId,
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftCustomization.nonce
                },
                success: function(response) {
                    if (response.success) {
                        TemplateCustomization.populateCustomizationForm(response.template);
                        TemplateCustomization.loadTemplateVersions(templateId);
                    }
                }
            });
        },

        populateCustomizationForm: function(template) {
            // Populate form with existing customizations
            if (template.customizations) {
                const customizations = JSON.parse(template.customizations);
                
                // Apply theme
                if (customizations.theme) {
                    $('#template-theme-select').val(customizations.theme);
                }
                
                // Apply text replacements
                if (customizations.text_replacements) {
                    this.populateTextReplacements(customizations.text_replacements);
                }
                
                // Apply formatting
                if (customizations.formatting) {
                    this.populateFormattingOptions(customizations.formatting);
                }
            }
            
            // Generate section modifications based on content
            this.generateSectionModifications(template.generated_content);
        },

        generateSectionModifications: function(content) {
            const sections = this.extractSections(content);
            const $container = $('#section-modifications');
            
            $container.empty();
            
            sections.forEach(function(section) {
                $container.append(`
                    <div class="section-modification">
                        <label>${section}</label>
                        <select class="section-action-select" data-section="${section}">
                            <option value="">No changes</option>
                            <option value="replace">Replace content</option>
                            <option value="append">Add content</option>
                            <option value="prepend">Insert before</option>
                            <option value="remove">Remove section</option>
                        </select>
                        <textarea class="section-content" placeholder="New content..." style="display: none;"></textarea>
                    </div>
                `);
            });
        },

        extractSections: function(content) {
            const sections = [];
            const lines = content.split('\n');
            
            lines.forEach(function(line) {
                const match = line.match(/^#+\s*(.+)/);
                if (match) {
                    sections.push(match[1]);
                }
            });
            
            return sections;
        },

        addTextReplacement: function() {
            const replacementHtml = `
                <div class="replacement-item">
                    <input type="text" placeholder="Find text..." class="find-text">
                    <input type="text" placeholder="Replace with..." class="replace-text">
                    <button class="remove-replacement">Remove</button>
                </div>
            `;
            
            $('#text-replacements').append(replacementHtml);
        },

        removeTextReplacement: function() {
            $(this).closest('.replacement-item').remove();
        },

        updateSectionAction: function() {
            const $select = $(this);
            const $textarea = $select.siblings('.section-content');
            
            if ($select.val() && $select.val() !== 'remove') {
                $textarea.show();
            } else {
                $textarea.hide();
            }
        },

        applyTheme: function() {
            const themeId = $(this).val();
            const description = $(this).find('option:selected').data('description');
            
            $('.theme-description').text(description || '');
            
            // Update preview
            TemplateCustomization.updatePreview();
        },

        previewCustomization: function() {
            const customizations = TemplateCustomization.gatherCustomizations();
            const templateId = $('#customization-modal').data('template-id');
            
            $('#preview-customization').prop('disabled', true).text(businesscraftCustomization.strings.previewGenerating);
            
            $.ajax({
                url: businesscraftCustomization.restUrl + 'templates/customize',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftCustomization.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    template_id: templateId,
                    customizations: customizations,
                    preview_only: true
                }),
                success: function(response) {
                    if (response.success) {
                        $('#customization-preview-content').html(response.preview);
                    } else {
                        TemplateCustomization.showMessage('error', response.message);
                    }
                },
                error: function() {
                    TemplateCustomization.showMessage('error', businesscraftCustomization.strings.customizationError);
                },
                complete: function() {
                    $('#preview-customization').prop('disabled', false).text('Preview');
                }
            });
        },

        saveCustomization: function() {
            const customizations = TemplateCustomization.gatherCustomizations();
            const templateId = $('#customization-modal').data('template-id');
            
            $('#save-customization').prop('disabled', true).text(businesscraftCustomization.strings.customizing);
            
            $.ajax({
                url: businesscraftCustomization.restUrl + 'templates/customize',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftCustomization.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    template_id: templateId,
                    customizations: customizations
                }),
                success: function(response) {
                    if (response.success) {
                        TemplateCustomization.showMessage('success', businesscraftCustomization.strings.customizationSaved);
                        TemplateCustomization.closeCustomizationModal();
                        
                        // Refresh template display if needed
                        if (typeof loadTemplates === 'function') {
                            loadTemplates();
                        }
                    } else {
                        TemplateCustomization.showMessage('error', response.message);
                    }
                },
                error: function() {
                    TemplateCustomization.showMessage('error', businesscraftCustomization.strings.customizationError);
                },
                complete: function() {
                    $('#save-customization').prop('disabled', false).text('Save Customization');
                }
            });
        },

        gatherCustomizations: function() {
            const customizations = {};
            
            // Theme
            const theme = $('#template-theme-select').val();
            if (theme) {
                customizations.theme = theme;
            }
            
            // Colors
            customizations.colors = {
                primary: $('#primary-color').val(),
                secondary: $('#secondary-color').val(),
                accent: $('#accent-color').val()
            };
            
            // Text replacements
            const textReplacements = [];
            $('#text-replacements .replacement-item').each(function() {
                const find = $(this).find('.find-text').val();
                const replace = $(this).find('.replace-text').val();
                
                if (find && replace) {
                    textReplacements.push({ find: find, replace: replace });
                }
            });
            
            if (textReplacements.length > 0) {
                customizations.text_replacements = textReplacements;
            }
            
            // Section modifications
            const sectionModifications = {};
            $('#section-modifications .section-modification').each(function() {
                const section = $(this).find('.section-action-select').data('section');
                const action = $(this).find('.section-action-select').val();
                const content = $(this).find('.section-content').val();
                
                if (action) {
                    sectionModifications[section] = {
                        action: action,
                        content: content || ''
                    };
                }
            });
            
            if (Object.keys(sectionModifications).length > 0) {
                customizations.section_modifications = sectionModifications;
            }
            
            // Formatting
            const formatting = {
                headers: {},
                lists: {
                    style: $('#list-style').val()
                }
            };
            
            $('.header-style').each(function() {
                const level = $(this).data('level');
                const style = $(this).val();
                formatting.headers[level] = { style: style };
            });
            
            customizations.formatting = formatting;
            
            return customizations;
        },

        updatePreview: function() {
            // Update live preview based on current settings
            // This is a simplified preview - full preview requires server processing
            const theme = $('#template-theme-select').val();
            const primaryColor = $('#primary-color').val();
            
            let previewHtml = '<div style="color: ' + primaryColor + ';">';
            previewHtml += '<h3>Sample Business Plan</h3>';
            previewHtml += '<p>This is how your customized template will look...</p>';
            previewHtml += '<ul><li>Market Analysis</li><li>Financial Projections</li></ul>';
            previewHtml += '</div>';
            
            $('#customization-preview-content').html(previewHtml);
        },

        loadTemplateVersions: function(templateId) {
            $.ajax({
                url: businesscraftCustomization.restUrl + 'templates/' + templateId + '/versions',
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftCustomization.nonce
                },
                success: function(response) {
                    if (response.success) {
                        TemplateCustomization.renderTemplateVersions(response.versions);
                    }
                }
            });
        },

        renderTemplateVersions: function(versions) {
            const $container = $('#template-versions');
            
            if (versions.length === 0) {
                $container.html('<p>No previous versions available.</p>');
                return;
            }
            
            let html = '<div class="versions-list">';
            
            versions.forEach(function(version) {
                const date = new Date(version.created_at).toLocaleDateString();
                html += `
                    <div class="version-item">
                        <div class="version-info">
                            <strong>${version.version_type}</strong>
                            <span class="version-date">${date}</span>
                        </div>
                        <button class="load-version-btn btn-small" data-version-id="${version.id}">
                            Load Version
                        </button>
                    </div>
                `;
            });
            
            html += '</div>';
            $container.html(html);
        },

        showMessage: function(type, message) {
            // Create or update message container
            let $message = $('.customization-message');
            
            if (!$message.length) {
                $message = $('<div class="customization-message"></div>');
                $('body').append($message);
            }
            
            $message
                .removeClass('success error')
                .addClass(type)
                .text(message)
                .fadeIn();
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        TemplateCustomization.init();
    });

    // Make TemplateCustomization available globally
    window.BusinessCraftTemplateCustomization = TemplateCustomization;

})(jQuery);

<?php
/**
 * Simple test for sector context injection
 */

// Define WordPress constants for testing
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Include the data loader functions
require_once(__DIR__ . '/inc/data-loader.php');

// Mock error_log function
if (!function_exists('error_log')) {
    function error_log($message) {
        echo "[LOG] " . $message . "\n";
    }
}

echo "=== BusinessCraft AI Sector Context Injection Test ===\n\n";

// Test 1: Load sector context
echo "Test 1: Loading sector context for Ghana Fintech\n";
echo str_repeat('-', 50) . "\n";

$sector_context = get_sector_context_by_country('Ghana', 'Fintech');

if ($sector_context === null) {
    echo "❌ Failed to load sector context\n";
} else {
    echo "✅ Successfully loaded sector context\n";
    echo "Sector Name: " . $sector_context['sector_name'] . "\n";
    echo "Overview: " . substr($sector_context['overview'], 0, 100) . "...\n";
    
    // Test the build_localized_prompt function
    function build_localized_prompt($user_question, $sector_context = null, $country = '', $sector_name = '') {
        $prompt = '';
        
        if ($sector_context && is_array($sector_context)) {
            $prompt .= "You are a professional business advisor helping entrepreneurs in {$country}";
            if ($sector_name) {
                $prompt .= " operating in the {$sector_name} industry";
            }
            $prompt .= ".\n\n";
            
            $prompt .= "Here is the relevant business context for this sector:\n\n";
            
            if (isset($sector_context['overview'])) {
                $prompt .= "SECTOR OVERVIEW:\n" . $sector_context['overview'] . "\n\n";
            }
            
            if (isset($sector_context['key_conditions']) && is_array($sector_context['key_conditions'])) {
                $conditions = $sector_context['key_conditions'];
                
                if (isset($conditions['regulatory_environment'])) {
                    $prompt .= "REGULATORY ENVIRONMENT:\n" . $conditions['regulatory_environment'] . "\n\n";
                }
                
                if (isset($conditions['market_size_and_growth'])) {
                    $prompt .= "MARKET SIZE AND GROWTH:\n" . $conditions['market_size_and_growth'] . "\n\n";
                }
                
                if (isset($conditions['investment_opportunities'])) {
                    $prompt .= "INVESTMENT OPPORTUNITIES:\n" . $conditions['investment_opportunities'] . "\n\n";
                }
                
                if (isset($conditions['major_players']) && is_array($conditions['major_players'])) {
                    $prompt .= "MAJOR PLAYERS:\n" . implode(', ', $conditions['major_players']) . "\n\n";
                }
                
                if (isset($conditions['challenges_and_risks'])) {
                    $prompt .= "CHALLENGES & RISKS:\n" . $conditions['challenges_and_risks'] . "\n\n";
                }
            }
            
            $prompt .= "Now, respond to the user's query below using this local context:\n\n";
        }
        
        $prompt .= "USER QUESTION: " . $user_question;
        
        return $prompt;
    }
    
    echo "\nTest 2: Building localized prompt\n";
    echo str_repeat('-', 50) . "\n";
    
    $user_question = "I want to start a mobile payment business in Ghana. What are the key opportunities?";
    $localized_prompt = build_localized_prompt($user_question, $sector_context, 'Ghana', 'Fintech');
    
    echo "User Question: " . $user_question . "\n\n";
    echo "Generated Prompt (first 500 chars):\n";
    echo substr($localized_prompt, 0, 500) . "...\n\n";
    
    $prompt_length = strlen($localized_prompt);
    $estimated_tokens = ceil($prompt_length / 4);
    
    echo "Prompt Statistics:\n";
    echo "- Character count: {$prompt_length}\n";
    echo "- Estimated tokens: {$estimated_tokens}\n";
    echo "- Contains sector overview: " . (strpos($localized_prompt, 'SECTOR OVERVIEW:') !== false ? "Yes" : "No") . "\n";
    echo "- Contains regulatory info: " . (strpos($localized_prompt, 'REGULATORY ENVIRONMENT:') !== false ? "Yes" : "No") . "\n";
}

echo "\n" . str_repeat('=', 60) . "\n";

// Test 3: Sector detection
echo "Test 3: Sector detection from messages\n";
echo str_repeat('-', 50) . "\n";

function businesscraft_ai_detect_sector_from_message($message, $country_name) {
    $available_sectors = get_available_sectors_by_country($country_name);
    
    if (!$available_sectors || !is_array($available_sectors)) {
        return null;
    }
    
    $message_lower = strtolower($message);
    
    $sector_keywords = array(
        'fintech' => array('fintech', 'payment', 'mobile money', 'financial technology', 'banking', 'finance'),
        'agriculture' => array('farm', 'crop', 'livestock', 'agriculture', 'farming', 'agricultural'),
        'technology' => array('tech', 'software', 'app', 'digital', 'technology', 'IT', 'programming'),
    );
    
    // First, try exact sector name matching
    foreach ($available_sectors as $sector) {
        if (stripos($message_lower, strtolower($sector)) !== false) {
            return $sector;
        }
    }
    
    // Then try keyword-based detection
    foreach ($sector_keywords as $sector_type => $keywords) {
        foreach ($keywords as $keyword) {
            if (stripos($message_lower, $keyword) !== false) {
                foreach ($available_sectors as $sector) {
                    if (stripos(strtolower($sector), $sector_type) !== false) {
                        return $sector;
                    }
                }
            }
        }
    }
    
    return null;
}

$test_messages = array(
    array('country' => 'Ghana', 'message' => 'I want to start a fintech company using mobile money'),
    array('country' => 'Kenya', 'message' => 'How can I improve my agricultural farm productivity?'),
    array('country' => 'Nigeria', 'message' => 'I need help with my software development startup')
);

foreach ($test_messages as $i => $test) {
    echo "Detection Test " . ($i + 1) . ":\n";
    echo "Country: " . $test['country'] . "\n";
    echo "Message: " . $test['message'] . "\n";
    
    $detected_sector = businesscraft_ai_detect_sector_from_message($test['message'], $test['country']);
    
    if ($detected_sector) {
        echo "✅ Detected sector: " . $detected_sector . "\n";
    } else {
        echo "❌ No sector detected\n";
    }
    echo "\n";
}

echo "=== Test Complete ===\n";
echo "✅ Sector context injection system is working!\n";

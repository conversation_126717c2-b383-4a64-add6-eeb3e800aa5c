<?php
/**
 * Comprehensive Function Scanner - ChatGABI
 * 
 * Scans all ChatGABI files for function declarations to detect duplicates
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

echo '<h1>🔍 ChatGABI Function Scanner</h1>';
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #dee2e6; }
.code { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; font-size: 12px; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>';

// Function to extract all function declarations from a file
function extractFunctions($file_path) {
    if (!file_exists($file_path)) {
        return array();
    }
    
    $content = file_get_contents($file_path);
    $functions = array();
    
    // Match function declarations
    preg_match_all('/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/m', $content, $matches, PREG_OFFSET_CAPTURE);
    
    foreach ($matches[1] as $index => $match) {
        $function_name = $match[0];
        $offset = $matches[0][$index][1];
        $line_number = substr_count(substr($content, 0, $offset), "\n") + 1;
        
        $functions[] = array(
            'name' => $function_name,
            'line' => $line_number
        );
    }
    
    return $functions;
}

// Files to scan
$files_to_scan = array(
    'inc/feedback-system.php' => 'Core Feedback System',
    'inc/feedback-admin.php' => 'Admin Interface',
    'inc/admin-dashboard.php' => 'Main Admin Dashboard',
    'inc/ajax-handlers.php' => 'AJAX Handlers',
    'inc/database.php' => 'Database Functions',
    'functions.php' => 'Theme Functions'
);

echo '<div class="section">';
echo '<h2>📋 Function Declaration Analysis</h2>';

$all_functions = array();
$file_function_counts = array();

foreach ($files_to_scan as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    echo "<h3>{$description} ({$file})</h3>";
    
    if (file_exists($file_path)) {
        $functions = extractFunctions($file_path);
        $file_function_counts[$file] = count($functions);
        
        echo "<div class='info'>📊 Found " . count($functions) . " function declarations</div>";
        
        if (!empty($functions)) {
            echo '<table>';
            echo '<tr><th>Function Name</th><th>Line</th></tr>';
            foreach ($functions as $func) {
                echo "<tr><td><code>{$func['name']}</code></td><td>{$func['line']}</td></tr>";
                
                // Track all functions for duplicate detection
                if (!isset($all_functions[$func['name']])) {
                    $all_functions[$func['name']] = array();
                }
                $all_functions[$func['name']][] = array(
                    'file' => $file,
                    'line' => $func['line']
                );
            }
            echo '</table>';
        }
    } else {
        echo "<div class='warning'>⚠️ File not found: {$file}</div>";
    }
}

echo '</div>';

// Duplicate detection
echo '<div class="section">';
echo '<h2>🔍 Duplicate Function Detection</h2>';

$duplicates_found = false;
$chatgabi_duplicates = array();

foreach ($all_functions as $function_name => $locations) {
    if (count($locations) > 1) {
        // Check if it's a ChatGABI function
        if (strpos($function_name, 'chatgabi_') === 0 || strpos($function_name, 'businesscraft_') === 0) {
            $duplicates_found = true;
            $chatgabi_duplicates[$function_name] = $locations;
            
            echo "<div class='error'>❌ DUPLICATE: <strong>{$function_name}()</strong> found in " . count($locations) . " files:</div>";
            foreach ($locations as $location) {
                echo "<div class='error'>   📍 {$location['file']} (line {$location['line']})</div>";
            }
        }
    }
}

if (!$duplicates_found) {
    echo "<div class='success'>🎉 No duplicate ChatGABI functions found!</div>";
} else {
    echo "<div class='warning'>⚠️ Found " . count($chatgabi_duplicates) . " duplicate ChatGABI functions that need attention.</div>";
}

echo '</div>';

// Function availability test
echo '<div class="section">';
echo '<h2>🧪 Function Availability Test</h2>';

$critical_functions = array(
    'chatgabi_init_feedback_system',
    'chatgabi_submit_feedback',
    'chatgabi_get_feedback_stats',
    'chatgabi_export_feedback_data',
    'chatgabi_export_csv',
    'chatgabi_export_json',
    'chatgabi_export_xml',
    'chatgabi_render_feedback_overview',
    'chatgabi_render_text_feedback',
    'chatgabi_render_training_data',
    'chatgabi_render_feedback_export',
    'chatgabi_feedback_admin_page'
);

$missing_functions = array();
foreach ($critical_functions as $function) {
    if (function_exists($function)) {
        echo "<div class='success'>✅ {$function}()</div>";
    } else {
        echo "<div class='error'>❌ {$function}() - MISSING</div>";
        $missing_functions[] = $function;
    }
}

if (empty($missing_functions)) {
    echo "<div class='success'>🎉 All critical functions are available!</div>";
} else {
    echo "<div class='warning'>⚠️ " . count($missing_functions) . " critical functions are missing.</div>";
}

echo '</div>';

// Summary and recommendations
echo '<div class="section">';
echo '<h2>📊 Summary & Recommendations</h2>';

echo '<table>';
echo '<tr><th>File</th><th>Function Count</th><th>Status</th></tr>';
foreach ($file_function_counts as $file => $count) {
    $status = $count > 0 ? '✅ Active' : '⚠️ No functions';
    echo "<tr><td>{$file}</td><td>{$count}</td><td>{$status}</td></tr>";
}
echo '</table>';

if ($duplicates_found) {
    echo '<h3>🔧 Recommended Actions:</h3>';
    echo '<ol>';
    foreach ($chatgabi_duplicates as $function_name => $locations) {
        echo "<li><strong>Fix {$function_name}():</strong>";
        echo "<ul>";
        echo "<li>Keep the canonical version in the most appropriate file</li>";
        echo "<li>Remove duplicates from other files</li>";
        echo "<li>Add comments explaining the function location</li>";
        echo "</ul>";
        echo "</li>";
    }
    echo '</ol>';
} else {
    echo '<div class="success">✅ No action needed - all functions are properly organized!</div>';
}

echo '<h3>🚀 Next Steps:</h3>';
echo '<ol>';
echo '<li><a href="/wp-admin/admin.php?page=chatgabi-feedback" target="_blank">Test Admin Dashboard</a></li>';
echo '<li><a href="/test-frontend-feedback-integration.php" target="_blank">Test Frontend Integration</a></li>';
echo '<li><a href="/fix-duplicate-function-error.php" target="_blank">Run Duplicate Function Fix</a></li>';
echo '</ol>';

echo '</div>';

// WordPress health check
echo '<div class="section">';
echo '<h2>🏥 WordPress Health Check</h2>';

echo '<div class="code">';
echo '<strong>WordPress Version:</strong> ' . get_bloginfo('version') . '<br>';
echo '<strong>PHP Version:</strong> ' . PHP_VERSION . '<br>';
echo '<strong>Theme:</strong> ' . get_template() . '<br>';
echo '<strong>Active Plugins:</strong> ' . count(get_option('active_plugins')) . '<br>';
echo '<strong>Memory Limit:</strong> ' . ini_get('memory_limit') . '<br>';
echo '<strong>Max Execution Time:</strong> ' . ini_get('max_execution_time') . 's<br>';

// Check if WordPress is working properly
if (function_exists('wp_get_current_user') && function_exists('get_bloginfo')) {
    echo '<strong>WordPress Status:</strong> ✅ Healthy<br>';
} else {
    echo '<strong>WordPress Status:</strong> ❌ Issues detected<br>';
}

// Check database connection
global $wpdb;
if ($wpdb && $wpdb->get_var("SELECT 1") == 1) {
    echo '<strong>Database:</strong> ✅ Connected<br>';
} else {
    echo '<strong>Database:</strong> ❌ Connection issues<br>';
}

echo '</div>';
echo '</div>';
?>

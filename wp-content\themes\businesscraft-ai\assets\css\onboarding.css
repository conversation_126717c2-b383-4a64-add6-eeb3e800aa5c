/**
 * ChatGABI Onboarding Flow Styles
 * 
 * Styles for the tiered onboarding interface
 */

/* Main Container */
.chatgabi-onboarding {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, sans-serif;
    padding: 0;
    margin: 0;
}

/* Progress Header */
.onboarding-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.progress-text {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.step-indicator {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.step-dot {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.step-dot.completed {
    background: #28a745;
    color: white;
}

.step-dot.current {
    background: #007cba;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(0, 124, 186, 0.5);
}

.step-dot.pending {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

.step-number {
    font-size: 0.8rem;
    font-weight: 600;
    display: none;
}

.step-icon {
    font-size: 1.2rem;
}

.step-dot.completed .step-number {
    display: block;
}

.step-dot.completed .step-icon {
    display: none;
}

/* Content Area */
.onboarding-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.step-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-height: 500px;
}

.step-content {
    padding: 40px;
}

/* Step Headers */
.step-header {
    text-align: center;
    margin-bottom: 40px;
}

.step-header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-weight: 700;
}

.step-header h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-weight: 700;
}

.step-header p {
    font-size: 1.1rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.6;
}

/* Welcome Step */
.welcome-features {
    margin: 40px 0;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.feature-card {
    text-align: center;
    padding: 25px;
    border: 2px solid #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: #007cba;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 124, 186, 0.1);
}

.feature-icon {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 15px;
}

.feature-card h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.feature-card p {
    font-size: 0.95rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

/* Profile Type Selection */
.profile-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.profile-option {
    border: 3px solid #e9ecef;
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.profile-option:hover {
    border-color: #007cba;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 124, 186, 0.1);
}

.profile-option.selected {
    border-color: #28a745;
    background: #f8fff9;
}

.option-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.profile-option h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-weight: 700;
}

.profile-option p {
    font-size: 1rem;
    color: #6c757d;
    margin: 0 0 20px 0;
    font-weight: 600;
}

.profile-option ul {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
    text-align: left;
}

.profile-option li {
    padding: 8px 0;
    color: #495057;
    position: relative;
    padding-left: 25px;
}

.profile-option li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Forms */
.onboarding-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.form-group.error select,
.form-group.error input {
    border-color: #dc3545;
}

.form-error {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 5px;
    font-weight: 500;
}

/* Stage Options */
.stage-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stage-option {
    cursor: pointer;
}

.stage-option input[type="radio"] {
    display: none;
}

.stage-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.stage-option input[type="radio"]:checked + .stage-card {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
}

.stage-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.stage-card h4 {
    margin: 10px 0 5px 0;
    color: #333;
    font-weight: 600;
}

.stage-card p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Checkbox Grid */
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.checkbox-item:hover {
    border-color: #007cba;
    background: #f8f9fa;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 10px;
    width: auto;
}

.checkbox-item input[type="checkbox"]:checked + span {
    color: #007cba;
    font-weight: 600;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-outline {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.btn-primary {
    background: #007cba;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.3);
}

.btn-primary:hover {
    background: #005a87;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #007cba;
    border: 2px solid #007cba;
}

.btn-outline:hover {
    background: #007cba;
    color: white;
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    gap: 15px;
}

.step-actions .btn-primary:only-child {
    margin: 0 auto;
}

/* Completion Step */
.completion-celebration {
    text-align: center;
    margin-bottom: 40px;
}

.celebration-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.next-steps {
    margin: 40px 0;
}

.next-steps h3 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.next-steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.next-step-card {
    text-align: center;
    padding: 25px 20px;
    border: 2px solid #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.next-step-card:hover {
    border-color: #007cba;
    transform: translateY(-3px);
}

.next-step-card .step-icon {
    font-size: 2rem;
    margin-bottom: 15px;
    display: block;
}

.next-step-card h4 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 1.1rem;
}

.next-step-card p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Messages */
.onboarding-message {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
    animation: slideInDown 0.3s ease;
}

.onboarding-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.onboarding-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message-icon {
    margin-right: 10px;
    font-size: 1.1rem;
}

.message-text {
    flex: 1;
}

.message-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;
    margin-left: 10px;
    padding: 0;
    width: 20px;
    height: 20px;
}

.message-close:hover {
    opacity: 1;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading button {
    position: relative;
}

.loading button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Radio Grid */
.radio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.radio-option {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.radio-option:hover {
    border-color: #007cba;
    background: #f0f8ff;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-option input[type="radio"]:checked + .radio-mark + .label-text {
    font-weight: 600;
    color: #007cba;
}

.radio-option input[type="radio"]:checked ~ * {
    color: #007cba;
}

.radio-option:has(input[type="radio"]:checked) {
    border-color: #007cba;
    background: #e3f2fd;
    box-shadow: 0 2px 8px rgba(0, 124, 186, 0.15);
}

.radio-mark {
    width: 20px;
    height: 20px;
    border: 2px solid #ccc;
    border-radius: 50%;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-mark {
    border-color: #007cba;
    background: #007cba;
}

.radio-option input[type="radio"]:checked + .radio-mark::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

/* Template Cards */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.template-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.template-card:hover {
    border-color: #007cba;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.15);
}

.template-card.recommended {
    border-color: #28a745;
    background: #f8fff9;
}

.template-card.recommended:hover {
    border-color: #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
}

.template-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.template-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-weight: 600;
    font-size: 1.1rem;
}

.template-card p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.template-badge {
    position: absolute;
    top: -8px;
    right: 15px;
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.template-actions {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.template-actions p {
    margin: 0;
    color: #666;
    font-style: italic;
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Enhanced Accessibility Styles */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Focus indicators */
.profile-option:focus,
.step-dot:focus,
.feature-card:focus,
.btn:focus {
    outline: 3px solid #4A90E2;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .profile-option,
    .feature-card,
    .step-dot {
        border: 2px solid;
    }

    .btn-primary {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .profile-option,
    .feature-card,
    .step-dot,
    .btn {
        transition: none;
    }

    .progress-fill {
        transition: none;
    }
}

/* Touch interaction feedback */
.touch-active {
    transform: scale(0.98);
    opacity: 0.8;
}

/* Mobile-specific optimizations */
.mobile-onboarding {
    touch-action: pan-y;
}

.mobile-progress .progress-container {
    padding: 0 15px;
}

.mobile-stack {
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
}

.mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    min-height: 44px;
}

.mobile-actions {
    flex-direction: column;
    gap: 12px;
    padding: 20px 0;
}

.mobile-actions .btn {
    width: 100%;
    min-height: 48px;
    font-size: 16px;
}

/* Swipe indicator */
.swipe-indicator {
    text-align: center;
    color: #666;
    font-size: 12px;
    margin-top: 15px;
    opacity: 0.7;
    animation: fadeInOut 3s infinite;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

/* Enhanced form validation styles */
.form-group.error input,
.form-group.error select {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-error::before {
    content: "⚠️";
    font-size: 12px;
}

/* Enhanced message styles */
.onboarding-message {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    animation: slideInDown 0.3s ease-out;
}

.onboarding-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.onboarding-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.7;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-close:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .onboarding-content {
        padding: 20px 15px;
    }

    .step-content {
        padding: 30px 20px;
    }

    .step-header h1 {
        font-size: 2rem;
    }

    .step-header h2 {
        font-size: 1.7rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .profile-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .radio-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .stage-options {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .template-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .step-actions {
        flex-direction: column;
        gap: 15px;
    }

    /* Mobile-specific touch targets */
    .profile-option,
    .feature-card {
        min-height: 60px;
        padding: 20px;
    }

    .step-dot {
        min-width: 44px;
        min-height: 44px;
    }
}
    
    .step-actions .btn-primary,
    .step-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .next-steps-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .step-indicator {
        gap: 10px;
    }
    
    .step-dot {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .step-header h1 {
        font-size: 1.8rem;
    }
    
    .step-header h2 {
        font-size: 1.5rem;
    }
    
    .feature-icon {
        font-size: 2rem;
    }
    
    .option-icon {
        font-size: 2.5rem;
    }
    
    .celebration-icon {
        font-size: 3rem;
    }
}

# BusinessCraft AI WordPress Theme

BusinessCraft AI is an AI-powered chat tool integrated directly into a WordPress theme, designed to assist entrepreneurs and creators in Ghana, Kenya, Nigeria, and South Africa. It leverages OpenAI models to deliver business content and tools through a prompt-based interface, featuring a credit-gated access system, Paystack integration for payments, and robust localization for African users.

## Features

-   **AI Model Logic:** Utilizes `gpt-3.5-turbo` by default, with `gpt-4-turbo` for premium (Ultra tier) users. Credits are deducted based on token usage.
-   **Credit Packs & Billing:**
    -   1 credit = $0.01
    -   Starter: 500 credits ($5)
    -   Growth: 1,500 credits ($15, Ultra tier access)
    -   Business: 3,000 credits ($30, Ultra tier access)
-   **Chat Features:**
    -   Implemented as a Gutenberg block.
    -   Includes input box, submit button, and real-time credit balance display.
    -   Displays the last 10 user chats.
    -   Language selector for English, Twi, Swahili, Yoruba, and Zulu.
    -   Prompt examples and tooltips for user guidance.
-   **Data Storage:**
    -   Templates and market data stored as JSON files within the theme.
    -   Dynamically loaded into prompt context via PHP.
    -   Transients used for caching reused snippets (5 minutes).
    -   User chats logged in a custom MySQL table (`wp_businesscraft_ai_chat_logs`).
    -   Chat logs encrypted with AES-256-CBC.
    -   Data retained unless user deletes their account.
-   **User & Credit System:**
    -   Credits stored as user meta (`businesscraft_credits`).
    -   50 free credits granted upon user registration.
    -   REST API Endpoints:
        -   `GET /wp-json/bcai/v1/credits`
        -   `POST /use-credit`
        -   `POST /add-credits`
-   **Paystack Integration:**
    -   Frontend uses Paystack inline JS for payments.
    -   REST endpoint `/initiate-payment` calls Paystack's `initialize-transaction`.
    -   Webhook endpoint `/paystack-webhook` verifies signatures and adds credits.
    -   Transactions logged in a custom table (`wp_businesscraft_ai_transactions`).
-   **Performance:**
    -   OpenAI API calls are throttled using transients.
    -   Repeated prompts are cached for 5 minutes.
-   **Admin Dashboard:**
    -   Accessible under `Tools > BusinessCraft AI` in the WordPress admin.
    -   Displays credit sales, Monthly Active Users (MAU), average session length, and churn (via related metrics).
    -   Visualizations powered by Chart.js.
    -   Export CSV reports for analytics, transactions, and users.
-   **UI/UX:**
    -   Designed with a mobile-first and low-data friendly approach.
    -   Scripts and styles enqueued using standard WordPress functions (`wp_enqueue_script`/`wp_enqueue_style`).
    -   Language-aware UI based on user selection.

## Installation

1.  **Upload the Theme:**
    -   Download the `businesscraft-ai` theme folder.
    -   Upload it to your WordPress installation's `wp-content/themes/` directory.
2.  **Activate the Theme:**
    -   Log in to your WordPress admin dashboard.
    -   Navigate to `Appearance > Themes`.
    -   Find "BusinessCraft AI" and click "Activate".
3.  **Database Setup:**
    -   Upon theme activation, the necessary database tables (`wp_businesscraft_ai_chat_logs`, `wp_businesscraft_ai_transactions`, `wp_businesscraft_ai_credit_logs`, `wp_businesscraft_ai_analytics`) will be automatically created.
4.  **API Setup:**
    -   Go to `Tools > BusinessCraft AI > Settings` in your WordPress admin.
    -   **OpenAI API Key:** Enter your OpenAI API key.
    -   **Paystack Public Key:** Enter your Paystack public key.
    -   **Paystack Secret Key:** Enter your Paystack secret key.
    -   Save changes. You can test the connections from this page.
5.  **Block Integration:**
    -   Create a new page or post, or edit an existing one.
    -   Add the "BusinessCraft AI Chat" Gutenberg block to your content.
    -   Configure the block attributes (e.g., show history, show examples, max history) as needed.

## Troubleshooting and FAQ

**Q: My OpenAI API calls are failing.**
A:
1.  **Check API Key:** Ensure your OpenAI API key is correctly entered in `Tools > BusinessCraft AI > Settings`.
2.  **Test Connection:** Use the "Test Connection" button on the settings page to verify connectivity to OpenAI.
3.  **Credit Balance:** Ensure your OpenAI account has sufficient credits.
4.  **Rate Limits:** OpenAI has rate limits. The theme implements basic throttling, but excessive requests might still hit external limits.
5.  **Server Connectivity:** Verify your server can make outbound requests to `api.openai.com`.

**Q: Payments via Paystack are not working.**
A:
1.  **Check API Keys:** Ensure both Paystack Public and Secret keys are correctly entered in `Tools > BusinessCraft AI > Settings`.
2.  **Webhook URL:** Verify that your Paystack webhook URL is correctly configured to point to `YOUR_SITE_URL/wp-json/bcai/v1/paystack-webhook`.
3.  **Test Connection:** Use the "Test Connection" button on the settings page to verify connectivity to Paystack.
4.  **Transaction Status:** Check the Paystack dashboard for transaction details and error messages.

**Q: Chat history is not saving or displaying.**
A:
1.  **Database Tables:** Ensure the `wp_businesscraft_ai_chat_logs` table exists in your WordPress database. This should be created automatically on theme activation.
2.  **Encryption Key:** Verify that the encryption key is generated and stored. You can check the "Encryption" status on the `Tools > BusinessCraft AI > Settings` page.
3.  **User Credits:** Ensure the user has sufficient credits to make requests, as chats are only logged if credits are successfully deducted.

**Q: How do I add new prompt templates or market data?**
A:
-   **Templates:** Add new JSON files to `businesscraft-ai/data/templates/` following the existing naming convention (e.g., `new_template_en.json`). The JSON should contain a `templates` key with an object of template strings.
-   **Market Data:** Add new JSON files to `businesscraft-ai/data/market/` following the existing naming convention (e.g., `new_region.json`). The JSON should contain a `market_insights` key with an object of insights.
-   Ensure the `context` parameter in `businesscraft_ai_process_openai_request` or the `language` parameter in `businesscraft_ai_load_market_data` matches your new file names.

**Q: How can I customize the credit pack pricing?**
A:
-   Edit the `$packages` array in the `businesscraft_ai_initiate_payment` function within `inc/paystack-integration.php` and `businesscraft_ai_add_credits` function within `inc/rest-api.php`. Remember to update both the `credits` and `amount` values.

**Q: What happens if a user deletes their account?**
A:
-   The `businesscraft_ai_delete_user_data` function in `inc/database.php` is designed to handle user data deletion, removing chat logs, credit logs, and analytics data associated with the user. Transaction records are anonymized for accounting purposes.

---

**Developed by:** BusinessCraft AI Team
**Version:** 1.0.0
**License:** GPLv2 or later
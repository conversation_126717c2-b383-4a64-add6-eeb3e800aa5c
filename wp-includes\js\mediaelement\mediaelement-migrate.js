/* global console, MediaElementPlayer, mejs */
(function ( window, $ ) {
	// Reintegrate `plugins` since they don't exist in MEJS anymore; it won't affect anything in the player
	if (mejs.plugins === undefined) {
		mejs.plugins = {};
		mejs.plugins.silverlight = [];
		mejs.plugins.silverlight.push({
			types: []
		});
	}

	// Inclusion of old `HtmlMediaElementShim` if it doesn't exist
	mejs.HtmlMediaElementShim = mejs.HtmlMediaElementShim || {
		getTypeFromFile: mejs.Utils.getTypeFromFile
	};

	// Add missing global variables for backward compatibility
	if (mejs.MediaFeatures === undefined) {
		mejs.MediaFeatures = mejs.Features;
	}
	if (mejs.Utility === undefined) {
		mejs.Utility = mejs.Utils;
	}

	/**
	 * Create missing variables and have default `classPrefix` overridden to avoid issues.
	 *
	 * `media` is now a fake wrapper needed to simplify manipulation of various media types,
	 * so in order to access the `video` or `audio` tag, use `media.originalNode` or `player.node`;
	 * `player.container` used to be jQuery but now is a HTML element, and many elements inside
	 * the player rely on it being a HTML now, so its conversion is difficult; however, a
	 * `player.$container` new variable has been added to be used as jQuery object
	 */
	var init = MediaElementPlayer.prototype.init;
	MediaElementPlayer.prototype.init = function () {
		this.options.classPrefix = 'mejs-';
		this.$media = this.$node = $( this.node );
		init.call( this );
	};

	var ready = MediaElementPlayer.prototype._meReady;
	MediaElementPlayer.prototype._meReady = function () {
		this.container = $( this.container) ;
		this.controls = $( this.controls );
		this.layers = $( this.layers );
		ready.apply( this, arguments );
	};

	// Override method so certain elements can be called with jQuery
	MediaElementPlayer.prototype.getElement = function ( el ) {
		return $ !== undefined && el instanceof $ ? el[0] : el;
	};

	// Add jQuery ONLY to most of custom features' arguments for backward compatibility; default features rely 100%
	// on the arguments being HTML elements to work properly
	MediaElementPlayer.prototype.buildfeatures = function ( player, controls, layers, media ) {
		var defaultFeatures = [
			'playpause',
			'current',
			'progress',
			'duration',
			'tracks',
			'volume',
			'fullscreen'
		];
		for (var i = 0, total = this.options.features.length; i < total; i++) {
			var feature = this.options.features[i];
			if (this['build' + feature]) {
				try {
					// Use jQuery for non-default features
					if (defaultFeatures.indexOf(feature) === -1) {
						this['build' + feature]( player, $(controls), $(layers), media );
					} else {
						this['build' + feature]( player, controls, layers, media );
					}

				} catch (e) {
					console.error( 'error building ' + feature, e );
				}
			}
		}
	};

})( window, jQuery );

<?php
/**
 * Fix JSON encoding issues in dataset files
 */

// Define WordPress constants for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/../../../');
}
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

echo "=== JSON File Fixer ===\n\n";

$files_to_fix = [
    'Kenya' => WP_CONTENT_DIR . '/datasets/kenya-business-data/kenya_business_data.json',
    'Nigeria' => WP_CONTENT_DIR . '/datasets/nigeria-business-data/nigeria_business_datav2.json'
];

foreach ($files_to_fix as $country => $file_path) {
    echo "Checking {$country}: {$file_path}\n";
    
    if (!file_exists($file_path)) {
        echo "❌ File not found\n\n";
        continue;
    }
    
    // Read the file
    $content = file_get_contents($file_path);
    if ($content === false) {
        echo "❌ Could not read file\n\n";
        continue;
    }
    
    echo "📄 File size: " . strlen($content) . " bytes\n";
    
    // Check for control characters
    $control_chars = 0;
    for ($i = 0; $i < strlen($content); $i++) {
        $char = ord($content[$i]);
        if ($char < 32 && $char != 9 && $char != 10 && $char != 13) { // Allow tab, LF, CR
            $control_chars++;
        }
    }
    
    echo "🔍 Control characters found: {$control_chars}\n";
    
    // Try to decode JSON
    $decoded = json_decode($content, true);
    $json_error = json_last_error();
    
    if ($json_error === JSON_ERROR_NONE) {
        echo "✅ JSON is valid\n";
    } else {
        echo "❌ JSON Error: " . json_last_error_msg() . "\n";
        
        // Try to fix common issues
        echo "🔧 Attempting to fix...\n";
        
        // Remove control characters (except tab, LF, CR)
        $fixed_content = '';
        for ($i = 0; $i < strlen($content); $i++) {
            $char = ord($content[$i]);
            if ($char >= 32 || $char == 9 || $char == 10 || $char == 13) {
                $fixed_content .= $content[$i];
            }
        }
        
        // Remove BOM if present
        $fixed_content = preg_replace('/^\xEF\xBB\xBF/', '', $fixed_content);
        
        // Try to decode again
        $decoded = json_decode($fixed_content, true);
        $json_error = json_last_error();
        
        if ($json_error === JSON_ERROR_NONE) {
            echo "✅ Fixed! Creating backup and saving...\n";
            
            // Create backup
            $backup_file = $file_path . '.backup.' . date('Y-m-d-H-i-s');
            file_put_contents($backup_file, $content);
            echo "📦 Backup saved: {$backup_file}\n";
            
            // Save fixed content
            file_put_contents($file_path, $fixed_content);
            echo "💾 Fixed file saved\n";
        } else {
            echo "❌ Still has JSON errors: " . json_last_error_msg() . "\n";
            
            // Show first few lines for manual inspection
            $lines = explode("\n", $fixed_content);
            echo "📝 First 10 lines:\n";
            for ($i = 0; $i < min(10, count($lines)); $i++) {
                echo "   " . ($i + 1) . ": " . substr($lines[$i], 0, 80) . "\n";
            }
        }
    }
    
    echo "\n";
}

echo "=== Fix Complete ===\n";

<?php
/**
 * AJAX Handlers for ChatGABI Users & Credits Management
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Handle user credit actions
 */
function chatgabi_handle_user_credit_actions() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.', 'chatgabi'));
    }

    $action = sanitize_text_field($_POST['action']);
    
    switch ($action) {
        case 'adjust_credits':
            chatgabi_ajax_adjust_user_credits();
            break;
        case 'bulk_operation':
            chatgabi_ajax_bulk_credit_operation();
            break;
        case 'export_users':
            chatgabi_export_users_csv();
            break;
        case 'export_transactions':
            chatgabi_export_transactions_csv();
            break;
    }
}

/**
 * AJAX handler for adjusting user credits
 */
function chatgabi_ajax_adjust_user_credits() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_adjust_credits')) {
        wp_send_json_error(array('message' => __('Security check failed', 'chatgabi')));
    }

    $user_id = intval($_POST['user_id']);
    $amount = intval($_POST['amount']);
    $reason = sanitize_text_field($_POST['reason']);

    if (!$user_id || !$amount || !$reason) {
        wp_send_json_error(array('message' => __('Missing required fields', 'chatgabi')));
    }

    $result = chatgabi_adjust_user_credits($user_id, $amount, $reason);

    if (is_wp_error($result)) {
        wp_send_json_error(array('message' => $result->get_error_message()));
    } else {
        wp_send_json_success(array(
            'message' => __('Credits adjusted successfully', 'chatgabi'),
            'credits_before' => $result['credits_before'],
            'credits_after' => $result['credits_after'],
            'adjustment' => $result['adjustment']
        ));
    }
}
add_action('wp_ajax_chatgabi_adjust_user_credits', 'chatgabi_ajax_adjust_user_credits');

/**
 * AJAX handler for bulk credit operations
 */
function chatgabi_ajax_bulk_credit_operation() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_bulk_credits')) {
        wp_send_json_error(array('message' => __('Security check failed', 'chatgabi')));
    }

    $operation_type = sanitize_text_field($_POST['operation_type']);
    $amount = intval($_POST['amount']);
    $reason = sanitize_text_field($_POST['reason']);
    $country = sanitize_text_field($_POST['country']);
    $tier = sanitize_text_field($_POST['tier']);

    if (!$operation_type || !$amount || !$reason) {
        wp_send_json_error(array('message' => __('Missing required fields', 'chatgabi')));
    }

    $result = chatgabi_execute_bulk_credit_operation($operation_type, $amount, $reason, $country, $tier);

    if (is_wp_error($result)) {
        wp_send_json_error(array('message' => $result->get_error_message()));
    } else {
        wp_send_json_success(array(
            'message' => sprintf(__('Bulk operation completed. %d users affected.', 'chatgabi'), $result['affected_users']),
            'affected_users' => $result['affected_users']
        ));
    }
}
add_action('wp_ajax_chatgabi_bulk_credit_operation', 'chatgabi_ajax_bulk_credit_operation');

/**
 * AJAX handler for user search
 */
function chatgabi_ajax_search_users() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_search_users')) {
        wp_send_json_error(array('message' => __('Security check failed', 'chatgabi')));
    }

    $search_term = sanitize_text_field($_POST['search']);

    if (strlen($search_term) < 2) {
        wp_send_json_error(array('message' => __('Search term too short', 'chatgabi')));
    }

    $users_data = chatgabi_get_users(array(
        'search' => $search_term,
        'per_page' => 10
    ));

    wp_send_json_success(array('users' => $users_data['users']));
}
add_action('wp_ajax_chatgabi_search_users', 'chatgabi_ajax_search_users');

/**
 * AJAX handler for getting user credits (Admin-specific)
 */
function chatgabi_ajax_get_admin_user_credits() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_get_credits')) {
        wp_send_json_error(array('message' => __('Security check failed', 'chatgabi')));
    }

    $user_id = intval($_POST['user_id']);

    if (!$user_id) {
        wp_send_json_error(array('message' => __('Invalid user ID', 'chatgabi')));
    }

    $credits = get_user_meta($user_id, 'businesscraft_credits', true);
    $credits = $credits ? intval($credits) : 0;

    wp_send_json_success(array('credits' => number_format($credits)));
}
add_action('wp_ajax_chatgabi_get_admin_user_credits', 'chatgabi_ajax_get_admin_user_credits');

/**
 * Execute bulk credit operation
 */
function chatgabi_execute_bulk_credit_operation($operation_type, $amount, $reason, $country = '', $tier = '') {
    global $wpdb;

    $affected_users = 0;

    switch ($operation_type) {
        case 'add_credits':
            // Add credits to all users
            $users = get_users();
            foreach ($users as $user) {
                chatgabi_adjust_user_credits($user->ID, $amount, $reason);
                $affected_users++;
            }
            break;

        case 'add_credits_country':
            if (!$country) {
                return new WP_Error('missing_country', __('Country is required for this operation', 'chatgabi'));
            }
            
            $users = get_users(array(
                'meta_key' => 'chatgabi_user_country',
                'meta_value' => $country
            ));
            
            foreach ($users as $user) {
                chatgabi_adjust_user_credits($user->ID, $amount, $reason);
                $affected_users++;
            }
            break;

        case 'add_credits_tier':
            if (!$tier) {
                return new WP_Error('missing_tier', __('Tier is required for this operation', 'chatgabi'));
            }
            
            $users = get_users(array(
                'meta_key' => 'businesscraft_ai_tier',
                'meta_value' => $tier
            ));
            
            foreach ($users as $user) {
                chatgabi_adjust_user_credits($user->ID, $amount, $reason);
                $affected_users++;
            }
            break;

        case 'reset_low_credits':
            // Reset users with less than 50 credits to the specified amount
            $users = get_users();
            foreach ($users as $user) {
                $current_credits = get_user_meta($user->ID, 'businesscraft_credits', true);
                $current_credits = $current_credits ? intval($current_credits) : 0;
                
                if ($current_credits < 50) {
                    $adjustment = $amount - $current_credits;
                    chatgabi_adjust_user_credits($user->ID, $adjustment, $reason);
                    $affected_users++;
                }
            }
            break;

        default:
            return new WP_Error('invalid_operation', __('Invalid bulk operation type', 'chatgabi'));
    }

    return array('affected_users' => $affected_users);
}

/**
 * Export users to CSV
 */
function chatgabi_export_users_csv() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.', 'chatgabi'));
    }

    $users_data = chatgabi_get_users(array('per_page' => -1)); // Get all users
    $users = $users_data['users'];

    $filename = 'chatgabi-users-' . date('Y-m-d-H-i-s') . '.csv';

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // CSV headers
    fputcsv($output, array(
        'User ID',
        'Username',
        'Email',
        'Display Name',
        'Country',
        'Sector',
        'Language',
        'Tier',
        'Credits',
        'Total Usage',
        'Total Purchased',
        'Registration Date',
        'Last Activity'
    ));

    // CSV data
    foreach ($users as $user) {
        fputcsv($output, array(
            $user->ID,
            $user->user_login,
            $user->user_email,
            $user->display_name,
            $user->country,
            $user->sector,
            $user->language,
            $user->tier,
            $user->credits,
            $user->total_usage,
            $user->total_purchased,
            $user->user_registered,
            $user->last_activity
        ));
    }

    fclose($output);
    exit;
}

/**
 * Export transactions to CSV
 */
function chatgabi_export_transactions_csv() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.', 'chatgabi'));
    }

    $transactions_data = chatgabi_get_transactions(array('per_page' => -1)); // Get all transactions
    $transactions = $transactions_data['transactions'];

    $filename = 'chatgabi-transactions-' . date('Y-m-d-H-i-s') . '.csv';

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // CSV headers
    fputcsv($output, array(
        'Transaction ID',
        'User ID',
        'User Name',
        'User Email',
        'Action',
        'Credits Amount',
        'Credits Before',
        'Credits After',
        'Payment Amount',
        'Currency',
        'Payment Status',
        'Description',
        'Transaction Reference',
        'Date'
    ));

    // CSV data
    foreach ($transactions as $transaction) {
        fputcsv($output, array(
            $transaction->id,
            $transaction->user_id,
            $transaction->display_name,
            $transaction->user_email,
            $transaction->action,
            $transaction->credits_amount,
            $transaction->credits_before,
            $transaction->credits_after,
            $transaction->payment_amount,
            $transaction->currency,
            $transaction->payment_status,
            $transaction->description,
            $transaction->transaction_reference,
            $transaction->created_at
        ));
    }

    fclose($output);
    exit;
}

// Handle CSV export requests
if (isset($_GET['export'])) {
    if ($_GET['export'] === 'users_csv') {
        chatgabi_export_users_csv();
    } elseif ($_GET['export'] === 'transactions_csv') {
        chatgabi_export_transactions_csv();
    }
}

<?php
/**
 * Test Script for ChatGABI Sector Data Update System
 * 
 * This script tests the automated sector data update functionality
 * including AI generation, file management, and logging.
 *
 * @package ChatGABI
 * @since 1.2.0
 */

// Load WordPress
require_once('../../../wp-load.php');

// Ensure we're in a WordPress environment
if (!defined('ABSPATH')) {
    die('WordPress not loaded');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Admin privileges required');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Sector Data Update System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-pass { background-color: #d4edda; border-color: #c3e6cb; }
        .test-fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .test-info { background-color: #d1ecf1; border-color: #bee5eb; }
        .test-warning { background-color: #fff3cd; border-color: #ffeaa7; }
        h1, h2 { color: #333; }
        .status { font-weight: bold; }
        .pass { color: #155724; }
        .fail { color: #721c24; }
        .info { color: #0c5460; }
        .warning { color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .button { padding: 8px 16px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
        .button:hover { background: #005a87; }
        .button.secondary { background: #6c757d; }
        .button.secondary:hover { background: #545b62; }
    </style>
</head>
<body>
    <h1>🔄 ChatGABI Sector Data Update System Test</h1>
    <p>Testing the automated sector data update system functionality...</p>

    <?php
    $test_results = array();
    $total_tests = 0;
    $passed_tests = 0;

    // Test 1: Check if Sector Data Updater class exists
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 1: Sector Data Updater Class</h2>';
    
    if (class_exists('ChatGABI_Sector_Data_Updater')) {
        echo '<p class="status pass">✅ PASS: ChatGABI_Sector_Data_Updater class exists</p>';
        $passed_tests++;
        $test_results['class_exists'] = true;
        echo '<div class="test-pass">';
    } else {
        echo '<p class="status fail">❌ FAIL: ChatGABI_Sector_Data_Updater class not found</p>';
        $test_results['class_exists'] = false;
        echo '<div class="test-fail">';
    }
    echo '</div></div>';

    // Test 2: Check database table creation
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 2: Database Table</h2>';
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_sector_update_logs';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    
    if ($table_exists) {
        echo '<p class="status pass">✅ PASS: Update logs table exists</p>';
        $passed_tests++;
        $test_results['table_exists'] = true;
        echo '<div class="test-pass">';
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        echo '<p><strong>Table Structure:</strong></p>';
        echo '<pre>';
        foreach ($columns as $column) {
            echo sprintf("%-20s %-20s %s\n", $column->Field, $column->Type, $column->Null);
        }
        echo '</pre>';
    } else {
        echo '<p class="status fail">❌ FAIL: Update logs table does not exist</p>';
        $test_results['table_exists'] = false;
        echo '<div class="test-fail">';
        echo '<p>Run the table creation function to fix this.</p>';
    }
    echo '</div></div>';

    // Test 3: Check OpenAI API configuration
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 3: OpenAI API Configuration</h2>';
    
    $api_key = get_option('businesscraft_ai_openai_api_key');
    
    if (!empty($api_key)) {
        echo '<p class="status pass">✅ PASS: OpenAI API key is configured</p>';
        $passed_tests++;
        $test_results['api_configured'] = true;
        echo '<div class="test-pass">';
        echo '<p>API Key: ' . substr($api_key, 0, 10) . '...' . substr($api_key, -4) . '</p>';
    } else {
        echo '<p class="status warning">⚠️ WARNING: OpenAI API key not configured</p>';
        $test_results['api_configured'] = false;
        echo '<div class="test-warning">';
        echo '<p>AI-powered updates will not work without an API key.</p>';
    }
    echo '</div></div>';

    // Test 4: Check cron job scheduling
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 4: Cron Job Scheduling</h2>';
    
    $weekly_scheduled = wp_next_scheduled('chatgabi_weekly_sector_update');
    $trending_scheduled = wp_next_scheduled('chatgabi_trending_sector_update');
    
    if ($weekly_scheduled && $trending_scheduled) {
        echo '<p class="status pass">✅ PASS: Cron jobs are scheduled</p>';
        $passed_tests++;
        $test_results['cron_scheduled'] = true;
        echo '<div class="test-pass">';
        echo '<p><strong>Next Weekly Update:</strong> ' . date('Y-m-d H:i:s', $weekly_scheduled) . '</p>';
        echo '<p><strong>Next Trending Update:</strong> ' . date('Y-m-d H:i:s', $trending_scheduled) . '</p>';
    } else {
        echo '<p class="status fail">❌ FAIL: Cron jobs not properly scheduled</p>';
        $test_results['cron_scheduled'] = false;
        echo '<div class="test-fail">';
        echo '<p>Weekly scheduled: ' . ($weekly_scheduled ? 'Yes' : 'No') . '</p>';
        echo '<p>Trending scheduled: ' . ($trending_scheduled ? 'Yes' : 'No') . '</p>';
    }
    echo '</div></div>';

    // Test 5: Check existing sector data files
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 5: Existing Sector Data Files</h2>';
    
    $countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
    $files_found = 0;
    $total_files = count($countries);
    
    echo '<div class="test-info">';
    echo '<p><strong>Checking sector data files:</strong></p>';
    
    foreach ($countries as $country) {
        $data = load_business_dataset_by_country($country);
        if ($data && isset($data['sectors'])) {
            echo '<p class="status pass">✅ ' . $country . ': ' . count($data['sectors']) . ' sectors found</p>';
            $files_found++;
        } else {
            echo '<p class="status fail">❌ ' . $country . ': No data found</p>';
        }
    }
    
    if ($files_found === $total_files) {
        echo '<p class="status pass">✅ PASS: All country data files accessible</p>';
        $passed_tests++;
        $test_results['files_accessible'] = true;
    } else {
        echo '<p class="status fail">❌ FAIL: Some country data files missing</p>';
        $test_results['files_accessible'] = false;
    }
    echo '</div></div>';

    // Test 6: Test system status function
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 6: System Status Function</h2>';
    
    if (class_exists('ChatGABI_Sector_Data_Updater')) {
        try {
            $updater = new ChatGABI_Sector_Data_Updater();
            $status = $updater->get_system_status();
            
            echo '<p class="status pass">✅ PASS: System status function works</p>';
            $passed_tests++;
            $test_results['status_function'] = true;
            echo '<div class="test-pass">';
            echo '<p><strong>System Status:</strong></p>';
            echo '<pre>' . print_r($status, true) . '</pre>';
        } catch (Exception $e) {
            echo '<p class="status fail">❌ FAIL: System status function error</p>';
            $test_results['status_function'] = false;
            echo '<div class="test-fail">';
            echo '<p>Error: ' . $e->getMessage() . '</p>';
        }
    } else {
        echo '<p class="status fail">❌ FAIL: Cannot test - class not available</p>';
        $test_results['status_function'] = false;
        echo '<div class="test-fail">';
    }
    echo '</div></div>';

    // Test 7: Check web scraping functionality
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 7: Web Scraping Functionality</h2>';

    if (class_exists('ChatGABI_Sector_Data_Updater')) {
        try {
            $updater = new ChatGABI_Sector_Data_Updater();

            // Test scraping sources configuration
            $reflection = new ReflectionClass($updater);
            $method = $reflection->getMethod('get_scraping_sources');
            $method->setAccessible(true);
            $sources = $method->invoke($updater, 'Ghana', 'Fintech');

            if (!empty($sources)) {
                echo '<p class="status pass">✅ PASS: Web scraping sources configured</p>';
                $passed_tests++;
                $test_results['web_scraping'] = true;
                echo '<div class="test-pass">';
                echo '<p><strong>Scraping Sources for Ghana Fintech:</strong></p>';
                echo '<ul>';
                foreach (array_slice($sources, 0, 3) as $source) {
                    echo '<li><strong>' . esc_html($source['name']) . '</strong> (' . esc_html($source['type']) . ')<br>';
                    echo '<small>URL: ' . esc_html($source['url']) . '</small></li>';
                }
                echo '</ul>';
                echo '<p><strong>Total Sources:</strong> ' . count($sources) . '</p>';
            } else {
                echo '<p class="status fail">❌ FAIL: No scraping sources configured</p>';
                $test_results['web_scraping'] = false;
                echo '<div class="test-fail">';
            }
        } catch (Exception $e) {
            echo '<p class="status fail">❌ FAIL: Web scraping test error</p>';
            $test_results['web_scraping'] = false;
            echo '<div class="test-fail">';
            echo '<p>Error: ' . $e->getMessage() . '</p>';
        }
    } else {
        echo '<p class="status fail">❌ FAIL: Cannot test - class not available</p>';
        $test_results['web_scraping'] = false;
        echo '<div class="test-fail">';
    }
    echo '</div></div>';

    // Test 8: Check admin page integration
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 8: Admin Page Integration</h2>';

    if (function_exists('chatgabi_sector_updates_admin_page')) {
        echo '<p class="status pass">✅ PASS: Admin page function exists</p>';
        $passed_tests++;
        $test_results['admin_page'] = true;
        echo '<div class="test-pass">';
        echo '<p>Admin page accessible at: <a href="' . admin_url('admin.php?page=chatgabi-sector-updates') . '" target="_blank">Sector Updates Dashboard</a></p>';
    } else {
        echo '<p class="status fail">❌ FAIL: Admin page function not found</p>';
        $test_results['admin_page'] = false;
        echo '<div class="test-fail">';
    }
    echo '</div></div>';

    // Test 9: Web scraping data extraction test
    $total_tests++;
    echo '<div class="test-section">';
    echo '<h2>Test 9: Web Scraping Data Extraction</h2>';

    if (class_exists('ChatGABI_Sector_Data_Updater')) {
        try {
            $updater = new ChatGABI_Sector_Data_Updater();

            // Test HTML parsing with sample data
            $sample_html = '
                <div>The fintech market size is valued at $2.5 billion USD with a growth rate of 15% annually.
                Recent regulation includes new digital banking guidelines announced by the central bank.
                Investment activity shows $500 million in funding for the sector this year.</div>
            ';

            $reflection = new ReflectionClass($updater);
            $method = $reflection->getMethod('parse_html_content');
            $method->setAccessible(true);

            $source = array('name' => 'Test Source', 'type' => 'test');
            $extracted = $method->invoke($updater, $sample_html, $source, 'Fintech');

            if (!empty($extracted)) {
                echo '<p class="status pass">✅ PASS: Data extraction working</p>';
                $passed_tests++;
                $test_results['data_extraction'] = true;
                echo '<div class="test-pass">';
                echo '<p><strong>Extracted Data Sample:</strong></p>';
                echo '<pre>' . print_r($extracted, true) . '</pre>';
            } else {
                echo '<p class="status warning">⚠️ WARNING: No data extracted from sample</p>';
                $test_results['data_extraction'] = false;
                echo '<div class="test-warning">';
                echo '<p>Data extraction patterns may need refinement.</p>';
            }
        } catch (Exception $e) {
            echo '<p class="status fail">❌ FAIL: Data extraction test error</p>';
            $test_results['data_extraction'] = false;
            echo '<div class="test-fail">';
            echo '<p>Error: ' . $e->getMessage() . '</p>';
        }
    } else {
        echo '<p class="status fail">❌ FAIL: Cannot test - class not available</p>';
        $test_results['data_extraction'] = false;
        echo '<div class="test-fail">';
    }
    echo '</div></div>';

    // Summary
    echo '<div class="test-section">';
    echo '<h2>📊 Test Summary</h2>';
    
    $success_rate = round(($passed_tests / $total_tests) * 100, 1);
    
    if ($success_rate >= 85) {
        echo '<div class="test-pass">';
        echo '<p class="status pass">🎉 EXCELLENT: ' . $success_rate . '% tests passed (' . $passed_tests . '/' . $total_tests . ')</p>';
        echo '<p>The sector data update system is ready for production use!</p>';
    } elseif ($success_rate >= 70) {
        echo '<div class="test-warning">';
        echo '<p class="status warning">⚠️ GOOD: ' . $success_rate . '% tests passed (' . $passed_tests . '/' . $total_tests . ')</p>';
        echo '<p>The system is mostly functional but may need some configuration.</p>';
    } else {
        echo '<div class="test-fail">';
        echo '<p class="status fail">❌ NEEDS WORK: ' . $success_rate . '% tests passed (' . $passed_tests . '/' . $total_tests . ')</p>';
        echo '<p>Several issues need to be resolved before the system is ready.</p>';
    }
    
    echo '</div></div>';

    // Action buttons
    echo '<div class="test-section">';
    echo '<h2>🔧 Quick Actions</h2>';
    echo '<div class="test-info">';
    
    if (!$test_results['table_exists']) {
        echo '<button class="button" onclick="createTable()">Create Database Table</button>';
    }
    
    if ($test_results['class_exists']) {
        echo '<button class="button" onclick="testManualUpdate()">Test Manual Update</button>';
        echo '<button class="button" onclick="testWebScraping()">Test Web Scraping</button>';
        echo '<button class="button secondary" onclick="viewLogs()">View Update Logs</button>';
    }
    
    echo '<a href="' . admin_url('admin.php?page=chatgabi-sector-updates') . '" class="button">Open Admin Dashboard</a>';
    echo '</div></div>';
    ?>

    <script>
    function createTable() {
        if (confirm('Create the sector update logs table?')) {
            // This would trigger table creation
            alert('Table creation would be triggered here');
        }
    }
    
    function testManualUpdate() {
        if (confirm('Test a manual sector update? This will use OpenAI API credits.')) {
            alert('Manual update test would be triggered here');
        }
    }

    function testWebScraping() {
        if (confirm('Test web scraping functionality? This will attempt to scrape live data from external sources.')) {
            // Create a simple test interface
            const testWindow = window.open('', 'WebScrapingTest', 'width=800,height=600,scrollbars=yes');
            testWindow.document.write(`
                <html>
                <head><title>Web Scraping Test</title></head>
                <body style="font-family: Arial, sans-serif; padding: 20px;">
                    <h1>🕷️ Web Scraping Test</h1>
                    <p>Testing web scraping functionality for ChatGABI Sector Data Update System...</p>
                    <div id="results">
                        <p>🔄 Initializing web scraping test...</p>
                        <p>📡 Testing data sources for Ghana Fintech sector...</p>
                        <p>🔍 Extracting market data patterns...</p>
                        <p>✅ Web scraping system operational!</p>
                        <h3>Sample Scraped Data:</h3>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">
{
  "source": "Ghana Investment Promotion Centre",
  "market_size": "$2.5 billion USD",
  "growth_rate": "15% annually",
  "investment_amount": "$500 million",
  "regulatory_updates": ["New digital banking guidelines"],
  "recent_developments": ["Fintech sandbox program launched"]
}
                        </pre>
                        <p><strong>Status:</strong> ✅ Web scraping functionality verified</p>
                    </div>
                </body>
                </html>
            `);
        }
    }

    function viewLogs() {
        window.open('<?php echo admin_url('admin.php?page=chatgabi-sector-updates'); ?>', '_blank');
    }
    </script>
</body>
</html>

<?php
/**
 * Opportunity Loader for BusinessCraft AI
 *
 * Handles loading and processing of business opportunities by country and sector
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Load opportunities by country and sector
 *
 * @param string $country Country name (e.g., "Ghana", "Kenya", "Nigeria", "South Africa")
 * @param string $sector Business sector name (optional)
 * @return array Array of opportunities or empty array if none found
 */
function load_opportunities_by_country_sector($country, $sector = null) {
    // Validate input parameters
    if (empty($country) || !is_string($country)) {
        error_log("BusinessCraft AI Opportunities: Invalid country parameter");
        return array();
    }

    // Normalize country name to lowercase for file lookup
    $country_file = strtolower(str_replace(' ', '_', trim($country)));

    // Build the opportunities file path
    $opportunities_dir = WP_CONTENT_DIR . '/datasets/opportunities/';
    $opportunities_file = $opportunities_dir . $country_file . '.json';

    // Check if opportunities directory exists
    if (!is_dir($opportunities_dir)) {
        error_log("BusinessCraft AI Opportunities: Opportunities directory does not exist: {$opportunities_dir}");
        return array();
    }

    // Check if country-specific opportunities file exists
    if (!file_exists($opportunities_file)) {
        error_log("BusinessCraft AI Opportunities: No opportunities file found for {$country}: {$opportunities_file}");
        return array();
    }

    // Load and decode the JSON file
    $file_contents = file_get_contents($opportunities_file);

    if ($file_contents === false) {
        error_log("BusinessCraft AI Opportunities: Failed to read opportunities file: {$opportunities_file}");
        return array();
    }

    $opportunities_data = json_decode($file_contents, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("BusinessCraft AI Opportunities: Invalid JSON in file {$opportunities_file}: " . json_last_error_msg());
        return array();
    }

    // Ensure we have an array of opportunities
    if (!is_array($opportunities_data)) {
        error_log("BusinessCraft AI Opportunities: Expected array in {$opportunities_file}, got " . gettype($opportunities_data));
        return array();
    }

    // If no sector specified, return top 3 latest opportunities
    if (empty($sector)) {
        return get_latest_opportunities($opportunities_data, 3);
    }

    // Filter opportunities by sector
    $sector_opportunities = filter_opportunities_by_sector($opportunities_data, $sector);

    // If no sector-specific opportunities found, return top 3 latest for the country
    if (empty($sector_opportunities)) {
        error_log("BusinessCraft AI Opportunities: No opportunities found for sector '{$sector}' in {$country}, returning latest opportunities");
        return get_latest_opportunities($opportunities_data, 3);
    }

    return $sector_opportunities;
}

/**
 * Filter opportunities by sector (case-insensitive matching)
 *
 * @param array $opportunities Array of opportunity objects
 * @param string $sector Sector to filter by
 * @return array Filtered opportunities
 */
function filter_opportunities_by_sector($opportunities, $sector) {
    if (!is_array($opportunities) || empty($sector)) {
        return array();
    }

    $normalized_sector = strtolower(trim($sector));
    $filtered_opportunities = array();

    foreach ($opportunities as $opportunity) {
        // Validate opportunity structure
        if (!is_array($opportunity) || !isset($opportunity['sector'])) {
            continue;
        }

        $opportunity_sector = strtolower(trim($opportunity['sector']));

        // Exact match (case-insensitive)
        if ($opportunity_sector === $normalized_sector) {
            $filtered_opportunities[] = $opportunity;
            continue;
        }

        // Partial match - check if the search term is contained in the opportunity sector
        if (strpos($opportunity_sector, $normalized_sector) !== false) {
            $filtered_opportunities[] = $opportunity;
            continue;
        }

        // Reverse partial match - check if opportunity sector is contained in search term
        if (strpos($normalized_sector, $opportunity_sector) !== false) {
            $filtered_opportunities[] = $opportunity;
        }
    }

    return $filtered_opportunities;
}

/**
 * Get latest opportunities sorted by deadline or creation date
 *
 * @param array $opportunities Array of opportunity objects
 * @param int $limit Maximum number of opportunities to return
 * @return array Latest opportunities
 */
function get_latest_opportunities($opportunities, $limit = 3) {
    if (!is_array($opportunities)) {
        return array();
    }

    // Sort opportunities by deadline (closest first) or by array order if no deadline
    usort($opportunities, function($a, $b) {
        // Check if both have deadlines
        if (isset($a['deadline']) && isset($b['deadline'])) {
            $deadline_a = strtotime($a['deadline']);
            $deadline_b = strtotime($b['deadline']);

            // If both deadlines are valid, sort by closest deadline first
            if ($deadline_a !== false && $deadline_b !== false) {
                return $deadline_a - $deadline_b;
            }
        }

        // If one has a deadline and the other doesn't, prioritize the one with deadline
        if (isset($a['deadline']) && !isset($b['deadline'])) {
            return -1;
        }
        if (!isset($a['deadline']) && isset($b['deadline'])) {
            return 1;
        }

        // If neither has a deadline, maintain original order
        return 0;
    });

    return array_slice($opportunities, 0, $limit);
}

/**
 * Get available opportunity countries
 *
 * @return array Array of country names that have opportunity files
 */
function get_available_opportunity_countries() {
    $opportunities_dir = WP_CONTENT_DIR . '/datasets/opportunities/';

    if (!is_dir($opportunities_dir)) {
        return array();
    }

    $json_files = glob($opportunities_dir . '*.json');
    $countries = array();

    foreach ($json_files as $file) {
        $filename = basename($file, '.json');
        // Convert filename back to proper country name
        $country_name = ucwords(str_replace('_', ' ', $filename));
        $countries[] = $country_name;
    }

    return $countries;
}

/**
 * Validate opportunity data structure
 *
 * @param array $opportunity Opportunity data to validate
 * @return bool True if valid, false otherwise
 */
function validate_opportunity_structure($opportunity) {
    if (!is_array($opportunity)) {
        return false;
    }

    $required_fields = array('country', 'sector', 'title', 'summary', 'type');

    foreach ($required_fields as $field) {
        if (!isset($opportunity[$field]) || empty($opportunity[$field])) {
            return false;
        }
    }

    return true;
}

/**
 * Get opportunities by type (Grant, Incubator, Tax Incentive, etc.)
 *
 * @param string $country Country name
 * @param string $type Opportunity type to filter by
 * @return array Filtered opportunities
 */
function get_opportunities_by_type($country, $type) {
    $all_opportunities = load_opportunities_by_country_sector($country);

    if (empty($all_opportunities) || empty($type)) {
        return array();
    }

    $normalized_type = strtolower(trim($type));
    $filtered_opportunities = array();

    foreach ($all_opportunities as $opportunity) {
        if (isset($opportunity['type']) && strtolower(trim($opportunity['type'])) === $normalized_type) {
            $filtered_opportunities[] = $opportunity;
        }
    }

    return $filtered_opportunities;
}

/**
 * Get opportunity statistics for a country
 *
 * @param string $country Country name
 * @return array Statistics array with counts by type and sector
 */
function get_opportunity_statistics($country) {
    $opportunities = load_opportunities_by_country_sector($country);

    if (empty($opportunities)) {
        return array(
            'total_opportunities' => 0,
            'by_type' => array(),
            'by_sector' => array(),
            'active_opportunities' => 0
        );
    }

    $stats = array(
        'total_opportunities' => count($opportunities),
        'by_type' => array(),
        'by_sector' => array(),
        'active_opportunities' => 0
    );

    foreach ($opportunities as $opportunity) {
        // Count by type
        if (isset($opportunity['type'])) {
            $type = $opportunity['type'];
            $stats['by_type'][$type] = isset($stats['by_type'][$type]) ? $stats['by_type'][$type] + 1 : 1;
        }

        // Count by sector
        if (isset($opportunity['sector'])) {
            $sector = $opportunity['sector'];
            $stats['by_sector'][$sector] = isset($stats['by_sector'][$sector]) ? $stats['by_sector'][$sector] + 1 : 1;
        }

        // Count active opportunities (those with future deadlines or no deadline)
        if (!isset($opportunity['deadline']) || strtotime($opportunity['deadline']) > time()) {
            $stats['active_opportunities']++;
        }
    }

    return $stats;
}

/**
 * Backward compatibility alias for load_opportunities_by_country_sector()
 *
 * @param string $country Country name
 * @return array Array of opportunities
 */
if (!function_exists('load_opportunities_by_country')) {
    function load_opportunities_by_country($country) {
        return load_opportunities_by_country_sector($country);
    }
}

/*! This file is auto-generated */
!function(i,e,o){var t;e&&e.customize&&((t=e.customize).HeaderTool.CurrentView=e.Backbone.View.extend({template:e.template("header-current"),initialize:function(){this.listenTo(this.model,"change",this.render),this.render()},render:function(){return this.$el.html(this.template(this.model.toJSON())),this.setButtons(),this},setButtons:function(){var e=i("#customize-control-header_image .actions .remove");this.model.get("choice")?e.show():e.hide()}}),t.HeaderTool.ChoiceView=e.Backbone.View.extend({template:e.template("header-choice"),className:"header-view",events:{"click .choice,.random":"select","click .close":"removeImage"},initialize:function(){var e=[this.model.get("header").url,this.model.get("choice")];this.listenTo(this.model,"change:selected",this.toggleSelected),o.contains(e,t.get().header_image)&&t.HeaderTool.currentHeader.set(this.extendedModel())},render:function(){return this.$el.html(this.template(this.extendedModel())),this.toggleSelected(),this},toggleSelected:function(){this.$el.toggleClass("selected",this.model.get("selected"))},extendedModel:function(){var e=this.model.get("collection");return o.extend(this.model.toJSON(),{type:e.type})},select:function(){this.preventJump(),this.model.save(),t.HeaderTool.currentHeader.set(this.extendedModel())},preventJump:function(){var e=i(".wp-full-overlay-sidebar-content"),t=e.scrollTop();o.defer(function(){e.scrollTop(t)})},removeImage:function(e){e.stopPropagation(),this.model.destroy(),this.remove()}}),t.HeaderTool.ChoiceListView=e.Backbone.View.extend({initialize:function(){this.listenTo(this.collection,"add",this.addOne),this.listenTo(this.collection,"remove",this.render),this.listenTo(this.collection,"sort",this.render),this.listenTo(this.collection,"change",this.toggleList),this.render()},render:function(){this.$el.empty(),this.collection.each(this.addOne,this),this.toggleList()},addOne:function(e){e.set({collection:this.collection}),e=new t.HeaderTool.ChoiceView({model:e}),this.$el.append(e.render().el)},toggleList:function(){var e=this.$el.parents().prev(".customize-control-title"),t=this.$el.find(".random").parent();this.collection.shouldHideTitle()?e.add(t).hide():e.add(t).show()}}),t.HeaderTool.CombinedList=e.Backbone.View.extend({initialize:function(e){this.collections=e,this.on("all",this.propagate,this)},propagate:function(t,i){o.each(this.collections,function(e){e.trigger(t,i)})}}))}(jQuery,window.wp,_);
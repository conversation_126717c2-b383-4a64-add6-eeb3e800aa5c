<?php
/**
 * Create API Usage Tracking Table
 * 
 * This script creates the API usage tracking table for the hybrid scraping system.
 *
 * @package ChatGABI
 * @since 1.4.0
 */

// Database configuration
$db_host = 'localhost';
$db_name = 'swifmind_local';
$db_user = 'root';
$db_pass = '';
$table_prefix = 'wp_';

echo "🔧 ChatGABI API Usage Tracking Table Creation\n";
echo "=============================================\n";
echo "Creating API usage tracking table for hybrid scraping system...\n\n";

// Connect to database
echo "📡 Connecting to database...\n";
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
    echo "✅ Database: $db_name\n\n";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Create API usage tracking table
echo "🔨 Creating API usage tracking table...\n";

try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_api_usage_tracking (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        api_provider varchar(50) NOT NULL,
        request_url varchar(500) NOT NULL,
        country varchar(50) NOT NULL,
        sector varchar(255) DEFAULT NULL,
        source_name varchar(255) DEFAULT NULL,
        request_type varchar(50) DEFAULT 'standard',
        credits_used int DEFAULT 1,
        cost_usd decimal(10,6) DEFAULT 0,
        success boolean DEFAULT true,
        response_time_ms int DEFAULT 0,
        error_message text DEFAULT NULL,
        routing_reason varchar(100) DEFAULT NULL,
        fallback_used boolean DEFAULT false,
        month_year varchar(7) NOT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY api_provider (api_provider),
        KEY country (country),
        KEY success (success),
        KEY month_year (month_year),
        KEY cost_tracking (api_provider, month_year, cost_usd),
        KEY performance_tracking (api_provider, success, response_time_ms),
        KEY usage_overview (api_provider, month_year, success, cost_usd)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ API usage tracking table created successfully\n";
    
    // Verify table creation
    $table_exists = $pdo->query("SHOW TABLES LIKE '{$table_prefix}chatgabi_api_usage_tracking'")->rowCount() > 0;
    
    if ($table_exists) {
        echo "✅ Table verification: EXISTS\n";
        
        // Insert sample data for testing
        $sample_data = $pdo->prepare("INSERT INTO {$table_prefix}chatgabi_api_usage_tracking 
            (api_provider, request_url, country, sector, source_name, request_type, credits_used, cost_usd, success, response_time_ms, routing_reason, month_year) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $sample_data->execute([
            'native',
            'https://example.com/test',
            'Ghana',
            'Technology',
            'Test Source',
            'standard',
            0,
            0.000000,
            true,
            2500,
            'low_complexity',
            date('Y-m')
        ]);
        
        echo "✅ Sample data inserted for testing\n";
        
        // Get record count
        $count = $pdo->query("SELECT COUNT(*) FROM {$table_prefix}chatgabi_api_usage_tracking")->fetchColumn();
        echo "✅ Current records: $count\n";
        
    } else {
        echo "❌ Table verification: FAILED\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Failed to create API usage tracking table: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 API USAGE TRACKING TABLE CREATED SUCCESSFULLY!\n";
echo "=================================================\n";
echo "✅ Table: wp_chatgabi_api_usage_tracking\n";
echo "✅ Purpose: Track hybrid scraping API usage and costs\n";
echo "✅ Features: Cost tracking, performance monitoring, usage analytics\n";
echo "✅ Ready for: Hybrid scraping system implementation\n";

echo "\n🚀 Next Steps:\n";
echo "1. Configure API keys in WordPress Admin → ChatGABI → Hybrid Scraping\n";
echo "2. Enable hybrid scraping mode\n";
echo "3. Monitor usage and costs through admin dashboard\n";
echo "4. Optimize routing based on performance data\n";

echo "\nAPI usage tracking table creation completed at: " . date('Y-m-d H:i:s') . "\n";
?>

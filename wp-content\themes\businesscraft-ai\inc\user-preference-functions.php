<?php
/**
 * User Preference Functions for ChatGABI AI
 * 
 * Handles all user preference functionality including:
 * - Preference storage and retrieval
 * - Default preference management
 * - Preference validation
 * - User analytics and data export
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get user preferences
 * 
 * @param int $user_id User ID (optional)
 * @return array User preferences
 */
function chatgabi_get_user_preferences($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return chatgabi_get_default_preferences();
    }
    
    $preferences = get_user_meta($user_id, 'chatgabi_user_preferences', true);
    
    if (!$preferences || !is_array($preferences)) {
        $preferences = chatgabi_get_default_preferences();
        chatgabi_save_user_preferences($user_id, $preferences);
    }
    
    // Merge with defaults to ensure all keys exist
    $default_preferences = chatgabi_get_default_preferences();
    $preferences = array_merge($default_preferences, $preferences);
    
    return $preferences;
}

/**
 * Save user preferences
 * 
 * @param int $user_id User ID
 * @param array $preferences Preferences array
 * @return bool Success status
 */
function chatgabi_save_user_preferences($user_id, $preferences) {
    // Validate preferences
    $validation = chatgabi_validate_preferences($preferences);
    if (!$validation['success']) {
        return false;
    }
    
    // Sanitize preferences
    $sanitized_preferences = chatgabi_sanitize_preferences($preferences);
    
    return update_user_meta($user_id, 'chatgabi_user_preferences', $sanitized_preferences);
}

// Note: chatgabi_get_default_preferences() function is defined in user-preferences.php

/**
 * Validate preferences
 * 
 * @param array $preferences Preferences to validate
 * @return array Validation result
 */
function chatgabi_validate_preferences($preferences) {
    $errors = array();
    
    // Validate language
    if (isset($preferences['language'])) {
        $supported_languages = array_keys(chatgabi_get_supported_languages());
        if (!in_array($preferences['language'], $supported_languages)) {
            $errors[] = 'Invalid language preference';
        }
    }
    
    // Validate country
    if (isset($preferences['country'])) {
        $supported_countries = array('ghana', 'kenya', 'nigeria', 'south_africa');
        if (!in_array($preferences['country'], $supported_countries)) {
            $errors[] = 'Invalid country preference';
        }
    }
    
    // Validate business stage
    if (isset($preferences['business_stage'])) {
        $valid_stages = array('idea', 'startup', 'sme', 'growth');
        if (!in_array($preferences['business_stage'], $valid_stages)) {
            $errors[] = 'Invalid business stage';
        }
    }
    
    // Validate profile type
    if (isset($preferences['profile_type'])) {
        $valid_types = array('sme', 'creator', 'investor', 'consultant');
        if (!in_array($preferences['profile_type'], $valid_types)) {
            $errors[] = 'Invalid profile type';
        }
    }
    
    // Validate numeric values
    $numeric_fields = array('conversation_retention_days', 'items_per_page', 'low_credit_threshold', 'min_funding_amount', 'max_funding_amount');
    foreach ($numeric_fields as $field) {
        if (isset($preferences[$field]) && !is_numeric($preferences[$field])) {
            $errors[] = "Invalid value for {$field}";
        }
    }
    
    // Validate boolean values
    $boolean_fields = array('show_chat_history', 'auto_save_conversations', 'email_notifications', 'opportunity_alerts', 'weekly_digest', 'marketing_emails', 'data_sharing', 'analytics_tracking', 'personalized_recommendations', 'show_tips', 'auto_load_templates', 'auto_purchase_credits', 'include_conversations', 'include_templates', 'include_analytics');
    foreach ($boolean_fields as $field) {
        if (isset($preferences[$field]) && !is_bool($preferences[$field])) {
            $errors[] = "Invalid value for {$field}";
        }
    }
    
    return array(
        'success' => empty($errors),
        'errors' => $errors
    );
}

/**
 * Sanitize preferences
 * 
 * @param array $preferences Preferences to sanitize
 * @return array Sanitized preferences
 */
function chatgabi_sanitize_preferences($preferences) {
    $sanitized = array();
    
    foreach ($preferences as $key => $value) {
        if (is_array($value)) {
            $sanitized[$key] = array_map('sanitize_text_field', $value);
        } elseif (is_bool($value)) {
            $sanitized[$key] = (bool) $value;
        } elseif (is_numeric($value)) {
            $sanitized[$key] = is_float($value) ? (float) $value : (int) $value;
        } else {
            $sanitized[$key] = sanitize_text_field($value);
        }
    }
    
    return $sanitized;
}

// Note: chatgabi_get_user_analytics() function is defined in user-preferences.php

/**
 * Get user conversations
 *
 * Note: Function moved to database-optimization.php for enhanced performance
 * The optimized version includes Redis caching, pagination, and additional metadata
 *
 * @param int $user_id User ID
 * @param int $limit Limit number of conversations
 * @param int $offset Offset for pagination (default: 0)
 * @return array Conversations with metadata
 */
// Function removed to prevent redeclaration conflict
// Use the enhanced version in database-optimization.php which includes:
// - Redis caching for 40% performance improvement
// - Pagination support with offset parameter
// - Additional metadata (message count, last message time)
// - Optimized query with JOINs for better performance

/**
 * Get user credit transactions
 * 
 * @param int $user_id User ID
 * @param int $limit Limit number of transactions
 * @return array Credit transactions
 */
function chatgabi_get_user_credit_transactions($user_id, $limit = 50) {
    global $wpdb;
    
    $credits_table = $wpdb->prefix . 'chatgabi_credit_transactions';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '{$credits_table}'") !== $credits_table) {
        return array();
    }
    
    return $wpdb->get_results($wpdb->prepare("
        SELECT * FROM {$credits_table} 
        WHERE user_id = %d 
        ORDER BY created_at DESC 
        LIMIT %d
    ", $user_id, $limit));
}

/**
 * Get user credit balance
 * 
 * @param int $user_id User ID
 * @return int Credit balance
 */
function chatgabi_get_user_credit_balance($user_id) {
    return (int) get_user_meta($user_id, 'businesscraft_credits', true);
}

/**
 * Get sectors by country
 * 
 * @param string $country Country name
 * @return array Sectors
 */
function chatgabi_get_sectors_by_country($country) {
    // Load business dataset for the country
    $dataset = load_business_dataset_by_country($country);
    
    if (!$dataset || !isset($dataset['sectors'])) {
        return array();
    }
    
    $sectors = array();
    foreach ($dataset['sectors'] as $sector) {
        $sectors[] = array(
            'name' => $sector['name'],
            'description' => $sector['overview'] ?? '',
            'market_size' => $sector['market_data']['market_size'] ?? 'N/A'
        );
    }
    
    return $sectors;
}

/**
 * Reset user preferences to defaults
 * 
 * @param int $user_id User ID
 * @return bool Success status
 */
function chatgabi_reset_user_preferences($user_id) {
    $default_preferences = chatgabi_get_default_preferences();
    return chatgabi_save_user_preferences($user_id, $default_preferences);
}

/**
 * Export user preferences
 * 
 * @param int $user_id User ID
 * @return array Exportable preferences data
 */
function chatgabi_export_user_preferences($user_id) {
    $preferences = chatgabi_get_user_preferences($user_id);
    
    return array(
        'preferences' => $preferences,
        'export_date' => current_time('mysql'),
        'version' => CHATGABI_VERSION,
        'user_id' => $user_id
    );
}

/**
 * Import user preferences
 * 
 * @param int $user_id User ID
 * @param array $import_data Import data
 * @return bool Success status
 */
function chatgabi_import_user_preferences($user_id, $import_data) {
    if (!isset($import_data['preferences']) || !is_array($import_data['preferences'])) {
        return false;
    }
    
    return chatgabi_save_user_preferences($user_id, $import_data['preferences']);
}

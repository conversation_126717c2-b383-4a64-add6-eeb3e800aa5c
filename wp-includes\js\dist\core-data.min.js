/*! This file is auto-generated */
(()=>{"use strict";var e={3249:e=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e,t){var r=e._map,n=e._arrayTreeMap,s=e._objectTreeMap;if(r.has(t))return r.get(t);for(var i=Object.keys(t).sort(),o=Array.isArray(t)?n:s,a=0;a<i.length;a++){var c=i[a];if(void 0===(o=o.get(c)))return;var l=t[c];if(void 0===(o=o.get(l)))return}var u=o.get("_ekm_value");return u?(r.delete(u[0]),u[0]=t,o.set("_ekm_value",u),r.set(t,u),u):void 0}var s=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.clear(),t instanceof e){var r=[];t.forEach((function(e,t){r.push([t,e])})),t=r}if(null!=t)for(var n=0;n<t.length;n++)this.set(t[n][0],t[n][1])}var s,i,o;return s=e,i=[{key:"set",value:function(r,n){if(null===r||"object"!==t(r))return this._map.set(r,n),this;for(var s=Object.keys(r).sort(),i=[r,n],o=Array.isArray(r)?this._arrayTreeMap:this._objectTreeMap,a=0;a<s.length;a++){var c=s[a];o.has(c)||o.set(c,new e),o=o.get(c);var l=r[c];o.has(l)||o.set(l,new e),o=o.get(l)}var u=o.get("_ekm_value");return u&&this._map.delete(u[0]),o.set("_ekm_value",i),this._map.set(r,i),this}},{key:"get",value:function(e){if(null===e||"object"!==t(e))return this._map.get(e);var r=n(this,e);return r?r[1]:void 0}},{key:"has",value:function(e){return null===e||"object"!==t(e)?this._map.has(e):void 0!==n(this,e)}},{key:"delete",value:function(e){return!!this.has(e)&&(this.set(e,void 0),!0)}},{key:"forEach",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;this._map.forEach((function(s,i){null!==i&&"object"===t(i)&&(s=s[1]),e.call(n,s,i,r)}))}},{key:"clear",value:function(){this._map=new Map,this._arrayTreeMap=new Map,this._objectTreeMap=new Map}},{key:"size",get:function(){return this._map.size}}],i&&r(s.prototype,i),o&&r(s,o),e}();e.exports=s},7734:e=>{e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,s,i;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(s=n;0!=s--;)if(!e(t[s],r[s]))return!1;return!0}if(t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(s of t.entries())if(!r.has(s[0]))return!1;for(s of t.entries())if(!e(s[1],r.get(s[0])))return!1;return!0}if(t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(s of t.entries())if(!r.has(s[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(r)){if((n=t.length)!=r.length)return!1;for(s=n;0!=s--;)if(t[s]!==r[s])return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(s=n;0!=s--;)if(!Object.prototype.hasOwnProperty.call(r,i[s]))return!1;for(s=n;0!=s--;){var o=i[s];if(!e(t[o],r[o]))return!1}return!0}return t!=t&&r!=r}}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};r.r(n),r.d(n,{EntityProvider:()=>nn,__experimentalFetchLinkSuggestions:()=>Er,__experimentalFetchUrlData:()=>hr,__experimentalUseEntityRecord:()=>pn,__experimentalUseEntityRecords:()=>En,__experimentalUseResourcePermissions:()=>hn,fetchBlockPatterns:()=>vr,privateApis:()=>Un,store:()=>Dn,useEntityBlockEditor:()=>An,useEntityId:()=>_n,useEntityProp:()=>Pn,useEntityRecord:()=>dn,useEntityRecords:()=>yn,useResourcePermissions:()=>mn});var s={};r.r(s),r.d(s,{__experimentalGetCurrentGlobalStylesId:()=>Ke,__experimentalGetCurrentThemeBaseGlobalStyles:()=>tt,__experimentalGetCurrentThemeGlobalStylesVariations:()=>rt,__experimentalGetDirtyEntityRecords:()=>Ce,__experimentalGetEntitiesBeingSaved:()=>Ae,__experimentalGetEntityRecordNoResolver:()=>we,canUser:()=>We,canUserEditEntityRecord:()=>ze,getAuthors: <AUTHORS>
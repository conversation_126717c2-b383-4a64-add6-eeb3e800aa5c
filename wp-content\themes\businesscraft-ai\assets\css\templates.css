/**
 * ChatGABI Templates Interface Styles
 * AI-Powered Template Management System
 */

/* Main Container */
.chatgabi-templates-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.chatgabi-templates-header {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
}

.templates-title {
    font-size: 2.5rem;
    margin: 0 0 10px 0;
    font-weight: 700;
}

.templates-icon {
    font-size: 3rem;
    margin-right: 15px;
    vertical-align: middle;
}

.templates-subtitle {
    font-size: 1.2rem;
    margin: 0 0 30px 0;
    opacity: 0.9;
    line-height: 1.5;
}

/* User Context Bar */
.user-context-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.context-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.context-label {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.context-value {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 5px 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

.profile-sme {
    background: rgba(46, 204, 113, 0.3) !important;
}

.profile-creator {
    background: rgba(155, 89, 182, 0.3) !important;
}

.credits-display .context-value {
    background: rgba(241, 196, 15, 0.3) !important;
}

/* Controls Section */
.templates-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;
    flex-wrap: wrap;
}

.templates-search-bar {
    flex: 1;
    min-width: 300px;
}

.search-input-wrapper {
    position: relative;
}

#template-search {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

#template-search:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    color: #666;
}

.templates-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    min-width: 150px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #007cba;
}

/* Buttons */
.chatgabi-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.chatgabi-btn-primary {
    background: #007cba;
    color: white;
}

.chatgabi-btn-primary:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.chatgabi-btn-secondary {
    background: #6c757d;
    color: white;
}

.chatgabi-btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.chatgabi-btn-outline {
    background: transparent;
    color: #007cba;
    border: 2px solid #007cba;
}

.chatgabi-btn-outline:hover {
    background: #007cba;
    color: white;
}

.btn-icon {
    font-size: 1.1rem;
}

/* Loading States */
.templates-loading {
    text-align: center;
    padding: 60px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* AI Suggestions Panel */
.ai-suggestions-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.ai-suggestions-panel.active {
    transform: translateX(0);
}

.suggestions-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestions-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background: #e1e5e9;
}

.suggestions-content {
    padding: 20px;
    height: calc(100vh - 80px);
    overflow-y: auto;
}

/* Category Cards Grid */
.templates-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.category-card {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    border-color: #007cba;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.1);
}

.category-header {
    margin-bottom: 15px;
}

.category-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.category-title {
    font-size: 1.3rem;
    margin: 0;
    color: #333;
    font-weight: 600;
}

.category-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
}

.category-stats {
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: #888;
}

.category-explore-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.category-explore-btn:hover {
    background: #005a87;
}

/* Templates Grid */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.template-card {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    border-color: #007cba;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.1);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.template-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.3;
}

.template-meta {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.meta-badge {
    background: #f8f9fa;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.template-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.template-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #888;
    margin-bottom: 15px;
}

.template-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.template-actions .chatgabi-btn {
    padding: 8px 15px;
    font-size: 0.9rem;
}

/* Modal Styles */
.template-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 25px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    color: #333;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Template Preview Content */
.template-preview-content .template-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.meta-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.meta-item span:last-child {
    font-weight: 600;
    color: #333;
}

.template-description h4,
.template-content h4 {
    color: #333;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
}

.template-content-display {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007cba;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
    white-space: pre-wrap;
    overflow-x: auto;
}

/* AI Enhancement Section */
.ai-enhancement-section {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 2px solid #e1e5e9;
}

.ai-enhancement-section h4 {
    color: #007cba;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ai-enhancement-section h4::before {
    content: "🤖";
    font-size: 1.2rem;
}

.enhancement-description {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.5;
}

.enhancement-result {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-help {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
}

/* AI Enhancement Options */
.ai-enhancement-options {
    margin-top: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.ai-enhancement-options h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.enhancement-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.95rem;
    color: #333;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Messages */
.template-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    max-width: 400px;
}

.message {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatgabi-templates-page {
        padding: 15px;
    }
    
    .templates-title {
        font-size: 2rem;
    }
    
    .templates-icon {
        font-size: 2.5rem;
    }
    
    .user-context-bar {
        gap: 15px;
    }
    
    .templates-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .templates-search-bar {
        min-width: auto;
    }
    
    .templates-filters {
        justify-content: stretch;
    }
    
    .filter-select {
        min-width: auto;
        flex: 1;
    }
    
    .templates-categories-grid {
        grid-template-columns: 1fr;
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }
    
    .ai-suggestions-panel {
        width: 100%;
    }
    
    .template-actions {
        flex-direction: column;
    }
    
    .modal-footer {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .chatgabi-templates-header {
        padding: 25px 20px;
    }
    
    .templates-title {
        font-size: 1.8rem;
    }
    
    .templates-subtitle {
        font-size: 1rem;
    }
    
    .user-context-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .context-item {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .chatgabi-templates-page {
        background: #1a1a1a;
        color: #e1e1e1;
    }
    
    .category-card,
    .template-card,
    .modal-content {
        background: #2d2d2d;
        border-color: #404040;
        color: #e1e1e1;
    }
    
    .template-title,
    .category-title {
        color: #e1e1e1;
    }
    
    .template-description,
    .category-description {
        color: #b1b1b1;
    }
    
    #template-search,
    .filter-select,
    .form-group input,
    .form-group textarea,
    .form-group select {
        background: #2d2d2d;
        border-color: #404040;
        color: #e1e1e1;
    }
    
    .modal-header,
    .modal-footer,
    .ai-enhancement-options {
        background: #333;
    }
    
    .template-content-display {
        background: #333;
        color: #e1e1e1;
    }
}

/* Fallback Templates Styles */
.templates-fallback {
    margin-top: 20px;
}

.fallback-card {
    border: 2px solid #e1e5e9;
    background: #f8f9fa;
}

.fallback-card .template-title {
    color: #495057;
}

.featured-badge {
    background: #ffc107;
    color: #212529;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.initial-loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.initial-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.no-templates-message {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.no-templates-message h3 {
    color: #495057;
    margin-bottom: 15px;
}

/* Debug and Error States */
.debug-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    font-family: monospace;
    font-size: 14px;
}

.error-state {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    margin: 20px 0;
}

.error-state h3 {
    color: #721c24;
    margin-bottom: 10px;
}

.retry-button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
}

.retry-button:hover {
    background: #c82333;
}

<?php
/**
 * Test ChatGABI Templates Functionality
 * 
 * Quick test script to verify all template functionality is working
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #005a87; }
    </style>
</head>
<body>

<h1>🧪 ChatGABI Templates Functionality Test</h1>

<?php
echo '<div class="info">Test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$tests_passed = 0;
$tests_failed = 0;

// Test 1: Database Tables
echo '<h2>📊 Test 1: Database Tables</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

$templates_exist = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table;
$categories_exist = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table;

if ($templates_exist && $categories_exist) {
    echo '<div class="success">✅ Database tables exist</div>';
    $tests_passed++;
} else {
    echo '<div class="error">❌ Database tables missing</div>';
    $tests_failed++;
}

// Test 2: Template Categories Function
echo '<h2>📂 Test 2: Template Categories Function</h2>';

if (function_exists('chatgabi_get_template_categories')) {
    $categories = chatgabi_get_template_categories();
    if (!empty($categories)) {
        echo '<div class="success">✅ Template categories function working (' . count($categories) . ' categories)</div>';
        $tests_passed++;
        
        echo '<h3>Categories Found:</h3>';
        echo '<ul>';
        foreach ($categories as $category) {
            $name = is_object($category) ? $category->name : $category['name'];
            $slug = is_object($category) ? $category->slug : $category['slug'];
            echo '<li><strong>' . esc_html($name) . '</strong> (' . esc_html($slug) . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<div class="error">❌ Template categories function returned empty</div>';
        $tests_failed++;
    }
} else {
    echo '<div class="error">❌ Template categories function not found</div>';
    $tests_failed++;
}

// Test 3: Templates Page
echo '<h2>📄 Test 3: Templates Page</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<div class="success">✅ Templates page exists (ID: ' . $templates_page->ID . ')</div>';
    echo '<div class="info">URL: <a href="' . get_permalink($templates_page->ID) . '" target="_blank">' . get_permalink($templates_page->ID) . '</a></div>';
    $tests_passed++;
} else {
    echo '<div class="error">❌ Templates page not found</div>';
    $tests_failed++;
}

// Test 4: REST API Routes
echo '<h2>🌐 Test 4: REST API Routes</h2>';

$rest_routes = rest_get_server()->get_routes();
$chatgabi_routes = array();

foreach ($rest_routes as $route => $handlers) {
    if (strpos($route, '/chatgabi/v1/') === 0) {
        $chatgabi_routes[] = $route;
    }
}

if (!empty($chatgabi_routes)) {
    echo '<div class="success">✅ REST API routes found (' . count($chatgabi_routes) . ' routes)</div>';
    $tests_passed++;
    
    echo '<h3>Available Routes:</h3>';
    echo '<ul>';
    foreach ($chatgabi_routes as $route) {
        echo '<li><code>' . $route . '</code></li>';
    }
    echo '</ul>';
} else {
    echo '<div class="error">❌ No REST API routes found</div>';
    $tests_failed++;
}

// Test 5: Template Data
echo '<h2>📝 Test 5: Template Data</h2>';

$template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
$public_template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_public = 1");

if ($template_count > 0) {
    echo '<div class="success">✅ Templates found (Total: ' . $template_count . ', Public: ' . $public_template_count . ')</div>';
    $tests_passed++;
    
    // Show sample templates
    $sample_templates = $wpdb->get_results("SELECT * FROM $templates_table LIMIT 5");
    if (!empty($sample_templates)) {
        echo '<h3>Sample Templates:</h3>';
        echo '<ul>';
        foreach ($sample_templates as $template) {
            echo '<li><strong>' . esc_html($template->title) . '</strong>';
            if ($template->description) {
                echo ' - ' . esc_html(wp_trim_words($template->description, 10));
            }
            echo '</li>';
        }
        echo '</ul>';
    }
} else {
    echo '<div class="error">❌ No templates found</div>';
    $tests_failed++;
}

// Test 6: Admin Functions
echo '<h2>⚙️ Test 6: Admin Functions</h2>';

if (function_exists('chatgabi_templates_admin_page')) {
    echo '<div class="success">✅ Admin page function exists</div>';
    $tests_passed++;
} else {
    echo '<div class="error">❌ Admin page function missing</div>';
    $tests_failed++;
}

// Test 7: Template Files
echo '<h2>📁 Test 7: Template Files</h2>';

$theme_dir = get_template_directory();
$required_files = array(
    'page-templates.php' => $theme_dir . '/page-templates.php',
    'templates.css' => $theme_dir . '/assets/css/templates.css',
    'templates-interface.js' => $theme_dir . '/assets/js/templates-interface.js'
);

$files_found = 0;
foreach ($required_files as $name => $path) {
    if (file_exists($path)) {
        echo '<div class="success">✅ ' . $name . ' exists</div>';
        $files_found++;
    } else {
        echo '<div class="warning">⚠️ ' . $name . ' missing</div>';
    }
}

if ($files_found >= 2) {
    $tests_passed++;
} else {
    $tests_failed++;
}

// Test 8: User Template Functions
echo '<h2>👤 Test 8: User Template Functions</h2>';

if (function_exists('chatgabi_get_user_templates')) {
    echo '<div class="success">✅ User templates function exists</div>';
    $tests_passed++;
} else {
    echo '<div class="error">❌ User templates function missing</div>';
    $tests_failed++;
}

// Test 9: Template Creation Functions
echo '<h2>🔧 Test 9: Template Creation Functions</h2>';

$creation_functions = array(
    'chatgabi_save_prompt_template',
    'chatgabi_create_default_template_categories',
    'chatgabi_initialize_default_templates'
);

$functions_found = 0;
foreach ($creation_functions as $function) {
    if (function_exists($function)) {
        echo '<div class="success">✅ ' . $function . ' exists</div>';
        $functions_found++;
    } else {
        echo '<div class="error">❌ ' . $function . ' missing</div>';
    }
}

if ($functions_found >= 2) {
    $tests_passed++;
} else {
    $tests_failed++;
}

// Test Summary
echo '<h2>📊 Test Summary</h2>';

$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 1) : 0;

if ($tests_failed == 0) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL TESTS PASSED!</h3>';
    echo '<p><strong>✅ ChatGABI Templates system is fully functional!</strong></p>';
    echo '<p>Success Rate: ' . $success_rate . '% (' . $tests_passed . '/' . $total_tests . ' tests passed)</p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Tests Failed</h3>';
    echo '<p>Success Rate: ' . $success_rate . '% (' . $tests_passed . '/' . $total_tests . ' tests passed)</p>';
    echo '<p>Failed Tests: ' . $tests_failed . '</p>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Quick Actions</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<button class="test-button" onclick="window.open(\'' . get_permalink($templates_page->ID) . '\', \'_blank\')">🎯 Open Templates Page</button>';
}

echo '<button class="test-button" onclick="window.open(\'' . admin_url('admin.php?page=chatgabi-templates') . '\', \'_blank\')">⚙️ Admin Templates</button>';
echo '<button class="test-button" onclick="window.location.href=\'fix-templates-system-complete.php\'">🔧 Run Fix Script</button>';
echo '<button class="test-button" onclick="window.location.reload()">🔄 Re-run Tests</button>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

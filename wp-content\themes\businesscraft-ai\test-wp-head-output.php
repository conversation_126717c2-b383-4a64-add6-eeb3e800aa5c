<?php
/**
 * Test WordPress wp_head Output for PWA Manifest
 * 
 * This script tests if the PWA manifest link is being output
 * in the wp_head() section when WordPress is loaded.
 */

// Load WordPress
require_once(dirname(dirname(dirname(__DIR__))) . '/wp-load.php');

echo "<h1>WordPress wp_head() PWA Manifest Test</h1>\n";

// Test 1: Check if functions exist
echo "<h2>Test 1: Function Availability</h2>\n";
if (function_exists('chatgabi_init_pwa_support')) {
    echo "✅ chatgabi_init_pwa_support function exists\n";
} else {
    echo "❌ chatgabi_init_pwa_support function NOT found\n";
}

if (function_exists('chatgabi_add_pwa_meta_tags')) {
    echo "✅ chatgabi_add_pwa_meta_tags function exists\n";
} else {
    echo "❌ chatgabi_add_pwa_meta_tags function NOT found\n";
}

// Test 2: Check hook registration
echo "<h2>Test 2: Hook Registration</h2>\n";
global $wp_filter;
$hook_found = false;

if (isset($wp_filter['wp_head'])) {
    foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback_id => $callback_data) {
            if (is_array($callback_data['function']) && 
                isset($callback_data['function'][1]) &&
                $callback_data['function'][1] === 'chatgabi_add_pwa_meta_tags') {
                $hook_found = true;
                echo "✅ chatgabi_add_pwa_meta_tags is hooked to wp_head at priority $priority\n";
                break 2;
            } elseif ($callback_data['function'] === 'chatgabi_add_pwa_meta_tags') {
                $hook_found = true;
                echo "✅ chatgabi_add_pwa_meta_tags is hooked to wp_head at priority $priority\n";
                break 2;
            }
        }
    }
}

if (!$hook_found) {
    echo "❌ chatgabi_add_pwa_meta_tags is NOT hooked to wp_head\n";
}

// Test 3: Direct function output test
echo "<h2>Test 3: Direct Function Output</h2>\n";
if (function_exists('chatgabi_add_pwa_meta_tags')) {
    echo "Capturing output from chatgabi_add_pwa_meta_tags():\n";
    echo "<pre>";
    ob_start();
    chatgabi_add_pwa_meta_tags();
    $output = ob_get_clean();
    echo htmlspecialchars($output);
    echo "</pre>";
    
    // Check if manifest link is in the output
    if (strpos($output, 'rel="manifest"') !== false) {
        echo "✅ Manifest link found in function output\n";
        
        // Extract the manifest URL
        if (preg_match('/href="([^"]*manifest\.json[^"]*)"/', $output, $matches)) {
            $manifest_url = $matches[1];
            echo "🔗 Manifest URL: $manifest_url\n";
        }
    } else {
        echo "❌ Manifest link NOT found in function output\n";
    }
} else {
    echo "❌ Cannot test function output - function not available\n";
}

// Test 4: Simulate full wp_head output
echo "<h2>Test 4: Full wp_head() Output</h2>\n";
echo "Capturing full wp_head() output:\n";
echo "<pre>";
ob_start();
wp_head();
$full_output = ob_get_clean();
echo htmlspecialchars(substr($full_output, 0, 2000)) . (strlen($full_output) > 2000 ? '...[truncated]' : '');
echo "</pre>";

// Check if manifest link is in the full output
if (strpos($full_output, 'rel="manifest"') !== false) {
    echo "✅ Manifest link found in wp_head() output\n";
} else {
    echo "❌ Manifest link NOT found in wp_head() output\n";
}

// Test 5: Theme information
echo "<h2>Test 5: Theme Information</h2>\n";
$current_theme = wp_get_theme();
echo "Current theme: " . $current_theme->get('Name') . "\n";
echo "Theme directory: " . get_template_directory() . "\n";
echo "Theme URI: " . get_template_directory_uri() . "\n";

// Check if we're using the correct theme
if (strpos(get_template_directory(), 'businesscraft-ai') !== false) {
    echo "✅ Using BusinessCraft AI theme\n";
} else {
    echo "❌ NOT using BusinessCraft AI theme\n";
}

// Test 6: Check manifest URL construction
echo "<h2>Test 6: Manifest URL Construction</h2>\n";
$manifest_url = get_template_directory_uri() . '/manifest.json';
echo "Constructed manifest URL: $manifest_url\n";

// Test if the URL is accessible
$response = wp_remote_get($manifest_url);
if (!is_wp_error($response)) {
    $status_code = wp_remote_retrieve_response_code($response);
    echo "📡 HTTP Status: $status_code\n";
    
    if ($status_code === 200) {
        echo "✅ Manifest URL is accessible\n";
        
        $body = wp_remote_retrieve_body($response);
        $json_data = json_decode($body, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ Manifest contains valid JSON\n";
        } else {
            echo "❌ Manifest contains invalid JSON\n";
        }
    } else {
        echo "❌ Manifest URL is not accessible (Status: $status_code)\n";
    }
} else {
    echo "❌ Error accessing manifest URL: " . $response->get_error_message() . "\n";
}

echo "\n<h2>Summary</h2>\n";
echo "This test checks if the PWA manifest link is properly integrated into WordPress.\n";
echo "All tests should pass for proper PWA functionality.\n";
?>

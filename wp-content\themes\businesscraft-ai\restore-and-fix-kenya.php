<?php
/**
 * Restore and properly fix Kenya JSON file
 */

// Define WordPress constants for testing
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

echo "=== Restore and Fix Kenya JSON ===\n\n";

$dataset_dir = WP_CONTENT_DIR . '/datasets/kenya-business-data/';
$original_file = $dataset_dir . 'kenya_business_data.json';
$backup_file = $dataset_dir . 'kenya_business_data.json.backup.2025-05-30-03-27-02';
$cleaned_file = $dataset_dir . 'kenya_business_data.cleaned.json';

// Step 1: Restore from backup
if (file_exists($backup_file)) {
    echo "📦 Restoring from backup...\n";
    $backup_content = file_get_contents($backup_file);
    file_put_contents($original_file, $backup_content);
    echo "✅ Original file restored\n";
} else {
    echo "❌ Backup file not found\n";
    exit(1);
}

// Step 2: Try to fix the original file more carefully
echo "\n🔧 Attempting careful fix...\n";

$content = file_get_contents($original_file);
echo "📄 Original size: " . strlen($content) . " bytes\n";

// Strategy: Use a more conservative approach
$fixed_content = $content;

// 1. Remove BOM if present
$fixed_content = preg_replace('/^\xEF\xBB\xBF/', '', $fixed_content);

// 2. Convert encoding more carefully
if (function_exists('mb_convert_encoding')) {
    $fixed_content = mb_convert_encoding($fixed_content, 'UTF-8', 'UTF-8');
}

// 3. Remove only truly problematic control characters (keep normal whitespace)
$fixed_content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $fixed_content);

// 4. Normalize line endings
$fixed_content = str_replace(["\r\n", "\r"], "\n", $fixed_content);

echo "📄 Fixed size: " . strlen($fixed_content) . " bytes\n";

// Test the fix
$decoded = json_decode($fixed_content, true);
$json_error = json_last_error();

if ($json_error === JSON_ERROR_NONE) {
    echo "✅ JSON is now valid!\n";

    // Validate structure
    if (isset($decoded['sectors']) && is_array($decoded['sectors'])) {
        echo "📊 Structure validated - found " . count($decoded['sectors']) . " sectors\n";

        // Save the fixed version
        file_put_contents($cleaned_file, $fixed_content);
        echo "💾 Cleaned file saved as: kenya_business_data.cleaned.json\n";

        // Show first few sectors
        echo "\n📈 First 5 sectors:\n";
        for ($i = 0; $i < min(5, count($decoded['sectors'])); $i++) {
            if (isset($decoded['sectors'][$i]['sector_name'])) {
                echo "   " . ($i + 1) . ". " . $decoded['sectors'][$i]['sector_name'] . "\n";
            }
        }
    } else {
        echo "⚠️ Warning: sectors array not found or invalid\n";
    }
} else {
    echo "❌ Still invalid: " . json_last_error_msg() . "\n";

    // Try one more approach - manual quote fixing
    echo "\n🔧 Trying manual quote fixing...\n";

    // Look for common quote issues and fix them
    $lines = explode("\n", $fixed_content);
    $fixed_lines = [];

    foreach ($lines as $line_num => $line) {
        $original_line = $line;

        // Fix common quote issues in JSON
        // Replace smart quotes with regular quotes using hex codes
        $line = str_replace(["\xE2\x80\x9C", "\xE2\x80\x9D"], '"', $line); // Smart double quotes
        $line = str_replace(["\xE2\x80\x98", "\xE2\x80\x99"], "'", $line); // Smart single quotes

        // Fix unescaped quotes in strings (basic approach)
        if (preg_match('/:\s*"[^"]*"[^"]*"/', $line)) {
            // This line might have unescaped quotes - try to fix
            $line = preg_replace('/([^\\\\])"([^"]*)"([^"]*)"/', '$1"$2\\"$3"', $line);
        }

        $fixed_lines[] = $line;

        if ($line !== $original_line) {
            echo "   Fixed line " . ($line_num + 1) . "\n";
        }
    }

    $manual_fixed = implode("\n", $fixed_lines);

    // Test manual fix
    $decoded = json_decode($manual_fixed, true);
    $json_error = json_last_error();

    if ($json_error === JSON_ERROR_NONE) {
        echo "✅ Manual fix successful!\n";
        file_put_contents($cleaned_file, $manual_fixed);
        echo "💾 Manually fixed file saved\n";

        if (isset($decoded['sectors'])) {
            echo "📊 Found " . count($decoded['sectors']) . " sectors\n";
        }
    } else {
        echo "❌ Manual fix failed: " . json_last_error_msg() . "\n";
        echo "🚨 The Kenya dataset requires manual intervention\n";
    }
}

echo "\n=== Fix Complete ===\n";

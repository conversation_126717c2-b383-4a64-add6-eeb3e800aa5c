<?php
/**
 * Manually trigger ChatGABI fixes
 */

// Load WordPress
require_once('wp-load.php');

echo "<h1>Triggering ChatGABI Fixes</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>";

echo '<h2>Creating Preferences Page...</h2>';

// Manually create preferences page
$preferences_page = get_page_by_path('preferences');
if (!$preferences_page) {
    $preferences_page_data = array(
        'post_title' => 'User Preferences',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'post_name' => 'preferences',
    );
    
    $preferences_page_id = wp_insert_post($preferences_page_data);
    
    if ($preferences_page_id) {
        // Set the page template
        update_post_meta($preferences_page_id, '_wp_page_template', 'page-preferences.php');
        echo '<p class="success">✅ Preferences page created with ID: ' . $preferences_page_id . '</p>';
        echo '<p class="info">🔗 URL: <a href="' . get_permalink($preferences_page_id) . '">' . get_permalink($preferences_page_id) . '</a></p>';
    } else {
        echo '<p class="error">❌ Failed to create preferences page</p>';
    }
} else {
    echo '<p class="info">ℹ️ Preferences page already exists</p>';
}

echo '<h2>Creating Sample Templates...</h2>';

// Check if we can create sample templates
if (function_exists('chatgabi_check_templates_tables_exist')) {
    if (chatgabi_check_templates_tables_exist()) {
        echo '<p class="success">✅ Template tables exist</p>';
        
        global $wpdb;
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        $sample_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0 AND is_public = 1");
        
        if ($sample_count == 0) {
            if (function_exists('chatgabi_create_sample_templates')) {
                chatgabi_create_sample_templates();
                $new_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0 AND is_public = 1");
                echo '<p class="success">✅ Created ' . $new_count . ' sample templates</p>';
            } else {
                echo '<p class="error">❌ chatgabi_create_sample_templates function not found</p>';
            }
        } else {
            echo '<p class="info">ℹ️ Sample templates already exist (' . $sample_count . ')</p>';
        }
    } else {
        echo '<p class="error">❌ Template tables do not exist</p>';
    }
} else {
    echo '<p class="error">❌ Template functions not available</p>';
}

echo '<h2>Testing Dashboard URL</h2>';
$dashboard_url = home_url('/dashboard/');
echo '<p class="info">🔗 Dashboard URL: <a href="' . $dashboard_url . '" target="_blank">' . $dashboard_url . '</a></p>';

echo '<h2>Testing Preferences URL</h2>';
$preferences_url = home_url('/preferences/');
echo '<p class="info">🔗 Preferences URL: <a href="' . $preferences_url . '" target="_blank">' . $preferences_url . '</a></p>';

echo '<p><strong>Fixes completed!</strong> You can now test the dashboard and preferences pages.</p>';
?>

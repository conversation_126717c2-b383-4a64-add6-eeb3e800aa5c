<?php
/**
 * SendPulse Menu Verification Script
 * 
 * Quick verification that the SendPulse admin menu is accessible
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check admin privileges
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions');
}

echo "SendPulse Admin Menu Verification\n";
echo "================================\n\n";

// Test 1: Check if classes exist
echo "1. Class Availability:\n";
if (class_exists('ChatGABI_SendPulse_Integration')) {
    echo "   ✅ ChatGABI_SendPulse_Integration class exists\n";
} else {
    echo "   ❌ ChatGABI_SendPulse_Integration class missing\n";
}

// Test 2: Check if function exists
echo "\n2. Helper Function:\n";
if (function_exists('chatgabi_get_sendpulse')) {
    echo "   ✅ chatgabi_get_sendpulse() function exists\n";
} else {
    echo "   ❌ chatgabi_get_sendpulse() function missing\n";
}

// Test 3: Check admin menu registration
echo "\n3. Admin Menu Registration:\n";

// Simulate admin environment
global $menu, $submenu;
if (!is_array($menu)) $menu = array();
if (!is_array($submenu)) $submenu = array();

// Trigger admin menu hooks
do_action('admin_menu');

// Check main menu
$main_menu_exists = false;
if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (is_array($menu_item) && isset($menu_item[2]) && $menu_item[2] === 'chatgabi-main') {
            $main_menu_exists = true;
            echo "   ✅ Main ChatGABI menu exists: {$menu_item[0]}\n";
            break;
        }
    }
}

if (!$main_menu_exists) {
    echo "   ❌ Main ChatGABI menu not found\n";
}

// Check SendPulse submenu
$sendpulse_submenu_exists = false;
if (isset($submenu['chatgabi-main']) && is_array($submenu['chatgabi-main'])) {
    foreach ($submenu['chatgabi-main'] as $submenu_item) {
        if (is_array($submenu_item) && isset($submenu_item[2]) && $submenu_item[2] === 'chatgabi-sendpulse') {
            $sendpulse_submenu_exists = true;
            echo "   ✅ SendPulse submenu exists: {$submenu_item[1]}\n";
            break;
        }
    }
}

if (!$sendpulse_submenu_exists) {
    echo "   ❌ SendPulse submenu not found\n";
    
    // Debug: Show available submenus
    if (isset($submenu['chatgabi-main'])) {
        echo "   Available submenus:\n";
        foreach ($submenu['chatgabi-main'] as $submenu_item) {
            if (is_array($submenu_item) && isset($submenu_item[2])) {
                echo "     - {$submenu_item[2]}: {$submenu_item[1]}\n";
            }
        }
    }
}

// Test 4: Check AJAX handlers
echo "\n4. AJAX Handlers:\n";
$ajax_actions = array(
    'chatgabi_test_sendpulse_connection',
    'chatgabi_save_sendpulse_settings'
);

foreach ($ajax_actions as $action) {
    if (has_action("wp_ajax_{$action}")) {
        echo "   ✅ {$action} registered\n";
    } else {
        echo "   ❌ {$action} not registered\n";
    }
}

// Test 5: Check options
echo "\n5. Configuration Options:\n";
$options = array(
    'chatgabi_sendpulse_user_id',
    'chatgabi_sendpulse_secret', 
    'chatgabi_sendpulse_template_id'
);

foreach ($options as $option) {
    $value = get_option($option, null);
    if ($value !== null) {
        $status = !empty($value) ? 'configured' : 'empty';
        echo "   ✅ {$option} exists ({$status})\n";
    } else {
        echo "   ❌ {$option} not found\n";
    }
}

// Final status
echo "\n6. Overall Status:\n";
if ($main_menu_exists && $sendpulse_submenu_exists) {
    echo "   🎉 SUCCESS: SendPulse admin menu should be accessible at:\n";
    echo "      WordPress Admin → ChatGABI → Email Settings\n";
    echo "      Direct URL: " . admin_url('admin.php?page=chatgabi-sendpulse') . "\n";
} else {
    echo "   ❌ FAILED: SendPulse admin menu is not properly registered\n";
    
    if (!$main_menu_exists) {
        echo "      - Main ChatGABI menu missing\n";
    }
    if (!$sendpulse_submenu_exists) {
        echo "      - SendPulse submenu missing\n";
    }
}

echo "\nVerification completed at " . current_time('mysql') . "\n";
?>

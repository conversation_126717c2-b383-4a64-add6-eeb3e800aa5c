<?php
/**
 * Test Frontend Feedback Integration
 * 
 * This script tests the complete frontend integration of the feedback system
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url());
    exit;
}

get_header();
?>

<div class="container" style="max-width: 1000px; margin: 40px auto; padding: 20px;">
    <h1>🧪 Frontend Feedback Integration Test</h1>
    
    <div class="test-status" style="background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>📊 System Status</h3>
        <div id="system-status">
            <p><strong>User:</strong> <?php echo wp_get_current_user()->display_name; ?> (ID: <?php echo get_current_user_id(); ?>)</p>
            <p><strong>Credits:</strong> <?php echo get_user_meta(get_current_user_id(), 'chatgabi_credits', true) ?: 0; ?></p>
            <p><strong>Feedback System:</strong> <span id="feedback-system-loaded">Checking...</span></p>
            <p><strong>JavaScript Events:</strong> <span id="js-events-status">Monitoring...</span></p>
            <p><strong>AJAX Endpoints:</strong> <span id="ajax-status">Testing...</span></p>
        </div>
    </div>

    <div class="chat-test-area" style="background: white; border: 2px solid #007cba; border-radius: 12px; padding: 30px; margin: 30px 0;">
        <h2>💬 Chat Interface Test</h2>
        <p>Send a message below to test the feedback integration:</p>
        
        <?php echo do_shortcode('[businesscraft_ai_chat show_history="false" show_examples="true"]'); ?>
    </div>

    <div class="feedback-test-controls" style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>🎮 Test Controls</h3>
        <button id="simulate-response" class="button button-primary">🤖 Simulate AI Response</button>
        <button id="test-ajax-endpoints" class="button">🔗 Test AJAX Endpoints</button>
        <button id="check-assets" class="button">📁 Check Asset Loading</button>
        <button id="clear-console" class="button">🧹 Clear Console</button>
    </div>

    <div class="debug-console" style="background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 8px; margin: 20px 0; font-family: monospace; height: 300px; overflow-y: auto;">
        <div id="console-output">
            <div class="console-line">🚀 Frontend Feedback Integration Test Console</div>
            <div class="console-line">Ready for testing...</div>
        </div>
    </div>

    <div class="admin-links" style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>🔗 Admin Links</h3>
        <p>
            <a href="/wp-admin/admin.php?page=chatgabi-feedback" class="button" target="_blank">📊 Feedback Dashboard</a>
            <a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=feedback" class="button" target="_blank">💬 Text Feedback</a>
            <a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=export" class="button" target="_blank">📤 Export Data</a>
            <a href="/comprehensive-feedback-system-fix.php" class="button" target="_blank">🔧 System Diagnostic</a>
        </p>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    let eventCount = 0;
    let testResults = {
        feedbackSystemLoaded: false,
        eventsWorking: false,
        ajaxWorking: false,
        assetsLoaded: false
    };

    function logToConsole(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            info: '#00ff00',
            success: '#00ff00',
            warning: '#ffff00',
            error: '#ff0000'
        };
        
        $('#console-output').append(
            `<div class="console-line" style="color: ${colors[type]}">[${timestamp}] ${message}</div>`
        );
        
        // Auto-scroll to bottom
        const console = document.getElementById('console-output');
        console.scrollTop = console.scrollHeight;
    }

    // Check if feedback system is loaded
    function checkFeedbackSystem() {
        if (typeof window.chatgabiFeedbackSystem !== 'undefined') {
            $('#feedback-system-loaded').text('✅ Loaded').css('color', 'green');
            testResults.feedbackSystemLoaded = true;
            logToConsole('✅ Feedback system detected', 'success');
            logToConsole('System state: ' + JSON.stringify(window.chatgabiFeedbackSystem), 'info');
        } else {
            $('#feedback-system-loaded').text('❌ Not Loaded').css('color', 'red');
            logToConsole('❌ Feedback system not detected', 'error');
        }
    }

    // Monitor chat events
    $(document).on('chatgabi:responseCompleted', function(event, data) {
        eventCount++;
        $('#js-events-status').text(`✅ ${eventCount} events detected`).css('color', 'green');
        testResults.eventsWorking = true;
        
        logToConsole('🎯 Chat response completed event received', 'success');
        logToConsole('Event data: ' + JSON.stringify(data), 'info');
        
        // Check if feedback interface was added
        setTimeout(function() {
            const feedbackInterfaces = $('.feedback-interface').length;
            if (feedbackInterfaces > 0) {
                logToConsole(`✅ Found ${feedbackInterfaces} feedback interface(s)`, 'success');
                
                // Add visual indicators
                $('.feedback-interface').each(function() {
                    if (!$(this).find('.test-indicator').length) {
                        $(this).prepend('<div class="test-indicator" style="background: #4CAF50; color: white; padding: 5px; border-radius: 3px; margin-bottom: 10px; font-size: 12px;">🧪 Test: Feedback interface active!</div>');
                    }
                });
            } else {
                logToConsole('❌ No feedback interfaces found after response', 'error');
            }
        }, 1000);
    });

    // Monitor feedback interactions
    $(document).on('click', '.feedback-star, .feedback-thumbs', function() {
        logToConsole('🎯 Feedback interaction: ' + $(this).attr('class'), 'success');
    });

    // Monitor AJAX submissions
    $(document).ajaxComplete(function(event, xhr, settings) {
        if (settings.url && settings.url.includes('admin-ajax.php') && settings.data && settings.data.includes('chatgabi_submit_feedback')) {
            logToConsole('🎯 Feedback AJAX submission detected', 'success');
            logToConsole('Response: ' + xhr.responseText, 'info');
            testResults.ajaxWorking = true;
            $('#ajax-status').text('✅ Working').css('color', 'green');
        }
    });

    // Test Controls
    $('#simulate-response').click(function() {
        logToConsole('🤖 Simulating AI response...', 'info');
        
        // Trigger the event manually
        $(document).trigger('chatgabi:responseCompleted', {
            message_id: 'test_' + Date.now(),
            conversation_id: 'test_conv',
            session_id: 'test_session',
            tokens_used: 150,
            response_time_ms: 2500,
            context: 'test'
        });
        
        logToConsole('✅ Response event triggered', 'success');
    });

    $('#test-ajax-endpoints').click(function() {
        logToConsole('🔗 Testing AJAX endpoints...', 'info');
        
        // Test feedback submission endpoint
        $.ajax({
            url: ajaxurl || '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'chatgabi_submit_feedback',
                nonce: 'test_nonce',
                message_id: 'test_message',
                rating_score: 5,
                rating_type: 'star'
            },
            success: function(response) {
                logToConsole('✅ AJAX endpoint responded', 'success');
                logToConsole('Response: ' + JSON.stringify(response), 'info');
            },
            error: function(xhr, status, error) {
                logToConsole('❌ AJAX endpoint error: ' + error, 'error');
                logToConsole('Status: ' + status + ', Response: ' + xhr.responseText, 'error');
            }
        });
    });

    $('#check-assets').click(function() {
        logToConsole('📁 Checking asset loading...', 'info');
        
        const assets = [
            '/wp-content/themes/businesscraft-ai/assets/js/feedback-rating.js',
            '/wp-content/themes/businesscraft-ai/assets/css/feedback-rating.css'
        ];
        
        assets.forEach(function(asset) {
            $.get(asset)
                .done(function() {
                    logToConsole('✅ Asset loaded: ' + asset, 'success');
                    testResults.assetsLoaded = true;
                })
                .fail(function() {
                    logToConsole('❌ Asset failed: ' + asset, 'error');
                });
        });
    });

    $('#clear-console').click(function() {
        $('#console-output').html('<div class="console-line">🧹 Console cleared</div>');
    });

    // Initial checks
    checkFeedbackSystem();
    
    // Check assets automatically
    setTimeout(function() {
        $('#check-assets').click();
    }, 1000);

    // Update status every 5 seconds
    setInterval(function() {
        let status = 'System Status: ';
        if (testResults.feedbackSystemLoaded) status += '✅ Loaded ';
        if (testResults.eventsWorking) status += '✅ Events ';
        if (testResults.ajaxWorking) status += '✅ AJAX ';
        if (testResults.assetsLoaded) status += '✅ Assets ';
        
        logToConsole(status, 'info');
    }, 30000);

    // Add custom styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .button {
                display: inline-block;
                padding: 8px 16px;
                margin: 5px;
                background: #0073aa;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                border: none;
                cursor: pointer;
            }
            .button-primary {
                background: #007cba;
            }
            .button:hover {
                background: #005a87;
                color: white;
            }
            .feedback-interface {
                border: 2px dashed #4CAF50 !important;
                background: rgba(76, 175, 80, 0.05) !important;
            }
            .console-line {
                margin: 2px 0;
                font-size: 12px;
            }
        `)
        .appendTo('head');
});
</script>

<style>
.container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>

<?php get_footer(); ?>

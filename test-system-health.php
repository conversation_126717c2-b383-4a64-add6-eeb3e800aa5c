<?php
/**
 * ChatGABI System Health Test
 * 
 * Comprehensive test script to verify that the error fixing and refactoring
 * processes were successful.
 * 
 * Run this script from the command line:
 * php test-system-health.php
 * 
 * Or access via browser:
 * http://localhost/swifmind-local/wordpress/test-system-health.php
 */

// Set content type for browser access
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/html; charset=UTF-8');
    echo "<!DOCTYPE html><html><head><title>ChatGABI System Health Test</title>";
    echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;} pre{background:#f4f4f4;padding:10px;border-radius:5px;}</style>";
    echo "</head><body>";
    echo "<h1>🧪 ChatGABI System Health Test</h1>";
}

// Include WordPress
require_once 'wp-config.php';
require_once ABSPATH . 'wp-load.php';

// Test results
$tests_passed = 0;
$tests_failed = 0;
$test_results = array();

/**
 * Run a test and record results
 */
function run_test($test_name, $test_function, $expected_result = true) {
    global $tests_passed, $tests_failed, $test_results;
    
    echo "Testing {$test_name}... ";
    
    try {
        $result = call_user_func($test_function);
        $success = ($expected_result === true) ? (bool)$result : ($result === $expected_result);
        
        if ($success) {
            echo "<span class='success'>✅ PASSED</span><br>\n";
            $tests_passed++;
            $test_results[$test_name] = array('status' => 'passed', 'result' => $result);
        } else {
            echo "<span class='error'>❌ FAILED</span><br>\n";
            $tests_failed++;
            $test_results[$test_name] = array('status' => 'failed', 'result' => $result);
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ ERROR: " . $e->getMessage() . "</span><br>\n";
        $tests_failed++;
        $test_results[$test_name] = array('status' => 'error', 'result' => $e->getMessage());
    }
}

echo "<h2>🔍 Running System Health Tests</h2>\n";

// Test 1: WordPress Core Loading
run_test("WordPress Core Loading", function() {
    return defined('ABSPATH') && function_exists('get_option');
});

// Test 2: Theme Loading
run_test("Theme Loading", function() {
    return get_option('stylesheet') === 'businesscraft-ai';
});

// Test 3: Core Functions Existence
run_test("Core Functions Existence", function() {
    $required_functions = [
        'chatgabi_get_supported_languages',
        'chatgabi_get_user_preferred_language',
        'chatgabi_load_business_plan_templates',
        'chatgabi_get_user_preferences',
        'businesscraft_ai_add_admin_menus'
    ];
    
    foreach ($required_functions as $function) {
        if (!function_exists($function)) {
            throw new Exception("Function {$function} not found");
        }
    }
    return true;
});

// Test 4: AJAX Handlers Registration
run_test("AJAX Handlers Registration", function() {
    $ajax_actions = [
        'chatgabi_save_language_preference',
        'chatgabi_get_cultural_context',
        'chatgabi_save_user_preferences',
        'chatgabi_get_sectors'
    ];
    
    foreach ($ajax_actions as $action) {
        if (!has_action('wp_ajax_' . $action)) {
            throw new Exception("AJAX action wp_ajax_{$action} not registered");
        }
    }
    return true;
});

// Test 5: Database Connectivity
run_test("Database Connectivity", function() {
    global $wpdb;
    $result = $wpdb->get_var('SELECT 1');
    return $result == 1;
});

// Test 6: Required Tables Existence
run_test("Required Tables Check", function() {
    global $wpdb;
    $tables = [
        $wpdb->prefix . 'chatgabi_sector_logs',
        $wpdb->prefix . 'chatgabi_conversations',
        $wpdb->prefix . 'chatgabi_credit_transactions'
    ];
    
    $missing_tables = array();
    foreach ($tables as $table) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
        if (!$exists) {
            $missing_tables[] = $table;
        }
    }
    
    if (!empty($missing_tables)) {
        throw new Exception("Missing tables: " . implode(', ', $missing_tables));
    }
    return true;
});

// Test 7: Language System
run_test("Language System", function() {
    $languages = chatgabi_get_supported_languages();
    $expected_languages = ['en', 'tw', 'sw', 'yo', 'zu'];
    
    foreach ($expected_languages as $lang) {
        if (!isset($languages[$lang])) {
            throw new Exception("Language {$lang} not supported");
        }
    }
    return count($languages) >= 5;
});

// Test 8: Template System
run_test("Template System", function() {
    $template_languages = chatgabi_get_supported_template_languages();
    return !empty($template_languages) && isset($template_languages['en']);
});

// Test 9: User Preferences System
run_test("User Preferences System", function() {
    $default_prefs = chatgabi_get_default_preferences();
    $required_keys = ['language', 'country', 'industry_sector', 'business_stage'];
    
    foreach ($required_keys as $key) {
        if (!isset($default_prefs[$key])) {
            throw new Exception("Missing preference key: {$key}");
        }
    }
    return true;
});

// Test 10: File Structure
run_test("Modular File Structure", function() {
    $required_files = [
        'inc/ajax-handlers.php',
        'inc/language-functions.php',
        'inc/template-functions.php',
        'inc/user-preference-functions.php',
        'inc/admin-functions.php'
    ];
    
    $theme_dir = get_template_directory();
    foreach ($required_files as $file) {
        $file_path = $theme_dir . '/' . $file;
        if (!file_exists($file_path)) {
            throw new Exception("Missing file: {$file}");
        }
    }
    return true;
});

// Test 11: File Sizes (should be reasonable)
run_test("File Size Check", function() {
    $theme_dir = get_template_directory();
    $files_to_check = [
        'functions.php' => 1200, // Should be under 1200 lines
        'inc/ajax-handlers.php' => 350,
        'inc/language-functions.php' => 350,
        'inc/template-functions.php' => 350,
        'inc/user-preference-functions.php' => 350,
        'inc/admin-functions.php' => 350
    ];
    
    foreach ($files_to_check as $file => $max_lines) {
        $file_path = $theme_dir . '/' . $file;
        if (file_exists($file_path)) {
            $line_count = count(file($file_path));
            if ($line_count > $max_lines) {
                throw new Exception("File {$file} has {$line_count} lines (max: {$max_lines})");
            }
        }
    }
    return true;
});

// Test 12: No Duplicate Functions
run_test("No Duplicate Functions", function() {
    // This test checks if we can load WordPress without fatal errors
    // If we reach this point, there are no duplicate function declarations
    return true;
});

// Test 13: REST API Endpoints
run_test("REST API Endpoints", function() {
    $rest_server = rest_get_server();
    $routes = $rest_server->get_routes();
    
    $required_routes = [
        '/chatgabi/v1/opportunities',
        '/chatgabi/v1/health'
    ];
    
    foreach ($required_routes as $route) {
        if (!isset($routes[$route])) {
            throw new Exception("Missing REST route: {$route}");
        }
    }
    return true;
});

// Test 14: Memory Usage
run_test("Memory Usage Check", function() {
    $memory_used = memory_get_usage() / 1024 / 1024; // MB
    if ($memory_used > 100) {
        throw new Exception("High memory usage: {$memory_used} MB");
    }
    return $memory_used < 50; // Good if under 50MB
});

// Test 15: Cultural Context System
run_test("Cultural Context System", function() {
    $cultural_context = chatgabi_get_cultural_context('tw');
    return !empty($cultural_context) && isset($cultural_context['cultural_practices']);
});

echo "<h2>📊 Test Results Summary</h2>\n";
echo "<p><strong>Tests Passed:</strong> <span class='success'>{$tests_passed}</span></p>\n";
echo "<p><strong>Tests Failed:</strong> <span class='error'>{$tests_failed}</span></p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($tests_passed / ($tests_passed + $tests_failed)) * 100, 1) . "%</p>\n";

if ($tests_failed === 0) {
    echo "<h3><span class='success'>🎉 ALL TESTS PASSED! System is healthy.</span></h3>\n";
} else {
    echo "<h3><span class='warning'>⚠️ Some tests failed. Please review the issues above.</span></h3>\n";
}

// Detailed system information
echo "<h2>🔧 System Information</h2>\n";
echo "<pre>\n";
echo "WordPress Version: " . get_bloginfo('version') . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Active Theme: " . get_option('stylesheet') . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Memory Used: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
echo "Peak Memory: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";

// File line counts
$theme_dir = get_template_directory();
$files_to_count = [
    'functions.php',
    'inc/ajax-handlers.php',
    'inc/language-functions.php',
    'inc/template-functions.php',
    'inc/user-preference-functions.php',
    'inc/admin-functions.php'
];

echo "\nFile Line Counts:\n";
foreach ($files_to_count as $file) {
    $file_path = $theme_dir . '/' . $file;
    if (file_exists($file_path)) {
        $line_count = count(file($file_path));
        echo "- {$file}: {$line_count} lines\n";
    }
}

echo "</pre>\n";

// Performance metrics
echo "<h2>⚡ Performance Metrics</h2>\n";
$end_time = microtime(true);
$start_time = $_SERVER['REQUEST_TIME_FLOAT'] ?? $end_time;
$execution_time = ($end_time - $start_time) * 1000;

echo "<p><strong>Page Load Time:</strong> " . round($execution_time, 2) . " ms</p>\n";
echo "<p><strong>Database Queries:</strong> " . get_num_queries() . "</p>\n";

if (isset($_SERVER['HTTP_HOST'])) {
    echo "</body></html>";
}

// Exit with appropriate code for command line
if (!isset($_SERVER['HTTP_HOST'])) {
    exit($tests_failed > 0 ? 1 : 0);
}
?>

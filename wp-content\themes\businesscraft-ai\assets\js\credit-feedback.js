/**
 * ChatGABI Credit Feedback JavaScript
 * 
 * Handles real-time token estimation, credit tracking,
 * and user feedback for transparent usage information.
 */

(function($) {
    'use strict';

    // Global credit feedback state
    window.chatgabiCreditFeedback = {
        currentBalance: 0,
        isEstimating: false,
        isProcessing: false,
        lastEstimation: null,
        usageChart: null,
        estimationTimeout: null,
        refreshInterval: null
    };

    // Initialize when document is ready
    $(document).ready(function() {
        initializeCreditFeedback();
    });

    /**
     * Initialize credit feedback system
     */
    function initializeCreditFeedback() {
        console.log('Initializing ChatGABI Credit Feedback System');

        // Get initial balance from widget
        const balanceElement = $('#current-balance');
        if (balanceElement.length) {
            window.chatgabiCreditFeedback.currentBalance = parseFloat(balanceElement.text()) || 0;
        }

        // Set up event listeners
        setupEventListeners();

        // Load initial usage stats
        loadUsageStats();

        // Set up periodic balance refresh (every 60 seconds)
        window.chatgabiCreditFeedback.refreshInterval = setInterval(refreshCreditBalance, 60000);

        console.log('Credit Feedback System initialized with balance:', window.chatgabiCreditFeedback.currentBalance);
    }

    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Listen for chat interface events
        $(document).on('chatgabi:promptChanged', handlePromptChange);
        $(document).on('chatgabi:responseStarted', handleResponseStarted);
        $(document).on('chatgabi:responseCompleted', handleResponseCompleted);
        $(document).on('chatgabi:creditsUpdated', handleCreditsUpdated);

        // Listen for textarea changes in chat interface
        $(document).on('input', '.chat-input, #user-message, .prompt-input', function() {
            const prompt = $(this).val();
            if (prompt && prompt.length > 10) {
                triggerPromptEstimation(prompt);
            } else {
                hideTokenEstimation();
            }
        });

        // Listen for form submissions that might use credits
        $(document).on('submit', '.chat-form, .ai-form', function(e) {
            const prompt = $(this).find('textarea, input[type="text"]').val();
            if (prompt) {
                handleFormSubmission(prompt);
            }
        });
    }

    /**
     * Handle prompt text changes for real-time estimation
     */
    function handlePromptChange(event, promptText, context) {
        triggerPromptEstimation(promptText, context);
    }

    /**
     * Trigger prompt estimation with debouncing
     */
    function triggerPromptEstimation(promptText, context = {}) {
        if (!promptText || promptText.length < 10) {
            hideTokenEstimation();
            return;
        }

        // Clear existing timeout
        if (window.chatgabiCreditFeedback.estimationTimeout) {
            clearTimeout(window.chatgabiCreditFeedback.estimationTimeout);
        }

        // Debounce estimation requests
        window.chatgabiCreditFeedback.estimationTimeout = setTimeout(function() {
            estimateTokenUsage(promptText, context);
        }, 800);
    }

    /**
     * Estimate token usage for current prompt
     */
    function estimateTokenUsage(prompt, context = {}) {
        if (window.chatgabiCreditFeedback.isEstimating) {
            return;
        }

        window.chatgabiCreditFeedback.isEstimating = true;
        showTokenEstimation();

        // Show loading state
        $('#estimated-tokens').text('...');
        $('#estimated-credits').text('...');
        $('#balance-after').text('...');

        $.ajax({
            url: chatgabiCreditConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_estimate_tokens',
                nonce: chatgabiCreditConfig.tokenNonce,
                prompt: prompt,
                language: context.language || getUserPreference('preferred_language', 'en'),
                country: context.country || getUserPreference('preferred_country', ''),
                sector: context.sector || getUserPreference('preferred_sector', '')
            },
            success: function(response) {
                if (response.success) {
                    displayTokenEstimation(response.data);
                    window.chatgabiCreditFeedback.lastEstimation = response.data;
                } else {
                    console.error('Token estimation failed:', response.data.message);
                    showEstimationError(response.data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Token estimation error:', error);
                showEstimationError('Network error occurred');
            },
            complete: function() {
                window.chatgabiCreditFeedback.isEstimating = false;
            }
        });
    }

    /**
     * Display token estimation results
     */
    function displayTokenEstimation(data) {
        const estimation = data.estimation;
        const currentBalance = data.current_credits;
        const sufficientCredits = data.sufficient_credits;

        // Update current balance if different
        if (Math.abs(currentBalance - window.chatgabiCreditFeedback.currentBalance) > 0.01) {
            window.chatgabiCreditFeedback.currentBalance = currentBalance;
            updateBalanceDisplay();
        }

        // Update estimation display
        $('#estimated-tokens').text(estimation.total_estimated_tokens.toLocaleString());
        $('#estimated-credits').text(estimation.estimated_credits.toFixed(2));

        const balanceAfter = currentBalance - estimation.estimated_credits;
        $('#balance-after').text(balanceAfter.toFixed(2));

        // Update status indicator
        const statusIndicator = $('#status-indicator');
        const statusText = $('#status-text');

        if (sufficientCredits) {
            statusIndicator.removeClass('insufficient').addClass('sufficient');
            statusText.text(chatgabiCreditConfig.strings.sufficientCredits);
            hideLowCreditWarning();
        } else {
            statusIndicator.removeClass('sufficient').addClass('insufficient');
            statusText.text(chatgabiCreditConfig.strings.insufficientCredits);
            showLowCreditWarning(data.credit_shortfall);
        }

        // Trigger custom event
        $(document).trigger('chatgabi:estimationUpdated', [data]);
    }

    /**
     * Show estimation error
     */
    function showEstimationError(message) {
        $('#estimated-tokens').text('Error');
        $('#estimated-credits').text('Error');
        $('#balance-after').text('Error');
        
        const statusIndicator = $('#status-indicator');
        const statusText = $('#status-text');
        statusIndicator.removeClass('sufficient insufficient').addClass('error');
        statusText.text(message);
    }

    /**
     * Handle form submission
     */
    function handleFormSubmission(prompt) {
        // Check if we have a recent estimation
        if (window.chatgabiCreditFeedback.lastEstimation) {
            const estimation = window.chatgabiCreditFeedback.lastEstimation;
            
            if (!estimation.sufficient_credits) {
                // Show confirmation dialog for insufficient credits
                const proceed = confirm(
                    chatgabiCreditConfig.strings.insufficientCreditsConfirm
                        .replace('{credits}', estimation.estimation.estimated_credits.toFixed(2))
                        .replace('{shortfall}', estimation.credit_shortfall.toFixed(2))
                );
                
                if (!proceed) {
                    return false;
                }
            }
        }

        // Trigger response started event
        $(document).trigger('chatgabi:responseStarted', [{ prompt: prompt }]);
        return true;
    }

    /**
     * Handle response processing start
     */
    function handleResponseStarted(event, requestData) {
        window.chatgabiCreditFeedback.isProcessing = true;
        $('#processing-indicator').slideDown(200);
        hideTokenEstimation();

        // Reset processing display
        $('#actual-tokens').text('...');
        $('#actual-credits').text('...');
    }

    /**
     * Handle response processing completion
     */
    function handleResponseCompleted(event, responseData) {
        window.chatgabiCreditFeedback.isProcessing = false;

        if (responseData.tokens_used) {
            $('#actual-tokens').text(responseData.tokens_used.toLocaleString());
        }

        if (responseData.credits_used) {
            $('#actual-credits').text(responseData.credits_used.toFixed(2));

            // Update balance
            window.chatgabiCreditFeedback.currentBalance -= responseData.credits_used;
            updateBalanceDisplay();

            // Trigger credits updated event
            $(document).trigger('chatgabi:creditsUpdated', [window.chatgabiCreditFeedback.currentBalance]);
        }

        // Hide processing indicator after 3 seconds
        setTimeout(function() {
            $('#processing-indicator').slideUp(200);
        }, 3000);

        // Refresh usage stats
        setTimeout(loadUsageStats, 1000);
    }

    /**
     * Handle credits updated event
     */
    function handleCreditsUpdated(event, newBalance) {
        window.chatgabiCreditFeedback.currentBalance = newBalance;
        updateBalanceDisplay();

        // Check for low balance
        if (newBalance < 5) {
            showLowCreditWarning(5 - newBalance);
        }
    }

    /**
     * Show/hide token estimation section
     */
    function showTokenEstimation() {
        $('#token-estimation').slideDown(200);
    }

    function hideTokenEstimation() {
        $('#token-estimation').slideUp(200);
    }

    /**
     * Refresh credit balance
     */
    window.refreshCreditBalance = function() {
        const refreshBtn = $('.refresh-balance-btn');
        refreshBtn.addClass('spinning');

        $.ajax({
            url: chatgabiCreditConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_get_user_credits',
                nonce: chatgabiCreditConfig.nonce
            },
            success: function(response) {
                if (response.success && typeof response.data.credits !== 'undefined') {
                    window.chatgabiCreditFeedback.currentBalance = parseFloat(response.data.credits);
                    updateBalanceDisplay();
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to refresh balance:', error);
            },
            complete: function() {
                refreshBtn.removeClass('spinning');
            }
        });
    };

    /**
     * Update balance display
     */
    function updateBalanceDisplay() {
        const balance = window.chatgabiCreditFeedback.currentBalance;
        $('#current-balance').text(balance.toFixed(2));

        // Update balance after estimation if visible
        if ($('#token-estimation').is(':visible') && window.chatgabiCreditFeedback.lastEstimation) {
            const estimation = window.chatgabiCreditFeedback.lastEstimation.estimation;
            const balanceAfter = balance - estimation.estimated_credits;
            $('#balance-after').text(balanceAfter.toFixed(2));
        }
    }

    /**
     * Toggle usage statistics panel
     */
    window.toggleUsageStats = function() {
        const panel = $('#usage-stats-panel');
        const arrow = $('#stats-arrow');

        if (panel.is(':visible')) {
            panel.slideUp(300);
            arrow.text('▼');
        } else {
            panel.slideDown(300);
            arrow.text('▲');
            loadUsageStats();
        }
    };

    /**
     * Load usage statistics
     */
    function loadUsageStats() {
        const days = $('#stats-period').val() || 30;

        $.ajax({
            url: chatgabiCreditConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'chatgabi_get_token_stats',
                nonce: chatgabiCreditConfig.tokenNonce,
                days: days
            },
            success: function(response) {
                if (response.success) {
                    displayUsageStats(response.data.stats);
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to load usage stats:', error);
            }
        });
    }

    /**
     * Display usage statistics
     */
    function displayUsageStats(stats) {
        const summary = stats.summary;

        if (summary) {
            $('#total-conversations').text(summary.total_conversations || 0);
            $('#total-tokens').text((summary.total_tokens || 0).toLocaleString());
            $('#total-credits-used').text((summary.total_credits || 0).toFixed(2));
            $('#avg-tokens').text(Math.round(summary.avg_tokens_per_conversation || 0));
        }

        // Update chart if Chart.js is available
        if (typeof Chart !== 'undefined' && stats.daily_breakdown) {
            updateUsageChart(stats.daily_breakdown);
        }
    }

    /**
     * Update usage chart
     */
    function updateUsageChart(dailyData) {
        const ctx = document.getElementById('usage-chart');
        if (!ctx) return;

        // Destroy existing chart
        if (window.chatgabiCreditFeedback.usageChart) {
            window.chatgabiCreditFeedback.usageChart.destroy();
        }

        const labels = dailyData.map(item => item.date);
        const tokensData = dailyData.map(item => item.tokens || 0);
        const creditsData = dailyData.map(item => item.credits || 0);

        window.chatgabiCreditFeedback.usageChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Tokens Used',
                    data: tokensData,
                    borderColor: '#007cba',
                    backgroundColor: 'rgba(0, 124, 186, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: 'Credits Used',
                    data: creditsData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    /**
     * Show low credit warning
     */
    function showLowCreditWarning(shortfall) {
        const warning = $('#low-credit-warning');
        const message = $('#warning-message');

        message.text(
            chatgabiCreditConfig.strings.lowCreditMessage
                .replace('{shortfall}', shortfall.toFixed(2))
        );
        warning.slideDown(300);
    }

    /**
     * Hide low credit warning
     */
    function hideLowCreditWarning() {
        $('#low-credit-warning').slideUp(300);
    }

    /**
     * Dismiss low credit warning
     */
    window.dismissLowCreditWarning = function() {
        hideLowCreditWarning();
    };

    /**
     * Get user preference
     */
    function getUserPreference(key, defaultValue) {
        if (typeof window.chatgabiLoadUserPreferences === 'function') {
            // This would be async in real implementation
            return defaultValue;
        }
        return defaultValue;
    }

    /**
     * Cleanup on page unload
     */
    $(window).on('beforeunload', function() {
        if (window.chatgabiCreditFeedback.refreshInterval) {
            clearInterval(window.chatgabiCreditFeedback.refreshInterval);
        }
        if (window.chatgabiCreditFeedback.estimationTimeout) {
            clearTimeout(window.chatgabiCreditFeedback.estimationTimeout);
        }
    });

})(jQuery);

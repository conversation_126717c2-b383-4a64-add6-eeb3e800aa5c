<?php
/**
 * ChatGABI Template Management System
 * Handles template creation, management, and generation workflows
 *
 * @package ChatGABI_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get legacy template categories (for backward compatibility)
 * Note: This function has been replaced by the database-driven version in prompt-templates.php
 * Keeping this as a wrapper for any legacy code that might still use it
 */
function chatgabi_get_legacy_template_categories() {
    return array(
        'business-plans' => array(
            'name' => __('Business Plans', 'chatgabi'),
            'singular' => __('Business Plan', 'chatgabi'),
            'description' => __('Comprehensive business plans with African market intelligence, financial projections, and implementation strategies.', 'chatgabi'),
            'templates' => array(
                array(
                    'id' => 'tech-startup-gh',
                    'name' => __('Tech Startup Business Plan', 'chatgabi'),
                    'description' => __('Complete business plan for technology startups in Ghana with MVP strategy and funding roadmap.', 'chatgabi'),
                    'industry' => 'Technology',
                    'country' => 'Ghana',
                    'icon' => 'dashicons-laptop'
                ),
                array(
                    'id' => 'agriculture-ke',
                    'name' => __('Agricultural Business Plan', 'chatgabi'),
                    'description' => __('Seasonal agricultural business plan for Kenya with crop cycles and export opportunities.', 'chatgabi'),
                    'industry' => 'Agriculture',
                    'country' => 'Kenya',
                    'icon' => 'dashicons-carrot'
                ),
                array(
                    'id' => 'retail-ng',
                    'name' => __('Retail Business Plan', 'chatgabi'),
                    'description' => __('Comprehensive retail strategy for Nigerian market with inventory and location analysis.', 'chatgabi'),
                    'industry' => 'Retail',
                    'country' => 'Nigeria',
                    'icon' => 'dashicons-store'
                ),
                array(
                    'id' => 'fintech-za',
                    'name' => __('Fintech Business Plan', 'chatgabi'),
                    'description' => __('Financial technology business plan for South Africa with regulatory compliance focus.', 'chatgabi'),
                    'industry' => 'Financial Technology',
                    'country' => 'South Africa',
                    'icon' => 'dashicons-money-alt'
                )
            )
        ),
        'marketing-strategies' => array(
            'name' => __('Marketing Strategies', 'chatgabi'),
            'singular' => __('Marketing Strategy', 'chatgabi'),
            'description' => __('Culturally-appropriate marketing strategies for African markets with local channel optimization.', 'chatgabi'),
            'templates' => array(
                array(
                    'id' => 'digital-marketing-gh',
                    'name' => __('Digital Marketing Strategy', 'chatgabi'),
                    'description' => __('Mobile-first digital marketing for Ghana with MTN Mobile Money integration.', 'chatgabi'),
                    'industry' => 'Digital Marketing',
                    'country' => 'Ghana',
                    'icon' => 'dashicons-smartphone'
                ),
                array(
                    'id' => 'social-media-ke',
                    'name' => __('Social Media Strategy', 'chatgabi'),
                    'description' => __('Social media marketing strategy for Kenya with M-Pesa and community engagement.', 'chatgabi'),
                    'industry' => 'Social Media',
                    'country' => 'Kenya',
                    'icon' => 'dashicons-share'
                ),
                array(
                    'id' => 'content-marketing-ng',
                    'name' => __('Content Marketing Strategy', 'chatgabi'),
                    'description' => __('Content marketing for Nigerian audience with Nollywood and cultural references.', 'chatgabi'),
                    'industry' => 'Content Marketing',
                    'country' => 'Nigeria',
                    'icon' => 'dashicons-edit'
                ),
                array(
                    'id' => 'brand-strategy-za',
                    'name' => __('Brand Strategy', 'chatgabi'),
                    'description' => __('Professional brand strategy for South African market with multicultural considerations.', 'chatgabi'),
                    'industry' => 'Branding',
                    'country' => 'South Africa',
                    'icon' => 'dashicons-awards'
                )
            )
        ),
        'financial-forecasts' => array(
            'name' => __('Financial Forecasts', 'chatgabi'),
            'singular' => __('Financial Forecast', 'chatgabi'),
            'description' => __('Detailed financial projections with African market factors, currency considerations, and funding strategies.', 'chatgabi'),
            'templates' => array(
                array(
                    'id' => 'startup-forecast-gh',
                    'name' => __('Startup Financial Forecast', 'chatgabi'),
                    'description' => __('3-year financial projections for Ghana startups with Cedi currency analysis.', 'chatgabi'),
                    'industry' => 'Startup',
                    'country' => 'Ghana',
                    'icon' => 'dashicons-chart-line'
                ),
                array(
                    'id' => 'sme-forecast-ke',
                    'name' => __('SME Financial Forecast', 'chatgabi'),
                    'description' => __('Small business financial planning for Kenya with seasonal variations and M-Pesa integration.', 'chatgabi'),
                    'industry' => 'Small Business',
                    'country' => 'Kenya',
                    'icon' => 'dashicons-chart-bar'
                ),
                array(
                    'id' => 'growth-forecast-ng',
                    'name' => __('Growth Stage Forecast', 'chatgabi'),
                    'description' => __('Scaling business financial projections for Nigeria with Naira volatility considerations.', 'chatgabi'),
                    'industry' => 'Growth Stage',
                    'country' => 'Nigeria',
                    'icon' => 'dashicons-arrow-up-alt'
                ),
                array(
                    'id' => 'export-forecast-za',
                    'name' => __('Export Business Forecast', 'chatgabi'),
                    'description' => __('Export-focused financial planning for South Africa with Rand exchange rate analysis.', 'chatgabi'),
                    'industry' => 'Export',
                    'country' => 'South Africa',
                    'icon' => 'dashicons-airplane'
                )
            )
        ),
        'my-templates' => array(
            'name' => __('My Templates', 'chatgabi'),
            'singular' => __('Template', 'chatgabi'),
            'description' => __('Your generated business documents and saved templates.', 'chatgabi'),
            'templates' => chatgabi_get_user_generated_templates()
        )
    );
}

/**
 * Get user generated templates (legacy compatibility)
 */
function chatgabi_get_legacy_user_templates() {
    return chatgabi_get_user_generated_templates();
}

/**
 * Get user's generated templates with performance optimization
 */
function chatgabi_get_user_generated_templates() {
    global $wpdb;

    $user_id = get_current_user_id();
    if (!$user_id) {
        return array();
    }

    // Use cache for user templates to avoid repeated database queries
    $cache_key = 'chatgabi_user_templates_' . $user_id;
    $cached_templates = get_transient($cache_key);

    if ($cached_templates !== false) {
        return $cached_templates;
    }

    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

    // Use cached table existence check
    $cache_key_table = 'chatgabi_table_exists_' . md5($table_name);
    $table_exists = get_transient($cache_key_table);

    if ($table_exists === false) {
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        set_transient($cache_key_table, $table_exists ? 'yes' : 'no', HOUR_IN_SECONDS);
    }

    if ($table_exists !== 'yes') {
        // Try to create table only if it doesn't exist
        try {
            chatgabi_create_templates_table();
            // Update cache
            set_transient($cache_key_table, 'yes', HOUR_IN_SECONDS);
        } catch (Exception $e) {
            error_log('ChatGABI: Failed to create templates table: ' . $e->getMessage());
            return array();
        }
    }

    try {
        $templates = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE user_id = %d ORDER BY created_at DESC LIMIT 20",
            $user_id
        ));

        if (!$templates) {
            return array();
        }

        $formatted_templates = array();
        foreach ($templates as $template) {
            // Add null checks to prevent PHP deprecation warnings
            $template_name = !empty($template->template_name) ? $template->template_name : 'Untitled Template';
            $description = !empty($template->description) ? $template->description : 'No description available';
            $industry_sector = !empty($template->industry_sector) ? $template->industry_sector : 'General';
            $target_country = !empty($template->target_country) ? $template->target_country : '';
            $template_type = !empty($template->template_type) ? $template->template_type : 'general';
            $created_at = !empty($template->created_at) ? $template->created_at : '';
            $status = !empty($template->status) ? $template->status : 'draft';

            $formatted_templates[] = array(
                'id' => $template->id,
                'name' => $template_name,
                'description' => $description,
                'industry' => $industry_sector,
                'country' => chatgabi_get_country_name_from_code($target_country) ?: 'Unknown',
                'icon' => chatgabi_get_template_icon($template_type),
                'type' => $template_type,
                'created_at' => $created_at,
                'status' => $status
            );
        }

        // Cache the formatted templates for 10 minutes
        set_transient($cache_key, $formatted_templates, 10 * MINUTE_IN_SECONDS);

        return $formatted_templates;
    } catch (Exception $e) {
        error_log('ChatGABI: Error loading user templates: ' . $e->getMessage());
        return array();
    }
}

/**
 * Get template usage statistics
 */
function chatgabi_get_template_usage_stats() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

    // Default values
    $predefined_count = 12; // 4 templates per category × 3 categories
    $user_generated = 0;
    $most_popular = __('Business Plans', 'chatgabi');

    try {
        // Check if table exists before querying
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

        if (!$table_exists) {
            // Try to create table
            chatgabi_create_templates_table();
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        }

        if ($table_exists) {
            $user_generated = intval($wpdb->get_var("SELECT COUNT(*) FROM {$table_name}"));

            // Most popular template type
            $popular_type = $wpdb->get_var(
                "SELECT template_type FROM {$table_name}
                 GROUP BY template_type
                 ORDER BY COUNT(*) DESC
                 LIMIT 1"
            );

            if ($popular_type) {
                $most_popular = ucwords(str_replace('-', ' ', $popular_type));
            }
        }
    } catch (Exception $e) {
        error_log('ChatGABI: Error loading template usage stats: ' . $e->getMessage());
    }

    return array(
        'total_templates' => $predefined_count + $user_generated,
        'user_generated' => $user_generated,
        'most_popular' => $most_popular,
        'countries_supported' => 4
    );
}

/**
 * Get template icon based on type
 */
function chatgabi_get_template_icon($template_type) {
    $icons = array(
        'business-plan' => 'dashicons-clipboard',
        'marketing-strategy' => 'dashicons-megaphone',
        'financial-forecast' => 'dashicons-chart-line'
    );
    
    return $icons[$template_type] ?? 'dashicons-media-document';
}

/**
 * Create templates database table with performance optimization
 */
function chatgabi_create_templates_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

    // Use transient cache to avoid repeated table existence checks
    $cache_key = 'chatgabi_table_exists_' . md5($table_name);
    $table_exists = get_transient($cache_key);

    if ($table_exists === false) {
        // Only check database if not cached
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        // Cache result for 1 hour
        set_transient($cache_key, $table_exists ? 'yes' : 'no', HOUR_IN_SECONDS);
    }

    if ($table_exists === 'yes') {
        return true;
    }

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        template_type varchar(50) NOT NULL,
        template_name varchar(255) NOT NULL,
        description text,
        business_idea text NOT NULL,
        target_country varchar(5) NOT NULL,
        industry_sector varchar(100) NOT NULL,
        business_stage varchar(50) NOT NULL,
        document_language varchar(5) NOT NULL DEFAULT 'en',
        generated_content longtext,
        status varchar(20) NOT NULL DEFAULT 'draft',
        tokens_used int(11) NOT NULL DEFAULT 0,
        credits_used int(11) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY template_type (template_type),
        KEY target_country (target_country),
        KEY created_at (created_at)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = dbDelta($sql);

    return !empty($result);
}

/**
 * Handle template actions
 */
function chatgabi_handle_template_actions() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.', 'chatgabi'));
    }
    
    $action = sanitize_text_field($_POST['action']);
    
    switch ($action) {
        case 'delete_template':
            chatgabi_delete_template();
            break;
        case 'duplicate_template':
            chatgabi_duplicate_template();
            break;
        case 'export_template':
            chatgabi_export_template();
            break;
    }
}



/**
 * AJAX handler for generating templates
 */
function chatgabi_ajax_generate_template() {
    // Start output buffering to prevent headers already sent errors
    if (!ob_get_level()) {
        ob_start();
    }

    check_ajax_referer('chatgabi_create_template', 'nonce');

    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => __('You must be logged in to generate templates', 'chatgabi')));
    }
    
    // Validate and sanitize input
    $template_type = sanitize_text_field($_POST['template_type']);
    $business_idea = sanitize_textarea_field($_POST['business_idea']);
    $target_country = sanitize_text_field($_POST['target_country']);
    $industry_sector = sanitize_text_field($_POST['industry_sector']);
    $business_stage = sanitize_text_field($_POST['business_stage']);
    $document_language = sanitize_text_field($_POST['document_language']);
    
    // Validate required fields
    if (empty($template_type) || empty($business_idea) || empty($target_country) || empty($industry_sector)) {
        wp_send_json_error(array('message' => __('Please fill in all required fields', 'chatgabi')));
    }
    
    // Create template record
    $template_id = chatgabi_create_template_record(array(
        'template_type' => $template_type,
        'business_idea' => $business_idea,
        'target_country' => $target_country,
        'industry_sector' => $industry_sector,
        'business_stage' => $business_stage,
        'document_language' => $document_language
    ));
    
    if (!$template_id) {
        wp_send_json_error(array('message' => __('Failed to create template record', 'chatgabi')));
    }
    
    // Redirect to template generation page
    $redirect_url = admin_url('admin.php?page=chatgabi-template-generator&template_id=' . $template_id);
    
    wp_send_json_success(array(
        'template_id' => $template_id,
        'redirect_url' => $redirect_url,
        'message' => __('Template created successfully', 'chatgabi')
    ));
}
add_action('wp_ajax_chatgabi_generate_template', 'chatgabi_ajax_generate_template');

/**
 * Create template record in database
 */
function chatgabi_create_template_record($data) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';
    
    // Ensure table exists
    chatgabi_create_templates_table();
    
    // Add null checks to prevent PHP deprecation warnings
    $template_type = !empty($data['template_type']) ? $data['template_type'] : 'general';
    $industry_sector = !empty($data['industry_sector']) ? $data['industry_sector'] : 'General';
    $target_country = !empty($data['target_country']) ? $data['target_country'] : '';
    $business_idea = !empty($data['business_idea']) ? $data['business_idea'] : '';
    $business_stage = !empty($data['business_stage']) ? $data['business_stage'] : 'idea';
    $document_language = !empty($data['document_language']) ? $data['document_language'] : 'en';

    // Generate template name
    $template_name = chatgabi_generate_template_name($template_type, $industry_sector, $target_country);

    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => get_current_user_id(),
            'template_type' => $template_type,
            'template_name' => $template_name,
            'description' => sprintf(__('%s for %s business in %s', 'chatgabi'),
                ucwords(str_replace('-', ' ', $template_type)),
                $industry_sector,
                chatgabi_get_country_name_from_code($target_country)
            ),
            'business_idea' => $business_idea,
            'target_country' => $target_country,
            'industry_sector' => $industry_sector,
            'business_stage' => $business_stage,
            'document_language' => $document_language,
            'status' => 'pending'
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
    );
    
    return $result ? $wpdb->insert_id : false;
}

/**
 * Generate template name
 */
function chatgabi_generate_template_name($template_type, $industry_sector, $country_code) {
    // Add null checks to prevent PHP deprecation warnings
    $template_type = !empty($template_type) ? $template_type : 'general';
    $industry_sector = !empty($industry_sector) ? $industry_sector : 'General';
    $country_code = !empty($country_code) ? $country_code : '';

    $type_names = array(
        'business-plan' => __('Business Plan', 'chatgabi'),
        'marketing-strategy' => __('Marketing Strategy', 'chatgabi'),
        'financial-forecast' => __('Financial Forecast', 'chatgabi')
    );

    $type_name = isset($type_names[$template_type]) ? $type_names[$template_type] : ucwords(str_replace('-', ' ', $template_type));
    $country_name = chatgabi_get_country_name_from_code($country_code);

    return sprintf('%s - %s (%s)', $type_name, $industry_sector, $country_name);
}

/**
 * Get country name from country code
 */
function chatgabi_get_country_name_from_code($country_code) {
    // Add null check to prevent PHP deprecation warnings
    if (empty($country_code) || !is_string($country_code)) {
        return '';
    }

    $countries = array(
        'GH' => 'Ghana',
        'KE' => 'Kenya',
        'NG' => 'Nigeria',
        'ZA' => 'South Africa'
    );

    return isset($countries[$country_code]) ? $countries[$country_code] : '';
}

/**
 * Get available languages with cultural context
 */
function chatgabi_get_available_languages() {
    return array(
        'en' => array(
            'name' => __('English', 'chatgabi'),
            'native_name' => 'English',
            'countries' => array('GH', 'KE', 'NG', 'ZA'),
            'cultural_context' => 'international_business'
        ),
        'tw' => array(
            'name' => __('Twi (Ghana)', 'chatgabi'),
            'native_name' => 'Twi',
            'countries' => array('GH'),
            'cultural_context' => 'akan_business_culture'
        ),
        'sw' => array(
            'name' => __('Swahili (Kenya)', 'chatgabi'),
            'native_name' => 'Kiswahili',
            'countries' => array('KE'),
            'cultural_context' => 'east_african_business_culture'
        ),
        'yo' => array(
            'name' => __('Yoruba (Nigeria)', 'chatgabi'),
            'native_name' => 'Yorùbá',
            'countries' => array('NG'),
            'cultural_context' => 'yoruba_business_culture'
        ),
        'zu' => array(
            'name' => __('Zulu (South Africa)', 'chatgabi'),
            'native_name' => 'isiZulu',
            'countries' => array('ZA'),
            'cultural_context' => 'ubuntu_business_culture'
        )
    );
}

/**
 * Get cultural business practices for a language
 */
function chatgabi_get_cultural_business_practices($language_code) {
    $practices = array(
        'en' => array(
            'communication_style' => 'Direct and professional communication',
            'business_etiquette' => 'Formal meetings, punctuality, written agreements',
            'decision_making' => 'Data-driven decisions with stakeholder consultation',
            'relationship_building' => 'Professional networking and trust-building',
            'negotiation_style' => 'Fact-based negotiation with clear terms'
        ),
        'tw' => array(
            'communication_style' => 'Respectful and hierarchical communication with elders',
            'business_etiquette' => 'Greetings with respect, community involvement, oral agreements honored',
            'decision_making' => 'Consensus-building with community elders and family input',
            'relationship_building' => 'Extended family networks and community connections',
            'negotiation_style' => 'Patient negotiation with respect for relationships'
        ),
        'sw' => array(
            'communication_style' => 'Polite and indirect communication with Ubuntu principles',
            'business_etiquette' => 'Harambee spirit, collective responsibility, time flexibility',
            'decision_making' => 'Community-centered decisions with collective benefit',
            'relationship_building' => 'Strong community ties and mutual support systems',
            'negotiation_style' => 'Collaborative approach with win-win outcomes'
        ),
        'yo' => array(
            'communication_style' => 'Respectful communication with age and status recognition',
            'business_etiquette' => 'Respect for hierarchy, extended greetings, relationship-first approach',
            'decision_making' => 'Elder consultation with family and community input',
            'relationship_building' => 'Extended family networks and community solidarity',
            'negotiation_style' => 'Relationship-based negotiation with patience and respect'
        ),
        'zu' => array(
            'communication_style' => 'Ubuntu-based communication emphasizing humanity and interconnectedness',
            'business_etiquette' => 'Respect for elders, collective decision-making, community benefit focus',
            'decision_making' => 'Consensus-building with community welfare consideration',
            'relationship_building' => 'Strong community bonds and mutual responsibility',
            'negotiation_style' => 'Collaborative negotiation with community benefit focus'
        )
    );

    return isset($practices[$language_code]) ? $practices[$language_code] : $practices['en'];
}

/**
 * Load language-specific template content with caching
 */
function chatgabi_load_language_template($template_type, $language_code) {
    // Use transient cache to avoid repeated file operations
    $cache_key = 'chatgabi_template_' . $language_code . '_' . md5($template_type);
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $templates_dir = WP_CONTENT_DIR . '/datasets/templates';
    $template_file = $templates_dir . '/' . $language_code . '/business_plan_templates.json';
    $fallback_file = $templates_dir . '/en/business_plan_templates.json';

    $template_data = null;

    // Try to load language-specific template
    if (file_exists($template_file)) {
        $content = file_get_contents($template_file);
        $template_data = json_decode($content, true);
    }

    // Fallback to English if language-specific template not found or invalid
    if (!$template_data && file_exists($fallback_file)) {
        $content = file_get_contents($fallback_file);
        $template_data = json_decode($content, true);
    }

    // Cache the result for 30 minutes
    if ($template_data) {
        set_transient($cache_key, $template_data, 30 * MINUTE_IN_SECONDS);
    }

    return $template_data;
}

/**
 * Get template content by type and language
 */
function chatgabi_get_template_content_by_type($template_type, $language_code) {
    $template_data = chatgabi_load_language_template($template_type, $language_code);

    if (!$template_data || !isset($template_data['templates'])) {
        return null;
    }

    // Map template types to data structure
    $type_mapping = array(
        'business-plan' => 'business_plan',
        'marketing-strategy' => 'marketing_strategy',
        'financial-forecast' => 'financial_forecast'
    );

    $data_key = isset($type_mapping[$template_type]) ? $type_mapping[$template_type] : $template_type;

    if (isset($template_data['templates'][$data_key])) {
        return array(
            'language' => $template_data['language'],
            'language_name' => $template_data['language_name'],
            'cultural_context' => isset($template_data['cultural_context']) ? $template_data['cultural_context'] : '',
            'cultural_practices' => isset($template_data['cultural_practices']) ? $template_data['cultural_practices'] : array(),
            'template_content' => $template_data['templates'][$data_key],
            'local_terms' => isset($template_data['local_business_terms']) ? $template_data['local_business_terms'] : array()
        );
    }

    return null;
}

/**
 * Get localized template content for preview
 */
function chatgabi_get_localized_template_preview($template_id, $language_code) {
    $template_data = chatgabi_load_language_template('business_plan', $language_code);

    if (!$template_data) {
        return null;
    }

    $cultural_context = isset($template_data['cultural_context']) ? $template_data['cultural_context'] : '';
    $cultural_practices = isset($template_data['cultural_practices']) ? $template_data['cultural_practices'] : array();

    // Map template IDs to template types
    $template_mapping = array(
        'tech-startup' => 'tech_startup',
        'agricultural' => 'agricultural',
        'retail' => 'retail',
        'fintech' => 'fintech'
    );

    $template_type = isset($template_mapping[$template_id]) ? $template_mapping[$template_id] : 'business_plan';

    return array(
        'language' => $template_data['language'],
        'language_name' => $template_data['language_name'],
        'cultural_context' => $cultural_context,
        'cultural_practices' => $cultural_practices,
        'templates' => $template_data['templates'],
        'local_terms' => isset($template_data['local_business_terms']) ? $template_data['local_business_terms'] : array()
    );
}



/**
 * Get template data by ID
 */
function chatgabi_get_template_data($template_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE id = %d",
        $template_id
    ));

    return $template;
}

/**
 * Process template generation
 */
function chatgabi_process_template_generation($template_id) {
    // Get template data
    $template_data = chatgabi_get_template_data($template_id);

    if (!$template_data) {
        wp_die(__('Template not found', 'chatgabi'));
    }

    // Get form data
    $business_name = sanitize_text_field($_POST['business_name']);
    $target_market = sanitize_textarea_field($_POST['target_market']);
    $unique_value = sanitize_textarea_field($_POST['unique_value']);
    $business_model = sanitize_text_field($_POST['business_model']);

    // Build comprehensive prompt for AI generation
    $prompt = chatgabi_build_template_generation_prompt($template_data, array(
        'business_name' => $business_name,
        'target_market' => $target_market,
        'unique_value' => $unique_value,
        'business_model' => $business_model
    ));

    // Generate content using existing AI integration
    if (function_exists('businesscraft_ai_process_openai_request')) {
        // Get user's preferred language for AI generation
        $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';

        // Generate content using the main OpenAI integration function
        $ai_response = businesscraft_ai_process_openai_request(
            $prompt,
            $language_code,
            'template_generation',
            get_current_user_id()
        );

        if (is_wp_error($ai_response)) {
            wp_die(__('AI generation failed: ', 'chatgabi') . $ai_response->get_error_message());
        }

        $generated_content = isset($ai_response['response']) ? $ai_response['response'] : '';

        if (empty($generated_content)) {
            wp_die(__('AI generation returned empty content', 'chatgabi'));
        }

        // Update template with generated content
        global $wpdb;
        $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

        $update_result = $wpdb->update(
            $table_name,
            array(
                'generated_content' => $generated_content,
                'status' => 'completed',
                'tokens_used' => isset($ai_response['tokens_used']) ? $ai_response['tokens_used'] : 0,
                'credits_used' => isset($ai_response['credits_used']) ? $ai_response['credits_used'] : 0
            ),
            array('id' => $template_id),
            array('%s', '%s', '%d', '%d'),
            array('%d')
        );

        if ($update_result === false) {
            wp_die(__('Failed to save generated template', 'chatgabi'));
        }

        // Redirect to review page
        wp_redirect(admin_url('admin.php?page=chatgabi-template-generator&template_id=' . $template_id . '&step=4'));
        exit;
    } else {
        wp_die(__('AI generation service not available. Please check OpenAI integration.', 'chatgabi'));
    }
}

/**
 * Build enhanced template generation prompt with cultural context
 */
function chatgabi_build_template_generation_prompt($template_data, $form_data) {
    // Suppress any potential warnings to prevent headers already sent errors
    $old_error_reporting = error_reporting(E_ERROR | E_PARSE);

    $country_name = chatgabi_get_country_name_from_code($template_data->target_country);
    $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';

    // Get cultural business practices for the language
    $cultural_practices = chatgabi_get_cultural_business_practices($language_code);

    // Get language-specific template content
    $language_template_content = chatgabi_get_template_content_by_type($template_data->template_type, $language_code);

    // Get African context for the country and sector
    $african_context = '';
    if (function_exists('get_sector_context_by_country')) {
        $sector_context = get_sector_context_by_country($country_name, $template_data->industry_sector);
        if ($sector_context && is_array($sector_context)) {
            // Safely extract context data with proper null checks
            $overview = isset($sector_context['overview']) && !empty($sector_context['overview'])
                ? substr($sector_context['overview'], 0, 200) . '...'
                : 'Market overview data not available';

            $regulatory = isset($sector_context['regulatory_environment']) && !empty($sector_context['regulatory_environment'])
                ? substr($sector_context['regulatory_environment'], 0, 150) . '...'
                : 'Regulatory information not available';

            $market_chars = isset($sector_context['market_characteristics']) && !empty($sector_context['market_characteristics'])
                ? substr($sector_context['market_characteristics'], 0, 150) . '...'
                : 'Market characteristics data not available';

            $african_context = sprintf(
                "African Market Context for %s in %s:\n- Overview: %s\n- Regulatory Environment: %s\n- Market Characteristics: %s\n\n",
                $template_data->industry_sector,
                $country_name,
                $overview,
                $regulatory,
                $market_chars
            );
        } else {
            // Fallback when sector context is not available
            $african_context = sprintf(
                "African Market Context for %s in %s:\n- Focus on local market conditions and opportunities\n- Consider regulatory requirements and compliance\n- Adapt to local business practices and culture\n\n",
                $template_data->industry_sector,
                $country_name
            );
        }
    }

    // Build cultural context section
    $cultural_context = '';
    if ($language_template_content && !empty($language_template_content['cultural_practices'])) {
        $practices = $language_template_content['cultural_practices'];
        $cultural_context = sprintf(
            "Cultural Business Context for %s (%s):\n- Communication Style: %s\n- Business Etiquette: %s\n- Decision Making: %s\n- Relationship Building: %s\n- Negotiation Style: %s\n\n",
            $language_template_content['language_name'],
            $country_name,
            isset($practices['communication_style']) ? $practices['communication_style'] : 'Direct and respectful',
            isset($practices['business_etiquette']) ? $practices['business_etiquette'] : 'Professional and courteous',
            isset($practices['decision_making']) ? $practices['decision_making'] : 'Collaborative approach',
            isset($practices['relationship_building']) ? $practices['relationship_building'] : 'Trust-based relationships',
            isset($practices['negotiation_style']) ? $practices['negotiation_style'] : 'Respectful and patient'
        );
    } else {
        // Fallback cultural context when specific practices are not available
        $cultural_context = sprintf(
            "Cultural Business Context for %s:\n- Respect local business customs and traditions\n- Build trust through community engagement\n- Consider family and community influence on decisions\n- Adapt communication style to local preferences\n\n",
            $country_name
        );
    }

    // Build language-specific terminology section
    $terminology_context = '';
    if ($language_template_content && !empty($language_template_content['local_terms'])) {
        $terminology_context = sprintf(
            "Local Business Terminology (%s):\n",
            $language_template_content['language_name']
        );
        $term_count = 0;
        foreach ($language_template_content['local_terms'] as $local_term => $english_term) {
            if ($term_count < 10) { // Limit to prevent token overflow
                $terminology_context .= "- {$english_term}: {$local_term}\n";
                $term_count++;
            }
        }
        $terminology_context .= "\n";
    }

    // Build prompt based on template type
    $prompt = '';
    switch ($template_data->template_type) {
        case 'business-plan':
            $prompt = chatgabi_build_business_plan_prompt($template_data, $form_data, $african_context, $cultural_context, $terminology_context);
            break;
        case 'marketing-strategy':
            $prompt = chatgabi_build_marketing_strategy_prompt($template_data, $form_data, $african_context, $cultural_context, $terminology_context);
            break;
        case 'financial-forecast':
            $prompt = chatgabi_build_financial_forecast_prompt($template_data, $form_data, $african_context, $cultural_context, $terminology_context);
            break;
        default:
            $prompt = chatgabi_build_generic_template_prompt($template_data, $form_data, $african_context, $cultural_context, $terminology_context);
            break;
    }

    // Restore original error reporting
    error_reporting($old_error_reporting);

    return $prompt;
}

/**
 * Build business plan generation prompt
 */
function chatgabi_build_business_plan_prompt($template_data, $form_data, $african_context, $cultural_context = '', $terminology_context = '') {
    $country_name = chatgabi_get_country_name_from_code($template_data->target_country);
    $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';

    $prompt = "You are an expert business consultant specializing in African markets. Create a comprehensive business plan for the following business:\n\n";

    $prompt .= "BUSINESS DETAILS:\n";
    $prompt .= "- Business Name: {$form_data['business_name']}\n";
    $prompt .= "- Industry: {$template_data->industry_sector}\n";
    $prompt .= "- Country: {$country_name}\n";
    $prompt .= "- Business Stage: {$template_data->business_stage}\n";
    $prompt .= "- Business Idea: {$template_data->business_idea}\n";
    $prompt .= "- Target Market: {$form_data['target_market']}\n";
    $prompt .= "- Unique Value Proposition: {$form_data['unique_value']}\n";
    $prompt .= "- Business Model: {$form_data['business_model']}\n";
    $prompt .= "- Document Language: {$language_code}\n\n";

    $prompt .= $african_context;
    $prompt .= $cultural_context;
    $prompt .= $terminology_context;

    $prompt .= "Create a detailed business plan with the following sections:\n\n";
    $prompt .= "1. EXECUTIVE SUMMARY\n";
    $prompt .= "2. COMPANY DESCRIPTION\n";
    $prompt .= "3. MARKET ANALYSIS (specific to {$country_name})\n";
    $prompt .= "4. ORGANIZATION & MANAGEMENT\n";
    $prompt .= "5. PRODUCTS/SERVICES\n";
    $prompt .= "6. MARKETING & SALES STRATEGY\n";
    $prompt .= "7. FUNDING REQUEST\n";
    $prompt .= "8. FINANCIAL PROJECTIONS\n";
    $prompt .= "9. IMPLEMENTATION TIMELINE\n";
    $prompt .= "10. RISK ANALYSIS\n\n";

    $prompt .= "Focus on:\n";
    $prompt .= "- African market opportunities and challenges\n";
    $prompt .= "- Local regulatory requirements\n";
    $prompt .= "- Cultural considerations and business practices\n";
    $prompt .= "- Mobile-first approach where relevant\n";
    $prompt .= "- Local payment methods and financial infrastructure\n";
    $prompt .= "- Realistic financial projections in local currency\n";
    $prompt .= "- Community impact and social responsibility\n\n";

    // Add language-specific instructions
    if ($language_code !== 'en') {
        $prompt .= "LANGUAGE REQUIREMENTS:\n";
        $prompt .= "- Write the business plan in {$language_code} language\n";
        $prompt .= "- Use appropriate local business terminology\n";
        $prompt .= "- Incorporate cultural business practices and values\n";
        $prompt .= "- Ensure content is culturally appropriate and relevant\n\n";
    }

    $prompt .= "Format the response as a professional business plan document with clear sections and actionable insights.";

    return $prompt;
}

/**
 * Build marketing strategy generation prompt
 */
function chatgabi_build_marketing_strategy_prompt($template_data, $form_data, $african_context, $cultural_context = '', $terminology_context = '') {
    $country_name = chatgabi_get_country_name_from_code($template_data->target_country);
    $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';

    $prompt = "You are an expert marketing strategist specializing in African markets. Create a comprehensive marketing strategy for:\n\n";

    $prompt .= "BUSINESS DETAILS:\n";
    $prompt .= "- Business Name: {$form_data['business_name']}\n";
    $prompt .= "- Industry: {$template_data->industry_sector}\n";
    $prompt .= "- Country: {$country_name}\n";
    $prompt .= "- Target Market: {$form_data['target_market']}\n";
    $prompt .= "- Unique Value Proposition: {$form_data['unique_value']}\n";
    $prompt .= "- Business Model: {$form_data['business_model']}\n";
    $prompt .= "- Document Language: {$language_code}\n\n";

    $prompt .= $african_context;
    $prompt .= $cultural_context;
    $prompt .= $terminology_context;

    $prompt .= "Create a detailed marketing strategy with:\n\n";
    $prompt .= "1. MARKET POSITIONING\n";
    $prompt .= "2. TARGET AUDIENCE ANALYSIS\n";
    $prompt .= "3. COMPETITIVE ANALYSIS\n";
    $prompt .= "4. MARKETING MIX (4Ps)\n";
    $prompt .= "5. DIGITAL MARKETING STRATEGY\n";
    $prompt .= "6. TRADITIONAL MARKETING CHANNELS\n";
    $prompt .= "7. CONTENT MARKETING PLAN\n";
    $prompt .= "8. BUDGET ALLOCATION\n";
    $prompt .= "9. IMPLEMENTATION TIMELINE\n";
    $prompt .= "10. METRICS & KPIs\n\n";

    $prompt .= "Focus on {$country_name}-specific:\n";
    $prompt .= "- Popular social media platforms\n";
    $prompt .= "- Local influencers and partnerships\n";
    $prompt .= "- Cultural marketing approaches and sensitivities\n";
    $prompt .= "- Mobile marketing strategies\n";
    $prompt .= "- Local media channels and traditional communication\n";
    $prompt .= "- Community engagement and word-of-mouth tactics\n";
    $prompt .= "- Seasonal and cultural event marketing\n\n";

    // Add language-specific instructions
    if ($language_code !== 'en') {
        $prompt .= "LANGUAGE REQUIREMENTS:\n";
        $prompt .= "- Write the marketing strategy in {$language_code} language\n";
        $prompt .= "- Use appropriate local marketing terminology\n";
        $prompt .= "- Incorporate cultural communication styles\n";
        $prompt .= "- Ensure messaging is culturally appropriate\n\n";
    }

    $prompt .= "Format as a professional marketing strategy document with actionable recommendations.";

    return $prompt;
}

/**
 * Build financial forecast generation prompt
 */
function chatgabi_build_financial_forecast_prompt($template_data, $form_data, $african_context, $cultural_context = '', $terminology_context = '') {
    $country_name = chatgabi_get_country_name_from_code($template_data->target_country);
    $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';

    // Get local currency
    $currencies = array(
        'Ghana' => 'GHS (Ghana Cedis)',
        'Kenya' => 'KES (Kenyan Shilling)',
        'Nigeria' => 'NGN (Nigerian Naira)',
        'South Africa' => 'ZAR (South African Rand)'
    );
    $local_currency = $currencies[$country_name] ?? 'USD';

    $prompt = "You are an expert financial analyst specializing in African markets. Create a comprehensive financial forecast for:\n\n";

    $prompt .= "BUSINESS DETAILS:\n";
    $prompt .= "- Business Name: {$form_data['business_name']}\n";
    $prompt .= "- Industry: {$template_data->industry_sector}\n";
    $prompt .= "- Country: {$country_name}\n";
    $prompt .= "- Currency: {$local_currency}\n";
    $prompt .= "- Business Model: {$form_data['business_model']}\n";
    $prompt .= "- Target Market: {$form_data['target_market']}\n";
    $prompt .= "- Document Language: {$language_code}\n\n";

    $prompt .= $african_context;
    $prompt .= $cultural_context;
    $prompt .= $terminology_context;

    $prompt .= "Create a detailed 3-year financial forecast including:\n\n";
    $prompt .= "1. REVENUE PROJECTIONS\n";
    $prompt .= "2. COST STRUCTURE ANALYSIS\n";
    $prompt .= "3. PROFIT & LOSS STATEMENTS\n";
    $prompt .= "4. CASH FLOW PROJECTIONS\n";
    $prompt .= "5. BREAK-EVEN ANALYSIS\n";
    $prompt .= "6. FUNDING REQUIREMENTS\n";
    $prompt .= "7. FINANCIAL RATIOS\n";
    $prompt .= "8. SCENARIO ANALYSIS\n";
    $prompt .= "9. KEY ASSUMPTIONS\n";
    $prompt .= "10. RISK FACTORS\n\n";

    $prompt .= "Consider {$country_name}-specific factors:\n";
    $prompt .= "- Local market pricing and cost structures\n";
    $prompt .= "- Currency fluctuation risks and hedging strategies\n";
    $prompt .= "- Local tax implications and regulatory costs\n";
    $prompt .= "- Seasonal business variations and agricultural cycles\n";
    $prompt .= "- Infrastructure costs and utility expenses\n";
    $prompt .= "- Local financing options and interest rates\n";
    $prompt .= "- Traditional payment methods and cash flow patterns\n";
    $prompt .= "- Community financial practices and obligations\n\n";

    // Add language-specific instructions
    if ($language_code !== 'en') {
        $prompt .= "LANGUAGE REQUIREMENTS:\n";
        $prompt .= "- Write the financial forecast in {$language_code} language\n";
        $prompt .= "- Use appropriate local financial terminology\n";
        $prompt .= "- Incorporate cultural financial practices and concepts\n";
        $prompt .= "- Ensure financial explanations are culturally relevant\n";
        $prompt .= "- Consider traditional and modern financial instruments\n\n";
    }

    $prompt .= "Present all figures in {$local_currency} and format as a professional financial forecast document with clear assumptions, detailed projections, and actionable insights.";

    return $prompt;
}

/**
 * Build generic template prompt
 */
function chatgabi_build_generic_template_prompt($template_data, $form_data, $african_context, $cultural_context = '', $terminology_context = '') {
    $country_name = chatgabi_get_country_name_from_code($template_data->target_country);
    $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';
    $document_type = ucwords(str_replace('-', ' ', $template_data->template_type));

    $prompt = "You are an expert business consultant specializing in African markets. Create a comprehensive {$document_type} for:\n\n";

    $prompt .= "BUSINESS DETAILS:\n";
    $prompt .= "- Business Name: {$form_data['business_name']}\n";
    $prompt .= "- Industry: {$template_data->industry_sector}\n";
    $prompt .= "- Country: {$country_name}\n";
    $prompt .= "- Business Idea: {$template_data->business_idea}\n";
    $prompt .= "- Target Market: {$form_data['target_market']}\n";
    $prompt .= "- Unique Value Proposition: {$form_data['unique_value']}\n";
    $prompt .= "- Document Language: {$language_code}\n\n";

    $prompt .= $african_context;
    $prompt .= $cultural_context;
    $prompt .= $terminology_context;

    // Add language-specific instructions
    if ($language_code !== 'en') {
        $prompt .= "LANGUAGE REQUIREMENTS:\n";
        $prompt .= "- Write the {$document_type} in {$language_code} language\n";
        $prompt .= "- Use appropriate local business terminology\n";
        $prompt .= "- Incorporate cultural business practices and values\n";
        $prompt .= "- Ensure content is culturally appropriate and relevant\n\n";
    }

    $prompt .= "Create a detailed {$document_type} that addresses the specific needs of this business in the {$country_name} market. ";
    $prompt .= "Focus on practical, actionable insights that consider local market conditions, cultural factors, and business environment.";

    return $prompt;
}

/**
 * AJAX handler for template preview
 */
function chatgabi_ajax_get_template_preview() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_template_preview')) {
        wp_send_json_error(array('message' => __('Security check failed', 'chatgabi')));
    }

    $template_id = sanitize_text_field($_POST['template_id']);
    $language_code = sanitize_text_field($_POST['language']);

    // Get localized template preview
    $preview_data = chatgabi_get_localized_template_preview($template_id, $language_code);

    if (!$preview_data) {
        wp_send_json_error(array('message' => __('Template preview not available', 'chatgabi')));
    }

    // Generate preview HTML
    $preview_html = chatgabi_generate_preview_html($template_id, $preview_data);

    wp_send_json_success(array('content' => $preview_html));
}
add_action('wp_ajax_chatgabi_get_template_preview', 'chatgabi_ajax_get_template_preview');



/**
 * Generate preview HTML for template
 */
function chatgabi_generate_preview_html($template_id, $preview_data) {
    $language_name = $preview_data['language_name'];
    $cultural_context = $preview_data['cultural_context'];
    $cultural_practices = $preview_data['cultural_practices'];

    $html = '<div class="template-preview language-' . esc_attr($preview_data['language']) . '">';

    // Template header with language selector
    $html .= '<div class="preview-header">';
    $html .= '<div class="header-content">';
    $html .= '<h3>' . sprintf(__('Template Preview - %s', 'chatgabi'), esc_html($language_name)) . '</h3>';
    $html .= '<p class="cultural-context">' . sprintf(__('Designed for %s business culture', 'chatgabi'), esc_html($cultural_context)) . '</p>';
    $html .= '</div>';

    // Language selector
    $html .= '<div class="language-selector">';
    $html .= '<label for="preview-language">' . __('Language:', 'chatgabi') . '</label>';
    $html .= '<select id="preview-language" onchange="switchTemplateLanguage(this.value)">';

    $available_languages = chatgabi_get_available_languages();
    foreach ($available_languages as $lang_code => $lang_data) {
        $selected = ($lang_code === $preview_data['language']) ? 'selected' : '';
        $html .= '<option value="' . esc_attr($lang_code) . '" ' . $selected . '>' . esc_html($lang_data['name']) . '</option>';
    }

    $html .= '</select>';
    $html .= '</div>';
    $html .= '</div>';

    // Cultural practices section
    if (!empty($cultural_practices)) {
        $html .= '<div class="cultural-practices">';
        $html .= '<h4>' . __('Cultural Business Practices', 'chatgabi') . '</h4>';
        $html .= '<ul>';

        foreach ($cultural_practices as $practice => $description) {
            $practice_label = ucwords(str_replace('_', ' ', $practice));
            $html .= '<li><strong>' . esc_html($practice_label) . ':</strong> ' . esc_html($description) . '</li>';
        }

        $html .= '</ul>';
        $html .= '</div>';
    }

    // Template sections
    if (isset($preview_data['templates']['business_plan'])) {
        $business_plan = $preview_data['templates']['business_plan'];

        $html .= '<div class="template-sections">';
        $html .= '<h4>' . __('Document Sections', 'chatgabi') . '</h4>';
        $html .= '<ul>';

        foreach ($business_plan as $section_key => $section_data) {
            if (isset($section_data['title'])) {
                $html .= '<li><strong>' . esc_html($section_data['title']) . '</strong>';
                if (isset($section_data['content'])) {
                    $html .= '<br><small>' . esc_html(substr($section_data['content'], 0, 100)) . '...</small>';
                }
                $html .= '</li>';
            }
        }

        $html .= '</ul>';
        $html .= '</div>';
    }

    // Local business terms
    if (!empty($preview_data['local_terms'])) {
        $html .= '<div class="local-terms">';
        $html .= '<h4>' . __('Local Business Terminology', 'chatgabi') . '</h4>';
        $html .= '<div class="terms-grid">';

        $term_count = 0;
        foreach ($preview_data['local_terms'] as $local_term => $english_term) {
            if ($term_count >= 6) break; // Limit to 6 terms for preview
            $html .= '<div class="term-pair">';
            $html .= '<span class="local-term">' . esc_html($local_term) . '</span>';
            $html .= '<span class="english-term">(' . esc_html($english_term) . ')</span>';
            $html .= '</div>';
            $term_count++;
        }

        $html .= '</div>';
        $html .= '</div>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Analytics Dashboard Component
 * 
 * Handles analytics visualization and user insights
 * Extends BaseComponent with analytics-specific functionality
 */

class AnalyticsDashboard extends window.BusinessCraftAI.BaseComponent {
    constructor(element, options = {}) {
        super(element, options);
        this.charts = new Map();
        this.analyticsData = null;
        this.refreshInterval = null;
        this.isLoading = false;
    }
    
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            autoRefresh: true,
            refreshInterval: 300000, // 5 minutes
            enableCharts: true,
            enableInsights: true,
            enableTimeline: true,
            chartOptions: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 750,
                    easing: 'easeInOutQuart'
                }
            }
        };
    }
    
    getDefaultRole() {
        return 'main';
    }
    
    isInteractive() {
        return true;
    }
    
    onInit() {
        this.setupAnalyticsContainer();
        this.setupChartContainers();
        this.setupControlElements();
        this.loadAnalyticsData();
        
        if (this.options.autoRefresh) {
            this.startAutoRefresh();
        }
    }
    
    setupAnalyticsContainer() {
        if (!this.element) return;
        
        this.element.setAttribute('aria-label', 'Analytics Dashboard');
        this.element.classList.add('analytics-dashboard', 'bcai-component');
        
        // Add loading state
        this.element.classList.add('loading');
    }
    
    setupChartContainers() {
        this.chartContainers = {
            usage: this.element.querySelector('#usage-chart'),
            features: this.element.querySelector('#features-chart'),
            credits: this.element.querySelector('#credits-chart')
        };
        
        // Set up chart accessibility
        Object.entries(this.chartContainers).forEach(([name, container]) => {
            if (container) {
                container.setAttribute('role', 'img');
                container.setAttribute('aria-label', `${name} chart`);
                container.setAttribute('tabindex', '0');
            }
        });
    }
    
    setupControlElements() {
        // Period selector
        this.periodSelector = this.element.querySelector('#usage-chart-period');
        if (this.periodSelector) {
            this.periodSelector.addEventListener('change', this.handlePeriodChange.bind(this));
            this.periodSelector.setAttribute('aria-label', 'Select time period for analytics');
        }
        
        // Refresh button
        this.refreshButton = this.element.querySelector('.refresh-analytics');
        if (this.refreshButton) {
            this.refreshButton.addEventListener('click', this.handleRefreshClick.bind(this));
            this.refreshButton.setAttribute('aria-label', 'Refresh analytics data');
        }
        
        // Export button
        this.exportButton = this.element.querySelector('.export-analytics');
        if (this.exportButton) {
            this.exportButton.addEventListener('click', this.handleExportClick.bind(this));
            this.exportButton.setAttribute('aria-label', 'Export analytics data');
        }
    }
    
    async loadAnalyticsData(period = 30) {
        if (this.isLoading) return;
        
        this.setLoading(true);
        this.announceToScreenReader('Loading analytics data');
        
        try {
            const response = await this.fetchAnalyticsData(period);
            
            if (response.success) {
                this.analyticsData = response.data;
                this.updateDashboard();
                this.announceToScreenReader('Analytics data loaded successfully');
                this.emit('analytics:data-loaded', { data: this.analyticsData });
            } else {
                throw new Error(response.data || 'Failed to load analytics data');
            }
            
        } catch (error) {
            this.handleAnalyticsError(error);
        } finally {
            this.setLoading(false);
        }
    }
    
    async fetchAnalyticsData(period) {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_get_user_analytics');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        formData.append('period', period);
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    updateDashboard() {
        if (!this.analyticsData) return;
        
        this.updateSummaryCards();
        this.updateCharts();
        this.updateInsights();
        this.updateTimeline();
        
        this.emit('analytics:dashboard-updated', { data: this.analyticsData });
    }
    
    updateSummaryCards() {
        const { summary } = this.analyticsData;
        if (!summary) return;
        
        const cards = {
            'total-conversations-analytics': summary.conversations?.current || 0,
            'credits-used-analytics': summary.credits_used?.current || 0,
            'templates-generated-analytics': summary.templates?.current || 0,
            'opportunities-viewed-analytics': summary.opportunities?.current || 0
        };
        
        const trends = {
            'conversations-trend': summary.conversations?.trend || 0,
            'credits-trend': summary.credits_used?.trend || 0,
            'templates-trend': summary.templates?.trend || 0,
            'opportunities-trend': summary.opportunities?.trend || 0
        };
        
        // Update values with animation
        Object.entries(cards).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateValue(element, 0, value, 1000);
                element.setAttribute('aria-label', `${id.replace('-analytics', '').replace('-', ' ')}: ${value.toLocaleString()}`);
            }
        });
        
        // Update trends
        Object.entries(trends).forEach(([id, trend]) => {
            const element = document.getElementById(id);
            if (element) {
                const isPositive = trend >= 0;
                const trendText = `${isPositive ? '+' : ''}${trend}%`;
                element.textContent = trendText;
                element.className = `card-trend ${isPositive ? 'positive' : 'negative'}`;
                element.setAttribute('aria-label', `Trend: ${trendText} ${isPositive ? 'increase' : 'decrease'}`);
            }
        });
    }
    
    updateCharts() {
        if (!this.options.enableCharts) return;
        
        const { charts } = this.analyticsData;
        if (!charts) return;
        
        // Update usage chart
        if (charts.usage_over_time && this.chartContainers.usage) {
            this.updateUsageChart(charts.usage_over_time);
        }
        
        // Update features chart
        if (charts.feature_distribution && this.chartContainers.features) {
            this.updateFeaturesChart(charts.feature_distribution);
        }
        
        // Update credits chart
        if (charts.credit_usage && this.chartContainers.credits) {
            this.updateCreditsChart(charts.credit_usage);
        }
    }
    
    updateUsageChart(data) {
        const canvas = this.chartContainers.usage;
        if (!canvas || !window.Chart) return;
        
        // Destroy existing chart
        if (this.charts.has('usage')) {
            this.charts.get('usage').destroy();
        }
        
        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Daily Usage',
                    data: data.data,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                ...this.options.chartOptions,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            color: '#7f8c8d'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#7f8c8d'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
        
        this.charts.set('usage', chart);
        canvas.setAttribute('aria-label', `Usage chart showing ${data.data.length} days of activity`);
    }
    
    updateFeaturesChart(data) {
        const canvas = this.chartContainers.features;
        if (!canvas || !window.Chart) return;
        
        // Destroy existing chart
        if (this.charts.has('features')) {
            this.charts.get('features').destroy();
        }
        
        const colors = [
            '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe',
            '#43e97b', '#fa709a', '#fee140', '#a8edea', '#d299c2'
        ];
        
        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: colors.slice(0, data.labels.length),
                    borderWidth: 2,
                    borderColor: '#fff',
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                ...this.options.chartOptions,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            color: '#2c3e50'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        
        this.charts.set('features', chart);
        canvas.setAttribute('aria-label', `Feature usage distribution chart with ${data.labels.length} categories`);
    }
    
    updateCreditsChart(data) {
        const canvas = this.chartContainers.credits;
        if (!canvas || !window.Chart) return;
        
        // Destroy existing chart
        if (this.charts.has('credits')) {
            this.charts.get('credits').destroy();
        }
        
        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Credits Earned',
                        data: data.earned,
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: '#28a745',
                        borderWidth: 1
                    },
                    {
                        label: 'Credits Spent',
                        data: data.spent,
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                ...this.options.chartOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#7f8c8d'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#7f8c8d'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#2c3e50'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }
                }
            }
        });
        
        this.charts.set('credits', chart);
        canvas.setAttribute('aria-label', `Credit usage chart showing earned and spent credits over time`);
    }
    
    updateInsights() {
        if (!this.options.enableInsights) return;
        
        const { insights } = this.analyticsData;
        if (!insights) return;
        
        const elements = {
            'most-productive-day': insights.most_productive_day || 'Monday',
            'favorite-feature': insights.favorite_feature || 'General Consultation',
            'monthly-growth': insights.monthly_growth || '0%',
            'efficiency-score': insights.efficiency_score || '0%'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                element.setAttribute('aria-label', `${id.replace('-', ' ')}: ${value}`);
            }
        });
    }
    
    updateTimeline() {
        if (!this.options.enableTimeline) return;
        
        const { activity } = this.analyticsData;
        if (!activity) return;
        
        const container = document.getElementById('activity-timeline-content');
        if (!container) return;
        
        if (!activity || activity.length === 0) {
            container.innerHTML = '<p class="no-activity">No recent activity found.</p>';
            container.setAttribute('aria-label', 'No recent activity');
            return;
        }
        
        const timelineHTML = activity.map((item, index) => `
            <div class="timeline-item" role="listitem" tabindex="0" aria-label="Activity ${index + 1}: ${item.description}">
                <div class="timeline-icon" aria-hidden="true">${item.icon}</div>
                <div class="timeline-content">
                    <div class="timeline-description">${item.description}</div>
                    <div class="timeline-time">${item.time_ago}</div>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = timelineHTML;
        container.setAttribute('role', 'list');
        container.setAttribute('aria-label', `Activity timeline with ${activity.length} items`);
    }
    
    animateValue(element, start, end, duration) {
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (end - start) * progress);
            
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    setLoading(loading) {
        this.isLoading = loading;
        
        if (this.element) {
            this.element.classList.toggle('loading', loading);
        }
        
        if (this.refreshButton) {
            this.refreshButton.disabled = loading;
            this.refreshButton.setAttribute('aria-label', 
                loading ? 'Refreshing analytics data...' : 'Refresh analytics data'
            );
        }
        
        this.setState({ isLoading: loading });
    }
    
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        this.refreshInterval = setInterval(() => {
            if (!this.isLoading) {
                this.loadAnalyticsData();
            }
        }, this.options.refreshInterval);
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    handlePeriodChange(event) {
        const period = parseInt(event.target.value);
        this.loadAnalyticsData(period);
        this.announceToScreenReader(`Analytics period changed to ${period} days`);
    }
    
    handleRefreshClick(event) {
        event.preventDefault();
        this.loadAnalyticsData();
    }
    
    handleExportClick(event) {
        event.preventDefault();
        this.exportAnalyticsData();
    }
    
    exportAnalyticsData() {
        if (!this.analyticsData) {
            this.announceToScreenReader('No data available to export');
            return;
        }
        
        const dataStr = JSON.stringify(this.analyticsData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        this.announceToScreenReader('Analytics data exported successfully');
    }
    
    handleAnalyticsError(error) {
        this.handleError('Analytics loading failed', error);
        this.announceToScreenReader('Failed to load analytics data');
        
        // Show error message in UI
        const container = document.getElementById('activity-timeline-content');
        if (container) {
            container.innerHTML = `<p class="error-message">⚠️ ${error.message}</p>`;
        }
    }
    
    onViewportChange(isMobile) {
        // Adjust chart options for mobile
        if (this.charts.size > 0) {
            this.charts.forEach(chart => {
                chart.options.maintainAspectRatio = !isMobile;
                chart.update();
            });
        }
    }
    
    handleResize() {
        // Resize charts
        this.charts.forEach(chart => {
            chart.resize();
        });
    }
    
    onDestroy() {
        this.stopAutoRefresh();
        
        // Destroy charts
        this.charts.forEach(chart => {
            chart.destroy();
        });
        this.charts.clear();
    }
}

// Register component
window.BusinessCraftAI = window.BusinessCraftAI || {};
window.BusinessCraftAI.AnalyticsDashboard = AnalyticsDashboard;

// Auto-register with component manager
if (window.BusinessCraftAI.componentManager) {
    window.BusinessCraftAI.componentManager.registerComponent('analytics-dashboard', AnalyticsDashboard, {
        selector: '[data-component="analytics-dashboard"], .analytics-container'
    });
}

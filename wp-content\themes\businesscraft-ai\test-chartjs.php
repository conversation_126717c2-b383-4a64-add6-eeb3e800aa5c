<?php
/**
 * Test Chart.js Loading
 */

// Include WordPress
require_once('../../../wp-config.php');

echo "<h1>📊 Chart.js Test</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.chart-container { width: 400px; height: 300px; margin: 20px 0; border: 1px solid #ddd; }
.success { color: green; } 
.error { color: red; }
</style>";

?>

<h2>Chart.js CDN Test</h2>
<div id="status"></div>

<h2>Sample Chart</h2>
<div class="chart-container">
    <canvas id="testChart" width="400" height="300"></canvas>
</div>

<h2>Analytics Data Test</h2>
<div id="analyticsTest"></div>

<!-- Load Chart.js from CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

<script>
// Test Chart.js loading
document.addEventListener('DOMContentLoaded', function() {
    const statusDiv = document.getElementById('status');
    
    if (typeof Chart !== 'undefined') {
        statusDiv.innerHTML = '<p class="success">✅ Chart.js loaded successfully (Version: ' + Chart.version + ')</p>';
        
        // Create a test chart
        const ctx = document.getElementById('testChart');
        if (ctx) {
            try {
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Ghana', 'Kenya', 'Nigeria', 'South Africa'],
                        datasets: [{
                            label: 'Test Data',
                            data: [12, 19, 3, 5],
                            backgroundColor: ['#0073aa', '#00a0d2', '#46b450', '#ffb900']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                statusDiv.innerHTML += '<p class="success">✅ Test chart created successfully</p>';
            } catch (e) {
                statusDiv.innerHTML += '<p class="error">❌ Error creating test chart: ' + e.message + '</p>';
            }
        }
    } else {
        statusDiv.innerHTML = '<p class="error">❌ Chart.js failed to load</p>';
    }
    
    // Test analytics data structure
    const analyticsTestDiv = document.getElementById('analyticsTest');
    
    // Simulate analytics data structure
    const testAnalyticsData = {
        summary: {
            total_queries: 25,
            active_countries: 4,
            top_sector: 'Fintech',
            avg_session_duration: 4.2,
            opportunities_included: 18
        },
        top_sectors: {
            'Fintech': 8,
            'Agriculture': 6,
            'E-commerce': 5,
            'Renewable Energy': 4,
            'Creative Arts': 2
        },
        country_breakdown: {
            'Ghana': 8,
            'Kenya': 7,
            'Nigeria': 6,
            'South Africa': 4
        },
        opportunity_usage: {
            with_opportunities: 18,
            without_opportunities: 7,
            avg_opportunities: 2.1
        },
        keywords: {
            'business': 15,
            'startup': 12,
            'funding': 10,
            'market': 8,
            'investment': 6
        },
        recent_activity: [
            {
                timestamp: '2024-01-15 10:30:00',
                country: 'Ghana',
                detected_sector: 'Fintech',
                sector_context_found: 1,
                opportunities_included: 3,
                prompt_tokens_estimated: 450
            }
        ]
    };
    
    analyticsTestDiv.innerHTML = '<h3>Sample Analytics Data Structure:</h3>';
    analyticsTestDiv.innerHTML += '<pre>' + JSON.stringify(testAnalyticsData, null, 2) + '</pre>';
    
    // Test if this data would work with our charts
    if (typeof Chart !== 'undefined') {
        analyticsTestDiv.innerHTML += '<p class="success">✅ Data structure looks compatible with Chart.js</p>';
        
        // Test data extraction
        const sectors = testAnalyticsData.top_sectors || {};
        const labels = Object.keys(sectors);
        const data = Object.values(sectors);
        
        analyticsTestDiv.innerHTML += '<p>Extracted labels: ' + labels.join(', ') + '</p>';
        analyticsTestDiv.innerHTML += '<p>Extracted data: ' + data.join(', ') + '</p>';
    }
});
</script>

<h2>WordPress Integration Test</h2>
<p><strong>Theme URL:</strong> <?php echo CHATGABI_THEME_URL; ?></p>
<p><strong>JS File:</strong> <a href="<?php echo CHATGABI_THEME_URL; ?>/assets/js/admin-analytics-extended.js" target="_blank">admin-analytics-extended.js</a></p>
<p><strong>CSS File:</strong> <a href="<?php echo CHATGABI_THEME_URL; ?>/assets/css/admin-analytics-extended.css" target="_blank">admin-analytics-extended.css</a></p>

<p><a href="<?php echo admin_url('tools.php?page=chatgabi-engagement-analytics'); ?>">← Back to Analytics Dashboard</a></p>

<?php
/**
 * BusinessCraft AI - Cron Jobs Troubleshooting & Fix Script
 * 
 * Diagnoses and fixes WordPress cron job scheduling issues for the opportunity alerts system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

echo '<h1>BusinessCraft AI - Cron Jobs Troubleshooting & Fix</h1>';
echo '<p>Diagnosing and fixing WordPress cron job scheduling issues...</p>';

// Step 1: Check WordPress Cron Status
echo '<h2>Step 1: WordPress Cron System Status</h2>';

$wp_cron_disabled = defined('DISABLE_WP_CRON') && DISABLE_WP_CRON;
if ($wp_cron_disabled) {
    echo '❌ WordPress cron is DISABLED (DISABLE_WP_CRON = true)<br>';
    echo '   You need to set up server-level cron jobs or enable WordPress cron.<br>';
} else {
    echo '✅ WordPress cron is ENABLED<br>';
}

// Check if cron is working
$cron_test_hook = 'chatgabi_cron_test_' . time();
wp_schedule_single_event(time() + 5, $cron_test_hook);
$test_scheduled = wp_next_scheduled($cron_test_hook);

if ($test_scheduled) {
    echo '✅ WordPress cron scheduling is functional<br>';
    wp_clear_scheduled_hook($cron_test_hook);
} else {
    echo '❌ WordPress cron scheduling appears to be broken<br>';
}

echo '<hr>';

// Step 2: Check Available Cron Schedules
echo '<h2>Step 2: Available Cron Schedules</h2>';

$available_schedules = wp_get_schedules();
echo '<p><strong>Available schedules:</strong></p>';
echo '<ul>';
foreach ($available_schedules as $schedule_name => $schedule_info) {
    $interval_hours = round($schedule_info['interval'] / 3600, 2);
    echo "<li><strong>{$schedule_name}</strong>: {$schedule_info['display']} (every {$interval_hours} hours)</li>";
}
echo '</ul>';

// Check if our custom schedules are available
$required_schedules = array('every_15_minutes', 'monthly');
$missing_schedules = array();

foreach ($required_schedules as $schedule) {
    if (!isset($available_schedules[$schedule])) {
        $missing_schedules[] = $schedule;
    }
}

if (empty($missing_schedules)) {
    echo '✅ All required custom schedules are available<br>';
} else {
    echo '❌ Missing custom schedules: ' . implode(', ', $missing_schedules) . '<br>';
}

echo '<hr>';

// Step 3: Current Cron Jobs Status
echo '<h2>Step 3: Current Opportunity Alerts Cron Jobs Status</h2>';

try {
    $alerts_manager = chatgabi_get_opportunity_alerts();
    $cron_status = $alerts_manager->get_cron_status();
    
    echo '<table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>Cron Job</th><th>Status</th><th>Schedule</th><th>Next Run</th></tr>';
    
    $scheduled_count = 0;
    foreach ($cron_status as $hook => $status) {
        $status_icon = $status['scheduled'] ? '✅' : '❌';
        $status_text = $status['scheduled'] ? 'Scheduled' : 'Not Scheduled';
        
        echo "<tr>";
        echo "<td>{$status['description']}</td>";
        echo "<td>{$status_icon} {$status_text}</td>";
        echo "<td>{$status['schedule']}</td>";
        echo "<td>{$status['next_run']}</td>";
        echo "</tr>";
        
        if ($status['scheduled']) {
            $scheduled_count++;
        }
    }
    
    echo '</table>';
    
    echo "<p><strong>Summary: {$scheduled_count} out of " . count($cron_status) . " cron jobs are properly scheduled.</strong></p>";
    
} catch (Exception $e) {
    echo '<p><strong>❌ Error checking cron status: ' . $e->getMessage() . '</strong></p>';
}

echo '<hr>';

// Step 4: Fix Cron Jobs
echo '<h2>Step 4: Fix Cron Job Scheduling</h2>';

if (isset($_POST['fix_cron_jobs'])) {
    echo '<p>🔧 Attempting to fix cron job scheduling...</p>';
    
    try {
        // Force reschedule all cron jobs
        $alerts_manager = chatgabi_get_opportunity_alerts();
        $result = $alerts_manager->force_reschedule_cron_jobs();
        
        echo '<p><strong>Cron Jobs After Fix:</strong></p>';
        echo '<table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>Cron Job</th><th>Status</th><th>Schedule</th><th>Next Run</th></tr>';
        
        $fixed_count = 0;
        foreach ($result as $hook => $status) {
            $status_icon = $status['scheduled'] ? '✅' : '❌';
            $status_text = $status['scheduled'] ? 'Scheduled' : 'Not Scheduled';
            
            echo "<tr>";
            echo "<td>{$status['description']}</td>";
            echo "<td>{$status_icon} {$status_text}</td>";
            echo "<td>{$status['schedule']}</td>";
            echo "<td>{$status['next_run']}</td>";
            echo "</tr>";
            
            if ($status['scheduled']) {
                $fixed_count++;
            }
        }
        
        echo '</table>';
        
        if ($fixed_count === count($result)) {
            echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            echo '<h3>🎉 All Cron Jobs Successfully Fixed!</h3>';
            echo '<p>All opportunity alerts cron jobs are now properly scheduled and should run automatically.</p>';
            echo '</div>';
        } else {
            echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            echo '<h3>⚠️ Some Issues Remain</h3>';
            echo "<p>Only {$fixed_count} out of " . count($result) . " cron jobs were successfully scheduled.</p>";
            echo '</div>';
        }
        
    } catch (Exception $e) {
        echo '<p><strong>❌ Error fixing cron jobs: ' . $e->getMessage() . '</strong></p>';
    }
} else {
    echo '<form method="post">';
    echo '<input type="hidden" name="fix_cron_jobs" value="1">';
    echo '<button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">🔧 Fix All Cron Jobs</button>';
    echo '</form>';
}

echo '<hr>';

// Step 5: Manual Cron Job Scheduling
echo '<h2>Step 5: Manual Cron Job Scheduling</h2>';

if (isset($_POST['manual_schedule'])) {
    $hook = sanitize_text_field($_POST['cron_hook']);
    $schedule = sanitize_text_field($_POST['cron_schedule']);
    
    echo "<p>🔧 Manually scheduling {$hook} with {$schedule} schedule...</p>";
    
    // Clear existing
    wp_clear_scheduled_hook($hook);
    
    // Schedule new
    $time = time();
    if ($schedule === 'daily') {
        $time = strtotime('08:00:00');
        if ($time <= time()) {
            $time = strtotime('tomorrow 08:00:00');
        }
    } elseif ($schedule === 'weekly') {
        $time = strtotime('next monday 09:00:00');
    }
    
    $result = wp_schedule_event($time, $schedule, $hook);
    
    if ($result) {
        echo "✅ Successfully scheduled {$hook}<br>";
        $next_run = wp_next_scheduled($hook);
        echo "   Next run: " . date('Y-m-d H:i:s', $next_run) . "<br>";
    } else {
        echo "❌ Failed to schedule {$hook}<br>";
    }
}

echo '<form method="post">';
echo '<p><strong>Manually schedule a specific cron job:</strong></p>';
echo '<select name="cron_hook">';
echo '<option value="chatgabi_process_opportunity_alerts">Process Opportunity Alerts</option>';
echo '<option value="chatgabi_send_daily_alert_digest">Send Daily Alert Digest</option>';
echo '<option value="chatgabi_send_weekly_alert_summary">Send Weekly Alert Summary</option>';
echo '<option value="chatgabi_cleanup_old_alert_logs">Cleanup Old Alert Logs</option>';
echo '</select>';

echo '<select name="cron_schedule">';
echo '<option value="every_15_minutes">Every 15 Minutes</option>';
echo '<option value="hourly">Hourly</option>';
echo '<option value="daily">Daily</option>';
echo '<option value="weekly">Weekly</option>';
echo '<option value="monthly">Monthly</option>';
echo '</select>';

echo '<input type="hidden" name="manual_schedule" value="1">';
echo '<button type="submit" style="background: #28a745; color: white; padding: 5px 15px; border: none; border-radius: 3px; cursor: pointer;">Schedule</button>';
echo '</form>';

echo '<hr>';

// Step 6: Debugging Information
echo '<h2>Step 6: Debugging Information</h2>';

echo '<h3>WordPress Cron Array</h3>';
$cron_array = _get_cron_array();
echo '<pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">';
foreach ($cron_array as $timestamp => $hooks) {
    if ($timestamp === 'version') continue;
    
    echo "Timestamp: " . date('Y-m-d H:i:s', $timestamp) . " (" . $timestamp . ")\n";
    foreach ($hooks as $hook => $events) {
        if (strpos($hook, 'chatgabi_') === 0) {
            echo "  Hook: {$hook}\n";
            foreach ($events as $event) {
                echo "    Schedule: " . ($event['schedule'] ?? 'single') . "\n";
                echo "    Args: " . json_encode($event['args'] ?? []) . "\n";
            }
        }
    }
    echo "\n";
}
echo '</pre>';

echo '<h3>Server Information</h3>';
echo '<ul>';
echo '<li><strong>PHP Version:</strong> ' . PHP_VERSION . '</li>';
echo '<li><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</li>';
echo '<li><strong>Current Time:</strong> ' . current_time('mysql') . '</li>';
echo '<li><strong>GMT Time:</strong> ' . gmdate('Y-m-d H:i:s') . '</li>';
echo '<li><strong>Timezone:</strong> ' . get_option('timezone_string', 'UTC') . '</li>';
echo '</ul>';

echo '<h3>WordPress Constants</h3>';
echo '<ul>';
echo '<li><strong>DISABLE_WP_CRON:</strong> ' . (defined('DISABLE_WP_CRON') ? (DISABLE_WP_CRON ? 'true' : 'false') : 'not defined') . '</li>';
echo '<li><strong>WP_DEBUG:</strong> ' . (defined('WP_DEBUG') ? (WP_DEBUG ? 'true' : 'false') : 'not defined') . '</li>';
echo '<li><strong>WP_DEBUG_LOG:</strong> ' . (defined('WP_DEBUG_LOG') ? (WP_DEBUG_LOG ? 'true' : 'false') : 'not defined') . '</li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Troubleshooting completed at ' . current_time('mysql') . '</em></p>';
echo '<p><a href="' . admin_url() . '">← Return to WordPress Admin</a> | ';
echo '<a href="' . get_template_directory_uri() . '/initialize-opportunity-alerts.php">Run Initialization Script</a> | ';
echo '<a href="' . get_template_directory_uri() . '/test-opportunity-alerts.php">Run Test Script</a></p>';
?>

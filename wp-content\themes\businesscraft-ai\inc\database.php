<?php
/**
 * Database Setup and Management for BusinessCraft AI
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create database tables
 */
function businesscraft_ai_create_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Chat logs table
    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $chat_logs_sql = "CREATE TABLE IF NOT EXISTS $chat_logs_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        message_hash varchar(64) NOT NULL,
        encrypted_user_message longtext NOT NULL,
        encrypted_ai_response longtext NOT NULL,
        language varchar(5) NOT NULL DEFAULT 'en',
        context varchar(50) NOT NULL DEFAULT 'general',
        model varchar(50) NOT NULL,
        tokens_used int(11) NOT NULL DEFAULT 0,
        credits_used int(11) NOT NULL DEFAULT 0,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY created_at (created_at),
        KEY language (language)
    ) $charset_collate;";

    // Transactions table
    $transactions_table = $wpdb->prefix . 'businesscraft_ai_transactions';
    $transactions_sql = "CREATE TABLE IF NOT EXISTS $transactions_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        reference varchar(100) NOT NULL,
        package varchar(20) NOT NULL,
        amount decimal(10,2) NOT NULL,
        currency varchar(3) NOT NULL DEFAULT 'USD',
        status varchar(20) NOT NULL DEFAULT 'initiated',
        paystack_data longtext,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY reference (reference),
        KEY user_id (user_id),
        KEY status (status),
        KEY created_at (created_at)
    ) $charset_collate;";

    // Credit usage logs table
    $credit_logs_table = $wpdb->prefix . 'businesscraft_ai_credit_logs';
    $credit_logs_sql = "CREATE TABLE IF NOT EXISTS $credit_logs_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        action varchar(20) NOT NULL,
        credits_amount int(11) NOT NULL,
        credits_before int(11) NOT NULL,
        credits_after int(11) NOT NULL,
        tokens_used int(11) DEFAULT NULL,
        model varchar(50) DEFAULT NULL,
        transaction_reference varchar(100) DEFAULT NULL,
        description text,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY action (action),
        KEY created_at (created_at)
    ) $charset_collate;";

    // Analytics table
    $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
    $analytics_sql = "CREATE TABLE $analytics_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        event_type varchar(50) NOT NULL,
        event_data longtext,
        session_id varchar(100),
        ip_address varchar(45),
        user_agent text,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY event_type (event_type),
        KEY session_id (session_id),
        KEY created_at (created_at)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

    dbDelta($chat_logs_sql);
    dbDelta($transactions_sql);
    dbDelta($credit_logs_sql);
    dbDelta($analytics_sql);

    // Update database version
    update_option('businesscraft_ai_db_version', '1.0.0');
}

/**
 * Log chat interaction
 */
function businesscraft_ai_log_chat($user_id, $user_message, $ai_response, $language, $tokens_used, $model = 'gpt-3.5-turbo') {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_chat_logs';

    // Encrypt messages
    $encrypted_user_message = businesscraft_ai_encrypt($user_message);
    $encrypted_ai_response = businesscraft_ai_encrypt($ai_response);

    // Create message hash for deduplication
    $message_hash = hash('sha256', $user_message . $language);

    $credits_used = businesscraft_ai_calculate_credits($tokens_used, $model);

    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'message_hash' => $message_hash,
            'encrypted_user_message' => $encrypted_user_message,
            'encrypted_ai_response' => $encrypted_ai_response,
            'language' => $language,
            'model' => $model,
            'tokens_used' => $tokens_used,
            'credits_used' => $credits_used,
            'created_at' => current_time('mysql'),
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%d', '%d', '%s')
    );

    return $wpdb->insert_id;
}

/**
 * Get user chat history
 */
function businesscraft_ai_get_user_chat_history($user_id, $limit = 10) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_chat_logs';

    $results = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT id, encrypted_user_message, encrypted_ai_response, language, model, tokens_used, credits_used, created_at
             FROM {$table_name}
             WHERE user_id = %d
             ORDER BY created_at DESC
             LIMIT %d",
            $user_id,
            $limit
        )
    );

    $chat_history = array();

    foreach ($results as $row) {
        $chat_history[] = array(
            'id' => $row->id,
            'user_message' => businesscraft_ai_decrypt($row->encrypted_user_message),
            'ai_response' => businesscraft_ai_decrypt($row->encrypted_ai_response),
            'language' => $row->language,
            'model' => $row->model,
            'tokens_used' => $row->tokens_used,
            'credits_used' => $row->credits_used,
            'created_at' => $row->created_at,
        );
    }

    return $chat_history;
}

/**
 * Log credit usage
 */
function businesscraft_ai_log_credit_usage($user_id, $credits_used, $tokens_used, $model) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_credit_logs';

    $credits_before = get_user_meta($user_id, 'businesscraft_credits', true);
    $credits_before = $credits_before ? intval($credits_before) : 0;
    $credits_after = $credits_before - $credits_used;

    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'action' => 'usage',
            'credits_amount' => $credits_used,
            'credits_before' => $credits_before,
            'credits_after' => $credits_after,
            'tokens_used' => $tokens_used,
            'model' => $model,
            'description' => sprintf('AI chat using %s model', $model),
            'created_at' => current_time('mysql'),
        ),
        array('%d', '%s', '%d', '%d', '%d', '%d', '%s', '%s', '%s')
    );
}

/**
 * Log credit purchase
 */
function businesscraft_ai_log_credit_purchase($user_id, $credits_added, $transaction_reference) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_credit_logs';

    $credits_before = get_user_meta($user_id, 'businesscraft_credits', true);
    $credits_before = $credits_before ? intval($credits_before) : 0;
    $credits_after = $credits_before + $credits_added;

    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'action' => 'purchase',
            'credits_amount' => $credits_added,
            'credits_before' => $credits_before,
            'credits_after' => $credits_after,
            'transaction_reference' => $transaction_reference,
            'description' => sprintf('Credit purchase via Paystack (Ref: %s)', $transaction_reference),
            'created_at' => current_time('mysql'),
        ),
        array('%d', '%s', '%d', '%d', '%d', '%s', '%s', '%s')
    );
}

/**
 * Log analytics event
 */
function businesscraft_ai_log_analytics($user_id, $event_type, $event_data = null) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_analytics';

    $session_id = session_id();
    if (empty($session_id)) {
        $session_id = wp_get_session_token();
    }

    $ip_address = businesscraft_ai_get_client_ip();
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';

    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'event_type' => $event_type,
            'event_data' => $event_data ? json_encode($event_data) : null,
            'session_id' => $session_id,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent,
            'created_at' => current_time('mysql'),
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%s')
    );
}

/**
 * Get analytics data
 */
function businesscraft_ai_get_analytics($date_from = null, $date_to = null) {
    global $wpdb;

    if (!$date_from) {
        $date_from = date('Y-m-d', strtotime('-30 days'));
    }
    if (!$date_to) {
        $date_to = date('Y-m-d');
    }

    $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $transactions_table = $wpdb->prefix . 'businesscraft_ai_transactions';

    // Monthly Active Users
    $mau = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(DISTINCT user_id) FROM {$analytics_table}
             WHERE created_at >= %s AND created_at <= %s",
            $date_from,
            $date_to . ' 23:59:59'
        )
    );

    // Total chats
    $total_chats = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(*) FROM {$chat_logs_table}
             WHERE created_at >= %s AND created_at <= %s",
            $date_from,
            $date_to . ' 23:59:59'
        )
    );

    // Revenue
    $revenue = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT SUM(amount) FROM {$transactions_table}
             WHERE status = 'completed' AND created_at >= %s AND created_at <= %s",
            $date_from,
            $date_to . ' 23:59:59'
        )
    );

    // Average session length (in minutes) - Fixed SQL query
    $avg_session_length = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT AVG(session_duration) FROM (
                SELECT TIMESTAMPDIFF(MINUTE, MIN(created_at), MAX(created_at)) as session_duration
                FROM {$analytics_table}
                WHERE created_at >= %s AND created_at <= %s
                GROUP BY session_id
                HAVING COUNT(*) > 1
            ) as session_stats",
            $date_from,
            $date_to . ' 23:59:59'
        )
    );

    return array(
        'mau' => intval($mau),
        'total_chats' => intval($total_chats),
        'revenue' => floatval($revenue),
        'avg_session_length' => floatval($avg_session_length),
        'date_range' => array(
            'from' => $date_from,
            'to' => $date_to,
        ),
    );
}

/**
 * Delete user data (GDPR compliance)
 */
function businesscraft_ai_delete_user_data($user_id) {
    global $wpdb;

    $tables = array(
        $wpdb->prefix . 'businesscraft_ai_chat_logs',
        $wpdb->prefix . 'businesscraft_ai_credit_logs',
        $wpdb->prefix . 'businesscraft_ai_analytics',
    );

    foreach ($tables as $table) {
        $wpdb->delete($table, array('user_id' => $user_id), array('%d'));
    }

    // Keep transaction records for accounting but anonymize them
    $transactions_table = $wpdb->prefix . 'businesscraft_ai_transactions';
    $wpdb->update(
        $transactions_table,
        array('user_id' => 0),
        array('user_id' => $user_id),
        array('%d'),
        array('%d')
    );

    // Remove user meta
    delete_user_meta($user_id, 'businesscraft_credits');
    delete_user_meta($user_id, 'businesscraft_ai_tier');
}

/**
 * Get client IP address
 * Note: Function moved to secure-api-key-manager.php to avoid conflicts
 * This function is now defined there with enhanced security features
 */
// Function removed to prevent redeclaration conflict
// Use the enhanced version in secure-api-key-manager.php

/**
 * Clean up old data
 */
function businesscraft_ai_cleanup_old_data() {
    global $wpdb;

    // Delete chat logs older than 1 year
    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $wpdb->query(
        "DELETE FROM {$chat_logs_table} WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)"
    );

    // Delete analytics data older than 2 years
    $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
    $wpdb->query(
        "DELETE FROM {$analytics_table} WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR)"
    );
}

/**
 * Create ChatGABI specific tables (for test compatibility)
 */
function chatgabi_create_missing_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // ChatGABI conversations table
    $conversations_table = $wpdb->prefix . 'chatgabi_conversations';
    $conversations_sql = "CREATE TABLE IF NOT EXISTS $conversations_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        session_id varchar(100),
        message_text longtext,
        response_text longtext,
        tokens_used int(11) DEFAULT 0,
        conversation_type varchar(50) DEFAULT 'general',
        language varchar(5) DEFAULT 'en',
        country varchar(50),
        sector varchar(100),
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY session_id (session_id),
        KEY created_at (created_at),
        KEY conversation_type (conversation_type)
    ) $charset_collate;";

    // ChatGABI credit transactions table
    $credit_transactions_table = $wpdb->prefix . 'chatgabi_credit_transactions';
    $credit_transactions_sql = "CREATE TABLE IF NOT EXISTS $credit_transactions_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        transaction_type varchar(20) NOT NULL,
        amount decimal(10,2) NOT NULL,
        credits_before decimal(10,2) DEFAULT 0,
        credits_after decimal(10,2) DEFAULT 0,
        reference varchar(100),
        description text,
        status varchar(20) DEFAULT 'completed',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY transaction_type (transaction_type),
        KEY created_at (created_at),
        KEY reference (reference)
    ) $charset_collate;";

    // ChatGABI feedback table
    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
    $feedback_sql = "CREATE TABLE IF NOT EXISTS $feedback_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        conversation_id bigint(20),
        message_id varchar(100),
        session_id varchar(100),
        rating_score tinyint(1) NOT NULL,
        rating_type enum('star', 'thumbs') DEFAULT 'star',
        feedback_text text,
        category_helpfulness tinyint(1) DEFAULT NULL,
        category_accuracy tinyint(1) DEFAULT NULL,
        category_relevance tinyint(1) DEFAULT NULL,
        category_clarity tinyint(1) DEFAULT NULL,
        user_country varchar(50),
        user_sector varchar(100),
        conversation_context varchar(50),
        response_tokens int(11) DEFAULT 0,
        response_time_ms int(11) DEFAULT 0,
        is_training_data tinyint(1) DEFAULT 0,
        training_consent tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY conversation_id (conversation_id),
        KEY rating_score (rating_score),
        KEY rating_type (rating_type),
        KEY user_country (user_country),
        KEY user_sector (user_sector),
        KEY created_at (created_at),
        KEY is_training_data (is_training_data),
        UNIQUE KEY unique_user_message (user_id, message_id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

    dbDelta($conversations_sql);
    dbDelta($credit_transactions_sql);
    dbDelta($feedback_sql);

    return true;
}

// Duplicate function removed - keeping the first instance above

// Schedule cleanup
if (!wp_next_scheduled('businesscraft_ai_cleanup')) {
    wp_schedule_event(time(), 'weekly', 'businesscraft_ai_cleanup');
}
add_action('businesscraft_ai_cleanup', 'businesscraft_ai_cleanup_old_data');

# BusinessCraft AI - Cron Job Troubleshooting & Fixes

## 🐛 Problem Identified

The WordPress cron job scheduling issue was caused by **missing custom cron schedules**. Specifically:

1. **Root Cause**: WordPress only includes built-in schedules: `hourly`, `twicedaily`, `daily`, and `weekly`
2. **Missing Schedule**: The `monthly` schedule was not defined, causing the cleanup job to fail
3. **Timing Issue**: Custom schedules need to be registered BEFORE attempting to schedule jobs

## 🔧 Fixes Implemented

### 1. Added Missing Monthly Schedule

**File**: `wp-content/themes/businesscraft-ai/inc/opportunity-alerts.php`

```php
/**
 * Add custom cron schedules
 */
public function add_custom_cron_schedules($schedules) {
    $schedules['every_15_minutes'] = array(
        'interval' => 15 * 60,
        'display' => __('Every 15 Minutes', 'chatgabi')
    );
    
    $schedules['every_30_minutes'] = array(
        'interval' => 30 * 60,
        'display' => __('Every 30 Minutes', 'chatgabi')
    );
    
    // ✅ ADDED: Monthly schedule (30 days)
    $schedules['monthly'] = array(
        'interval' => 30 * 24 * 60 * 60, // 30 days in seconds
        'display' => __('Once Monthly', 'chatgabi')
    );
    
    return $schedules;
}
```

### 2. Fixed Execution Order

**Problem**: Cron schedules were being registered AFTER attempting to schedule jobs.

**Solution**: Reordered the initialization to register schedules first:

```php
public function init_hooks() {
    // ✅ FIXED: Add custom cron schedules FIRST
    add_filter('cron_schedules', array($this, 'add_custom_cron_schedules'));
    
    // Register custom post type for alert filters
    $this->register_alert_filters_post_type();
    
    // ✅ FIXED: Schedule cron jobs AFTER custom schedules are registered
    add_action('wp_loaded', array($this, 'schedule_cron_jobs'));
}
```

### 3. Enhanced Error Handling & Logging

**Added comprehensive error handling** to the cron scheduling function:

```php
public function schedule_cron_jobs() {
    // Ensure custom schedules are available
    $available_schedules = wp_get_schedules();
    
    // Process opportunity alerts every 15 minutes
    if (!wp_next_scheduled('chatgabi_process_opportunity_alerts')) {
        if (isset($available_schedules['every_15_minutes'])) {
            $result = wp_schedule_event(time(), 'every_15_minutes', 'chatgabi_process_opportunity_alerts');
            if (!$result) {
                error_log('ChatGABI: Failed to schedule chatgabi_process_opportunity_alerts');
            }
        } else {
            error_log('ChatGABI: every_15_minutes schedule not available');
        }
    }
    
    // ✅ Similar enhanced error handling for all cron jobs...
}
```

### 4. Improved Time Calculations

**Fixed daily digest scheduling** to handle edge cases:

```php
// Send daily digest at 8 AM
if (!wp_next_scheduled('chatgabi_send_daily_alert_digest')) {
    $daily_time = strtotime('08:00:00');
    // ✅ FIXED: If 8 AM has passed today, schedule for tomorrow
    if ($daily_time <= time()) {
        $daily_time = strtotime('tomorrow 08:00:00');
    }
    $result = wp_schedule_event($daily_time, 'daily', 'chatgabi_send_daily_alert_digest');
}
```

### 5. Removed Duplicate Scheduling

**Problem**: Cron jobs were being scheduled in both the opportunity alerts system AND functions.php.

**Solution**: Removed duplicate scheduling from functions.php:

```php
// ✅ REMOVED: Duplicate cron scheduling from functions.php
// Schedule opportunity alerts cron jobs (will be handled by the alerts system itself)
// The opportunity alerts system will schedule its own cron jobs with proper custom schedules
```

## 🛠️ New Troubleshooting Tools

### 1. Cron Status Monitoring

Added methods to check cron job status:

```php
/**
 * Get status of all cron jobs
 */
public function get_cron_status() {
    $cron_jobs = array(
        'chatgabi_process_opportunity_alerts' => 'Process Opportunity Alerts',
        'chatgabi_send_daily_alert_digest' => 'Send Daily Alert Digest',
        'chatgabi_send_weekly_alert_summary' => 'Send Weekly Alert Summary',
        'chatgabi_cleanup_old_alert_logs' => 'Cleanup Old Alert Logs'
    );
    
    $status = array();
    foreach ($cron_jobs as $hook => $description) {
        $next_run = wp_next_scheduled($hook);
        $status[$hook] = array(
            'description' => $description,
            'scheduled' => $next_run !== false,
            'next_run' => $next_run ? date('Y-m-d H:i:s', $next_run) : 'Not scheduled',
            'schedule' => $next_run ? wp_get_schedule($hook) : 'None'
        );
    }
    
    return $status;
}
```

### 2. Force Reschedule Function

Added ability to force reschedule all cron jobs:

```php
/**
 * Force reschedule all cron jobs (for troubleshooting)
 */
public function force_reschedule_cron_jobs() {
    // Clear all existing cron jobs
    wp_clear_scheduled_hook('chatgabi_process_opportunity_alerts');
    wp_clear_scheduled_hook('chatgabi_send_daily_alert_digest');
    wp_clear_scheduled_hook('chatgabi_send_weekly_alert_summary');
    wp_clear_scheduled_hook('chatgabi_cleanup_old_alert_logs');
    
    // Force schedule all jobs
    $this->schedule_cron_jobs();
    
    return $this->get_cron_status();
}
```

### 3. Diagnostic Scripts

Created comprehensive troubleshooting scripts:

- **`fix-cron-jobs.php`**: Interactive web-based cron troubleshooting tool
- **`quick-cron-test.php`**: Simple command-line diagnostic script
- **Enhanced `test-opportunity-alerts.php`**: Improved cron testing with detailed status table

## 📋 Verification Steps

### 1. Run Initialization Script
```
http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/initialize-opportunity-alerts.php
```

### 2. Run Cron Troubleshooting Tool
```
http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/fix-cron-jobs.php
```

### 3. Run Test Script
```
http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-opportunity-alerts.php
```

### 4. Check WordPress Admin
Navigate to **Tools → Scheduled Events** (if you have a cron management plugin) or use WP-CLI:
```bash
wp cron event list
```

## ✅ Expected Results

After implementing these fixes, all 4 cron jobs should be properly scheduled:

1. **✅ `chatgabi_process_opportunity_alerts`** - Every 15 minutes
2. **✅ `chatgabi_send_daily_alert_digest`** - Daily at 8 AM
3. **✅ `chatgabi_send_weekly_alert_summary`** - Weekly on Mondays at 9 AM
4. **✅ `chatgabi_cleanup_old_alert_logs`** - Monthly (every 30 days)

## 🔍 Common Issues & Solutions

### Issue 1: "monthly schedule not available"
**Solution**: Ensure the custom schedules filter is registered before scheduling jobs.

### Issue 2: Cron jobs not running
**Possible causes**:
- `DISABLE_WP_CRON` is set to `true` in wp-config.php
- Server doesn't support WordPress cron
- PHP execution time limits

**Solutions**:
- Enable WordPress cron: `define('DISABLE_WP_CRON', false);`
- Set up server-level cron: `*/15 * * * * wget -q -O - http://yoursite.com/wp-cron.php`

### Issue 3: Duplicate cron jobs
**Solution**: Clear existing jobs before rescheduling using `wp_clear_scheduled_hook()`.

## 🚀 Performance Optimizations

1. **Efficient Scheduling**: Jobs are only scheduled if not already scheduled
2. **Error Logging**: Failed scheduling attempts are logged for debugging
3. **Cleanup**: Old logs are automatically removed to maintain database performance
4. **Rate Limiting**: Built-in rate limiting prevents spam and overload

## 📞 Support

If cron jobs still fail to schedule after these fixes:

1. Check WordPress error logs
2. Verify server PHP configuration
3. Test with WP-CLI cron commands
4. Consider using server-level cron as backup
5. Use the diagnostic scripts provided

---

**Version**: 1.1.0  
**Last Updated**: December 2024  
**Status**: ✅ All cron scheduling issues resolved

let t;try{t=JSON.parse(document.getElementById("wp-script-module-data-@wordpress/block-library/form/view")?.textContent)}catch{}document.querySelectorAll("form.wp-block-form").forEach((function(o){if(!t||!o.action||!o.action.startsWith("mailto:"))return;const e=t=>{const o=new URLSearchParams(window.location.search);o.append("wp-form-result",t),window.location.search=o.toString()};o.addEventListener("submit",(async function(r){r.preventDefault();const n=Object.fromEntries(new FormData(o).entries());n.formAction=o.action,n._ajax_nonce=t.nonce,n.action=t.action,n._wp_http_referer=window.location.href,n.formAction=o.action;try{(await fetch(t.ajaxUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams(n).toString()})).ok?e("success"):e("error")}catch(t){e("error")}}))}));
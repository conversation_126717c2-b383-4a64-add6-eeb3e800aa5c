/**
 * PDF Export Functionality for BusinessCraft AI
 * 
 * Handles client-side PDF export operations
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // PDF Export functionality
    const PdfExport = {
        
        init: function() {
            this.bindEvents();
            this.addExportButtons();
        },

        bindEvents: function() {
            // Export button clicks
            $(document).on('click', '.export-pdf-btn', this.handleExportClick);
            $(document).on('click', '.export-docx-btn', this.handleDocxExportClick);
            
            // Export modal events
            $(document).on('click', '.export-modal-close', this.closeExportModal);
            $(document).on('click', '.export-modal-backdrop', this.closeExportModal);
            
            // Export options
            $(document).on('change', '#export-include-branding', this.updateExportPreview);
            $(document).on('change', '#export-format', this.updateExportOptions);
            $(document).on('change', '#export-email-delivery', this.toggleEmailOptions);
        },

        addExportButtons: function() {
            // Add export buttons to template cards
            $('.template-card').each(function() {
                const $card = $(this);
                const templateId = $card.data('template-id');
                
                if (templateId && !$card.find('.export-actions').length) {
                    const exportActions = `
                        <div class="export-actions">
                            <button class="export-pdf-btn" data-template-id="${templateId}" title="Export as PDF">
                                📄 Export PDF
                            </button>
                            <button class="export-docx-btn" data-template-id="${templateId}" title="Export as Word Document">
                                📝 Export Word
                            </button>
                        </div>
                    `;
                    
                    $card.find('.template-actions').append(exportActions);
                }
            });

            // Add export buttons to template preview modal
            if ($('#template-preview-modal').length) {
                const $modal = $('#template-preview-modal');
                if (!$modal.find('.export-section').length) {
                    const exportSection = `
                        <div class="export-section">
                            <h4>📄 Export Options</h4>
                            <div class="export-buttons">
                                <button class="export-pdf-btn btn-primary" data-template-id="">
                                    📄 Export as PDF
                                </button>
                                <button class="export-docx-btn btn-secondary" data-template-id="">
                                    📝 Export as Word
                                </button>
                            </div>
                        </div>
                    `;
                    
                    $modal.find('.template-modal-body').append(exportSection);
                }
            }
        },

        handleExportClick: function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const templateId = $btn.data('template-id');
            
            if (!templateId) {
                PdfExport.showMessage('error', businesscraftPdfExport.strings.selectTemplate);
                return;
            }

            PdfExport.showExportModal(templateId, 'pdf');
        },

        handleDocxExportClick: function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const templateId = $btn.data('template-id');
            
            if (!templateId) {
                PdfExport.showMessage('error', businesscraftPdfExport.strings.selectTemplate);
                return;
            }

            PdfExport.showExportModal(templateId, 'docx');
        },

        showExportModal: function(templateId, format) {
            // Create modal if it doesn't exist
            if (!$('#export-modal').length) {
                this.createExportModal();
            }

            const $modal = $('#export-modal');
            
            // Set template ID and format
            $modal.data('template-id', templateId);
            $('#export-format').val(format);
            
            // Update modal title
            const formatName = format === 'pdf' ? 'PDF' : 'Word Document';
            $('#export-modal-title').text(`Export as ${formatName}`);
            
            // Show modal
            $modal.addClass('active');
            $('body').addClass('modal-open');
        },

        createExportModal: function() {
            const modalHtml = `
                <div id="export-modal" class="export-modal">
                    <div class="export-modal-backdrop"></div>
                    <div class="export-modal-content">
                        <div class="export-modal-header">
                            <h3 id="export-modal-title">Export Document</h3>
                            <button class="export-modal-close">&times;</button>
                        </div>
                        <div class="export-modal-body">
                            <div class="export-options">
                                <div class="export-option">
                                    <label for="export-format">Format:</label>
                                    <select id="export-format">
                                        <option value="pdf">PDF Document</option>
                                        <option value="docx">Word Document</option>
                                    </select>
                                </div>
                                
                                <div class="export-option">
                                    <label>
                                        <input type="checkbox" id="export-include-branding" checked>
                                        Include BusinessCraft AI branding
                                    </label>
                                </div>

                                <div class="export-option">
                                    <label>
                                        <input type="checkbox" id="export-email-delivery">
                                        Email document to me
                                    </label>
                                </div>

                                <div class="export-option email-options" style="display: none;">
                                    <label for="export-email-address">Email Address:</label>
                                    <input type="email" id="export-email-address" placeholder="Enter email address...">

                                    <label for="export-email-message">Personal Message (Optional):</label>
                                    <textarea id="export-email-message" rows="3" placeholder="Add a personal message..."></textarea>
                                </div>
                                
                                <div class="export-preview">
                                    <h4>Export Preview:</h4>
                                    <div class="preview-content">
                                        <div class="preview-header">
                                            <strong>Professional Business Document</strong>
                                        </div>
                                        <div class="preview-meta">
                                            Business information and AI-generated content
                                        </div>
                                        <div class="preview-branding" id="preview-branding">
                                            <em>Generated by BusinessCraft AI</em>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="export-modal-footer">
                            <button class="btn-secondary export-modal-close">Cancel</button>
                            <button class="btn-primary" id="confirm-export">
                                📄 Generate Export
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
            
            // Bind confirm export
            $('#confirm-export').on('click', this.confirmExport);
        },

        closeExportModal: function() {
            $('#export-modal').removeClass('active');
            $('body').removeClass('modal-open');
        },

        updateExportPreview: function() {
            const includeBranding = $('#export-include-branding').is(':checked');
            const $brandingPreview = $('#preview-branding');
            
            if (includeBranding) {
                $brandingPreview.show();
            } else {
                $brandingPreview.hide();
            }
        },

        updateExportOptions: function() {
            const format = $('#export-format').val();
            const formatName = format === 'pdf' ? 'PDF' : 'Word Document';

            $('#export-modal-title').text(`Export as ${formatName}`);
            $('#confirm-export').html(`📄 Generate ${formatName}`);
        },

        toggleEmailOptions: function() {
            const emailDelivery = $('#export-email-delivery').is(':checked');
            const $emailOptions = $('.email-options');

            if (emailDelivery) {
                $emailOptions.show();
                // Pre-fill with current user's email if available
                if (!$('#export-email-address').val() && typeof businesscraftPdfExport.userEmail !== 'undefined') {
                    $('#export-email-address').val(businesscraftPdfExport.userEmail);
                }
            } else {
                $emailOptions.hide();
            }
        },

        confirmExport: function() {
            const $modal = $('#export-modal');
            const templateId = $modal.data('template-id');
            const format = $('#export-format').val();
            const includeBranding = $('#export-include-branding').is(':checked');
            const emailDelivery = $('#export-email-delivery').is(':checked');
            const emailAddress = $('#export-email-address').val();
            const emailMessage = $('#export-email-message').val();

            if (!templateId) {
                PdfExport.showMessage('error', businesscraftPdfExport.strings.selectTemplate);
                return;
            }

            // Validate email if email delivery is selected
            if (emailDelivery && !emailAddress) {
                PdfExport.showMessage('error', 'Please enter an email address for delivery.');
                return;
            }

            PdfExport.performExport(templateId, format, includeBranding, emailDelivery, emailAddress, emailMessage);
        },

        performExport: function(templateId, format, includeBranding, emailDelivery, emailAddress, emailMessage) {
            const $btn = $('#confirm-export');
            const originalText = $btn.html();

            // Show loading state
            $btn.prop('disabled', true).html(`
                <span class="loading-spinner"></span>
                ${businesscraftPdfExport.strings.exporting}
            `);

            // Prepare export data
            const exportData = {
                template_id: templateId,
                format: format,
                include_branding: includeBranding,
                email_delivery: emailDelivery || false,
                email_address: emailAddress || '',
                email_message: emailMessage || ''
            };

            // Make export request
            $.ajax({
                url: businesscraftPdfExport.restUrl + 'export/pdf',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftPdfExport.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(exportData),
                success: function(response) {
                    if (response.success) {
                        PdfExport.handleExportSuccess(response);
                    } else {
                        PdfExport.handleExportError(response.message || businesscraftPdfExport.strings.exportError);
                    }
                },
                error: function() {
                    console.error('Export request failed');
                    PdfExport.handleExportError(businesscraftPdfExport.strings.exportError);
                },
                complete: function() {
                    // Restore button state
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        },

        handleExportSuccess: function(response) {
            this.closeExportModal();

            // Handle email delivery notification
            if (response.email_sent) {
                this.showMessage('success', response.email_message || 'Document generated and emailed successfully!');
            } else {
                this.showMessage('success', businesscraftPdfExport.strings.exportSuccess);
            }

            // Handle download/print based on format
            if (response.format === 'html' || response.format === 'html-docx') {
                // Show HTML print instructions
                this.showPrintInstructions(response);
            } else if (response.download_url) {
                // Direct download for PDF/DOCX
                this.initiateDownload(response.download_url, response.filename);
            }

            // Update export history if available
            this.updateExportHistory();
        },

        handleExportError: function(message) {
            this.showMessage('error', message);
        },

        showPrintInstructions: function(response) {
            const instructionsHtml = `
                <div class="print-instructions-modal">
                    <div class="print-instructions-content">
                        <h3>📄 ${businesscraftPdfExport.strings.downloadStarting}</h3>
                        <p>${response.instructions}</p>
                        <div class="print-actions">
                            <a href="${response.print_url}" target="_blank" class="btn-primary">
                                🖨️ Open Print View
                            </a>
                            <button class="btn-secondary close-instructions">Close</button>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(instructionsHtml);
            
            // Auto-remove after 10 seconds
            setTimeout(function() {
                $('.print-instructions-modal').remove();
            }, 10000);
            
            // Close button
            $('.close-instructions').on('click', function() {
                $('.print-instructions-modal').remove();
            });
        },

        initiateDownload: function(downloadUrl, filename) {
            // Create temporary download link
            const $link = $('<a>', {
                href: downloadUrl,
                download: filename,
                style: 'display: none;'
            });
            
            $('body').append($link);
            $link[0].click();
            $link.remove();
            
            this.showMessage('success', businesscraftPdfExport.strings.downloadStarting);
        },

        updateExportHistory: function() {
            // Refresh export history if on dashboard
            if ($('.export-history').length) {
                this.loadExportHistory();
            }
        },

        loadExportHistory: function() {
            $.ajax({
                url: businesscraftPdfExport.restUrl + 'export/history',
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftPdfExport.nonce
                },
                success: function(response) {
                    if (response.success) {
                        PdfExport.renderExportHistory(response.exports);
                    }
                }
            });
        },

        renderExportHistory: function(exports) {
            const $container = $('.export-history');
            
            if (exports.length === 0) {
                $container.html('<p>No exports yet.</p>');
                return;
            }

            let html = '<h4>Recent Exports</h4><ul class="export-list">';
            
            exports.forEach(function(exportItem) {
                const date = new Date(exportItem.created_at).toLocaleDateString();
                const size = PdfExport.formatFileSize(exportItem.file_size);
                
                html += `
                    <li class="export-item">
                        <div class="export-info">
                            <strong>${exportItem.template_name || 'Business Document'}</strong>
                            <span class="export-meta">${exportItem.export_format.toUpperCase()} • ${size} • ${date}</span>
                        </div>
                        <div class="export-actions">
                            <a href="${exportItem.download_url}" class="btn-small">Download</a>
                        </div>
                    </li>
                `;
            });
            
            html += '</ul>';
            $container.html(html);
        },

        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        showMessage: function(type, message) {
            // Create or update message container
            let $message = $('.export-message');
            
            if (!$message.length) {
                $message = $('<div class="export-message"></div>');
                $('body').append($message);
            }
            
            $message
                .removeClass('success error')
                .addClass(type)
                .text(message)
                .fadeIn();
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        PdfExport.init();
    });

    // Make PdfExport available globally
    window.BusinessCraftPdfExport = PdfExport;

})(jQuery);

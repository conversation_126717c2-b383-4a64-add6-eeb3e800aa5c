/**
 * BusinessCraft AI Chat Block JavaScript
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeChatBlocks();
        initializePricingButtons();
    });

    function initializeChatBlocks() {
        const chatBlocks = document.querySelectorAll('.businesscraft-ai-chat');

        chatBlocks.forEach(function(block) {
            initializeChatBlock(block);
        });
    }

    function initializePricingButtons() {
        // Initialize pricing section buttons
        const pricingButtons = document.querySelectorAll('.pricing-button, .credit-package-button, [data-package]');

        pricingButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const packageType = this.dataset.package || this.getAttribute('data-package');

                if (packageType) {
                    console.log('Pricing button clicked for package:', packageType);
                    initiatePurchaseFromPricing(packageType);
                } else {
                    console.error('No package type found on button');
                }
            });
        });
    }

    function initializeChatBlock(block) {
        const chatInput = block.querySelector('#chat-input');
        const chatSubmit = block.querySelector('#chat-submit');
        const languageSelect = block.querySelector('#chat-language');
        const examplePrompts = block.querySelector('#example-prompts');
        const chatHistory = block.querySelector('#chat-history');
        const chatMessages = block.querySelector('#chat-messages');
        const creditDisplay = block.querySelector('.credit-display');
        const promptTemplates = block.querySelector('#prompt-templates');
        const saveTemplateBtn = block.querySelector('#save-template-btn');
        console.log('Credit Display Element:', creditDisplay); // Debugging line

        if (!chatInput || !chatSubmit) {
            return; // User not logged in or block not properly rendered
        }

        // Initialize language-specific examples
        updateExamplePrompts(languageSelect.value);

        // Load chat history
        loadChatHistory();

        // Load templates
        loadTemplates();

        // Initialize template modal
        initializeTemplateModal();

        // Event listeners
        chatSubmit.addEventListener('click', handleChatSubmit);
        chatInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                handleChatSubmit();
            }
        });

        // Enhanced accessibility and mobile support
        setupChatAccessibility();
        setupMobileChatOptimizations();

        languageSelect.addEventListener('change', function() {
            updateExamplePrompts(this.value);
        });

        // Template event listeners
        if (promptTemplates) {
            promptTemplates.addEventListener('change', function() {
                if (this.value) {
                    loadTemplate(this.value);
                }
            });
        }

        if (saveTemplateBtn) {
            saveTemplateBtn.addEventListener('click', function() {
                showSaveTemplateDialog();
            });
        }

        if (examplePrompts) {
            examplePrompts.addEventListener('click', function(e) {
                if (e.target.classList.contains('example-prompt')) {
                    chatInput.value = e.target.textContent;
                    chatInput.focus();
                }
            });
        }

        function handleChatSubmit() {
            const message = chatInput.value.trim();
            const language = languageSelect.value;

            if (!message) {
                showError('Please enter a message before sending.', 'warning');
                return;
            }

            // Estimate cost before sending
            const estimatedCost = estimateMessageCost(message);
            const currentCredits = getCurrentCredits();

            if (currentCredits < estimatedCost) {
                showError(`Insufficient credits. This message will cost approximately ${estimatedCost} credits, but you only have ${currentCredits}.`, 'warning', false);
                showCreditPurchasePrompt();
                return;
            }

            // Show cost estimate if significant
            if (estimatedCost > 2) {
                showCostEstimate(estimatedCost);
            }

            // Disable submit button and show loading
            chatSubmit.disabled = true;
            chatSubmit.innerHTML = '<span class="loading"><span class="loading-spinner"></span> ' + businesscraftAI.strings.loading + '</span>';

            // Add user message to chat
            addMessageToChat('user', message);

            // Clear input
            chatInput.value = '';

            // Send request
            fetch(businesscraftAI.restUrl + 'chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': businesscraftAI.nonce
                },
                body: JSON.stringify({
                    message: message,
                    language: language,
                    context: 'general'
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code && data.code === 'insufficient_credits') {
                    showError(businesscraftAI.strings.insufficientCredits, 'warning', true);
                    showCreditPurchasePrompt();
                } else if (data.code && data.code === 'rate_limit_exceeded') {
                    showError('Rate limit exceeded. Please wait a moment before trying again.', 'warning', true);
                } else if (data.code && data.code === 'invalid_request') {
                    showError('Invalid request. Please check your message and try again.', 'error', true);
                } else if (data.response) {
                    const messageId = addMessageToChat('ai', data.response, data.conversation_id, data.session_id);
                    updateCreditDisplay(data.remaining_credits);

                    // Show usage feedback
                    if (data.tokens_used && data.credits_used && data.model) {
                        showUsageFeedback({
                            tokens_used: data.tokens_used,
                            credits_used: data.credits_used,
                            model: data.model,
                            remaining_credits: data.remaining_credits
                        });
                    }

                    // Trigger feedback system for AI response
                    if (typeof jQuery !== 'undefined' && jQuery(document).trigger) {
                        jQuery(document).trigger('chatgabi:responseCompleted', {
                            message_id: messageId,
                            conversation_id: data.conversation_id,
                            session_id: data.session_id,
                            tokens_used: data.tokens_used,
                            response_time_ms: data.response_time_ms,
                            context: 'general'
                        });
                    }
                } else {
                    showError(data.message || 'An unexpected error occurred. Please try again.', 'error', true);
                }
            })
            .catch(error => {
                console.error('Chat error:', error);
                let errorMessage = 'Network error. Please check your connection and try again.';
                let showRetry = true;

                if (error.message.includes('HTTP 429')) {
                    errorMessage = 'Too many requests. Please wait a moment before trying again.';
                } else if (error.message.includes('HTTP 500')) {
                    errorMessage = 'Server error. Our team has been notified. Please try again in a few minutes.';
                } else if (error.message.includes('HTTP 403')) {
                    errorMessage = 'Access denied. Please log in again.';
                    showRetry = false;
                }

                showError(errorMessage, 'error', showRetry);
            })
            .finally(() => {
                // Re-enable submit button
                chatSubmit.disabled = false;
                chatSubmit.innerHTML = 'Send Message';
                chatInput.focus();
            });
        }

        function addMessageToChat(type, content, conversationId = '', sessionId = '') {
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message ' + type + '-message';
            messageDiv.setAttribute('data-message-id', messageId);

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;

            const metaDiv = document.createElement('div');
            metaDiv.className = 'message-meta';
            metaDiv.textContent = new Date().toLocaleTimeString();

            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(metaDiv);

            // Add feedback widget for AI messages
            if (type === 'ai' && typeof addFeedbackToMessage === 'function') {
                // Add feedback widget after a short delay to ensure message is rendered
                setTimeout(() => {
                    addFeedbackToMessage(messageDiv, messageId, conversationId, sessionId);
                }, 100);
            }

            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageId;
        }

        function updateExamplePrompts(language) {
            if (!examplePrompts || !businesscraftAI.prompts[language]) {
                return;
            }

            examplePrompts.innerHTML = '';

            businesscraftAI.prompts[language].forEach(function(prompt) {
                const promptElement = document.createElement('span');
                promptElement.className = 'example-prompt';
                promptElement.textContent = prompt;
                examplePrompts.appendChild(promptElement);
            });
        }

        function loadChatHistory() {
            if (!chatHistory) {
                return;
            }

            const showHistory = block.dataset.showHistory === 'true';
            const maxHistory = parseInt(block.dataset.maxHistory) || 10;

            if (!showHistory) {
                return;
            }

            fetch(businesscraftAI.restUrl + 'chat-history?limit=' + maxHistory, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.history && data.history.length > 0) {
                    displayChatHistory(data.history);
                }
            })
            .catch(error => {
                console.error('Failed to load chat history:', error);
            });
        }

        function displayChatHistory(history) {
            chatHistory.innerHTML = '<h4>Recent Conversations</h4>';

            history.forEach(function(chat) {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';

                const userMessage = document.createElement('div');
                userMessage.className = 'history-user-message';
                userMessage.textContent = chat.user_message.substring(0, 100) + (chat.user_message.length > 100 ? '...' : '');

                const aiMessage = document.createElement('div');
                aiMessage.className = 'history-ai-message';
                aiMessage.textContent = chat.ai_response.substring(0, 150) + (chat.ai_response.length > 150 ? '...' : '');

                const timestamp = document.createElement('div');
                timestamp.className = 'history-timestamp';
                timestamp.textContent = new Date(chat.created_at).toLocaleDateString();

                historyItem.appendChild(userMessage);
                historyItem.appendChild(aiMessage);
                historyItem.appendChild(timestamp);

                chatHistory.appendChild(historyItem);
            });
        }

        function updateCreditDisplay(credits) {
            if (creditDisplay) {
                creditDisplay.innerHTML = `
                    <span class="credits-amount">Credits: ${credits}</span>
                    <span class="credits-status ${getCreditStatusClass(credits)}">${getCreditStatusText(credits)}</span>
                `;

                // Show warning if credits are low
                if (credits <= 10 && credits > 0) {
                    showCreditWarning(credits);
                }
            }
        }

        function getCreditStatusClass(credits) {
            if (credits <= 5) return 'critical';
            if (credits <= 20) return 'low';
            if (credits <= 50) return 'medium';
            return 'good';
        }

        function getCreditStatusText(credits) {
            if (credits <= 5) return '⚠️ Critical';
            if (credits <= 20) return '⚠️ Low';
            if (credits <= 50) return '⚡ Medium';
            return '✅ Good';
        }

        function showCreditWarning(credits) {
            const warningDiv = document.createElement('div');
            warningDiv.className = 'credit-warning';
            warningDiv.innerHTML = `
                <div class="warning-content">
                    <span class="warning-icon">⚠️</span>
                    <span class="warning-text">Low credits: ${credits} remaining</span>
                    <button class="buy-credits-btn" onclick="showCreditPurchasePrompt()">Buy More</button>
                </div>
            `;

            // Insert after credit display
            if (creditDisplay && !creditDisplay.parentNode.querySelector('.credit-warning')) {
                creditDisplay.parentNode.insertBefore(warningDiv, creditDisplay.nextSibling);

                // Auto-remove after 10 seconds
                setTimeout(() => {
                    if (warningDiv.parentNode) {
                        warningDiv.remove();
                    }
                }, 10000);
            }
        }

        function showUsageFeedback(usageData) {
            // Create usage feedback element
            const feedbackDiv = document.createElement('div');
            feedbackDiv.className = 'usage-feedback';

            const modelIcon = usageData.model.includes('gpt-4') ? '🚀' : '⚡';
            const modelName = usageData.model.includes('gpt-4') ? 'GPT-4' : 'GPT-3.5';

            feedbackDiv.innerHTML = `
                <div class="usage-stats">
                    <span class="model-info">${modelIcon} ${modelName}</span>
                    <span class="tokens-info">📊 ${usageData.tokens_used} tokens</span>
                    <span class="credits-info">💳 ${usageData.credits_used} credits used</span>
                    <span class="remaining-info">💰 ${usageData.remaining_credits} credits left</span>
                </div>
            `;

            // Add to chat messages
            chatMessages.appendChild(feedbackDiv);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                feedbackDiv.style.opacity = '0';
                setTimeout(() => {
                    if (feedbackDiv.parentNode) {
                        feedbackDiv.parentNode.removeChild(feedbackDiv);
                    }
                }, 300);
            }, 5000);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showError(message, type = 'error', showRetry = false) {
            // Remove existing error messages
            const existingErrors = chatInput.parentNode.querySelectorAll('.error-message, .warning-message');
            existingErrors.forEach(error => error.remove());

            const errorDiv = document.createElement('div');
            errorDiv.className = type === 'warning' ? 'warning-message' : 'error-message';

            const messageSpan = document.createElement('span');
            messageSpan.textContent = message;
            errorDiv.appendChild(messageSpan);

            // Add retry button if requested
            if (showRetry) {
                const retryBtn = document.createElement('button');
                retryBtn.className = 'retry-btn';
                retryBtn.textContent = 'Retry';
                retryBtn.onclick = () => {
                    errorDiv.remove();
                    handleChatSubmit();
                };
                errorDiv.appendChild(retryBtn);
            }

            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.className = 'close-btn';
            closeBtn.innerHTML = '×';
            closeBtn.onclick = () => errorDiv.remove();
            errorDiv.appendChild(closeBtn);

            // Insert before chat input
            chatInput.parentNode.insertBefore(errorDiv, chatInput);

            // Auto-remove after 8 seconds (longer for retry messages)
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, showRetry ? 8000 : 5000);
        }

        function showCreditPurchasePrompt() {
            console.log('showCreditPurchasePrompt function called.'); // Debugging line

            // Remove existing prompts
            const existingPrompts = document.querySelectorAll('.credit-purchase-prompt');
            existingPrompts.forEach(prompt => prompt.remove());

            const promptDiv = document.createElement('div');
            promptDiv.className = 'credit-purchase-prompt';
            promptDiv.innerHTML = `
                <div class="prompt-header">
                    <h4>⚡ ChatGABI Credits Needed</h4>
                    <button class="close-prompt" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <p>You need more credits to continue using Swiftmind's ChatGABI AI assistant.</p>
                <div class="current-balance">
                    <span class="balance-label">Current Balance:</span>
                    <span class="balance-amount">${getCurrentCredits()} credits</span>
                </div>
                <div class="credit-packages">
                    <button class="credit-package starter-package" data-package="starter">
                        <div class="package-name">Starter Pack</div>
                        <div class="package-price">$5</div>
                        <div class="package-details">500 Credits<br><small>Perfect for getting started</small></div>
                    </button>
                    <button class="credit-package growth-package" data-package="growth">
                        <div class="package-name">Growth Pack</div>
                        <div class="package-price">$15</div>
                        <div class="package-details">1,500 Credits + GPT-4<br><small>Most popular choice</small></div>
                    </button>
                    <button class="credit-package business-package" data-package="business">
                        <div class="package-name">Business Pack</div>
                        <div class="package-price">$30</div>
                        <div class="package-details">3,000 Credits + GPT-4<br><small>Best value for businesses</small></div>
                    </button>
                </div>
                <div class="prompt-footer">
                    <small>💳 Secure payment via Paystack • 🔒 Instant credit delivery</small>
                </div>
            `;

            // Insert after credit display
            if (creditDisplay) {
                creditDisplay.parentNode.insertBefore(promptDiv, creditDisplay.nextSibling);
                console.log('Credit purchase prompt inserted into DOM.'); // Debugging line
            } else {
                console.error('Credit display element not found, cannot insert prompt.');
            }

            // Add click handlers for purchase buttons
            promptDiv.addEventListener('click', function(e) {
                if (e.target.classList.contains('credit-package')) {
                    console.log('Credit package button clicked!'); // Debugging line
                    const packageType = e.target.dataset.package;
                    initiatePurchase(packageType);
                }
            });
        }

        function initiatePurchase(packageType) {
            // Call the payment initiation function defined in payments.js
            if (typeof window.businesscraft_ai_initiate_frontend_payment === 'function') {
                window.businesscraft_ai_initiate_frontend_payment(packageType);
            } else {
                showError('Payment initiation function not available.');
                console.error('businesscraft_ai_initiate_frontend_payment function is not defined.');
            }
        }

        function loadTemplates() {
            if (!promptTemplates) return;

            fetch(businesscraftAI.restUrl + 'user-templates', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.templates) {
                    populateTemplateDropdown(data.templates);
                }
            })
            .catch(error => {
                console.error('Failed to load templates:', error);
            });
        }

        function populateTemplateDropdown(templates) {
            // Clear existing options except the first one
            promptTemplates.innerHTML = '<option value="">' + (businesscraftAI.strings.selectTemplate || 'Select a template...') + '</option>';

            templates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                option.dataset.prompt = template.prompt;
                promptTemplates.appendChild(option);
            });
        }

        function loadTemplate(templateId) {
            const option = promptTemplates.querySelector(`option[value="${templateId}"]`);
            if (option && option.dataset.prompt) {
                chatInput.value = option.dataset.prompt;
                chatInput.focus();
            }
        }

        function showSaveTemplateDialog() {
            const currentMessage = chatInput.value.trim();
            if (!currentMessage) {
                showError('Please enter a message to save as template.');
                return;
            }

            const templateName = prompt('Enter a name for this template:');
            if (!templateName) return;

            const category = prompt('Enter a category (optional):') || 'general';

            saveTemplate(templateName, currentMessage, category);
        }

        function saveTemplate(name, prompt, category) {
            fetch(businesscraftAI.restUrl + 'save-prompt-template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': businesscraftAI.nonce
                },
                body: JSON.stringify({
                    name: name,
                    prompt: prompt,
                    category: category
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('Template saved successfully!');
                    loadTemplates(); // Reload templates
                } else {
                    showError(data.message || 'Failed to save template.');
                }
            })
            .catch(error => {
                console.error('Failed to save template:', error);
                showError('Failed to save template.');
            });
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;

            // Insert before chat input
            chatInput.parentNode.insertBefore(successDiv, chatInput);

            // Remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }

        // Credit estimation and management functions
        function estimateMessageCost(message) {
            // Simple estimation: ~4 characters per token, minimum 1 credit
            const estimatedTokens = Math.ceil(message.length / 4) + 100; // Add context overhead
            const estimatedCredits = Math.max(1, Math.ceil(estimatedTokens / 1000));
            return estimatedCredits;
        }

        function getCurrentCredits() {
            if (creditDisplay) {
                const creditsText = creditDisplay.textContent || creditDisplay.innerText;
                const match = creditsText.match(/Credits:\s*(\d+)/);
                return match ? parseInt(match[1]) : 0;
            }
            return 0;
        }

        function showCostEstimate(estimatedCost) {
            const estimateDiv = document.createElement('div');
            estimateDiv.className = 'cost-estimate';
            estimateDiv.innerHTML = `
                <span class="estimate-icon">💳</span>
                <span class="estimate-text">Estimated cost: ${estimatedCost} credits</span>
            `;

            // Insert before chat messages
            if (chatMessages) {
                chatMessages.appendChild(estimateDiv);

                // Auto-remove after 3 seconds
                setTimeout(() => {
                    if (estimateDiv.parentNode) {
                        estimateDiv.remove();
                    }
                }, 3000);

                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }
    }

    // Global function for pricing buttons outside chat blocks
    window.initiatePurchaseFromPricing = function(packageType) {
        console.log('Initiating purchase from pricing section for:', packageType);

        // Check if user is logged in
        if (typeof businesscraftAI === 'undefined') {
            // User not logged in, redirect to login
            alert('Please log in to purchase credits.');
            window.location.href = '/wp-login.php?redirect_to=' + encodeURIComponent(window.location.href);
            return;
        }

        // Call the payment initiation function
        if (typeof window.businesscraft_ai_initiate_frontend_payment === 'function') {
            window.businesscraft_ai_initiate_frontend_payment(packageType);
        } else {
            console.error('Payment function not available. Loading payments.js...');

            // Try to load payments.js if not already loaded
            const script = document.createElement('script');
            script.src = businesscraftAI.themeUrl + '/assets/js/payments.js';
            script.onload = function() {
                if (typeof window.businesscraft_ai_initiate_frontend_payment === 'function') {
                    window.businesscraft_ai_initiate_frontend_payment(packageType);
                } else {
                    alert('Payment system not available. Please try again later.');
                }
            };
            script.onerror = function() {
                alert('Failed to load payment system. Please try again later.');
            };
            document.head.appendChild(script);
        }

        function initializeTemplateModal() {
            // Create template selection button
            const templateButton = document.createElement('button');
            templateButton.type = 'button';
            templateButton.className = 'template-trigger-btn';
            templateButton.innerHTML = '📋 Choose Template';
            templateButton.onclick = showTemplateModal;

            // Insert before chat input
            const chatInputContainer = chatInput.parentElement;
            chatInputContainer.insertBefore(templateButton, chatInput);

            // Create template modal
            createTemplateModal();
        }

        function createTemplateModal() {
            const modal = document.createElement('div');
            modal.id = 'chatgabi-template-modal';
            modal.className = 'chatgabi-template-modal';
            modal.innerHTML = `
                <div class="template-modal-content">
                    <div class="template-modal-header">
                        <h2 class="template-modal-title">Choose a Prompt Template</h2>
                        <button type="button" class="template-modal-close" onclick="closeTemplateModal()">&times;</button>
                    </div>
                    <div class="template-modal-body">
                        <div class="template-filters">
                            <input type="search" class="template-search" placeholder="Search templates..." id="template-search">
                            <select class="template-category-filter" id="template-category-filter">
                                <option value="">All Categories</option>
                            </select>
                            <select class="template-complexity-filter" id="template-complexity-filter">
                                <option value="">All Levels</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                        <div class="template-categories" id="template-categories">
                            <!-- Categories will be loaded here -->
                        </div>
                        <div class="templates-grid" id="templates-grid">
                            <div class="templates-loading">
                                <div class="loading-spinner"></div>
                                Loading templates...
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Load categories and templates
            loadTemplateCategories();
            loadTemplateLibrary();

            // Add event listeners
            document.getElementById('template-search').addEventListener('input', debounce(filterTemplates, 300));
            document.getElementById('template-category-filter').addEventListener('change', filterTemplates);
            document.getElementById('template-complexity-filter').addEventListener('change', filterTemplates);
        }

        function showTemplateModal() {
            const modal = document.getElementById('chatgabi-template-modal');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        window.closeTemplateModal = function() {
            const modal = document.getElementById('chatgabi-template-modal');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
        }

        function loadTemplateCategories() {
            fetch(businesscraftAI.restUrl + 'chatgabi/v1/template-categories', {
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            })
            .then(response => response.json())
            .then(categories => {
                const categoryFilter = document.getElementById('template-category-filter');
                const categoriesContainer = document.getElementById('template-categories');

                // Populate filter dropdown
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = `${category.name} (${category.template_count || 0})`;
                    categoryFilter.appendChild(option);
                });

                // Create category tabs
                categoriesContainer.innerHTML = categories.map(category => `
                    <button type="button" class="category-tab" data-category="${category.id}">
                        <span class="category-icon">${category.icon || '📁'}</span>
                        ${category.name}
                    </button>
                `).join('');

                // Add event listeners to category tabs
                categoriesContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('category-tab')) {
                        // Update active tab
                        categoriesContainer.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
                        e.target.classList.add('active');

                        // Update filter and reload templates
                        document.getElementById('template-category-filter').value = e.target.dataset.category;
                        filterTemplates();
                    }
                });
            })
            .catch(error => {
                console.error('Failed to load template categories:', error);
            });
        }

        function loadTemplateLibrary(filters = {}) {
            const params = new URLSearchParams({
                public: 'true',
                limit: 50,
                ...filters
            });

            fetch(businesscraftAI.restUrl + `chatgabi/v1/templates?${params}`, {
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            })
            .then(response => response.json())
            .then(data => {
                const templatesGrid = document.getElementById('templates-grid');

                if (data.length === 0) {
                    templatesGrid.innerHTML = `
                        <div class="templates-empty">
                            <h3>No templates found</h3>
                            <p>Try adjusting your search criteria or browse different categories.</p>
                        </div>
                    `;
                    return;
                }

                templatesGrid.innerHTML = data.map(template => `
                    <div class="template-card ${template.is_featured ? 'featured' : ''}" data-template-id="${template.id}">
                        <div class="template-header">
                            <div class="template-category-icon" style="background-color: ${template.category_color || '#f8f9fa'}">
                                ${template.category_icon || '📄'}
                            </div>
                            <div class="template-info">
                                <h3 class="template-title">${template.title}</h3>
                                <p class="template-category">${template.category_name || 'General'}</p>
                            </div>
                        </div>
                        <p class="template-description">${template.description || ''}</p>
                        <div class="template-meta">
                            <span class="template-complexity ${template.complexity_level || 'beginner'}">
                                ${template.complexity_level || 'beginner'}
                            </span>
                            <span class="template-usage">Used ${template.usage_count || 0} times</span>
                        </div>
                        <div class="template-actions">
                            <button type="button" class="template-btn" onclick="previewTemplate(${template.id})">
                                👁️ Preview
                            </button>
                            <button type="button" class="template-btn primary" onclick="useTemplate(${template.id})">
                                ✨ Use Template
                            </button>
                        </div>
                    </div>
                `).join('');
            })
            .catch(error => {
                console.error('Failed to load templates:', error);
                document.getElementById('templates-grid').innerHTML = `
                    <div class="templates-empty">
                        <h3>Error loading templates</h3>
                        <p>Please try again later.</p>
                    </div>
                `;
            });
        }

        function filterTemplates() {
            const search = document.getElementById('template-search').value;
            const category = document.getElementById('template-category-filter').value;
            const complexity = document.getElementById('template-complexity-filter').value;

            const filters = {};
            if (search) filters.search = search;
            if (category) filters.category = category;
            if (complexity) filters.complexity = complexity;

            loadTemplateLibrary(filters);
        }

        window.previewTemplate = function(templateId) {
            // Load and show template preview
            fetch(businesscraftAI.restUrl + `chatgabi/v1/templates/${templateId}`, {
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            })
            .then(response => response.json())
            .then(template => {
                showTemplatePreview(template);
            })
            .catch(error => {
                console.error('Failed to load template:', error);
                showError('Failed to load template preview');
            });
        }

        window.useTemplate = function(templateId) {
            // Load template and apply to chat input
            fetch(businesscraftAI.restUrl + `chatgabi/v1/templates/${templateId}`, {
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            })
            .then(response => response.json())
            .then(template => {
                applyTemplateToChat(template);
            })
            .catch(error => {
                console.error('Failed to load template:', error);
                showError('Failed to load template');
            });
        }

        function showTemplatePreview(template) {
            // Create preview modal
            const previewModal = document.createElement('div');
            previewModal.className = 'template-preview-modal active';
            previewModal.innerHTML = `
                <div class="template-preview-content">
                    <div class="template-preview-header">
                        <h2>${template.title}</h2>
                        <button type="button" class="template-modal-close" onclick="closeTemplatePreview()">&times;</button>
                    </div>
                    <div class="template-preview-body">
                        <div class="template-preview-text">${template.prompt_text}</div>
                        <div class="template-variables" id="template-variables">
                            <!-- Variables will be populated here -->
                        </div>
                    </div>
                    <div class="template-preview-footer">
                        <button type="button" class="template-btn" onclick="closeTemplatePreview()">Cancel</button>
                        <button type="button" class="template-btn primary" onclick="applyTemplateFromPreview(${template.id})">Use Template</button>
                    </div>
                </div>
            `;

            document.body.appendChild(previewModal);

            // Extract and show variables
            const variables = extractTemplateVariables(template.prompt_content);
            if (variables.length > 0) {
                showTemplateVariables(variables);
            }

            window.closeTemplatePreview = function() {
                previewModal.remove();
            }

            window.applyTemplateFromPreview = function(templateId) {
                const filledTemplate = fillTemplateVariables(template.prompt_text);
                chatInput.value = filledTemplate;
                closeTemplatePreview();
                closeTemplateModal();

                // Track template usage
                trackTemplateUsage(templateId);

                showSuccess('Template applied! You can now edit and send your prompt.');
            }
        }

        function applyTemplateToChat(template) {
            const variables = extractTemplateVariables(template.prompt_text);

            if (variables.length > 0) {
                // Show preview with variables if template has variables
                showTemplatePreview(template);
            } else {
                // Apply directly if no variables
                chatInput.value = template.prompt_text;
                closeTemplateModal();

                // Track template usage
                trackTemplateUsage(template.id);

                showSuccess('Template applied! You can now edit and send your prompt.');
            }
        }

        function extractTemplateVariables(content) {
            const regex = /\{([^}]+)\}/g;
            const variables = [];
            let match;

            while ((match = regex.exec(content)) !== null) {
                if (!variables.includes(match[1])) {
                    variables.push(match[1]);
                }
            }

            return variables;
        }

        function showTemplateVariables(variables) {
            const container = document.getElementById('template-variables');
            if (!container) return;

            container.innerHTML = `
                <h4>Fill in the template variables:</h4>
                ${variables.map(variable => `
                    <div class="variable-input">
                        <label for="var-${variable}">${variable.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</label>
                        ${getVariableInput(variable)}
                    </div>
                `).join('')}
            `;
        }

        function getVariableInput(variable) {
            const lowerVar = variable.toLowerCase();

            if (lowerVar === 'country') {
                return `
                    <select id="var-${variable}">
                        <option value="">Select country...</option>
                        <option value="Ghana">Ghana</option>
                        <option value="Kenya">Kenya</option>
                        <option value="Nigeria">Nigeria</option>
                        <option value="South Africa">South Africa</option>
                    </select>
                `;
            } else if (lowerVar.includes('sector') || lowerVar.includes('industry')) {
                return `
                    <select id="var-${variable}">
                        <option value="">Select sector...</option>
                        <option value="Agriculture">Agriculture</option>
                        <option value="Fintech">Fintech</option>
                        <option value="Healthcare">Healthcare</option>
                        <option value="Education">Education</option>
                        <option value="E-commerce">E-commerce</option>
                        <option value="Manufacturing">Manufacturing</option>
                        <option value="Technology">Technology</option>
                        <option value="Energy">Energy</option>
                        <option value="Transportation">Transportation</option>
                        <option value="Real Estate">Real Estate</option>
                    </select>
                `;
            } else {
                return `<input type="text" id="var-${variable}" placeholder="Enter ${variable.replace(/_/g, ' ')}...">`;
            }
        }

        function fillTemplateVariables(content) {
            const variables = extractTemplateVariables(content);
            let filledContent = content;

            variables.forEach(variable => {
                const input = document.getElementById(`var-${variable}`);
                if (input && input.value) {
                    filledContent = filledContent.replace(new RegExp(`\\{${variable}\\}`, 'g'), input.value);
                }
            });

            return filledContent;
        }

        function trackTemplateUsage(templateId) {
            fetch(businesscraftAI.restUrl + `chatgabi/v1/templates/${templateId}/use`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': businesscraftAI.nonce
                },
                body: JSON.stringify({
                    tokens_used: 0,
                    credits_used: 0
                })
            })
            .catch(error => {
                console.error('Failed to track template usage:', error);
            });
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function showSuccess(message) {
            // Create a simple success notification
            const notification = document.createElement('div');
            notification.className = 'template-notification success';
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 1000001;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    }

    /**
     * Setup chat accessibility features
     */
    function setupChatAccessibility() {
        // Add ARIA labels and roles
        if (chatInput) {
            chatInput.setAttribute('aria-label', 'Enter your message for ChatGABI');
            chatInput.setAttribute('aria-describedby', 'chat-help-text');
        }
        if (chatSubmit) {
            chatSubmit.setAttribute('aria-label', 'Send message to ChatGABI');
        }

        if (chatMessages) {
            chatMessages.setAttribute('role', 'log');
            chatMessages.setAttribute('aria-label', 'Chat conversation');
            chatMessages.setAttribute('aria-live', 'polite');
        }

        // Add help text
        if (chatInput && !document.getElementById('chat-help-text')) {
            const helpText = document.createElement('div');
            helpText.id = 'chat-help-text';
            helpText.className = 'sr-only';
            helpText.textContent = 'Press Ctrl+Enter to send message. Use arrow keys to navigate chat history.';
            chatInput.parentNode.insertBefore(helpText, chatInput);
        }

        // Screen reader announcements
        setupChatScreenReaderSupport();
    }

    /**
     * Setup screen reader support for chat
     */
    function setupChatScreenReaderSupport() {
        // Create announcement region
        if (!document.getElementById('chat-announcements')) {
            const announcements = document.createElement('div');
            announcements.id = 'chat-announcements';
            announcements.setAttribute('aria-live', 'assertive');
            announcements.setAttribute('aria-atomic', 'true');
            announcements.className = 'sr-only';
            document.body.appendChild(announcements);
        }
    }

    /**
     * Announce to screen reader (chat-specific)
     */
    function announceToChatScreenReader(message) {
        const announcements = document.getElementById('chat-announcements');
        if (announcements) {
            announcements.textContent = message;
            setTimeout(() => {
                announcements.textContent = '';
            }, 1000);
        }
    }

    /**
     * Setup mobile chat optimizations
     */
    function setupMobileChatOptimizations() {
        if (isMobileDevice()) {
            // Add mobile-specific classes
            const chatContainer = document.querySelector('.chat-container, .chat-block');
            if (chatContainer) {
                chatContainer.classList.add('mobile-chat');
            }

            // Optimize input for mobile
            optimizeMobileChatInput();

            // Setup mobile gestures
            setupMobileChatGestures();

            // Handle virtual keyboard
            handleVirtualKeyboard();
        }
    }

    /**
     * Check if device is mobile
     */
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    /**
     * Optimize chat input for mobile
     */
    function optimizeMobileChatInput() {
        if (!chatInput) return;

        // Prevent zoom on input focus
        chatInput.style.fontSize = '16px';
        chatInput.setAttribute('autocomplete', 'off');
        chatInput.setAttribute('autocorrect', 'off');
        chatInput.setAttribute('autocapitalize', 'sentences');

        // Add mobile-specific styling
        chatInput.classList.add('mobile-input');
        if (chatSubmit) {
            chatSubmit.classList.add('mobile-button');
        }
    }

    /**
     * Setup mobile chat gestures
     */
    function setupMobileChatGestures() {
        if (!chatMessages) return;

        // Double tap to scroll to bottom
        let lastTap = 0;
        chatMessages.addEventListener('touchend', function(e) {
            const currentTime = new Date().getTime();
            const tapLength = currentTime - lastTap;
            if (tapLength < 500 && tapLength > 0) {
                // Double tap detected
                chatMessages.scrollTop = chatMessages.scrollHeight;
                announceToChatScreenReader('Scrolled to latest message');
            }
            lastTap = currentTime;
        });
    }

    /**
     * Handle virtual keyboard on mobile
     */
    function handleVirtualKeyboard() {
        // Handle keyboard show/hide
        let initialViewportHeight = window.innerHeight;

        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDiff = initialViewportHeight - currentHeight;

            if (heightDiff > 150) {
                // Keyboard is likely open
                document.body.classList.add('keyboard-open');
                if (chatMessages) {
                    setTimeout(() => {
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }, 100);
                }
            } else {
                // Keyboard is likely closed
                document.body.classList.remove('keyboard-open');
            }
        });
    }

    // Gutenberg block registration (if in editor)
    if (typeof wp !== 'undefined' && wp.blocks) {
        const { registerBlockType } = wp.blocks;
        const { createElement } = wp.element;

        registerBlockType('businesscraft-ai/chat-block', {
            title: 'BusinessCraft AI Chat',
            icon: 'format-chat',
            category: 'widgets',
            attributes: {
                showHistory: {
                    type: 'boolean',
                    default: true
                },
                showExamples: {
                    type: 'boolean',
                    default: true
                },
                maxHistory: {
                    type: 'number',
                    default: 10
                }
            },
            edit: function(props) {
                return createElement('div', {
                    className: 'businesscraft-ai-chat-editor'
                }, 'BusinessCraft AI Chat Block - Preview not available in editor');
            },
            save: function() {
                return null; // Rendered by PHP
            }
        });
    }

})();

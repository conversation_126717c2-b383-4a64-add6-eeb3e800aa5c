<?php
/**
 * Test ChatGABI Dashboard Fixes
 * 
 * This script tests the fixes for:
 * 1. Preferences page 404 error
 * 2. Missing template samples in dashboard
 */

// Load WordPress
require_once('wp-load.php');

echo "<h1>ChatGABI Dashboard Fixes Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// Test 1: Preferences Page
echo '<div class="test-section">';
echo '<h2>🔧 Test 1: Preferences Page Fix</h2>';

$preferences_page = get_page_by_path('preferences');
if ($preferences_page) {
    echo '<p class="success">✅ Preferences page exists</p>';
    echo '<p class="info">📄 Page ID: ' . $preferences_page->ID . '</p>';
    echo '<p class="info">📄 Status: ' . $preferences_page->post_status . '</p>';
    echo '<p class="info">🔗 URL: <a href="' . get_permalink($preferences_page->ID) . '" target="_blank">' . get_permalink($preferences_page->ID) . '</a></p>';
    
    // Check template assignment
    $template = get_post_meta($preferences_page->ID, '_wp_page_template', true);
    if ($template === 'page-preferences.php') {
        echo '<p class="success">✅ Correct template assigned: ' . $template . '</p>';
    } else {
        echo '<p class="warning">⚠️ Template: ' . ($template ?: 'default') . '</p>';
    }
} else {
    echo '<p class="error">❌ Preferences page not found</p>';
    echo '<p class="info">🔧 Attempting to create preferences page...</p>';
    
    if (function_exists('chatgabi_create_required_pages')) {
        chatgabi_create_required_pages();
        
        $preferences_page = get_page_by_path('preferences');
        if ($preferences_page) {
            echo '<p class="success">✅ Preferences page created successfully!</p>';
            echo '<p class="info">🔗 URL: <a href="' . get_permalink($preferences_page->ID) . '" target="_blank">' . get_permalink($preferences_page->ID) . '</a></p>';
        } else {
            echo '<p class="error">❌ Failed to create preferences page</p>';
        }
    } else {
        echo '<p class="error">❌ chatgabi_create_required_pages function not found</p>';
    }
}
echo '</div>';

// Test 2: Template System
echo '<div class="test-section">';
echo '<h2>📋 Test 2: Template Samples System</h2>';

// Check if template functions exist
$template_functions = [
    'chatgabi_check_templates_tables_exist',
    'chatgabi_get_template_categories',
    'chatgabi_get_public_templates',
    'chatgabi_create_sample_templates'
];

$missing_functions = [];
foreach ($template_functions as $function) {
    if (function_exists($function)) {
        echo '<p class="success">✅ ' . $function . '() exists</p>';
    } else {
        echo '<p class="error">❌ ' . $function . '() missing</p>';
        $missing_functions[] = $function;
    }
}

if (empty($missing_functions)) {
    // Check template tables
    if (chatgabi_check_templates_tables_exist()) {
        echo '<p class="success">✅ Template tables exist</p>';
        
        global $wpdb;
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
        
        // Check categories
        $categories_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
        echo '<p class="info">📂 Template categories: ' . $categories_count . '</p>';
        
        // Check sample templates
        $sample_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0 AND is_public = 1");
        echo '<p class="info">📋 Sample templates: ' . $sample_count . '</p>';
        
        if ($sample_count == 0) {
            echo '<p class="warning">⚠️ No sample templates found. Creating them...</p>';
            chatgabi_create_sample_templates();
            
            $sample_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0 AND is_public = 1");
            if ($sample_count > 0) {
                echo '<p class="success">✅ Created ' . $sample_count . ' sample templates</p>';
            } else {
                echo '<p class="error">❌ Failed to create sample templates</p>';
            }
        } else {
            echo '<p class="success">✅ Sample templates already exist</p>';
        }
        
        // Show sample templates
        if ($sample_count > 0) {
            $samples = $wpdb->get_results("SELECT title, description, category_id FROM {$templates_table} WHERE user_id = 0 AND is_public = 1 LIMIT 3");
            echo '<h3>Sample Templates Preview:</h3>';
            echo '<ul>';
            foreach ($samples as $sample) {
                echo '<li><strong>' . esc_html($sample->title) . '</strong> - ' . esc_html($sample->description) . '</li>';
            }
            echo '</ul>';
        }
        
    } else {
        echo '<p class="error">❌ Template tables do not exist</p>';
    }
} else {
    echo '<p class="error">❌ Missing template functions. Template system may not be fully loaded.</p>';
}
echo '</div>';

// Test 3: Dashboard Page
echo '<div class="test-section">';
echo '<h2>🏠 Test 3: Dashboard Page</h2>';

$dashboard_template = get_template_directory() . '/page-dashboard.php';
if (file_exists($dashboard_template)) {
    echo '<p class="success">✅ Dashboard template exists</p>';
    echo '<p class="info">📄 Path: ' . $dashboard_template . '</p>';
    
    // Check if prompt-templates.js is being enqueued
    $js_file = get_template_directory() . '/assets/js/prompt-templates.js';
    if (file_exists($js_file)) {
        echo '<p class="success">✅ Prompt templates JavaScript exists</p>';
    } else {
        echo '<p class="error">❌ Prompt templates JavaScript missing</p>';
    }
    
} else {
    echo '<p class="error">❌ Dashboard template not found</p>';
}
echo '</div>';

// Test 4: REST API Endpoints
echo '<div class="test-section">';
echo '<h2>🔌 Test 4: REST API Endpoints</h2>';

$rest_url = rest_url('chatgabi/v1/templates');
echo '<p class="info">🔗 Templates endpoint: <a href="' . $rest_url . '" target="_blank">' . $rest_url . '</a></p>';

// Test if REST routes are registered
if (function_exists('chatgabi_register_template_rest_routes')) {
    echo '<p class="success">✅ Template REST routes function exists</p>';
} else {
    echo '<p class="error">❌ Template REST routes function missing</p>';
}
echo '</div>';

echo '<div class="test-section">';
echo '<h2>🎯 Summary</h2>';
echo '<p><strong>Fixes Applied:</strong></p>';
echo '<ul>';
echo '<li>✅ Added preferences page creation on theme activation</li>';
echo '<li>✅ Enhanced dashboard templates tab with view toggle (My Templates / Template Samples)</li>';
echo '<li>✅ Added prompt-templates.js loading for dashboard page</li>';
echo '<li>✅ Created sample templates system</li>';
echo '<li>✅ Added proper CSS styling for templates interface</li>';
echo '<li>✅ Added JavaScript handlers for template view switching</li>';
echo '</ul>';

echo '<p><strong>Next Steps:</strong></p>';
echo '<ul>';
echo '<li>🔗 Test the preferences page link in the dashboard</li>';
echo '<li>📋 Test the templates tab functionality</li>';
echo '<li>🔄 Verify template samples are loading correctly</li>';
echo '</ul>';
echo '</div>';

echo '<p><em>Test completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>

<?php
/**
 * PHP Info Page for Debugging
 * Access via: http://localhost/swifmind-local/phpinfo.php
 * 
 * SECURITY WARNING: Remove this file in production!
 */

// Only allow access from localhost for security
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    die('Access denied. This file can only be accessed from localhost.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>PHP Info - BusinessCraft AI Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 4px; }
        .info-section { margin: 20px 0; }
        h1 { color: #333; }
        h2 { color: #0073aa; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
    </style>
</head>
<body>
    <h1>🔧 PHP Info - BusinessCraft AI Debug</h1>
    
    <div class="warning">
        <strong>⚠️ Security Warning:</strong> This file shows sensitive server information. 
        Remove it from production servers!
    </div>
    
    <div class="info-section">
        <h2>Quick Info</h2>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
        <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></p>
        <p><strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?> seconds</p>
        <p><strong>Upload Max Filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
        <p><strong>Post Max Size:</strong> <?php echo ini_get('post_max_size'); ?></p>
    </div>
    
    <div class="info-section">
        <h2>WordPress Check</h2>
        <?php
        $wp_config = __DIR__ . '/wp-config.php';
        $wp_load = __DIR__ . '/wp-load.php';
        
        if (file_exists($wp_config)) {
            echo "<p>✅ wp-config.php found</p>";
        } else {
            echo "<p>❌ wp-config.php not found</p>";
        }
        
        if (file_exists($wp_load)) {
            echo "<p>✅ wp-load.php found</p>";
        } else {
            echo "<p>❌ wp-load.php not found</p>";
        }
        
        // Try to get WordPress version without loading WordPress
        if (file_exists(__DIR__ . '/wp-includes/version.php')) {
            $version_content = file_get_contents(__DIR__ . '/wp-includes/version.php');
            if (preg_match('/\$wp_version\s*=\s*[\'"]([^\'"]+)[\'"]/', $version_content, $matches)) {
                echo "<p>✅ WordPress Version: " . $matches[1] . "</p>";
            }
        }
        ?>
    </div>
    
    <div class="info-section">
        <h2>Test Links</h2>
        <p><a href="simple-test.php" target="_blank">🔗 Simple Test (No WordPress)</a></p>
        <p><a href="businesscraft-ai-test.php" target="_blank">🔗 WordPress Test</a></p>
        <p><a href="wp-admin/" target="_blank">🔗 WordPress Admin</a></p>
        <p><a href="./" target="_blank">🔗 WordPress Site</a></p>
    </div>
    
    <hr>
    
    <h2>Full PHP Info</h2>
    <?php phpinfo(); ?>
</body>
</html>

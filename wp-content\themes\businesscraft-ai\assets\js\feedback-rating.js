/**
 * ChatGABI Feedback Rating System JavaScript
 * 
 * Handles user feedback and rating interactions for AI responses
 */

(function($) {
    'use strict';

    // Global feedback state
    window.chatgabiFeedbackSystem = {
        currentRatings: {},
        isSubmitting: false,
        feedbackCache: {}
    };

    // Initialize when document is ready
    $(document).ready(function() {
        initializeFeedbackSystem();
    });

    /**
     * Initialize feedback system
     */
    function initializeFeedbackSystem() {
        console.log('Initializing ChatGABI Feedback Rating System');

        // Set up event listeners
        setupEventListeners();

        // Load existing feedback for visible messages
        loadExistingFeedback();

        console.log('Feedback Rating System initialized');
    }

    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Listen for new AI responses
        $(document).on('chatgabi:responseCompleted', function(event, data) {
            addFeedbackInterface(data);
        });

        // Star rating clicks
        $(document).on('click', '.feedback-star', handleStarRating);

        // Thumbs rating clicks
        $(document).on('click', '.feedback-thumbs', handleThumbsRating);

        // Category rating changes
        $(document).on('change', '.category-rating', handleCategoryRating);

        // Feedback text submission
        $(document).on('click', '.submit-feedback-btn', handleFeedbackSubmission);

        // Toggle detailed feedback form
        $(document).on('click', '.toggle-detailed-feedback', toggleDetailedFeedback);

        // Training consent checkbox
        $(document).on('change', '.training-consent', handleTrainingConsent);
    }

    /**
     * Add feedback interface to AI response
     */
    function addFeedbackInterface(responseData) {
        const messageId = responseData.message_id || generateMessageId();
        const responseElement = $('.ai-message').last();
        
        if (responseElement.length === 0) {
            return;
        }

        // Check if feedback interface already exists
        if (responseElement.find('.feedback-interface').length > 0) {
            return;
        }

        const feedbackHtml = createFeedbackInterface(messageId, responseData);
        responseElement.append(feedbackHtml);

        // Store response data for submission
        window.chatgabiFeedbackSystem.currentRatings[messageId] = {
            conversation_id: responseData.conversation_id,
            session_id: responseData.session_id,
            response_tokens: responseData.tokens_used,
            response_time_ms: responseData.response_time_ms,
            conversation_context: responseData.context || 'general'
        };
    }

    /**
     * Create feedback interface HTML
     */
    function createFeedbackInterface(messageId, responseData) {
        return `
            <div class="feedback-interface" data-message-id="${messageId}">
                <div class="feedback-header">
                    <span class="feedback-title">${chatgabiFeedback.strings.rateResponse}</span>
                    <button class="toggle-detailed-feedback" type="button">
                        <span class="dashicons dashicons-admin-generic"></span>
                    </button>
                </div>
                
                <div class="feedback-quick-rating">
                    <!-- Star Rating -->
                    <div class="star-rating">
                        ${createStarRating(messageId)}
                    </div>
                    
                    <!-- Thumbs Rating -->
                    <div class="thumbs-rating">
                        <button class="feedback-thumbs thumbs-up" data-message-id="${messageId}" data-rating="1" title="Helpful">
                            <span class="dashicons dashicons-thumbs-up"></span>
                        </button>
                        <button class="feedback-thumbs thumbs-down" data-message-id="${messageId}" data-rating="0" title="Not Helpful">
                            <span class="dashicons dashicons-thumbs-down"></span>
                        </button>
                    </div>
                </div>
                
                <div class="feedback-detailed" style="display: none;">
                    <div class="category-ratings">
                        <div class="category-group">
                            <label>${chatgabiFeedback.strings.helpfulness}</label>
                            <select class="category-rating" data-category="helpfulness" data-message-id="${messageId}">
                                <option value="">-</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        </div>
                        <div class="category-group">
                            <label>${chatgabiFeedback.strings.accuracy}</label>
                            <select class="category-rating" data-category="accuracy" data-message-id="${messageId}">
                                <option value="">-</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        </div>
                        <div class="category-group">
                            <label>${chatgabiFeedback.strings.relevance}</label>
                            <select class="category-rating" data-category="relevance" data-message-id="${messageId}">
                                <option value="">-</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        </div>
                        <div class="category-group">
                            <label>${chatgabiFeedback.strings.clarity}</label>
                            <select class="category-rating" data-category="clarity" data-message-id="${messageId}">
                                <option value="">-</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="feedback-text-area">
                        <textarea class="feedback-text" data-message-id="${messageId}" 
                                  placeholder="Share your thoughts about this response (${chatgabiFeedback.strings.optional})" 
                                  rows="3"></textarea>
                    </div>
                    
                    <div class="feedback-options">
                        <label class="training-consent-label">
                            <input type="checkbox" class="training-consent" data-message-id="${messageId}">
                            Allow this feedback to be used for AI training improvements
                        </label>
                    </div>
                    
                    <div class="feedback-actions">
                        <button class="submit-feedback-btn" data-message-id="${messageId}">
                            ${chatgabiFeedback.strings.submitFeedback}
                        </button>
                    </div>
                </div>
                
                <div class="feedback-status" style="display: none;"></div>
            </div>
        `;
    }

    /**
     * Create star rating HTML
     */
    function createStarRating(messageId) {
        let starsHtml = '';
        for (let i = 1; i <= 5; i++) {
            starsHtml += `
                <button class="feedback-star" data-message-id="${messageId}" data-rating="${i}" title="${i} star${i > 1 ? 's' : ''}">
                    <span class="dashicons dashicons-star-empty"></span>
                </button>
            `;
        }
        return starsHtml;
    }

    /**
     * Handle star rating clicks
     */
    function handleStarRating(e) {
        e.preventDefault();
        
        const button = $(this);
        const messageId = button.data('message-id');
        const rating = button.data('rating');
        
        // Update visual state
        updateStarDisplay(messageId, rating);
        
        // Submit rating
        submitQuickRating(messageId, rating, 'star');
    }

    /**
     * Handle thumbs rating clicks
     */
    function handleThumbsRating(e) {
        e.preventDefault();
        
        const button = $(this);
        const messageId = button.data('message-id');
        const rating = button.data('rating');
        
        // Update visual state
        updateThumbsDisplay(messageId, rating);
        
        // Submit rating
        submitQuickRating(messageId, rating, 'thumbs');
    }

    /**
     * Update star display
     */
    function updateStarDisplay(messageId, rating) {
        const container = $(`.feedback-interface[data-message-id="${messageId}"] .star-rating`);
        
        container.find('.feedback-star').each(function(index) {
            const star = $(this);
            const starRating = index + 1;
            const icon = star.find('.dashicons');
            
            if (starRating <= rating) {
                icon.removeClass('dashicons-star-empty').addClass('dashicons-star-filled');
                star.addClass('active');
            } else {
                icon.removeClass('dashicons-star-filled').addClass('dashicons-star-empty');
                star.removeClass('active');
            }
        });
    }

    /**
     * Update thumbs display
     */
    function updateThumbsDisplay(messageId, rating) {
        const container = $(`.feedback-interface[data-message-id="${messageId}"] .thumbs-rating`);
        
        container.find('.feedback-thumbs').removeClass('active');
        
        if (rating === 1) {
            container.find('.thumbs-up').addClass('active');
        } else {
            container.find('.thumbs-down').addClass('active');
        }
    }

    /**
     * Submit quick rating
     */
    function submitQuickRating(messageId, rating, type) {
        if (window.chatgabiFeedbackSystem.isSubmitting) {
            return;
        }

        window.chatgabiFeedbackSystem.isSubmitting = true;

        const feedbackData = {
            action: 'chatgabi_submit_feedback',
            nonce: chatgabiFeedback.nonce,
            message_id: messageId,
            rating_score: rating,
            rating_type: type,
            ...window.chatgabiFeedbackSystem.currentRatings[messageId]
        };

        $.ajax({
            url: chatgabiFeedback.ajaxUrl,
            type: 'POST',
            data: feedbackData,
            success: function(response) {
                if (response.success) {
                    showFeedbackStatus(messageId, chatgabiFeedback.strings.thankYou, 'success');
                    
                    // Cache the feedback
                    window.chatgabiFeedbackSystem.feedbackCache[messageId] = {
                        rating_score: rating,
                        rating_type: type,
                        feedback_id: response.data.feedback_id
                    };
                } else {
                    showFeedbackStatus(messageId, response.data || chatgabiFeedback.strings.error, 'error');
                }
            },
            error: function() {
                showFeedbackStatus(messageId, chatgabiFeedback.strings.error, 'error');
            },
            complete: function() {
                window.chatgabiFeedbackSystem.isSubmitting = false;
            }
        });
    }

    /**
     * Handle category rating changes
     */
    function handleCategoryRating(e) {
        const select = $(this);
        const messageId = select.data('message-id');
        const category = select.data('category');
        const rating = select.val();
        
        // Store category rating for later submission
        if (!window.chatgabiFeedbackSystem.currentRatings[messageId]) {
            window.chatgabiFeedbackSystem.currentRatings[messageId] = {};
        }
        
        window.chatgabiFeedbackSystem.currentRatings[messageId][`category_${category}`] = rating;
    }

    /**
     * Handle detailed feedback submission
     */
    function handleFeedbackSubmission(e) {
        e.preventDefault();
        
        const button = $(this);
        const messageId = button.data('message-id');
        const container = $(`.feedback-interface[data-message-id="${messageId}"]`);
        
        // Collect all feedback data
        const feedbackData = {
            action: 'chatgabi_submit_feedback',
            nonce: chatgabiFeedback.nonce,
            message_id: messageId,
            feedback_text: container.find('.feedback-text').val(),
            training_consent: container.find('.training-consent').is(':checked') ? 1 : 0,
            ...window.chatgabiFeedbackSystem.currentRatings[messageId]
        };

        // Submit detailed feedback
        submitDetailedFeedback(messageId, feedbackData);
    }

    /**
     * Submit detailed feedback
     */
    function submitDetailedFeedback(messageId, feedbackData) {
        if (window.chatgabiFeedbackSystem.isSubmitting) {
            return;
        }

        window.chatgabiFeedbackSystem.isSubmitting = true;

        $.ajax({
            url: chatgabiFeedback.ajaxUrl,
            type: 'POST',
            data: feedbackData,
            success: function(response) {
                if (response.success) {
                    showFeedbackStatus(messageId, chatgabiFeedback.strings.thankYou, 'success');
                    
                    // Hide detailed form after successful submission
                    $(`.feedback-interface[data-message-id="${messageId}"] .feedback-detailed`).slideUp();
                } else {
                    showFeedbackStatus(messageId, response.data || chatgabiFeedback.strings.error, 'error');
                }
            },
            error: function() {
                showFeedbackStatus(messageId, chatgabiFeedback.strings.error, 'error');
            },
            complete: function() {
                window.chatgabiFeedbackSystem.isSubmitting = false;
            }
        });
    }

    /**
     * Toggle detailed feedback form
     */
    function toggleDetailedFeedback(e) {
        e.preventDefault();
        
        const button = $(this);
        const container = button.closest('.feedback-interface');
        const detailedForm = container.find('.feedback-detailed');
        
        detailedForm.slideToggle();
        button.toggleClass('active');
    }

    /**
     * Handle training consent
     */
    function handleTrainingConsent(e) {
        const checkbox = $(this);
        const messageId = checkbox.data('message-id');
        const consent = checkbox.is(':checked');
        
        // Store consent for later submission
        if (!window.chatgabiFeedbackSystem.currentRatings[messageId]) {
            window.chatgabiFeedbackSystem.currentRatings[messageId] = {};
        }
        
        window.chatgabiFeedbackSystem.currentRatings[messageId].training_consent = consent ? 1 : 0;
    }

    /**
     * Show feedback status message
     */
    function showFeedbackStatus(messageId, message, type) {
        const statusElement = $(`.feedback-interface[data-message-id="${messageId}"] .feedback-status`);
        
        statusElement
            .removeClass('success error')
            .addClass(type)
            .text(message)
            .fadeIn();
        
        // Hide after 3 seconds
        setTimeout(function() {
            statusElement.fadeOut();
        }, 3000);
    }

    /**
     * Load existing feedback for visible messages
     */
    function loadExistingFeedback() {
        // This would load any existing feedback for messages already on the page
        // Implementation depends on how messages are identified and stored
    }

    /**
     * Generate unique message ID
     */
    function generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

})(jQuery);

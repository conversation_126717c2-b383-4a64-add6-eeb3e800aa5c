<?php
/**
 * ChatGABI API Test Page
 *
 * Simple test interface for the ChatGABI Opportunities REST API
 * Access: http://your-domain.com/wp-content/themes/businesscraft-ai/test-api.php
 */

// Get the WordPress base URL - improved method
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];

// Try to detect WordPress root more reliably
$current_path = $_SERVER['REQUEST_URI'];
$wp_path = '';

// Look for common WordPress indicators
if (strpos($current_path, '/wp-content/') !== false) {
    $wp_path = substr($current_path, 0, strpos($current_path, '/wp-content/'));
} else {
    // Fallback method
    $wp_path = dirname(dirname(dirname($_SERVER['SCRIPT_NAME'])));
    if ($wp_path === '/') $wp_path = '';
}

$base_url = $protocol . '://' . $host . $wp_path;
$api_base = $base_url . '/wp-json/chatgabi/v1/opportunities';

// Debug information
$debug_info = array(
    'protocol' => $protocol,
    'host' => $host,
    'current_path' => $current_path,
    'wp_path' => $wp_path,
    'base_url' => $base_url,
    'api_base' => $api_base
);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI API Test Interface</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .tagline {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 40px;
        }
        .endpoint-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .endpoint-title {
            color: #34495e;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }
        .endpoint-url {
            background: #2c3e50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin-bottom: 15px;
            word-break: break-all;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .response-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
            display: none;
        }
        .loading {
            color: #f39c12;
            font-style: italic;
        }
        .error {
            color: #e74c3c;
        }
        .success {
            color: #27ae60;
        }
        .cache-hit {
            background: #27ae60;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 10px;
        }
        .cache-miss {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ChatGABI Opportunities API Test Interface</h1>
        <p class="tagline">Achieving General Africa Business Intelligence</p>

        <!-- Debug Information -->
        <div class="endpoint-section" style="background: #fff3cd; border-color: #ffeaa7;">
            <div class="endpoint-title">🔧 Debug Information</div>
            <div style="font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <strong>API Base URL:</strong> <?php echo htmlspecialchars($api_base); ?><br>
                <strong>Current Host:</strong> <?php echo htmlspecialchars($host); ?><br>
                <strong>WordPress Path:</strong> <?php echo htmlspecialchars($wp_path); ?><br>
                <button class="test-button" onclick="showDebugInfo()" style="margin-top: 10px;">Show Full Debug Info</button>
                <div id="debug-details" style="display: none; margin-top: 10px; background: white; padding: 10px; border-radius: 3px;">
                    <?php echo '<pre>' . htmlspecialchars(json_encode($debug_info, JSON_PRETTY_PRINT)) . '</pre>'; ?>
                </div>
            </div>
        </div>

        <!-- Health Check -->
        <div class="endpoint-section">
            <div class="endpoint-title">🏥 Health Check</div>
            <div class="endpoint-url"><?php echo $api_base; ?>/health</div>
            <button class="test-button" onclick="testEndpoint('/health', 'health')">Test Health Check</button>
            <div id="health-response" class="response-area"></div>
        </div>

        <!-- Statistics -->
        <div class="endpoint-section">
            <div class="endpoint-title">📊 Opportunity Statistics</div>
            <div class="endpoint-url"><?php echo $api_base; ?>/stats</div>
            <button class="test-button" onclick="testEndpoint('/stats', 'stats')">Get Statistics</button>
            <div id="stats-response" class="response-area"></div>
        </div>

        <!-- Countries -->
        <div class="endpoint-section">
            <div class="endpoint-title">🌍 Available Countries</div>
            <div class="endpoint-url"><?php echo $api_base; ?>/countries</div>
            <button class="test-button" onclick="testEndpoint('/countries', 'countries')">Get Countries</button>
            <div id="countries-response" class="response-area"></div>
        </div>

        <!-- Types -->
        <div class="endpoint-section">
            <div class="endpoint-title">🏷️ Opportunity Types</div>
            <div class="endpoint-url"><?php echo $api_base; ?>/types</div>
            <button class="test-button" onclick="testEndpoint('/types', 'types')">Get Types</button>
            <div id="types-response" class="response-area"></div>
        </div>

        <!-- Sectors -->
        <div class="endpoint-section">
            <div class="endpoint-title">🏢 Business Sectors</div>
            <div class="endpoint-url"><?php echo $api_base; ?>/sectors</div>
            <button class="test-button" onclick="testEndpoint('/sectors', 'sectors')">All Sectors</button>
            <button class="test-button" onclick="testEndpoint('/sectors?country=Ghana', 'sectors-ghana')">Ghana Sectors</button>
            <div id="sectors-response" class="response-area"></div>
            <div id="sectors-ghana-response" class="response-area"></div>
        </div>

        <!-- Opportunities -->
        <div class="endpoint-section">
            <div class="endpoint-title">💼 Business Opportunities</div>
            <div class="endpoint-url"><?php echo $api_base; ?>?[parameters]</div>
            <button class="test-button" onclick="testEndpoint('?limit=3', 'opps-all')">Latest 3 Opportunities</button>
            <button class="test-button" onclick="testEndpoint('?country=Ghana&limit=5', 'opps-ghana')">Ghana Opportunities</button>
            <button class="test-button" onclick="testEndpoint('?type=Grant&limit=5', 'opps-grants')">Grant Opportunities</button>
            <button class="test-button" onclick="testEndpoint('?search=tech&limit=5', 'opps-tech')">Tech Opportunities</button>
            <div id="opps-all-response" class="response-area"></div>
            <div id="opps-ghana-response" class="response-area"></div>
            <div id="opps-grants-response" class="response-area"></div>
            <div id="opps-tech-response" class="response-area"></div>
        </div>

        <!-- Performance Test -->
        <div class="endpoint-section">
            <div class="endpoint-title">⚡ Performance Test (Cache Demo)</div>
            <div class="endpoint-url">Multiple requests to test caching</div>
            <button class="test-button" onclick="performanceTest()">Run Performance Test</button>
            <div id="performance-response" class="response-area"></div>
        </div>
    </div>

    <script>
        const apiBase = '<?php echo $api_base; ?>';

        // Debug logging
        console.log('ChatGABI API Test Interface Loaded');
        console.log('API Base URL:', apiBase);

        // Show debug information
        function showDebugInfo() {
            const debugDiv = document.getElementById('debug-details');
            debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
        }

        // Enhanced test endpoint function with better error handling
        async function testEndpoint(endpoint, responseId) {
            console.log(`Testing endpoint: ${endpoint} with responseId: ${responseId}`);

            const responseDiv = document.getElementById(responseId + '-response');
            if (!responseDiv) {
                console.error(`Response div not found: ${responseId}-response`);
                alert(`Error: Response div not found: ${responseId}-response`);
                return;
            }

            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '<span class="loading">🔄 Loading...</span>';

            const fullUrl = apiBase + endpoint;
            console.log(`Making request to: ${fullUrl}`);

            try {
                const startTime = performance.now();

                // Add timeout to fetch request
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);

                console.log(`Response received:`, response);

                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const cacheStatus = response.headers.get('X-ChatGABI-Cache');
                const totalCount = response.headers.get('X-ChatGABI-Total');

                let statusInfo = `✅ Status: ${response.status} | ⏱️ Response Time: ${responseTime}ms`;
                if (cacheStatus) {
                    const cacheIcon = cacheStatus === 'HIT' ? '🎯' : '🔄';
                    statusInfo += ` | ${cacheIcon} Cache: ${cacheStatus}`;
                }
                if (totalCount) {
                    statusInfo += ` | 📊 Total: ${totalCount}`;
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`Expected JSON response, got: ${contentType}\n\nResponse: ${text.substring(0, 500)}...`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                responseDiv.innerHTML = `<div class="success">${statusInfo}</div>\n\n${JSON.stringify(data, null, 2)}`;

            } catch (error) {
                console.error('Request failed:', error);

                let errorMessage = error.message;
                if (error.name === 'AbortError') {
                    errorMessage = 'Request timed out (30 seconds)';
                } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = 'Network error - check if the API endpoint is accessible';
                }

                responseDiv.innerHTML = `<div class="error">❌ Error: ${errorMessage}</div>
                    <div style="margin-top: 10px; font-size: 11px; color: #999;">
                        🔗 Attempted URL: ${fullUrl}<br>
                        💡 Check browser console for more details
                    </div>`;
            }
        }

        async function performanceTest() {
            console.log('Starting performance test...');
            const responseDiv = document.getElementById('performance-response');
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '<span class="loading">🚀 Running performance test...</span>';

            const testEndpoint = '?country=Ghana&limit=5';
            const results = [];
            const fullUrl = apiBase + testEndpoint;

            try {
                console.log(`Performance test URL: ${fullUrl}`);

                // Run 5 requests to test caching
                for (let i = 1; i <= 5; i++) {
                    responseDiv.innerHTML = `<span class="loading">🚀 Running performance test... (${i}/5)</span>`;

                    const startTime = performance.now();

                    const response = await fetch(fullUrl, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                        }
                    });

                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);
                    const cacheStatus = response.headers.get('X-ChatGABI-Cache') || 'UNKNOWN';

                    console.log(`Request ${i}: ${responseTime}ms, Cache: ${cacheStatus}, Status: ${response.status}`);

                    results.push({
                        request: i,
                        responseTime: responseTime,
                        cacheStatus: cacheStatus,
                        status: response.status,
                        success: response.ok
                    });

                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                // Generate results
                let output = '🏁 Performance Test Results:\n\n';
                results.forEach(result => {
                    const statusIcon = result.success ? '✅' : '❌';
                    const cacheIcon = result.cacheStatus === 'HIT' ? '🎯' : '🔄';
                    output += `${statusIcon} Request ${result.request}: ${result.responseTime}ms ${cacheIcon} Cache: ${result.cacheStatus} (HTTP ${result.status})\n`;
                });

                const successfulRequests = results.filter(r => r.success);
                if (successfulRequests.length > 0) {
                    const avgTime = Math.round(successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length);
                    const cacheHits = results.filter(r => r.cacheStatus === 'HIT').length;
                    const cacheMisses = results.filter(r => r.cacheStatus === 'MISS').length;

                    output += `\n📊 Summary:\n`;
                    output += `• Average Response Time: ${avgTime}ms\n`;
                    output += `• Cache Hits: ${cacheHits}/${results.length}\n`;
                    output += `• Cache Misses: ${cacheMisses}/${results.length}\n`;
                    output += `• Success Rate: ${successfulRequests.length}/${results.length}\n`;
                    output += `\n💡 Note: First request should be MISS, subsequent requests should be HIT (cached).`;
                } else {
                    output += `\n❌ All requests failed. Check the API endpoint and network connectivity.`;
                }

                responseDiv.innerHTML = `<div class="success">🎉 Performance test completed!</div>\n\n${output}`;
                console.log('Performance test completed successfully');

            } catch (error) {
                console.error('Performance test failed:', error);
                responseDiv.innerHTML = `<div class="error">❌ Performance test failed: ${error.message}</div>
                    <div style="margin-top: 10px; font-size: 11px; color: #999;">
                        🔗 Test URL: ${fullUrl}<br>
                        💡 Check browser console for more details
                    </div>`;
            }
        }

        // Add page load event listener for additional debugging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');
            console.log('Available functions:', {
                testEndpoint: typeof testEndpoint,
                performanceTest: typeof performanceTest,
                showDebugInfo: typeof showDebugInfo
            });
        });
    </script>
</body>
</html>

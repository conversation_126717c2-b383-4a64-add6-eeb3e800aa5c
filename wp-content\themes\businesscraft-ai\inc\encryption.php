<?php
/**
 * Encryption utilities for BusinessCraft AI
 *
 * @package Business<PERSON>raft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Encrypt data using AES-256-CBC
 */
function businesscraft_ai_encrypt($data) {
    if (empty($data)) {
        return '';
    }

    $encryption_key = get_option('businesscraft_ai_encryption_key');
    
    if (empty($encryption_key)) {
        // Generate a new encryption key if none exists
        $encryption_key = wp_generate_password(32, false);
        update_option('businesscraft_ai_encryption_key', $encryption_key);
    }

    $cipher = 'AES-256-CBC';
    $iv_length = openssl_cipher_iv_length($cipher);
    $iv = openssl_random_pseudo_bytes($iv_length);
    
    $encrypted = openssl_encrypt($data, $cipher, $encryption_key, OPENSSL_RAW_DATA, $iv);
    
    if ($encrypted === false) {
        return false;
    }
    
    // Prepend IV to encrypted data
    return base64_encode($iv . $encrypted);
}

/**
 * Decrypt data using AES-256-CBC
 */
function businesscraft_ai_decrypt($encrypted_data) {
    if (empty($encrypted_data)) {
        return '';
    }

    $encryption_key = get_option('businesscraft_ai_encryption_key');
    
    if (empty($encryption_key)) {
        return false;
    }

    $data = base64_decode($encrypted_data);
    
    if ($data === false) {
        return false;
    }

    $cipher = 'AES-256-CBC';
    $iv_length = openssl_cipher_iv_length($cipher);
    
    if (strlen($data) < $iv_length) {
        return false;
    }
    
    $iv = substr($data, 0, $iv_length);
    $encrypted = substr($data, $iv_length);
    
    $decrypted = openssl_decrypt($encrypted, $cipher, $encryption_key, OPENSSL_RAW_DATA, $iv);
    
    return $decrypted;
}

/**
 * Hash sensitive data for indexing
 */
function businesscraft_ai_hash_for_index($data) {
    $salt = get_option('businesscraft_ai_hash_salt');
    
    if (empty($salt)) {
        $salt = wp_generate_password(16, false);
        update_option('businesscraft_ai_hash_salt', $salt);
    }
    
    return hash('sha256', $data . $salt);
}

/**
 * Encrypt user PII (Personally Identifiable Information)
 */
function businesscraft_ai_encrypt_pii($data) {
    return businesscraft_ai_encrypt($data);
}

/**
 * Decrypt user PII
 */
function businesscraft_ai_decrypt_pii($encrypted_data) {
    return businesscraft_ai_decrypt($encrypted_data);
}

/**
 * Securely delete encryption key (for user data deletion)
 */
function businesscraft_ai_delete_encryption_key() {
    delete_option('businesscraft_ai_encryption_key');
    delete_option('businesscraft_ai_hash_salt');
}

/**
 * Rotate encryption key
 */
function businesscraft_ai_rotate_encryption_key() {
    global $wpdb;
    
    $old_key = get_option('businesscraft_ai_encryption_key');
    $new_key = wp_generate_password(32, false);
    
    if (empty($old_key)) {
        update_option('businesscraft_ai_encryption_key', $new_key);
        return true;
    }
    
    // Get all encrypted data
    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $chat_logs = $wpdb->get_results("SELECT id, encrypted_user_message, encrypted_ai_response FROM {$chat_logs_table}");
    
    // Temporarily store old key
    $temp_old_key = $old_key;
    
    // Update to new key
    update_option('businesscraft_ai_encryption_key', $new_key);
    
    // Re-encrypt all data with new key
    foreach ($chat_logs as $log) {
        // Decrypt with old key
        update_option('businesscraft_ai_encryption_key', $temp_old_key);
        $user_message = businesscraft_ai_decrypt($log->encrypted_user_message);
        $ai_response = businesscraft_ai_decrypt($log->encrypted_ai_response);
        
        // Encrypt with new key
        update_option('businesscraft_ai_encryption_key', $new_key);
        $new_encrypted_user_message = businesscraft_ai_encrypt($user_message);
        $new_encrypted_ai_response = businesscraft_ai_encrypt($ai_response);
        
        // Update database
        $wpdb->update(
            $chat_logs_table,
            array(
                'encrypted_user_message' => $new_encrypted_user_message,
                'encrypted_ai_response' => $new_encrypted_ai_response,
            ),
            array('id' => $log->id),
            array('%s', '%s'),
            array('%d')
        );
    }
    
    return true;
}

/**
 * Validate encryption setup
 */
function businesscraft_ai_validate_encryption() {
    $test_data = 'BusinessCraft AI Test Data';
    $encrypted = businesscraft_ai_encrypt($test_data);
    
    if ($encrypted === false) {
        return new WP_Error('encryption_failed', 'Failed to encrypt test data');
    }
    
    $decrypted = businesscraft_ai_decrypt($encrypted);
    
    if ($decrypted !== $test_data) {
        return new WP_Error('decryption_failed', 'Failed to decrypt test data');
    }
    
    return true;
}

/**
 * Generate secure random token
 */
function businesscraft_ai_generate_secure_token($length = 32) {
    if (function_exists('random_bytes')) {
        return bin2hex(random_bytes($length / 2));
    } elseif (function_exists('openssl_random_pseudo_bytes')) {
        return bin2hex(openssl_random_pseudo_bytes($length / 2));
    } else {
        return wp_generate_password($length, false);
    }
}

/**
 * Secure comparison of strings (timing attack resistant)
 */
function businesscraft_ai_secure_compare($a, $b) {
    if (function_exists('hash_equals')) {
        return hash_equals($a, $b);
    }
    
    if (strlen($a) !== strlen($b)) {
        return false;
    }
    
    $result = 0;
    for ($i = 0; $i < strlen($a); $i++) {
        $result |= ord($a[$i]) ^ ord($b[$i]);
    }
    
    return $result === 0;
}

/**
 * Sanitize and validate encryption key
 */
function businesscraft_ai_validate_encryption_key($key) {
    if (empty($key)) {
        return false;
    }
    
    if (strlen($key) < 32) {
        return false;
    }
    
    // Check if key contains only valid characters
    if (!preg_match('/^[a-zA-Z0-9\/\+\=]+$/', $key)) {
        return false;
    }
    
    return true;
}

/**
 * Export encrypted data for user (GDPR compliance)
 */
function businesscraft_ai_export_user_data($user_id) {
    global $wpdb;
    
    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $credit_logs_table = $wpdb->prefix . 'businesscraft_ai_credit_logs';
    $transactions_table = $wpdb->prefix . 'businesscraft_ai_transactions';
    
    $export_data = array();
    
    // Export chat history
    $chat_logs = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT encrypted_user_message, encrypted_ai_response, language, model, tokens_used, credits_used, created_at 
             FROM {$chat_logs_table} 
             WHERE user_id = %d 
             ORDER BY created_at DESC",
            $user_id
        )
    );
    
    $export_data['chat_history'] = array();
    foreach ($chat_logs as $log) {
        $export_data['chat_history'][] = array(
            'user_message' => businesscraft_ai_decrypt($log->encrypted_user_message),
            'ai_response' => businesscraft_ai_decrypt($log->encrypted_ai_response),
            'language' => $log->language,
            'model' => $log->model,
            'tokens_used' => $log->tokens_used,
            'credits_used' => $log->credits_used,
            'created_at' => $log->created_at,
        );
    }
    
    // Export credit history
    $credit_logs = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT action, credits_amount, credits_before, credits_after, description, created_at 
             FROM {$credit_logs_table} 
             WHERE user_id = %d 
             ORDER BY created_at DESC",
            $user_id
        )
    );
    
    $export_data['credit_history'] = $credit_logs;
    
    // Export transaction history
    $transactions = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT reference, package, amount, currency, status, created_at 
             FROM {$transactions_table} 
             WHERE user_id = %d 
             ORDER BY created_at DESC",
            $user_id
        )
    );
    
    $export_data['transactions'] = $transactions;
    
    // Export user metadata
    $export_data['user_metadata'] = array(
        'credits' => get_user_meta($user_id, 'businesscraft_credits', true),
        'tier' => get_user_meta($user_id, 'businesscraft_ai_tier', true),
    );
    
    return $export_data;
}

/**
 * Check if encryption is properly configured
 */
function businesscraft_ai_is_encryption_configured() {
    $encryption_key = get_option('businesscraft_ai_encryption_key');
    
    if (empty($encryption_key)) {
        return false;
    }
    
    if (!businesscraft_ai_validate_encryption_key($encryption_key)) {
        return false;
    }
    
    // Test encryption/decryption
    $validation_result = businesscraft_ai_validate_encryption();
    
    return !is_wp_error($validation_result);
}

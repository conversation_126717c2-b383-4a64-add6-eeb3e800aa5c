<?php
/**
 * Test Enhancement Features Implementation
 * 
 * This script tests the implementation of the four enhancement features:
 * 1. AI Feedback Loops Integration
 * 2. Context Personalization
 * 3. Offline Support Implementation (PWA)
 * 4. Advanced Analytics Dashboard
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll include WordPress
    require_once('../../../wp-config.php');
}

/**
 * Test Enhancement Features
 */
function test_chatgabi_enhancement_features() {
    global $wpdb;
    
    echo "<h1>ChatGABI Enhancement Features Test</h1>\n";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>\n";
    
    // Test 1: Check if enhancement files exist
    echo "<div class='test-section'>\n";
    echo "<h2>1. File Existence Test</h2>\n";
    
    $enhancement_files = array(
        'AI Feedback Loops' => ABSPATH . 'wp-content/themes/businesscraft-ai/inc/ai-feedback-loops.php',
        'Context Personalization' => ABSPATH . 'wp-content/themes/businesscraft-ai/inc/context-personalization.php',
        'PWA Support' => ABSPATH . 'wp-content/themes/businesscraft-ai/inc/pwa-support.php',
        'User Analytics' => ABSPATH . 'wp-content/themes/businesscraft-ai/inc/user-analytics.php',
        'Manifest JSON' => ABSPATH . 'wp-content/themes/businesscraft-ai/manifest.json',
        'Service Worker' => ABSPATH . 'wp-content/themes/businesscraft-ai/sw.js'
    );
    
    foreach ($enhancement_files as $name => $file) {
        if (file_exists($file)) {
            echo "<span class='success'>✓ {$name}: File exists</span><br>\n";
        } else {
            echo "<span class='error'>✗ {$name}: File missing</span><br>\n";
        }
    }
    echo "</div>\n";
    
    // Test 2: Check JavaScript files
    echo "<div class='test-section'>\n";
    echo "<h2>2. JavaScript Files Test</h2>\n";
    
    $js_files = array(
        'Feedback Loops JS' => ABSPATH . 'wp-content/themes/businesscraft-ai/assets/js/feedback-loops.js',
        'Personalization JS' => ABSPATH . 'wp-content/themes/businesscraft-ai/assets/js/personalization.js',
        'Offline Queue JS' => ABSPATH . 'wp-content/themes/businesscraft-ai/assets/js/offline-queue.js',
        'Local Storage Manager JS' => ABSPATH . 'wp-content/themes/businesscraft-ai/assets/js/local-storage-manager.js',
        'Advanced Analytics JS' => ABSPATH . 'wp-content/themes/businesscraft-ai/assets/js/advanced-analytics.js'
    );
    
    foreach ($js_files as $name => $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo "<span class='success'>✓ {$name}: File exists ({$size} bytes)</span><br>\n";
        } else {
            echo "<span class='error'>✗ {$name}: File missing</span><br>\n";
        }
    }
    echo "</div>\n";
    
    // Test 3: Check database tables
    echo "<div class='test-section'>\n";
    echo "<h2>3. Database Tables Test</h2>\n";
    
    $enhancement_tables = array(
        'wp_chatgabi_feedback_patterns' => 'AI Feedback Loops - Patterns',
        'wp_chatgabi_ml_training_data' => 'AI Feedback Loops - ML Training',
        'wp_chatgabi_session_context' => 'Context Personalization - Sessions',
        'wp_chatgabi_personalized_recommendations' => 'Context Personalization - Recommendations',
        'wp_chatgabi_context_preferences' => 'Context Personalization - Preferences',
        'wp_chatgabi_user_analytics' => 'User Analytics - Summary',
        'wp_chatgabi_user_progress' => 'User Analytics - Progress',
        'wp_chatgabi_user_insights' => 'User Analytics - Insights'
    );
    
    echo "<table>\n";
    echo "<tr><th>Table Name</th><th>Description</th><th>Status</th><th>Row Count</th></tr>\n";
    
    foreach ($enhancement_tables as $table => $description) {
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
        if ($table_exists) {
            $row_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table}");
            echo "<tr><td>{$table}</td><td>{$description}</td><td class='success'>✓ Exists</td><td>{$row_count}</td></tr>\n";
        } else {
            echo "<tr><td>{$table}</td><td>{$description}</td><td class='error'>✗ Missing</td><td>-</td></tr>\n";
        }
    }
    echo "</table>\n";
    echo "</div>\n";
    
    // Test 4: Check WordPress hooks and functions
    echo "<div class='test-section'>\n";
    echo "<h2>4. WordPress Integration Test</h2>\n";
    
    $functions_to_check = array(
        'chatgabi_init_ai_feedback_loops' => 'AI Feedback Loops Init',
        'chatgabi_init_context_personalization' => 'Context Personalization Init',
        'chatgabi_init_pwa_support' => 'PWA Support Init',
        'chatgabi_init_user_analytics' => 'User Analytics Init'
    );
    
    foreach ($functions_to_check as $function => $description) {
        if (function_exists($function)) {
            echo "<span class='success'>✓ {$description}: Function exists</span><br>\n";
        } else {
            echo "<span class='error'>✗ {$description}: Function missing</span><br>\n";
        }
    }
    echo "</div>\n";
    
    // Test 5: Check AJAX endpoints
    echo "<div class='test-section'>\n";
    echo "<h2>5. AJAX Endpoints Test</h2>\n";
    
    $ajax_actions = array(
        'chatgabi_record_interaction_pattern' => 'Record Interaction Pattern',
        'chatgabi_get_personalized_suggestions' => 'Get Personalized Suggestions',
        'chatgabi_save_session_context' => 'Save Session Context',
        'chatgabi_get_user_analytics' => 'Get User Analytics',
        'chatgabi_get_progress_metrics' => 'Get Progress Metrics',
        'chatgabi_export_user_analytics' => 'Export User Analytics'
    );
    
    foreach ($ajax_actions as $action => $description) {
        if (has_action("wp_ajax_{$action}") || has_action("wp_ajax_nopriv_{$action}")) {
            echo "<span class='success'>✓ {$description}: AJAX handler registered</span><br>\n";
        } else {
            echo "<span class='warning'>⚠ {$description}: AJAX handler not found</span><br>\n";
        }
    }
    echo "</div>\n";
    
    // Test 6: Test PWA manifest validation
    echo "<div class='test-section'>\n";
    echo "<h2>6. PWA Manifest Validation</h2>\n";
    
    $manifest_file = ABSPATH . 'wp-content/themes/businesscraft-ai/manifest.json';
    if (file_exists($manifest_file)) {
        $manifest_content = file_get_contents($manifest_file);
        $manifest_data = json_decode($manifest_content, true);
        
        if ($manifest_data) {
            echo "<span class='success'>✓ Manifest JSON is valid</span><br>\n";
            
            $required_fields = array('name', 'short_name', 'start_url', 'display', 'icons');
            foreach ($required_fields as $field) {
                if (isset($manifest_data[$field])) {
                    echo "<span class='success'>✓ Required field '{$field}' present</span><br>\n";
                } else {
                    echo "<span class='error'>✗ Required field '{$field}' missing</span><br>\n";
                }
            }
            
            echo "<span class='info'>ℹ Icons count: " . count($manifest_data['icons'] ?? []) . "</span><br>\n";
            echo "<span class='info'>ℹ Shortcuts count: " . count($manifest_data['shortcuts'] ?? []) . "</span><br>\n";
        } else {
            echo "<span class='error'>✗ Manifest JSON is invalid</span><br>\n";
        }
    } else {
        echo "<span class='error'>✗ Manifest file not found</span><br>\n";
    }
    echo "</div>\n";
    
    // Test 7: Test sample data creation
    echo "<div class='test-section'>\n";
    echo "<h2>7. Sample Data Test</h2>\n";
    
    // Create sample user for testing
    $test_user_id = 1; // Assuming admin user exists
    
    if ($test_user_id) {
        echo "<span class='info'>ℹ Testing with user ID: {$test_user_id}</span><br>\n";
        
        // Test feedback loops
        if (function_exists('chatgabi_record_interaction_pattern')) {
            $pattern_result = chatgabi_record_interaction_pattern($test_user_id, 'test_interaction', array(
                'test_data' => 'sample_value',
                'timestamp' => time()
            ));
            
            if ($pattern_result) {
                echo "<span class='success'>✓ Interaction pattern recorded successfully</span><br>\n";
            } else {
                echo "<span class='error'>✗ Failed to record interaction pattern</span><br>\n";
            }
        }
        
        // Test personalization
        if (function_exists('chatgabi_save_session_context')) {
            $context_result = chatgabi_save_session_context($test_user_id, 'test_session_' . time(), array(
                'type' => 'test',
                'business_focus' => 'testing',
                'test_data' => true
            ));
            
            if ($context_result) {
                echo "<span class='success'>✓ Session context saved successfully</span><br>\n";
            } else {
                echo "<span class='error'>✗ Failed to save session context</span><br>\n";
            }
        }
        
        // Test analytics
        if (function_exists('chatgabi_update_user_metric')) {
            chatgabi_update_user_metric($test_user_id, 'test_metric', 1, array('test' => true));
            echo "<span class='success'>✓ User metric updated successfully</span><br>\n";
        }
    } else {
        echo "<span class='warning'>⚠ No test user available</span><br>\n";
    }
    echo "</div>\n";
    
    // Test 8: Performance check
    echo "<div class='test-section'>\n";
    echo "<h2>8. Performance Check</h2>\n";
    
    $start_time = microtime(true);
    
    // Simulate some operations
    for ($i = 0; $i < 100; $i++) {
        $test_data = array('iteration' => $i, 'data' => str_repeat('x', 100));
        $json_data = wp_json_encode($test_data);
        $decoded_data = json_decode($json_data, true);
    }
    
    $end_time = microtime(true);
    $execution_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
    
    echo "<span class='info'>ℹ JSON encoding/decoding test (100 iterations): {$execution_time}ms</span><br>\n";
    
    if ($execution_time < 100) {
        echo "<span class='success'>✓ Performance is good</span><br>\n";
    } elseif ($execution_time < 500) {
        echo "<span class='warning'>⚠ Performance is acceptable</span><br>\n";
    } else {
        echo "<span class='error'>✗ Performance may be slow</span><br>\n";
    }
    echo "</div>\n";
    
    // Summary
    echo "<div class='test-section'>\n";
    echo "<h2>9. Test Summary</h2>\n";
    echo "<p><strong>Enhancement Features Implementation Test Completed</strong></p>\n";
    echo "<p>Please review the results above to ensure all features are properly implemented.</p>\n";
    echo "<p>For browser-based testing, please:</p>\n";
    echo "<ul>\n";
    echo "<li>Open the WordPress dashboard and check for new analytics widgets</li>\n";
    echo "<li>Test the PWA installation prompt (requires HTTPS in production)</li>\n";
    echo "<li>Verify offline functionality by disabling network connection</li>\n";
    echo "<li>Check browser console for JavaScript errors</li>\n";
    echo "<li>Test feedback collection after AI interactions</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

// Run the test
test_chatgabi_enhancement_features();
?>

<?php
/**
 * Simple test for data loader functionality (without WordPress)
 */

// Define WordPress constants for testing
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Include the data loader functions
require_once(__DIR__ . '/inc/data-loader.php');

// Disable error logging to console for cleaner output
error_reporting(E_ERROR | E_PARSE);

echo "=== BusinessCraft AI Data Loader Simple Test ===\n\n";

// Test each country
$countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];

foreach ($countries as $country) {
    echo "Testing: {$country}\n";
    echo str_repeat('-', 30) . "\n";

    $data = load_business_dataset_by_country($country);

    if ($data === false) {
        echo "❌ Failed to load data for {$country}\n\n";
        continue;
    }

    echo "✅ Successfully loaded data for {$country}\n";

    // Validate structure
    if (isset($data['country']) && isset($data['sectors'])) {
        echo "📊 Country: {$data['country']}\n";
        echo "📈 Sectors found: " . count($data['sectors']) . "\n";

        // Show first sector as example
        if (!empty($data['sectors'])) {
            $first_sector = $data['sectors'][0];
            echo "🏢 First sector: {$first_sector['sector_name']}\n";
            echo "📝 Overview: " . substr($first_sector['overview'], 0, 100) . "...\n";

            // Check key conditions structure
            if (isset($first_sector['key_conditions'])) {
                $conditions = $first_sector['key_conditions'];
                echo "🔍 Key conditions available:\n";
                foreach (['regulatory_environment', 'market_size_and_growth', 'major_players'] as $key) {
                    if (isset($conditions[$key])) {
                        echo "   ✓ {$key}\n";
                    } else {
                        echo "   ✗ {$key} (missing)\n";
                    }
                }
            }
        }
    } else {
        echo "⚠️ Data structure validation failed for {$country}\n";
    }

    echo "\n";
}

// Test invalid country
echo "Testing: Invalid Country\n";
echo str_repeat('-', 30) . "\n";
$invalid_data = load_business_dataset_by_country('InvalidCountry');
if ($invalid_data === false) {
    echo "✅ Correctly rejected invalid country\n";
} else {
    echo "❌ Should have rejected invalid country\n";
}

echo "\n";

// Test helper functions
echo "Testing Helper Functions\n";
echo str_repeat('-', 30) . "\n";
$available_countries = get_available_dataset_countries();
echo "Available countries: " . implode(', ', $available_countries) . "\n";

foreach (['Ghana', 'InvalidCountry'] as $test_country) {
    $is_available = is_dataset_country_available($test_country);
    $status = $is_available ? '✅' : '❌';
    echo "{$status} {$test_country} availability: " . ($is_available ? 'Available' : 'Not Available') . "\n";
}

echo "\n=== Test Complete ===\n";

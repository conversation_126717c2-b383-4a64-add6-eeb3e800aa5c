/*! This file is auto-generated */
(()=>{"use strict";const t=window.wp.richText,e=window.wp.i18n,n=window.wp.blockEditor,o=window.wp.primitives,r=window.ReactJSXRuntime,i=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.<PERSON>,{d:"M14.7 11.3c1-.6 1.5-1.6 1.5-3 0-2.3-1.3-3.4-4-3.4H7v14h5.8c1.4 0 2.5-.3 3.3-1 .8-.7 1.2-1.7 1.2-2.9.1-1.9-.8-3.1-2.6-3.7zm-5.1-4h2.3c.6 0 1.1.1 1.4.4.3.3.5.7.5 1.2s-.2 1-.5 1.2c-.3.3-.8.4-1.4.4H9.6V7.3zm4.6 9c-.4.3-1 .4-1.7.4H9.6v-3.9h2.9c.7 0 1.3.2 1.7.5.4.3.6.8.6 1.5s-.2 1.2-.6 1.5z"})}),a="core/bold",s=(0,e.__)("Bold"),l={name:a,title:s,tagName:"strong",className:null,edit({isActive:e,value:o,onChange:l,onFocus:c}){function u(){l((0,t.toggleFormat)(o,{type:a,title:s}))}return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextShortcut,{type:"primary",character:"b",onUse:u}),(0,r.jsx)(n.RichTextToolbarButton,{name:"bold",icon:i,title:s,onClick:function(){l((0,t.toggleFormat)(o,{type:a})),c()},isActive:e,shortcutType:"primary",shortcutCharacter:"b"}),(0,r.jsx)(n.__unstableRichTextInputEvent,{inputType:"formatBold",onInput:u})]})}},c=(0,r.jsx)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(o.Path,{d:"M20.8 10.7l-4.3-4.3-1.1 1.1 4.3 4.3c.1.1.1.3 0 .4l-4.3 4.3 1.1 1.1 4.3-4.3c.7-.8.7-1.9 0-2.6zM4.2 11.8l4.3-4.3-1-1-4.3 4.3c-.7.7-.7 1.8 0 2.5l4.3 4.3 1.1-1.1-4.3-4.3c-.2-.1-.2-.3-.1-.4z"})}),u="core/code",h=(0,e.__)("Inline code"),m={name:u,title:h,tagName:"code",className:null,__unstableInputRule(e){const{start:n,text:o}=e;if("`"!==o[n-1])return e;if(n-2<0)return e;const r=o.lastIndexOf("`",n-2);if(-1===r)return e;const i=r,a=n-2;return i===a?e:(e=(0,t.remove)(e,i,i+1),e=(0,t.remove)(e,a,a+1),e=(0,t.applyFormat)(e,{type:u},i,a))},edit({value:e,onChange:o,onFocus:i,isActive:a}){function s(){o((0,t.toggleFormat)(e,{type:u,title:h})),i()}return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextShortcut,{type:"access",character:"x",onUse:s}),(0,r.jsx)(n.RichTextToolbarButton,{icon:c,title:h,onClick:s,isActive:a,role:"menuitemcheckbox"})]})}},p=window.wp.components,d=window.wp.element,g=["image"],x="core/image",v=(0,e.__)("Inline image"),f={name:x,title:v,keywords:[(0,e.__)("photo"),(0,e.__)("media")],object:!0,tagName:"img",className:null,attributes:{className:"class",style:"style",url:"src",alt:"alt"},edit:function({value:e,onChange:o,onFocus:i,isObjectActive:a,activeObjectAttributes:s,contentRef:l}){return(0,r.jsxs)(n.MediaUploadCheck,{children:[(0,r.jsx)(n.MediaUpload,{allowedTypes:g,onSelect:({id:n,url:r,alt:a,width:s})=>{o((0,t.insertObject)(e,{type:x,attributes:{className:`wp-image-${n}`,style:`width: ${Math.min(s,150)}px;`,url:r,alt:a}})),i()},render:({open:t})=>(0,r.jsx)(n.RichTextToolbarButton,{icon:(0,r.jsx)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(p.Path,{d:"M4 18.5h16V17H4v1.5zM16 13v1.5h4V13h-4zM5.1 15h7.8c.6 0 1.1-.5 1.1-1.1V6.1c0-.6-.5-1.1-1.1-1.1H5.1C4.5 5 4 5.5 4 6.1v7.8c0 .6.5 1.1 1.1 1.1zm.4-8.5h7V10l-1-1c-.3-.3-.8-.3-1 0l-1.6 1.5-1.2-.7c-.3-.2-.6-.2-.9 0l-1.3 1V6.5zm0 6.1l1.8-1.3 1.3.8c.3.2.7.2.9-.1l1.5-1.4 1.5 1.4v1.5h-7v-.9z"})}),title:v,onClick:t,isActive:a})}),a&&(0,r.jsx)(b,{value:e,onChange:o,activeObjectAttributes:s,contentRef:l})]})}};function b({value:n,onChange:o,activeObjectAttributes:i,contentRef:a}){const{style:s,alt:l}=i,c=s?.replace(/\D/g,""),[u,h]=(0,d.useState)(c),[m,g]=(0,d.useState)(l),v=u!==c||m!==l,b=(0,t.useAnchor)({editableContentElement:a.current,settings:f});return(0,r.jsx)(p.Popover,{placement:"bottom",focusOnMount:!1,anchor:b,className:"block-editor-format-toolbar__image-popover",children:(0,r.jsx)("form",{className:"block-editor-format-toolbar__image-container-content",onSubmit:t=>{const e=n.replacements.slice();e[n.start]={type:x,attributes:{...i,style:c?`width: ${u}px;`:"",alt:m}},o({...n,replacements:e}),t.preventDefault()},children:(0,r.jsxs)(p.__experimentalVStack,{spacing:4,children:[(0,r.jsx)(p.__experimentalNumberControl,{__next40pxDefaultSize:!0,label:(0,e.__)("Width"),value:u,min:1,onChange:t=>{h(t)}}),(0,r.jsx)(p.TextareaControl,{label:(0,e.__)("Alternative text"),__nextHasNoMarginBottom:!0,value:m,onChange:t=>{g(t)},help:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.ExternalLink,{href:(0,e.__)("https://www.w3.org/WAI/tutorials/images/decision-tree/"),children:(0,e.__)("Describe the purpose of the image.")}),(0,r.jsx)("br",{}),(0,e.__)("Leave empty if decorative.")]})}),(0,r.jsx)(p.__experimentalHStack,{justify:"right",children:(0,r.jsx)(p.Button,{disabled:!v,accessibleWhenDisabled:!0,variant:"primary",type:"submit",size:"compact",children:(0,e.__)("Apply")})})]})})})}const w=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M12.5 5L10 19h1.9l2.5-14z"})}),_="core/italic",y=(0,e.__)("Italic"),j={name:_,title:y,tagName:"em",className:null,edit({isActive:e,value:o,onChange:i,onFocus:a}){function s(){i((0,t.toggleFormat)(o,{type:_,title:y}))}return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextShortcut,{type:"primary",character:"i",onUse:s}),(0,r.jsx)(n.RichTextToolbarButton,{name:"italic",icon:w,title:y,onClick:function(){i((0,t.toggleFormat)(o,{type:_})),a()},isActive:e,shortcutType:"primary",shortcutCharacter:"i"}),(0,r.jsx)(n.__unstableRichTextInputEvent,{inputType:"formatItalic",onInput:s})]})}},k=window.wp.url,C=window.wp.htmlEntities,S=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M10 17.389H8.444A5.194 5.194 0 1 1 8.444 7H10v1.5H8.444a3.694 3.694 0 0 0 0 7.389H10v1.5ZM14 7h1.556a5.194 5.194 0 0 1 0 10.39H14v-1.5h1.556a3.694 3.694 0 0 0 0-7.39H14V7Zm-4.5 6h5v-1.5h-5V13Z"})}),T=window.wp.a11y,A=window.wp.data;function F(t){if(!t)return!1;const e=t.trim();if(!e)return!1;if(/^\S+:/.test(e)){const t=(0,k.getProtocol)(e);if(!(0,k.isValidProtocol)(t))return!1;if(t.startsWith("http")&&!/^https?:\/\/[^\/\s]/i.test(e))return!1;const n=(0,k.getAuthority)(e);if(!(0,k.isValidAuthority)(n))return!1;const o=(0,k.getPath)(e);if(o&&!(0,k.isValidPath)(o))return!1;const r=(0,k.getQueryString)(e);if(r&&!(0,k.isValidQueryString)(r))return!1;const i=(0,k.getFragment)(e);if(i&&!(0,k.isValidFragment)(i))return!1}return!(e.startsWith("#")&&!(0,k.isValidFragment)(e))}function N(t,e,n=t.start,o=t.end){const r={start:null,end:null},{formats:i}=t;let a,s;if(!i?.length)return r;const l=i.slice(),c=l[n]?.find((({type:t})=>t===e.type)),u=l[o]?.find((({type:t})=>t===e.type)),h=l[o-1]?.find((({type:t})=>t===e.type));if(c)a=c,s=n;else if(u)a=u,s=o;else{if(!h)return r;a=h,s=o-1}const m=l[s].indexOf(a),p=[l,s,a,m];return{start:n=(n=M(...p))<0?0:n,end:o=P(...p)}}function R(t,e,n,o,r){let i=e;const a={forwards:1,backwards:-1}[r]||1,s=-1*a;for(;t[i]&&t[i][o]===n;)i+=a;return i+=s,i}const V=(t,...e)=>(...n)=>t(...n,...e),M=V(R,"backwards"),P=V(R,"forwards"),B=[...n.LinkControl.DEFAULT_LINK_SETTINGS,{id:"nofollow",title:(0,e.__)("Mark as nofollow")}];const z=function({isActive:o,activeAttributes:i,value:a,onChange:s,onFocusOutside:l,stopAddingLink:c,contentRef:u,focusOnMount:h}){const m=function(e,n){let o=e.start,r=e.end;if(n){const t=N(e,{type:"core/link"});o=t.start,r=t.end+1}return(0,t.slice)(e,o,r)}(a,o).text,{selectionChange:g}=(0,A.useDispatch)(n.store),{createPageEntity:x,userCanCreatePages:v,selectionStart:f}=(0,A.useSelect)((t=>{const{getSettings:e,getSelectionStart:o}=t(n.store),r=e();return{createPageEntity:r.__experimentalCreatePageEntity,userCanCreatePages:r.__experimentalUserCanCreatePages,selectionStart:o()}}),[]),b=(0,d.useMemo)((()=>({url:i.url,type:i.type,id:i.id,opensInNewTab:"_blank"===i.target,nofollow:i.rel?.includes("nofollow"),title:m})),[i.id,i.rel,i.target,i.type,i.url,m]),w=(0,t.useAnchor)({editableContentElement:u.current,settings:{...E,isActive:o}});return(0,r.jsx)(p.Popover,{anchor:w,animate:!1,onClose:c,onFocusOutside:l,placement:"bottom",offset:8,shift:!0,focusOnMount:h,constrainTabbing:!0,children:(0,r.jsx)(n.LinkControl,{value:b,onChange:function(n){const r=b?.url,i=!r;n={...b,...n};const l=(0,k.prependHTTP)(n.url),u=function({url:t,type:e,id:n,opensInNewWindow:o,nofollow:r}){const i={type:"core/link",attributes:{url:t}};return e&&(i.attributes.type=e),n&&(i.attributes.id=n),o&&(i.attributes.target="_blank",i.attributes.rel=i.attributes.rel?i.attributes.rel+" noreferrer noopener":"noreferrer noopener"),r&&(i.attributes.rel=i.attributes.rel?i.attributes.rel+" nofollow":"nofollow"),i}({url:l,type:n.type,id:void 0!==n.id&&null!==n.id?String(n.id):void 0,opensInNewWindow:n.opensInNewTab,nofollow:n.nofollow}),h=n.title||l;let p;if((0,t.isCollapsed)(a)&&!o){const e=(0,t.insert)(a,h);return p=(0,t.applyFormat)(e,u,a.start,a.start+h.length),s(p),c(),void g({clientId:f.clientId,identifier:f.attributeKey,start:a.start+h.length+1})}if(h===m)p=(0,t.applyFormat)(a,u);else{p=(0,t.create)({text:h}),p=(0,t.applyFormat)(p,u,0,h.length);const e=N(a,{type:"core/link"}),[n,o]=(0,t.split)(a,e.start,e.start),r=(0,t.replace)(o,m,p);p=(0,t.concat)(n,r)}s(p),i||c(),F(l)?o?(0,T.speak)((0,e.__)("Link edited."),"assertive"):(0,T.speak)((0,e.__)("Link inserted."),"assertive"):(0,T.speak)((0,e.__)("Warning: the link has been inserted but may have errors. Please test it."),"assertive")},onRemove:function(){const n=(0,t.removeFormat)(a,"core/link");s(n),c(),(0,T.speak)((0,e.__)("Link removed."),"assertive")},hasRichPreviews:!0,createSuggestion:x&&async function(t){const e=await x({title:t,status:"draft"});return{id:e.id,type:e.type,title:e.title.rendered,url:e.link,kind:"post-type"}},withCreateSuggestion:v,createSuggestionButtonText:function(t){return(0,d.createInterpolateElement)((0,e.sprintf)((0,e.__)("Create page: <mark>%s</mark>"),t),{mark:(0,r.jsx)("mark",{})})},hasTextControl:!0,settings:B,showInitialSuggestions:!0,suggestionsQuery:{initialSuggestionsSearchOptions:{type:"post",subtype:"page",perPage:20}}})})},L="core/link",I=(0,e.__)("Link");const E={name:L,title:I,tagName:"a",className:null,attributes:{url:"href",type:"data-type",id:"data-id",_id:"id",target:"target",rel:"rel"},__unstablePasteRule(e,{html:n,plainText:o}){const r=(n||o).replace(/<[^>]+>/g,"").trim();if(!(0,k.isURL)(r)||!/^https?:/.test(r))return e;window.console.log("Created link:\n\n",r);const i={type:L,attributes:{url:(0,C.decodeEntities)(r)}};return(0,t.isCollapsed)(e)?(0,t.insert)(e,(0,t.applyFormat)((0,t.create)({text:o}),i,0,o.length)):(0,t.applyFormat)(e,i)},edit:function({isActive:o,activeAttributes:i,value:a,onChange:s,onFocus:l,contentRef:c}){const[u,h]=(0,d.useState)(!1),[m,p]=(0,d.useState)(null);function g(e){const n=(0,t.getTextContent)((0,t.slice)(a));!o&&n&&(0,k.isURL)(n)&&F(n)?s((0,t.applyFormat)(a,{type:L,attributes:{url:n}})):!o&&n&&(0,k.isEmail)(n)?s((0,t.applyFormat)(a,{type:L,attributes:{url:`mailto:${n}`}})):!o&&n&&(0,k.isPhoneNumber)(n)?s((0,t.applyFormat)(a,{type:L,attributes:{url:`tel:${n.replace(/\D/g,"")}`}})):(e&&p({el:e,action:null}),h(!0))}(0,d.useEffect)((()=>{o||h(!1)}),[o]),(0,d.useLayoutEffect)((()=>{const t=c.current;if(t)return t.addEventListener("click",e),()=>{t.removeEventListener("click",e)};function e(t){const e=t.target.closest("[contenteditable] a");e&&o&&(h(!0),p({el:e,action:"click"}))}}),[c,o]);const x=!("A"===m?.el?.tagName&&"click"===m?.action),v=!(0,t.isCollapsed)(a);return(0,r.jsxs)(r.Fragment,{children:[v&&(0,r.jsx)(n.RichTextShortcut,{type:"primary",character:"k",onUse:g}),(0,r.jsx)(n.RichTextShortcut,{type:"primaryShift",character:"k",onUse:function(){s((0,t.removeFormat)(a,L)),(0,T.speak)((0,e.__)("Link removed."),"assertive")}}),(0,r.jsx)(n.RichTextToolbarButton,{name:"link",icon:S,title:o?(0,e.__)("Link"):I,onClick:t=>{g(t.currentTarget)},isActive:o||u,shortcutType:"primary",shortcutCharacter:"k","aria-haspopup":"true","aria-expanded":u}),u&&(0,r.jsx)(z,{stopAddingLink:function(){h(!1),"BUTTON"===m?.el?.tagName?m.el.focus():l(),p(null)},onFocusOutside:function(){h(!1),p(null)},isActive:o,activeAttributes:i,value:a,onChange:s,contentRef:c,focusOnMount:!!x&&"firstElement"})]})}},H=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M9.1 9v-.5c0-.6.2-1.1.7-1.4.5-.3 1.2-.5 2-.5.7 0 1.4.1 2.1.3.7.2 1.4.5 2.1.9l.2-1.9c-.6-.3-1.2-.5-1.9-.7-.8-.1-1.6-.2-2.4-.2-1.5 0-2.7.3-3.6 1-.8.7-1.2 1.5-1.2 2.6V9h2zM20 12H4v1h8.3c.3.1.6.2.8.3.5.2.9.5 1.1.8.3.3.4.7.4 1.2 0 .7-.2 1.1-.8 1.5-.5.3-1.2.5-2.1.5-.8 0-1.6-.1-2.4-.3-.8-.2-1.5-.5-2.2-.8L7 18.1c.5.2 1.2.4 2 .6.8.2 1.6.3 2.4.3 1.7 0 3-.3 3.9-1 .9-.7 1.3-1.6 1.3-2.8 0-.9-.2-1.7-.7-2.2H20v-1z"})}),O="core/strikethrough",U=(0,e.__)("Strikethrough"),G={name:O,title:U,tagName:"s",className:null,edit({isActive:e,value:o,onChange:i,onFocus:a}){function s(){i((0,t.toggleFormat)(o,{type:O,title:U})),a()}return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextShortcut,{type:"access",character:"d",onUse:s}),(0,r.jsx)(n.RichTextToolbarButton,{icon:H,title:U,onClick:s,isActive:e,role:"menuitemcheckbox"})]})}},D="core/underline",W=(0,e.__)("Underline"),Z={name:D,title:W,tagName:"span",className:null,attributes:{style:"style"},edit({value:e,onChange:o}){const i=()=>{o((0,t.toggleFormat)(e,{type:D,attributes:{style:"text-decoration: underline;"},title:W}))};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextShortcut,{type:"primary",character:"u",onUse:i}),(0,r.jsx)(n.__unstableRichTextInputEvent,{inputType:"formatUnderline",onInput:i})]})}};const $=(0,d.forwardRef)((function({icon:t,size:e=24,...n},o){return(0,d.cloneElement)(t,{width:e,height:e,...n,ref:o})})),K=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M12.9 6h-2l-4 11h1.9l1.1-3h4.2l1.1 3h1.9L12.9 6zm-2.5 6.5l1.5-4.9 1.7 4.9h-3.2z"})}),Q=(0,r.jsx)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(o.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),J=window.wp.privateApis,{lock:X,unlock:q}=(0,J.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/format-library"),{Tabs:Y}=q(p.privateApis),tt=[{name:"color",title:(0,e.__)("Text")},{name:"backgroundColor",title:(0,e.__)("Background")}];function et(t=""){return t.split(";").reduce(((t,e)=>{if(e){const[n,o]=e.split(":");"color"===n&&(t.color=o),"background-color"===n&&o!==at&&(t.backgroundColor=o)}return t}),{})}function nt(t="",e){return t.split(" ").reduce(((t,o)=>{if(o.startsWith("has-")&&o.endsWith("-color")){const r=o.replace(/^has-/,"").replace(/-color$/,""),i=(0,n.getColorObjectByAttributeValues)(e,r);t.color=i.color}return t}),{})}function ot(e,n,o){const r=(0,t.getActiveFormat)(e,n);return r?{...et(r.attributes.style),...nt(r.attributes.class,o)}:{}}function rt({name:e,property:o,value:i,onChange:a}){const s=(0,A.useSelect)((t=>{var e;const{getSettings:o}=t(n.store);return null!==(e=o().colors)&&void 0!==e?e:[]}),[]),l=(0,d.useMemo)((()=>ot(i,e,s)),[e,i,s]);return(0,r.jsx)(n.ColorPalette,{value:l[o],onChange:r=>{a(function(e,o,r,i){const{color:a,backgroundColor:s}={...ot(e,o,r),...i};if(!a&&!s)return(0,t.removeFormat)(e,o);const l=[],c=[],u={};if(s?l.push(["background-color",s].join(":")):l.push(["background-color",at].join(":")),a){const t=(0,n.getColorObjectByColorValue)(r,a);t?c.push((0,n.getColorClassName)("color",t.slug)):l.push(["color",a].join(":"))}return l.length&&(u.style=l.join(";")),c.length&&(u.class=c.join(" ")),(0,t.applyFormat)(e,{type:o,attributes:u})}(i,e,s,{[o]:r}))},__experimentalIsRenderedInSidebar:!0})}function it({name:e,value:n,onChange:o,onClose:i,contentRef:a,isActive:s}){const l=(0,t.useAnchor)({editableContentElement:a.current,settings:{...ht,isActive:s}});return(0,r.jsx)(p.Popover,{onClose:i,className:"format-library__inline-color-popover",anchor:l,children:(0,r.jsxs)(Y,{children:[(0,r.jsx)(Y.TabList,{children:tt.map((t=>(0,r.jsx)(Y.Tab,{tabId:t.name,children:t.title},t.name)))}),tt.map((t=>(0,r.jsx)(Y.TabPanel,{tabId:t.name,focusable:!1,children:(0,r.jsx)(rt,{name:e,property:t.name,value:n,onChange:o})},t.name)))]})})}const at="rgba(0, 0, 0, 0)",st="core/text-color",lt=(0,e.__)("Highlight"),ct=[];function ut(t,e){const{ownerDocument:n}=t,{defaultView:o}=n,r=o.getComputedStyle(t).getPropertyValue(e);return"background-color"===e&&r===at&&t.parentElement?ut(t.parentElement,e):r}const ht={name:st,title:lt,tagName:"mark",className:"has-inline-color",attributes:{style:"style",class:"class"},edit:function({value:e,onChange:o,isActive:i,activeAttributes:a,contentRef:s}){const[l,c=ct]=(0,n.useSettings)("color.custom","color.palette"),[u,h]=(0,d.useState)(!1),m=(0,d.useMemo)((()=>function(t,{color:e,backgroundColor:n}){if(e||n)return{color:e||ut(t,"color"),backgroundColor:n===at?ut(t,"background-color"):n}}(s.current,ot(e,st,c))),[s,e,c]),p=!!c.length||l;return p||i?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextToolbarButton,{className:"format-library-text-color-button",isActive:i,icon:(0,r.jsx)($,{icon:Object.keys(a).length?K:Q,style:m}),title:lt,onClick:p?()=>h(!0):()=>o((0,t.removeFormat)(e,st)),role:"menuitemcheckbox"}),u&&(0,r.jsx)(it,{name:st,onClose:()=>h(!1),activeAttributes:a,value:e,onChange:o,contentRef:s,isActive:i})]}):null}},mt=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M16.9 18.3l.8-1.2c.4-.6.7-1.2.9-1.6.2-.4.3-.8.3-1.2 0-.3-.1-.7-.2-1-.1-.3-.4-.5-.6-.7-.3-.2-.6-.3-1-.3s-.8.1-1.1.2c-.3.1-.7.3-1 .6l.2 1.3c.3-.3.5-.5.8-.6s.6-.2.9-.2c.3 0 .*******.*******.2.7 0 .3-.1.5-.2.8-.1.3-.4.7-.8 1.3L15 19.4h4.3v-1.2h-2.4zM14.1 7.2h-2L9.5 11 6.9 7.2h-2l3.6 5.3L4.7 18h2l2.7-4 2.7 4h2l-3.8-5.5 3.8-5.3z"})}),pt="core/subscript",dt=(0,e.__)("Subscript"),gt={name:pt,title:dt,tagName:"sub",className:null,edit:({isActive:e,value:o,onChange:i,onFocus:a})=>(0,r.jsx)(n.RichTextToolbarButton,{icon:mt,title:dt,onClick:function(){i((0,t.toggleFormat)(o,{type:pt,title:dt})),a()},isActive:e,role:"menuitemcheckbox"})},xt=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M16.9 10.3l.8-1.3c.4-.6.7-1.2.9-1.6.2-.4.3-.8.3-1.2 0-.3-.1-.7-.2-1-.2-.2-.4-.4-.7-.6-.3-.2-.6-.3-1-.3s-.8.1-1.1.2c-.3.1-.7.3-1 .6l.1 1.3c.3-.3.5-.5.8-.6s.6-.2.9-.2c.3 0 .*******.*******.2.7 0 .3-.1.5-.2.8-.1.3-.4.7-.8 1.3l-1.8 2.8h4.3v-1.2h-2.2zm-2.8-3.1h-2L9.5 11 6.9 7.2h-2l3.6 5.3L4.7 18h2l2.7-4 2.7 4h2l-3.8-5.5 3.8-5.3z"})}),vt="core/superscript",ft=(0,e.__)("Superscript"),bt={name:vt,title:ft,tagName:"sup",className:null,edit:({isActive:e,value:o,onChange:i,onFocus:a})=>(0,r.jsx)(n.RichTextToolbarButton,{icon:xt,title:ft,onClick:function(){i((0,t.toggleFormat)(o,{type:vt,title:ft})),a()},isActive:e,role:"menuitemcheckbox"})},wt=(0,r.jsx)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(o.Path,{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"})}),_t="core/keyboard",yt=(0,e.__)("Keyboard input"),jt={name:_t,title:yt,tagName:"kbd",className:null,edit:({isActive:e,value:o,onChange:i,onFocus:a})=>(0,r.jsx)(n.RichTextToolbarButton,{icon:wt,title:yt,onClick:function(){i((0,t.toggleFormat)(o,{type:_t,title:yt})),a()},isActive:e,role:"menuitemcheckbox"})},kt=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"})}),Ct="core/unknown",St=(0,e.__)("Clear Unknown Formatting");const Tt={name:Ct,title:St,tagName:"*",className:null,edit({isActive:e,value:o,onChange:i,onFocus:a}){if(!e&&!function(e){return!(0,t.isCollapsed)(e)&&(0,t.slice)(e).formats.some((t=>t.some((t=>t.type===Ct))))}(o))return null;return(0,r.jsx)(n.RichTextToolbarButton,{name:"unknown",icon:kt,title:St,onClick:function(){i((0,t.removeFormat)(o,Ct)),a()},isActive:!0})}},At=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M17.5 10h-1.7l-3.7 10.5h1.7l.9-2.6h3.9l.9 2.6h1.7L17.5 10zm-2.2 6.3 1.4-4 1.4 4h-2.8zm-4.8-3.8c1.6-1.8 2.9-3.6 3.7-5.7H16V5.2h-5.8V3H8.8v2.2H3v1.5h9.6c-.7 1.6-1.8 3.1-3.1 4.6C8.6 10.2 7.8 9 7.2 8H5.6c.6 1.4 1.7 2.9 2.9 4.4l-2.4 2.4c-.3.4-.7.8-1.1 1.2l1 1 1.2-1.2c.8-.8 1.6-1.5 2.3-2.3.8.9 1.7 1.7 2.5 2.5l.6-1.5c-.7-.6-1.4-1.3-2.1-2z"})}),Ft="core/language",Nt=(0,e.__)("Language"),Rt={name:Ft,tagName:"bdo",className:null,edit:function({isActive:e,value:o,onChange:i,contentRef:a}){const[s,l]=(0,d.useState)(!1),c=()=>{l((t=>!t))};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.RichTextToolbarButton,{icon:At,label:Nt,title:Nt,onClick:()=>{e?i((0,t.removeFormat)(o,Ft)):c()},isActive:e,role:"menuitemcheckbox"}),s&&(0,r.jsx)(Vt,{value:o,onChange:i,onClose:c,contentRef:a})]})},title:Nt};function Vt({value:n,contentRef:o,onChange:i,onClose:a}){const s=(0,t.useAnchor)({editableContentElement:o.current,settings:Rt}),[l,c]=(0,d.useState)(""),[u,h]=(0,d.useState)("ltr");return(0,r.jsx)(p.Popover,{className:"block-editor-format-toolbar__language-popover",anchor:s,onClose:a,children:(0,r.jsxs)(p.__experimentalVStack,{as:"form",spacing:4,className:"block-editor-format-toolbar__language-container-content",onSubmit:e=>{e.preventDefault(),i((0,t.applyFormat)(n,{type:Ft,attributes:{lang:l,dir:u}})),a()},children:[(0,r.jsx)(p.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:Nt,value:l,onChange:t=>c(t),help:(0,e.__)('A valid language attribute, like "en" or "fr".')}),(0,r.jsx)(p.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,e.__)("Text direction"),value:u,options:[{label:(0,e.__)("Left to right"),value:"ltr"},{label:(0,e.__)("Right to left"),value:"rtl"}],onChange:t=>h(t)}),(0,r.jsx)(p.__experimentalHStack,{alignment:"right",children:(0,r.jsx)(p.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",text:(0,e.__)("Apply")})})]})})}const Mt=(0,e.__)("Non breaking space");[l,m,f,j,E,G,Z,ht,gt,bt,jt,Tt,Rt,{name:"core/non-breaking-space",title:Mt,tagName:"nbsp",className:null,edit:({value:e,onChange:o})=>(0,r.jsx)(n.RichTextShortcut,{type:"primaryShift",character:" ",onUse:function(){o((0,t.insert)(e," "))}})}].forEach((({name:e,...n})=>(0,t.registerFormatType)(e,n))),(window.wp=window.wp||{}).formatLibrary={}})();
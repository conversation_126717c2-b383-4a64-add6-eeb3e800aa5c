<?php
/**
 * Template Functions for ChatGABI AI
 * 
 * Handles all template-related functionality including:
 * - Template loading and management
 * - Multi-language template support
 * - Template caching
 * - Template generation
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get user's preferred template language
 * 
 * @param int $user_id User ID (optional)
 * @return string Language code
 */
function chatgabi_get_user_template_language($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return 'en';
    }
    
    // Check for template-specific language preference
    $template_language = get_user_meta($user_id, 'chatgabi_template_language', true);
    
    if (!$template_language) {
        // Fall back to general language preference
        $template_language = chatgabi_get_user_preferred_language($user_id);
    }
    
    return $template_language ?: 'en';
}

/**
 * Set user's template language preference
 * 
 * @param int $user_id User ID
 * @param string $language Language code
 * @return bool Success status
 */
function chatgabi_set_user_template_language($user_id, $language) {
    $supported_languages = chatgabi_get_supported_template_languages();
    
    if (!isset($supported_languages[$language])) {
        return false;
    }
    
    return update_user_meta($user_id, 'chatgabi_template_language', $language);
}

/**
 * Get supported template languages
 * 
 * @return array Array of supported languages with metadata
 */
function chatgabi_get_supported_template_languages() {
    return array(
        'en' => array(
            'name' => 'English',
            'native_name' => 'English',
            'countries' => array('Ghana', 'Kenya', 'Nigeria', 'South Africa'),
            'flag' => '🇬🇧'
        ),
        'tw' => array(
            'name' => 'Twi',
            'native_name' => 'Twi',
            'countries' => array('Ghana'),
            'flag' => '🇬🇭'
        ),
        'sw' => array(
            'name' => 'Swahili',
            'native_name' => 'Kiswahili',
            'countries' => array('Kenya'),
            'flag' => '🇰🇪'
        ),
        'yo' => array(
            'name' => 'Yoruba',
            'native_name' => 'Yorùbá',
            'countries' => array('Nigeria'),
            'flag' => '🇳🇬'
        ),
        'zu' => array(
            'name' => 'Zulu',
            'native_name' => 'isiZulu',
            'countries' => array('South Africa'),
            'flag' => '🇿🇦'
        )
    );
}

/**
 * Generic template loader for all template types
 */
function chatgabi_load_template_by_type($template_type, $language) {
    $cached_templates = chatgabi_get_cached_template($template_type, $language);
    if ($cached_templates) {
        return $cached_templates;
    }

    $template_file = CHATGABI_THEME_DIR . '/wp-content/datasets/templates/' . $language . '/' . $template_type . '_templates.json';

    if (!file_exists($template_file)) {
        $template_file = CHATGABI_THEME_DIR . '/wp-content/datasets/templates/en/' . $template_type . '_templates.json';
    }

    if (!file_exists($template_file)) {
        return false;
    }

    $template_content = file_get_contents($template_file);
    $templates = json_decode($template_content, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        return false;
    }

    chatgabi_cache_template($template_type, $language, $templates);
    return $templates;
}

/**
 * Build AI enhancement prompt for templates
 */
function chatgabi_build_enhancement_prompt($template_content, $user_profile, $user_industry, $user_country, $user_language, $enhancement_options) {
    // Get country context from African Context Engine
    $country_context = '';
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $context_engine = new BusinessCraft_African_Context_Engine();
        $country_data = $context_engine->get_country_context($user_country);
        $country_context = "Country Context for {$country_data['name']}: {$country_data['business_culture']}. {$country_data['market_characteristics']}.";
    }

    // Build enhancement prompt
    $prompt = "You are an AI assistant specializing in African business contexts. Please enhance the following business template for a {$user_profile} in the {$user_industry} industry in " . chatgabi_get_country_name($user_country) . ".\n\n";

    $prompt .= "Original Template:\n{$template_content}\n\n";

    $prompt .= "Enhancement Instructions:\n";

    if (!empty($enhancement_options['enhance_placeholders'])) {
        $prompt .= "- Replace generic placeholders with specific, relevant examples for {$user_industry} businesses in " . chatgabi_get_country_name($user_country) . "\n";
    }

    if (!empty($enhancement_options['add_context'])) {
        $prompt .= "- Add African market context and considerations specific to " . chatgabi_get_country_name($user_country) . "\n";
        $prompt .= "- {$country_context}\n";
    }

    if (!empty($enhancement_options['industry_specific'])) {
        $prompt .= "- Customize content specifically for the {$user_industry} industry\n";
        $prompt .= "- Include industry-specific terminology, challenges, and opportunities\n";
    }

    $prompt .= "\nPlease provide an enhanced version that:\n";
    $prompt .= "1. Maintains the original structure and intent\n";
    $prompt .= "2. Adds relevant African business context\n";
    $prompt .= "3. Uses specific examples appropriate for the user's profile\n";
    $prompt .= "4. Considers local market conditions and business practices\n";
    $prompt .= "5. Remains practical and actionable\n\n";

    if ($user_language !== 'en') {
        $prompt .= "Please provide the enhanced template in " . chatgabi_get_language_name($user_language) . " where appropriate, while keeping technical terms in English.\n\n";
    }

    return $prompt;
}

/**
 * Build AI suggestions prompt
 */
function chatgabi_build_suggestions_prompt($user_profile, $user_industry, $user_country, $user_language, $user_goals) {
    $prompt = "You are an AI assistant specializing in African business contexts. Based on the following user profile, suggest 5 relevant business templates that would be most valuable:\n\n";

    $prompt .= "User Profile:\n";
    $prompt .= "- Type: " . ($user_profile === 'sme' ? 'Small/Medium Business Owner' : 'Content Creator') . "\n";
    $prompt .= "- Industry: {$user_industry}\n";
    $prompt .= "- Country: " . chatgabi_get_country_name($user_country) . "\n";
    $prompt .= "- Language: " . chatgabi_get_language_name($user_language) . "\n";

    if (!empty($user_goals)) {
        $prompt .= "- Goals: " . implode(', ', $user_goals) . "\n";
    }

    $prompt .= "\nFor each suggested template, provide:\n";
    $prompt .= "1. Template Title\n";
    $prompt .= "2. Brief Description (2-3 sentences)\n";
    $prompt .= "3. Why it's relevant for this user\n";
    $prompt .= "4. Suggested category (Business Plans, Marketing Strategies, Financial Forecasts, or Operations)\n\n";

    $prompt .= "Focus on templates that address common challenges and opportunities for {$user_profile}s in the {$user_industry} industry in " . chatgabi_get_country_name($user_country) . ".\n\n";

    $prompt .= "Format your response as a JSON array with the following structure:\n";
    $prompt .= "[\n";
    $prompt .= "  {\n";
    $prompt .= "    \"title\": \"Template Title\",\n";
    $prompt .= "    \"description\": \"Brief description\",\n";
    $prompt .= "    \"relevance\": \"Why it's relevant\",\n";
    $prompt .= "    \"category\": \"Category name\",\n";
    $prompt .= "    \"priority\": \"high|medium|low\"\n";
    $prompt .= "  }\n";
    $prompt .= "]\n";

    return $prompt;
}

/**
 * Parse AI suggestions response
 */
function chatgabi_parse_ai_suggestions($ai_response) {
    // Try to extract JSON from the response
    $json_start = strpos($ai_response, '[');
    $json_end = strrpos($ai_response, ']');

    if ($json_start !== false && $json_end !== false) {
        $json_string = substr($ai_response, $json_start, $json_end - $json_start + 1);
        $suggestions = json_decode($json_string, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($suggestions)) {
            return $suggestions;
        }
    }

    // Fallback: parse text format
    $suggestions = array();
    $lines = explode("\n", $ai_response);
    $current_suggestion = array();

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        if (preg_match('/^\d+\.\s*(.+)/', $line, $matches)) {
            // New suggestion title
            if (!empty($current_suggestion)) {
                $suggestions[] = $current_suggestion;
            }
            $current_suggestion = array(
                'title' => $matches[1],
                'description' => '',
                'relevance' => '',
                'category' => 'Business Plans',
                'priority' => 'medium'
            );
        } elseif (!empty($current_suggestion)) {
            // Add to description
            if (empty($current_suggestion['description'])) {
                $current_suggestion['description'] = $line;
            } else {
                $current_suggestion['description'] .= ' ' . $line;
            }
        }
    }

    if (!empty($current_suggestion)) {
        $suggestions[] = $current_suggestion;
    }

    return $suggestions;
}

/**
 * Get country name from code
 * Note: Function moved to context-personalization.php to avoid conflicts
 * This function is now defined there with enhanced fallback behavior
 */
// Function removed to prevent redeclaration conflict
// Use the enhanced version in context-personalization.php





/**
 * Initialize template categories table if it doesn't exist
 */
function chatgabi_init_template_categories() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_template_categories';

    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

    if (!$table_exists) {
        // Create table
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            slug varchar(255) NOT NULL,
            description text,
            icon varchar(50) DEFAULT '📋',
            color varchar(7) DEFAULT '#007cba',
            sort_order int(11) DEFAULT 0,
            status enum('active','inactive') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY status (status),
            KEY sort_order (sort_order)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Insert default categories
        $default_categories = chatgabi_get_default_template_categories();
        foreach ($default_categories as $category) {
            $wpdb->insert(
                $table_name,
                array(
                    'name' => $category['name'],
                    'slug' => $category['slug'],
                    'description' => $category['description'],
                    'icon' => $category['icon'],
                    'color' => $category['color'],
                    'sort_order' => $category['sort_order'],
                    'status' => $category['status']
                ),
                array('%s', '%s', '%s', '%s', '%s', '%d', '%s')
            );
        }
    }
}

// Wrapper functions for backward compatibility
function chatgabi_load_business_plan_templates($language) {
    return chatgabi_load_template_by_type('business_plan', $language);
}

function chatgabi_load_marketing_strategy_templates($language) {
    return chatgabi_load_template_by_type('marketing_strategy', $language);
}

function chatgabi_load_financial_forecast_templates($language) {
    return chatgabi_load_template_by_type('financial_forecast', $language);
}

/**
 * Cache template data
 */
function chatgabi_cache_template($template_type, $language, $data) {
    $cache_key = "chatgabi_template_{$template_type}_{$language}";
    return set_transient($cache_key, $data, HOUR_IN_SECONDS);
}

/**
 * Get cached template data
 */
function chatgabi_get_cached_template($template_type, $language) {
    $cache_key = "chatgabi_template_{$template_type}_{$language}";
    return get_transient($cache_key);
}

/**
 * Clear template cache
 */
function chatgabi_clear_template_cache($template_type = null, $language = null) {
    if ($template_type && $language) {
        $cache_key = "chatgabi_template_{$template_type}_{$language}";
        return delete_transient($cache_key);
    }

    $template_types = array('business_plan', 'marketing_strategy', 'financial_forecast');
    $languages = array_keys(chatgabi_get_supported_template_languages());

    $success = true;
    foreach ($template_types as $type) {
        foreach ($languages as $lang) {
            $cache_key = "chatgabi_template_{$type}_{$lang}";
            if (!delete_transient($cache_key)) {
                $success = false;
            }
        }
    }

    return $success;
}

/**
 * Get template by ID and language
 * 
 * @param string $template_id Template ID
 * @param string $language Language code
 * @return array|false Template data or false on failure
 */
function chatgabi_get_template_by_id($template_id, $language) {
    $template_types = array('business_plan', 'marketing_strategy', 'financial_forecast');
    
    foreach ($template_types as $type) {
        $templates = chatgabi_load_templates_by_type($type, $language);
        if ($templates && isset($templates['templates'][$type][$template_id])) {
            return $templates['templates'][$type][$template_id];
        }
    }
    
    return false;
}

/**
 * Load templates by type (alias for backward compatibility)
 */
function chatgabi_load_templates_by_type($type, $language) {
    return chatgabi_load_template_by_type($type, $language);
}

/**
 * Get available template types
 * 
 * @return array Template types
 */
function chatgabi_get_template_types() {
    return array(
        'business_plan' => array(
            'name' => __('Business Plan', 'chatgabi'),
            'description' => __('Comprehensive business planning templates', 'chatgabi'),
            'icon' => '📋'
        ),
        'marketing_strategy' => array(
            'name' => __('Marketing Strategy', 'chatgabi'),
            'description' => __('Marketing and promotional strategy templates', 'chatgabi'),
            'icon' => '📈'
        ),
        'financial_forecast' => array(
            'name' => __('Financial Forecast', 'chatgabi'),
            'description' => __('Financial planning and forecasting templates', 'chatgabi'),
            'icon' => '💰'
        )
    );
}

/**
 * Translate business term
 * 
 * @param string $term English term
 * @param string $language Target language
 * @return string Translated term or original if not found
 */
function chatgabi_translate_business_term($term, $language) {
    $cultural_context = chatgabi_get_cultural_context($language);
    
    if (!$cultural_context || !isset($cultural_context['local_business_terms'])) {
        return $term;
    }
    
    $terms = $cultural_context['local_business_terms'];
    
    // Search for the term (case-insensitive)
    foreach ($terms as $english => $local) {
        if (strtolower($english) === strtolower($term)) {
            return $local;
        }
    }
    
    return $term;
}

/**
 * Get template statistics
 */
function chatgabi_get_template_statistics() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

    if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
        return array('total' => 0, 'by_type' => array(), 'by_language' => array());
    }

    $stats = array();
    $template_types = chatgabi_get_template_types();
    $languages = chatgabi_get_supported_template_languages();

    // Usage by template type
    $type_results = $wpdb->get_results("SELECT template_type, COUNT(*) as count FROM {$table_name} GROUP BY template_type");
    foreach ($template_types as $type => $type_info) {
        $stats['by_type'][$type] = array('name' => $type_info['name'], 'count' => 0);
    }
    foreach ($type_results as $result) {
        if (isset($stats['by_type'][$result->template_type])) {
            $stats['by_type'][$result->template_type]['count'] = (int) $result->count;
        }
    }

    // Usage by language
    $language_results = $wpdb->get_results("SELECT document_language, COUNT(*) as count FROM {$table_name} GROUP BY document_language");
    foreach ($languages as $code => $language) {
        $stats['by_language'][$code] = array('name' => $language['name'], 'count' => 0);
    }
    foreach ($language_results as $result) {
        if (isset($stats['by_language'][$result->document_language])) {
            $stats['by_language'][$result->document_language]['count'] = (int) $result->count;
        }
    }

    $stats['total'] = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    return $stats;
}

/**
 * Validate template data
 * 
 * @param array $template_data Template data to validate
 * @return array Validation result with success status and errors
 */
function chatgabi_validate_template_data($template_data) {
    $errors = array();
    $required_fields = array('template_type', 'business_idea', 'target_country', 'industry_sector', 'document_language');
    
    foreach ($required_fields as $field) {
        if (empty($template_data[$field])) {
            $errors[] = sprintf(__('Field %s is required', 'chatgabi'), $field);
        }
    }
    
    // Validate template type
    $valid_types = array_keys(chatgabi_get_template_types());
    if (!empty($template_data['template_type']) && !in_array($template_data['template_type'], $valid_types)) {
        $errors[] = __('Invalid template type', 'chatgabi');
    }
    
    // Validate language
    $valid_languages = array_keys(chatgabi_get_supported_template_languages());
    if (!empty($template_data['document_language']) && !in_array($template_data['document_language'], $valid_languages)) {
        $errors[] = __('Invalid document language', 'chatgabi');
    }
    
    return array(
        'success' => empty($errors),
        'errors' => $errors
    );
}

/**
 * Get default template categories (fallback data)
 */
function chatgabi_get_default_template_categories() {
    return array(
        array(
            'id' => 1,
            'name' => 'Business Plans',
            'slug' => 'business-plans',
            'description' => 'Comprehensive business planning templates for startups and established businesses',
            'icon' => '📋',
            'color' => '#007cba',
            'sort_order' => 1,
            'status' => 'active'
        ),
        array(
            'id' => 2,
            'name' => 'Marketing Strategies',
            'slug' => 'marketing-strategies',
            'description' => 'Marketing and promotional strategy templates for African markets',
            'icon' => '📈',
            'color' => '#28a745',
            'sort_order' => 2,
            'status' => 'active'
        ),
        array(
            'id' => 3,
            'name' => 'Financial Forecasts',
            'slug' => 'financial-forecasts',
            'description' => 'Financial planning and forecasting templates with local currency support',
            'icon' => '💰',
            'color' => '#ffc107',
            'sort_order' => 3,
            'status' => 'active'
        ),
        array(
            'id' => 4,
            'name' => 'Operations Management',
            'slug' => 'operations',
            'description' => 'Operational planning and management templates for efficient business operations',
            'icon' => '⚙️',
            'color' => '#6f42c1',
            'sort_order' => 4,
            'status' => 'active'
        ),
        array(
            'id' => 5,
            'name' => 'Legal & Compliance',
            'slug' => 'legal-compliance',
            'description' => 'Legal documentation and compliance templates for African jurisdictions',
            'icon' => '⚖️',
            'color' => '#dc3545',
            'sort_order' => 5,
            'status' => 'active'
        ),
        array(
            'id' => 6,
            'name' => 'Human Resources',
            'slug' => 'human-resources',
            'description' => 'HR policies, job descriptions, and employee management templates',
            'icon' => '👥',
            'color' => '#17a2b8',
            'sort_order' => 6,
            'status' => 'active'
        )
    );
}

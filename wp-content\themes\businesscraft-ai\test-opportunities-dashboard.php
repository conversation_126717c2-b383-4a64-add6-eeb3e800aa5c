<?php
/**
 * Test file for Opportunities Dashboard
 * 
 * This file tests the Live Opportunities functionality
 * Access via: /wp-content/themes/businesscraft-ai/test-opportunities-dashboard.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is logged in (for testing, we'll simulate a user)
if (!is_user_logged_in()) {
    echo "<h1>Test: Live Opportunities Dashboard</h1>";
    echo "<p><strong>Note:</strong> This is a test page. In production, users must be logged in.</p>";
    echo "<p>Simulating user with Ghana/Technology preferences...</p>";
    
    // Simulate user preferences for testing
    $user_country = 'Ghana';
    $user_sector = 'Technology';
} else {
    $user_id = get_current_user_id();
    $user_country = get_user_meta($user_id, 'bcai_country', true) ?: 'Ghana';
    $user_sector = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: 'Technology';
}

echo "<h1>Live Opportunities Test</h1>";
echo "<p><strong>User Country:</strong> " . esc_html($user_country) . "</p>";
echo "<p><strong>User Sector:</strong> " . esc_html($user_sector) . "</p>";

// Test opportunity loading
echo "<h2>Testing Opportunity Loader Functions</h2>";

// Test 1: Check if functions exist
echo "<h3>1. Function Availability Check</h3>";
if (function_exists('load_opportunities_by_country_sector')) {
    echo "✅ load_opportunities_by_country_sector() function exists<br>";
} else {
    echo "❌ load_opportunities_by_country_sector() function NOT found<br>";
}

if (function_exists('get_available_opportunity_countries')) {
    echo "✅ get_available_opportunity_countries() function exists<br>";
} else {
    echo "❌ get_available_opportunity_countries() function NOT found<br>";
}

// Test 2: Load available countries
echo "<h3>2. Available Countries</h3>";
if (function_exists('get_available_opportunity_countries')) {
    $countries = get_available_opportunity_countries();
    if (!empty($countries)) {
        echo "✅ Found " . count($countries) . " countries: " . implode(', ', $countries) . "<br>";
    } else {
        echo "❌ No countries found<br>";
    }
} else {
    echo "❌ Function not available<br>";
}

// Test 3: Load opportunities for user's country and sector
echo "<h3>3. Load Opportunities for User's Preferences</h3>";
if (function_exists('load_opportunities_by_country_sector')) {
    $opportunities = load_opportunities_by_country_sector($user_country, $user_sector);
    
    if (!empty($opportunities)) {
        echo "✅ Found " . count($opportunities) . " opportunities for {$user_country} / {$user_sector}<br>";
        
        echo "<h4>Opportunity Details:</h4>";
        foreach ($opportunities as $index => $opportunity) {
            echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
            echo "<h5>" . esc_html($opportunity['title'] ?? 'No Title') . "</h5>";
            echo "<p><strong>Type:</strong> " . esc_html($opportunity['type'] ?? 'N/A') . "</p>";
            echo "<p><strong>Sector:</strong> " . esc_html($opportunity['sector'] ?? 'N/A') . "</p>";
            echo "<p><strong>Summary:</strong> " . esc_html(wp_trim_words($opportunity['summary'] ?? '', 20)) . "</p>";
            if (!empty($opportunity['deadline'])) {
                echo "<p><strong>Deadline:</strong> " . esc_html($opportunity['deadline']) . "</p>";
            }
            if (!empty($opportunity['amount'])) {
                echo "<p><strong>Amount:</strong> " . esc_html($opportunity['amount']) . "</p>";
            }
            if (!empty($opportunity['source'])) {
                echo "<p><strong>Source:</strong> <a href='" . esc_url($opportunity['source']) . "' target='_blank'>Apply Now</a></p>";
            }
            echo "</div>";
        }
    } else {
        echo "❌ No opportunities found for {$user_country} / {$user_sector}<br>";
        
        // Try loading general opportunities for the country
        echo "<h4>Trying general opportunities for {$user_country}:</h4>";
        $general_opportunities = load_opportunities_by_country_sector($user_country);
        if (!empty($general_opportunities)) {
            echo "✅ Found " . count($general_opportunities) . " general opportunities for {$user_country}<br>";
        } else {
            echo "❌ No general opportunities found for {$user_country}<br>";
        }
    }
} else {
    echo "❌ Function not available<br>";
}

// Test 4: Test opportunity file paths
echo "<h3>4. Opportunity File Check</h3>";
$opportunities_dir = WP_CONTENT_DIR . '/datasets/opportunities/';
echo "<p><strong>Opportunities Directory:</strong> " . $opportunities_dir . "</p>";

if (is_dir($opportunities_dir)) {
    echo "✅ Opportunities directory exists<br>";
    
    $files = glob($opportunities_dir . '*.json');
    if (!empty($files)) {
        echo "✅ Found " . count($files) . " JSON files:<br>";
        foreach ($files as $file) {
            $filename = basename($file);
            $filesize = filesize($file);
            echo "  - {$filename} (" . number_format($filesize) . " bytes)<br>";
            
            // Test if file is valid JSON
            $content = file_get_contents($file);
            $data = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                echo "    ✅ Valid JSON with " . count($data) . " opportunities<br>";
            } else {
                echo "    ❌ Invalid JSON: " . json_last_error_msg() . "<br>";
            }
        }
    } else {
        echo "❌ No JSON files found in opportunities directory<br>";
    }
} else {
    echo "❌ Opportunities directory does not exist<br>";
}

// Test 5: Template part test
echo "<h3>5. Template Part Test</h3>";
echo "<p>Testing if the dashboard opportunities template part can be loaded...</p>";

$template_part_path = get_template_directory() . '/template-parts/dashboard-opportunities.php';
if (file_exists($template_part_path)) {
    echo "✅ Template part exists: {$template_part_path}<br>";
    echo "<h4>Template Part Preview:</h4>";
    echo "<div style='border: 2px solid #007cba; padding: 20px; margin: 20px 0; background: #f0f8ff;'>";
    
    // Simulate user for template part
    if (!is_user_logged_in()) {
        // For testing, we'll just include the template part
        // In production, this would be called within WordPress context
        echo "<p><em>Template part would be rendered here with user context...</em></p>";
        echo "<p>The template part includes:</p>";
        echo "<ul>";
        echo "<li>Opportunity filters (Country, Type, Sector)</li>";
        echo "<li>Opportunity cards with details</li>";
        echo "<li>Modal for detailed view</li>";
        echo "<li>JavaScript for filtering and interactions</li>";
        echo "</ul>";
    } else {
        // If user is logged in, we can actually include the template part
        include $template_part_path;
    }
    
    echo "</div>";
} else {
    echo "❌ Template part not found: {$template_part_path}<br>";
}

echo "<h2>Test Summary</h2>";
echo "<p>This test verifies that:</p>";
echo "<ul>";
echo "<li>✅ Opportunity loader functions are available</li>";
echo "<li>✅ Opportunity data files exist and are valid</li>";
echo "<li>✅ Template parts are in place</li>";
echo "<li>✅ User preferences can be retrieved</li>";
echo "</ul>";

echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li>Create a page with the 'User Dashboard' template</li>";
echo "<li>Set user country and sector preferences</li>";
echo "<li>Navigate to the 'Live Opportunities' tab</li>";
echo "<li>Test filtering and modal functionality</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>Test completed at: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

h1 {
    border-bottom: 3px solid #007cba;
    padding-bottom: 10px;
}

h2 {
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
    margin-top: 30px;
}

h3 {
    color: #007cba;
    margin-top: 25px;
}

code {
    background: #f4f4f4;
    padding: 2px 5px;
    border-radius: 3px;
}

.success {
    color: #28a745;
}

.error {
    color: #dc3545;
}

ul, ol {
    margin-left: 20px;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 1px solid #ccc;
}
</style>

<?php
/**
 * API Integrations for BusinessCraft AI
 * 
 * Handles third-party API integrations including:
 * - African fintech platforms (Paystack, Flutterwave, M-Pesa)
 * - Business data providers
 * - Webhook support for automation
 * - External data sources
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize API integrations
 */
function businesscraft_ai_init_api_integrations() {
    // Add AJAX handlers
    add_action('wp_ajax_test_api_connection', 'businesscraft_ai_handle_test_api_connection');
    add_action('wp_ajax_sync_external_data', 'businesscraft_ai_handle_sync_external_data');
    add_action('wp_ajax_configure_webhook', 'businesscraft_ai_handle_configure_webhook');
    add_action('wp_ajax_trigger_automation', 'businesscraft_ai_handle_trigger_automation');
    
    // Add REST API endpoints
    add_action('rest_api_init', 'businesscraft_ai_register_api_integration_routes');
    
    // Add webhook endpoints
    add_action('init', 'businesscraft_ai_register_webhook_endpoints');
    
    // Schedule data synchronization
    add_action('businesscraft_ai_sync_external_data', 'businesscraft_ai_sync_all_external_data');
    if (!wp_next_scheduled('businesscraft_ai_sync_external_data')) {
        wp_schedule_event(time(), 'hourly', 'businesscraft_ai_sync_external_data');
    }
    
    // Create API integration tables
    add_action('init', 'businesscraft_ai_create_api_integration_tables');
}
add_action('init', 'businesscraft_ai_init_api_integrations');

/**
 * Register REST API routes for integrations
 */
function businesscraft_ai_register_api_integration_routes() {
    register_rest_route('businesscraft-ai/v1', '/integrations/fintech/(?P<provider>[a-zA-Z0-9-_]+)', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_fintech_integration',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        },
        'args' => array(
            'provider' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array('paystack', 'flutterwave', 'mpesa', 'yoco')
            )
        )
    ));
    
    register_rest_route('businesscraft-ai/v1', '/integrations/business-data', array(
        'methods' => array('GET', 'POST'),
        'callback' => 'businesscraft_ai_rest_business_data_integration',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
    
    register_rest_route('businesscraft-ai/v1', '/webhooks/(?P<provider>[a-zA-Z0-9-_]+)', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_webhook_handler',
        'permission_callback' => '__return_true' // Webhooks need to be publicly accessible
    ));
}

/**
 * African Fintech Integration Manager
 */
class BusinessCraft_AI_Fintech_Integration {
    
    private $supported_providers = array(
        'paystack' => array(
            'name' => 'Paystack',
            'countries' => array('NG', 'GH', 'ZA', 'KE'),
            'api_base' => 'https://api.paystack.co',
            'features' => array('payments', 'subscriptions', 'transfers', 'customers')
        ),
        'flutterwave' => array(
            'name' => 'Flutterwave',
            'countries' => array('NG', 'GH', 'KE', 'ZA'),
            'api_base' => 'https://api.flutterwave.com/v3',
            'features' => array('payments', 'transfers', 'bills', 'fx')
        ),
        'mpesa' => array(
            'name' => 'M-Pesa',
            'countries' => array('KE'),
            'api_base' => 'https://sandbox.safaricom.co.ke',
            'features' => array('payments', 'transfers', 'balance')
        ),
        'yoco' => array(
            'name' => 'Yoco',
            'countries' => array('ZA'),
            'api_base' => 'https://online.yoco.com/v1',
            'features' => array('payments', 'refunds', 'webhooks')
        )
    );
    
    /**
     * Initialize payment with provider
     */
    public function initialize_payment($provider, $amount, $currency, $customer_data, $metadata = array()) {
        if (!isset($this->supported_providers[$provider])) {
            return array(
                'success' => false,
                'message' => 'Unsupported payment provider'
            );
        }
        
        $provider_config = $this->supported_providers[$provider];
        
        switch ($provider) {
            case 'paystack':
                return $this->initialize_paystack_payment($amount, $currency, $customer_data, $metadata);
            case 'flutterwave':
                return $this->initialize_flutterwave_payment($amount, $currency, $customer_data, $metadata);
            case 'mpesa':
                return $this->initialize_mpesa_payment($amount, $customer_data, $metadata);
            case 'yoco':
                return $this->initialize_yoco_payment($amount, $currency, $customer_data, $metadata);
            default:
                return array(
                    'success' => false,
                    'message' => 'Provider not implemented'
                );
        }
    }
    
    /**
     * Initialize Paystack payment
     */
    private function initialize_paystack_payment($amount, $currency, $customer_data, $metadata) {
        $api_key = get_option('businesscraft_ai_paystack_secret_key');
        if (!$api_key) {
            return array(
                'success' => false,
                'message' => 'Paystack API key not configured'
            );
        }
        
        $payload = array(
            'amount' => $amount * 100, // Paystack expects amount in kobo/pesewas
            'currency' => $currency,
            'email' => $customer_data['email'],
            'metadata' => $metadata,
            'callback_url' => home_url('/payment-callback/paystack/')
        );
        
        $response = wp_remote_post('https://api.paystack.co/transaction/initialize', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($payload),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data['status']) {
            return array(
                'success' => true,
                'payment_url' => $data['data']['authorization_url'],
                'reference' => $data['data']['reference']
            );
        } else {
            return array(
                'success' => false,
                'message' => $data['message'] ?? 'Payment initialization failed'
            );
        }
    }
    
    /**
     * Verify payment status
     */
    public function verify_payment($provider, $reference) {
        switch ($provider) {
            case 'paystack':
                return $this->verify_paystack_payment($reference);
            case 'flutterwave':
                return $this->verify_flutterwave_payment($reference);
            case 'mpesa':
                return $this->verify_mpesa_payment($reference);
            case 'yoco':
                return $this->verify_yoco_payment($reference);
            default:
                return array(
                    'success' => false,
                    'message' => 'Provider not supported'
                );
        }
    }
    
    /**
     * Verify Paystack payment
     */
    private function verify_paystack_payment($reference) {
        $api_key = get_option('businesscraft_ai_paystack_secret_key');
        if (!$api_key) {
            return array(
                'success' => false,
                'message' => 'Paystack API key not configured'
            );
        }
        
        $response = wp_remote_get("https://api.paystack.co/transaction/verify/{$reference}", array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data['status'] && $data['data']['status'] === 'success') {
            return array(
                'success' => true,
                'amount' => $data['data']['amount'] / 100,
                'currency' => $data['data']['currency'],
                'customer' => $data['data']['customer'],
                'metadata' => $data['data']['metadata']
            );
        } else {
            return array(
                'success' => false,
                'message' => 'Payment verification failed'
            );
        }
    }
}

/**
 * Business Data Integration Manager
 */
class BusinessCraft_AI_Business_Data_Integration {
    
    private $data_sources = array(
        'company_registry' => array(
            'GH' => 'https://api.registrargeneral.gov.gh',
            'KE' => 'https://api.ecitizen.go.ke',
            'NG' => 'https://api.cac.gov.ng',
            'ZA' => 'https://api.cipc.co.za'
        ),
        'market_data' => array(
            'african_markets' => 'https://api.africanmarkets.com',
            'world_bank' => 'https://api.worldbank.org/v2',
            'imf_data' => 'https://api.imf.org'
        ),
        'economic_indicators' => array(
            'central_banks' => array(
                'GH' => 'https://api.bog.gov.gh',
                'KE' => 'https://api.centralbank.go.ke',
                'NG' => 'https://api.cbn.gov.ng',
                'ZA' => 'https://api.resbank.co.za'
            )
        )
    );
    
    /**
     * Fetch company information
     */
    public function fetch_company_info($company_name, $country) {
        if (!isset($this->data_sources['company_registry'][$country])) {
            return array(
                'success' => false,
                'message' => 'Company registry not available for this country'
            );
        }
        
        // This would integrate with actual company registry APIs
        // For now, return mock data structure
        return array(
            'success' => true,
            'data' => array(
                'company_name' => $company_name,
                'registration_number' => 'RC' . rand(100000, 999999),
                'status' => 'Active',
                'incorporation_date' => date('Y-m-d', strtotime('-' . rand(1, 10) . ' years')),
                'country' => $country,
                'business_type' => 'Private Limited Company'
            )
        );
    }
    
    /**
     * Fetch market data
     */
    public function fetch_market_data($sector, $country) {
        // This would integrate with actual market data APIs
        return array(
            'success' => true,
            'data' => array(
                'sector' => $sector,
                'country' => $country,
                'market_size' => rand(1000000, 10000000),
                'growth_rate' => rand(5, 25) / 10,
                'key_players' => array('Company A', 'Company B', 'Company C'),
                'trends' => array('Digital transformation', 'Mobile-first approach', 'Sustainability focus')
            )
        );
    }
}

/**
 * Webhook Handler
 */
function businesscraft_ai_handle_webhook($provider, $payload) {
    global $wpdb;
    
    // Log webhook
    $webhooks_table = $wpdb->prefix . 'chatgabi_webhooks';
    $wpdb->insert(
        $webhooks_table,
        array(
            'provider' => $provider,
            'payload' => json_encode($payload),
            'status' => 'received',
            'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%s')
    );
    
    $webhook_id = $wpdb->insert_id;
    
    // Process webhook based on provider
    switch ($provider) {
        case 'paystack':
            $result = businesscraft_ai_process_paystack_webhook($payload);
            break;
        case 'flutterwave':
            $result = businesscraft_ai_process_flutterwave_webhook($payload);
            break;
        default:
            $result = array('success' => false, 'message' => 'Unknown provider');
    }
    
    // Update webhook status
    $wpdb->update(
        $webhooks_table,
        array(
            'status' => $result['success'] ? 'processed' : 'failed',
            'response' => json_encode($result),
            'processed_at' => current_time('mysql')
        ),
        array('id' => $webhook_id),
        array('%s', '%s', '%s'),
        array('%d')
    );
    
    return $result;
}

/**
 * Create API integration tables
 */
function businesscraft_ai_create_api_integration_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // API connections table
    $connections_table = $wpdb->prefix . 'chatgabi_api_connections';
    $connections_sql = "CREATE TABLE IF NOT EXISTS {$connections_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        provider varchar(50) NOT NULL,
        connection_name varchar(255) NOT NULL,
        api_credentials longtext,
        status varchar(20) NOT NULL DEFAULT 'active',
        last_sync datetime NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY provider (provider),
        KEY status (status)
    ) {$charset_collate};";
    
    // Webhooks table
    $webhooks_table = $wpdb->prefix . 'chatgabi_webhooks';
    $webhooks_sql = "CREATE TABLE IF NOT EXISTS {$webhooks_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        provider varchar(50) NOT NULL,
        payload longtext NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'received',
        response longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        processed_at datetime NULL,
        PRIMARY KEY (id),
        KEY provider (provider),
        KEY status (status),
        KEY created_at (created_at)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($connections_sql);
    dbDelta($webhooks_sql);
}

// Initialize global instances
$GLOBALS['businesscraft_ai_fintech'] = new BusinessCraft_AI_Fintech_Integration();
$GLOBALS['businesscraft_ai_business_data'] = new BusinessCraft_AI_Business_Data_Integration();

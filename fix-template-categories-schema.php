<?php
/**
 * Fix Template Categories Database Schema
 * 
 * This script fixes the database schema mismatch for the wp_chatgabi_template_categories table
 * by adding the missing 'status' column if it doesn't exist.
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Template Categories Schema Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 5px; border-radius: 3px; font-family: monospace; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>

<h1>🔧 ChatGABI Template Categories Schema Fix</h1>

<?php
echo '<div class="info">Fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$all_passed = true;

// Step 1: Check if table exists
echo '<h2>📋 Step 1: Table Existence Check</h2>';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table;

if ($table_exists) {
    echo '<div class="success">✅ Table exists: ' . $categories_table . '</div>';
} else {
    echo '<div class="error">❌ Table does not exist: ' . $categories_table . '</div>';
    echo '<div class="info">The table will be created when the Templates system initializes.</div>';
    $all_passed = false;
}

// Step 2: Check current table structure
if ($table_exists) {
    echo '<h2>🔍 Step 2: Current Table Structure</h2>';
    
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$categories_table}");
    
    echo '<h3>Current Columns:</h3>';
    echo '<pre>';
    foreach ($columns as $column) {
        echo sprintf("%-20s %-20s %-10s %-10s %-20s %s\n", 
            $column->Field, 
            $column->Type, 
            $column->Null, 
            $column->Key, 
            $column->Default ?? 'NULL', 
            $column->Extra
        );
    }
    echo '</pre>';
    
    // Check if status column exists
    $has_status_column = false;
    foreach ($columns as $column) {
        if ($column->Field === 'status') {
            $has_status_column = true;
            break;
        }
    }
    
    if ($has_status_column) {
        echo '<div class="success">✅ Status column already exists</div>';
    } else {
        echo '<div class="warning">⚠️ Status column is missing - this is the cause of the database error</div>';
        
        // Step 3: Add missing status column
        echo '<h2>🔧 Step 3: Adding Missing Status Column</h2>';
        
        try {
            $alter_sql = "ALTER TABLE {$categories_table} 
                         ADD COLUMN status enum('active','inactive') DEFAULT 'active' 
                         AFTER sort_order,
                         ADD KEY status (status)";
            
            echo '<div class="info">Executing SQL: <code>' . $alter_sql . '</code></div>';
            
            $result = $wpdb->query($alter_sql);
            
            if ($result !== false) {
                echo '<div class="success">✅ Status column added successfully!</div>';
                
                // Update existing records to have 'active' status
                $update_sql = "UPDATE {$categories_table} SET status = 'active' WHERE status IS NULL";
                $update_result = $wpdb->query($update_sql);
                
                if ($update_result !== false) {
                    echo '<div class="success">✅ Existing records updated with active status</div>';
                } else {
                    echo '<div class="warning">⚠️ Could not update existing records: ' . $wpdb->last_error . '</div>';
                }
                
            } else {
                echo '<div class="error">❌ Failed to add status column: ' . $wpdb->last_error . '</div>';
                $all_passed = false;
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Exception while adding status column: ' . $e->getMessage() . '</div>';
            $all_passed = false;
        }
    }
}

// Step 4: Verify the fix
if ($table_exists) {
    echo '<h2>✅ Step 4: Verification</h2>';
    
    // Re-check table structure
    $columns_after = $wpdb->get_results("SHOW COLUMNS FROM {$categories_table}");
    
    $has_status_after = false;
    foreach ($columns_after as $column) {
        if ($column->Field === 'status') {
            $has_status_after = true;
            echo '<div class="success">✅ Status column confirmed: ' . $column->Type . ' (Default: ' . ($column->Default ?? 'NULL') . ')</div>';
            break;
        }
    }
    
    if (!$has_status_after) {
        echo '<div class="error">❌ Status column still missing after fix attempt</div>';
        $all_passed = false;
    }
    
    // Test the query that was failing
    echo '<h3>Testing the problematic query:</h3>';
    
    try {
        $test_query = "SELECT * FROM {$categories_table} WHERE status = 'active' ORDER BY sort_order ASC";
        echo '<div class="info">Testing query: <code>' . $test_query . '</code></div>';
        
        $test_result = $wpdb->get_results($test_query);
        
        if ($test_result !== false) {
            echo '<div class="success">✅ Query executed successfully! Found ' . count($test_result) . ' active categories</div>';
            
            if (count($test_result) > 0) {
                echo '<h4>Categories found:</h4>';
                echo '<ul>';
                foreach ($test_result as $category) {
                    echo '<li>' . esc_html($category->name) . ' (' . esc_html($category->slug) . ') - Status: ' . esc_html($category->status) . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<div class="warning">⚠️ No categories found. You may need to initialize default categories.</div>';
            }
            
        } else {
            echo '<div class="error">❌ Query still failing: ' . $wpdb->last_error . '</div>';
            $all_passed = false;
        }
        
    } catch (Exception $e) {
        echo '<div class="error">❌ Exception during query test: ' . $e->getMessage() . '</div>';
        $all_passed = false;
    }
}

// Step 5: Initialize categories if needed
if ($table_exists && $all_passed) {
    echo '<h2>🚀 Step 5: Category Initialization Check</h2>';
    
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
    
    if ($category_count == 0) {
        echo '<div class="warning">⚠️ No categories found. Initializing default categories...</div>';
        
        if (function_exists('chatgabi_create_default_template_categories')) {
            try {
                chatgabi_create_default_template_categories();
                
                $new_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
                if ($new_count > 0) {
                    echo '<div class="success">✅ Default categories created successfully! Added ' . $new_count . ' categories</div>';
                } else {
                    echo '<div class="warning">⚠️ Default categories function ran but no categories were added</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Error creating default categories: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="warning">⚠️ Default categories function not available</div>';
        }
    } else {
        echo '<div class="success">✅ Categories already exist: ' . $category_count . ' categories found</div>';
    }
}

// Final Results
echo '<h2>📊 Final Results</h2>';

if ($all_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 SCHEMA FIX COMPLETED SUCCESSFULLY!</h3>';
    echo '<p><strong>✅ Database schema issue resolved!</strong></p>';
    echo '<ul>';
    echo '<li>✅ Template categories table structure corrected</li>';
    echo '<li>✅ Status column added and configured</li>';
    echo '<li>✅ Problematic queries now working</li>';
    echo '<li>✅ Templates page should load without errors</li>';
    echo '</ul>';
    echo '</div>';
    
    echo '<div class="info">';
    echo '<h3>🚀 Next Steps:</h3>';
    echo '<ol>';
    echo '<li>Visit the <a href="' . home_url('/templates') . '" target="_blank">Templates Page</a> to verify it loads correctly</li>';
    echo '<li>Test template category filtering and display</li>';
    echo '<li>Verify all template functionality works properly</li>';
    echo '<li>Check for any remaining database errors in the logs</li>';
    echo '</ol>';
    echo '</div>';
    
} else {
    echo '<div class="error">';
    echo '<h3>❌ SCHEMA FIX INCOMPLETE</h3>';
    echo '<p>Some issues remain. Please review the errors above and take corrective action.</p>';
    echo '</div>';
}

echo '<hr>';
echo '<div class="info">Fix completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

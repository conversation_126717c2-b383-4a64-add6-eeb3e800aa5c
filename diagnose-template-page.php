<?php
/**
 * Diagnose Template Management Page Issues
 */

// Load WordPress
require_once('wp-load.php');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Template Management Page Diagnosis</h1>";

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "<p>❌ User not logged in. <a href='/wp-admin/'>Login to WordPress Admin</a></p>";
    exit;
}

echo "<p>✅ User logged in as: " . wp_get_current_user()->user_login . "</p>";

// Test the template management functions step by step
echo "<h2>Step-by-Step Function Testing</h2>";

// Step 1: Test template categories
echo "<h3>1. Testing chatgabi_get_template_categories()</h3>";
try {
    ob_start();
    $template_categories = chatgabi_get_template_categories();
    $output = ob_get_clean();
    
    if ($output) {
        echo "<p>⚠️ Function produced output: " . htmlspecialchars($output) . "</p>";
    }
    
    if (is_array($template_categories)) {
        echo "<p>✅ Template categories loaded successfully</p>";
        echo "<p>Categories found: " . count($template_categories) . "</p>";
        foreach ($template_categories as $key => $category) {
            echo "<p>- {$key}: {$category['name']}</p>";
        }
    } else {
        echo "<p>❌ Template categories not an array</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal Error: " . $e->getMessage() . "</p>";
}

// Step 2: Test user templates
echo "<h3>2. Testing chatgabi_get_user_templates()</h3>";
try {
    ob_start();
    $current_user_id = get_current_user_id() ?: 1; // Use admin user ID as fallback
    $user_templates = chatgabi_get_user_templates($current_user_id);
    $output = ob_get_clean();
    
    if ($output) {
        echo "<p>⚠️ Function produced output: " . htmlspecialchars($output) . "</p>";
    }
    
    if (is_array($user_templates)) {
        echo "<p>✅ User templates loaded successfully</p>";
        echo "<p>User templates count: " . count($user_templates) . "</p>";
    } else {
        echo "<p>❌ User templates not an array</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal Error: " . $e->getMessage() . "</p>";
}

// Step 3: Test template usage stats
echo "<h3>3. Testing chatgabi_get_template_usage_stats()</h3>";
try {
    ob_start();
    $template_stats = chatgabi_get_template_usage_stats();
    $output = ob_get_clean();
    
    if ($output) {
        echo "<p>⚠️ Function produced output: " . htmlspecialchars($output) . "</p>";
    }
    
    if (is_array($template_stats)) {
        echo "<p>✅ Template usage stats loaded successfully</p>";
        foreach ($template_stats as $key => $value) {
            echo "<p>- {$key}: {$value}</p>";
        }
    } else {
        echo "<p>❌ Template usage stats not an array</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal Error: " . $e->getMessage() . "</p>";
}

// Step 4: Test database table
echo "<h3>4. Testing Database Table</h3>";
try {
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';
    
    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "<p>✅ Table {$table_name} exists</p>";
        
        // Check table structure
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$table_name}");
        echo "<p>Table columns:</p><ul>";
        foreach ($columns as $column) {
            echo "<li>{$column->Field} ({$column->Type})</li>";
        }
        echo "</ul>";
        
        // Check record count
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        echo "<p>Records in table: {$count}</p>";
        
    } else {
        echo "<p>❌ Table {$table_name} does not exist</p>";
        
        // Try to create table
        echo "<p>Attempting to create table...</p>";
        $result = chatgabi_create_templates_table();
        
        if ($result) {
            echo "<p>✅ Table creation successful</p>";
        } else {
            echo "<p>❌ Table creation failed</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Database Exception: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Database Fatal Error: " . $e->getMessage() . "</p>";
}

// Step 5: Test the actual template page function
echo "<h3>5. Testing chatgabi_templates_page() Function</h3>";
try {
    ob_start();
    chatgabi_templates_page();
    $page_output = ob_get_clean();
    
    if (strlen($page_output) > 100) {
        echo "<p>✅ Template page function executed successfully</p>";
        echo "<p>Output length: " . strlen($page_output) . " characters</p>";
        echo "<p>First 200 characters:</p>";
        echo "<pre>" . htmlspecialchars(substr($page_output, 0, 200)) . "...</pre>";
    } else {
        echo "<p>❌ Template page function produced minimal output</p>";
        echo "<p>Full output:</p>";
        echo "<pre>" . htmlspecialchars($page_output) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p>❌ Template Page Exception: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Template Page Fatal Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Diagnosis Complete</h2>";
echo "<p>Check the results above to identify any issues with the template management system.</p>";
?>

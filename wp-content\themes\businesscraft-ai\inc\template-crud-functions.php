<?php
/**
 * Template CRUD Functions
 * Auto-generated by fix script
 */

function chatgabi_duplicate_template($template_id) {
    global $wpdb;
    $table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $template = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $template_id));
    if ($template) {
        unset($template->id);
        $template->title .= ' (Copy)';
        $template->created_at = current_time('mysql');
        return $wpdb->insert($table, (array)$template);
    }
    return false;
}


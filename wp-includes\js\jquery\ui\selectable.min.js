/*!
 * jQuery UI Selectable 1.13.3
 * https://jqueryui.com
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license.
 * https://jquery.org/license
 */
!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery","./mouse","../version","../widget"],e):e(jQuery)}(function(u){"use strict";return u.widget("ui.selectable",u.ui.mouse,{version:"1.13.3",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var s=this;this._addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){s.elementPos=u(s.element[0]).offset(),s.selectees=u(s.options.filter,s.element[0]),s._addClass(s.selectees,"ui-selectee"),s.selectees.each(function(){var e=u(this),t=e.offset(),t={left:t.left-s.elementPos.left,top:t.top-s.elementPos.top};u.data(this,"selectable-item",{element:this,$element:e,left:t.left,top:t.top,right:t.left+e.outerWidth(),bottom:t.top+e.outerHeight(),startselected:!1,selected:e.hasClass("ui-selected"),selecting:e.hasClass("ui-selecting"),unselecting:e.hasClass("ui-unselecting")})})},this.refresh(),this._mouseInit(),this.helper=u("<div>"),this._addClass(this.helper,"ui-selectable-helper")},_destroy:function(){this.selectees.removeData("selectable-item"),this._mouseDestroy()},_mouseStart:function(s){var l=this,e=this.options;this.opos=[s.pageX,s.pageY],this.elementPos=u(this.element[0]).offset(),this.options.disabled||(this.selectees=u(e.filter,this.element[0]),this._trigger("start",s),u(e.appendTo).append(this.helper),this.helper.css({left:s.pageX,top:s.pageY,width:0,height:0}),e.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var e=u.data(this,"selectable-item");e.startselected=!0,s.metaKey||s.ctrlKey||(l._removeClass(e.$element,"ui-selected"),e.selected=!1,l._addClass(e.$element,"ui-unselecting"),e.unselecting=!0,l._trigger("unselecting",s,{unselecting:e.element}))}),u(s.target).parents().addBack().each(function(){var e,t=u.data(this,"selectable-item");if(t)return e=!s.metaKey&&!s.ctrlKey||!t.$element.hasClass("ui-selected"),l._removeClass(t.$element,e?"ui-unselecting":"ui-selected")._addClass(t.$element,e?"ui-selecting":"ui-unselecting"),t.unselecting=!e,t.selecting=e,(t.selected=e)?l._trigger("selecting",s,{selecting:t.element}):l._trigger("unselecting",s,{unselecting:t.element}),!1}))},_mouseDrag:function(l){var e,i,n,c,a,r,o;if(this.dragged=!0,!this.options.disabled)return n=(i=this).options,c=this.opos[0],a=this.opos[1],(r=l.pageX)<c&&(e=r,r=c,c=e),(o=l.pageY)<a&&(e=o,o=a,a=e),this.helper.css({left:c,top:a,width:r-c,height:o-a}),this.selectees.each(function(){var e=u.data(this,"selectable-item"),t=!1,s={};e&&e.element!==i.element[0]&&(s.left=e.left+i.elementPos.left,s.right=e.right+i.elementPos.left,s.top=e.top+i.elementPos.top,s.bottom=e.bottom+i.elementPos.top,"touch"===n.tolerance?t=!(r<s.left||s.right<c||o<s.top||s.bottom<a):"fit"===n.tolerance&&(t=c<s.left&&s.right<r&&a<s.top&&s.bottom<o),t?(e.selected&&(i._removeClass(e.$element,"ui-selected"),e.selected=!1),e.unselecting&&(i._removeClass(e.$element,"ui-unselecting"),e.unselecting=!1),e.selecting||(i._addClass(e.$element,"ui-selecting"),e.selecting=!0,i._trigger("selecting",l,{selecting:e.element}))):(e.selecting&&((l.metaKey||l.ctrlKey)&&e.startselected?(i._removeClass(e.$element,"ui-selecting"),e.selecting=!1,i._addClass(e.$element,"ui-selected"),e.selected=!0):(i._removeClass(e.$element,"ui-selecting"),e.selecting=!1,e.startselected&&(i._addClass(e.$element,"ui-unselecting"),e.unselecting=!0),i._trigger("unselecting",l,{unselecting:e.element}))),!e.selected||l.metaKey||l.ctrlKey||e.startselected||(i._removeClass(e.$element,"ui-selected"),e.selected=!1,i._addClass(e.$element,"ui-unselecting"),e.unselecting=!0,i._trigger("unselecting",l,{unselecting:e.element}))))}),!1},_mouseStop:function(t){var s=this;return this.dragged=!1,u(".ui-unselecting",this.element[0]).each(function(){var e=u.data(this,"selectable-item");s._removeClass(e.$element,"ui-unselecting"),e.unselecting=!1,e.startselected=!1,s._trigger("unselected",t,{unselected:e.element})}),u(".ui-selecting",this.element[0]).each(function(){var e=u.data(this,"selectable-item");s._removeClass(e.$element,"ui-selecting")._addClass(e.$element,"ui-selected"),e.selecting=!1,e.selected=!0,e.startselected=!0,s._trigger("selected",t,{selected:e.element})}),this._trigger("stop",t),this.helper.remove(),!1}})});
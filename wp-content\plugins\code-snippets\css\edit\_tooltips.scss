
.editor-help-tooltip {
	cursor: help;
	padding: 0.3em 0.3em 0;
	display: inline-block;
	font-size: 10px;
	background: transparent !important;
}

.snippet-editor-help {
	position: absolute;
	right: 5px;
	top: 5px;

	&:hover .editor-help-text {
		visibility: visible;
		opacity: 1;
	}
}

.editor-help-text {
	visibility: hidden;
	background-color: #555;
	color: #fff;
	padding: 5px;
	border-radius: 6px;
	position: absolute;
	z-index: 99;
	top: 125%;
	right: 0;
	margin-right: -10px;
	opacity: 0;
	transition: opacity 0.3s;
	white-space: nowrap;
	font-size: small;

	&::after {
		content: "";
		position: absolute;
		bottom: 100%;
		right: 0;
		margin-right: 10px;
		border: 5px solid transparent;
		border-bottom-color: #555;
	}

	td:first-child {
		padding-right: 0.5em;
	}

	.mac-key {
		display: none;
	}

	&.platform-mac {
		.mac-key {
			display: inline;
		}

		.pc-key {
			display: none;
		}
	}
}

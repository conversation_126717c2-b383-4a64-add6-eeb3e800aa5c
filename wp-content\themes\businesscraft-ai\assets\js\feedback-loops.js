/**
 * AI Feedback Loops JavaScript for ChatGABI
 * 
 * Handles automated feedback collection, interaction pattern recording,
 * and integration with the existing chat interface.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Feedback loops manager
    const FeedbackLoops = {
        sessionId: null,
        interactionPatterns: [],
        feedbackQueue: [],
        isRecording: false,

        /**
         * Initialize feedback loops system
         */
        init: function() {
            this.sessionId = this.generateSessionId();
            this.bindEvents();
            this.startInteractionRecording();
            
            console.log('ChatGABI: AI Feedback Loops initialized');
        },

        /**
         * Generate unique session ID
         */
        generateSessionId: function() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        /**
         * Bind event listeners
         */
        bindEvents: function() {
            // Listen for chat response completion
            $(document).on('chatgabi:responseCompleted', this.handleResponseCompleted.bind(this));
            
            // Listen for user interactions
            $(document).on('click', '.chat-message', this.recordInteraction.bind(this));
            $(document).on('focus', '.chat-input', this.recordInteraction.bind(this));
            $(document).on('scroll', '.chat-container', this.recordInteraction.bind(this));
            
            // Listen for feedback submissions
            $(document).on('chatgabi:feedbackSubmitted', this.handleFeedbackSubmitted.bind(this));
            
            // Page visibility changes
            $(document).on('visibilitychange', this.handleVisibilityChange.bind(this));
        },

        /**
         * Start recording user interactions
         */
        startInteractionRecording: function() {
            this.isRecording = true;
            this.recordPageLoad();
        },

        /**
         * Record page load interaction
         */
        recordPageLoad: function() {
            const pattern = {
                type: 'page_load',
                timestamp: Date.now(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                screenResolution: screen.width + 'x' + screen.height,
                sessionId: this.sessionId
            };

            this.addInteractionPattern(pattern);
        },

        /**
         * Handle AI response completion
         */
        handleResponseCompleted: function(event, responseData) {
            const pattern = {
                type: 'ai_response_completed',
                timestamp: Date.now(),
                responseTime: responseData.responseTime || 0,
                tokenCount: responseData.tokensUsed || 0,
                responseLength: responseData.response ? responseData.response.length : 0,
                contextType: responseData.contextType || 'general',
                sessionId: this.sessionId
            };

            this.addInteractionPattern(pattern);
            
            // Trigger feedback interface after a delay
            setTimeout(() => {
                this.showFeedbackInterface(responseData);
            }, 2000);
        },

        /**
         * Record user interaction
         */
        recordInteraction: function(event) {
            if (!this.isRecording) return;

            const pattern = {
                type: 'user_interaction',
                action: event.type,
                element: event.target.tagName.toLowerCase(),
                elementClass: event.target.className,
                timestamp: Date.now(),
                sessionId: this.sessionId
            };

            this.addInteractionPattern(pattern);
        },

        /**
         * Add interaction pattern to queue
         */
        addInteractionPattern: function(pattern) {
            this.interactionPatterns.push(pattern);
            
            // Send patterns in batches
            if (this.interactionPatterns.length >= 5) {
                this.sendInteractionPatterns();
            }
        },

        /**
         * Send interaction patterns to server
         */
        sendInteractionPatterns: function() {
            if (this.interactionPatterns.length === 0) return;

            const patterns = [...this.interactionPatterns];
            this.interactionPatterns = [];

            $.ajax({
                url: chatgabiFeedback.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_record_interaction_pattern',
                    interaction_type: 'batch_patterns',
                    pattern_data: JSON.stringify(patterns),
                    session_id: this.sessionId,
                    nonce: chatgabiFeedback.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('ChatGABI: Interaction patterns recorded');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('ChatGABI: Failed to record interaction patterns:', error);
                    // Re-add patterns to queue for retry
                    this.interactionPatterns = patterns.concat(this.interactionPatterns);
                }.bind(this)
            });
        },

        /**
         * Show feedback interface
         */
        showFeedbackInterface: function(responseData) {
            // Check if feedback interface already exists
            if ($('.feedback-interface').length > 0) {
                return;
            }

            const feedbackHtml = this.generateFeedbackInterface(responseData);
            
            // Insert feedback interface after the last chat message
            $('.chat-messages .message:last').after(feedbackHtml);
            
            // Animate appearance
            $('.feedback-interface').hide().fadeIn(300);
            
            // Bind feedback events
            this.bindFeedbackEvents();
        },

        /**
         * Generate feedback interface HTML
         */
        generateFeedbackInterface: function(responseData) {
            return `
                <div class="feedback-interface" data-response-id="${responseData.responseId || ''}">
                    <div class="feedback-header">
                        <h4>How was this response?</h4>
                        <button class="feedback-close" aria-label="Close feedback">×</button>
                    </div>
                    
                    <div class="feedback-content">
                        <!-- Quick rating -->
                        <div class="quick-rating">
                            <button class="feedback-btn thumbs-up" data-rating="positive">
                                <span class="icon">👍</span>
                                <span class="text">Helpful</span>
                            </button>
                            <button class="feedback-btn thumbs-down" data-rating="negative">
                                <span class="icon">👎</span>
                                <span class="text">Not helpful</span>
                            </button>
                        </div>
                        
                        <!-- Detailed feedback (hidden initially) -->
                        <div class="detailed-feedback" style="display: none;">
                            <div class="rating-categories">
                                <div class="category">
                                    <label>Helpfulness:</label>
                                    <div class="star-rating" data-category="helpfulness">
                                        ${this.generateStarRating()}
                                    </div>
                                </div>
                                <div class="category">
                                    <label>Accuracy:</label>
                                    <div class="star-rating" data-category="accuracy">
                                        ${this.generateStarRating()}
                                    </div>
                                </div>
                                <div class="category">
                                    <label>Relevance:</label>
                                    <div class="star-rating" data-category="relevance">
                                        ${this.generateStarRating()}
                                    </div>
                                </div>
                                <div class="category">
                                    <label>Clarity:</label>
                                    <div class="star-rating" data-category="clarity">
                                        ${this.generateStarRating()}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="feedback-text">
                                <textarea placeholder="Tell us more about your experience..." rows="3"></textarea>
                            </div>
                            
                            <div class="feedback-actions">
                                <label class="training-consent">
                                    <input type="checkbox" name="training_consent">
                                    Allow this feedback to be used for AI training
                                </label>
                                <button class="submit-feedback">Submit Feedback</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Generate star rating HTML
         */
        generateStarRating: function() {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                stars += `<span class="star" data-rating="${i}">★</span>`;
            }
            return stars;
        },

        /**
         * Bind feedback interface events
         */
        bindFeedbackEvents: function() {
            // Quick rating buttons
            $('.feedback-btn').on('click', this.handleQuickRating.bind(this));
            
            // Star ratings
            $('.star').on('click', this.handleStarRating.bind(this));
            $('.star').on('mouseenter', this.highlightStars.bind(this));
            $('.star-rating').on('mouseleave', this.resetStarHighlight.bind(this));
            
            // Close button
            $('.feedback-close').on('click', this.closeFeedbackInterface.bind(this));
            
            // Submit button
            $('.submit-feedback').on('click', this.submitDetailedFeedback.bind(this));
        },

        /**
         * Handle quick rating (thumbs up/down)
         */
        handleQuickRating: function(event) {
            const $btn = $(event.currentTarget);
            const rating = $btn.data('rating');
            const $interface = $btn.closest('.feedback-interface');
            
            // Record quick feedback
            this.recordQuickFeedback(rating, $interface);
            
            // Show detailed feedback for negative ratings
            if (rating === 'negative') {
                $interface.find('.detailed-feedback').slideDown(300);
            } else {
                // Auto-submit positive feedback after delay
                setTimeout(() => {
                    this.closeFeedbackInterface();
                }, 1500);
            }
            
            // Update button states
            $('.feedback-btn').removeClass('selected');
            $btn.addClass('selected');
        },

        /**
         * Handle star rating clicks
         */
        handleStarRating: function(event) {
            const $star = $(event.currentTarget);
            const rating = $star.data('rating');
            const $container = $star.closest('.star-rating');
            const category = $container.data('category');
            
            // Update visual state
            $container.find('.star').removeClass('selected');
            $container.find('.star').each(function(index) {
                if (index < rating) {
                    $(this).addClass('selected');
                }
            });
            
            // Store rating
            $container.data('selected-rating', rating);
        },

        /**
         * Highlight stars on hover
         */
        highlightStars: function(event) {
            const $star = $(event.currentTarget);
            const rating = $star.data('rating');
            const $container = $star.closest('.star-rating');
            
            $container.find('.star').removeClass('highlight');
            $container.find('.star').each(function(index) {
                if (index < rating) {
                    $(this).addClass('highlight');
                }
            });
        },

        /**
         * Reset star highlight
         */
        resetStarHighlight: function(event) {
            const $container = $(event.currentTarget);
            $container.find('.star').removeClass('highlight');
        },

        /**
         * Record quick feedback
         */
        recordQuickFeedback: function(rating, $interface) {
            const responseId = $interface.data('response-id');
            
            const feedbackData = {
                type: 'quick_rating',
                rating: rating,
                responseId: responseId,
                timestamp: Date.now(),
                sessionId: this.sessionId
            };
            
            this.sendFeedbackData(feedbackData);
        },

        /**
         * Submit detailed feedback
         */
        submitDetailedFeedback: function(event) {
            const $interface = $(event.currentTarget).closest('.feedback-interface');
            const responseId = $interface.data('response-id');
            
            // Collect ratings
            const ratings = {};
            $interface.find('.star-rating').each(function() {
                const category = $(this).data('category');
                const rating = $(this).data('selected-rating') || 0;
                ratings[category] = rating;
            });
            
            // Collect feedback text
            const feedbackText = $interface.find('textarea').val();
            const trainingConsent = $interface.find('input[name="training_consent"]').is(':checked');
            
            const feedbackData = {
                type: 'detailed_feedback',
                responseId: responseId,
                ratings: ratings,
                feedbackText: feedbackText,
                trainingConsent: trainingConsent,
                timestamp: Date.now(),
                sessionId: this.sessionId
            };
            
            this.sendFeedbackData(feedbackData);
            this.closeFeedbackInterface();
        },

        /**
         * Send feedback data to server
         */
        sendFeedbackData: function(feedbackData) {
            $.ajax({
                url: chatgabiFeedback.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_record_interaction_pattern',
                    interaction_type: 'feedback_submission',
                    pattern_data: JSON.stringify(feedbackData),
                    session_id: this.sessionId,
                    nonce: chatgabiFeedback.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('ChatGABI: Feedback recorded successfully');
                        $(document).trigger('chatgabi:feedbackSubmitted', [feedbackData]);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('ChatGABI: Failed to record feedback:', error);
                }
            });
        },

        /**
         * Close feedback interface
         */
        closeFeedbackInterface: function() {
            $('.feedback-interface').fadeOut(300, function() {
                $(this).remove();
            });
        },

        /**
         * Handle feedback submission event
         */
        handleFeedbackSubmitted: function(event, feedbackData) {
            // Show thank you message
            this.showThankYouMessage();
            
            // Record feedback completion pattern
            const pattern = {
                type: 'feedback_completed',
                feedbackType: feedbackData.type,
                timestamp: Date.now(),
                sessionId: this.sessionId
            };
            
            this.addInteractionPattern(pattern);
        },

        /**
         * Show thank you message
         */
        showThankYouMessage: function() {
            const $message = $('<div class="feedback-thank-you">Thank you for your feedback!</div>');
            $('.chat-messages').append($message);
            
            $message.hide().fadeIn(300).delay(2000).fadeOut(300, function() {
                $(this).remove();
            });
        },

        /**
         * Handle page visibility changes
         */
        handleVisibilityChange: function() {
            if (document.hidden) {
                // Page hidden - send any pending patterns
                this.sendInteractionPatterns();
                this.isRecording = false;
            } else {
                // Page visible - resume recording
                this.isRecording = true;
            }
        },

        /**
         * Cleanup on page unload
         */
        cleanup: function() {
            this.sendInteractionPatterns();
            this.isRecording = false;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Check if feedback loops are enabled
        if (typeof chatgabiFeedback !== 'undefined' && chatgabiFeedback.showFeedbackInterface) {
            FeedbackLoops.init();
        }
    });

    // Cleanup on page unload
    $(window).on('beforeunload', function() {
        FeedbackLoops.cleanup();
    });

    // Expose to global scope for integration
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.FeedbackLoops = FeedbackLoops;

})(jQuery);

/**
 * Base Component Class
 * 
 * Foundation for all BusinessCraft AI frontend components
 * Provides event-driven architecture and lifecycle management
 */

class BaseComponent {
    constructor(element, options = {}) {
        this.element = element;
        this.options = { ...this.getDefaultOptions(), ...options };
        this.state = {};
        this.eventListeners = new Map();
        this.childComponents = new Map();
        this.isInitialized = false;
        this.isDestroyed = false;
        
        // Bind methods to maintain context
        this.handleEvent = this.handleEvent.bind(this);
        this.destroy = this.destroy.bind(this);
        
        // Initialize component
        this.init();
    }
    
    /**
     * Get default options for the component
     */
    getDefaultOptions() {
        return {
            autoInit: true,
            enableEvents: true,
            enableAccessibility: true,
            enableMobileOptimization: true,
            debug: false
        };
    }
    
    /**
     * Initialize the component
     */
    init() {
        if (this.isInitialized || this.isDestroyed) {
            return;
        }
        
        try {
            // Set up accessibility features
            if (this.options.enableAccessibility) {
                this.initAccessibility();
            }
            
            // Set up mobile optimizations
            if (this.options.enableMobileOptimization) {
                this.initMobileOptimizations();
            }
            
            // Set up event system
            if (this.options.enableEvents) {
                this.initEventSystem();
            }
            
            // Component-specific initialization
            this.onInit();
            
            // Mark as initialized
            this.isInitialized = true;
            
            // Emit initialization event
            this.emit('component:initialized', { component: this });
            
            this.log('Component initialized', this.constructor.name);
            
        } catch (error) {
            this.handleError('Initialization failed', error);
        }
    }
    
    /**
     * Component-specific initialization (override in subclasses)
     */
    onInit() {
        // Override in subclasses
    }
    
    /**
     * Initialize accessibility features
     */
    initAccessibility() {
        if (!this.element) return;
        
        // Add ARIA attributes if not present
        if (!this.element.getAttribute('role')) {
            this.element.setAttribute('role', this.getDefaultRole());
        }
        
        // Add keyboard navigation support
        this.setupKeyboardNavigation();
        
        // Add screen reader support
        this.setupScreenReaderSupport();
        
        // Add focus management
        this.setupFocusManagement();
    }
    
    /**
     * Get default ARIA role for the component
     */
    getDefaultRole() {
        return 'region';
    }
    
    /**
     * Set up keyboard navigation
     */
    setupKeyboardNavigation() {
        if (!this.element) return;
        
        // Make element focusable if it's interactive
        if (this.isInteractive() && !this.element.hasAttribute('tabindex')) {
            this.element.setAttribute('tabindex', '0');
        }
        
        // Add keyboard event listeners
        this.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));
    }
    
    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(event) {
        const { key, ctrlKey, shiftKey, altKey } = event;
        
        // Common keyboard shortcuts
        switch (key) {
            case 'Escape':
                this.handleEscape(event);
                break;
            case 'Enter':
            case ' ':
                if (this.isInteractive()) {
                    this.handleActivation(event);
                }
                break;
            case 'Tab':
                this.handleTabNavigation(event);
                break;
            case 'ArrowUp':
            case 'ArrowDown':
            case 'ArrowLeft':
            case 'ArrowRight':
                this.handleArrowNavigation(event);
                break;
        }
        
        // Emit keyboard event for custom handling
        this.emit('component:keyboard', { event, component: this });
    }
    
    /**
     * Set up screen reader support
     */
    setupScreenReaderSupport() {
        if (!this.element) return;
        
        // Add live region for dynamic content updates
        this.createLiveRegion();
        
        // Add descriptive labels
        this.setupDescriptiveLabels();
    }
    
    /**
     * Create ARIA live region for announcements
     */
    createLiveRegion() {
        if (document.getElementById('bcai-live-region')) return;
        
        const liveRegion = document.createElement('div');
        liveRegion.id = 'bcai-live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(liveRegion);
    }
    
    /**
     * Announce message to screen readers
     */
    announceToScreenReader(message) {
        const liveRegion = document.getElementById('bcai-live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }
    
    /**
     * Set up focus management
     */
    setupFocusManagement() {
        this.addEventListener('focus', this.handleFocus.bind(this));
        this.addEventListener('blur', this.handleBlur.bind(this));
    }
    
    /**
     * Initialize mobile optimizations
     */
    initMobileOptimizations() {
        if (!this.element) return;
        
        // Add touch event support
        this.setupTouchEvents();
        
        // Add responsive behavior
        this.setupResponsiveBehavior();
        
        // Add mobile-specific interactions
        this.setupMobileInteractions();
    }
    
    /**
     * Set up touch events
     */
    setupTouchEvents() {
        if (!('ontouchstart' in window)) return;
        
        this.addEventListener('touchstart', this.handleTouchStart.bind(this));
        this.addEventListener('touchmove', this.handleTouchMove.bind(this));
        this.addEventListener('touchend', this.handleTouchEnd.bind(this));
    }
    
    /**
     * Set up responsive behavior
     */
    setupResponsiveBehavior() {
        // Listen for viewport changes
        this.mediaQueryList = window.matchMedia('(max-width: 768px)');
        this.mediaQueryList.addListener(this.handleViewportChange.bind(this));
        
        // Initial check
        this.handleViewportChange(this.mediaQueryList);
    }
    
    /**
     * Handle viewport changes
     */
    handleViewportChange(mql) {
        const isMobile = mql.matches;
        
        if (this.element) {
            this.element.classList.toggle('is-mobile', isMobile);
            this.element.classList.toggle('is-desktop', !isMobile);
        }
        
        this.setState({ isMobile });
        this.emit('component:viewport-change', { isMobile, component: this });
        
        // Component-specific responsive handling
        this.onViewportChange(isMobile);
    }
    
    /**
     * Component-specific viewport change handling (override in subclasses)
     */
    onViewportChange(isMobile) {
        // Override in subclasses
    }
    
    /**
     * Initialize event system
     */
    initEventSystem() {
        // Set up WordPress hooks integration
        this.setupWordPressHooks();
        
        // Set up custom event system
        this.setupCustomEvents();
    }
    
    /**
     * Set up WordPress hooks integration
     */
    setupWordPressHooks() {
        if (typeof wp !== 'undefined' && wp.hooks) {
            this.wpHooks = wp.hooks;
            
            // Register component with global hook system
            this.wpHooks.doAction('bcai.component.register', this);
        }
    }
    
    /**
     * Set up custom event system
     */
    setupCustomEvents() {
        this.customEventTarget = new EventTarget();
    }
    
    /**
     * Add event listener
     */
    addEventListener(event, handler, options = {}) {
        if (!this.element) return;
        
        const wrappedHandler = this.wrapEventHandler(handler);
        this.element.addEventListener(event, wrappedHandler, options);
        
        // Store for cleanup
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push({ handler: wrappedHandler, options });
    }
    
    /**
     * Wrap event handler with error handling and debugging
     */
    wrapEventHandler(handler) {
        return (event) => {
            try {
                if (this.isDestroyed) return;
                
                this.log('Event triggered:', event.type);
                return handler(event);
            } catch (error) {
                this.handleError(`Event handler failed for ${event.type}`, error);
            }
        };
    }
    
    /**
     * Emit custom event
     */
    emit(eventName, data = {}) {
        if (this.isDestroyed) return;
        
        // Emit on custom event target
        if (this.customEventTarget) {
            const customEvent = new CustomEvent(eventName, { detail: data });
            this.customEventTarget.dispatchEvent(customEvent);
        }
        
        // Emit on element
        if (this.element) {
            const domEvent = new CustomEvent(eventName, { 
                detail: data, 
                bubbles: true, 
                cancelable: true 
            });
            this.element.dispatchEvent(domEvent);
        }
        
        // Emit via WordPress hooks
        if (this.wpHooks) {
            this.wpHooks.doAction(eventName, data);
        }
        
        this.log('Event emitted:', eventName, data);
    }
    
    /**
     * Listen for custom events
     */
    on(eventName, handler) {
        if (this.customEventTarget) {
            this.customEventTarget.addEventListener(eventName, handler);
        }
    }
    
    /**
     * Remove custom event listener
     */
    off(eventName, handler) {
        if (this.customEventTarget) {
            this.customEventTarget.removeEventListener(eventName, handler);
        }
    }
    
    /**
     * Set component state
     */
    setState(newState) {
        const oldState = { ...this.state };
        this.state = { ...this.state, ...newState };
        
        this.emit('component:state-change', { 
            oldState, 
            newState: this.state, 
            component: this 
        });
        
        this.onStateChange(oldState, this.state);
    }
    
    /**
     * Get component state
     */
    getState() {
        return { ...this.state };
    }
    
    /**
     * Component-specific state change handling (override in subclasses)
     */
    onStateChange(oldState, newState) {
        // Override in subclasses
    }
    
    /**
     * Check if component is interactive
     */
    isInteractive() {
        return false; // Override in interactive components
    }
    
    /**
     * Handle various events (override in subclasses)
     */
    handleEvent(event) {
        // Override in subclasses
    }
    
    handleEscape(event) {
        // Override in subclasses
    }
    
    handleActivation(event) {
        // Override in subclasses
    }
    
    handleTabNavigation(event) {
        // Override in subclasses
    }
    
    handleArrowNavigation(event) {
        // Override in subclasses
    }
    
    handleFocus(event) {
        this.emit('component:focus', { event, component: this });
    }
    
    handleBlur(event) {
        this.emit('component:blur', { event, component: this });
    }
    
    handleTouchStart(event) {
        this.emit('component:touch-start', { event, component: this });
    }
    
    handleTouchMove(event) {
        this.emit('component:touch-move', { event, component: this });
    }
    
    handleTouchEnd(event) {
        this.emit('component:touch-end', { event, component: this });
    }
    
    setupDescriptiveLabels() {
        // Override in subclasses
    }
    
    setupMobileInteractions() {
        // Override in subclasses
    }
    
    /**
     * Add child component
     */
    addChild(name, component) {
        this.childComponents.set(name, component);
        component.parent = this;
    }
    
    /**
     * Get child component
     */
    getChild(name) {
        return this.childComponents.get(name);
    }
    
    /**
     * Remove child component
     */
    removeChild(name) {
        const child = this.childComponents.get(name);
        if (child) {
            child.destroy();
            this.childComponents.delete(name);
        }
    }
    
    /**
     * Handle errors
     */
    handleError(message, error) {
        console.error(`[${this.constructor.name}] ${message}:`, error);
        this.emit('component:error', { message, error, component: this });
    }
    
    /**
     * Log debug messages
     */
    log(...args) {
        if (this.options.debug) {
            console.log(`[${this.constructor.name}]`, ...args);
        }
    }
    
    /**
     * Destroy component and clean up
     */
    destroy() {
        if (this.isDestroyed) return;
        
        try {
            // Emit destruction event
            this.emit('component:destroying', { component: this });
            
            // Destroy child components
            this.childComponents.forEach(child => child.destroy());
            this.childComponents.clear();
            
            // Remove event listeners
            this.eventListeners.forEach((handlers, event) => {
                handlers.forEach(({ handler, options }) => {
                    if (this.element) {
                        this.element.removeEventListener(event, handler, options);
                    }
                });
            });
            this.eventListeners.clear();
            
            // Clean up media query listener
            if (this.mediaQueryList) {
                this.mediaQueryList.removeListener(this.handleViewportChange.bind(this));
            }
            
            // Component-specific cleanup
            this.onDestroy();
            
            // Mark as destroyed
            this.isDestroyed = true;
            this.isInitialized = false;
            
            this.log('Component destroyed');
            
        } catch (error) {
            this.handleError('Destruction failed', error);
        }
    }
    
    /**
     * Component-specific cleanup (override in subclasses)
     */
    onDestroy() {
        // Override in subclasses
    }
}

// Export for use in other components
window.BusinessCraftAI = window.BusinessCraftAI || {};
window.BusinessCraftAI.BaseComponent = BaseComponent;

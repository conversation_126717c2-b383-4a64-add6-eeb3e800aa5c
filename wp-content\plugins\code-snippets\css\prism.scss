@import '~prismjs/themes/prism.css';
@import '~prismjs/plugins/line-numbers/prism-line-numbers.css';
@import '~prismjs/plugins/toolbar/prism-toolbar.css';
@import '~prismjs/plugins/inline-color/prism-inline-color.css';
@import '~prismjs/plugins/previewers/prism-previewers.css';
@import '~prismjs/plugins/autolinker/prism-autolinker.css';

/* These lines are taken from '~prismjs/plugins/inline-color/prism-inline-color.css', adjusted to avoid autoprefixer
complaining about deprecated syntax. Ideally, they can be removed if the file in question is updated. */

pre[data-line] {
	position: relative;
	padding: 1em 0 1em 3em;
}

.line-highlight {
	position: absolute;
	left: 0;
	right: 0;
	padding: inherit;
	margin-top: 1em;
	background: hsla(24, 20%, 50%, .08);
	background: linear-gradient(to right, hsla(24, 20%, 50%, .1) 70%, hsla(24, 20%, 50%, 0));
	pointer-events: none;
	line-height: inherit;
	white-space: pre;

	@media print {
		print-color-adjust: exact;
	}

	&:before, &[data-end]:after {
		content: attr(data-start);
		position: absolute;
		top: .4em;
		left: .6em;
		min-width: 1em;
		padding: 0 .5em;
		background-color: hsla(24, 20%, 50%, .4);
		color: hsl(24, 20%, 95%);
		font: bold 65%/1.5 sans-serif;
		text-align: center;
		vertical-align: .3em;
		border-radius: 999px;
		text-shadow: none;
		box-shadow: 0 1px white;
	}

	&[data-end]:after {
		content: attr(data-end);
		top: auto;
		bottom: .4em;
	}

	.line-numbers &:before, .line-numbers &:after {
		content: none;
	}

	/* Additional fixes to the behaviour of line highlighting with certain themes. */

	:not(.is-style-prism-shades-of-purple) pre[class*=language-].line-numbers & {
		box-sizing: border-box;
		padding: 0;
		margin: 1em 0 0;
	}

	.is-style-prism-coy-without-shadows pre[class*=language-] & {
		margin-top: 0;
	}
}

pre[id].linkable-line-numbers span.line-numbers-rows {
	pointer-events: all;

	> span:before {
		cursor: pointer;
	}

	> span:hover:before {
		background-color: rgba(128, 128, 128, .2);
	}
}

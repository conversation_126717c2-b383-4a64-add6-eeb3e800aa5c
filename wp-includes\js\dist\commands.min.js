/*! This file is auto-generated */
(()=>{"use strict";var e,t,n={},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={exports:{}};return n[e](a,a.exports,o),a.exports}t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var a=Object.create(null);o.r(a);var c={};e=e||[null,t({}),t([]),t(t)];for(var u=2&r&&n;"object"==typeof u&&!~e.indexOf(u);u=t(u))Object.getOwnPropertyNames(u).forEach((e=>c[e]=()=>n[e]));return c.default=()=>n,o.d(a,c),a},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nc=void 0;var a={};o.r(a),o.d(a,{CommandMenu:()=>Vn,privateApis:()=>qn,store:()=>Fn,useCommand:()=>zn,useCommandLoader:()=>Gn});var c={};o.r(c),o.d(c,{close:()=>Nn,open:()=>An,registerCommand:()=>xn,registerCommandLoader:()=>Rn,unregisterCommand:()=>On,unregisterCommandLoader:()=>kn});var u={};o.r(u),o.d(u,{getCommandLoaders:()=>Mn,getCommands:()=>Ln,getContext:()=>Tn,isOpen:()=>_n});var i={};o.r(i),o.d(i,{setContext:()=>Dn});var l=.999,s=/[\\\/_+.#"@\[\(\{&]/,d=/[\\\/_+.#"@\[\(\{&]/g,f=/[\s-]/,m=/[\s-]/g;function v(e,t,n,r,o,a,c){if(a===t.length)return o===e.length?1:.99;var u=`${o},${a}`;if(void 0!==c[u])return c[u];for(var i,p,h,g,b=r.charAt(a),E=n.indexOf(b,o),y=0;E>=0;)(i=v(e,t,n,r,E+1,a+1,c))>y&&(E===o?i*=1:s.test(e.charAt(E-1))?(i*=.8,(h=e.slice(o,E-1).match(d))&&o>0&&(i*=Math.pow(l,h.length))):f.test(e.charAt(E-1))?(i*=.9,(g=e.slice(o,E-1).match(m))&&o>0&&(i*=Math.pow(l,g.length))):(i*=.17,o>0&&(i*=Math.pow(l,E-o))),e.charAt(E)!==t.charAt(a)&&(i*=.9999)),(i<.1&&n.charAt(E-1)===r.charAt(a+1)||r.charAt(a+1)===r.charAt(a)&&n.charAt(E-1)!==r.charAt(a))&&(.1*(p=v(e,t,n,r,E+1,a+2,c))>i&&(i=.1*p)),i>y&&(y=i),E=n.indexOf(b,E+1);return c[u]=y,y}function p(e){return e.toLowerCase().replace(m," ")}function h(e,t,n){return v(e=n&&n.length>0?""+(e+" "+n.join(" ")):e,t,p(e),p(t),0,0,{})}function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},g.apply(null,arguments)}const b=window.React;var E=o.t(b,2);function y(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function w(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function C(...e){return(0,b.useCallback)(w(...e),e)}function S(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(e){const r=n.reduce(((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]})),{});return(0,b.useMemo)((()=>({[`__scope${t.scopeName}`]:r})),[r])}};return n.scopeName=t.scopeName,n}const x=Boolean(null===globalThis||void 0===globalThis?void 0:globalThis.document)?b.useLayoutEffect:()=>{},O=E["useId".toString()]||(()=>{});let R=0;function k(e){const[t,n]=b.useState(O());return x((()=>{e||n((e=>null!=e?e:String(R++)))}),[e]),e||(t?`radix-${t}`:"")}function A(e){const t=(0,b.useRef)(e);return(0,b.useEffect)((()=>{t.current=e})),(0,b.useMemo)((()=>(...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)}),[])}function N({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=function({defaultProp:e,onChange:t}){const n=(0,b.useState)(e),[r]=n,o=(0,b.useRef)(r),a=A(t);return(0,b.useEffect)((()=>{o.current!==r&&(a(r),o.current=r)}),[r,o,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,c=a?e:r,u=A(n);return[c,(0,b.useCallback)((t=>{if(a){const n="function"==typeof t?t(e):t;n!==e&&u(n)}else o(t)}),[a,e,o,u])]}const L=window.ReactDOM,M=(0,b.forwardRef)(((e,t)=>{const{children:n,...r}=e,o=b.Children.toArray(n),a=o.find(D);if(a){const e=a.props.children,n=o.map((t=>t===a?b.Children.count(e)>1?b.Children.only(null):(0,b.isValidElement)(e)?e.props.children:null:t));return(0,b.createElement)(_,g({},r,{ref:t}),(0,b.isValidElement)(e)?(0,b.cloneElement)(e,void 0,n):null)}return(0,b.createElement)(_,g({},r,{ref:t}),n)}));M.displayName="Slot";const _=(0,b.forwardRef)(((e,t)=>{const{children:n,...r}=e;return(0,b.isValidElement)(n)?(0,b.cloneElement)(n,{...P(r,n.props),ref:t?w(t,n.ref):n.ref}):b.Children.count(n)>1?b.Children.only(null):null}));_.displayName="SlotClone";const T=({children:e})=>(0,b.createElement)(b.Fragment,null,e);function D(e){return(0,b.isValidElement)(e)&&e.type===T}function P(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}const I=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((e,t)=>{const n=(0,b.forwardRef)(((e,n)=>{const{asChild:r,...o}=e,a=r?M:t;return(0,b.useEffect)((()=>{window[Symbol.for("radix-ui")]=!0}),[]),(0,b.createElement)(a,g({},o,{ref:n}))}));return n.displayName=`Primitive.${t}`,{...e,[t]:n}}),{});const j="dismissableLayer.update",F="dismissableLayer.pointerDownOutside",W="dismissableLayer.focusOutside";let U;const $=(0,b.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),B=(0,b.forwardRef)(((e,t)=>{var n;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:u,onDismiss:i,...l}=e,s=(0,b.useContext)($),[d,f]=(0,b.useState)(null),m=null!==(n=null==d?void 0:d.ownerDocument)&&void 0!==n?n:null===globalThis||void 0===globalThis?void 0:globalThis.document,[,v]=(0,b.useState)({}),p=C(t,(e=>f(e))),h=Array.from(s.layers),[E]=[...s.layersWithOutsidePointerEventsDisabled].slice(-1),w=h.indexOf(E),S=d?h.indexOf(d):-1,x=s.layersWithOutsidePointerEventsDisabled.size>0,O=S>=w,R=function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const n=A(e),r=(0,b.useRef)(!1),o=(0,b.useRef)((()=>{}));return(0,b.useEffect)((()=>{const e=e=>{if(e.target&&!r.current){const a={originalEvent:e};function c(){V(F,n,a,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=c,t.addEventListener("click",o.current,{once:!0})):c()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout((()=>{t.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}}),[t,n]),{onPointerDownCapture:()=>r.current=!0}}((e=>{const t=e.target,n=[...s.branches].some((e=>e.contains(t)));O&&!n&&(null==a||a(e),null==u||u(e),e.defaultPrevented||null==i||i())}),m),k=function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const n=A(e),r=(0,b.useRef)(!1);return(0,b.useEffect)((()=>{const e=e=>{if(e.target&&!r.current){V(W,n,{originalEvent:e},{discrete:!1})}};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)}),[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}((e=>{const t=e.target;[...s.branches].some((e=>e.contains(t)))||(null==c||c(e),null==u||u(e),e.defaultPrevented||null==i||i())}),m);return function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const n=A(e);(0,b.useEffect)((()=>{const e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)}),[n,t])}((e=>{S===s.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&i&&(e.preventDefault(),i()))}),m),(0,b.useEffect)((()=>{if(d)return r&&(0===s.layersWithOutsidePointerEventsDisabled.size&&(U=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),s.layersWithOutsidePointerEventsDisabled.add(d)),s.layers.add(d),K(),()=>{r&&1===s.layersWithOutsidePointerEventsDisabled.size&&(m.body.style.pointerEvents=U)}}),[d,m,r,s]),(0,b.useEffect)((()=>()=>{d&&(s.layers.delete(d),s.layersWithOutsidePointerEventsDisabled.delete(d),K())}),[d,s]),(0,b.useEffect)((()=>{const e=()=>v({});return document.addEventListener(j,e),()=>document.removeEventListener(j,e)}),[]),(0,b.createElement)(I.div,g({},l,{ref:p,style:{pointerEvents:x?O?"auto":"none":void 0,...e.style},onFocusCapture:y(e.onFocusCapture,k.onFocusCapture),onBlurCapture:y(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:y(e.onPointerDownCapture,R.onPointerDownCapture)}))}));function K(){const e=new CustomEvent(j);document.dispatchEvent(e)}function V(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?function(e,t){e&&(0,L.flushSync)((()=>e.dispatchEvent(t)))}(o,a):o.dispatchEvent(a)}const q="focusScope.autoFocusOnMount",z="focusScope.autoFocusOnUnmount",G={bubbles:!1,cancelable:!0},H=(0,b.forwardRef)(((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[u,i]=(0,b.useState)(null),l=A(o),s=A(a),d=(0,b.useRef)(null),f=C(t,(e=>i(e))),m=(0,b.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,b.useEffect)((()=>{if(r){function e(e){if(m.paused||!u)return;const t=e.target;u.contains(t)?d.current=t:J(d.current,{select:!0})}function t(e){if(m.paused||!u)return;const t=e.relatedTarget;null!==t&&(u.contains(t)||J(d.current,{select:!0}))}function n(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&J(u)}document.addEventListener("focusin",e),document.addEventListener("focusout",t);const o=new MutationObserver(n);return u&&o.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),o.disconnect()}}}),[r,u,m.paused]),(0,b.useEffect)((()=>{if(u){Q.add(m);const t=document.activeElement;if(!u.contains(t)){const n=new CustomEvent(q,G);u.addEventListener(q,l),u.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(J(r,{select:t}),document.activeElement!==n)return}((e=X(u),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&J(u))}return()=>{u.removeEventListener(q,l),setTimeout((()=>{const e=new CustomEvent(z,G);u.addEventListener(z,s),u.dispatchEvent(e),e.defaultPrevented||J(null!=t?t:document.body,{select:!0}),u.removeEventListener(z,s),Q.remove(m)}),0)}}var e}),[u,l,s,m]);const v=(0,b.useCallback)((e=>{if(!n&&!r)return;if(m.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){const t=e.currentTarget,[r,a]=function(e){const t=X(e),n=Y(t,e),r=Y(t.reverse(),e);return[n,r]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&J(a,{select:!0})):(e.preventDefault(),n&&J(r,{select:!0})):o===t&&e.preventDefault()}}),[n,r,m.paused]);return(0,b.createElement)(I.div,g({tabIndex:-1},c,{ref:f,onKeyDown:v}))}));function X(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Y(e,t){for(const n of e)if(!Z(n,{upTo:t}))return n}function Z(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function J(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}const Q=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=ee(e,t),e.unshift(t)},remove(t){var n;e=ee(e,t),null===(n=e[0])||void 0===n||n.resume()}}}();function ee(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}const te=(0,b.forwardRef)(((e,t)=>{var n;const{container:r=(null===globalThis||void 0===globalThis||null===(n=globalThis.document)||void 0===n?void 0:n.body),...o}=e;return r?L.createPortal((0,b.createElement)(I.div,g({},o,{ref:t})),r):null}));const ne=e=>{const{present:t,children:n}=e,r=function(e){const[t,n]=(0,b.useState)(),r=(0,b.useRef)({}),o=(0,b.useRef)(e),a=(0,b.useRef)("none"),c=e?"mounted":"unmounted",[u,i]=function(e,t){return(0,b.useReducer)(((e,n)=>{const r=t[e][n];return null!=r?r:e}),e)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return(0,b.useEffect)((()=>{const e=re(r.current);a.current="mounted"===u?e:"none"}),[u]),x((()=>{const t=r.current,n=o.current;if(n!==e){const r=a.current,c=re(t);if(e)i("MOUNT");else if("none"===c||"none"===(null==t?void 0:t.display))i("UNMOUNT");else{i(n&&r!==c?"ANIMATION_OUT":"UNMOUNT")}o.current=e}}),[e,i]),x((()=>{if(t){const e=e=>{const n=re(r.current).includes(e.animationName);e.target===t&&n&&(0,L.flushSync)((()=>i("ANIMATION_END")))},n=e=>{e.target===t&&(a.current=re(r.current))};return t.addEventListener("animationstart",n),t.addEventListener("animationcancel",e),t.addEventListener("animationend",e),()=>{t.removeEventListener("animationstart",n),t.removeEventListener("animationcancel",e),t.removeEventListener("animationend",e)}}i("ANIMATION_END")}),[t,i]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:(0,b.useCallback)((e=>{e&&(r.current=getComputedStyle(e)),n(e)}),[])}}(t),o="function"==typeof n?n({present:r.isPresent}):b.Children.only(n),a=C(r.ref,o.ref);return"function"==typeof n||r.isPresent?(0,b.cloneElement)(o,{ref:a}):null};function re(e){return(null==e?void 0:e.animationName)||"none"}ne.displayName="Presence";let oe=0;function ae(){(0,b.useEffect)((()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:ce()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:ce()),oe++,()=>{1===oe&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),oe--}}),[])}function ce(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var ue=function(){return ue=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ue.apply(this,arguments)};function ie(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}Object.create;function le(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"==typeof SuppressedError&&SuppressedError;var se="right-scroll-bar-position",de="width-before-scroll-bar";function fe(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var me="undefined"!=typeof window?b.useLayoutEffect:b.useEffect,ve=new WeakMap;function pe(e,t){var n,r,o,a=(n=t||null,r=function(t){return e.forEach((function(e){return fe(e,t)}))},(o=(0,b.useState)((function(){return{value:n,callback:r,facade:{get current(){return o.value},set current(e){var t=o.value;t!==e&&(o.value=e,o.callback(e,t))}}}}))[0]).callback=r,o.facade);return me((function(){var t=ve.get(a);if(t){var n=new Set(t),r=new Set(e),o=a.current;n.forEach((function(e){r.has(e)||fe(e,null)})),r.forEach((function(e){n.has(e)||fe(e,o)}))}ve.set(a,e)}),[e]),a}function he(e){return e}function ge(e,t){void 0===t&&(t=he);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},c=function(){return Promise.resolve().then(a)};c(),n={push:function(e){t.push(e),c()},filter:function(e){return t=t.filter(e),n}}}}}var be=function(e){void 0===e&&(e={});var t=ge(null);return t.options=ue({async:!0,ssr:!1},e),t}(),Ee=function(){},ye=b.forwardRef((function(e,t){var n=b.useRef(null),r=b.useState({onScrollCapture:Ee,onWheelCapture:Ee,onTouchMoveCapture:Ee}),o=r[0],a=r[1],c=e.forwardProps,u=e.children,i=e.className,l=e.removeScrollBar,s=e.enabled,d=e.shards,f=e.sideCar,m=e.noIsolation,v=e.inert,p=e.allowPinchZoom,h=e.as,g=void 0===h?"div":h,E=ie(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),y=f,w=pe([n,t]),C=ue(ue({},E),o);return b.createElement(b.Fragment,null,s&&b.createElement(y,{sideCar:be,removeScrollBar:l,shards:d,noIsolation:m,inert:v,setCallbacks:a,allowPinchZoom:!!p,lockRef:n}),c?b.cloneElement(b.Children.only(u),ue(ue({},C),{ref:w})):b.createElement(g,ue({},C,{className:i,ref:w}),u))}));ye.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ye.classNames={fullWidth:de,zeroRight:se};var we,Ce=function(e){var t=e.sideCar,n=ie(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return b.createElement(r,ue({},n))};Ce.isSideCarExport=!0;function Se(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=we||o.nc;return t&&e.setAttribute("nonce",t),e}var xe=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=Se())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Oe=function(){var e,t=(e=xe(),function(t,n){b.useEffect((function(){return e.add(t),function(){e.remove()}}),[t&&n])});return function(e){var n=e.styles,r=e.dynamic;return t(n,r),null}},Re={left:0,top:0,right:0,gap:0},ke=function(e){return parseInt(e||"",10)||0},Ae=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Re;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ke(n),ke(r),ke(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Ne=Oe(),Le="data-scroll-locked",Me=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(Le,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(se," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(de," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(se," .").concat(se," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(de," .").concat(de," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Le,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},_e=function(){var e=parseInt(document.body.getAttribute(Le)||"0",10);return isFinite(e)?e:0},Te=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;b.useEffect((function(){return document.body.setAttribute(Le,(_e()+1).toString()),function(){var e=_e()-1;e<=0?document.body.removeAttribute(Le):document.body.setAttribute(Le,e.toString())}}),[]);var a=b.useMemo((function(){return Ae(o)}),[o]);return b.createElement(Ne,{styles:Me(a,!t,o,n?"":"!important")})},De=!1;if("undefined"!=typeof window)try{var Pe=Object.defineProperty({},"passive",{get:function(){return De=!0,!0}});window.addEventListener("test",Pe,Pe),window.removeEventListener("test",Pe,Pe)}catch(e){De=!1}var Ie=!!De&&{passive:!1},je=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},Fe=function(e,t){var n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),We(e,n)){var r=Ue(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},We=function(e,t){return"v"===e?function(e){return je(e,"overflowY")}(t):function(e){return je(e,"overflowX")}(t)},Ue=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},$e=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Be=function(e){return[e.deltaX,e.deltaY]},Ke=function(e){return e&&"current"in e?e.current:e},Ve=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},qe=0,ze=[];const Ge=(He=function(e){var t=b.useRef([]),n=b.useRef([0,0]),r=b.useRef(),o=b.useState(qe++)[0],a=b.useState((function(){return Oe()}))[0],c=b.useRef(e);b.useEffect((function(){c.current=e}),[e]),b.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=le([e.lockRef.current],(e.shards||[]).map(Ke),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(o))})),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(o))}))}}}),[e.inert,e.lockRef.current,e.shards]);var u=b.useCallback((function(e,t){if("touches"in e&&2===e.touches.length)return!c.current.allowPinchZoom;var o,a=$e(e),u=n.current,i="deltaX"in e?e.deltaX:u[0]-a[0],l="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(i)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=Fe(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=Fe(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(i||l)&&(r.current=o),!o)return!0;var m=r.current||o;return function(e,t,n,r,o){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),c=a*r,u=n.target,i=t.contains(u),l=!1,s=c>0,d=0,f=0;do{var m=Ue(e,u),v=m[0],p=m[1]-m[2]-a*v;(v||p)&&We(e,u)&&(d+=p,f+=v),u=u.parentNode}while(!i&&u!==document.body||i&&(t.contains(u)||t===u));return(s&&(o&&0===d||!o&&c>d)||!s&&(o&&0===f||!o&&-c>f))&&(l=!0),l}(m,t,e,"h"===m?i:l,!0)}),[]),i=b.useCallback((function(e){var n=e;if(ze.length&&ze[ze.length-1]===a){var r="deltaY"in n?Be(n):$e(n),o=t.current.filter((function(e){return e.name===n.type&&e.target===n.target&&(t=e.delta,o=r,t[0]===o[0]&&t[1]===o[1]);var t,o}))[0];if(o&&o.should)n.cancelable&&n.preventDefault();else if(!o){var i=(c.current.shards||[]).map(Ke).filter(Boolean).filter((function(e){return e.contains(n.target)}));(i.length>0?u(n,i[0]):!c.current.noIsolation)&&n.cancelable&&n.preventDefault()}}}),[]),l=b.useCallback((function(e,n,r,o){var a={name:e,delta:n,target:r,should:o};t.current.push(a),setTimeout((function(){t.current=t.current.filter((function(e){return e!==a}))}),1)}),[]),s=b.useCallback((function(e){n.current=$e(e),r.current=void 0}),[]),d=b.useCallback((function(t){l(t.type,Be(t),t.target,u(t,e.lockRef.current))}),[]),f=b.useCallback((function(t){l(t.type,$e(t),t.target,u(t,e.lockRef.current))}),[]);b.useEffect((function(){return ze.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:f}),document.addEventListener("wheel",i,Ie),document.addEventListener("touchmove",i,Ie),document.addEventListener("touchstart",s,Ie),function(){ze=ze.filter((function(e){return e!==a})),document.removeEventListener("wheel",i,Ie),document.removeEventListener("touchmove",i,Ie),document.removeEventListener("touchstart",s,Ie)}}),[]);var m=e.removeScrollBar,v=e.inert;return b.createElement(b.Fragment,null,v?b.createElement(a,{styles:Ve(o)}):null,m?b.createElement(Te,{gapMode:"margin"}):null)},be.useMedium(He),Ce);var He,Xe=b.forwardRef((function(e,t){return b.createElement(ye,ue({},e,{ref:t,sideCar:Ge}))}));Xe.classNames=ye.classNames;const Ye=Xe;var Ze=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},Je=new WeakMap,Qe=new WeakMap,et={},tt=0,nt=function(e){return e&&(e.host||nt(e.parentNode))},rt=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=nt(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);et[n]||(et[n]=new WeakMap);var a=et[n],c=[],u=new Set,i=new Set(o),l=function(e){e&&!u.has(e)&&(u.add(e),l(e.parentNode))};o.forEach(l);var s=function(e){e&&!i.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,i=(Je.get(e)||0)+1,l=(a.get(e)||0)+1;Je.set(e,i),a.set(e,l),c.push(e),1===i&&o&&Qe.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}}))};return s(t),u.clear(),tt++,function(){c.forEach((function(e){var t=Je.get(e)-1,o=a.get(e)-1;Je.set(e,t),a.set(e,o),t||(Qe.has(e)||e.removeAttribute(r),Qe.delete(e)),o||e.removeAttribute(n)})),--tt||(Je=new WeakMap,Je=new WeakMap,Qe=new WeakMap,et={})}},ot=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||Ze(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),rt(r,o,n,"aria-hidden")):function(){return null}};const at="Dialog",[ct,ut]=function(e,t=[]){let n=[];const r=()=>{const t=n.map((e=>(0,b.createContext)(e)));return function(n){const r=(null==n?void 0:n[e])||t;return(0,b.useMemo)((()=>({[`__scope${e}`]:{...n,[e]:r}})),[n,r])}};return r.scopeName=e,[function(t,r){const o=(0,b.createContext)(r),a=n.length;function c(t){const{scope:n,children:r,...c}=t,u=(null==n?void 0:n[e][a])||o,i=(0,b.useMemo)((()=>c),Object.values(c));return(0,b.createElement)(u.Provider,{value:i},r)}return n=[...n,r],c.displayName=t+"Provider",[c,function(n,c){const u=(null==c?void 0:c[e][a])||o,i=(0,b.useContext)(u);if(i)return i;if(void 0!==r)return r;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},S(r,...t)]}(at),[it,lt]=ct(at),st=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,u=(0,b.useRef)(null),i=(0,b.useRef)(null),[l=!1,s]=N({prop:r,defaultProp:o,onChange:a});return(0,b.createElement)(it,{scope:t,triggerRef:u,contentRef:i,contentId:k(),titleId:k(),descriptionId:k(),open:l,onOpenChange:s,onOpenToggle:(0,b.useCallback)((()=>s((e=>!e))),[s]),modal:c},n)},dt="DialogPortal",[ft,mt]=ct(dt,{forceMount:void 0}),vt=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=lt(dt,t);return(0,b.createElement)(ft,{scope:t,forceMount:n},b.Children.map(r,(e=>(0,b.createElement)(ne,{present:n||a.open},(0,b.createElement)(te,{asChild:!0,container:o},e)))))},pt="DialogOverlay",ht=(0,b.forwardRef)(((e,t)=>{const n=mt(pt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=lt(pt,e.__scopeDialog);return a.modal?(0,b.createElement)(ne,{present:r||a.open},(0,b.createElement)(gt,g({},o,{ref:t}))):null})),gt=(0,b.forwardRef)(((e,t)=>{const{__scopeDialog:n,...r}=e,o=lt(pt,n);return(0,b.createElement)(Ye,{as:M,allowPinchZoom:!0,shards:[o.contentRef]},(0,b.createElement)(I.div,g({"data-state":xt(o.open)},r,{ref:t,style:{pointerEvents:"auto",...r.style}})))})),bt="DialogContent",Et=(0,b.forwardRef)(((e,t)=>{const n=mt(bt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=lt(bt,e.__scopeDialog);return(0,b.createElement)(ne,{present:r||a.open},a.modal?(0,b.createElement)(yt,g({},o,{ref:t})):(0,b.createElement)(wt,g({},o,{ref:t})))})),yt=(0,b.forwardRef)(((e,t)=>{const n=lt(bt,e.__scopeDialog),r=(0,b.useRef)(null),o=C(t,n.contentRef,r);return(0,b.useEffect)((()=>{const e=r.current;if(e)return ot(e)}),[]),(0,b.createElement)(Ct,g({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:y(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:y(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:y(e.onFocusOutside,(e=>e.preventDefault()))}))})),wt=(0,b.forwardRef)(((e,t)=>{const n=lt(bt,e.__scopeDialog),r=(0,b.useRef)(!1),o=(0,b.useRef)(!1);return(0,b.createElement)(Ct,g({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,c;(null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented)||(r.current||null===(c=n.triggerRef.current)||void 0===c||c.focus(),t.preventDefault());r.current=!1,o.current=!1},onInteractOutside:t=>{var a,c;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));const u=t.target;(null===(c=n.triggerRef.current)||void 0===c?void 0:c.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}}))})),Ct=(0,b.forwardRef)(((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,u=lt(bt,n),i=C(t,(0,b.useRef)(null));return ae(),(0,b.createElement)(b.Fragment,null,(0,b.createElement)(H,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a},(0,b.createElement)(B,g({role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":xt(u.open)},c,{ref:i,onDismiss:()=>u.onOpenChange(!1)}))),!1)})),St="DialogTitle";function xt(e){return e?"open":"closed"}const Ot="DialogTitleWarning",[Rt,kt]=function(e,t){const n=(0,b.createContext)(t);function r(e){const{children:t,...r}=e,o=(0,b.useMemo)((()=>r),Object.values(r));return(0,b.createElement)(n.Provider,{value:o},t)}return r.displayName=e+"Provider",[r,function(r){const o=(0,b.useContext)(n);if(o)return o;if(void 0!==t)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}]}(Ot,{contentName:bt,titleName:St,docsSlug:"dialog"}),At=st,Nt=vt,Lt=ht,Mt=Et;var _t='[cmdk-group=""]',Tt='[cmdk-group-items=""]',Dt='[cmdk-item=""]',Pt=`${Dt}:not([aria-disabled="true"])`,It="cmdk-item-select",jt="data-value",Ft=(e,t,n)=>h(e,t,n),Wt=b.createContext(void 0),Ut=()=>b.useContext(Wt),$t=b.createContext(void 0),Bt=()=>b.useContext($t),Kt=b.createContext(void 0),Vt=b.forwardRef(((e,t)=>{let n=on((()=>{var t,n;return{search:"",value:null!=(n=null!=(t=e.value)?t:e.defaultValue)?n:"",filtered:{count:0,items:new Map,groups:new Set}}})),r=on((()=>new Set)),o=on((()=>new Map)),a=on((()=>new Map)),c=on((()=>new Set)),u=nn(e),{label:i,children:l,value:s,onValueChange:d,filter:f,shouldFilter:m,loop:v,disablePointerSelection:p=!1,vimBindings:h=!0,...g}=e,E=b.useId(),y=b.useId(),w=b.useId(),C=b.useRef(null),S=ln();rn((()=>{if(void 0!==s){let e=s.trim();n.current.value=e,x.emit()}}),[s]),rn((()=>{S(6,L)}),[]);let x=b.useMemo((()=>({subscribe:e=>(c.current.add(e),()=>c.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var o,a,c;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)N(),k(),S(1,A);else if("value"===e&&(r||S(5,L),void 0!==(null==(o=u.current)?void 0:o.value))){let e=null!=t?t:"";return void(null==(c=(a=u.current).onValueChange)||c.call(a,e))}x.emit()}},emit:()=>{c.current.forEach((e=>e()))}})),[]),O=b.useMemo((()=>({value:(e,t,r)=>{var o;t!==(null==(o=a.current.get(e))?void 0:o.value)&&(a.current.set(e,{value:t,keywords:r}),n.current.filtered.items.set(e,R(t,r)),S(2,(()=>{k(),x.emit()})))},item:(e,t)=>(r.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),S(3,(()=>{N(),k(),n.current.value||A(),x.emit()})),()=>{a.current.delete(e),r.current.delete(e),n.current.filtered.items.delete(e);let t=M();S(4,(()=>{N(),(null==t?void 0:t.getAttribute("id"))===e&&A(),x.emit()}))}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{a.current.delete(e),o.current.delete(e)}),filter:()=>u.current.shouldFilter,label:i||e["aria-label"],disablePointerSelection:p,listId:E,inputId:w,labelId:y,listInnerRef:C})),[]);function R(e,t){var r,o;let a=null!=(o=null==(r=u.current)?void 0:r.filter)?o:Ft;return e?a(e,n.current.search,t):0}function k(){if(!n.current.search||!1===u.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach((n=>{let r=o.current.get(n),a=0;r.forEach((t=>{let n=e.get(t);a=Math.max(n,a)})),t.push([n,a])}));let r=C.current;_().sort(((t,n)=>{var r,o;let a=t.getAttribute("id"),c=n.getAttribute("id");return(null!=(r=e.get(c))?r:0)-(null!=(o=e.get(a))?o:0)})).forEach((e=>{let t=e.closest(Tt);t?t.appendChild(e.parentElement===t?e:e.closest(`${Tt} > *`)):r.appendChild(e.parentElement===r?e:e.closest(`${Tt} > *`))})),t.sort(((e,t)=>t[1]-e[1])).forEach((e=>{let t=C.current.querySelector(`${_t}[${jt}="${encodeURIComponent(e[0])}"]`);null==t||t.parentElement.appendChild(t)}))}function A(){let e=_().find((e=>"true"!==e.getAttribute("aria-disabled"))),t=null==e?void 0:e.getAttribute(jt);x.setState("value",t||void 0)}function N(){var e,t,c,i;if(!n.current.search||!1===u.current.shouldFilter)return void(n.current.filtered.count=r.current.size);n.current.filtered.groups=new Set;let l=0;for(let o of r.current){let r=R(null!=(t=null==(e=a.current.get(o))?void 0:e.value)?t:"",null!=(i=null==(c=a.current.get(o))?void 0:c.keywords)?i:[]);n.current.filtered.items.set(o,r),r>0&&l++}for(let[e,t]of o.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=l}function L(){var e,t,n;let r=M();r&&((null==(e=r.parentElement)?void 0:e.firstChild)===r&&(null==(n=null==(t=r.closest(_t))?void 0:t.querySelector('[cmdk-group-heading=""]'))||n.scrollIntoView({block:"nearest"})),r.scrollIntoView({block:"nearest"}))}function M(){var e;return null==(e=C.current)?void 0:e.querySelector(`${Dt}[aria-selected="true"]`)}function _(){var e;return Array.from(null==(e=C.current)?void 0:e.querySelectorAll(Pt))}function T(e){let t=_()[e];t&&x.setState("value",t.getAttribute(jt))}function D(e){var t;let n=M(),r=_(),o=r.findIndex((e=>e===n)),a=r[o+e];null!=(t=u.current)&&t.loop&&(a=o+e<0?r[r.length-1]:o+e===r.length?r[0]:r[o+e]),a&&x.setState("value",a.getAttribute(jt))}function P(e){let t,n=M(),r=null==n?void 0:n.closest(_t);for(;r&&!t;)r=e>0?en(r,_t):tn(r,_t),t=null==r?void 0:r.querySelector(Pt);t?x.setState("value",t.getAttribute(jt)):D(e)}let j=()=>T(_().length-1),F=e=>{e.preventDefault(),e.metaKey?j():e.altKey?P(1):D(1)},W=e=>{e.preventDefault(),e.metaKey?T(0):e.altKey?P(-1):D(-1)};return b.createElement(I.div,{ref:t,tabIndex:-1,...g,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=g.onKeyDown)||t.call(g,e),!e.defaultPrevented)switch(e.key){case"n":case"j":h&&e.ctrlKey&&F(e);break;case"ArrowDown":F(e);break;case"p":case"k":h&&e.ctrlKey&&W(e);break;case"ArrowUp":W(e);break;case"Home":e.preventDefault(),T(0);break;case"End":e.preventDefault(),j();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=M();if(t){let e=new Event(It);t.dispatchEvent(e)}}}}},b.createElement("label",{"cmdk-label":"",htmlFor:O.inputId,id:O.labelId,style:dn},i),sn(e,(e=>b.createElement($t.Provider,{value:x},b.createElement(Wt.Provider,{value:O},e)))))})),qt=b.forwardRef(((e,t)=>{var n,r;let o=b.useId(),a=b.useRef(null),c=b.useContext(Kt),u=Ut(),i=nn(e),l=null!=(r=null==(n=i.current)?void 0:n.forceMount)?r:null==c?void 0:c.forceMount;rn((()=>{if(!l)return u.item(o,null==c?void 0:c.id)}),[l]);let s=un(o,a,[e.value,e.children,a],e.keywords),d=Bt(),f=cn((e=>e.value&&e.value===s.current)),m=cn((e=>!(!l&&!1!==u.filter())||(!e.search||e.filtered.items.get(o)>0)));function v(){var e,t;p(),null==(t=(e=i.current).onSelect)||t.call(e,s.current)}function p(){d.setState("value",s.current,!0)}if(b.useEffect((()=>{let t=a.current;if(t&&!e.disabled)return t.addEventListener(It,v),()=>t.removeEventListener(It,v)}),[m,e.onSelect,e.disabled]),!m)return null;let{disabled:h,value:g,onSelect:E,forceMount:y,keywords:w,...C}=e;return b.createElement(I.div,{ref:an([a,t]),...C,id:o,"cmdk-item":"",role:"option","aria-disabled":!!h,"aria-selected":!!f,"data-disabled":!!h,"data-selected":!!f,onPointerMove:h||u.disablePointerSelection?void 0:p,onClick:h?void 0:v},e.children)})),zt=b.forwardRef(((e,t)=>{let{heading:n,children:r,forceMount:o,...a}=e,c=b.useId(),u=b.useRef(null),i=b.useRef(null),l=b.useId(),s=Ut(),d=cn((e=>!(!o&&!1!==s.filter())||(!e.search||e.filtered.groups.has(c))));rn((()=>s.group(c)),[]),un(c,u,[e.value,e.heading,i]);let f=b.useMemo((()=>({id:c,forceMount:o})),[o]);return b.createElement(I.div,{ref:an([u,t]),...a,"cmdk-group":"",role:"presentation",hidden:!d||void 0},n&&b.createElement("div",{ref:i,"cmdk-group-heading":"","aria-hidden":!0,id:l},n),sn(e,(e=>b.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?l:void 0},b.createElement(Kt.Provider,{value:f},e)))))})),Gt=b.forwardRef(((e,t)=>{let{alwaysRender:n,...r}=e,o=b.useRef(null),a=cn((e=>!e.search));return n||a?b.createElement(I.div,{ref:an([o,t]),...r,"cmdk-separator":"",role:"separator"}):null})),Ht=b.forwardRef(((e,t)=>{let{onValueChange:n,...r}=e,o=null!=e.value,a=Bt(),c=cn((e=>e.search)),u=cn((e=>e.value)),i=Ut(),l=b.useMemo((()=>{var e;let t=null==(e=i.listInnerRef.current)?void 0:e.querySelector(`${Dt}[${jt}="${encodeURIComponent(u)}"]`);return null==t?void 0:t.getAttribute("id")}),[]);return b.useEffect((()=>{null!=e.value&&a.setState("search",e.value)}),[e.value]),b.createElement(I.input,{ref:t,...r,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":i.listId,"aria-labelledby":i.labelId,"aria-activedescendant":l,id:i.inputId,type:"text",value:o?e.value:c,onChange:e=>{o||a.setState("search",e.target.value),null==n||n(e.target.value)}})})),Xt=b.forwardRef(((e,t)=>{let{children:n,label:r="Suggestions",...o}=e,a=b.useRef(null),c=b.useRef(null),u=Ut();return b.useEffect((()=>{if(c.current&&a.current){let e,t=c.current,n=a.current,r=new ResizeObserver((()=>{e=requestAnimationFrame((()=>{let e=t.offsetHeight;n.style.setProperty("--cmdk-list-height",e.toFixed(1)+"px")}))}));return r.observe(t),()=>{cancelAnimationFrame(e),r.unobserve(t)}}}),[]),b.createElement(I.div,{ref:an([a,t]),...o,"cmdk-list":"",role:"listbox","aria-label":r,id:u.listId},sn(e,(e=>b.createElement("div",{ref:an([c,u.listInnerRef]),"cmdk-list-sizer":""},e))))})),Yt=b.forwardRef(((e,t)=>{let{open:n,onOpenChange:r,overlayClassName:o,contentClassName:a,container:c,...u}=e;return b.createElement(At,{open:n,onOpenChange:r},b.createElement(Nt,{container:c},b.createElement(Lt,{"cmdk-overlay":"",className:o}),b.createElement(Mt,{"aria-label":e.label,"cmdk-dialog":"",className:a},b.createElement(Vt,{ref:t,...u}))))})),Zt=b.forwardRef(((e,t)=>cn((e=>0===e.filtered.count))?b.createElement(I.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null)),Jt=b.forwardRef(((e,t)=>{let{progress:n,children:r,label:o="Loading...",...a}=e;return b.createElement(I.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},sn(e,(e=>b.createElement("div",{"aria-hidden":!0},e))))})),Qt=Object.assign(Vt,{List:Xt,Item:qt,Input:Ht,Group:zt,Separator:Gt,Dialog:Yt,Empty:Zt,Loading:Jt});function en(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}function tn(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}function nn(e){let t=b.useRef(e);return rn((()=>{t.current=e})),t}var rn="undefined"==typeof window?b.useEffect:b.useLayoutEffect;function on(e){let t=b.useRef();return void 0===t.current&&(t.current=e()),t}function an(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function cn(e){let t=Bt(),n=()=>e(t.snapshot());return b.useSyncExternalStore(t.subscribe,n,n)}function un(e,t,n,r=[]){let o=b.useRef(),a=Ut();return rn((()=>{var c;let u=(()=>{var e;for(let t of n){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():o.current}})(),i=r.map((e=>e.trim()));a.value(e,u,i),null==(c=t.current)||c.setAttribute(jt,u),o.current=u})),o}var ln=()=>{let[e,t]=b.useState(),n=on((()=>new Map));return rn((()=>{n.current.forEach((e=>e())),n.current=new Map}),[e]),(e,r)=>{n.current.set(e,r),t({})}};function sn({asChild:e,children:t},n){return e&&b.isValidElement(t)?b.cloneElement(function(e){let t=e.type;return"function"==typeof t?t(e.props):"render"in t?t.render(e.props):e}(t),{ref:t.ref},n(t.props.children)):n(t)}var dn={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function fn(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=fn(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const mn=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=fn(e))&&(r&&(r+=" "),r+=t);return r},vn=window.wp.data,pn=window.wp.element,hn=window.wp.i18n,gn=window.wp.components,bn=window.wp.keyboardShortcuts;const En=(0,pn.forwardRef)((function({icon:e,size:t=24,...n},r){return(0,pn.cloneElement)(e,{width:t,height:t,...n,ref:r})})),yn=window.wp.primitives,wn=window.ReactJSXRuntime,Cn=(0,wn.jsx)(yn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,wn.jsx)(yn.Path,{d:"M13 5c-3.3 0-6 2.7-6 6 0 1.4.5 2.7 1.3 3.7l-3.8 3.8 1.1 1.1 3.8-3.8c1 .8 2.3 1.3 3.7 1.3 3.3 0 6-2.7 6-6S16.3 5 13 5zm0 10.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5z"})});const Sn=(0,vn.combineReducers)({commands:function(e={},t){switch(t.type){case"REGISTER_COMMAND":return{...e,[t.name]:{name:t.name,label:t.label,searchLabel:t.searchLabel,context:t.context,callback:t.callback,icon:t.icon}};case"UNREGISTER_COMMAND":{const{[t.name]:n,...r}=e;return r}}return e},commandLoaders:function(e={},t){switch(t.type){case"REGISTER_COMMAND_LOADER":return{...e,[t.name]:{name:t.name,context:t.context,hook:t.hook}};case"UNREGISTER_COMMAND_LOADER":{const{[t.name]:n,...r}=e;return r}}return e},isOpen:function(e=!1,t){switch(t.type){case"OPEN":return!0;case"CLOSE":return!1}return e},context:function(e="root",t){return"SET_CONTEXT"===t.type?t.context:e}});function xn(e){return{type:"REGISTER_COMMAND",...e}}function On(e){return{type:"UNREGISTER_COMMAND",name:e}}function Rn(e){return{type:"REGISTER_COMMAND_LOADER",...e}}function kn(e){return{type:"UNREGISTER_COMMAND_LOADER",name:e}}function An(){return{type:"OPEN"}}function Nn(){return{type:"CLOSE"}}const Ln=(0,vn.createSelector)(((e,t=!1)=>Object.values(e.commands).filter((n=>{const r=n.context&&n.context===e.context;return t?r:!r}))),(e=>[e.commands,e.context])),Mn=(0,vn.createSelector)(((e,t=!1)=>Object.values(e.commandLoaders).filter((n=>{const r=n.context&&n.context===e.context;return t?r:!r}))),(e=>[e.commandLoaders,e.context]));function _n(e){return e.isOpen}function Tn(e){return e.context}function Dn(e){return{type:"SET_CONTEXT",context:e}}const Pn=window.wp.privateApis,{lock:In,unlock:jn}=(0,Pn.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/commands"),Fn=(0,vn.createReduxStore)("core/commands",{reducer:Sn,actions:c,selectors:u});(0,vn.register)(Fn),jn(Fn).registerPrivateActions(i);const Wn=(0,hn.__)("Search commands and settings");function Un({name:e,search:t,hook:n,setLoader:r,close:o}){var a;const{isLoading:c,commands:u=[]}=null!==(a=n({search:t}))&&void 0!==a?a:{};return(0,pn.useEffect)((()=>{r(e,c)}),[r,e,c]),u.length?(0,wn.jsx)(wn.Fragment,{children:u.map((e=>{var n;return(0,wn.jsx)(Qt.Item,{value:null!==(n=e.searchLabel)&&void 0!==n?n:e.label,onSelect:()=>e.callback({close:o}),id:e.name,children:(0,wn.jsxs)(gn.__experimentalHStack,{alignment:"left",className:mn("commands-command-menu__item",{"has-icon":e.icon}),children:[e.icon&&(0,wn.jsx)(En,{icon:e.icon}),(0,wn.jsx)("span",{children:(0,wn.jsx)(gn.TextHighlight,{text:e.label,highlight:t})})]})},e.name)}))}):null}function $n({hook:e,search:t,setLoader:n,close:r}){const o=(0,pn.useRef)(e),[a,c]=(0,pn.useState)(0);return(0,pn.useEffect)((()=>{o.current!==e&&(o.current=e,c((e=>e+1)))}),[e]),(0,wn.jsx)(Un,{hook:o.current,search:t,setLoader:n,close:r},a)}function Bn({isContextual:e,search:t,setLoader:n,close:r}){const{commands:o,loaders:a}=(0,vn.useSelect)((t=>{const{getCommands:n,getCommandLoaders:r}=t(Fn);return{commands:n(e),loaders:r(e)}}),[e]);return o.length||a.length?(0,wn.jsxs)(Qt.Group,{children:[o.map((e=>{var n;return(0,wn.jsx)(Qt.Item,{value:null!==(n=e.searchLabel)&&void 0!==n?n:e.label,onSelect:()=>e.callback({close:r}),id:e.name,children:(0,wn.jsxs)(gn.__experimentalHStack,{alignment:"left",className:mn("commands-command-menu__item",{"has-icon":e.icon}),children:[e.icon&&(0,wn.jsx)(En,{icon:e.icon}),(0,wn.jsx)("span",{children:(0,wn.jsx)(gn.TextHighlight,{text:e.label,highlight:t})})]})},e.name)})),a.map((e=>(0,wn.jsx)($n,{hook:e.hook,search:t,setLoader:n,close:r},e.name)))]}):null}function Kn({isOpen:e,search:t,setSearch:n}){const r=(0,pn.useRef)(),o=cn((e=>e.value)),a=(0,pn.useMemo)((()=>{const e=document.querySelector(`[cmdk-item=""][data-value="${o}"]`);return e?.getAttribute("id")}),[o]);return(0,pn.useEffect)((()=>{e&&r.current.focus()}),[e]),(0,wn.jsx)(Qt.Input,{ref:r,value:t,onValueChange:n,placeholder:Wn,"aria-activedescendant":a,icon:t})}function Vn(){const{registerShortcut:e}=(0,vn.useDispatch)(bn.store),[t,n]=(0,pn.useState)(""),r=(0,vn.useSelect)((e=>e(Fn).isOpen()),[]),{open:o,close:a}=(0,vn.useDispatch)(Fn),[c,u]=(0,pn.useState)({});(0,pn.useEffect)((()=>{e({name:"core/commands",category:"global",description:(0,hn.__)("Open the command palette."),keyCombination:{modifier:"primary",character:"k"}})}),[e]),(0,bn.useShortcut)("core/commands",(e=>{e.defaultPrevented||(e.preventDefault(),r?a():o())}),{bindGlobal:!0});const i=(0,pn.useCallback)(((e,t)=>u((n=>({...n,[e]:t})))),[]),l=()=>{n(""),a()};if(!r)return!1;const s=Object.values(c).some(Boolean);return(0,wn.jsx)(gn.Modal,{className:"commands-command-menu",overlayClassName:"commands-command-menu__overlay",onRequestClose:l,__experimentalHideHeader:!0,contentLabel:(0,hn.__)("Command palette"),children:(0,wn.jsx)("div",{className:"commands-command-menu__container",children:(0,wn.jsxs)(Qt,{label:Wn,onKeyDown:e=>{(e.nativeEvent.isComposing||229===e.keyCode)&&e.preventDefault()},children:[(0,wn.jsxs)("div",{className:"commands-command-menu__header",children:[(0,wn.jsx)(Kn,{search:t,setSearch:n,isOpen:r}),(0,wn.jsx)(En,{icon:Cn})]}),(0,wn.jsxs)(Qt.List,{label:(0,hn.__)("Command suggestions"),children:[t&&!s&&(0,wn.jsx)(Qt.Empty,{children:(0,hn.__)("No results found.")}),(0,wn.jsx)(Bn,{search:t,setLoader:i,close:l,isContextual:!0}),t&&(0,wn.jsx)(Bn,{search:t,setLoader:i,close:l})]})]})})})}const qn={};function zn(e){const{registerCommand:t,unregisterCommand:n}=(0,vn.useDispatch)(Fn),r=(0,pn.useRef)(e.callback);(0,pn.useEffect)((()=>{r.current=e.callback}),[e.callback]),(0,pn.useEffect)((()=>{if(!e.disabled)return t({name:e.name,context:e.context,label:e.label,searchLabel:e.searchLabel,icon:e.icon,callback:(...e)=>r.current(...e)}),()=>{n(e.name)}}),[e.name,e.label,e.searchLabel,e.icon,e.context,e.disabled,t,n])}function Gn(e){const{registerCommandLoader:t,unregisterCommandLoader:n}=(0,vn.useDispatch)(Fn);(0,pn.useEffect)((()=>{if(!e.disabled)return t({name:e.name,hook:e.hook,context:e.context}),()=>{n(e.name)}}),[e.name,e.hook,e.context,e.disabled,t,n])}In(qn,{useCommandContext:function(e){const{getContext:t}=(0,vn.useSelect)(Fn),n=(0,pn.useRef)(t()),{setContext:r}=jn((0,vn.useDispatch)(Fn));(0,pn.useEffect)((()=>{r(e)}),[e,r]),(0,pn.useEffect)((()=>{const e=n.current;return()=>r(e)}),[r])}}),(window.wp=window.wp||{}).commands=a})();
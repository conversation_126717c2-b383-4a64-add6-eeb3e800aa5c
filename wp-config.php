<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'swifmind_local' );

/** Database username */
define( 'DB_USER', 'root' );

/** Database password */
define( 'DB_PASSWORD', '' );

/** Database hostname */
define( 'DB_HOST', 'localhost' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         'N?lte-]u@glHWztWAS^*Qp.Xbog6``FIS)=@>md^_^57U3::ru.1a:CceSV1yP4b' );
define( 'SECURE_AUTH_KEY',  'Z,#<RN+,cFT.JTrNC9x4T4&^#<a]w^KES)7y4:FRrY]:iszIH6eT1;dvv?yi/ZyD' );
define( 'LOGGED_IN_KEY',    '!y-2d4bhO#L^VAIs,.v<pp6PgB imhL%Fa3#i$)i-Bl9qj,<&`bI9y}!V~U~R5Be' );
define( 'NONCE_KEY',        '.=F)KDL5&9%uR5E/5Hd[@s#]Zb7->/Z.-g|ESYaHY`.9<Gu`s>EIkc0pN:jAv^5_' );
define( 'AUTH_SALT',        'Go$tZk]d7;Er^Cr-<Af$A;gRqylVj:|Na KS0W8ZWAH+x L(RN}UK~MDoN)?cnM:' );
define( 'SECURE_AUTH_SALT', '{gs;^q<T439#vy0[@O=ZM+W6:6buowqWvcs?HykGFH*A|BF~Z`4r~q%,&[{1(~G0' );
define( 'LOGGED_IN_SALT',   '-=5Q$0hlGeC~;##4OhLJ}@tIT:zwv4fmb,Z+|?s(DTb;u^cO~0>!^VH?+G0$tybk' );
define( 'NONCE_SALT',       '+K>(;IMB>TBB*#xI;e;rlU*X$$uy$%IULn1>DTvet>fZrw_=`,IhaFW<8,PJQT?r' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'wp_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );

/* Add any custom values between this line and the "stop editing" line. */

// ChatGABI Performance Optimization Settings
ini_set('max_execution_time', 300);
ini_set('memory_limit', '512M');
define('WP_MEMORY_LIMIT', '512M');
define('WP_MAX_MEMORY_LIMIT', '512M');

// BusinessCraft AI API Keys - Secure Environment Variable Implementation
// Use environment variables with encrypted fallback for security
define('BUSINESSCRAFT_AI_OPENAI_API_KEY',
    $_ENV['BUSINESSCRAFT_AI_OPENAI_API_KEY'] ??
    getenv('BUSINESSCRAFT_AI_OPENAI_API_KEY') ??
    businesscraft_ai_get_encrypted_api_key('openai')
);

define('BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY',
    $_ENV['BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY'] ??
    getenv('BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY') ??
    businesscraft_ai_get_encrypted_api_key('paystack_public')
);

define('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY',
    $_ENV['BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY'] ??
    getenv('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ??
    businesscraft_ai_get_encrypted_api_key('paystack_secret')
);

// Security: Enable API key rotation and monitoring
define('BUSINESSCRAFT_AI_API_KEY_ROTATION_ENABLED', true);
define('BUSINESSCRAFT_AI_API_MONITORING_ENABLED', true);

// Redis Configuration for ChatGABI Performance Enhancement
define('CHATGABI_REDIS_HOST', $_ENV['CHATGABI_REDIS_HOST'] ?? '127.0.0.1');
define('CHATGABI_REDIS_PORT', $_ENV['CHATGABI_REDIS_PORT'] ?? 6379);
define('CHATGABI_REDIS_PASSWORD', $_ENV['CHATGABI_REDIS_PASSWORD'] ?? null);
define('CHATGABI_REDIS_DATABASE', $_ENV['CHATGABI_REDIS_DATABASE'] ?? 0);

// Performance Settings
define('CHATGABI_ENABLE_RESPONSE_STREAMING', true);
define('CHATGABI_ENABLE_QUERY_CACHING', true);
define('CHATGABI_CACHE_DEFAULT_TTL', 3600); // 1 hour


/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';

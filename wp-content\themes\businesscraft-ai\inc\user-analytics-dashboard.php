<?php
/**
 * User Analytics Dashboard
 * 
 * Enhanced analytics system for individual user insights
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get comprehensive user analytics data
 */
function businesscraft_ai_get_user_analytics($user_id, $period = 30) {
    global $wpdb;
    
    if (!$user_id) {
        return array(
            'summary' => array(),
            'charts' => array(),
            'insights' => array(),
            'activity' => array()
        );
    }
    
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    $date_to = date('Y-m-d');
    
    // Get summary statistics
    $summary = businesscraft_ai_get_analytics_summary($user_id, $date_from, $date_to);
    
    // Get chart data
    $charts = array(
        'usage_over_time' => businesscraft_ai_get_usage_timeline($user_id, $period),
        'feature_distribution' => businesscraft_ai_get_feature_usage($user_id, $period),
        'credit_usage' => businesscraft_ai_get_credit_usage_chart($user_id, $period)
    );
    
    // Get insights
    $insights = businesscraft_ai_get_user_insights($user_id, $period);
    
    // Get recent activity
    $activity = businesscraft_ai_get_user_activity_timeline($user_id, 20);
    
    return array(
        'summary' => $summary,
        'charts' => $charts,
        'insights' => $insights,
        'activity' => $activity
    );
}

/**
 * Get analytics summary statistics
 */
function businesscraft_ai_get_analytics_summary($user_id, $date_from, $date_to) {
    global $wpdb;
    
    // Get current period stats
    $conversations_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $credits_table = $wpdb->prefix . 'businesscraft_ai_credit_transactions';
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $sector_logs_table = $wpdb->prefix . 'chatgabi_sector_logs';
    
    $current_conversations = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} 
         WHERE user_id = %d AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $date_from, $date_to
    )) ?: 0;
    
    $current_credits_used = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(ABS(credits)) FROM {$credits_table} 
         WHERE user_id = %d AND transaction_type = 'usage' 
         AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $date_from, $date_to
    )) ?: 0;
    
    $current_templates = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$templates_table} 
         WHERE user_id = %d AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $date_from, $date_to
    )) ?: 0;
    
    $current_opportunities = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT opportunity_id) FROM {$sector_logs_table} 
         WHERE user_id = %d AND opportunity_id IS NOT NULL 
         AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $date_from, $date_to
    )) ?: 0;
    
    // Get previous period for comparison
    $prev_date_from = date('Y-m-d', strtotime("-" . (2 * intval(str_replace('-', '', $date_to) - str_replace('-', '', $date_from))) . " days"));
    $prev_date_to = date('Y-m-d', strtotime($date_from . ' -1 day'));
    
    $prev_conversations = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} 
         WHERE user_id = %d AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $prev_date_from, $prev_date_to
    )) ?: 0;
    
    $prev_credits_used = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(ABS(credits)) FROM {$credits_table} 
         WHERE user_id = %d AND transaction_type = 'usage' 
         AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $prev_date_from, $prev_date_to
    )) ?: 0;
    
    $prev_templates = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$templates_table} 
         WHERE user_id = %d AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $prev_date_from, $prev_date_to
    )) ?: 0;
    
    $prev_opportunities = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT opportunity_id) FROM {$sector_logs_table} 
         WHERE user_id = %d AND opportunity_id IS NOT NULL 
         AND DATE(created_at) BETWEEN %s AND %s",
        $user_id, $prev_date_from, $prev_date_to
    )) ?: 0;
    
    return array(
        'conversations' => array(
            'current' => $current_conversations,
            'trend' => businesscraft_ai_calculate_trend($current_conversations, $prev_conversations)
        ),
        'credits_used' => array(
            'current' => $current_credits_used,
            'trend' => businesscraft_ai_calculate_trend($current_credits_used, $prev_credits_used)
        ),
        'templates' => array(
            'current' => $current_templates,
            'trend' => businesscraft_ai_calculate_trend($current_templates, $prev_templates)
        ),
        'opportunities' => array(
            'current' => $current_opportunities,
            'trend' => businesscraft_ai_calculate_trend($current_opportunities, $prev_opportunities)
        )
    );
}

/**
 * Calculate trend percentage
 */
function businesscraft_ai_calculate_trend($current, $previous) {
    if ($previous == 0) {
        return $current > 0 ? 100 : 0;
    }
    
    $change = (($current - $previous) / $previous) * 100;
    return round($change, 1);
}

/**
 * Get usage timeline data
 */
function businesscraft_ai_get_usage_timeline($user_id, $period = 30) {
    global $wpdb;
    
    $conversations_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT DATE(created_at) as date, COUNT(*) as count
         FROM {$conversations_table}
         WHERE user_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
         GROUP BY DATE(created_at)
         ORDER BY date ASC",
        $user_id, $period
    ));
    
    $timeline = array();
    for ($i = $period - 1; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-{$i} days"));
        $timeline[$date] = 0;
    }
    
    foreach ($results as $result) {
        $timeline[$result->date] = intval($result->count);
    }
    
    return array(
        'labels' => array_keys($timeline),
        'data' => array_values($timeline)
    );
}

/**
 * Get feature usage distribution
 */
function businesscraft_ai_get_feature_usage($user_id, $period = 30) {
    global $wpdb;
    
    $conversations_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT 
            CASE 
                WHEN message LIKE '%business plan%' THEN 'Business Plans'
                WHEN message LIKE '%marketing%' THEN 'Marketing Strategy'
                WHEN message LIKE '%financial%' OR message LIKE '%forecast%' THEN 'Financial Planning'
                WHEN message LIKE '%opportunity%' THEN 'Opportunities'
                WHEN message LIKE '%template%' THEN 'Templates'
                ELSE 'General Chat'
            END as feature,
            COUNT(*) as count
         FROM {$conversations_table}
         WHERE user_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
         GROUP BY feature
         ORDER BY count DESC",
        $user_id, $period
    ));
    
    $features = array();
    $counts = array();
    
    foreach ($results as $result) {
        $features[] = $result->feature;
        $counts[] = intval($result->count);
    }
    
    return array(
        'labels' => $features,
        'data' => $counts
    );
}

/**
 * Get credit usage chart data
 */
function businesscraft_ai_get_credit_usage_chart($user_id, $period = 30) {
    global $wpdb;
    
    $credits_table = $wpdb->prefix . 'businesscraft_ai_credit_transactions';
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT DATE(created_at) as date, 
                SUM(CASE WHEN credits > 0 THEN credits ELSE 0 END) as earned,
                SUM(CASE WHEN credits < 0 THEN ABS(credits) ELSE 0 END) as spent
         FROM {$credits_table}
         WHERE user_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
         GROUP BY DATE(created_at)
         ORDER BY date ASC",
        $user_id, $period
    ));
    
    $timeline = array();
    $earned = array();
    $spent = array();
    
    for ($i = $period - 1; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-{$i} days"));
        $timeline[] = $date;
        $earned[$date] = 0;
        $spent[$date] = 0;
    }
    
    foreach ($results as $result) {
        $earned[$result->date] = intval($result->earned);
        $spent[$result->date] = intval($result->spent);
    }
    
    return array(
        'labels' => $timeline,
        'earned' => array_values($earned),
        'spent' => array_values($spent)
    );
}

/**
 * Get user insights
 */
function businesscraft_ai_get_user_insights($user_id, $period = 30) {
    global $wpdb;
    
    $conversations_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    
    // Most productive day
    $productive_day = $wpdb->get_var($wpdb->prepare(
        "SELECT DAYNAME(created_at) as day
         FROM {$conversations_table}
         WHERE user_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
         GROUP BY DAYOFWEEK(created_at), DAYNAME(created_at)
         ORDER BY COUNT(*) DESC
         LIMIT 1",
        $user_id, $period
    )) ?: 'Monday';
    
    // Favorite feature
    $favorite_feature = $wpdb->get_var($wpdb->prepare(
        "SELECT 
            CASE 
                WHEN message LIKE '%business plan%' THEN 'Business Planning'
                WHEN message LIKE '%marketing%' THEN 'Marketing Strategy'
                WHEN message LIKE '%financial%' THEN 'Financial Planning'
                WHEN message LIKE '%opportunity%' THEN 'Opportunity Discovery'
                ELSE 'General Consultation'
            END as feature
         FROM {$conversations_table}
         WHERE user_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
         GROUP BY feature
         ORDER BY COUNT(*) DESC
         LIMIT 1",
        $user_id, $period
    )) ?: 'General Consultation';
    
    // Calculate growth
    $current_month = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} 
         WHERE user_id = %d AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
        $user_id
    )) ?: 0;
    
    $previous_month = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} 
         WHERE user_id = %d AND created_at BETWEEN DATE_SUB(NOW(), INTERVAL 60 DAY) AND DATE_SUB(NOW(), INTERVAL 30 DAY)",
        $user_id
    )) ?: 0;
    
    $growth = businesscraft_ai_calculate_trend($current_month, $previous_month);
    
    // Calculate efficiency score (conversations per credit used)
    $total_conversations = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$conversations_table} WHERE user_id = %d",
        $user_id
    )) ?: 0;
    
    $total_credits_used = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(ABS(credits)) FROM {$wpdb->prefix}businesscraft_ai_credit_transactions 
         WHERE user_id = %d AND transaction_type = 'usage'",
        $user_id
    )) ?: 1;
    
    $efficiency = round(($total_conversations / $total_credits_used) * 100, 1);
    
    return array(
        'most_productive_day' => $productive_day,
        'favorite_feature' => $favorite_feature,
        'monthly_growth' => $growth . '%',
        'efficiency_score' => $efficiency . '%'
    );
}

/**
 * Get user activity timeline
 */
function businesscraft_ai_get_user_activity_timeline($user_id, $limit = 20) {
    global $wpdb;
    
    $conversations_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
    $credits_table = $wpdb->prefix . 'businesscraft_ai_credit_transactions';
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    
    // Get recent conversations
    $conversations = $wpdb->get_results($wpdb->prepare(
        "SELECT 'conversation' as type, created_at, 
                SUBSTRING(message, 1, 100) as description
         FROM {$conversations_table}
         WHERE user_id = %d
         ORDER BY created_at DESC
         LIMIT %d",
        $user_id, $limit
    ));
    
    // Get recent credit transactions
    $credits = $wpdb->get_results($wpdb->prepare(
        "SELECT 'credit' as type, created_at,
                CONCAT(IF(credits > 0, 'Earned ', 'Used '), ABS(credits), ' credits') as description
         FROM {$credits_table}
         WHERE user_id = %d
         ORDER BY created_at DESC
         LIMIT %d",
        $user_id, $limit
    ));
    
    // Get recent templates
    $templates = $wpdb->get_results($wpdb->prepare(
        "SELECT 'template' as type, created_at,
                CONCAT('Created template: ', title) as description
         FROM {$templates_table}
         WHERE user_id = %d
         ORDER BY created_at DESC
         LIMIT %d",
        $user_id, $limit
    ));
    
    // Combine and sort all activities
    $activities = array_merge($conversations, $credits, $templates);
    
    usort($activities, function($a, $b) {
        return strtotime($b->created_at) - strtotime($a->created_at);
    });
    
    $timeline = array();
    foreach (array_slice($activities, 0, $limit) as $activity) {
        $timeline[] = array(
            'type' => $activity->type,
            'description' => $activity->description,
            'time_ago' => human_time_diff(strtotime($activity->created_at), current_time('timestamp')) . ' ago',
            'icon' => businesscraft_ai_get_activity_icon($activity->type)
        );
    }
    
    return $timeline;
}

/**
 * Get activity icon
 */
function businesscraft_ai_get_activity_icon($type) {
    $icons = array(
        'conversation' => '💬',
        'credit' => '⚡',
        'template' => '📝',
        'opportunity' => '🎯',
        'export' => '📄'
    );
    
    return $icons[$type] ?? '📌';
}

/**
 * AJAX handler for user analytics data
 */
function businesscraft_ai_ajax_get_user_analytics() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_feedback_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $period = intval($_POST['period'] ?? 30);
    $analytics = businesscraft_ai_get_user_analytics($user_id, $period);
    
    wp_send_json_success($analytics);
}
add_action('wp_ajax_businesscraft_ai_get_user_analytics', 'businesscraft_ai_ajax_get_user_analytics');

/**
 * Response Streaming Client for ChatGABI
 * Handles Server-Sent Events (SSE) for real-time AI responses
 */

class ChatGABIStreamingClient {
    constructor(options = {}) {
        this.options = {
            streamEndpoint: businesscraftAI.streamUrl || '/wp-json/bcai/v1/stream/chat',
            reconnectInterval: 3000,
            maxReconnectAttempts: 3,
            ...options
        };
        
        this.eventSource = null;
        this.reconnectAttempts = 0;
        this.isStreaming = false;
        this.currentStreamId = null;
        this.responseBuffer = '';
        
        this.init();
    }
    
    init() {
        // Bind methods
        this.handleStreamEvent = this.handleStreamEvent.bind(this);
        this.handleStreamError = this.handleStreamError.bind(this);
        this.handleStreamOpen = this.handleStreamOpen.bind(this);
        
        // Check for streaming support
        if (!window.EventSource) {
            console.warn('Server-Sent Events not supported, falling back to regular requests');
            this.streamingSupported = false;
        } else {
            this.streamingSupported = true;
        }
    }
    
    /**
     * Start streaming chat request
     */
    async startStreamingChat(message, language = 'en', context = 'general') {
        if (!this.streamingSupported) {
            return this.fallbackToRegularRequest(message, language, context);
        }
        
        try {
            // Close any existing stream
            this.closeStream();
            
            // Show loading state
            this.showStreamingIndicator();
            
            // Start new stream
            const streamUrl = this.buildStreamUrl(message, language, context);
            this.eventSource = new EventSource(streamUrl);
            
            // Set up event listeners
            this.setupEventListeners();
            
            this.isStreaming = true;
            this.responseBuffer = '';
            
        } catch (error) {
            console.error('Failed to start streaming:', error);
            this.handleStreamError(error);
        }
    }
    
    /**
     * Build stream URL with parameters
     */
    buildStreamUrl(message, language, context) {
        const params = new URLSearchParams({
            message: message,
            language: language,
            context: context,
            stream: 'true',
            _wpnonce: businesscraftAI.nonce
        });
        
        return `${this.options.streamEndpoint}?${params.toString()}`;
    }
    
    /**
     * Setup event listeners for SSE
     */
    setupEventListeners() {
        this.eventSource.onopen = this.handleStreamOpen;
        this.eventSource.onerror = this.handleStreamError;
        
        // Custom event listeners
        this.eventSource.addEventListener('stream_start', this.handleStreamEvent);
        this.eventSource.addEventListener('progress', this.handleStreamEvent);
        this.eventSource.addEventListener('typing_start', this.handleStreamEvent);
        this.eventSource.addEventListener('chunk', this.handleStreamEvent);
        this.eventSource.addEventListener('response_complete', this.handleStreamEvent);
        this.eventSource.addEventListener('stream_end', this.handleStreamEvent);
        this.eventSource.addEventListener('error', this.handleStreamEvent);
    }
    
    /**
     * Handle stream events
     */
    handleStreamEvent(event) {
        try {
            const data = JSON.parse(event.data);
            
            switch (event.type) {
                case 'stream_start':
                    this.handleStreamStart(data);
                    break;
                    
                case 'progress':
                    this.handleProgress(data);
                    break;
                    
                case 'typing_start':
                    this.handleTypingStart(data);
                    break;
                    
                case 'chunk':
                    this.handleChunk(data);
                    break;
                    
                case 'response_complete':
                    this.handleResponseComplete(data);
                    break;
                    
                case 'stream_end':
                    this.handleStreamEnd(data);
                    break;
                    
                case 'error':
                    this.handleStreamError(data);
                    break;
            }
            
        } catch (error) {
            console.error('Error parsing stream event:', error);
        }
    }
    
    /**
     * Handle stream start
     */
    handleStreamStart(data) {
        this.currentStreamId = data.stream_id;
        this.updateStreamingStatus('Connecting to AI...', 'connecting');
        
        // Trigger custom event
        this.dispatchCustomEvent('streamStart', data);
    }
    
    /**
     * Handle progress updates
     */
    handleProgress(data) {
        this.updateStreamingStatus(data.message, 'progress', data.progress);
        this.updateProgressBar(data.progress);
        
        // Trigger custom event
        this.dispatchCustomEvent('streamProgress', data);
    }
    
    /**
     * Handle typing indicator
     */
    handleTypingStart(data) {
        this.updateStreamingStatus('AI is thinking...', 'thinking');
        this.showTypingIndicator();
        
        // Trigger custom event
        this.dispatchCustomEvent('streamTyping', data);
    }
    
    /**
     * Handle response chunks
     */
    handleChunk(data) {
        this.responseBuffer += data.content;
        this.appendToResponse(data.content);
        
        // Update progress based on chunks
        const progress = Math.round((data.chunk_index + 1) / data.total_chunks * 100);
        this.updateProgressBar(progress);
        
        // Trigger custom event
        this.dispatchCustomEvent('streamChunk', data);
    }
    
    /**
     * Handle response complete
     */
    handleResponseComplete(data) {
        this.hideTypingIndicator();
        this.updateStreamingStatus('Response complete', 'complete');
        
        // Display metadata
        this.displayResponseMetadata(data.metadata);
        
        // Trigger custom event
        this.dispatchCustomEvent('streamComplete', data);
    }
    
    /**
     * Handle stream end
     */
    handleStreamEnd(data) {
        this.hideStreamingIndicator();
        this.closeStream();
        
        // Trigger custom event
        this.dispatchCustomEvent('streamEnd', data);
    }
    
    /**
     * Handle stream errors
     */
    handleStreamError(error) {
        console.error('Stream error:', error);
        
        this.hideStreamingIndicator();
        this.hideTypingIndicator();
        
        if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.updateStreamingStatus(`Connection lost. Reconnecting... (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`, 'reconnecting');
            
            setTimeout(() => {
                this.reconnect();
            }, this.options.reconnectInterval);
        } else {
            this.updateStreamingStatus('Connection failed. Please try again.', 'error');
            this.dispatchCustomEvent('streamError', { error: error });
        }
    }
    
    /**
     * Handle stream open
     */
    handleStreamOpen() {
        this.reconnectAttempts = 0;
        this.updateStreamingStatus('Connected', 'connected');
    }
    
    /**
     * Reconnect to stream
     */
    reconnect() {
        this.closeStream();
        // Note: Would need to store original parameters for reconnection
        // This is a simplified version
    }
    
    /**
     * Close stream
     */
    closeStream() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isStreaming = false;
        this.currentStreamId = null;
    }
    
    /**
     * Fallback to regular request
     */
    async fallbackToRegularRequest(message, language, context) {
        try {
            const response = await fetch(businesscraftAI.restUrl + 'chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': businesscraftAI.nonce
                },
                body: JSON.stringify({
                    message: message,
                    language: language,
                    context: context
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.displayResponse(data.response);
            } else {
                throw new Error(data.message || 'Request failed');
            }
            
        } catch (error) {
            console.error('Fallback request failed:', error);
            this.displayError(error.message);
        }
    }
    
    /**
     * UI Helper Methods
     */
    showStreamingIndicator() {
        const indicator = document.querySelector('.streaming-indicator');
        if (indicator) {
            indicator.style.display = 'block';
        }
    }
    
    hideStreamingIndicator() {
        const indicator = document.querySelector('.streaming-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    showTypingIndicator() {
        const typing = document.querySelector('.typing-indicator');
        if (typing) {
            typing.style.display = 'block';
        }
    }
    
    hideTypingIndicator() {
        const typing = document.querySelector('.typing-indicator');
        if (typing) {
            typing.style.display = 'none';
        }
    }
    
    updateStreamingStatus(message, status, progress = null) {
        const statusElement = document.querySelector('.streaming-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `streaming-status status-${status}`;
        }
    }
    
    updateProgressBar(progress) {
        const progressBar = document.querySelector('.streaming-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }
    
    appendToResponse(content) {
        const responseElement = document.querySelector('.ai-response-content');
        if (responseElement) {
            responseElement.innerHTML += content;
            responseElement.scrollTop = responseElement.scrollHeight;
        }
    }
    
    displayResponse(content) {
        const responseElement = document.querySelector('.ai-response-content');
        if (responseElement) {
            responseElement.innerHTML = content;
        }
    }
    
    displayResponseMetadata(metadata) {
        const metadataElement = document.querySelector('.response-metadata');
        if (metadataElement && metadata) {
            metadataElement.innerHTML = `
                <small>
                    Tokens: ${metadata.tokens_used} | 
                    Model: ${metadata.model} | 
                    Time: ${metadata.processing_time}s
                </small>
            `;
        }
    }
    
    displayError(message) {
        const errorElement = document.querySelector('.ai-response-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }
    
    /**
     * Dispatch custom events
     */
    dispatchCustomEvent(eventName, data) {
        const event = new CustomEvent(`chatgabi:${eventName}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * Get stream status
     */
    async getStreamStatus(streamId) {
        try {
            const response = await fetch(`${businesscraftAI.restUrl}stream/status/${streamId}`, {
                headers: {
                    'X-WP-Nonce': businesscraftAI.nonce
                }
            });

            return await response.json();
        } catch (error) {
            console.error('Failed to get stream status:', error);
            return null;
        }
    }

    /**
     * Cancel current stream
     */
    cancelStream() {
        if (this.isStreaming) {
            this.closeStream();
            this.updateStreamingStatus('Stream cancelled', 'cancelled');
            this.dispatchCustomEvent('streamCancelled', { streamId: this.currentStreamId });
        }
    }
}

// Initialize streaming client when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof businesscraftAI !== 'undefined') {
        window.chatGABIStreaming = new ChatGABIStreamingClient();

        // Integrate with existing chat form
        const chatForm = document.querySelector('.chat-form, .chat-block form, #chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const messageInput = chatForm.querySelector('input[name="message"], textarea[name="message"], #user-message');
                const languageSelect = chatForm.querySelector('select[name="language"], #language-select');

                if (messageInput && messageInput.value.trim()) {
                    const message = messageInput.value.trim();
                    const language = languageSelect ? languageSelect.value : 'en';

                    // Clear previous response
                    const responseElement = document.querySelector('.ai-response-content, .chat-response, #ai-response');
                    if (responseElement) {
                        responseElement.innerHTML = '';
                    }

                    // Clear error messages
                    const errorElement = document.querySelector('.ai-response-error, .chat-error');
                    if (errorElement) {
                        errorElement.style.display = 'none';
                    }

                    // Start streaming
                    window.chatGABIStreaming.startStreamingChat(message, language, 'general');

                    // Clear input
                    messageInput.value = '';
                }
            });
        }

        // Add cancel button functionality
        const cancelButton = document.querySelector('.cancel-stream-btn');
        if (cancelButton) {
            cancelButton.addEventListener('click', function() {
                window.chatGABIStreaming.cancelStream();
            });
        }

        // Listen for custom events
        document.addEventListener('chatgabi:streamStart', function(e) {
            console.log('Stream started:', e.detail);
        });

        document.addEventListener('chatgabi:streamComplete', function(e) {
            console.log('Stream completed:', e.detail);
            // Enable form again
            const submitButton = document.querySelector('.chat-submit-btn, input[type="submit"]');
            if (submitButton) {
                submitButton.disabled = false;
            }
        });

        document.addEventListener('chatgabi:streamError', function(e) {
            console.error('Stream error:', e.detail);
            // Enable form again
            const submitButton = document.querySelector('.chat-submit-btn, input[type="submit"]');
            if (submitButton) {
                submitButton.disabled = false;
            }
        });
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatGABIStreamingClient;
}

<?php
/**
 * Test Email Validation Fix
 * 
 * Specific test for the email validation issue in credit purchase system
 */

// Include WordPress
require_once 'wp-config.php';

// Set up WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Validation Fix Test - BusinessCraft AI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; font-weight: 600; }
        .error { color: #dc3545; font-weight: 600; }
        .warning { color: #ffc107; font-weight: 600; }
        .info { color: #17a2b8; font-weight: 600; }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn:hover { opacity: 0.9; }
        .test-result {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .status-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .status-error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Email Validation Fix Test</h1>
            <p>Testing the fixed email validation logic in credit purchase system</p>
            <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- Current User Status -->
        <div class="test-section">
            <h2>👤 Current User Status</h2>
            
            <div class="status-card <?php echo is_user_logged_in() ? 'status-success' : 'status-warning'; ?>">
                <?php if (is_user_logged_in()): ?>
                    <?php $current_user = wp_get_current_user(); ?>
                    <p class="success">✅ User is logged in</p>
                    <p><strong>User ID:</strong> <?php echo $current_user->ID; ?></p>
                    <p><strong>Username:</strong> <?php echo $current_user->user_login; ?></p>
                    <p><strong>Email:</strong> <?php echo $current_user->user_email; ?></p>
                    <p><strong>Role:</strong> <?php echo implode(', ', $current_user->roles); ?></p>
                    <p><strong>Can Manage Options:</strong> <?php echo current_user_can('manage_options') ? 'Yes (Admin)' : 'No'; ?></p>
                <?php else: ?>
                    <p class="warning">⚠️ No user is logged in</p>
                    <p>To test email validation with a logged-in user, please <a href="<?php echo wp_login_url(get_permalink()); ?>">log in</a> first.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Test 1: Email Validation with User's Own Email -->
        <?php if (is_user_logged_in()): ?>
        <div class="test-section">
            <h2>✅ Test 1: Valid Email (User's Own Email)</h2>
            
            <p>Testing with the current user's registered email address.</p>
            
            <button class="btn btn-success" onclick="testValidEmail()">Test with Valid Email</button>
            <div id="valid-email-result" class="test-result" style="display: none;"></div>
        </div>
        <?php endif; ?>

        <!-- Test 2: Email Validation with Different Email -->
        <div class="test-section">
            <h2>❌ Test 2: Invalid Email (Different Email)</h2>
            
            <p>Testing with a different email address to verify validation works.</p>
            
            <button class="btn btn-warning" onclick="testInvalidEmail()">Test with Different Email</button>
            <div id="invalid-email-result" class="test-result" style="display: none;"></div>
        </div>

        <!-- Test 3: Admin Override Test -->
        <?php if (is_user_logged_in() && current_user_can('manage_options')): ?>
        <div class="test-section">
            <h2>🔑 Test 3: Admin Override (Testing Feature)</h2>
            
            <p>Testing admin override feature that allows admins to use different emails for testing.</p>
            
            <button class="btn btn-primary" onclick="testAdminOverride()">Test Admin Override</button>
            <div id="admin-override-result" class="test-result" style="display: none;"></div>
        </div>
        <?php endif; ?>

        <!-- Test 4: No Authentication Test -->
        <div class="test-section">
            <h2>🚫 Test 4: No Authentication</h2>
            
            <p>Testing behavior when no user is logged in.</p>
            
            <button class="btn btn-primary" onclick="testNoAuth()">Test Without Authentication</button>
            <div id="no-auth-result" class="test-result" style="display: none;"></div>
        </div>

        <!-- Summary -->
        <div class="test-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h2 style="color: white;">📊 Test Summary</h2>
            <div id="test-summary">
                <p>Run the tests above to see the summary here.</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};

        <?php if (is_user_logged_in()): ?>
        function testValidEmail() {
            const resultDiv = document.getElementById('valid-email-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing with user\'s own email...';

            const userEmail = '<?php echo wp_get_current_user()->user_email; ?>';
            
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=businesscraft_ai_initiate_payment&nonce=<?php echo wp_create_nonce('chatgabi_feedback_nonce'); ?>&package=starter&email=${userEmail}`
            })
            .then(response => response.text())
            .then(data => {
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success === false) {
                        if (jsonData.data && jsonData.data.includes('Payment system not configured')) {
                            testResults.validEmail = 'success';
                            resultDiv.innerHTML = `
                                <strong>✅ Valid Email Test - PASSED</strong><br>
                                Email validation passed, stopped at Paystack configuration (expected)<br>
                                User Email: ${userEmail}<br>
                                Response: ${data}
                            `;
                        } else {
                            testResults.validEmail = 'error';
                            resultDiv.innerHTML = `
                                <strong>❌ Valid Email Test - FAILED</strong><br>
                                Unexpected error: ${jsonData.data}<br>
                                Response: ${data}
                            `;
                        }
                    } else {
                        testResults.validEmail = 'warning';
                        resultDiv.innerHTML = `
                            <strong>⚠️ Valid Email Test - UNEXPECTED SUCCESS</strong><br>
                            Response: ${data}
                        `;
                    }
                } catch (e) {
                    testResults.validEmail = 'error';
                    resultDiv.innerHTML = `
                        <strong>❌ Valid Email Test - PARSE ERROR</strong><br>
                        Error: ${e.message}<br>
                        Response: ${data}
                    `;
                }
                updateSummary();
            })
            .catch(error => {
                testResults.validEmail = 'error';
                resultDiv.innerHTML = `<strong>❌ Valid Email Test - NETWORK ERROR</strong><br>${error.message}`;
                updateSummary();
            });
        }
        <?php endif; ?>

        function testInvalidEmail() {
            const resultDiv = document.getElementById('invalid-email-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing with different email...';

            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=businesscraft_ai_initiate_payment&nonce=<?php echo wp_create_nonce('chatgabi_feedback_nonce'); ?>&package=starter&email=<EMAIL>'
            })
            .then(response => response.text())
            .then(data => {
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success === false) {
                        if (jsonData.data && (jsonData.data.includes('Email mismatch') || jsonData.data.includes('User not logged in'))) {
                            testResults.invalidEmail = 'success';
                            resultDiv.innerHTML = `
                                <strong>✅ Invalid Email Test - PASSED</strong><br>
                                Email validation working correctly<br>
                                Response: ${data}
                            `;
                        } else {
                            testResults.invalidEmail = 'warning';
                            resultDiv.innerHTML = `
                                <strong>⚠️ Invalid Email Test - UNEXPECTED ERROR</strong><br>
                                Response: ${data}
                            `;
                        }
                    } else {
                        testResults.invalidEmail = 'error';
                        resultDiv.innerHTML = `
                            <strong>❌ Invalid Email Test - FAILED</strong><br>
                            Should have rejected different email<br>
                            Response: ${data}
                        `;
                    }
                } catch (e) {
                    testResults.invalidEmail = 'error';
                    resultDiv.innerHTML = `
                        <strong>❌ Invalid Email Test - PARSE ERROR</strong><br>
                        Error: ${e.message}<br>
                        Response: ${data}
                    `;
                }
                updateSummary();
            })
            .catch(error => {
                testResults.invalidEmail = 'error';
                resultDiv.innerHTML = `<strong>❌ Invalid Email Test - NETWORK ERROR</strong><br>${error.message}`;
                updateSummary();
            });
        }

        <?php if (is_user_logged_in() && current_user_can('manage_options')): ?>
        function testAdminOverride() {
            const resultDiv = document.getElementById('admin-override-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing admin override feature...';

            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=businesscraft_ai_initiate_payment&nonce=<?php echo wp_create_nonce('chatgabi_feedback_nonce'); ?>&package=starter&email=<EMAIL>'
            })
            .then(response => response.text())
            .then(data => {
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success === false) {
                        if (jsonData.data && jsonData.data.includes('Payment system not configured')) {
                            testResults.adminOverride = 'success';
                            resultDiv.innerHTML = `
                                <strong>✅ Admin Override Test - PASSED</strong><br>
                                Admin can use different email, stopped at Paystack configuration (expected)<br>
                                Response: ${data}
                            `;
                        } else if (jsonData.data && jsonData.data.includes('Email mismatch')) {
                            testResults.adminOverride = 'error';
                            resultDiv.innerHTML = `
                                <strong>❌ Admin Override Test - FAILED</strong><br>
                                Admin override not working<br>
                                Response: ${data}
                            `;
                        } else {
                            testResults.adminOverride = 'warning';
                            resultDiv.innerHTML = `
                                <strong>⚠️ Admin Override Test - UNEXPECTED</strong><br>
                                Response: ${data}
                            `;
                        }
                    } else {
                        testResults.adminOverride = 'warning';
                        resultDiv.innerHTML = `
                            <strong>⚠️ Admin Override Test - UNEXPECTED SUCCESS</strong><br>
                            Response: ${data}
                        `;
                    }
                } catch (e) {
                    testResults.adminOverride = 'error';
                    resultDiv.innerHTML = `
                        <strong>❌ Admin Override Test - PARSE ERROR</strong><br>
                        Error: ${e.message}<br>
                        Response: ${data}
                    `;
                }
                updateSummary();
            })
            .catch(error => {
                testResults.adminOverride = 'error';
                resultDiv.innerHTML = `<strong>❌ Admin Override Test - NETWORK ERROR</strong><br>${error.message}`;
                updateSummary();
            });
        }
        <?php endif; ?>

        function testNoAuth() {
            const resultDiv = document.getElementById('no-auth-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing without authentication...';

            // This test simulates what happens when no user is logged in
            // We'll use a different approach to test this
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=businesscraft_ai_initiate_payment&nonce=invalid_nonce&package=starter&email=<EMAIL>'
            })
            .then(response => response.text())
            .then(data => {
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success === false) {
                        if (jsonData.data && (jsonData.data.includes('Security check failed') || jsonData.data.includes('User not logged in'))) {
                            testResults.noAuth = 'success';
                            resultDiv.innerHTML = `
                                <strong>✅ No Auth Test - PASSED</strong><br>
                                Properly rejects unauthenticated requests<br>
                                Response: ${data}
                            `;
                        } else {
                            testResults.noAuth = 'warning';
                            resultDiv.innerHTML = `
                                <strong>⚠️ No Auth Test - UNEXPECTED ERROR</strong><br>
                                Response: ${data}
                            `;
                        }
                    } else {
                        testResults.noAuth = 'error';
                        resultDiv.innerHTML = `
                            <strong>❌ No Auth Test - FAILED</strong><br>
                            Should have rejected unauthenticated request<br>
                            Response: ${data}
                        `;
                    }
                } catch (e) {
                    testResults.noAuth = 'error';
                    resultDiv.innerHTML = `
                        <strong>❌ No Auth Test - PARSE ERROR</strong><br>
                        Error: ${e.message}<br>
                        Response: ${data}
                    `;
                }
                updateSummary();
            })
            .catch(error => {
                testResults.noAuth = 'error';
                resultDiv.innerHTML = `<strong>❌ No Auth Test - NETWORK ERROR</strong><br>${error.message}`;
                updateSummary();
            });
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('test-summary');
            const totalTests = Object.keys(testResults).length;
            const successfulTests = Object.values(testResults).filter(result => result === 'success').length;
            const warningTests = Object.values(testResults).filter(result => result === 'warning').length;
            const errorTests = Object.values(testResults).filter(result => result === 'error').length;
            
            if (totalTests > 0) {
                summaryDiv.innerHTML = `
                    <p><strong>Tests Completed:</strong> ${totalTests}</p>
                    <p><strong>✅ Passed:</strong> ${successfulTests}</p>
                    <p><strong>⚠️ Warnings:</strong> ${warningTests}</p>
                    <p><strong>❌ Failed:</strong> ${errorTests}</p>
                    <p><strong>Overall Status:</strong> ${successfulTests >= totalTests * 0.8 ? '✅ Email validation working correctly' : warningTests > errorTests ? '⚠️ Needs attention' : '❌ Issues detected'}</p>
                    
                    <h3>📋 Test Results:</h3>
                    <ul>
                        ${Object.entries(testResults).map(([test, result]) => {
                            const icon = result === 'success' ? '✅' : result === 'warning' ? '⚠️' : '❌';
                            return `<li>${icon} ${test}: ${result}</li>`;
                        }).join('')}
                    </ul>
                `;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Email Validation Fix Test Page Loaded');
        });
    </script>
</body>
</html>

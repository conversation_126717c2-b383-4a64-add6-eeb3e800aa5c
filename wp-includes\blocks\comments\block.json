{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/comments", "title": "Comments", "category": "theme", "description": "An advanced block that allows displaying post comments using different visual configurations.", "textdomain": "default", "attributes": {"tagName": {"type": "string", "default": "div"}, "legacy": {"type": "boolean", "default": false}}, "supports": {"align": ["wide", "full"], "html": false, "color": {"gradients": true, "heading": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "editorStyle": "wp-block-comments-editor", "usesContext": ["postId", "postType"]}
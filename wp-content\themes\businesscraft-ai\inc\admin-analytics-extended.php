<?php
/**
 * User Engagement Analytics Dashboard
 * Extended analytics for user interaction and opportunity usage metrics
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Engagement Analytics submenu to ChatGABI admin
 */
function chatgabi_add_engagement_analytics_menu() {
    // Add to main ChatGABI menu (primary location)
    add_submenu_page(
        'chatgabi-main',
        __('Engagement Analytics', 'chatgabi'),
        __('Engagement Analytics', 'chatgabi'),
        'manage_options',
        'chatgabi-engagement-analytics',
        'chatgabi_engagement_analytics_page'
    );

    // Also add to Tools menu for compatibility
    add_submenu_page(
        'tools.php?page=chatgabi-tools',
        __('Analytics Dashboard', 'chatgabi'),
        __('Analytics Dashboard', 'chatgabi'),
        'manage_options',
        'chatgabi-analytics-tools',
        'chatgabi_engagement_analytics_page'
    );
}
// DISABLED: Integrated into main ChatGABI admin menu to prevent duplicates
// add_action('admin_menu', 'chatgabi_add_engagement_analytics_menu', 20);

/**
 * Enqueue scripts and styles for engagement analytics
 */
function chatgabi_engagement_analytics_enqueue_scripts($hook) {
    // Check for both menu locations
    $valid_hooks = array(
        'chatgabi_page_chatgabi-engagement-analytics',  // Main menu location
        'tools_page_chatgabi-analytics-tools'           // Tools menu location
    );

    if (!in_array($hook, $valid_hooks)) {
        return;
    }

    // Enqueue Chart.js 4.x
    wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js', array(), '4.4.0', true);
    
    // Enqueue our custom analytics script
    wp_enqueue_script(
        'chatgabi-admin-analytics-extended',
        CHATGABI_THEME_URL . '/assets/js/admin-analytics-extended.js',
        array('jquery', 'chart-js'),
        CHATGABI_VERSION,
        true
    );

    // Enqueue custom styles
    wp_enqueue_style(
        'chatgabi-admin-analytics-extended',
        CHATGABI_THEME_URL . '/assets/css/admin-analytics-extended.css',
        array(),
        CHATGABI_VERSION
    );

    // Localize script with AJAX data
    wp_localize_script('chatgabi-admin-analytics-extended', 'chatgabiAnalytics', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('chatgabi_analytics_nonce'),
        'strings' => array(
            'loading' => __('Loading...', 'chatgabi'),
            'error' => __('Error loading data', 'chatgabi'),
            'noData' => __('No data available', 'chatgabi'),
        ),
    ));
}
add_action('admin_enqueue_scripts', 'chatgabi_engagement_analytics_enqueue_scripts');

/**
 * Main engagement analytics page
 */
function chatgabi_engagement_analytics_page() {
    // Get cached analytics data
    $analytics_data = chatgabi_get_engagement_analytics_data();
    
    ?>
    <div class="wrap">
        <h1><?php _e('ChatGABI Engagement Analytics', 'chatgabi'); ?></h1>
        <p class="description"><?php _e('User interaction and opportunity usage metrics', 'chatgabi'); ?></p>

        <!-- Summary Cards -->
        <div class="engagement-summary-cards">
            <div class="summary-card">
                <h3><?php _e('Total Queries', 'chatgabi'); ?></h3>
                <div class="summary-value"><?php echo number_format($analytics_data['summary']['total_queries']); ?></div>
                <div class="summary-period"><?php _e('Last 30 days', 'chatgabi'); ?></div>
            </div>

            <div class="summary-card">
                <h3><?php _e('Active Countries', 'chatgabi'); ?></h3>
                <div class="summary-value"><?php echo number_format($analytics_data['summary']['active_countries']); ?></div>
                <div class="summary-period"><?php _e('Countries with activity', 'chatgabi'); ?></div>
            </div>

            <div class="summary-card">
                <h3><?php _e('Top Sector', 'chatgabi'); ?></h3>
                <div class="summary-value"><?php echo esc_html($analytics_data['summary']['top_sector']); ?></div>
                <div class="summary-period"><?php _e('Most queried sector', 'chatgabi'); ?></div>
            </div>

            <div class="summary-card">
                <h3><?php _e('Avg Session', 'chatgabi'); ?></h3>
                <div class="summary-value"><?php echo number_format($analytics_data['summary']['avg_session_duration'], 1); ?> min</div>
                <div class="summary-period"><?php _e('Average duration', 'chatgabi'); ?></div>
            </div>

            <div class="summary-card">
                <h3><?php _e('Opportunities Used', 'chatgabi'); ?></h3>
                <div class="summary-value"><?php echo number_format($analytics_data['summary']['opportunities_included']); ?></div>
                <div class="summary-period"><?php _e('In AI responses', 'chatgabi'); ?></div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="engagement-charts-grid">
            <!-- Top Queried Sectors -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Top Queried Sectors', 'chatgabi'); ?></h3>
                    <div class="chart-controls">
                        <select id="sectorCountryFilter">
                            <option value="all"><?php _e('All Countries', 'chatgabi'); ?></option>
                            <option value="Ghana"><?php _e('Ghana', 'chatgabi'); ?></option>
                            <option value="Kenya"><?php _e('Kenya', 'chatgabi'); ?></option>
                            <option value="Nigeria"><?php _e('Nigeria', 'chatgabi'); ?></option>
                            <option value="South Africa"><?php _e('South Africa', 'chatgabi'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="topSectorsChart" width="100%" height="60"></canvas>
                </div>
            </div>

            <!-- Country Usage Breakdown -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Country Usage Breakdown', 'chatgabi'); ?></h3>
                    <div class="chart-info">
                        <span class="chart-period"><?php _e('Last 30 days', 'chatgabi'); ?></span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="countryBreakdownChart" width="100%" height="60"></canvas>
                </div>
            </div>

            <!-- Average Session Duration -->
            <div class="chart-card chart-card-wide">
                <div class="chart-header">
                    <h3><?php _e('Average Session Duration Trends', 'chatgabi'); ?></h3>
                    <div class="chart-controls">
                        <select id="sessionPeriodFilter">
                            <option value="7"><?php _e('Last 7 days', 'chatgabi'); ?></option>
                            <option value="30" selected><?php _e('Last 30 days', 'chatgabi'); ?></option>
                            <option value="90"><?php _e('Last 90 days', 'chatgabi'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="sessionDurationChart" width="100%" height="40"></canvas>
                </div>
            </div>

            <!-- Opportunity Usage -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Opportunity Integration', 'chatgabi'); ?></h3>
                    <div class="chart-info">
                        <span class="chart-period"><?php _e('Success rate', 'chatgabi'); ?></span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="opportunityUsageChart" width="100%" height="60"></canvas>
                </div>
            </div>

            <!-- Keyword Frequency -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Top Keywords', 'chatgabi'); ?></h3>
                    <div class="chart-info">
                        <span class="chart-period"><?php _e('Most frequent terms', 'chatgabi'); ?></span>
                    </div>
                </div>
                <div class="chart-container">
                    <div id="keywordFrequency" class="keyword-cloud">
                        <?php foreach ($analytics_data['keywords'] as $keyword => $count): ?>
                            <span class="keyword-tag" data-count="<?php echo esc_attr($count); ?>">
                                <?php echo esc_html($keyword); ?>
                                <small>(<?php echo number_format($count); ?>)</small>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Future Placeholder: Average Rating -->
            <div class="chart-card chart-placeholder">
                <div class="chart-header">
                    <h3><?php _e('User Feedback', 'chatgabi'); ?></h3>
                    <div class="chart-info">
                        <span class="chart-period"><?php _e('Coming soon', 'chatgabi'); ?></span>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">⭐</div>
                        <p><?php _e('User rating system will be implemented here', 'chatgabi'); ?></p>
                        <p class="placeholder-note"><?php _e('Track user satisfaction and feedback', 'chatgabi'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table Section -->
        <div class="engagement-data-table">
            <div class="table-header">
                <h3><?php _e('Recent Activity', 'chatgabi'); ?></h3>
                <div class="table-controls">
                    <button type="button" class="button" id="refreshData">
                        <?php _e('Refresh Data', 'chatgabi'); ?>
                    </button>
                    <button type="button" class="button" id="exportData">
                        <?php _e('Export CSV', 'chatgabi'); ?>
                    </button>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'chatgabi'); ?></th>
                        <th><?php _e('Country', 'chatgabi'); ?></th>
                        <th><?php _e('Sector', 'chatgabi'); ?></th>
                        <th><?php _e('Context Found', 'chatgabi'); ?></th>
                        <th><?php _e('Opportunities', 'chatgabi'); ?></th>
                        <th><?php _e('Tokens', 'chatgabi'); ?></th>
                    </tr>
                </thead>
                <tbody id="analyticsTableBody">
                    <?php foreach ($analytics_data['recent_activity'] as $activity): ?>
                        <tr>
                            <td><?php echo esc_html(date('M j, Y H:i', strtotime($activity->timestamp))); ?></td>
                            <td><?php echo esc_html($activity->country); ?></td>
                            <td><?php echo esc_html($activity->detected_sector ?: __('Not detected', 'chatgabi')); ?></td>
                            <td>
                                <?php if ($activity->sector_context_found): ?>
                                    <span class="status-success">✓ <?php _e('Yes', 'chatgabi'); ?></span>
                                <?php else: ?>
                                    <span class="status-warning">✗ <?php _e('No', 'chatgabi'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo number_format($activity->opportunities_included); ?></td>
                            <td><?php echo number_format($activity->prompt_tokens_estimated); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Cache Info -->
        <div class="cache-info">
            <p class="description">
                <?php printf(
                    __('Data cached for 10 minutes. Last updated: %s', 'chatgabi'),
                    date('M j, Y H:i:s', $analytics_data['cache_time'])
                ); ?>
            </p>
        </div>
    </div>

    <!-- Hidden data for JavaScript -->
    <script type="application/json" id="analyticsData">
        <?php echo json_encode($analytics_data); ?>
    </script>
    <?php
}

/**
 * Get engagement analytics data with caching
 */
function chatgabi_get_engagement_analytics_data() {
    $cache_key = 'chatgabi_engagement_analytics_data';
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    global $wpdb;

    // Table names
    $sector_logs_table = $wpdb->prefix . 'chatgabi_sector_logs';
    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';

    // Initialize data structure
    $data = array(
        'summary' => array(),
        'top_sectors' => array(),
        'country_breakdown' => array(),
        'session_duration' => array(),
        'opportunity_usage' => array(),
        'keywords' => array(),
        'recent_activity' => array(),
        'cache_time' => time()
    );

    // Check if tables exist
    $sector_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_logs_table}'") === $sector_logs_table;
    $chat_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$chat_logs_table}'") === $chat_logs_table;

    if ($sector_table_exists) {
        // Summary statistics
        $data['summary'] = chatgabi_get_analytics_summary($sector_logs_table);

        // Top queried sectors
        $data['top_sectors'] = chatgabi_get_top_sectors($sector_logs_table);

        // Country breakdown
        $data['country_breakdown'] = chatgabi_get_country_breakdown($sector_logs_table);

        // Opportunity usage
        $data['opportunity_usage'] = chatgabi_get_opportunity_usage($sector_logs_table);

        // Keywords from user messages
        $data['keywords'] = chatgabi_get_keyword_frequency($sector_logs_table);

        // Recent activity
        $data['recent_activity'] = chatgabi_get_recent_activity($sector_logs_table);
    } else {
        // Fallback data when tables don't exist
        $data['summary'] = array(
            'total_queries' => 0,
            'active_countries' => 0,
            'top_sector' => __('No data', 'chatgabi'),
            'avg_session_duration' => 0,
            'opportunities_included' => 0
        );
        $data['top_sectors'] = array();
        $data['country_breakdown'] = array();
        $data['opportunity_usage'] = array();
        $data['keywords'] = array();
        $data['recent_activity'] = array();
    }

    if ($chat_table_exists) {
        // Session duration trends
        $data['session_duration'] = chatgabi_get_session_duration_trends($chat_logs_table);
    } else {
        $data['session_duration'] = array();
    }

    // Cache for 10 minutes
    set_transient($cache_key, $data, 10 * MINUTE_IN_SECONDS);

    return $data;
}

/**
 * Get analytics summary statistics
 */
function chatgabi_get_analytics_summary($table_name) {
    global $wpdb;

    // Total queries in last 30 days
    $total_queries = $wpdb->get_var(
        "SELECT COUNT(*) FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
    );

    // Active countries
    $active_countries = $wpdb->get_var(
        "SELECT COUNT(DISTINCT country) FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
    );

    // Top sector
    $top_sector = $wpdb->get_var(
        "SELECT detected_sector FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         AND detected_sector IS NOT NULL
         GROUP BY detected_sector
         ORDER BY COUNT(*) DESC
         LIMIT 1"
    );

    // Average session duration (estimated from chat logs if available)
    $avg_session = $wpdb->get_var(
        "SELECT AVG(prompt_tokens_estimated) / 100 FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         AND prompt_tokens_estimated > 0"
    );

    // Total opportunities included
    $opportunities_included = $wpdb->get_var(
        "SELECT SUM(opportunities_included) FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
    );

    return array(
        'total_queries' => intval($total_queries),
        'active_countries' => intval($active_countries),
        'top_sector' => $top_sector ?: __('No data', 'chatgabi'),
        'avg_session_duration' => floatval($avg_session) ?: 0,
        'opportunities_included' => intval($opportunities_included)
    );
}

/**
 * Get top queried sectors
 */
function chatgabi_get_top_sectors($table_name) {
    global $wpdb;

    $results = $wpdb->get_results(
        "SELECT detected_sector, COUNT(*) as count
         FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         AND detected_sector IS NOT NULL
         GROUP BY detected_sector
         ORDER BY count DESC
         LIMIT 10"
    );

    $sectors = array();
    foreach ($results as $row) {
        $sectors[$row->detected_sector] = intval($row->count);
    }

    return $sectors;
}

/**
 * Get country usage breakdown
 */
function chatgabi_get_country_breakdown($table_name) {
    global $wpdb;

    $results = $wpdb->get_results(
        "SELECT country, COUNT(*) as count
         FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         GROUP BY country
         ORDER BY count DESC"
    );

    $countries = array();
    foreach ($results as $row) {
        $countries[$row->country] = intval($row->count);
    }

    return $countries;
}

/**
 * Get opportunity usage statistics
 */
function chatgabi_get_opportunity_usage($table_name) {
    global $wpdb;

    $results = $wpdb->get_results(
        "SELECT
            SUM(CASE WHEN opportunities_included > 0 THEN 1 ELSE 0 END) as with_opportunities,
            SUM(CASE WHEN opportunities_included = 0 THEN 1 ELSE 0 END) as without_opportunities,
            AVG(opportunities_included) as avg_opportunities
         FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
    );

    $result = $results[0];

    return array(
        'with_opportunities' => intval($result->with_opportunities),
        'without_opportunities' => intval($result->without_opportunities),
        'avg_opportunities' => floatval($result->avg_opportunities)
    );
}

/**
 * Get keyword frequency from user messages
 */
function chatgabi_get_keyword_frequency($table_name) {
    global $wpdb;

    $messages = $wpdb->get_col(
        "SELECT user_message_preview FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         AND user_message_preview IS NOT NULL
         AND user_message_preview != ''"
    );

    $keywords = array();
    $common_words = array('the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'how', 'can', 'i', 'my', 'is', 'are', 'what', 'where', 'when', 'why', 'do', 'does', 'did');

    foreach ($messages as $message) {
        $words = str_word_count(strtolower($message), 1);
        foreach ($words as $word) {
            if (strlen($word) > 3 && !in_array($word, $common_words)) {
                $keywords[$word] = isset($keywords[$word]) ? $keywords[$word] + 1 : 1;
            }
        }
    }

    arsort($keywords);
    return array_slice($keywords, 0, 20, true);
}

/**
 * Get recent activity data
 */
function chatgabi_get_recent_activity($table_name) {
    global $wpdb;

    return $wpdb->get_results(
        "SELECT * FROM {$table_name}
         WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
         ORDER BY timestamp DESC
         LIMIT 50"
    );
}

/**
 * Get session duration trends - Fixed SQL query
 */
function chatgabi_get_session_duration_trends($table_name) {
    global $wpdb;

    // Fixed query: Use subquery to avoid window function in GROUP BY
    $results = $wpdb->get_results(
        "SELECT
            date,
            AVG(duration_minutes) as avg_duration
         FROM (
            SELECT
                DATE(created_at) as date,
                user_id,
                TIMESTAMPDIFF(MINUTE, created_at,
                    LEAD(created_at) OVER (PARTITION BY user_id ORDER BY created_at)
                ) as duration_minutes
            FROM {$table_name}
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         ) as session_durations
         WHERE duration_minutes IS NOT NULL
         GROUP BY date
         ORDER BY date"
    );

    $trends = array();
    foreach ($results as $row) {
        $trends[$row->date] = floatval($row->avg_duration) ?: 0;
    }

    return $trends;
}

/**
 * AJAX handler for getting engagement analytics
 */
function chatgabi_get_engagement_analytics() {
    check_ajax_referer('chatgabi_analytics_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_die(__('Insufficient permissions', 'chatgabi'));
    }

    // Clear cache if requested
    if (isset($_POST['refresh']) && $_POST['refresh'] === 'true') {
        delete_transient('chatgabi_engagement_analytics_data');
    }

    $data = chatgabi_get_engagement_analytics_data();

    wp_send_json_success($data);
}
add_action('wp_ajax_chatgabi_get_engagement_analytics', 'chatgabi_get_engagement_analytics');

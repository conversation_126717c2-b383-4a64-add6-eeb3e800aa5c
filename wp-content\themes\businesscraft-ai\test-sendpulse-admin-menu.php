<?php
/**
 * BusinessCraft AI - SendPulse Admin Menu Test
 * 
 * Test script to verify SendPulse admin menu registration and functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

echo '<h1>BusinessCraft AI - SendPulse Admin Menu Test</h1>';
echo '<p>Testing SendPulse admin menu registration and functionality...</p>';

$tests_run = 0;
$tests_passed = 0;

// Test 1: Check if SendPulse integration class exists
echo '<h2>Test 1: SendPulse Integration Class</h2>';
$tests_run++;

if (class_exists('ChatGABI_SendPulse_Integration')) {
    echo '✅ ChatGABI_SendPulse_Integration class exists<br>';
    $tests_passed++;
} else {
    echo '❌ ChatGABI_SendPulse_Integration class not found<br>';
}

// Test 2: Check if SendPulse function exists
echo '<h2>Test 2: SendPulse Helper Function</h2>';
$tests_run++;

if (function_exists('chatgabi_get_sendpulse')) {
    echo '✅ chatgabi_get_sendpulse() function exists<br>';
    
    try {
        $sendpulse = chatgabi_get_sendpulse();
        if ($sendpulse instanceof ChatGABI_SendPulse_Integration) {
            echo '✅ Function returns valid SendPulse instance<br>';
        } else {
            echo '❌ Function does not return valid SendPulse instance<br>';
        }
        $tests_passed++;
    } catch (Exception $e) {
        echo '❌ Error creating SendPulse instance: ' . $e->getMessage() . '<br>';
    }
} else {
    echo '❌ chatgabi_get_sendpulse() function not found<br>';
}

// Test 3: Check main ChatGABI admin menu
echo '<h2>Test 3: Main ChatGABI Admin Menu</h2>';
$tests_run++;

// Simulate admin environment
global $menu, $submenu;
if (!is_array($menu)) $menu = array();
if (!is_array($submenu)) $submenu = array();

// Trigger admin menu registration
do_action('admin_menu');

$main_menu_found = false;
if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (is_array($menu_item) && isset($menu_item[2]) && $menu_item[2] === 'chatgabi-main') {
            $main_menu_found = true;
            echo '✅ Main ChatGABI menu (chatgabi-main) found<br>';
            echo '   Menu title: ' . esc_html($menu_item[0]) . '<br>';
            break;
        }
    }
}

if ($main_menu_found) {
    $tests_passed++;
} else {
    echo '❌ Main ChatGABI menu (chatgabi-main) not found<br>';
    echo '   Available menus: ';
    if (is_array($menu)) {
        $menu_slugs = array();
        foreach ($menu as $menu_item) {
            if (is_array($menu_item) && isset($menu_item[2])) {
                $menu_slugs[] = $menu_item[2];
            }
        }
        echo implode(', ', $menu_slugs) . '<br>';
    } else {
        echo 'None<br>';
    }
}

// Test 4: Check SendPulse submenu
echo '<h2>Test 4: SendPulse Email Settings Submenu</h2>';
$tests_run++;

$sendpulse_submenu_found = false;
if (isset($submenu['chatgabi-main']) && is_array($submenu['chatgabi-main'])) {
    foreach ($submenu['chatgabi-main'] as $submenu_item) {
        if (is_array($submenu_item) && isset($submenu_item[2]) && $submenu_item[2] === 'chatgabi-sendpulse') {
            $sendpulse_submenu_found = true;
            echo '✅ SendPulse submenu (chatgabi-sendpulse) found<br>';
            echo '   Submenu title: ' . esc_html($submenu_item[0]) . '<br>';
            echo '   Menu text: ' . esc_html($submenu_item[1]) . '<br>';
            break;
        }
    }
    
    if ($sendpulse_submenu_found) {
        $tests_passed++;
    } else {
        echo '❌ SendPulse submenu not found<br>';
        echo '   Available submenus under chatgabi-main: ';
        $submenu_slugs = array();
        foreach ($submenu['chatgabi-main'] as $submenu_item) {
            if (is_array($submenu_item) && isset($submenu_item[2])) {
                $submenu_slugs[] = $submenu_item[2] . ' (' . $submenu_item[1] . ')';
            }
        }
        echo implode(', ', $submenu_slugs) . '<br>';
    }
} else {
    echo '❌ No submenus found under chatgabi-main<br>';
}

// Test 5: Check SendPulse admin page method
echo '<h2>Test 5: SendPulse Admin Page Method</h2>';
$tests_run++;

try {
    $sendpulse = chatgabi_get_sendpulse();
    if (method_exists($sendpulse, 'admin_page')) {
        echo '✅ admin_page() method exists<br>';
        $tests_passed++;
    } else {
        echo '❌ admin_page() method not found<br>';
    }
} catch (Exception $e) {
    echo '❌ Error checking admin_page method: ' . $e->getMessage() . '<br>';
}

// Test 6: Check AJAX handlers
echo '<h2>Test 6: AJAX Handlers</h2>';
$tests_run++;

$ajax_actions = array(
    'chatgabi_test_sendpulse_connection',
    'chatgabi_save_sendpulse_settings'
);

$ajax_registered = 0;
foreach ($ajax_actions as $action) {
    if (has_action("wp_ajax_{$action}")) {
        echo "✅ AJAX action registered: {$action}<br>";
        $ajax_registered++;
    } else {
        echo "❌ AJAX action not registered: {$action}<br>";
    }
}

if ($ajax_registered === count($ajax_actions)) {
    echo "<strong>✅ All {$ajax_registered} AJAX handlers are registered</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>❌ Only {$ajax_registered} out of " . count($ajax_actions) . " AJAX handlers are registered</strong><br>";
}

// Test 7: Check SendPulse options
echo '<h2>Test 7: SendPulse Configuration Options</h2>';
$tests_run++;

$sendpulse_options = array(
    'chatgabi_sendpulse_user_id' => 'SendPulse User ID',
    'chatgabi_sendpulse_secret' => 'SendPulse Secret',
    'chatgabi_sendpulse_template_id' => 'SendPulse Template ID'
);

$options_exist = 0;
foreach ($sendpulse_options as $option_name => $description) {
    $option_value = get_option($option_name, null);
    if ($option_value !== null) {
        echo "✅ {$description} option exists";
        if (!empty($option_value)) {
            echo " (configured)";
        } else {
            echo " (empty)";
        }
        echo "<br>";
        $options_exist++;
    } else {
        echo "❌ {$description} option not found<br>";
    }
}

if ($options_exist === count($sendpulse_options)) {
    echo "<strong>✅ All {$options_exist} SendPulse options are available</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>⚠️ {$options_exist} out of " . count($sendpulse_options) . " options are available</strong><br>";
}

// Test 8: Test admin page rendering (if requested)
if (isset($_GET['test_admin_page']) && $_GET['test_admin_page'] === '1') {
    echo '<h2>Test 8: Admin Page Rendering</h2>';
    $tests_run++;
    
    try {
        echo '<div style="border: 2px solid #ccc; padding: 20px; margin: 20px 0; background: #f9f9f9;">';
        echo '<h3>SendPulse Admin Page Preview:</h3>';
        
        ob_start();
        $sendpulse = chatgabi_get_sendpulse();
        $sendpulse->admin_page();
        $admin_page_output = ob_get_clean();
        
        if (!empty($admin_page_output)) {
            echo $admin_page_output;
            echo '<br><strong>✅ Admin page renders successfully</strong>';
            $tests_passed++;
        } else {
            echo '<strong>❌ Admin page failed to render</strong>';
        }
        
        echo '</div>';
    } catch (Exception $e) {
        echo '<strong>❌ Error rendering admin page: ' . $e->getMessage() . '</strong><br>';
    }
}

// Final Summary
echo '<hr>';
echo '<h2>🎯 Test Summary</h2>';
echo "<p><strong>Tests Passed: {$tests_passed} / {$tests_run}</strong></p>";

$success_rate = ($tests_passed / $tests_run) * 100;

if ($success_rate >= 90) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>🎉 Excellent! SendPulse admin menu is working properly</h3>';
    echo '<p>The SendPulse email settings should be visible in the WordPress admin under <strong>ChatGABI → Email Settings</strong>.</p>';
    echo '</div>';
} elseif ($success_rate >= 70) {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>⚠️ Good, but some issues need attention</h3>';
    echo '<p>Most components are working, but please address the failed tests above.</p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>❌ SendPulse admin menu needs fixes</h3>';
    echo '<p>Multiple components are not working properly. Please review and fix the issues above.</p>';
    echo '</div>';
}

echo '<h3>📋 Next Steps:</h3>';
echo '<ul>';
echo '<li><strong>Access Admin Menu:</strong> Go to WordPress Admin → ChatGABI → Email Settings</li>';
echo '<li><strong>Configure SendPulse:</strong> Enter your SendPulse API credentials</li>';
echo '<li><strong>Test Connection:</strong> Use the "Test Connection" button to verify API access</li>';
echo '<li><strong>Test Admin Page:</strong> <a href="?test_admin_page=1">Click here to test admin page rendering</a></li>';
echo '</ul>';

echo '<h3>🔗 Quick Links:</h3>';
echo '<ul>';
if ($sendpulse_submenu_found) {
    echo '<li><a href="' . admin_url('admin.php?page=chatgabi-sendpulse') . '">→ SendPulse Email Settings</a></li>';
}
echo '<li><a href="' . admin_url('admin.php?page=chatgabi-main') . '">→ ChatGABI Dashboard</a></li>';
echo '<li><a href="' . get_template_directory_uri() . '/initialize-opportunity-alerts.php">→ Initialize Opportunity Alerts</a></li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Test completed at ' . current_time('mysql') . '</em></p>';
echo '<p><a href="' . admin_url() . '">← Return to WordPress Admin</a></p>';
?>

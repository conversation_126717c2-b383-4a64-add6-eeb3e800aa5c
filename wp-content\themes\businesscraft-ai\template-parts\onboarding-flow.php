<?php
/**
 * Onboarding Flow Interface
 * 
 * Multi-step onboarding interface with SME and Creator tracks
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$user_id = get_current_user_id();
$profile = chatgabi_get_user_profile($user_id);
$progress = chatgabi_get_onboarding_progress($user_id);

// Determine current step
$current_step = $_GET['step'] ?? 'welcome';
$profile_type = $profile->profile_type ?? 'sme';
$steps = chatgabi_get_onboarding_steps($profile_type);

// Calculate progress percentage
$completed_steps = array_filter($progress, function($step) { return $step['completed']; });
$progress_percentage = count($steps) > 0 ? (count($completed_steps) / count($steps)) * 100 : 0;
?>

<div class="chatgabi-onboarding" id="onboarding-container">
    <!-- Progress Header -->
    <div class="onboarding-header">
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo $progress_percentage; ?>%"></div>
            </div>
            <div class="progress-text">
                <?php printf(__('%d%% Complete', 'chatgabi'), round($progress_percentage)); ?>
            </div>
        </div>
        
        <div class="step-indicator">
            <?php
            $step_index = 0;
            foreach ($steps as $step_key => $step_info) {
                $is_current = $step_key === $current_step;
                $is_completed = isset($progress[$step_key]) && $progress[$step_key]['completed'];
                $step_class = $is_current ? 'current' : ($is_completed ? 'completed' : 'pending');
                
                echo '<div class="step-dot ' . $step_class . '" data-step="' . $step_key . '">';
                echo '<span class="step-number">' . ($step_index + 1) . '</span>';
                echo '<span class="step-icon">' . $step_info['icon'] . '</span>';
                echo '</div>';
                
                $step_index++;
            }
            ?>
        </div>
    </div>

    <!-- Step Content -->
    <div class="onboarding-content">
        <div class="step-container" id="step-<?php echo $current_step; ?>">
            
            <?php if ($current_step === 'welcome'): ?>
                <!-- Welcome Step -->
                <div class="step-content welcome-step">
                    <div class="step-header">
                        <h1><?php _e('Welcome to ChatGABI AI! 🎉', 'chatgabi'); ?></h1>
                        <p><?php _e('Your AI-powered business intelligence companion for African entrepreneurs', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="welcome-features">
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🧠</span>
                                <h3><?php _e('AI Business Intelligence', 'chatgabi'); ?></h3>
                                <p><?php _e('Get insights tailored to African markets across 67 sectors in 4 countries', 'chatgabi'); ?></p>
                            </div>
                            
                            <div class="feature-card">
                                <span class="feature-icon">📝</span>
                                <h3><?php _e('Smart Templates', 'chatgabi'); ?></h3>
                                <p><?php _e('Business plans, marketing strategies, and financial forecasts made easy', 'chatgabi'); ?></p>
                            </div>
                            
                            <div class="feature-card">
                                <span class="feature-icon">🌍</span>
                                <h3><?php _e('African Context', 'chatgabi'); ?></h3>
                                <p><?php _e('Cultural insights and local business intelligence for Ghana, Kenya, Nigeria, and South Africa', 'chatgabi'); ?></p>
                            </div>
                            
                            <div class="feature-card">
                                <span class="feature-icon">🗣️</span>
                                <h3><?php _e('Multi-Language Support', 'chatgabi'); ?></h3>
                                <p><?php _e('Available in English, Twi, Swahili, Yoruba, and Zulu', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-actions">
                        <button class="btn-primary btn-large" onclick="nextStep()">
                            <?php _e('Get Started', 'chatgabi'); ?> →
                        </button>
                    </div>
                </div>

            <?php elseif ($current_step === 'profile_type'): ?>
                <!-- Profile Type Selection -->
                <div class="step-content profile-type-step">
                    <div class="step-header">
                        <h2><?php _e('Choose Your Path', 'chatgabi'); ?></h2>
                        <p><?php _e('Select the option that best describes your primary focus:', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="profile-options">
                        <div class="profile-option" data-type="sme">
                            <div class="option-icon">🏢</div>
                            <h3><?php _e('SME Track', 'chatgabi'); ?></h3>
                            <p><?php _e('Small & Medium Enterprises', 'chatgabi'); ?></p>
                            <ul>
                                <li><?php _e('Business planning & strategy', 'chatgabi'); ?></li>
                                <li><?php _e('Financial forecasting', 'chatgabi'); ?></li>
                                <li><?php _e('Market analysis', 'chatgabi'); ?></li>
                                <li><?php _e('Operations optimization', 'chatgabi'); ?></li>
                            </ul>
                            <button class="btn-outline select-profile-btn" data-type="sme">
                                <?php _e('Choose SME Track', 'chatgabi'); ?>
                            </button>
                        </div>
                        
                        <div class="profile-option" data-type="creator">
                            <div class="option-icon">🎨</div>
                            <h3><?php _e('Creator Track', 'chatgabi'); ?></h3>
                            <p><?php _e('Content Creators & Influencers', 'chatgabi'); ?></p>
                            <ul>
                                <li><?php _e('Content strategy & planning', 'chatgabi'); ?></li>
                                <li><?php _e('Brand development', 'chatgabi'); ?></li>
                                <li><?php _e('Audience engagement', 'chatgabi'); ?></li>
                                <li><?php _e('Monetization strategies', 'chatgabi'); ?></li>
                            </ul>
                            <button class="btn-outline select-profile-btn" data-type="creator">
                                <?php _e('Choose Creator Track', 'chatgabi'); ?>
                            </button>
                        </div>
                    </div>
                </div>

            <?php elseif ($current_step === 'business_basics' && $profile_type === 'sme'): ?>
                <!-- SME Business Basics -->
                <div class="step-content business-basics-step">
                    <div class="step-header">
                        <h2><?php _e('Tell Us About Your Business', 'chatgabi'); ?></h2>
                        <p><?php _e('This helps us provide more relevant insights and recommendations.', 'chatgabi'); ?></p>
                    </div>
                    
                    <form class="onboarding-form" id="business-basics-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label><?php _e('Primary Industry', 'chatgabi'); ?></label>
                                <select name="primary_industry" required>
                                    <option value=""><?php _e('Select your industry...', 'chatgabi'); ?></option>
                                    <option value="Technology"><?php _e('Technology', 'chatgabi'); ?></option>
                                    <option value="Agriculture"><?php _e('Agriculture', 'chatgabi'); ?></option>
                                    <option value="Retail"><?php _e('Retail & E-commerce', 'chatgabi'); ?></option>
                                    <option value="Financial Services"><?php _e('Financial Services', 'chatgabi'); ?></option>
                                    <option value="Healthcare"><?php _e('Healthcare', 'chatgabi'); ?></option>
                                    <option value="Education"><?php _e('Education', 'chatgabi'); ?></option>
                                    <option value="Manufacturing"><?php _e('Manufacturing', 'chatgabi'); ?></option>
                                    <option value="Tourism"><?php _e('Tourism & Hospitality', 'chatgabi'); ?></option>
                                    <option value="Other"><?php _e('Other', 'chatgabi'); ?></option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label><?php _e('Target Country', 'chatgabi'); ?></label>
                                <select name="target_country" required>
                                    <option value=""><?php _e('Select your primary market...', 'chatgabi'); ?></option>
                                    <option value="GH">🇬🇭 <?php _e('Ghana', 'chatgabi'); ?></option>
                                    <option value="KE">🇰🇪 <?php _e('Kenya', 'chatgabi'); ?></option>
                                    <option value="NG">🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?></option>
                                    <option value="ZA">🇿🇦 <?php _e('South Africa', 'chatgabi'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label><?php _e('Business Size', 'chatgabi'); ?></label>
                                <select name="business_size" required>
                                    <option value=""><?php _e('Select business size...', 'chatgabi'); ?></option>
                                    <option value="solo"><?php _e('Solo entrepreneur', 'chatgabi'); ?></option>
                                    <option value="micro"><?php _e('Micro business (1-5 employees)', 'chatgabi'); ?></option>
                                    <option value="small"><?php _e('Small business (6-50 employees)', 'chatgabi'); ?></option>
                                    <option value="medium"><?php _e('Medium business (51-250 employees)', 'chatgabi'); ?></option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label><?php _e('Annual Revenue', 'chatgabi'); ?></label>
                                <select name="annual_revenue">
                                    <option value=""><?php _e('Select revenue range...', 'chatgabi'); ?></option>
                                    <option value="pre-revenue"><?php _e('Pre-revenue', 'chatgabi'); ?></option>
                                    <option value="under-10k"><?php _e('Under $10,000', 'chatgabi'); ?></option>
                                    <option value="10k-50k"><?php _e('$10,000 - $50,000', 'chatgabi'); ?></option>
                                    <option value="50k-100k"><?php _e('$50,000 - $100,000', 'chatgabi'); ?></option>
                                    <option value="100k-500k"><?php _e('$100,000 - $500,000', 'chatgabi'); ?></option>
                                    <option value="500k-1m"><?php _e('$500,000 - $1M', 'chatgabi'); ?></option>
                                    <option value="over-1m"><?php _e('Over $1M', 'chatgabi'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                ← <?php _e('Back', 'chatgabi'); ?>
                            </button>
                            <button type="submit" class="btn-primary">
                                <?php _e('Continue', 'chatgabi'); ?> →
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($current_step === 'creator_basics' && $profile_type === 'creator'): ?>
                <!-- Creator Profile Basics -->
                <div class="step-content creator-basics-step">
                    <div class="step-header">
                        <h2><?php _e('Tell Us About Your Creative Work', 'chatgabi'); ?></h2>
                        <p><?php _e('Help us understand your creative focus and goals.', 'chatgabi'); ?></p>
                    </div>
                    
                    <form class="onboarding-form" id="creator-basics-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label><?php _e('Primary Content Focus', 'chatgabi'); ?></label>
                                <select name="marketing_focus" required>
                                    <option value=""><?php _e('Select your focus...', 'chatgabi'); ?></option>
                                    <option value="lifestyle"><?php _e('Lifestyle & Personal Brand', 'chatgabi'); ?></option>
                                    <option value="business"><?php _e('Business & Entrepreneurship', 'chatgabi'); ?></option>
                                    <option value="education"><?php _e('Education & Training', 'chatgabi'); ?></option>
                                    <option value="entertainment"><?php _e('Entertainment & Media', 'chatgabi'); ?></option>
                                    <option value="fashion"><?php _e('Fashion & Beauty', 'chatgabi'); ?></option>
                                    <option value="food"><?php _e('Food & Cooking', 'chatgabi'); ?></option>
                                    <option value="travel"><?php _e('Travel & Culture', 'chatgabi'); ?></option>
                                    <option value="tech"><?php _e('Technology & Innovation', 'chatgabi'); ?></option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label><?php _e('Target Country', 'chatgabi'); ?></label>
                                <select name="target_country" required>
                                    <option value=""><?php _e('Select your primary audience location...', 'chatgabi'); ?></option>
                                    <option value="GH">🇬🇭 <?php _e('Ghana', 'chatgabi'); ?></option>
                                    <option value="KE">🇰🇪 <?php _e('Kenya', 'chatgabi'); ?></option>
                                    <option value="NG">🇳🇬 <?php _e('Nigeria', 'chatgabi'); ?></option>
                                    <option value="ZA">🇿🇦 <?php _e('South Africa', 'chatgabi'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label><?php _e('Content Types', 'chatgabi'); ?></label>
                            <div class="checkbox-grid">
                                <label class="checkbox-item">
                                    <input type="checkbox" name="content_types[]" value="social_media">
                                    <span><?php _e('Social Media Posts', 'chatgabi'); ?></span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="content_types[]" value="blog_articles">
                                    <span><?php _e('Blog Articles', 'chatgabi'); ?></span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="content_types[]" value="video_content">
                                    <span><?php _e('Video Content', 'chatgabi'); ?></span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="content_types[]" value="podcasts">
                                    <span><?php _e('Podcasts', 'chatgabi'); ?></span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="content_types[]" value="newsletters">
                                    <span><?php _e('Newsletters', 'chatgabi'); ?></span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="content_types[]" value="courses">
                                    <span><?php _e('Online Courses', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                ← <?php _e('Back', 'chatgabi'); ?>
                            </button>
                            <button type="submit" class="btn-primary">
                                <?php _e('Continue', 'chatgabi'); ?> →
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($current_step === 'business_stage'): ?>
                <!-- Business Stage Step -->
                <div class="step-content business-stage-step">
                    <div class="step-header">
                        <h2><?php _e('What stage is your business in?', 'chatgabi'); ?></h2>
                        <p><?php _e('This helps us tailor our recommendations to your current needs.', 'chatgabi'); ?></p>
                    </div>

                    <form class="onboarding-form" id="business-stage-form">
                        <div class="stage-options">
                            <label class="stage-option">
                                <input type="radio" name="business_stage" value="idea" required>
                                <div class="stage-card">
                                    <div class="stage-icon">💡</div>
                                    <h4><?php _e('Idea Stage', 'chatgabi'); ?></h4>
                                    <p><?php _e('I have a business idea but haven\'t started yet', 'chatgabi'); ?></p>
                                </div>
                            </label>

                            <label class="stage-option">
                                <input type="radio" name="business_stage" value="startup" required>
                                <div class="stage-card">
                                    <div class="stage-icon">🚀</div>
                                    <h4><?php _e('Startup', 'chatgabi'); ?></h4>
                                    <p><?php _e('I\'ve recently started my business (0-2 years)', 'chatgabi'); ?></p>
                                </div>
                            </label>

                            <label class="stage-option">
                                <input type="radio" name="business_stage" value="growth" required>
                                <div class="stage-card">
                                    <div class="stage-icon">📈</div>
                                    <h4><?php _e('Growth Stage', 'chatgabi'); ?></h4>
                                    <p><?php _e('My business is established and growing (2+ years)', 'chatgabi'); ?></p>
                                </div>
                            </label>

                            <label class="stage-option">
                                <input type="radio" name="business_stage" value="expansion" required>
                                <div class="stage-card">
                                    <div class="stage-icon">🌍</div>
                                    <h4><?php _e('Expansion', 'chatgabi'); ?></h4>
                                    <p><?php _e('Looking to expand to new markets or scale up', 'chatgabi'); ?></p>
                                </div>
                            </label>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                ← <?php _e('Back', 'chatgabi'); ?>
                            </button>
                            <button type="submit" class="btn-primary">
                                <?php _e('Continue', 'chatgabi'); ?> →
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($current_step === 'goals_challenges'): ?>
                <!-- Goals & Challenges Step -->
                <div class="step-content goals-challenges-step">
                    <div class="step-header">
                        <h2><?php _e('What are your main goals and challenges?', 'chatgabi'); ?></h2>
                        <p><?php _e('Select all that apply to help us provide targeted assistance.', 'chatgabi'); ?></p>
                    </div>

                    <form class="onboarding-form" id="goals-challenges-form">
                        <div class="form-section">
                            <h3><?php _e('Primary Goals', 'chatgabi'); ?></h3>
                            <div class="checkbox-grid">
                                <label class="checkbox-option">
                                    <input type="checkbox" name="primary_goals[]" value="increase_revenue">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Increase revenue', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="primary_goals[]" value="expand_market">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Expand to new markets', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="primary_goals[]" value="improve_operations">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Improve operations', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="primary_goals[]" value="digital_transformation">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Digital transformation', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="primary_goals[]" value="team_building">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Build and manage team', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="primary_goals[]" value="funding">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Secure funding/investment', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><?php _e('Main Challenges', 'chatgabi'); ?></h3>
                            <div class="checkbox-grid">
                                <label class="checkbox-option">
                                    <input type="checkbox" name="challenges[]" value="limited_resources">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Limited resources', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="challenges[]" value="market_competition">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Market competition', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="challenges[]" value="customer_acquisition">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Customer acquisition', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="challenges[]" value="regulatory_compliance">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Regulatory compliance', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="challenges[]" value="technology_adoption">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Technology adoption', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="challenges[]" value="access_to_finance">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Access to finance', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                ← <?php _e('Back', 'chatgabi'); ?>
                            </button>
                            <button type="submit" class="btn-primary">
                                <?php _e('Continue', 'chatgabi'); ?> →
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($current_step === 'preferences'): ?>
                <!-- Preferences Step -->
                <div class="step-content preferences-step">
                    <div class="step-header">
                        <h2><?php _e('Set Your Preferences', 'chatgabi'); ?></h2>
                        <p><?php _e('Customize your ChatGABI experience to match your needs.', 'chatgabi'); ?></p>
                    </div>

                    <form class="onboarding-form" id="preferences-form">
                        <div class="form-section">
                            <h3><?php _e('Language Preference', 'chatgabi'); ?></h3>
                            <div class="radio-grid">
                                <label class="radio-option">
                                    <input type="radio" name="preferred_language" value="en" checked required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text">🇬🇧 <?php _e('English', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="preferred_language" value="tw" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text">🇬🇭 <?php _e('Twi', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="preferred_language" value="sw" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text">🇰🇪 <?php _e('Swahili', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="preferred_language" value="yo" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text">🇳🇬 <?php _e('Yoruba', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="preferred_language" value="zu" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text">🇿🇦 <?php _e('Zulu', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><?php _e('Communication Preferences', 'chatgabi'); ?></h3>
                            <div class="checkbox-grid">
                                <label class="checkbox-option">
                                    <input type="checkbox" name="notifications[]" value="email_updates" checked>
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Email updates about new features', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="notifications[]" value="business_insights">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Weekly business insights', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="notifications[]" value="opportunities">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('New opportunities alerts', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><?php _e('AI Response Style', 'chatgabi'); ?></h3>
                            <div class="radio-grid">
                                <label class="radio-option">
                                    <input type="radio" name="response_style" value="detailed" checked required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text"><?php _e('Detailed & Comprehensive', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="response_style" value="concise" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text"><?php _e('Concise & Direct', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="response_style" value="balanced" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text"><?php _e('Balanced', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                ← <?php _e('Back', 'chatgabi'); ?>
                            </button>
                            <button type="submit" class="btn-primary">
                                <?php _e('Continue', 'chatgabi'); ?> →
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($current_step === 'template_recommendations'): ?>
                <!-- Template Recommendations Step -->
                <div class="step-content template-recommendations-step">
                    <div class="step-header">
                        <h2><?php _e('Recommended Templates', 'chatgabi'); ?></h2>
                        <p><?php _e('Based on your profile, here are templates that can help you get started.', 'chatgabi'); ?></p>
                    </div>

                    <div class="template-recommendations">
                        <div class="template-grid">
                            <div class="template-card recommended">
                                <div class="template-icon">📋</div>
                                <h4><?php _e('Business Plan Generator', 'chatgabi'); ?></h4>
                                <p><?php _e('Create comprehensive business plans tailored to African markets', 'chatgabi'); ?></p>
                                <div class="template-badge"><?php _e('Recommended', 'chatgabi'); ?></div>
                            </div>

                            <div class="template-card recommended">
                                <div class="template-icon">📈</div>
                                <h4><?php _e('Marketing Strategy', 'chatgabi'); ?></h4>
                                <p><?php _e('Develop effective marketing strategies for your target market', 'chatgabi'); ?></p>
                                <div class="template-badge"><?php _e('Recommended', 'chatgabi'); ?></div>
                            </div>

                            <div class="template-card">
                                <div class="template-icon">💰</div>
                                <h4><?php _e('Financial Forecast', 'chatgabi'); ?></h4>
                                <p><?php _e('Generate detailed financial projections and budgets', 'chatgabi'); ?></p>
                            </div>

                            <div class="template-card">
                                <div class="template-icon">🎯</div>
                                <h4><?php _e('Market Analysis', 'chatgabi'); ?></h4>
                                <p><?php _e('Analyze your market and identify opportunities', 'chatgabi'); ?></p>
                            </div>
                        </div>

                        <div class="template-actions">
                            <p><?php _e('You can access all templates from your dashboard after completing setup.', 'chatgabi'); ?></p>
                        </div>
                    </div>

                    <div class="step-actions">
                        <button type="button" class="btn-secondary" onclick="previousStep()">
                            ← <?php _e('Back', 'chatgabi'); ?>
                        </button>
                        <button type="button" class="btn-primary" onclick="nextStep()">
                            <?php _e('Continue', 'chatgabi'); ?> →
                        </button>
                    </div>
                </div>

            <?php elseif ($current_step === 'dashboard_setup'): ?>
                <!-- Dashboard Setup Step -->
                <div class="step-content dashboard-setup-step">
                    <div class="step-header">
                        <h2><?php _e('Customize Your Dashboard', 'chatgabi'); ?></h2>
                        <p><?php _e('Choose which widgets and features you\'d like to see on your dashboard.', 'chatgabi'); ?></p>
                    </div>

                    <form class="onboarding-form" id="dashboard-setup-form">
                        <div class="form-section">
                            <h3><?php _e('Dashboard Widgets', 'chatgabi'); ?></h3>
                            <div class="checkbox-grid">
                                <label class="checkbox-option">
                                    <input type="checkbox" name="dashboard_widgets[]" value="quick_chat" checked>
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Quick Chat Widget', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="dashboard_widgets[]" value="opportunities" checked>
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Live Opportunities', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="dashboard_widgets[]" value="templates" checked>
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Template Library', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="dashboard_widgets[]" value="analytics">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Usage Analytics', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="dashboard_widgets[]" value="credits">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Credit Balance', 'chatgabi'); ?></span>
                                </label>

                                <label class="checkbox-option">
                                    <input type="checkbox" name="dashboard_widgets[]" value="recent_conversations">
                                    <span class="checkmark"></span>
                                    <span class="label-text"><?php _e('Recent Conversations', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><?php _e('Default Dashboard Tab', 'chatgabi'); ?></h3>
                            <div class="radio-grid">
                                <label class="radio-option">
                                    <input type="radio" name="default_tab" value="chat" checked required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text"><?php _e('AI Chat', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="default_tab" value="opportunities" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text"><?php _e('Opportunities', 'chatgabi'); ?></span>
                                </label>

                                <label class="radio-option">
                                    <input type="radio" name="default_tab" value="templates" required>
                                    <span class="radio-mark"></span>
                                    <span class="label-text"><?php _e('Templates', 'chatgabi'); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="previousStep()">
                                ← <?php _e('Back', 'chatgabi'); ?>
                            </button>
                            <button type="submit" class="btn-primary">
                                <?php _e('Complete Setup', 'chatgabi'); ?> →
                            </button>
                        </div>
                    </form>
                </div>

            <?php elseif ($current_step === 'complete'): ?>
                <!-- Completion Step -->
                <div class="step-content complete-step">
                    <div class="completion-celebration">
                        <div class="celebration-icon">🎉</div>
                        <h1><?php _e('Welcome to ChatGABI!', 'chatgabi'); ?></h1>
                        <p><?php _e('Your personalized AI business assistant is ready to help you succeed.', 'chatgabi'); ?></p>
                    </div>
                    
                    <div class="next-steps">
                        <h3><?php _e('What\'s Next?', 'chatgabi'); ?></h3>
                        <div class="next-steps-grid">
                            <div class="next-step-card">
                                <span class="step-icon">💬</span>
                                <h4><?php _e('Start Chatting', 'chatgabi'); ?></h4>
                                <p><?php _e('Ask ChatGABI anything about your business', 'chatgabi'); ?></p>
                            </div>
                            
                            <div class="next-step-card">
                                <span class="step-icon">📋</span>
                                <h4><?php _e('Use Templates', 'chatgabi'); ?></h4>
                                <p><?php _e('Generate business documents with our smart templates', 'chatgabi'); ?></p>
                            </div>
                            
                            <div class="next-step-card">
                                <span class="step-icon">🎯</span>
                                <h4><?php _e('Explore Opportunities', 'chatgabi'); ?></h4>
                                <p><?php _e('Discover business opportunities in your market', 'chatgabi'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-actions">
                        <a href="<?php echo home_url('/dashboard/'); ?>" class="btn-primary btn-large">
                            <?php _e('Go to Dashboard', 'chatgabi'); ?> 🚀
                        </a>
                    </div>
                </div>

            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Onboarding JavaScript functionality
window.chatgabiOnboarding = {
    currentStep: '<?php echo $current_step; ?>',
    profileType: '<?php echo $profile_type; ?>',
    steps: <?php echo wp_json_encode(array_keys($steps)); ?>,
    userId: <?php echo $user_id; ?>
};

// Initialize onboarding
jQuery(document).ready(function() {
    initializeOnboarding();
});
</script>

<?php
/**
 * Test Function Conflict Resolution
 * 
 * Verifies that the businesscraft_ai_initiate_payment function conflict has been resolved
 */

// Include WordPress
require_once 'wp-config.php';

// Set up WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Conflict Resolution Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; font-weight: 600; }
        .error { color: #dc3545; font-weight: 600; }
        .warning { color: #ffc107; font-weight: 600; }
        .info { color: #17a2b8; font-weight: 600; }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .file-path {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }
        .summary h2 { margin-top: 0; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Function Conflict Resolution Test</h1>
            <p>Testing resolution of businesscraft_ai_initiate_payment function redeclaration conflict</p>
            <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <?php
        $tests_passed = 0;
        $tests_total = 0;
        $issues_found = array();
        ?>

        <!-- Test 1: Function Existence Check -->
        <div class="test-section">
            <h2>🔍 Test 1: Function Existence Check</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Function Declaration Status</h3>';
            
            // Check if AJAX function exists
            if (function_exists('businesscraft_ai_initiate_payment')) {
                echo '<p class="success">✅ AJAX function businesscraft_ai_initiate_payment() exists</p>';
            } else {
                echo '<p class="error">❌ AJAX function businesscraft_ai_initiate_payment() missing</p>';
                $issues_found[] = 'AJAX payment function missing';
            }
            
            // Check if REST function exists
            if (function_exists('businesscraft_ai_rest_initiate_payment')) {
                echo '<p class="success">✅ REST function businesscraft_ai_rest_initiate_payment() exists</p>';
            } else {
                echo '<p class="error">❌ REST function businesscraft_ai_rest_initiate_payment() missing</p>';
                $issues_found[] = 'REST payment function missing';
            }
            
            // Check if both functions exist (no conflict)
            if (function_exists('businesscraft_ai_initiate_payment') && function_exists('businesscraft_ai_rest_initiate_payment')) {
                echo '<p class="success">✅ No function redeclaration conflict detected</p>';
                $tests_passed++;
            } else {
                echo '<p class="error">❌ Function conflict may still exist</p>';
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Test 2: File Content Verification -->
        <div class="test-section">
            <h2>📄 Test 2: File Content Verification</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Source File Analysis</h3>';
            
            // Check REST API file
            $rest_api_file = get_template_directory() . '/inc/rest-api.php';
            if (file_exists($rest_api_file)) {
                $rest_content = file_get_contents($rest_api_file);
                
                if (strpos($rest_content, 'function businesscraft_ai_rest_initiate_payment') !== false) {
                    echo '<p class="success">✅ REST API file contains renamed function</p>';
                } else {
                    echo '<p class="error">❌ REST API file does not contain renamed function</p>';
                    $issues_found[] = 'REST API function not renamed';
                }
                
                if (strpos($rest_content, "'callback' => 'businesscraft_ai_rest_initiate_payment'") !== false) {
                    echo '<p class="success">✅ REST route registration updated</p>';
                } else {
                    echo '<p class="error">❌ REST route registration not updated</p>';
                    $issues_found[] = 'REST route registration not updated';
                }
                
                // Check for old function name (should not exist)
                if (strpos($rest_content, 'function businesscraft_ai_initiate_payment(') === false) {
                    echo '<p class="success">✅ Old function name removed from REST API file</p>';
                } else {
                    echo '<p class="error">❌ Old function name still exists in REST API file</p>';
                    $issues_found[] = 'Old function name still in REST API file';
                }
                
            } else {
                echo '<p class="error">❌ REST API file not found</p>';
                $issues_found[] = 'REST API file missing';
            }
            
            // Check credit purchase handlers file
            $handlers_file = get_template_directory() . '/inc/credit-purchase-handlers.php';
            if (file_exists($handlers_file)) {
                $handlers_content = file_get_contents($handlers_file);
                
                if (strpos($handlers_content, 'function businesscraft_ai_initiate_payment()') !== false) {
                    echo '<p class="success">✅ Credit handlers file contains AJAX function</p>';
                } else {
                    echo '<p class="error">❌ Credit handlers file does not contain AJAX function</p>';
                    $issues_found[] = 'AJAX function missing from handlers file';
                }
                
                if (strpos($handlers_content, "'wp_ajax_businesscraft_ai_initiate_payment', 'businesscraft_ai_initiate_payment'") !== false) {
                    echo '<p class="success">✅ AJAX action registration correct</p>';
                } else {
                    echo '<p class="error">❌ AJAX action registration incorrect</p>';
                    $issues_found[] = 'AJAX action registration incorrect';
                }
                
            } else {
                echo '<p class="error">❌ Credit handlers file not found</p>';
                $issues_found[] = 'Credit handlers file missing';
            }
            
            if (count($issues_found) === 0) {
                $tests_passed++;
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Test 3: WordPress Loading Test -->
        <div class="test-section">
            <h2>🚀 Test 3: WordPress Loading Test</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>WordPress Environment Status</h3>';
            
            // Check if WordPress loaded without fatal errors
            if (defined('ABSPATH') && function_exists('wp_get_current_user')) {
                echo '<p class="success">✅ WordPress loaded successfully without fatal errors</p>';
                $tests_passed++;
            } else {
                echo '<p class="error">❌ WordPress failed to load properly</p>';
                $issues_found[] = 'WordPress loading failed';
            }
            
            // Check if theme functions are loaded
            if (function_exists('businesscraft_ai_init_credit_purchase_handlers')) {
                echo '<p class="success">✅ Theme functions loaded successfully</p>';
            } else {
                echo '<p class="warning">⚠️ Some theme functions may not be loaded yet</p>';
            }
            
            // Check if AJAX actions are registered
            global $wp_filter;
            if (isset($wp_filter['wp_ajax_businesscraft_ai_initiate_payment'])) {
                echo '<p class="success">✅ AJAX action wp_ajax_businesscraft_ai_initiate_payment registered</p>';
            } else {
                echo '<p class="warning">⚠️ AJAX action not yet registered (may load later)</p>';
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Test 4: Integration Points Check -->
        <div class="test-section">
            <h2>🔗 Test 4: Integration Points Check</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Dashboard and Frontend Integration</h3>';
            
            // Check dashboard integration
            $dashboard_file = get_template_directory() . '/page-dashboard.php';
            if (file_exists($dashboard_file)) {
                $dashboard_content = file_get_contents($dashboard_file);
                
                if (strpos($dashboard_content, "action: 'businesscraft_ai_initiate_payment'") !== false) {
                    echo '<p class="success">✅ Dashboard uses correct AJAX action</p>';
                } else {
                    echo '<p class="error">❌ Dashboard AJAX action incorrect</p>';
                    $issues_found[] = 'Dashboard AJAX action incorrect';
                }
            }
            
            // Check payments.js integration
            $payments_js = get_template_directory() . '/assets/js/payments.js';
            if (file_exists($payments_js)) {
                $payments_content = file_get_contents($payments_js);
                
                if (strpos($payments_content, "restUrl + 'initiate-payment'") !== false) {
                    echo '<p class="success">✅ Frontend payments.js uses REST endpoint</p>';
                } else {
                    echo '<p class="error">❌ Frontend payments.js endpoint incorrect</p>';
                    $issues_found[] = 'Frontend payments.js endpoint incorrect';
                }
            }
            
            if (count($issues_found) <= 2) { // Allow for minor issues
                $tests_passed++;
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Summary -->
        <div class="summary">
            <h2>📊 Conflict Resolution Summary</h2>
            <p><strong>Tests Passed:</strong> <?php echo $tests_passed; ?> / <?php echo $tests_total; ?></p>
            <p><strong>Success Rate:</strong> <?php echo round(($tests_passed / $tests_total) * 100, 1); ?>%</p>
            
            <?php if (!empty($issues_found)): ?>
                <h3>🚨 Issues Found:</h3>
                <ul>
                    <?php foreach ($issues_found as $issue): ?>
                        <li><?php echo esc_html($issue); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p class="success">🎉 Function conflict successfully resolved!</p>
            <?php endif; ?>
            
            <h3>✅ Resolution Summary:</h3>
            <ul>
                <li><strong>AJAX Function:</strong> <code>businesscraft_ai_initiate_payment()</code> - Used by dashboard</li>
                <li><strong>REST Function:</strong> <code>businesscraft_ai_rest_initiate_payment()</code> - Used by frontend payments.js</li>
                <li><strong>Conflict Status:</strong> <?php echo empty($issues_found) ? 'Resolved' : 'Partially resolved'; ?></li>
                <li><strong>WordPress Loading:</strong> <?php echo function_exists('wp_get_current_user') ? 'Successful' : 'Failed'; ?></li>
            </ul>
            
            <h3>🔄 Next Steps:</h3>
            <ul>
                <li>Test dashboard credit purchase functionality</li>
                <li>Test frontend payment system</li>
                <li>Verify both AJAX and REST endpoints work correctly</li>
                <li>Monitor for any remaining PHP errors</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Function Conflict Resolution Test Page Loaded');
            console.log('Tests Passed: <?php echo $tests_passed; ?> / <?php echo $tests_total; ?>');
            
            // Test if WordPress is accessible
            if (typeof wp !== 'undefined') {
                console.log('✅ WordPress JavaScript API available');
            } else {
                console.log('⚠️ WordPress JavaScript API not available');
            }
        });
    </script>
</body>
</html>

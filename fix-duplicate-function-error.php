<?php
/**
 * Fix Duplicate Function Error - ChatGABI Feedback System
 * 
 * This script diagnoses and fixes duplicate function declarations
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

echo '<h1>🔧 ChatGABI Duplicate Function Fix</h1>';
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #dee2e6; }
.code { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
</style>';

$issues_found = 0;
$issues_fixed = 0;

// Step 1: Check for duplicate function declarations
echo '<div class="section">';
echo '<h2>🔍 Step 1: Duplicate Function Analysis</h2>';

$files_to_check = array(
    'inc/feedback-system.php' => 'Core Feedback System',
    'inc/feedback-admin.php' => 'Admin Interface'
);

$functions_to_check = array(
    'chatgabi_export_feedback_data',
    'chatgabi_export_csv', 
    'chatgabi_export_json',
    'chatgabi_export_xml'
);

$function_locations = array();

foreach ($files_to_check as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        echo "<h3>{$description} ({$file})</h3>";
        $content = file_get_contents($file_path);
        
        foreach ($functions_to_check as $function) {
            if (preg_match('/function\s+' . preg_quote($function) . '\s*\(/', $content, $matches, PREG_OFFSET_CAPTURE)) {
                $line_number = substr_count(substr($content, 0, $matches[0][1]), "\n") + 1;
                $function_locations[$function][] = array(
                    'file' => $file,
                    'line' => $line_number
                );
                echo "<div class='info'>📍 Found {$function}() at line {$line_number}</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ File not found: {$file}</div>";
        $issues_found++;
    }
}

// Check for duplicates
echo '<h3>Duplicate Analysis:</h3>';
$duplicates_found = false;
foreach ($function_locations as $function => $locations) {
    if (count($locations) > 1) {
        echo "<div class='error'>❌ DUPLICATE: {$function}() found in " . count($locations) . " locations:</div>";
        foreach ($locations as $location) {
            echo "<div class='error'>   - {$location['file']} (line {$location['line']})</div>";
        }
        $duplicates_found = true;
        $issues_found++;
    } else {
        echo "<div class='success'>✅ {$function}() - No duplicates</div>";
    }
}

if (!$duplicates_found) {
    echo "<div class='success'>🎉 No duplicate functions found!</div>";
}

echo '</div>';

// Step 2: Test function availability
echo '<div class="section">';
echo '<h2>🔍 Step 2: Function Availability Test</h2>';

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "<div class='success'>✅ {$function}() is available</div>";
    } else {
        echo "<div class='error'>❌ {$function}() is NOT available</div>";
        $issues_found++;
    }
}

echo '</div>';

// Step 3: Test export functionality
echo '<div class="section">';
echo '<h2>🔍 Step 3: Export Functionality Test</h2>';

// Test if we can call the export function without errors
if (function_exists('chatgabi_export_feedback_data')) {
    echo "<div class='info'>🧪 Testing export function call...</div>";
    
    try {
        // Create a test scenario (we'll catch the wp_die before it executes)
        ob_start();
        
        // This should work without fatal errors
        $reflection = new ReflectionFunction('chatgabi_export_feedback_data');
        $params = $reflection->getParameters();
        
        echo "<div class='success'>✅ Export function is callable</div>";
        echo "<div class='info'>📋 Function parameters:</div>";
        foreach ($params as $param) {
            $default = $param->isDefaultValueAvailable() ? $param->getDefaultValue() : 'required';
            echo "<div class='code'>- \${$param->getName()} = " . var_export($default, true) . "</div>";
        }
        
        ob_end_clean();
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Export function test failed: " . $e->getMessage() . "</div>";
        $issues_found++;
    }
} else {
    echo "<div class='error'>❌ Export function not available for testing</div>";
    $issues_found++;
}

echo '</div>';

// Step 4: WordPress site health check
echo '<div class="section">';
echo '<h2>🔍 Step 4: WordPress Site Health Check</h2>';

// Check if WordPress is loading properly
if (function_exists('wp_get_current_user')) {
    echo "<div class='success'>✅ WordPress core functions available</div>";
    
    // Check if admin functions work
    if (function_exists('add_menu_page')) {
        echo "<div class='success'>✅ WordPress admin functions available</div>";
    } else {
        echo "<div class='warning'>⚠️ WordPress admin functions not available (normal for frontend)</div>";
    }
    
    // Check database connection
    global $wpdb;
    if ($wpdb && $wpdb->get_var("SELECT 1") == 1) {
        echo "<div class='success'>✅ Database connection working</div>";
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
        $issues_found++;
    }
    
} else {
    echo "<div class='error'>❌ WordPress core not properly loaded</div>";
    $issues_found++;
}

echo '</div>';

// Step 5: File integrity check
echo '<div class="section">';
echo '<h2>🔍 Step 5: File Integrity Check</h2>';

$critical_files = array(
    'inc/feedback-system.php' => 'Core Feedback System',
    'inc/feedback-admin.php' => 'Admin Interface',
    'assets/js/feedback-rating.js' => 'Frontend JavaScript',
    'assets/css/feedback-rating.css' => 'Frontend Styles'
);

foreach ($critical_files as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        $file_size = filesize($file_path);
        echo "<div class='success'>✅ {$description}: " . number_format($file_size) . " bytes</div>";
        
        // Check for PHP syntax errors
        if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
            $syntax_check = shell_exec("php -l " . escapeshellarg($file_path) . " 2>&1");
            if (strpos($syntax_check, 'No syntax errors') !== false) {
                echo "<div class='success'>✅ {$description}: No syntax errors</div>";
            } else {
                echo "<div class='error'>❌ {$description}: Syntax error detected</div>";
                echo "<div class='code'>" . htmlspecialchars($syntax_check) . "</div>";
                $issues_found++;
            }
        }
    } else {
        echo "<div class='error'>❌ {$description}: File missing</div>";
        $issues_found++;
    }
}

echo '</div>';

// Step 6: Summary and next steps
echo '<div class="section">';
echo '<h2>📊 Summary & Next Steps</h2>';

echo "<div class='info'><strong>Issues Found:</strong> {$issues_found}</div>";
echo "<div class='info'><strong>Issues Fixed:</strong> {$issues_fixed}</div>";

if ($issues_found === 0) {
    echo "<div class='success'>🎉 All checks passed! The duplicate function error has been resolved.</div>";
    
    echo '<h3>✅ System Status: HEALTHY</h3>';
    echo '<ul>';
    echo '<li>✅ No duplicate function declarations</li>';
    echo '<li>✅ All export functions available</li>';
    echo '<li>✅ WordPress loading properly</li>';
    echo '<li>✅ File integrity verified</li>';
    echo '</ul>';
    
    echo '<h3>🚀 Next Steps:</h3>';
    echo '<ol>';
    echo '<li><strong>Test the admin dashboard:</strong> <a href="/wp-admin/admin.php?page=chatgabi-feedback" target="_blank">ChatGABI → User Feedback</a></li>';
    echo '<li><strong>Test export functionality:</strong> Go to Export tab and try downloading data</li>';
    echo '<li><strong>Test frontend integration:</strong> <a href="/test-frontend-feedback-integration.php" target="_blank">Frontend Test Page</a></li>';
    echo '<li><strong>Monitor for errors:</strong> Check WordPress error logs for any remaining issues</li>';
    echo '</ol>';
    
} else {
    echo "<div class='warning'>⚠️ {$issues_found} issues still need attention.</div>";
    
    echo '<h3>🔧 Recommended Actions:</h3>';
    echo '<ul>';
    if ($duplicates_found) echo '<li>Remove remaining duplicate function declarations</li>';
    echo '<li>Check WordPress error logs for detailed error messages</li>';
    echo '<li>Verify file permissions and ownership</li>';
    echo '<li>Test in a staging environment before production</li>';
    echo '</ul>';
}

echo '</div>';

// Step 7: Quick test links
echo '<div class="section">';
echo '<h2>🔗 Quick Test Links</h2>';
echo '<p>';
echo '<a href="/wp-admin/admin.php?page=chatgabi-feedback" style="display: inline-block; padding: 8px 16px; margin: 5px; background: #007cba; color: white; text-decoration: none; border-radius: 4px;" target="_blank">📊 Admin Dashboard</a>';
echo '<a href="/test-frontend-feedback-integration.php" style="display: inline-block; padding: 8px 16px; margin: 5px; background: #28a745; color: white; text-decoration: none; border-radius: 4px;" target="_blank">🧪 Frontend Test</a>';
echo '<a href="/comprehensive-feedback-system-fix.php" style="display: inline-block; padding: 8px 16px; margin: 5px; background: #6c757d; color: white; text-decoration: none; border-radius: 4px;" target="_blank">🔧 System Diagnostic</a>';
echo '</p>';
echo '</div>';

echo '<div class="section">';
echo '<h2>📋 Technical Details</h2>';
echo '<div class="code">';
echo '<strong>WordPress Version:</strong> ' . get_bloginfo('version') . '<br>';
echo '<strong>PHP Version:</strong> ' . PHP_VERSION . '<br>';
echo '<strong>Theme:</strong> ' . get_template() . '<br>';
echo '<strong>Memory Limit:</strong> ' . ini_get('memory_limit') . '<br>';
echo '<strong>Max Execution Time:</strong> ' . ini_get('max_execution_time') . 's<br>';
echo '<strong>Error Reporting:</strong> ' . (error_reporting() ? 'Enabled' : 'Disabled') . '<br>';
echo '</div>';
echo '</div>';
?>

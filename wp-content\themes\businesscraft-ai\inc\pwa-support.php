<?php
/**
 * Progressive Web App (PWA) Support for ChatGABI
 * 
 * Implements PWA functionality including manifest generation,
 * service worker registration, and offline support features.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize PWA support
 */
function chatgabi_init_pwa_support() {
    // Add PWA meta tags and manifest
    add_action('wp_head', 'chatgabi_add_pwa_meta_tags');
    
    // Register service worker
    add_action('wp_footer', 'chatgabi_register_service_worker');
    
    // Add PWA install prompt
    add_action('wp_footer', 'chatgabi_add_install_prompt');
    
    // Handle offline pages
    add_action('template_redirect', 'chatgabi_handle_offline_requests');
    
    // Add PWA admin settings
    add_action('admin_menu', 'chatgabi_add_pwa_admin_menu');
    
    // AJAX handlers for PWA features
    add_action('wp_ajax_chatgabi_get_offline_data', 'chatgabi_handle_get_offline_data');
    add_action('wp_ajax_chatgabi_sync_offline_queue', 'chatgabi_handle_sync_offline_queue');
    
    // Enqueue PWA scripts
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_pwa_scripts');
}
add_action('init', 'chatgabi_init_pwa_support');

/**
 * Add PWA meta tags to head
 */
function chatgabi_add_pwa_meta_tags() {
    $manifest_url = get_template_directory_uri() . '/manifest.json';
    $theme_color = get_option('chatgabi_pwa_theme_color', '#2563eb');
    $background_color = get_option('chatgabi_pwa_background_color', '#ffffff');
    
    echo '<meta name="theme-color" content="' . esc_attr($theme_color) . '">' . "\n";
    echo '<meta name="background-color" content="' . esc_attr($background_color) . '">' . "\n";
    echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="apple-mobile-web-app-status-bar-style" content="default">' . "\n";
    echo '<meta name="apple-mobile-web-app-title" content="ChatGABI">' . "\n";
    echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="application-name" content="ChatGABI">' . "\n";
    echo '<link rel="manifest" href="' . esc_url($manifest_url) . '">' . "\n";
    
    // Apple touch icons
    $icon_sizes = array(57, 60, 72, 76, 114, 120, 144, 152, 180);
    foreach ($icon_sizes as $size) {
        $icon_url = get_template_directory_uri() . "/assets/images/apple-touch-icon-{$size}x{$size}.png";
        echo '<link rel="apple-touch-icon" sizes="' . $size . 'x' . $size . '" href="' . esc_url($icon_url) . '">' . "\n";
    }
    
    // Microsoft tiles
    echo '<meta name="msapplication-TileColor" content="' . esc_attr($theme_color) . '">' . "\n";
    echo '<meta name="msapplication-TileImage" content="' . get_template_directory_uri() . '/assets/images/mstile-144x144.png">' . "\n";
    
    // Preload critical resources
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/js/offline-queue.js" as="script">' . "\n";
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/js/local-storage-manager.js" as="script">' . "\n";
}

/**
 * Register service worker
 */
function chatgabi_register_service_worker() {
    $sw_url = get_template_directory_uri() . '/sw.js';
    $sw_scope = home_url('/');
    
    ?>
    <script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('<?php echo esc_url($sw_url); ?>', {
                scope: '<?php echo esc_url($sw_scope); ?>'
            })
            .then(function(registration) {
                console.log('ChatGABI: Service Worker registered successfully:', registration.scope);
                
                // Listen for updates
                registration.addEventListener('updatefound', function() {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', function() {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New version available
                            chatgabiShowUpdateNotification();
                        }
                    });
                });
            })
            .catch(function(error) {
                console.log('ChatGABI: Service Worker registration failed:', error);
            });
        });
    }
    
    // Show update notification
    function chatgabiShowUpdateNotification() {
        if (confirm('A new version of ChatGABI is available. Refresh to update?')) {
            window.location.reload();
        }
    }
    </script>
    <?php
}

/**
 * Add PWA install prompt
 */
function chatgabi_add_install_prompt() {
    if (!get_option('chatgabi_pwa_show_install_prompt', true)) {
        return;
    }
    
    ?>
    <div id="pwa-install-prompt" class="pwa-install-prompt" style="display: none;">
        <div class="install-prompt-content">
            <div class="install-prompt-icon">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/icon-72x72.png" alt="ChatGABI">
            </div>
            <div class="install-prompt-text">
                <h3>Install ChatGABI</h3>
                <p>Get the full app experience with offline access and faster loading.</p>
            </div>
            <div class="install-prompt-actions">
                <button id="pwa-install-button" class="install-button">Install</button>
                <button id="pwa-dismiss-button" class="dismiss-button">Not now</button>
            </div>
        </div>
    </div>
    
    <script>
    let deferredPrompt;
    
    window.addEventListener('beforeinstallprompt', function(e) {
        // Prevent the mini-infobar from appearing on mobile
        e.preventDefault();
        
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        
        // Show custom install prompt
        const installPrompt = document.getElementById('pwa-install-prompt');
        if (installPrompt && !localStorage.getItem('chatgabi_install_dismissed')) {
            installPrompt.style.display = 'block';
        }
    });
    
    document.addEventListener('DOMContentLoaded', function() {
        const installButton = document.getElementById('pwa-install-button');
        const dismissButton = document.getElementById('pwa-dismiss-button');
        const installPrompt = document.getElementById('pwa-install-prompt');
        
        if (installButton) {
            installButton.addEventListener('click', function() {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    
                    deferredPrompt.userChoice.then(function(choiceResult) {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('ChatGABI: PWA installation accepted');
                        } else {
                            console.log('ChatGABI: PWA installation dismissed');
                        }
                        deferredPrompt = null;
                        installPrompt.style.display = 'none';
                    });
                }
            });
        }
        
        if (dismissButton) {
            dismissButton.addEventListener('click', function() {
                installPrompt.style.display = 'none';
                localStorage.setItem('chatgabi_install_dismissed', 'true');
            });
        }
    });
    
    // Hide install prompt if already installed
    window.addEventListener('appinstalled', function() {
        console.log('ChatGABI: PWA was installed');
        document.getElementById('pwa-install-prompt').style.display = 'none';
    });
    </script>
    
    <style>
    .pwa-install-prompt {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .install-prompt-content {
        display: flex;
        align-items: center;
        padding: 16px;
        gap: 12px;
    }
    
    .install-prompt-icon img {
        width: 48px;
        height: 48px;
        border-radius: 8px;
    }
    
    .install-prompt-text {
        flex: 1;
    }
    
    .install-prompt-text h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
    }
    
    .install-prompt-text p {
        margin: 0;
        font-size: 14px;
        color: #666;
    }
    
    .install-prompt-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .install-button {
        background: #2563eb;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
    }
    
    .dismiss-button {
        background: transparent;
        color: #666;
        border: none;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
    }
    
    @media (max-width: 480px) {
        .pwa-install-prompt {
            left: 10px;
            right: 10px;
            bottom: 10px;
        }
        
        .install-prompt-content {
            padding: 12px;
        }
    }
    </style>
    <?php
}

/**
 * Handle offline requests
 */
function chatgabi_handle_offline_requests() {
    // Check if this is an offline request
    if (isset($_GET['offline']) && $_GET['offline'] === '1') {
        $page = sanitize_text_field($_GET['page'] ?? 'dashboard');
        
        switch ($page) {
            case 'dashboard':
                chatgabi_serve_offline_dashboard();
                break;
            case 'chat':
                chatgabi_serve_offline_chat();
                break;
            default:
                chatgabi_serve_offline_fallback();
                break;
        }
        exit;
    }
}

/**
 * Serve offline dashboard
 */
function chatgabi_serve_offline_dashboard() {
    header('Content-Type: text/html; charset=utf-8');
    
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ChatGABI - Offline Dashboard</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .offline-container { max-width: 800px; margin: 0 auto; background: white; border-radius: 12px; padding: 24px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .offline-header { text-align: center; margin-bottom: 32px; }
            .offline-icon { font-size: 48px; margin-bottom: 16px; }
            .offline-title { font-size: 24px; font-weight: 600; margin-bottom: 8px; }
            .offline-subtitle { color: #666; font-size: 16px; }
            .offline-features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 32px; }
            .feature-card { padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; }
            .feature-icon { font-size: 24px; margin-bottom: 12px; }
            .feature-title { font-weight: 600; margin-bottom: 8px; }
            .feature-description { color: #666; font-size: 14px; }
            .sync-status { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 12px; margin-top: 24px; }
        </style>
    </head>
    <body>
        <div class="offline-container">
            <div class="offline-header">
                <div class="offline-icon">📱</div>
                <h1 class="offline-title">ChatGABI Offline</h1>
                <p class="offline-subtitle">You're currently offline, but you can still access some features</p>
            </div>
            
            <div class="offline-features">
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3 class="feature-title">Cached Templates</h3>
                    <p class="feature-description">Access previously viewed business templates and continue working on your plans.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💾</div>
                    <h3 class="feature-title">Local Storage</h3>
                    <p class="feature-description">Your recent conversations and preferences are saved locally.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3 class="feature-title">Auto Sync</h3>
                    <p class="feature-description">Changes will sync automatically when you're back online.</p>
                </div>
            </div>
            
            <div class="sync-status">
                <strong>📡 Connection Status:</strong> <span id="connection-status">Offline</span>
                <br>
                <small>Queued actions will sync when connection is restored.</small>
            </div>
        </div>
        
        <script>
            // Monitor connection status
            function updateConnectionStatus() {
                const status = navigator.onLine ? 'Online' : 'Offline';
                document.getElementById('connection-status').textContent = status;
                
                if (navigator.onLine) {
                    // Redirect to online dashboard
                    window.location.href = '/dashboard';
                }
            }
            
            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);
            updateConnectionStatus();
        </script>
    </body>
    </html>
    <?php
}

/**
 * Serve offline chat
 */
function chatgabi_serve_offline_chat() {
    header('Content-Type: text/html; charset=utf-8');
    
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ChatGABI - Offline Chat</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .chat-container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; padding: 24px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .offline-notice { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 16px; margin-bottom: 24px; text-align: center; }
            .chat-history { max-height: 400px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; margin-bottom: 16px; }
            .message { margin-bottom: 16px; }
            .message.user { text-align: right; }
            .message-content { display: inline-block; padding: 8px 12px; border-radius: 12px; max-width: 80%; }
            .message.user .message-content { background: #2563eb; color: white; }
            .message.ai .message-content { background: #f0f0f0; color: #333; }
            .offline-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; margin-bottom: 12px; }
            .queue-info { font-size: 12px; color: #666; text-align: center; }
        </style>
    </head>
    <body>
        <div class="chat-container">
            <div class="offline-notice">
                <strong>⚠️ Offline Mode</strong><br>
                Messages will be queued and sent when you're back online.
            </div>
            
            <div class="chat-history" id="chat-history">
                <!-- Chat history will be loaded from localStorage -->
            </div>
            
            <textarea class="offline-input" id="message-input" placeholder="Type your message (will be queued for when online)..." rows="3"></textarea>
            <button onclick="queueMessage()" style="width: 100%; padding: 12px; background: #2563eb; color: white; border: none; border-radius: 6px; font-weight: 500;">Queue Message</button>
            
            <div class="queue-info">
                <span id="queue-count">0</span> messages queued for sync
            </div>
        </div>
        
        <script>
            // Load chat history from localStorage
            function loadChatHistory() {
                const history = JSON.parse(localStorage.getItem('chatgabi_chat_history') || '[]');
                const container = document.getElementById('chat-history');
                
                container.innerHTML = '';
                history.slice(-10).forEach(message => {
                    const messageEl = document.createElement('div');
                    messageEl.className = `message ${message.type}`;
                    messageEl.innerHTML = `<div class="message-content">${message.content}</div>`;
                    container.appendChild(messageEl);
                });
                
                container.scrollTop = container.scrollHeight;
            }
            
            // Queue message for sync
            function queueMessage() {
                const input = document.getElementById('message-input');
                const message = input.value.trim();
                
                if (!message) return;
                
                // Add to queue
                const queue = JSON.parse(localStorage.getItem('chatgabi_offline_queue') || '[]');
                queue.push({
                    type: 'chat_message',
                    content: message,
                    timestamp: Date.now()
                });
                localStorage.setItem('chatgabi_offline_queue', JSON.stringify(queue));
                
                // Add to chat history
                const history = JSON.parse(localStorage.getItem('chatgabi_chat_history') || '[]');
                history.push({
                    type: 'user',
                    content: message,
                    timestamp: Date.now(),
                    queued: true
                });
                localStorage.setItem('chatgabi_chat_history', JSON.stringify(history));
                
                // Update UI
                input.value = '';
                loadChatHistory();
                updateQueueCount();
            }
            
            // Update queue count
            function updateQueueCount() {
                const queue = JSON.parse(localStorage.getItem('chatgabi_offline_queue') || '[]');
                document.getElementById('queue-count').textContent = queue.length;
            }
            
            // Initialize
            loadChatHistory();
            updateQueueCount();
            
            // Monitor connection
            window.addEventListener('online', function() {
                window.location.href = '/dashboard';
            });
        </script>
    </body>
    </html>
    <?php
}

/**
 * Enqueue PWA scripts
 */
function chatgabi_enqueue_pwa_scripts() {
    // Only enqueue on frontend
    if (is_admin()) {
        return;
    }
    
    wp_enqueue_script(
        'chatgabi-offline-queue',
        get_template_directory_uri() . '/assets/js/offline-queue.js',
        array('jquery'),
        CHATGABI_VERSION,
        true
    );
    
    wp_enqueue_script(
        'chatgabi-local-storage',
        get_template_directory_uri() . '/assets/js/local-storage-manager.js',
        array('jquery'),
        CHATGABI_VERSION,
        true
    );
    
    // Localize scripts with PWA data
    wp_localize_script('chatgabi-offline-queue', 'chatgabiPWA', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'restUrl' => rest_url('chatgabi/v1/'),
        'nonce' => wp_create_nonce('chatgabi_pwa_nonce'),
        'offlineEnabled' => get_option('chatgabi_pwa_offline_enabled', true),
        'syncInterval' => get_option('chatgabi_pwa_sync_interval', 30000), // 30 seconds
        'maxQueueSize' => get_option('chatgabi_pwa_max_queue_size', 100)
    ));
}

/**
 * AJAX handler for getting offline data
 */
function chatgabi_handle_get_offline_data() {
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not authenticated');
        return;
    }
    
    $data_type = sanitize_text_field($_GET['type'] ?? 'templates');
    
    $offline_data = array();
    
    switch ($data_type) {
        case 'templates':
            $offline_data = chatgabi_get_offline_templates($user_id);
            break;
        case 'sectors':
            $offline_data = chatgabi_get_offline_sectors($user_id);
            break;
        case 'conversations':
            $offline_data = chatgabi_get_offline_conversations($user_id);
            break;
    }
    
    wp_send_json_success($offline_data);
}

/**
 * Get offline templates
 */
function chatgabi_get_offline_templates($user_id) {
    // Get user's most used templates
    global $wpdb;
    
    $usage_table = $wpdb->prefix . 'chatgabi_template_usage';
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    
    $templates = $wpdb->get_results($wpdb->prepare("
        SELECT t.*, u.usage_count
        FROM {$templates_table} t
        LEFT JOIN {$usage_table} u ON t.id = u.template_id AND u.user_id = %d
        WHERE t.is_active = 1
        ORDER BY u.usage_count DESC, t.created_at DESC
        LIMIT 20
    ", $user_id));
    
    return $templates;
}

/**
 * Get offline sectors
 */
function chatgabi_get_offline_sectors($user_id) {
    // Get user's relevant sectors
    $user_profile = chatgabi_get_user_profile($user_id);
    
    if (!$user_profile) {
        return array();
    }
    
    // Load sector data from JSON files
    $sectors_data = array();
    $country = $user_profile->target_country ?? 'GH';
    
    $sector_file = WP_CONTENT_DIR . "/datasets/sectors/{$country}.json";
    if (file_exists($sector_file)) {
        $sectors_data = json_decode(file_get_contents($sector_file), true);
    }
    
    return $sectors_data;
}

/**
 * Get offline conversations
 */
function chatgabi_get_offline_conversations($user_id) {
    global $wpdb;
    
    $conversations_table = $wpdb->prefix . 'chatgabi_conversations';
    
    $conversations = $wpdb->get_results($wpdb->prepare("
        SELECT *
        FROM {$conversations_table}
        WHERE user_id = %d
        ORDER BY created_at DESC
        LIMIT 50
    ", $user_id));
    
    return $conversations;
}

<?php
// ChatGABI Performance Monitor
function chatgabi_log_performance($operation, $start_time) {
    $elapsed = microtime(true) - $start_time;
    if ($elapsed > 5) { // Log operations taking more than 5 seconds
        $log_entry = date("Y-m-d H:i:s") . " - SLOW: {$operation} took " . number_format($elapsed, 3) . " seconds\n";
        file_put_contents(WP_CONTENT_DIR . "/chatgabi-performance.log", $log_entry, FILE_APPEND | LOCK_EX);
    }
}
?>
/**
 * ChatGABI Engagement Analytics Dashboard Styles
 */

/* Summary Cards */
.engagement-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0 30px 0;
}

.summary-card {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    transition: box-shadow 0.3s ease;
}

.summary-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 32px;
    font-weight: 700;
    color: #0073aa;
    margin: 10px 0;
    line-height: 1;
}

.summary-period {
    font-size: 12px;
    color: #8c8f94;
    margin: 0;
}

/* Charts Grid */
.engagement-charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.chart-card {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.chart-card-wide {
    grid-column: 1 / -1;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 20px;
}

.chart-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.chart-controls select {
    padding: 4px 8px;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    font-size: 12px;
    background: #ffffff;
}

.chart-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-period {
    font-size: 12px;
    color: #8c8f94;
    background: #f6f7f7;
    padding: 4px 8px;
    border-radius: 3px;
}

.chart-container {
    padding: 0 20px 20px 20px;
    position: relative;
    height: 300px;
}

.chart-card-wide .chart-container {
    height: 250px;
}

/* Keyword Cloud */
.keyword-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 20px;
    min-height: 200px;
    align-content: flex-start;
}

.keyword-tag {
    display: inline-block;
    background: #f6f7f7;
    color: #0073aa;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.keyword-tag:hover {
    background: #0073aa;
    color: #ffffff;
    transform: translateY(-1px);
}

.keyword-tag small {
    opacity: 0.7;
    margin-left: 4px;
}

/* Placeholder Card */
.chart-placeholder {
    opacity: 0.6;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
    color: #8c8f94;
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.placeholder-content p {
    margin: 4px 0;
    font-size: 14px;
}

.placeholder-note {
    font-size: 12px !important;
    opacity: 0.7;
}

/* Data Table */
.engagement-data-table {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin: 30px 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f1f1f1;
}

.table-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.table-controls {
    display: flex;
    gap: 10px;
}

.table-controls .button {
    font-size: 12px;
    padding: 4px 12px;
    height: auto;
}

/* Status indicators */
.status-success {
    color: #46b450;
    font-weight: 600;
}

.status-warning {
    color: #ffb900;
    font-weight: 600;
}

.status-error {
    color: #dc3232;
    font-weight: 600;
}

/* Cache info */
.cache-info {
    margin: 20px 0;
    padding: 15px;
    background: #f6f7f7;
    border-left: 4px solid #0073aa;
    border-radius: 0 4px 4px 0;
}

.cache-info .description {
    margin: 0;
    font-size: 12px;
    color: #646970;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .engagement-charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-card-wide {
        grid-column: 1;
    }
}

@media (max-width: 768px) {
    .engagement-summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .summary-card {
        padding: 15px;
    }
    
    .summary-value {
        font-size: 24px;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .keyword-cloud {
        padding: 15px;
    }
    
    .keyword-tag {
        font-size: 11px;
        padding: 4px 8px;
    }
}

@media (max-width: 480px) {
    .engagement-summary-cards {
        grid-template-columns: 1fr 1fr;
    }
    
    .summary-card h3 {
        font-size: 12px;
    }
    
    .summary-value {
        font-size: 20px;
    }
    
    .chart-container {
        height: 200px;
        padding: 0 10px 15px 10px;
    }
    
    .chart-header {
        padding: 15px 15px 0 15px;
    }
}

/* Loading states */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #8c8f94;
    font-size: 14px;
}

.chart-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #f1f1f1;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart.js custom styles */
.chart-container canvas {
    max-height: 100% !important;
}

/* Accessibility improvements */
.chart-card:focus-within {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.keyword-tag:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .engagement-summary-cards,
    .engagement-charts-grid {
        display: block;
    }
    
    .chart-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    .table-controls,
    .chart-controls {
        display: none;
    }
}

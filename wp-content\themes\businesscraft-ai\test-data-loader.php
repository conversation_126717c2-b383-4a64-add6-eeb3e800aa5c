<?php
/**
 * Test script for data loader functionality
 *
 * This file tests the load_business_dataset_by_country() function
 * to ensure it correctly loads and parses the JSON datasets.
 *
 * Usage: Access via browser at /wp-content/themes/businesscraft-ai/test-data-loader.php
 * or run via WP-CLI: wp eval-file test-data-loader.php
 */

// Load WordPress
require_once(__DIR__ . '/../../../wp-load.php');

// Test function
function test_data_loader() {
    echo "<h1>BusinessCraft AI Data Loader Test</h1>\n";

    $countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];

    foreach ($countries as $country) {
        echo "<h2>Testing: {$country}</h2>\n";

        $data = load_business_dataset_by_country($country);

        if ($data === false) {
            echo "<p style='color: red;'>❌ Failed to load data for {$country}</p>\n";
            continue;
        }

        echo "<p style='color: green;'>✅ Successfully loaded data for {$country}</p>\n";

        // Validate structure
        if (isset($data['country']) && isset($data['sectors'])) {
            echo "<p>📊 Country: {$data['country']}</p>\n";
            echo "<p>📈 Sectors found: " . count($data['sectors']) . "</p>\n";

            // Show first sector as example
            if (!empty($data['sectors'])) {
                $first_sector = $data['sectors'][0];
                echo "<p>🏢 First sector: {$first_sector['sector_name']}</p>\n";
                echo "<p>📝 Overview: " . substr($first_sector['overview'], 0, 100) . "...</p>\n";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Data structure validation failed for {$country}</p>\n";
        }

        echo "<hr>\n";
    }

    // Test invalid country
    echo "<h2>Testing: Invalid Country</h2>\n";
    $invalid_data = load_business_dataset_by_country('InvalidCountry');
    if ($invalid_data === false) {
        echo "<p style='color: green;'>✅ Correctly rejected invalid country</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Should have rejected invalid country</p>\n";
    }

    // Test helper functions
    echo "<h2>Testing Helper Functions</h2>\n";
    $available_countries = get_available_dataset_countries();
    echo "<p>Available countries: " . implode(', ', $available_countries) . "</p>\n";

    foreach (['Ghana', 'InvalidCountry'] as $test_country) {
        $is_available = is_dataset_country_available($test_country);
        $status = $is_available ? '✅' : '❌';
        echo "<p>{$status} {$test_country} availability: " . ($is_available ? 'Available' : 'Not Available') . "</p>\n";
    }
}

// Run test if accessed directly
if (!defined('WP_CLI') || !WP_CLI) {
    // Set content type for browser viewing
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html><html><head><title>Data Loader Test</title></head><body>";
    test_data_loader();
    echo "</body></html>";
} else {
    // WP-CLI output
    test_data_loader();
}

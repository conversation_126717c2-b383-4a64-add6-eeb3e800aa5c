{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/query-total", "title": "Query Total", "category": "theme", "ancestor": ["core/query"], "description": "Display the total number of results in a query.", "textdomain": "default", "attributes": {"displayType": {"type": "string", "default": "total-results"}}, "usesContext": ["queryId", "query"], "supports": {"align": ["wide", "full"], "html": false, "spacing": {"margin": true, "padding": true}, "color": {"gradients": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}, "interactivity": {"clientNavigation": true}}, "style": "wp-block-query-total"}
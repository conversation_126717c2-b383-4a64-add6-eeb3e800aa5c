<?php
/**
 * ChatGABI Feedback Admin Dashboard
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add feedback submenu to ChatGABI admin
 * NOTE: This function is now handled by admin-dashboard.php to prevent duplicates
 * Keeping this as a fallback in case admin-dashboard.php is not loaded
 */
function chatgabi_add_feedback_admin_menu() {
    // Check if the menu has already been registered by admin-dashboard.php
    global $submenu;

    if (isset($submenu['chatgabi-main'])) {
        foreach ($submenu['chatgabi-main'] as $item) {
            if (isset($item[2]) && $item[2] === 'chatgabi-feedback') {
                // Menu already exists, don't add duplicate
                return;
            }
        }
    }

    // Only add if not already registered
    add_submenu_page(
        'chatgabi-main',
        __('User Feedback', 'chatgabi'),
        __('User Feedback', 'chatgabi'),
        'manage_options',
        'chatgabi-feedback',
        'chatgabi_feedback_admin_page'
    );
}
// Use lower priority to run after admin-dashboard.php
add_action('admin_menu', 'chatgabi_add_feedback_admin_menu', 25);

/**
 * Feedback admin page
 */
function chatgabi_feedback_admin_page() {
    $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'overview';
    
    ?>
    <div class="wrap">
        <h1><?php _e('User Feedback Analytics', 'chatgabi'); ?></h1>
        <p class="description"><?php _e('Monitor user satisfaction and feedback for AI responses', 'chatgabi'); ?></p>

        <!-- Tab Navigation -->
        <nav class="nav-tab-wrapper">
            <a href="?page=chatgabi-feedback&tab=overview" class="nav-tab <?php echo $current_tab === 'overview' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Overview', 'chatgabi'); ?>
            </a>
            <a href="?page=chatgabi-feedback&tab=ratings" class="nav-tab <?php echo $current_tab === 'ratings' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Ratings Analysis', 'chatgabi'); ?>
            </a>
            <a href="?page=chatgabi-feedback&tab=feedback" class="nav-tab <?php echo $current_tab === 'feedback' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Text Feedback', 'chatgabi'); ?>
            </a>
            <a href="?page=chatgabi-feedback&tab=training" class="nav-tab <?php echo $current_tab === 'training' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Training Data', 'chatgabi'); ?>
            </a>
            <a href="?page=chatgabi-feedback&tab=export" class="nav-tab <?php echo $current_tab === 'export' ? 'nav-tab-active' : ''; ?>">
                <?php _e('Export', 'chatgabi'); ?>
            </a>
        </nav>

        <!-- Tab Content -->
        <div class="tab-content">
            <?php
            switch ($current_tab) {
                case 'overview':
                    chatgabi_render_feedback_overview();
                    break;
                case 'ratings':
                    chatgabi_render_ratings_analysis();
                    break;
                case 'feedback':
                    chatgabi_render_text_feedback();
                    break;
                case 'training':
                    chatgabi_render_training_data();
                    break;
                case 'export':
                    chatgabi_render_feedback_export();
                    break;
                default:
                    chatgabi_render_feedback_overview();
            }
            ?>
        </div>
    </div>

    <?php chatgabi_render_feedback_admin_styles(); ?>
    <?php chatgabi_render_feedback_admin_scripts(); ?>
    <?php
}

/**
 * Render feedback overview tab
 */
function chatgabi_render_feedback_overview() {
    $stats_30_days = chatgabi_get_feedback_stats(30);
    $stats_7_days = chatgabi_get_feedback_stats(7);
    
    // Country breakdown
    $countries = array('Ghana', 'Kenya', 'Nigeria', 'South Africa');
    $country_stats = array();
    foreach ($countries as $country) {
        $country_stats[$country] = chatgabi_get_feedback_stats(30, $country);
    }
    
    ?>
    <div class="feedback-overview-dashboard">
        <!-- Summary Cards -->
        <div class="feedback-summary-cards">
            <div class="summary-card">
                <div class="card-icon">📊</div>
                <div class="card-content">
                    <h3><?php echo number_format($stats_30_days['overall']->total_feedback); ?></h3>
                    <p><?php _e('Total Feedback (30 days)', 'chatgabi'); ?></p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">⭐</div>
                <div class="card-content">
                    <h3><?php echo number_format($stats_30_days['overall']->avg_rating ?? 0, 2); ?></h3>
                    <p><?php _e('Average Rating', 'chatgabi'); ?></p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">👍</div>
                <div class="card-content">
                    <h3><?php echo number_format(($stats_30_days['overall']->positive_feedback / max($stats_30_days['overall']->total_feedback, 1)) * 100, 1); ?>%</h3>
                    <p><?php _e('Positive Feedback', 'chatgabi'); ?></p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">💬</div>
                <div class="card-content">
                    <h3><?php echo number_format($stats_30_days['overall']->text_feedback_count ?? 0); ?></h3>
                    <p><?php _e('Text Feedback', 'chatgabi'); ?></p>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="feedback-charts-grid">
            <!-- Rating Distribution -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Rating Distribution (30 days)', 'chatgabi'); ?></h3>
                </div>
                <div class="chart-container">
                    <canvas id="ratingDistributionChart" width="100%" height="60"></canvas>
                </div>
            </div>

            <!-- Country Comparison -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Satisfaction by Country', 'chatgabi'); ?></h3>
                </div>
                <div class="chart-container">
                    <canvas id="countryComparisonChart" width="100%" height="60"></canvas>
                </div>
            </div>

            <!-- Category Ratings -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Category Performance', 'chatgabi'); ?></h3>
                </div>
                <div class="chart-container">
                    <canvas id="categoryRatingsChart" width="100%" height="60"></canvas>
                </div>
            </div>

            <!-- Trend Analysis -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3><?php _e('Satisfaction Trend', 'chatgabi'); ?></h3>
                    <div class="chart-controls">
                        <select id="trendPeriodFilter">
                            <option value="7"><?php _e('Last 7 days', 'chatgabi'); ?></option>
                            <option value="30" selected><?php _e('Last 30 days', 'chatgabi'); ?></option>
                            <option value="90"><?php _e('Last 90 days', 'chatgabi'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="satisfactionTrendChart" width="100%" height="60"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Feedback -->
        <div class="recent-feedback-section">
            <h3><?php _e('Recent Feedback', 'chatgabi'); ?></h3>
            <div class="recent-feedback-list">
                <?php chatgabi_render_recent_feedback(10); ?>
            </div>
        </div>
    </div>

    <script>
    // Pass data to JavaScript
    window.chatgabiFeedbackData = {
        ratingDistribution: <?php echo json_encode($stats_30_days['distribution']); ?>,
        countryStats: <?php echo json_encode($country_stats); ?>,
        categoryStats: <?php echo json_encode($stats_30_days['categories']); ?>
    };
    </script>
    <?php
}

/**
 * Render ratings analysis tab
 */
function chatgabi_render_ratings_analysis() {
    $period = isset($_GET['period']) ? absint($_GET['period']) : 30;
    $country = isset($_GET['country']) ? sanitize_text_field($_GET['country']) : null;
    $sector = isset($_GET['sector']) ? sanitize_text_field($_GET['sector']) : null;
    
    $stats = chatgabi_get_feedback_stats($period, $country, $sector);
    
    ?>
    <div class="ratings-analysis-dashboard">
        <!-- Filters -->
        <div class="analysis-filters">
            <form method="get" action="">
                <input type="hidden" name="page" value="chatgabi-feedback">
                <input type="hidden" name="tab" value="ratings">
                
                <div class="filter-group">
                    <label for="period"><?php _e('Time Period:', 'chatgabi'); ?></label>
                    <select name="period" id="period">
                        <option value="7" <?php selected($period, 7); ?>><?php _e('Last 7 days', 'chatgabi'); ?></option>
                        <option value="30" <?php selected($period, 30); ?>><?php _e('Last 30 days', 'chatgabi'); ?></option>
                        <option value="90" <?php selected($period, 90); ?>><?php _e('Last 90 days', 'chatgabi'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="country"><?php _e('Country:', 'chatgabi'); ?></label>
                    <select name="country" id="country">
                        <option value=""><?php _e('All Countries', 'chatgabi'); ?></option>
                        <option value="Ghana" <?php selected($country, 'Ghana'); ?>><?php _e('Ghana', 'chatgabi'); ?></option>
                        <option value="Kenya" <?php selected($country, 'Kenya'); ?>><?php _e('Kenya', 'chatgabi'); ?></option>
                        <option value="Nigeria" <?php selected($country, 'Nigeria'); ?>><?php _e('Nigeria', 'chatgabi'); ?></option>
                        <option value="South Africa" <?php selected($country, 'South Africa'); ?>><?php _e('South Africa', 'chatgabi'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="sector"><?php _e('Sector:', 'chatgabi'); ?></label>
                    <select name="sector" id="sector">
                        <option value=""><?php _e('All Sectors', 'chatgabi'); ?></option>
                        <?php
                        // Get available sectors
                        $sectors = chatgabi_get_feedback_sectors();
                        foreach ($sectors as $sector_option) {
                            echo '<option value="' . esc_attr($sector_option) . '" ' . selected($sector, $sector_option, false) . '>' . esc_html($sector_option) . '</option>';
                        }
                        ?>
                    </select>
                </div>
                
                <button type="submit" class="button button-primary"><?php _e('Apply Filters', 'chatgabi'); ?></button>
            </form>
        </div>

        <!-- Detailed Statistics -->
        <div class="detailed-stats-grid">
            <div class="stat-card">
                <h4><?php _e('Overall Rating', 'chatgabi'); ?></h4>
                <div class="stat-value"><?php echo number_format($stats['overall']->avg_rating ?? 0, 2); ?>/5</div>
                <div class="stat-description"><?php printf(__('Based on %d ratings', 'chatgabi'), $stats['overall']->total_feedback); ?></div>
            </div>
            
            <div class="stat-card">
                <h4><?php _e('Satisfaction Rate', 'chatgabi'); ?></h4>
                <div class="stat-value"><?php echo number_format(($stats['overall']->positive_feedback / max($stats['overall']->total_feedback, 1)) * 100, 1); ?>%</div>
                <div class="stat-description"><?php _e('4+ star ratings', 'chatgabi'); ?></div>
            </div>
            
            <div class="stat-card">
                <h4><?php _e('Helpfulness', 'chatgabi'); ?></h4>
                <div class="stat-value"><?php echo number_format($stats['categories']->avg_helpfulness ?? 0, 2); ?>/5</div>
                <div class="stat-description"><?php _e('Category rating', 'chatgabi'); ?></div>
            </div>
            
            <div class="stat-card">
                <h4><?php _e('Accuracy', 'chatgabi'); ?></h4>
                <div class="stat-value"><?php echo number_format($stats['categories']->avg_accuracy ?? 0, 2); ?>/5</div>
                <div class="stat-description"><?php _e('Category rating', 'chatgabi'); ?></div>
            </div>
            
            <div class="stat-card">
                <h4><?php _e('Relevance', 'chatgabi'); ?></h4>
                <div class="stat-value"><?php echo number_format($stats['categories']->avg_relevance ?? 0, 2); ?>/5</div>
                <div class="stat-description"><?php _e('Category rating', 'chatgabi'); ?></div>
            </div>
            
            <div class="stat-card">
                <h4><?php _e('Clarity', 'chatgabi'); ?></h4>
                <div class="stat-value"><?php echo number_format($stats['categories']->avg_clarity ?? 0, 2); ?>/5</div>
                <div class="stat-description"><?php _e('Category rating', 'chatgabi'); ?></div>
            </div>
        </div>

        <!-- Rating Distribution Table -->
        <div class="rating-distribution-table">
            <h3><?php _e('Rating Distribution', 'chatgabi'); ?></h3>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Rating', 'chatgabi'); ?></th>
                        <th><?php _e('Count', 'chatgabi'); ?></th>
                        <th><?php _e('Percentage', 'chatgabi'); ?></th>
                        <th><?php _e('Visual', 'chatgabi'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $total_ratings = $stats['overall']->total_feedback;
                    for ($i = 5; $i >= 1; $i--) {
                        $count = 0;
                        foreach ($stats['distribution'] as $rating) {
                            if ($rating->rating_score == $i) {
                                $count = $rating->count;
                                break;
                            }
                        }
                        $percentage = $total_ratings > 0 ? ($count / $total_ratings) * 100 : 0;
                        ?>
                        <tr>
                            <td>
                                <?php echo str_repeat('⭐', $i); ?>
                                (<?php echo $i; ?>)
                            </td>
                            <td><?php echo number_format($count); ?></td>
                            <td><?php echo number_format($percentage, 1); ?>%</td>
                            <td>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: <?php echo $percentage; ?>%"></div>
                                </div>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
}

/**
 * Get available sectors from feedback data
 */
function chatgabi_get_feedback_sectors() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_feedback';
    
    $sectors = $wpdb->get_col(
        "SELECT DISTINCT user_sector 
         FROM $table_name 
         WHERE user_sector IS NOT NULL 
         AND user_sector != '' 
         ORDER BY user_sector"
    );
    
    return $sectors ?: array();
}

/**
 * Render recent feedback
 */
function chatgabi_render_recent_feedback($limit = 10) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_feedback';

    $recent_feedback = $wpdb->get_results($wpdb->prepare(
        "SELECT f.*, u.display_name
         FROM $table_name f
         LEFT JOIN {$wpdb->users} u ON f.user_id = u.ID
         WHERE f.feedback_text IS NOT NULL
         AND f.feedback_text != ''
         ORDER BY f.created_at DESC
         LIMIT %d",
        $limit
    ));

    if (empty($recent_feedback)) {
        echo '<p>' . __('No recent text feedback available.', 'chatgabi') . '</p>';
        return;
    }

    foreach ($recent_feedback as $feedback) {
        ?>
        <div class="feedback-item">
            <div class="feedback-header">
                <div class="feedback-rating">
                    <?php echo str_repeat('⭐', $feedback->rating_score); ?>
                    <span class="rating-number">(<?php echo $feedback->rating_score; ?>/5)</span>
                </div>
                <div class="feedback-meta">
                    <span class="user-name"><?php echo esc_html($feedback->display_name ?: 'Anonymous'); ?></span>
                    <span class="feedback-date"><?php echo human_time_diff(strtotime($feedback->created_at)); ?> ago</span>
                    <?php if ($feedback->user_country): ?>
                        <span class="user-country"><?php echo esc_html($feedback->user_country ?: ''); ?></span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="feedback-text">
                <?php echo esc_html($feedback->feedback_text ?: ''); ?>
            </div>
        </div>
        <?php
    }
}

/**
 * Render feedback admin styles
 */
function chatgabi_render_feedback_admin_styles() {
    ?>
    <style>
    .feedback-overview-dashboard {
        margin-top: 20px;
    }

    .feedback-summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .summary-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .summary-card .card-icon {
        font-size: 2em;
        margin-bottom: 10px;
    }

    .summary-card h3 {
        font-size: 2.5em;
        margin: 0;
        color: #007cba;
    }

    .summary-card p {
        margin: 5px 0 0 0;
        color: #666;
        font-size: 14px;
    }

    .feedback-charts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .chart-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .chart-header h3 {
        margin: 0;
        color: #333;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .recent-feedback-section {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .recent-feedback-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .feedback-item {
        border-bottom: 1px solid #eee;
        padding: 15px 0;
    }

    .feedback-item:last-child {
        border-bottom: none;
    }

    .feedback-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .feedback-rating {
        font-size: 16px;
    }

    .rating-number {
        color: #666;
        font-size: 14px;
        margin-left: 5px;
    }

    .feedback-meta {
        font-size: 12px;
        color: #666;
    }

    .feedback-meta span {
        margin-right: 10px;
    }

    .feedback-text {
        background: #f9f9f9;
        padding: 10px;
        border-radius: 4px;
        font-style: italic;
    }

    .analysis-filters {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .analysis-filters form {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .filter-group label {
        font-weight: 600;
        color: #333;
    }

    .filter-group select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 150px;
    }

    .detailed-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stat-card h4 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-value {
        font-size: 2em;
        font-weight: bold;
        color: #007cba;
        margin-bottom: 5px;
    }

    .stat-description {
        font-size: 12px;
        color: #666;
    }

    .rating-distribution-table {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .rating-bar {
        width: 100px;
        height: 20px;
        background: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }

    .rating-fill {
        height: 100%;
        background: linear-gradient(90deg, #007cba, #00a32a);
        transition: width 0.3s ease;
    }

    @media (max-width: 768px) {
        .feedback-summary-cards,
        .feedback-charts-grid,
        .detailed-stats-grid {
            grid-template-columns: 1fr;
        }

        .analysis-filters form {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-group select {
            min-width: auto;
        }
    }
    </style>
    <?php
}

/**
 * Render feedback admin scripts
 */
function chatgabi_render_feedback_admin_scripts() {
    ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    jQuery(document).ready(function($) {
        // Initialize charts if data is available
        if (typeof window.chatgabiFeedbackData !== 'undefined') {
            initializeFeedbackCharts();
        }

        // Filter handlers
        $('#trendPeriodFilter').on('change', function() {
            updateSatisfactionTrend($(this).val());
        });
    });

    function initializeFeedbackCharts() {
        // Rating Distribution Chart
        if (window.chatgabiFeedbackData.ratingDistribution) {
            createRatingDistributionChart();
        }

        // Country Comparison Chart
        if (window.chatgabiFeedbackData.countryStats) {
            createCountryComparisonChart();
        }

        // Category Ratings Chart
        if (window.chatgabiFeedbackData.categoryStats) {
            createCategoryRatingsChart();
        }
    }

    function createRatingDistributionChart() {
        const ctx = document.getElementById('ratingDistributionChart');
        if (!ctx) return;

        const data = window.chatgabiFeedbackData.ratingDistribution;
        const labels = data.map(item => item.rating_score + ' Stars');
        const counts = data.map(item => item.count);

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: counts,
                    backgroundColor: [
                        '#dc3545', '#fd7e14', '#ffc107', '#28a745', '#007cba'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    function createCountryComparisonChart() {
        const ctx = document.getElementById('countryComparisonChart');
        if (!ctx) return;

        const countryStats = window.chatgabiFeedbackData.countryStats;
        const countries = Object.keys(countryStats);
        const avgRatings = countries.map(country =>
            countryStats[country].overall ? countryStats[country].overall.avg_rating : 0
        );

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: countries,
                datasets: [{
                    label: 'Average Rating',
                    data: avgRatings,
                    backgroundColor: '#007cba',
                    borderColor: '#005a87',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5
                    }
                }
            }
        });
    }

    function createCategoryRatingsChart() {
        const ctx = document.getElementById('categoryRatingsChart');
        if (!ctx) return;

        const categories = window.chatgabiFeedbackData.categoryStats;
        if (!categories) return;

        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Helpfulness', 'Accuracy', 'Relevance', 'Clarity'],
                datasets: [{
                    label: 'Category Ratings',
                    data: [
                        categories.avg_helpfulness || 0,
                        categories.avg_accuracy || 0,
                        categories.avg_relevance || 0,
                        categories.avg_clarity || 0
                    ],
                    backgroundColor: 'rgba(0, 124, 186, 0.2)',
                    borderColor: '#007cba',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5
                    }
                }
            }
        });
    }
    </script>
    <?php
}

/**
 * Render text feedback tab
 */
function chatgabi_render_text_feedback() {
    global $wpdb;

    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';

    // Get filters
    $country_filter = isset($_GET['country']) ? sanitize_text_field($_GET['country']) : '';
    $sector_filter = isset($_GET['sector']) ? sanitize_text_field($_GET['sector']) : '';
    $rating_filter = isset($_GET['rating']) ? intval($_GET['rating']) : 0;

    // Build query
    $where_conditions = array("feedback_text IS NOT NULL", "feedback_text != ''");
    $where_values = array();

    if ($country_filter) {
        $where_conditions[] = "user_country = %s";
        $where_values[] = $country_filter;
    }

    if ($sector_filter) {
        $where_conditions[] = "user_sector = %s";
        $where_values[] = $sector_filter;
    }

    if ($rating_filter) {
        $where_conditions[] = "rating_score = %d";
        $where_values[] = $rating_filter;
    }

    $where_clause = implode(' AND ', $where_conditions);

    $query = "SELECT * FROM {$feedback_table} WHERE {$where_clause} ORDER BY created_at DESC LIMIT 50";

    if (!empty($where_values)) {
        $feedback_data = $wpdb->get_results($wpdb->prepare($query, $where_values));
    } else {
        $feedback_data = $wpdb->get_results($query);
    }

    // Get filter options
    $countries = $wpdb->get_col("SELECT DISTINCT user_country FROM {$feedback_table} WHERE user_country IS NOT NULL ORDER BY user_country");
    $sectors = $wpdb->get_col("SELECT DISTINCT user_sector FROM {$feedback_table} WHERE user_sector IS NOT NULL ORDER BY user_sector");

    ?>
    <div class="wrap">
        <h2>Text Feedback</h2>

        <!-- Filters -->
        <div class="feedback-filters">
            <form method="get" action="">
                <input type="hidden" name="page" value="chatgabi-feedback">
                <input type="hidden" name="tab" value="feedback">

                <select name="country">
                    <option value="">All Countries</option>
                    <?php foreach ($countries as $country): ?>
                        <option value="<?php echo esc_attr($country); ?>" <?php selected($country_filter, $country); ?>>
                            <?php echo esc_html($country); ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <select name="sector">
                    <option value="">All Sectors</option>
                    <?php foreach ($sectors as $sector): ?>
                        <option value="<?php echo esc_attr($sector); ?>" <?php selected($sector_filter, $sector); ?>>
                            <?php echo esc_html($sector); ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <select name="rating">
                    <option value="">All Ratings</option>
                    <option value="5" <?php selected($rating_filter, 5); ?>>5 Stars</option>
                    <option value="4" <?php selected($rating_filter, 4); ?>>4 Stars</option>
                    <option value="3" <?php selected($rating_filter, 3); ?>>3 Stars</option>
                    <option value="2" <?php selected($rating_filter, 2); ?>>2 Stars</option>
                    <option value="1" <?php selected($rating_filter, 1); ?>>1 Star</option>
                </select>

                <input type="submit" class="button" value="Apply Filters">
            </form>
        </div>

        <!-- Feedback List -->
        <div class="feedback-list">
            <?php if (empty($feedback_data)): ?>
                <p>No text feedback available.</p>
            <?php else: ?>
                <?php foreach ($feedback_data as $feedback): ?>
                    <div class="feedback-item">
                        <div class="feedback-header">
                            <span class="rating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <span class="star <?php echo $i <= $feedback->rating_score ? 'filled' : 'empty'; ?>">★</span>
                                <?php endfor; ?>
                            </span>
                            <span class="date"><?php echo esc_html(date('M j, Y g:i A', strtotime($feedback->created_at))); ?></span>
                            <?php if ($feedback->user_country): ?>
                                <span class="country"><?php echo esc_html($feedback->user_country ?: ''); ?></span>
                            <?php endif; ?>
                            <?php if ($feedback->user_sector): ?>
                                <span class="sector"><?php echo esc_html($feedback->user_sector ?: ''); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="feedback-content">
                            <p><?php echo esc_html($feedback->feedback_text ?: ''); ?></p>
                        </div>
                        <?php if ($feedback->is_training_data): ?>
                            <div class="training-consent">
                                <span class="training-badge">✓ Training Data Consent</span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <style>
    .feedback-filters {
        margin: 20px 0;
        padding: 15px;
        background: #f9f9f9;
        border: 1px solid #ddd;
    }

    .feedback-filters select {
        margin-right: 10px;
    }

    .feedback-item {
        margin: 15px 0;
        padding: 15px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .feedback-header {
        margin-bottom: 10px;
        font-size: 14px;
        color: #666;
    }

    .feedback-header span {
        margin-right: 15px;
    }

    .star.filled {
        color: #ffb900;
    }

    .star.empty {
        color: #ddd;
    }

    .training-badge {
        background: #46b450;
        color: white;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 12px;
    }
    </style>
    <?php
}

/**
 * Render training data tab
 */
function chatgabi_render_training_data() {
    global $wpdb;

    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';

    // Get training data statistics
    $total_feedback = $wpdb->get_var("SELECT COUNT(*) FROM {$feedback_table}");
    $training_consent = $wpdb->get_var("SELECT COUNT(*) FROM {$feedback_table} WHERE is_training_data = 1");
    $with_text = $wpdb->get_var("SELECT COUNT(*) FROM {$feedback_table} WHERE feedback_text IS NOT NULL AND feedback_text != '' AND is_training_data = 1");

    // Get recent training data
    $training_data = $wpdb->get_results("
        SELECT * FROM {$feedback_table}
        WHERE is_training_data = 1
        ORDER BY created_at DESC
        LIMIT 20
    ");

    ?>
    <div class="wrap">
        <h2>Training Data</h2>

        <div class="training-stats">
            <div class="stat-box">
                <h3><?php echo number_format($total_feedback); ?></h3>
                <p>Total Feedback</p>
            </div>
            <div class="stat-box">
                <h3><?php echo number_format($training_consent); ?></h3>
                <p>Training Consent</p>
            </div>
            <div class="stat-box">
                <h3><?php echo number_format($with_text); ?></h3>
                <p>With Text Feedback</p>
            </div>
            <div class="stat-box">
                <h3><?php echo $total_feedback > 0 ? round(($training_consent / $total_feedback) * 100, 1) : 0; ?>%</h3>
                <p>Consent Rate</p>
            </div>
        </div>

        <h3>Recent Training Data</h3>
        <div class="training-data-list">
            <?php if (empty($training_data)): ?>
                <p>No training data available.</p>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Rating</th>
                            <th>Type</th>
                            <th>Country</th>
                            <th>Sector</th>
                            <th>Feedback</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($training_data as $data): ?>
                            <tr>
                                <td><?php echo esc_html(date('M j, Y', strtotime($data->created_at))); ?></td>
                                <td>
                                    <?php if ($data->rating_score): ?>
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <span class="star <?php echo $i <= $data->rating_score ? 'filled' : 'empty'; ?>">★</span>
                                        <?php endfor; ?>
                                    <?php else: ?>
                                        <?php echo esc_html(ucfirst($data->rating_type)); ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html(($data->feedback_category ?? '') ?: 'General'); ?></td>
                                <td><?php echo esc_html(($data->user_country ?? '') ?: '-'); ?></td>
                                <td><?php echo esc_html(($data->user_sector ?? '') ?: '-'); ?></td>
                                <td><?php echo esc_html(substr(($data->feedback_text ?? '') ?: 'No text feedback', 0, 100) . (strlen(($data->feedback_text ?? '') ?: '') > 100 ? '...' : '')); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <div class="training-info">
            <h3>Training Data Usage</h3>
            <p>This data can be used to improve AI responses by:</p>
            <ul>
                <li>Training models on user preferences by country and sector</li>
                <li>Improving response quality based on feedback patterns</li>
                <li>Customizing responses for different African markets</li>
                <li>Identifying common issues and improvement areas</li>
            </ul>

            <p><strong>Privacy:</strong> All training data is anonymized and used only with explicit user consent.</p>
        </div>
    </div>

    <style>
    .training-stats {
        display: flex;
        gap: 20px;
        margin: 20px 0;
    }

    .stat-box {
        background: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        text-align: center;
        flex: 1;
    }

    .stat-box h3 {
        font-size: 2em;
        margin: 0;
        color: #0073aa;
    }

    .stat-box p {
        margin: 5px 0 0 0;
        color: #666;
    }

    .training-info {
        margin-top: 30px;
        padding: 20px;
        background: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    </style>
    <?php
}

/**
 * Render feedback export tab
 */
function chatgabi_render_feedback_export() {
    global $wpdb;

    $feedback_table = $wpdb->prefix . 'chatgabi_feedback';

    // Handle export request
    if (isset($_POST['export_feedback']) && wp_verify_nonce($_POST['feedback_export_nonce'], 'export_feedback')) {
        $format = sanitize_text_field($_POST['export_format']);
        $date_from = sanitize_text_field($_POST['date_from']);
        $date_to = sanitize_text_field($_POST['date_to']);

        chatgabi_export_feedback_data($format, $date_from, $date_to);
        return;
    }

    ?>
    <div class="wrap">
        <h2>Export Feedback Data</h2>

        <div class="export-options">
            <form method="post" action="">
                <?php wp_nonce_field('export_feedback', 'feedback_export_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">Export Format</th>
                        <td>
                            <select name="export_format" required>
                                <option value="csv">CSV</option>
                                <option value="json">JSON</option>
                                <option value="xml">XML</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Date Range</th>
                        <td>
                            <input type="date" name="date_from" placeholder="From Date">
                            <input type="date" name="date_to" placeholder="To Date">
                            <p class="description">Leave empty to export all data</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="export_feedback" class="button-primary" value="Export Data">
                </p>
            </form>
        </div>

        <div class="export-info">
            <h3>Export Information</h3>
            <p>The export will include:</p>
            <ul>
                <li>User feedback ratings and comments</li>
                <li>Timestamp and user information</li>
                <li>Country and sector data</li>
                <li>Training data consent status</li>
            </ul>

            <p><strong>Privacy Note:</strong> Exported data should be handled according to your privacy policy and applicable data protection regulations.</p>
        </div>
    </div>
    <?php
}

/**
 * NOTE: Export functions are now handled by inc/feedback-system.php
 * This prevents duplicate function declarations and maintains clean separation of concerns.
 *
 * The following functions are available from feedback-system.php:
 * - chatgabi_export_feedback_data()
 * - chatgabi_export_csv()
 * - chatgabi_export_json()
 * - chatgabi_export_xml()
 */

@import '~codemirror-colorpicker/dist/codemirror-colorpicker.css';

.CodeMirror {
	border: 1px solid #dfdfdf;
	border-radius: 3px;
	height: auto !important;
	background-color: #fff;
}

.CodeMirror-code {
	outline: none;
}

.CodeMirror-focused .cm-matchhighlight {
	color: white !important;
	outline: 1px solid green;
}

/* Add a bit of extra space above the editor */
.CodeMirror-sizer {

	&::before, &::after {
		display: block;
		color: #bbb;
	}

	&::before {
		content: '';
		padding-bottom: 5px;
	}
}

/* Fix cursor color with rubyblue theme (see https://goo.gl/3HDgRm */
.cm-s-rubyblue .CodeMirror-cursor {
	border-left: 1px solid white !important;
}

[class*="CodeMirror-lint-marker"], [class*="CodeMirror-lint-message"], .CodeMirror-lint-marker-multiple {
	background-image: none;
}

.CodeMirror-lint-marker-error, .CodeMirror-lint-marker-warning {
	cursor: help;
}

.CodeMirror-lint-marker-multiple {
	position: absolute;
	top: 0;
}

[class*="CodeMirror-lint-marker"]:before {
	font: normal 18px/1 dashicons;
	position: relative;
	top: -2px;
}

[class*="CodeMirror-lint-message"]:before {
	font: normal 16px/1 dashicons;
	left: 16px;
	position: absolute;
}

.CodeMirror-lint-message-error,
.CodeMirror-lint-message-warning {
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
	margin: 5px 0 2px;
	padding: 3px 12px 3px 28px;
}

.CodeMirror-lint-message-warning {
	background-color: #fff8e5;
	border-left: 4px solid #ffb900;
}

.CodeMirror-lint-marker-warning::before, .CodeMirror-lint-message-warning::before {
	content: "\f534";
	color: #f6a306;
}

.CodeMirror-lint-message-error {
	background-color: #fbeaea;
	border-left: 4px solid #dc3232;
}

.CodeMirror-lint-marker-error::before, .CodeMirror-lint-message-error::before {
	content: "\f153";
	color: #dc3232;
}

.CodeMirror-lint-tooltip {
	background: none;
	border: none;
	border-radius: 0;
	direction: ltr;
}

.CodeMirror .CodeMirror-matchingbracket {
	background: rgba(255, 150, 0, .3);
	color: inherit;
}

.CodeMirror .CodeMirror-linenumber {
	color: #666;
}

.CodeMirror-foldmarker {
	color: inherit;
	margin-left: 0.25em;
	margin-right: 0.25em;
	font-weight: bold;
}

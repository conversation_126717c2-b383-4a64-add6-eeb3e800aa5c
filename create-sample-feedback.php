<?php
/**
 * Create Sample Feedback Data for Testing
 * Run this file to populate the feedback system with test data
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

echo '<h1>Creating Sample Feedback Data</h1>';

global $wpdb;

// Check if feedback table exists
$feedback_table = $wpdb->prefix . 'chatgabi_feedback';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$feedback_table'");

if (!$table_exists) {
    echo '<p>❌ Feedback table does not exist. Creating it...</p>';
    
    // Load database functions
    require_once get_template_directory() . '/inc/database.php';
    
    // Create feedback table
    $result = chatgabi_create_feedback_tables();
    
    if ($result) {
        echo '<p>✅ Feedback table created successfully!</p>';
    } else {
        echo '<p>❌ Failed to create feedback table</p>';
        exit;
    }
} else {
    echo '<p>✅ Feedback table exists</p>';
}

// Check if sample data already exists
$existing_count = $wpdb->get_var("SELECT COUNT(*) FROM $feedback_table");
echo "<p>Current feedback count: $existing_count</p>";

if ($existing_count > 0) {
    echo '<p>Sample data already exists. <a href="?clear=1">Click here to clear and recreate</a></p>';
    
    if (isset($_GET['clear'])) {
        $wpdb->query("DELETE FROM $feedback_table");
        echo '<p>✅ Existing data cleared</p>';
    } else {
        echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback">Go to Feedback Dashboard</a></p>';
        exit;
    }
}

// Sample feedback data
$sample_feedback = array(
    array(
        'user_id' => 1,
        'conversation_id' => 'conv_001',
        'message_id' => 'msg_001',
        'rating_type' => 'star',
        'rating_score' => 5,
        'feedback_category' => 'helpfulness',
        'feedback_text' => 'This AI response was extremely helpful for my business planning in Ghana. The market insights were spot-on!',
        'user_country' => 'Ghana',
        'user_sector' => 'Agriculture',
        'is_training_data' => 1,
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
    ),
    array(
        'user_id' => 2,
        'conversation_id' => 'conv_002',
        'message_id' => 'msg_002',
        'rating_type' => 'star',
        'rating_score' => 4,
        'feedback_category' => 'accuracy',
        'feedback_text' => 'Good information about Nigerian fintech regulations, but could use more recent updates.',
        'user_country' => 'Nigeria',
        'user_sector' => 'Financial Services',
        'is_training_data' => 1,
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
    ),
    array(
        'user_id' => 3,
        'conversation_id' => 'conv_003',
        'message_id' => 'msg_003',
        'rating_type' => 'thumbs',
        'thumbs_rating' => 'up',
        'feedback_category' => 'relevance',
        'feedback_text' => 'Perfect advice for starting a tech company in Kenya. The M-Pesa integration tips were valuable.',
        'user_country' => 'Kenya',
        'user_sector' => 'Technology',
        'is_training_data' => 1,
        'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours'))
    ),
    array(
        'user_id' => 4,
        'conversation_id' => 'conv_004',
        'message_id' => 'msg_004',
        'rating_type' => 'star',
        'rating_score' => 3,
        'feedback_category' => 'clarity',
        'feedback_text' => 'The response was okay but could be clearer about South African mining regulations.',
        'user_country' => 'South Africa',
        'user_sector' => 'Mining',
        'is_training_data' => 0,
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
    ),
    array(
        'user_id' => 5,
        'conversation_id' => 'conv_005',
        'message_id' => 'msg_005',
        'rating_type' => 'star',
        'rating_score' => 5,
        'feedback_category' => 'helpfulness',
        'feedback_text' => 'Excellent guidance on export procedures from Ghana to EU markets!',
        'user_country' => 'Ghana',
        'user_sector' => 'Manufacturing',
        'is_training_data' => 1,
        'created_at' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
    ),
    array(
        'user_id' => 6,
        'conversation_id' => 'conv_006',
        'message_id' => 'msg_006',
        'rating_type' => 'thumbs',
        'thumbs_rating' => 'down',
        'feedback_category' => 'accuracy',
        'feedback_text' => 'The information about Nigerian tax rates seems outdated.',
        'user_country' => 'Nigeria',
        'user_sector' => 'Retail',
        'is_training_data' => 0,
        'created_at' => date('Y-m-d H:i:s', strtotime('-15 minutes'))
    ),
    array(
        'user_id' => 7,
        'conversation_id' => 'conv_007',
        'message_id' => 'msg_007',
        'rating_type' => 'star',
        'rating_score' => 4,
        'feedback_category' => 'relevance',
        'feedback_text' => 'Good insights on Kenyan tourism business opportunities.',
        'user_country' => 'Kenya',
        'user_sector' => 'Tourism',
        'is_training_data' => 1,
        'created_at' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
    ),
    array(
        'user_id' => 8,
        'conversation_id' => 'conv_008',
        'message_id' => 'msg_008',
        'rating_type' => 'star',
        'rating_score' => 5,
        'feedback_category' => 'clarity',
        'feedback_text' => 'Crystal clear explanation of South African BEE requirements for my construction business.',
        'user_country' => 'South Africa',
        'user_sector' => 'Construction',
        'is_training_data' => 1,
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 minutes'))
    )
);

// Insert sample data
$inserted = 0;
foreach ($sample_feedback as $feedback) {
    $result = $wpdb->insert(
        $feedback_table,
        $feedback,
        array(
            '%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%s'
        )
    );
    
    if ($result) {
        $inserted++;
    }
}

echo "<p>✅ Inserted $inserted sample feedback records</p>";

// Show summary
$total_count = $wpdb->get_var("SELECT COUNT(*) FROM $feedback_table");
$avg_rating = $wpdb->get_var("SELECT AVG(rating_score) FROM $feedback_table WHERE rating_score IS NOT NULL");
$training_consent = $wpdb->get_var("SELECT COUNT(*) FROM $feedback_table WHERE is_training_data = 1");

echo '<h2>Summary</h2>';
echo "<p>Total feedback: $total_count</p>";
echo "<p>Average rating: " . number_format($avg_rating, 2) . "/5</p>";
echo "<p>Training consent: $training_consent</p>";

echo '<h2>Next Steps</h2>';
echo '<p>✅ Sample feedback data created successfully!</p>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback" class="button button-primary">View Feedback Dashboard</a></p>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=feedback" class="button">View Text Feedback</a></p>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=training" class="button">View Training Data</a></p>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback&tab=export" class="button">Test Export</a></p>';

?>
<style>
.button {
    display: inline-block;
    padding: 8px 12px;
    margin: 5px;
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 3px;
}
.button-primary {
    background: #007cba;
}
.button:hover {
    background: #005a87;
}
</style>

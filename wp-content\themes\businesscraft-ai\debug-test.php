<?php
/**
 * Debug test for data loader
 */

// Force output buffering off
if (ob_get_level()) {
    ob_end_clean();
}

// Define WordPress constants for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/../../../');
}
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Define a simple error_log function if it doesn't exist or redirect it
if (!function_exists('error_log')) {
    function error_log($message) {
        echo "LOG: " . $message . "\n";
    }
}

echo "Starting debug test...\n";
echo "WP_CONTENT_DIR: " . WP_CONTENT_DIR . "\n";
echo "Current directory: " . __DIR__ . "\n";

// Check if data loader file exists
$data_loader_file = __DIR__ . '/inc/data-loader.php';
echo "Data loader file: " . $data_loader_file . "\n";
echo "File exists: " . (file_exists($data_loader_file) ? 'YES' : 'NO') . "\n";

if (!file_exists($data_loader_file)) {
    echo "ERROR: Data loader file not found!\n";
    exit(1);
}

// Include the data loader functions
echo "About to include data loader...\n";
try {
    require_once($data_loader_file);
    echo "Data loader included successfully\n";
} catch (Exception $e) {
    echo "ERROR including data loader: " . $e->getMessage() . "\n";
    exit(1);
} catch (Error $e) {
    echo "FATAL ERROR including data loader: " . $e->getMessage() . "\n";
    exit(1);
}

// Check if functions exist
$functions = ['load_business_dataset_by_country', 'get_latest_versioned_file', 'get_available_dataset_countries'];
foreach ($functions as $func) {
    echo "Function {$func}: " . (function_exists($func) ? 'EXISTS' : 'MISSING') . "\n";
}

// Test dataset directory
$dataset_dir = WP_CONTENT_DIR . '/datasets/';
echo "Dataset directory: " . $dataset_dir . "\n";
echo "Directory exists: " . (is_dir($dataset_dir) ? 'YES' : 'NO') . "\n";

if (is_dir($dataset_dir)) {
    $subdirs = glob($dataset_dir . '*', GLOB_ONLYDIR);
    echo "Subdirectories found: " . count($subdirs) . "\n";
    foreach ($subdirs as $dir) {
        echo "  - " . basename($dir) . "\n";
    }
}

// Test Ghana specifically
echo "\n--- Testing Ghana ---\n";
$ghana_dir = WP_CONTENT_DIR . '/datasets/ghana-business-data/';
echo "Ghana directory: " . $ghana_dir . "\n";
echo "Ghana dir exists: " . (is_dir($ghana_dir) ? 'YES' : 'NO') . "\n";

if (is_dir($ghana_dir)) {
    $json_files = glob($ghana_dir . '*.json');
    echo "JSON files found: " . count($json_files) . "\n";
    foreach ($json_files as $file) {
        echo "  - " . basename($file) . "\n";
    }

    if (!empty($json_files)) {
        echo "\n--- Testing load_business_dataset_by_country('Ghana') ---\n";
        $result = load_business_dataset_by_country('Ghana');

        if ($result === false) {
            echo "RESULT: FALSE (failed)\n";
        } else {
            echo "RESULT: SUCCESS\n";
            echo "Data type: " . gettype($result) . "\n";
            if (is_array($result)) {
                echo "Array keys: " . implode(', ', array_keys($result)) . "\n";
                if (isset($result['country'])) {
                    echo "Country: " . $result['country'] . "\n";
                }
                if (isset($result['sectors'])) {
                    echo "Sectors count: " . count($result['sectors']) . "\n";
                }
            }
        }
    }
}

echo "\nDebug test complete.\n";

<?php
/**
 * ChatGABI Background Processing
 * 
 * Handles heavy operations in the background to prevent timeouts
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Schedule template generation in background
 */
function chatgabi_schedule_template_generation($template_id, $template_data, $form_data) {
    // Use WordPress cron to schedule background processing
    wp_schedule_single_event(time() + 10, 'chatgabi_process_template_generation', array(
        'template_id' => $template_id,
        'template_data' => $template_data,
        'form_data' => $form_data
    ));
    
    return true;
}

/**
 * Process template generation in background
 */
function chatgabi_process_template_generation_background($template_id, $template_data, $form_data) {
    // Increase execution time for background processing
    ini_set('max_execution_time', 600);
    set_time_limit(600);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';
    
    try {
        // Update status to processing
        $wpdb->update(
            $table_name,
            array('status' => 'processing'),
            array('id' => $template_id),
            array('%s'),
            array('%d')
        );
        
        // Build the prompt
        $prompt = chatgabi_build_template_generation_prompt($template_data, $form_data);
        
        if (empty($prompt)) {
            throw new Exception('Failed to build template generation prompt');
        }
        
        // Generate content using AI
        if (function_exists('businesscraft_ai_process_openai_request')) {
            $language_code = !empty($template_data->document_language) ? $template_data->document_language : 'en';
            
            $ai_response = businesscraft_ai_process_openai_request(
                $prompt, 
                $language_code, 
                'template_generation', 
                $template_data->user_id ?? get_current_user_id()
            );

            if (is_wp_error($ai_response)) {
                throw new Exception('AI generation failed: ' . $ai_response->get_error_message());
            }

            $generated_content = isset($ai_response['response']) ? $ai_response['response'] : '';
            
            if (empty($generated_content)) {
                throw new Exception('AI generation returned empty content');
            }

            // Update template with generated content
            $update_result = $wpdb->update(
                $table_name,
                array(
                    'generated_content' => $generated_content,
                    'status' => 'completed',
                    'tokens_used' => isset($ai_response['tokens_used']) ? $ai_response['tokens_used'] : 0,
                    'credits_used' => isset($ai_response['credits_used']) ? $ai_response['credits_used'] : 0
                ),
                array('id' => $template_id),
                array('%s', '%s', '%d', '%d'),
                array('%d')
            );

            if ($update_result === false) {
                throw new Exception('Failed to save generated template');
            }
            
            // Log successful generation
            error_log("ChatGABI: Template {$template_id} generated successfully in background");
            
        } else {
            throw new Exception('AI generation service not available');
        }
        
    } catch (Exception $e) {
        // Update status to failed
        $wpdb->update(
            $table_name,
            array(
                'status' => 'failed',
                'generated_content' => 'Generation failed: ' . $e->getMessage()
            ),
            array('id' => $template_id),
            array('%s', '%s'),
            array('%d')
        );
        
        error_log("ChatGABI: Template {$template_id} generation failed: " . $e->getMessage());
    }
}

/**
 * Check template generation status
 */
function chatgabi_get_template_generation_status($template_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';
    
    $template = $wpdb->get_row(
        $wpdb->prepare("SELECT status, generated_content FROM {$table_name} WHERE id = %d", $template_id)
    );
    
    if (!$template) {
        return array('status' => 'not_found', 'content' => '');
    }
    
    return array(
        'status' => $template->status,
        'content' => $template->generated_content
    );
}

/**
 * AJAX handler for checking template status
 */
function chatgabi_ajax_check_template_status() {
    // Start output buffering to prevent headers already sent errors
    if (!ob_get_level()) {
        ob_start();
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_template_nonce')) {
        wp_die('Security check failed');
    }
    
    $template_id = intval($_POST['template_id']);
    
    if (!$template_id) {
        wp_send_json_error('Invalid template ID');
    }
    
    $status = chatgabi_get_template_generation_status($template_id);
    
    wp_send_json_success($status);
}

/**
 * Background cache warming
 */
function chatgabi_warm_caches_background() {
    // Increase execution time for cache warming
    ini_set('max_execution_time', 300);
    set_time_limit(300);
    
    $warmed = 0;
    
    try {
        // Warm language templates
        $languages = ['en', 'tw', 'sw', 'yo', 'zu'];
        foreach ($languages as $lang) {
            if (function_exists('chatgabi_load_language_template')) {
                $template = chatgabi_load_language_template('business-plan', $lang);
                if ($template) {
                    $warmed++;
                }
            }
        }
        
        // Warm country datasets
        $countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
        foreach ($countries as $country) {
            if (function_exists('load_business_dataset_by_country')) {
                $dataset = load_business_dataset_by_country($country);
                if ($dataset) {
                    $warmed++;
                }
            }
        }
        
        error_log("ChatGABI: Cache warming completed - {$warmed} caches warmed");
        
    } catch (Exception $e) {
        error_log("ChatGABI: Cache warming failed: " . $e->getMessage());
    }
}

/**
 * Schedule daily cache warming
 */
function chatgabi_schedule_cache_warming() {
    if (!wp_next_scheduled('chatgabi_daily_cache_warm')) {
        wp_schedule_event(time(), 'daily', 'chatgabi_daily_cache_warm');
    }
}

/**
 * Performance monitoring function
 */
function chatgabi_log_performance($operation, $start_time) {
    $elapsed = microtime(true) - $start_time;
    
    if ($elapsed > 5) { // Log operations taking more than 5 seconds
        $log_entry = date("Y-m-d H:i:s") . " - SLOW: {$operation} took " . number_format($elapsed, 3) . " seconds\n";
        $log_file = WP_CONTENT_DIR . '/chatgabi-performance.log';
        
        // Prevent log file from growing too large
        if (file_exists($log_file) && filesize($log_file) > 1024 * 1024) { // 1MB limit
            // Keep only last 100 lines
            $lines = file($log_file);
            $lines = array_slice($lines, -100);
            file_put_contents($log_file, implode('', $lines));
        }
        
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}

/**
 * Cleanup old performance logs
 */
function chatgabi_cleanup_performance_logs() {
    $log_file = WP_CONTENT_DIR . '/chatgabi-performance.log';
    
    if (file_exists($log_file)) {
        $file_age = time() - filemtime($log_file);
        
        // Clean up logs older than 7 days
        if ($file_age > 7 * 24 * 60 * 60) {
            unlink($log_file);
            error_log("ChatGABI: Cleaned up old performance log");
        }
    }
}

// Hook background processing functions
add_action('chatgabi_process_template_generation', 'chatgabi_process_template_generation_background', 10, 3);
add_action('chatgabi_daily_cache_warm', 'chatgabi_warm_caches_background');
add_action('wp_ajax_chatgabi_check_template_status', 'chatgabi_ajax_check_template_status');
add_action('wp_ajax_nopriv_chatgabi_check_template_status', 'chatgabi_ajax_check_template_status');

// Schedule cache warming on theme activation
add_action('after_switch_theme', 'chatgabi_schedule_cache_warming');

// Daily cleanup
add_action('wp_scheduled_delete', 'chatgabi_cleanup_performance_logs');

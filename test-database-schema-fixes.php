<?php
/**
 * Test Database Schema Fixes
 * 
 * Comprehensive test of all database schema fixes applied
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Database Schema Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        .schema-info { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

<h1>🧪 Test Database Schema Fixes</h1>

<?php
echo '<div class="info">Database schema test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$all_tests_passed = true;

// Test 1: Check Database Table Structure
echo '<h2>🔍 Test 1: Database Table Structure</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

try {
    // Check templates table
    $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") === $templates_table;
    echo '<div class="schema-info">';
    echo '<strong>Templates Table (' . $templates_table . '):</strong> ' . ($templates_exists ? '✅ EXISTS' : '❌ MISSING') . '<br>';
    
    if ($templates_exists) {
        $templates_columns = $wpdb->get_results("DESCRIBE {$templates_table}");
        $has_prompt_text = false;
        $has_prompt_content = false;
        $has_status = false;
        
        foreach ($templates_columns as $column) {
            if ($column->Field === 'prompt_text') $has_prompt_text = true;
            if ($column->Field === 'prompt_content') $has_prompt_content = true;
            if ($column->Field === 'status') $has_status = true;
        }
        
        echo 'Has prompt_text column: ' . ($has_prompt_text ? '✅ YES' : '❌ NO') . '<br>';
        echo 'Has prompt_content column: ' . ($has_prompt_content ? '✅ YES' : '❌ NO') . '<br>';
        echo 'Has status column: ' . ($has_status ? '✅ YES' : '❌ NO') . '<br>';
        
        if (!$has_prompt_text && !$has_prompt_content) {
            echo '<div class="error">❌ Missing prompt content column!</div>';
            $all_tests_passed = false;
        }
    }
    echo '</div>';
    
    // Check categories table
    $categories_exists = $wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table;
    echo '<div class="schema-info">';
    echo '<strong>Categories Table (' . $categories_table . '):</strong> ' . ($categories_exists ? '✅ EXISTS' : '❌ MISSING') . '<br>';
    
    if ($categories_exists) {
        $categories_columns = $wpdb->get_results("DESCRIBE {$categories_table}");
        $categories_has_status = false;
        
        foreach ($categories_columns as $column) {
            if ($column->Field === 'status') $categories_has_status = true;
        }
        
        echo 'Has status column: ' . ($categories_has_status ? '✅ YES' : '❌ NO') . '<br>';
    }
    echo '</div>';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Table structure check error: ' . $e->getMessage() . '</div>';
    $all_tests_passed = false;
}

// Test 2: Test Fixed Database Queries
echo '<h2>🧪 Test 2: Fixed Database Queries</h2>';

try {
    echo '<div class="info">🔧 Testing templates query with correct column names...</div>';
    
    // Test templates query (should work with either prompt_text or prompt_content)
    $columns = $wpdb->get_col("DESCRIBE {$templates_table}");
    $has_status = in_array('status', $columns);
    $has_prompt_text = in_array('prompt_text', $columns);
    $has_prompt_content = in_array('prompt_content', $columns);
    
    $prompt_column = $has_prompt_text ? 'prompt_text' : ($has_prompt_content ? 'prompt_content' : 'description');
    $status_condition = $has_status ? " AND t.status = 'active'" : "";
    
    $test_query = "SELECT t.id, t.title, t.description, t.{$prompt_column} as prompt_content, t.language_code,
                          t.tags, t.sector, t.usage_count, t.rating_average, t.rating_count,
                          t.is_public, t.user_id, t.created_at,
                          c.name as category_name, c.icon as category_icon, c.color as category_color
                   FROM {$templates_table} t
                   LEFT JOIN {$categories_table} c ON t.category_id = c.id
                   WHERE t.is_public = 1{$status_condition}
                   ORDER BY t.usage_count DESC
                   LIMIT 5";
    
    $test_templates = $wpdb->get_results($test_query);
    
    if ($test_templates !== false) {
        echo '<div class="success">✅ Templates query working: ' . count($test_templates) . ' templates found</div>';
        
        if (count($test_templates) > 0) {
            echo '<div class="schema-info">';
            echo '<strong>Sample Template:</strong><br>';
            $sample = $test_templates[0];
            echo 'ID: ' . $sample->id . '<br>';
            echo 'Title: ' . $sample->title . '<br>';
            echo 'Category: ' . ($sample->category_name ?: 'None') . '<br>';
            echo 'Prompt Content Length: ' . strlen($sample->prompt_content) . ' characters<br>';
            echo '</div>';
        }
    } else {
        echo '<div class="error">❌ Templates query failed: ' . $wpdb->last_error . '</div>';
        $all_tests_passed = false;
    }
    
    echo '<div class="info">🔧 Testing categories query...</div>';
    
    // Test categories query
    $categories_columns = $wpdb->get_col("DESCRIBE {$categories_table}");
    $categories_has_status = in_array('status', $categories_columns);
    
    if ($categories_has_status) {
        $categories_query = "SELECT id, name, slug, description, icon, color, sort_order
                            FROM {$categories_table}
                            WHERE status = 'active'
                            ORDER BY sort_order ASC, name ASC
                            LIMIT 10";
    } else {
        $categories_query = "SELECT id, name, slug, description, icon, color, sort_order
                            FROM {$categories_table}
                            ORDER BY sort_order ASC, name ASC
                            LIMIT 10";
    }
    
    $test_categories = $wpdb->get_results($categories_query);
    
    if ($test_categories !== false) {
        echo '<div class="success">✅ Categories query working: ' . count($test_categories) . ' categories found</div>';
        
        if (count($test_categories) > 0) {
            echo '<div class="schema-info">';
            echo '<strong>Sample Categories:</strong><br>';
            foreach (array_slice($test_categories, 0, 3) as $cat) {
                echo '- ' . $cat->name . ' (' . $cat->slug . ')<br>';
            }
            echo '</div>';
        }
    } else {
        echo '<div class="error">❌ Categories query failed: ' . $wpdb->last_error . '</div>';
        $all_tests_passed = false;
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Database query testing error: ' . $e->getMessage() . '</div>';
    $all_tests_passed = false;
}

// Test 3: Test REST API Endpoints with Fixed Queries
echo '<h2>🌐 Test 3: REST API Endpoints with Fixed Queries</h2>';

try {
    echo '<div class="info">🔧 Testing REST API endpoints...</div>';
    
    // Test templates endpoint
    $templates_url = rest_url('chatgabi/v1/templates');
    $response = wp_remote_get($templates_url, array(
        'timeout' => 15,
        'headers' => array('User-Agent' => 'ChatGABI-Schema-Test/1.0')
    ));
    
    if (!is_wp_error($response)) {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            $data = json_decode($body, true);
            if (isset($data['success']) && $data['success']) {
                echo '<div class="success">✅ Templates API working: ' . count($data['templates']) . ' templates returned</div>';
                
                if (!empty($data['templates'])) {
                    $sample_template = $data['templates'][0];
                    echo '<div class="schema-info">';
                    echo '<strong>Sample API Template:</strong><br>';
                    echo 'ID: ' . $sample_template['id'] . '<br>';
                    echo 'Title: ' . $sample_template['title'] . '<br>';
                    echo 'Has prompt_content: ' . (isset($sample_template['prompt_content']) ? '✅ YES' : '❌ NO') . '<br>';
                    echo 'Category: ' . ($sample_template['category']['name'] ?? 'None') . '<br>';
                    echo '</div>';
                }
            } else {
                echo '<div class="warning">⚠️ Templates API returned success=false</div>';
                echo '<div class="schema-info">Response: <pre>' . esc_html(json_encode($data, JSON_PRETTY_PRINT)) . '</pre></div>';
            }
        } else {
            echo '<div class="error">❌ Templates API returned status: ' . $status_code . '</div>';
            echo '<div class="schema-info">Response: <pre>' . esc_html($body) . '</pre></div>';
            $all_tests_passed = false;
        }
    } else {
        echo '<div class="error">❌ Templates API error: ' . $response->get_error_message() . '</div>';
        $all_tests_passed = false;
    }
    
    // Test categories endpoint
    $categories_url = rest_url('chatgabi/v1/template-categories');
    $response = wp_remote_get($categories_url, array(
        'timeout' => 10,
        'headers' => array('User-Agent' => 'ChatGABI-Schema-Test/1.0')
    ));
    
    if (!is_wp_error($response)) {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            $data = json_decode($body, true);
            if (isset($data['success']) && $data['success']) {
                echo '<div class="success">✅ Categories API working: ' . count($data['categories']) . ' categories returned</div>';
            } else {
                echo '<div class="warning">⚠️ Categories API returned success=false</div>';
                echo '<div class="schema-info">Response: <pre>' . esc_html(json_encode($data, JSON_PRETTY_PRINT)) . '</pre></div>';
            }
        } else {
            echo '<div class="error">❌ Categories API returned status: ' . $status_code . '</div>';
            echo '<div class="schema-info">Response: <pre>' . esc_html($body) . '</pre></div>';
            $all_tests_passed = false;
        }
    } else {
        echo '<div class="error">❌ Categories API error: ' . $response->get_error_message() . '</div>';
        $all_tests_passed = false;
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ REST API testing error: ' . $e->getMessage() . '</div>';
    $all_tests_passed = false;
}

// Test 4: Live Frontend Test
echo '<h2>🌐 Test 4: Live Frontend API Test</h2>';
?>

<div class="schema-info">
    <strong>Live API Test Results:</strong>
    <div id="live-api-results">
        <p>Testing frontend API connectivity with fixed schema...</p>
    </div>
</div>

<script>
// Live frontend test
$(document).ready(function() {
    const restUrl = '<?php echo rest_url('chatgabi/v1/'); ?>';
    let testResults = '';
    
    // Test templates endpoint from frontend
    $.ajax({
        url: restUrl + 'templates',
        method: 'GET',
        timeout: 15000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Templates API: Success</div>';
            testResults += '<div class="info">Templates loaded: ' + (data.templates ? data.templates.length : 0) + '</div>';
            
            if (data.templates && data.templates.length > 0) {
                const sample = data.templates[0];
                testResults += '<div class="info">Sample template: ' + sample.title + '</div>';
                testResults += '<div class="info">Has prompt_content: ' + (sample.prompt_content ? 'YES' : 'NO') + '</div>';
                testResults += '<div class="info">Category: ' + (sample.category ? sample.category.name : 'None') + '</div>';
            }
            
            updateResults();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Templates API Error: ' + error + '</div>';
            testResults += '<div class="warning">Status: ' + xhr.status + ' - ' + xhr.statusText + '</div>';
            if (xhr.responseText) {
                try {
                    const errorData = JSON.parse(xhr.responseText);
                    testResults += '<div class="schema-info">Error Details: <pre>' + JSON.stringify(errorData, null, 2) + '</pre></div>';
                } catch (e) {
                    testResults += '<div class="schema-info">Response: <pre>' + xhr.responseText.substring(0, 500) + '</pre></div>';
                }
            }
            updateResults();
        }
    });
    
    // Test categories endpoint from frontend
    $.ajax({
        url: restUrl + 'template-categories',
        method: 'GET',
        timeout: 10000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Categories API: Success</div>';
            testResults += '<div class="info">Categories loaded: ' + (data.categories ? data.categories.length : 0) + '</div>';
            updateResults();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Categories API Error: ' + error + '</div>';
            testResults += '<div class="warning">Status: ' + xhr.status + ' - ' + xhr.statusText + '</div>';
            updateResults();
        }
    });
    
    function updateResults() {
        $('#live-api-results').html(testResults);
    }
});
</script>

<?php
// Final Summary
echo '<h2>🏆 Database Schema Fix Summary</h2>';

if ($all_tests_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL DATABASE SCHEMA ISSUES RESOLVED!</h3>';
    echo '<p><strong>✅ The database schema fixes are working correctly!</strong></p>';
    echo '<ul>';
    echo '<li>✅ Database tables exist and have correct structure</li>';
    echo '<li>✅ Column name mismatches resolved (prompt_text vs prompt_content)</li>';
    echo '<li>✅ Missing status column handling implemented</li>';
    echo '<li>✅ Database queries working without errors</li>';
    echo '<li>✅ REST API endpoints returning data correctly</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Database Issues May Remain</h3>';
    echo '<p>Most schema fixes have been applied, but some issues may need additional attention.</p>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Test Actions</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 Test Templates Page</a>';
}

echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="test-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/template-categories') . '" target="_blank" class="test-button">📂 Test Categories API</a>';
echo '<a href="fix-database-schema-issues.php" class="test-button">🔧 Run Schema Fix</a>';
echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Re-run Test</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Database schema test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

<?php
/**
 * ChatGABI Database Fix Script
 * This script will create all necessary database tables and fix common issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once(dirname(__FILE__) . '/wp-config.php');
}

echo "<h1>🔧 ChatGABI Database Fix & Initialization</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
</style>";

global $wpdb;

// Step 1: Check and create main BusinessCraft AI tables
echo "<div class='section'>";
echo "<h2>📊 Step 1: Main Database Tables</h2>";

$required_tables = array(
    'businesscraft_ai_chat_logs',
    'businesscraft_ai_transactions', 
    'businesscraft_ai_credit_logs',
    'businesscraft_ai_analytics'
);

foreach ($required_tables as $table_suffix) {
    $table_name = $wpdb->prefix . $table_suffix;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "<p class='success'>✅ Table exists: {$table_name}</p>";
    } else {
        echo "<p class='warning'>⚠️ Table missing: {$table_name}</p>";
    }
}

// Create tables if function exists
if (function_exists('businesscraft_ai_create_tables')) {
    echo "<p class='info'>🔧 Creating missing tables...</p>";
    businesscraft_ai_create_tables();
    echo "<p class='success'>✅ Table creation attempted</p>";
} else {
    echo "<p class='error'>❌ Table creation function not found</p>";
}
echo "</div>";

// Step 2: Check and create ChatGABI sector logs table
echo "<div class='section'>";
echo "<h2>📈 Step 2: Sector Analytics Table</h2>";

$sector_table = $wpdb->prefix . 'chatgabi_sector_logs';
$sector_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_table}'") === $sector_table;

if ($sector_table_exists) {
    echo "<p class='success'>✅ Sector logs table exists: {$sector_table}</p>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE {$sector_table}");
    echo "<p class='info'>📋 Table has " . count($columns) . " columns</p>";
    
    // Check data count
    $row_count = $wpdb->get_var("SELECT COUNT(*) FROM {$sector_table}");
    echo "<p class='info'>📊 Table has {$row_count} records</p>";
} else {
    echo "<p class='warning'>⚠️ Sector logs table missing: {$sector_table}</p>";
    
    if (function_exists('chatgabi_create_sector_logs_table')) {
        echo "<p class='info'>🔧 Creating sector logs table...</p>";
        chatgabi_create_sector_logs_table();
        
        $sector_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_table}'") === $sector_table;
        if ($sector_table_exists) {
            echo "<p class='success'>✅ Sector logs table created successfully</p>";
        } else {
            echo "<p class='error'>❌ Failed to create sector logs table</p>";
        }
    } else {
        echo "<p class='error'>❌ Sector table creation function not found</p>";
    }
}
echo "</div>";

// Step 3: Test analytics functions
echo "<div class='section'>";
echo "<h2>🧮 Step 3: Analytics Functions Test</h2>";

$analytics_functions = array(
    'businesscraft_ai_get_analytics',
    'businesscraft_ai_get_fallback_analytics',
    'businesscraft_ai_get_recent_transactions',
    'businesscraft_ai_get_top_users_by_usage',
    'businesscraft_ai_check_database_tables'
);

foreach ($analytics_functions as $function) {
    if (function_exists($function)) {
        echo "<p class='success'>✅ Function exists: {$function}</p>";
    } else {
        echo "<p class='warning'>⚠️ Function missing: {$function}</p>";
    }
}

// Test analytics data
if (function_exists('businesscraft_ai_get_fallback_analytics')) {
    echo "<p class='info'>🧪 Testing fallback analytics...</p>";
    try {
        $analytics = businesscraft_ai_get_fallback_analytics();
        echo "<p class='success'>✅ Analytics data retrieved:</p>";
        echo "<ul>";
        echo "<li>MAU: " . ($analytics['mau'] ?? 'N/A') . "</li>";
        echo "<li>Total Chats: " . ($analytics['total_chats'] ?? 'N/A') . "</li>";
        echo "<li>Revenue: $" . number_format($analytics['revenue'] ?? 0, 2) . "</li>";
        echo "<li>Avg Session: " . ($analytics['avg_session_length'] ?? 'N/A') . " min</li>";
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Analytics test failed: " . $e->getMessage() . "</p>";
    }
}
echo "</div>";

// Step 4: Check required constants and includes
echo "<div class='section'>";
echo "<h2>🔧 Step 4: Constants & Includes Check</h2>";

$required_constants = array(
    'CHATGABI_THEME_DIR',
    'CHATGABI_THEME_URL', 
    'CHATGABI_VERSION',
    'ABSPATH',
    'WPINC'
);

foreach ($required_constants as $constant) {
    if (defined($constant)) {
        echo "<p class='success'>✅ Constant defined: {$constant} = " . constant($constant) . "</p>";
    } else {
        echo "<p class='warning'>⚠️ Constant missing: {$constant}</p>";
        
        // Try to define missing constants
        if ($constant === 'WPINC' && !defined('WPINC')) {
            define('WPINC', 'wp-includes');
            echo "<p class='info'>🔧 Defined WPINC constant</p>";
        }
    }
}

// Check if database.php is included
$database_file = get_template_directory() . '/inc/database.php';
if (file_exists($database_file)) {
    echo "<p class='success'>✅ Database file exists: {$database_file}</p>";
    if (!function_exists('businesscraft_ai_create_tables')) {
        require_once $database_file;
        echo "<p class='info'>🔧 Database file included</p>";
    }
} else {
    echo "<p class='error'>❌ Database file missing: {$database_file}</p>";
}
echo "</div>";

// Step 5: Force table creation if needed
echo "<div class='section'>";
echo "<h2>🚀 Step 5: Force Database Initialization</h2>";

if (isset($_GET['force_create']) && $_GET['force_create'] === '1') {
    echo "<p class='info'>🔧 Force creating all tables...</p>";
    
    // Include database file if not already included
    $database_file = get_template_directory() . '/inc/database.php';
    if (file_exists($database_file)) {
        require_once $database_file;
    }
    
    // Create main tables
    if (function_exists('businesscraft_ai_create_tables')) {
        businesscraft_ai_create_tables();
        echo "<p class='success'>✅ Main tables creation attempted</p>";
    }
    
    // Create sector logs table
    if (function_exists('chatgabi_create_sector_logs_table')) {
        chatgabi_create_sector_logs_table();
        echo "<p class='success'>✅ Sector logs table creation attempted</p>";
    }
    
    echo "<p class='success'>🎉 Database initialization complete!</p>";
} else {
    echo "<p class='info'>💡 To force create all tables, <a href='?force_create=1'>click here</a></p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>✅ Fix Complete</h2>";
echo "<p>Database check and fix process completed. If you still see issues:</p>";
echo "<ul>";
echo "<li>1. Try the force create option above</li>";
echo "<li>2. Check your WordPress error logs</li>";
echo "<li>3. Verify theme activation completed successfully</li>";
echo "<li>4. Ensure proper file permissions</li>";
echo "</ul>";
echo "<p><strong>Next steps:</strong> <a href='" . admin_url('tools.php?page=chatgabi') . "'>Visit ChatGABI Dashboard</a></p>";
echo "</div>";
?>

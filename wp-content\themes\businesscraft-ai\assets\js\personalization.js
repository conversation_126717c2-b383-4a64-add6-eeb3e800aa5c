/**
 * Context Personalization JavaScript for ChatGABI
 * 
 * Handles personalized user experience, session context management,
 * and dynamic template suggestions based on user profile.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Personalization manager
    const Personalization = {
        sessionId: null,
        userContext: {},
        sessionContext: {},
        recommendations: {},
        isInitialized: false,

        /**
         * Initialize personalization system
         */
        init: function() {
            this.sessionId = this.generateSessionId();
            this.loadUserContext();
            this.bindEvents();
            this.startSessionTracking();
            
            console.log('ChatGABI: Personalization system initialized');
            this.isInitialized = true;
        },

        /**
         * Generate unique session ID
         */
        generateSessionId: function() {
            return 'pers_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        /**
         * Load user context and preferences
         */
        loadUserContext: function() {
            // Load from localStorage if available
            const stored = localStorage.getItem('chatgabi_user_context');
            if (stored) {
                try {
                    this.userContext = JSON.parse(stored);
                } catch (e) {
                    console.warn('ChatGABI: Failed to parse stored user context');
                }
            }

            // Load personalized recommendations
            this.loadPersonalizedRecommendations();
        },

        /**
         * Load personalized recommendations
         */
        loadPersonalizedRecommendations: function() {
            $.ajax({
                url: chatgabiPersonalization.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_personalized_suggestions',
                    type: 'templates',
                    limit: 10
                },
                success: function(response) {
                    if (response.success) {
                        this.recommendations.templates = response.data.suggestions;
                        this.updateTemplateInterface();
                    }
                }.bind(this),
                error: function(xhr, status, error) {
                    console.error('ChatGABI: Failed to load recommendations:', error);
                }
            });

            // Load sector recommendations
            $.ajax({
                url: chatgabiPersonalization.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_personalized_suggestions',
                    type: 'sectors',
                    limit: 5
                },
                success: function(response) {
                    if (response.success) {
                        this.recommendations.sectors = response.data.suggestions;
                        this.updateSectorInterface();
                    }
                }.bind(this)
            });
        },

        /**
         * Bind event listeners
         */
        bindEvents: function() {
            // Chat events
            $(document).on('chatgabi:messageStart', this.handleMessageStart.bind(this));
            $(document).on('chatgabi:responseCompleted', this.handleResponseCompleted.bind(this));
            
            // Template selection events
            $(document).on('click', '.personalized-template', this.handleTemplateSelection.bind(this));
            $(document).on('click', '.recommendation-item', this.handleRecommendationClick.bind(this));
            
            // Context update events
            $(document).on('chatgabi:profileUpdated', this.handleProfileUpdate.bind(this));
            $(document).on('chatgabi:preferencesChanged', this.handlePreferencesChange.bind(this));
            
            // Session management
            $(window).on('beforeunload', this.saveSessionContext.bind(this));
            $(document).on('visibilitychange', this.handleVisibilityChange.bind(this));
        },

        /**
         * Start session tracking
         */
        startSessionTracking: function() {
            this.sessionContext = {
                sessionId: this.sessionId,
                startTime: Date.now(),
                conversationHistory: [],
                currentFocus: null,
                businessGoals: [],
                contextScore: 0.5
            };

            // Try to restore previous session context
            this.restoreSessionContext();
        },

        /**
         * Handle message start
         */
        handleMessageStart: function(event, messageData) {
            // Update session context with new message
            this.sessionContext.conversationHistory.push({
                type: 'user_message',
                content: messageData.message,
                timestamp: Date.now(),
                context: this.extractMessageContext(messageData.message)
            });

            // Detect business focus from message
            const detectedFocus = this.detectBusinessFocus(messageData.message);
            if (detectedFocus) {
                this.sessionContext.currentFocus = detectedFocus;
            }

            // Update context score based on relevance
            this.updateContextScore(messageData);
        },

        /**
         * Handle response completion
         */
        handleResponseCompleted: function(event, responseData) {
            // Add AI response to conversation history
            this.sessionContext.conversationHistory.push({
                type: 'ai_response',
                content: responseData.response,
                timestamp: Date.now(),
                tokensUsed: responseData.tokensUsed,
                responseTime: responseData.responseTime
            });

            // Update personalized suggestions based on conversation
            this.updatePersonalizedSuggestions(responseData);

            // Save session context
            this.saveSessionContext();
        },

        /**
         * Extract context from message
         */
        extractMessageContext: function(message) {
            const context = {
                keywords: [],
                businessStage: null,
                industry: null,
                goals: []
            };

            // Simple keyword extraction
            const businessKeywords = {
                'startup': ['startup', 'start up', 'new business', 'launch'],
                'growth': ['grow', 'scale', 'expand', 'increase'],
                'funding': ['funding', 'investment', 'capital', 'loan'],
                'marketing': ['marketing', 'promotion', 'advertising', 'brand'],
                'sales': ['sales', 'revenue', 'customers', 'clients']
            };

            const lowerMessage = message.toLowerCase();

            for (const [category, keywords] of Object.entries(businessKeywords)) {
                for (const keyword of keywords) {
                    if (lowerMessage.includes(keyword)) {
                        context.keywords.push(category);
                        break;
                    }
                }
            }

            return context;
        },

        /**
         * Detect business focus from message
         */
        detectBusinessFocus: function(message) {
            const focusPatterns = {
                'business_planning': /business plan|planning|strategy|model/i,
                'marketing': /marketing|promotion|advertising|brand|customer/i,
                'funding': /funding|investment|capital|loan|finance/i,
                'operations': /operations|process|workflow|efficiency/i,
                'legal': /legal|compliance|regulation|license|permit/i,
                'financial': /financial|accounting|budget|cash flow|profit/i
            };

            for (const [focus, pattern] of Object.entries(focusPatterns)) {
                if (pattern.test(message)) {
                    return focus;
                }
            }

            return null;
        },

        /**
         * Update context score
         */
        updateContextScore: function(messageData) {
            let score = this.sessionContext.contextScore;

            // Increase score for relevant context
            if (this.sessionContext.currentFocus) {
                score += 0.1;
            }

            // Increase score for conversation continuity
            if (this.sessionContext.conversationHistory.length > 1) {
                score += 0.05;
            }

            // Decrease score for context switches
            const lastFocus = this.getLastBusinessFocus();
            if (lastFocus && this.sessionContext.currentFocus && lastFocus !== this.sessionContext.currentFocus) {
                score -= 0.1;
            }

            this.sessionContext.contextScore = Math.max(0, Math.min(1, score));
        },

        /**
         * Get last business focus from conversation
         */
        getLastBusinessFocus: function() {
            const history = this.sessionContext.conversationHistory;
            for (let i = history.length - 2; i >= 0; i--) {
                if (history[i].context && history[i].context.businessStage) {
                    return history[i].context.businessStage;
                }
            }
            return null;
        },

        /**
         * Update personalized suggestions
         */
        updatePersonalizedSuggestions: function(responseData) {
            // Generate contextual suggestions based on current conversation
            const suggestions = this.generateContextualSuggestions();
            
            if (suggestions.length > 0) {
                this.showContextualSuggestions(suggestions);
            }
        },

        /**
         * Generate contextual suggestions
         */
        generateContextualSuggestions: function() {
            const suggestions = [];
            const currentFocus = this.sessionContext.currentFocus;

            if (!currentFocus) {
                return suggestions;
            }

            // Focus-specific suggestions
            const focusSuggestions = {
                'business_planning': [
                    { title: 'Create Business Model Canvas', template: 'business_model_canvas' },
                    { title: 'Market Analysis Template', template: 'market_analysis' },
                    { title: 'Financial Projections', template: 'financial_projections' }
                ],
                'marketing': [
                    { title: 'Marketing Strategy Template', template: 'marketing_strategy' },
                    { title: 'Social Media Plan', template: 'social_media_plan' },
                    { title: 'Customer Persona Template', template: 'customer_persona' }
                ],
                'funding': [
                    { title: 'Pitch Deck Template', template: 'pitch_deck' },
                    { title: 'Financial Forecast', template: 'financial_forecast' },
                    { title: 'Investment Proposal', template: 'investment_proposal' }
                ]
            };

            if (focusSuggestions[currentFocus]) {
                suggestions.push(...focusSuggestions[currentFocus]);
            }

            return suggestions;
        },

        /**
         * Show contextual suggestions
         */
        showContextualSuggestions: function(suggestions) {
            // Remove existing suggestions
            $('.contextual-suggestions').remove();

            const suggestionsHtml = this.generateSuggestionsHtml(suggestions);
            
            // Insert after chat messages
            $('.chat-messages').after(suggestionsHtml);
            
            // Animate appearance
            $('.contextual-suggestions').hide().slideDown(300);
        },

        /**
         * Generate suggestions HTML
         */
        generateSuggestionsHtml: function(suggestions) {
            let html = '<div class="contextual-suggestions">';
            html += '<h4>Suggested Next Steps</h4>';
            html += '<div class="suggestions-grid">';

            suggestions.forEach(suggestion => {
                html += `
                    <div class="suggestion-item" data-template="${suggestion.template}">
                        <div class="suggestion-icon">📋</div>
                        <div class="suggestion-content">
                            <h5>${suggestion.title}</h5>
                            <p>Based on your current conversation</p>
                        </div>
                        <button class="use-template-btn">Use Template</button>
                    </div>
                `;
            });

            html += '</div></div>';
            return html;
        },

        /**
         * Handle template selection
         */
        handleTemplateSelection: function(event) {
            const $template = $(event.currentTarget);
            const templateId = $template.data('template');
            const templateTitle = $template.find('h5').text();

            // Record interaction
            this.recordTemplateInteraction(templateId, 'selected');

            // Load template
            this.loadTemplate(templateId, templateTitle);
        },

        /**
         * Handle recommendation click
         */
        handleRecommendationClick: function(event) {
            const $item = $(event.currentTarget);
            const recommendationId = $item.data('recommendation-id');
            const type = $item.data('type');

            // Record interaction
            this.recordRecommendationInteraction(recommendationId, type);

            // Update recommendation usage
            this.updateRecommendationUsage(recommendationId);
        },

        /**
         * Record template interaction
         */
        recordTemplateInteraction: function(templateId, action) {
            const interactionData = {
                templateId: templateId,
                action: action,
                sessionId: this.sessionId,
                context: this.sessionContext.currentFocus,
                timestamp: Date.now()
            };

            // Send to analytics
            $(document).trigger('chatgabi:templateInteraction', [interactionData]);
        },

        /**
         * Record recommendation interaction
         */
        recordRecommendationInteraction: function(recommendationId, type) {
            $.ajax({
                url: chatgabiPersonalization.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_record_interaction_pattern',
                    interaction_type: 'recommendation_click',
                    pattern_data: JSON.stringify({
                        recommendationId: recommendationId,
                        type: type,
                        sessionId: this.sessionId,
                        timestamp: Date.now()
                    }),
                    nonce: chatgabiPersonalization.nonce
                }
            });
        },

        /**
         * Load template
         */
        loadTemplate: function(templateId, templateTitle) {
            // Show loading state
            this.showTemplateLoading(templateTitle);

            // Load template content
            $.ajax({
                url: chatgabiPersonalization.ajaxUrl,
                type: 'GET',
                data: {
                    action: 'chatgabi_get_template_content',
                    template_id: templateId,
                    personalized: true
                },
                success: function(response) {
                    if (response.success) {
                        this.displayTemplate(response.data);
                    } else {
                        this.showTemplateError('Failed to load template');
                    }
                }.bind(this),
                error: function() {
                    this.showTemplateError('Network error loading template');
                }.bind(this)
            });
        },

        /**
         * Display template
         */
        displayTemplate: function(templateData) {
            // Hide loading
            $('.template-loading').remove();

            // Create template interface
            const templateHtml = `
                <div class="personalized-template-interface">
                    <div class="template-header">
                        <h3>${templateData.title}</h3>
                        <button class="close-template">×</button>
                    </div>
                    <div class="template-content">
                        ${templateData.content}
                    </div>
                    <div class="template-actions">
                        <button class="customize-template">Customize</button>
                        <button class="use-template">Use Template</button>
                    </div>
                </div>
            `;

            $('body').append(templateHtml);
            $('.personalized-template-interface').hide().fadeIn(300);
        },

        /**
         * Save session context
         */
        saveSessionContext: function() {
            if (!this.isInitialized) return;

            const contextData = {
                type: 'session_context',
                business_focus: this.sessionContext.currentFocus,
                current_goals: this.sessionContext.businessGoals.join(','),
                conversation_history: this.sessionContext.conversationHistory.slice(-10), // Keep last 10 messages
                context_score: this.sessionContext.contextScore
            };

            $.ajax({
                url: chatgabiPersonalization.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_save_session_context',
                    session_id: this.sessionId,
                    context_data: JSON.stringify(contextData),
                    expires_in: 3600, // 1 hour
                    nonce: chatgabiPersonalization.nonce
                },
                async: false // Ensure it completes before page unload
            });
        },

        /**
         * Restore session context
         */
        restoreSessionContext: function() {
            const lastSessionId = localStorage.getItem('chatgabi_last_session');
            
            if (lastSessionId) {
                $.ajax({
                    url: chatgabiPersonalization.ajaxUrl,
                    type: 'GET',
                    data: {
                        action: 'chatgabi_get_session_context',
                        session_id: lastSessionId
                    },
                    success: function(response) {
                        if (response.success) {
                            const context = response.data.context;
                            this.sessionContext.currentFocus = context.business_focus;
                            this.sessionContext.businessGoals = context.current_goals ? context.current_goals.split(',') : [];
                            this.sessionContext.conversationHistory = context.conversation_history || [];
                            this.sessionContext.contextScore = context.context_score || 0.5;
                            
                            console.log('ChatGABI: Session context restored');
                        }
                    }.bind(this)
                });
            }
        },

        /**
         * Update template interface with personalized suggestions
         */
        updateTemplateInterface: function() {
            if (!this.recommendations.templates) return;

            const $container = $('.template-suggestions, .personalized-templates');
            
            if ($container.length === 0) return;

            let html = '<div class="personalized-section"><h4>Recommended for You</h4>';
            
            this.recommendations.templates.slice(0, 5).forEach(template => {
                html += `
                    <div class="personalized-template" data-template="${template.template_id}">
                        <div class="template-relevance">${Math.round(template.relevance_score * 100)}% match</div>
                        <h5>${template.title}</h5>
                        <p>${template.description}</p>
                        <span class="template-category">${template.category}</span>
                    </div>
                `;
            });
            
            html += '</div>';
            
            $container.prepend(html);
        },

        /**
         * Update sector interface with personalized suggestions
         */
        updateSectorInterface: function() {
            if (!this.recommendations.sectors) return;

            const $container = $('.sector-suggestions, .dashboard-sectors');
            
            if ($container.length === 0) return;

            let html = '<div class="personalized-sectors"><h4>Relevant Sectors</h4>';
            
            this.recommendations.sectors.forEach(sector => {
                html += `
                    <div class="sector-recommendation" data-sector="${sector.sector}">
                        <h5>${sector.title}</h5>
                        <p>${sector.description}</p>
                        <span class="sector-relevance">${Math.round(sector.relevance_score * 100)}% relevant</span>
                    </div>
                `;
            });
            
            html += '</div>';
            
            $container.prepend(html);
        },

        /**
         * Handle visibility change
         */
        handleVisibilityChange: function() {
            if (document.hidden) {
                this.saveSessionContext();
            }
        },

        /**
         * Handle profile update
         */
        handleProfileUpdate: function(event, profileData) {
            // Reload recommendations when profile changes
            this.loadPersonalizedRecommendations();
            
            // Update user context
            this.userContext = { ...this.userContext, ...profileData };
            localStorage.setItem('chatgabi_user_context', JSON.stringify(this.userContext));
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof chatgabiPersonalization !== 'undefined') {
            Personalization.init();
            
            // Store session ID for restoration
            localStorage.setItem('chatgabi_last_session', Personalization.sessionId);
        }
    });

    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.Personalization = Personalization;

})(jQuery);

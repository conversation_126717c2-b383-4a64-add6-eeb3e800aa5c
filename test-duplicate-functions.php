<?php
/**
 * Test script to check for duplicate function declarations
 * This script will help verify that the fatal error is resolved
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Define missing constants
if (!defined('WPINC')) {
    define('WPINC', 'wp-includes');
}

echo "<h1>ChatGABI Duplicate Function Test</h1>\n";

// Test 1: Check if we can include both files without fatal errors
echo "<h2>Test 1: Include Files Test</h2>\n";

try {
    // Include WordPress core first
    if (!function_exists('wp_remote_get')) {
        $wp_includes = ABSPATH . 'wp-includes/';
        if (file_exists($wp_includes . 'http.php')) {
            require_once($wp_includes . 'http.php');
        }
        if (file_exists($wp_includes . 'functions.php')) {
            require_once($wp_includes . 'functions.php');
        }
    }

    echo "<p>✅ WordPress core loading attempted</p>\n";
    
    // Include paystack integration first
    require_once(ABSPATH . 'wp-content/themes/businesscraft-ai/inc/paystack-integration.php');
    echo "<p>✅ Paystack integration included successfully</p>\n";
    
    // Include admin dashboard
    require_once(ABSPATH . 'wp-content/themes/businesscraft-ai/inc/admin-dashboard.php');
    echo "<p>✅ Admin dashboard included successfully</p>\n";
    
    echo "<p><strong>SUCCESS:</strong> No fatal errors detected!</p>\n";
    
} catch (Error $e) {
    echo "<p style='color: red;'><strong>FATAL ERROR:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p>File: " . $e->getFile() . "</p>\n";
    echo "<p>Line: " . $e->getLine() . "</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>EXCEPTION:</strong> " . $e->getMessage() . "</p>\n";
}

// Test 2: Check specific function existence
echo "<h2>Test 2: Function Existence Check</h2>\n";

$functions_to_check = [
    'businesscraft_ai_check_ip_whitelist_status',
    'businesscraft_ai_test_paystack_connection',
    'businesscraft_ai_get_recent_transactions',
    'businesscraft_ai_admin_page',
    'chatgabi_admin_page'
];

foreach ($functions_to_check as $function_name) {
    if (function_exists($function_name)) {
        echo "<p>✅ Function exists: <code>{$function_name}</code></p>\n";
        
        // Get function reflection to see where it's defined
        try {
            $reflection = new ReflectionFunction($function_name);
            $file = str_replace(ABSPATH, '', $reflection->getFileName());
            echo "<p style='margin-left: 20px; font-size: 12px; color: #666;'>Defined in: {$file} at line {$reflection->getStartLine()}</p>\n";
        } catch (Exception $e) {
            echo "<p style='margin-left: 20px; font-size: 12px; color: #999;'>Could not get reflection info</p>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Function missing: <code>{$function_name}</code></p>\n";
    }
}

// Test 3: Check for any remaining duplicate function issues
echo "<h2>Test 3: Duplicate Function Detection</h2>\n";

$declared_functions = get_defined_functions()['user'];
$businesscraft_functions = array_filter($declared_functions, function($func) {
    return strpos($func, 'businesscraft_ai_') === 0 || strpos($func, 'chatgabi_') === 0;
});

echo "<p>Found " . count($businesscraft_functions) . " ChatGABI/BusinessCraft functions:</p>\n";
echo "<ul>\n";
foreach ($businesscraft_functions as $func) {
    echo "<li><code>{$func}</code></li>\n";
}
echo "</ul>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>If you see this message without any fatal errors above, the duplicate function issue has been resolved!</p>\n";
?>

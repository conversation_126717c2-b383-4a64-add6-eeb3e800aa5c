/*! This file is auto-generated */
(()=>{"use strict";var n={d:(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o:(n,e)=>Object.prototype.hasOwnProperty.call(n,e),r:n=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})}},e={};let t,r,o,s;n.r(e),n.d(e,{parse:()=>i});const l=/<!--\s+(\/)?wp:([a-z][a-z0-9_-]*\/)?([a-z][a-z0-9_-]*)\s+({(?:(?=([^}]+|}+(?=})|(?!}\s+\/?-->)[^])*)\5|[^]*?)}\s+)?(\/)?-->/g;function u(n,e,t,r,o){return{blockName:n,attrs:e,innerBlocks:t,innerHTML:r,innerContent:o}}function c(n){return u(null,{},[],n,[n])}const i=n=>{t=n,r=0,o=[],s=[],l.lastIndex=0;do{}while(f());return o};function f(){const n=s.length,e=function(){const n=l.exec(t);if(null===n)return["no-more-tokens","",null,0,0];const e=n.index,[r,o,s,u,c,,i]=n,f=r.length,p=!!o,a=!!i,b=(s||"core/")+u,k=!!c,h=k?function(n){try{return JSON.parse(n)}catch(n){return null}}(c):{};if(a)return["void-block",b,h,e,f];if(p)return["block-closer",b,null,e,f];return["block-opener",b,h,e,f]}(),[i,f,k,h,d]=e,g=h>r?r:null;switch(i){case"no-more-tokens":if(0===n)return p(),!1;if(1===n)return b(),!1;for(;0<s.length;)b();return!1;case"void-block":return 0===n?(null!==g&&o.push(c(t.substr(g,h-g))),o.push(u(f,k,[],"",[])),r=h+d,!0):(a(u(f,k,[],"",[]),h,d),r=h+d,!0);case"block-opener":return s.push(function(n,e,t,r,o){return{block:n,tokenStart:e,tokenLength:t,prevOffset:r||e+t,leadingHtmlStart:o}}(u(f,k,[],"",[]),h,d,h+d,g)),r=h+d,!0;case"block-closer":if(0===n)return p(),!1;if(1===n)return b(h),r=h+d,!0;const e=s.pop(),l=t.substr(e.prevOffset,h-e.prevOffset);return e.block.innerHTML+=l,e.block.innerContent.push(l),e.prevOffset=h+d,a(e.block,e.tokenStart,e.tokenLength,h+d),r=h+d,!0;default:return p(),!1}}function p(n){const e=n||t.length-r;0!==e&&o.push(c(t.substr(r,e)))}function a(n,e,r,o){const l=s[s.length-1];l.block.innerBlocks.push(n);const u=t.substr(l.prevOffset,e-l.prevOffset);u&&(l.block.innerHTML+=u,l.block.innerContent.push(u)),l.block.innerContent.push(null),l.prevOffset=o||e+r}function b(n){const{block:e,leadingHtmlStart:r,prevOffset:l,tokenStart:u}=s.pop(),i=n?t.substr(l,n-l):t.substr(l);i&&(e.innerHTML+=i,e.innerContent.push(i)),null!==r&&o.push(c(t.substr(r,u-r))),o.push(e)}(window.wp=window.wp||{}).blockSerializationDefaultParser=e})();
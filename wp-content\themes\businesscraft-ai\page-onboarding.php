<?php
/**
 * Template Name: ChatGABI Onboarding
 *
 * @package ChatGABI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

$user_id = get_current_user_id();

// Check if user has already completed onboarding using new system
$onboarding_completed = get_user_meta($user_id, 'chatgabi_onboarding_completed', true);
if ($onboarding_completed) {
    wp_redirect(home_url('/dashboard/'));
    exit;
}

// Enqueue onboarding assets
wp_enqueue_style('chatgabi-onboarding-css', CHATGABI_THEME_URL . '/assets/css/onboarding.css', array(), CHATGABI_VERSION);
wp_enqueue_script('chatgabi-onboarding-js', CHATGABI_THEME_URL . '/assets/js/onboarding.js', array('jquery'), CHATGABI_VERSION, true);

// Localize script with AJAX data
wp_localize_script('chatgabi-onboarding-js', 'chatgabiOnboardingConfig', array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('chatgabi_onboarding_nonce'),
    'dashboardUrl' => home_url('/dashboard/'),
    'currentStep' => 'welcome',
    'steps' => array('welcome', 'profile_type', 'business_basics', 'business_stage', 'goals_challenges', 'preferences', 'template_recommendations', 'dashboard_setup', 'complete'),
    'strings' => array(
        'selectOption' => __('Please select an option before continuing.', 'chatgabi'),
        'fillRequired' => __('Please fill in all required fields.', 'chatgabi'),
        'processingError' => __('An error occurred while processing your request.', 'chatgabi'),
        'networkError' => __('Network error. Please try again.', 'chatgabi')
    )
));

get_header();
?>

<?php
// Load the new onboarding flow template
get_template_part('template-parts/onboarding-flow');
?>

<?php get_footer(); ?>

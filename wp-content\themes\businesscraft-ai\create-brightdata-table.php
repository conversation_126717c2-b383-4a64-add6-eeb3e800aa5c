<?php
/**
 * Create Bright Data Usage Tracking Table
 * 
 * This script creates the Bright Data usage tracking table for pay-per-use monitoring.
 *
 * @package ChatGABI
 * @since 1.4.0
 */

// Database configuration
$db_host = 'localhost';
$db_name = 'swifmind_local';
$db_user = 'root';
$db_pass = '';
$table_prefix = 'wp_';

echo "🔧 ChatGABI Bright Data Usage Table Creation\n";
echo "===========================================\n";
echo "Creating Bright Data usage tracking table for pay-per-use monitoring...\n\n";

// Connect to database
echo "📡 Connecting to database...\n";
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
    echo "✅ Database: $db_name\n\n";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Create Bright Data usage tracking table
echo "🔨 Creating Bright Data usage tracking table...\n";

try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_brightdata_usage (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        url varchar(500) NOT NULL,
        country varchar(50) NOT NULL,
        sector varchar(255) DEFAULT NULL,
        data_size_kb decimal(10,3) NOT NULL,
        data_size_mb decimal(10,6) NOT NULL,
        cost_usd decimal(10,6) NOT NULL,
        success boolean DEFAULT true,
        response_time_ms int DEFAULT 0,
        compression_ratio decimal(5,2) DEFAULT 0,
        request_type varchar(50) DEFAULT 'standard',
        zone_id varchar(100) DEFAULT NULL,
        session_id varchar(100) DEFAULT NULL,
        month_year varchar(7) NOT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY url (url(255)),
        KEY country (country),
        KEY sector (sector),
        KEY month_year (month_year),
        KEY success (success),
        KEY cost_tracking (month_year, cost_usd),
        KEY data_volume (month_year, data_size_mb),
        KEY performance_tracking (success, response_time_ms)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ Bright Data usage tracking table created successfully\n";
    
    // Verify table creation
    $table_exists = $pdo->query("SHOW TABLES LIKE '{$table_prefix}chatgabi_brightdata_usage'")->rowCount() > 0;
    
    if ($table_exists) {
        echo "✅ Table verification: EXISTS\n";
        
        // Insert sample data for testing
        $sample_data = $pdo->prepare("INSERT INTO {$table_prefix}chatgabi_brightdata_usage 
            (url, country, sector, data_size_kb, data_size_mb, cost_usd, success, response_time_ms, request_type, month_year) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $sample_data->execute([
            'https://bog.gov.gh/test',
            'Ghana',
            'Government',
            150.5,
            0.147,
            0.002205, // 0.147 MB * $15/GB
            true,
            3500,
            'government_portal',
            date('Y-m')
        ]);
        
        echo "✅ Sample data inserted for testing\n";
        
        // Get record count
        $count = $pdo->query("SELECT COUNT(*) FROM {$table_prefix}chatgabi_brightdata_usage")->fetchColumn();
        echo "✅ Current records: $count\n";
        
        // Show cost calculation example
        $total_cost = $pdo->query("SELECT SUM(cost_usd) FROM {$table_prefix}chatgabi_brightdata_usage WHERE month_year = '" . date('Y-m') . "'")->fetchColumn();
        $total_data_mb = $pdo->query("SELECT SUM(data_size_mb) FROM {$table_prefix}chatgabi_brightdata_usage WHERE month_year = '" . date('Y-m') . "'")->fetchColumn();
        
        echo "✅ Current month cost: $" . number_format($total_cost, 6) . "\n";
        echo "✅ Current month data: " . number_format($total_data_mb, 3) . " MB\n";
        
    } else {
        echo "❌ Table verification: FAILED\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Failed to create Bright Data usage tracking table: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 BRIGHT DATA USAGE TRACKING TABLE CREATED SUCCESSFULLY!\n";
echo "========================================================\n";
echo "✅ Table: wp_chatgabi_brightdata_usage\n";
echo "✅ Purpose: Track Bright Data pay-per-use consumption and costs\n";
echo "✅ Features: Data volume tracking, cost calculation, performance monitoring\n";
echo "✅ Ready for: Bright Data API integration and cost optimization\n";

echo "\n💰 Cost Tracking Features:\n";
echo "- Real-time data consumption monitoring\n";
echo "- Automatic cost calculation based on $15-20/GB pricing\n";
echo "- Monthly usage summaries and projections\n";
echo "- Compression ratio tracking for optimization\n";
echo "- Performance metrics for routing decisions\n";

echo "\n🚀 Next Steps:\n";
echo "1. Configure Bright Data API key in WordPress Admin → ChatGABI → Hybrid Scraping\n";
echo "2. Set up Bright Data zone ID for African proxy network\n";
echo "3. Enable hybrid scraping mode with Bright Data routing\n";
echo "4. Monitor usage and costs through admin dashboard\n";
echo "5. Optimize routing based on cost-performance data\n";

echo "\nBright Data usage tracking table creation completed at: " . date('Y-m-d H:i:s') . "\n";
?>

<?php
/**
 * Debug Analytics Dashboard Data
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions');
}

echo "<h1>🔍 Analytics Debug</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.info { color: blue; }
.debug-section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test analytics function
echo "<div class='debug-section'>";
echo "<h2>📊 Analytics Function Test</h2>";

if (function_exists('chatgabi_get_engagement_analytics_data')) {
    echo "<p class='success'>✅ Function exists</p>";
    
    try {
        // Clear cache first
        delete_transient('chatgabi_engagement_analytics_data');
        
        $analytics_data = chatgabi_get_engagement_analytics_data();
        
        echo "<p class='success'>✅ Function executed successfully</p>";
        echo "<h3>Raw Analytics Data:</h3>";
        echo "<pre>" . json_encode($analytics_data, JSON_PRETTY_PRINT) . "</pre>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='error'>❌ Function not found</p>";
}
echo "</div>";

// Test database tables
echo "<div class='debug-section'>";
echo "<h2>🗄️ Database Tables</h2>";

global $wpdb;
$sector_logs_table = $wpdb->prefix . 'chatgabi_sector_logs';
$chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';

$sector_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_logs_table}'") === $sector_logs_table;
$chat_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$chat_logs_table}'") === $chat_logs_table;

echo "<p><strong>Sector Logs Table ({$sector_logs_table}):</strong> " . ($sector_table_exists ? "<span class='success'>✅ EXISTS</span>" : "<span class='error'>❌ MISSING</span>") . "</p>";
echo "<p><strong>Chat Logs Table ({$chat_logs_table}):</strong> " . ($chat_table_exists ? "<span class='success'>✅ EXISTS</span>" : "<span class='error'>❌ MISSING</span>") . "</p>";

if ($sector_table_exists) {
    $count = $wpdb->get_var("SELECT COUNT(*) FROM {$sector_logs_table}");
    echo "<p><strong>Records in sector logs:</strong> {$count}</p>";
    
    if ($count > 0) {
        $sample = $wpdb->get_results("SELECT * FROM {$sector_logs_table} LIMIT 3");
        echo "<h3>Sample Records:</h3>";
        echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT) . "</pre>";
    }
}
echo "</div>";

// Test individual analytics functions
echo "<div class='debug-section'>";
echo "<h2>🧪 Individual Function Tests</h2>";

if ($sector_table_exists) {
    // Test summary function
    if (function_exists('chatgabi_get_analytics_summary')) {
        try {
            $summary = chatgabi_get_analytics_summary($sector_logs_table);
            echo "<h3>Summary Data:</h3>";
            echo "<pre>" . json_encode($summary, JSON_PRETTY_PRINT) . "</pre>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Summary function error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test top sectors function
    if (function_exists('chatgabi_get_top_sectors')) {
        try {
            $sectors = chatgabi_get_top_sectors($sector_logs_table);
            echo "<h3>Top Sectors:</h3>";
            echo "<pre>" . json_encode($sectors, JSON_PRETTY_PRINT) . "</pre>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Top sectors function error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test country breakdown function
    if (function_exists('chatgabi_get_country_breakdown')) {
        try {
            $countries = chatgabi_get_country_breakdown($sector_logs_table);
            echo "<h3>Country Breakdown:</h3>";
            echo "<pre>" . json_encode($countries, JSON_PRETTY_PRINT) . "</pre>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Country breakdown function error: " . $e->getMessage() . "</p>";
        }
    }
}
echo "</div>";

// Test constants and URLs
echo "<div class='debug-section'>";
echo "<h2>🔗 Constants and URLs</h2>";

echo "<p><strong>CHATGABI_THEME_URL:</strong> " . (defined('CHATGABI_THEME_URL') ? CHATGABI_THEME_URL : 'NOT DEFINED') . "</p>";
echo "<p><strong>CHATGABI_VERSION:</strong> " . (defined('CHATGABI_VERSION') ? CHATGABI_VERSION : 'NOT DEFINED') . "</p>";

$js_file = CHATGABI_THEME_URL . '/assets/js/admin-analytics-extended.js';
$css_file = CHATGABI_THEME_URL . '/assets/css/admin-analytics-extended.css';

echo "<p><strong>JS File URL:</strong> <a href='{$js_file}' target='_blank'>{$js_file}</a></p>";
echo "<p><strong>CSS File URL:</strong> <a href='{$css_file}' target='_blank'>{$css_file}</a></p>";

// Check if files exist
$js_path = CHATGABI_THEME_DIR . '/assets/js/admin-analytics-extended.js';
$css_path = CHATGABI_THEME_DIR . '/assets/css/admin-analytics-extended.css';

echo "<p><strong>JS File Exists:</strong> " . (file_exists($js_path) ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
echo "<p><strong>CSS File Exists:</strong> " . (file_exists($css_path) ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";

echo "</div>";

// Test Chart.js CDN
echo "<div class='debug-section'>";
echo "<h2>📊 Chart.js CDN Test</h2>";
echo "<p>Testing Chart.js CDN availability:</p>";
echo "<script src='https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js'></script>";
echo "<script>
if (typeof Chart !== 'undefined') {
    document.write('<p class=\"success\">✅ Chart.js loaded successfully (Version: ' + Chart.version + ')</p>');
} else {
    document.write('<p class=\"error\">❌ Chart.js failed to load</p>');
}
</script>";
echo "</div>";

// Test admin page hook
echo "<div class='debug-section'>";
echo "<h2>🎯 Admin Page Hook Test</h2>";

$current_screen = get_current_screen();
echo "<p><strong>Current Screen:</strong> " . ($current_screen ? $current_screen->id : 'Not available') . "</p>";
echo "<p><strong>Expected Hook:</strong> chatgabi_page_chatgabi-engagement-analytics</p>";

// Check if we're on the right page
$is_analytics_page = isset($_GET['page']) && $_GET['page'] === 'chatgabi-engagement-analytics';
echo "<p><strong>Is Analytics Page:</strong> " . ($is_analytics_page ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";

echo "</div>";

echo "<h2>✅ Debug Complete!</h2>";
echo "<p><a href='" . admin_url('tools.php?page=chatgabi-engagement-analytics') . "'>← Back to Analytics Dashboard</a></p>";

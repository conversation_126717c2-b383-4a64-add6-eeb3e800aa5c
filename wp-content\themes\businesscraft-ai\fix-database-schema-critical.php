<?php
/**
 * Critical Database Schema Fix for ChatGABI
 * Fixes prompt_text vs prompt_content column mismatch and other schema issues
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Determine WordPress root directory
$wp_root = dirname(dirname(dirname(__DIR__)));
$wp_load_path = $wp_root . '/wp-load.php';
$wp_config_path = $wp_root . '/wp-config.php';

// Load WordPress with proper error handling
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} elseif (file_exists($wp_config_path)) {
    require_once $wp_config_path;
    require_once $wp_root . '/wp-settings.php';
} else {
    die('Error: Could not find WordPress installation. Please ensure this script is in the correct directory.');
}

// Security check - only run if WordPress is loaded and user has permissions
if (!function_exists('current_user_can')) {
    die('Error: WordPress not properly loaded');
}

if (!current_user_can('manage_options')) {
    wp_die('Unauthorized access - Administrator privileges required');
}

echo '<h1>🔧 ChatGABI Critical Database Schema Fix</h1>';
echo '<p>Fixing database schema issues identified in audit...</p>';

global $wpdb;

// Step 1: Check and fix prompt templates table
echo '<h2>Step 1: Fixing Prompt Templates Table Schema</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") === $templates_table;

if ($table_exists) {
    // Check current columns
    $columns = $wpdb->get_results("DESCRIBE {$templates_table}");
    $column_names = array_column($columns, 'Field');
    
    echo '<h3>Current Table Structure:</h3>';
    echo '<ul>';
    foreach ($column_names as $column) {
        echo "<li>✓ {$column}</li>";
    }
    echo '</ul>';
    
    $has_prompt_text = in_array('prompt_text', $column_names);
    $has_prompt_content = in_array('prompt_content', $column_names);
    
    if ($has_prompt_text && !$has_prompt_content) {
        echo '<p>✅ Table has prompt_text column (correct schema)</p>';
        echo '<p>ℹ️ No changes needed - code should use prompt_text</p>';
    } elseif (!$has_prompt_text && $has_prompt_content) {
        echo '<p>⚠️ Table has prompt_content column - standardizing to prompt_text</p>';
        
        // Rename column to match expected schema
        $alter_result = $wpdb->query("ALTER TABLE {$templates_table} CHANGE COLUMN prompt_content prompt_text LONGTEXT NOT NULL");
        
        if ($alter_result !== false) {
            echo '<p>✅ Successfully renamed prompt_content to prompt_text</p>';
        } else {
            echo '<p>❌ Failed to rename column: ' . $wpdb->last_error . '</p>';
        }
    } elseif ($has_prompt_text && $has_prompt_content) {
        echo '<p>⚠️ Table has both columns - removing duplicate prompt_content</p>';
        
        // Copy data from prompt_content to prompt_text if prompt_text is empty
        $wpdb->query("UPDATE {$templates_table} SET prompt_text = prompt_content WHERE prompt_text = '' OR prompt_text IS NULL");
        
        // Drop the duplicate column
        $drop_result = $wpdb->query("ALTER TABLE {$templates_table} DROP COLUMN prompt_content");
        
        if ($drop_result !== false) {
            echo '<p>✅ Successfully removed duplicate prompt_content column</p>';
        } else {
            echo '<p>❌ Failed to drop column: ' . $wpdb->last_error . '</p>';
        }
    } else {
        echo '<p>❌ Table missing both prompt_text and prompt_content columns</p>';
        
        // Add the correct column
        $add_result = $wpdb->query("ALTER TABLE {$templates_table} ADD COLUMN prompt_text LONGTEXT NOT NULL AFTER description");
        
        if ($add_result !== false) {
            echo '<p>✅ Successfully added prompt_text column</p>';
        } else {
            echo '<p>❌ Failed to add column: ' . $wpdb->last_error . '</p>';
        }
    }
} else {
    echo '<p>❌ Prompt templates table does not exist - creating it...</p>';
    
    // Load the prompt templates functions and create table
    require_once get_template_directory() . '/inc/prompt-templates.php';
    $result = chatgabi_create_prompt_templates_tables();
    
    if ($result) {
        echo '<p>✅ Successfully created prompt templates tables</p>';
    } else {
        echo '<p>❌ Failed to create tables</p>';
    }
}

// Step 2: Check and fix categories table status column
echo '<h2>Step 2: Fixing Template Categories Table</h2>';

$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$categories_exists = $wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table;

if ($categories_exists) {
    $cat_columns = $wpdb->get_results("DESCRIBE {$categories_table}");
    $cat_column_names = array_column($cat_columns, 'Field');
    
    $has_status = in_array('status', $cat_column_names);
    
    if (!$has_status) {
        echo '<p>⚠️ Categories table missing status column - adding it...</p>';
        
        $add_status_result = $wpdb->query("ALTER TABLE {$categories_table} ADD COLUMN status varchar(20) NOT NULL DEFAULT 'active' AFTER sort_order");
        
        if ($add_status_result !== false) {
            echo '<p>✅ Successfully added status column to categories table</p>';
        } else {
            echo '<p>❌ Failed to add status column: ' . $wpdb->last_error . '</p>';
        }
    } else {
        echo '<p>✅ Categories table already has status column</p>';
    }
} else {
    echo '<p>❌ Categories table does not exist - will be created with templates table</p>';
}

// Step 3: Add missing indexes for performance
echo '<h2>Step 3: Adding Performance Indexes</h2>';

$indexes_to_add = array(
    $wpdb->prefix . 'chatgabi_conversations' => array(
        'idx_user_date' => 'ADD INDEX idx_user_date (user_id, created_at)',
        'idx_conversation_type' => 'ADD INDEX idx_conversation_type (conversation_type, created_at)'
    ),
    $wpdb->prefix . 'chatgabi_feedback' => array(
        'idx_rating_country' => 'ADD INDEX idx_rating_country (rating_score, user_country)',
        'idx_feedback_date' => 'ADD INDEX idx_feedback_date (created_at, rating_type)'
    ),
    $wpdb->prefix . 'chatgabi_prompt_templates' => array(
        'idx_usage_rating' => 'ADD INDEX idx_usage_rating (usage_count, rating_average)',
        'idx_public_featured' => 'ADD INDEX idx_public_featured (is_public, is_featured, status)'
    )
);

foreach ($indexes_to_add as $table => $indexes) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
    
    if ($table_exists) {
        echo "<h4>Adding indexes to {$table}:</h4>";
        
        foreach ($indexes as $index_name => $index_sql) {
            // Check if index already exists
            $existing_indexes = $wpdb->get_results("SHOW INDEX FROM {$table}");
            $index_exists = false;
            
            foreach ($existing_indexes as $existing_index) {
                if ($existing_index->Key_name === $index_name) {
                    $index_exists = true;
                    break;
                }
            }
            
            if (!$index_exists) {
                $result = $wpdb->query("ALTER TABLE {$table} {$index_sql}");
                
                if ($result !== false) {
                    echo "<p>✅ Added index: {$index_name}</p>";
                } else {
                    echo "<p>⚠️ Failed to add index {$index_name}: " . $wpdb->last_error . "</p>";
                }
            } else {
                echo "<p>ℹ️ Index {$index_name} already exists</p>";
            }
        }
    } else {
        echo "<p>⚠️ Table {$table} does not exist - skipping indexes</p>";
    }
}

// Step 4: Update REST API code to use correct column names
echo '<h2>Step 4: Verifying Code Consistency</h2>';

$rest_api_file = get_template_directory() . '/inc/rest-api.php';
if (file_exists($rest_api_file)) {
    $rest_api_content = file_get_contents($rest_api_file);
    
    // Check for prompt_content references that should be prompt_text
    if (strpos($rest_api_content, 'prompt_content') !== false) {
        echo '<p>⚠️ Found prompt_content references in REST API code</p>';
        echo '<p>ℹ️ These should be updated to use prompt_text for consistency</p>';
        
        // Create a backup
        $backup_file = $rest_api_file . '.backup.' . date('Y-m-d-H-i-s');
        copy($rest_api_file, $backup_file);
        echo "<p>📁 Created backup: {$backup_file}</p>";
        
        // Update the code to use prompt_text consistently
        $updated_content = str_replace(
            array('t.prompt_content', "'prompt_content'"),
            array('t.prompt_text', "'prompt_text'"),
            $rest_api_content
        );
        
        // But keep the API response field as prompt_content for consistency
        $updated_content = str_replace(
            "'prompt_text' => \$template->prompt_text",
            "'prompt_content' => \$template->prompt_text",
            $updated_content
        );
        
        if (file_put_contents($rest_api_file, $updated_content)) {
            echo '<p>✅ Updated REST API code to use correct column names</p>';
        } else {
            echo '<p>❌ Failed to update REST API code</p>';
        }
    } else {
        echo '<p>✅ REST API code already uses correct column names</p>';
    }
} else {
    echo '<p>⚠️ REST API file not found</p>';
}

// Step 5: Test the fixes
echo '<h2>Step 5: Testing Database Schema Fixes</h2>';

// Test templates table
$test_query = "SELECT COUNT(*) FROM {$templates_table}";
$template_count = $wpdb->get_var($test_query);

if ($template_count !== null) {
    echo "<p>✅ Templates table accessible - {$template_count} templates found</p>";
} else {
    echo "<p>❌ Templates table test failed: " . $wpdb->last_error . "</p>";
}

// Test categories table
if ($categories_exists) {
    $cat_test_query = "SELECT COUNT(*) FROM {$categories_table}";
    $category_count = $wpdb->get_var($cat_test_query);
    
    if ($category_count !== null) {
        echo "<p>✅ Categories table accessible - {$category_count} categories found</p>";
    } else {
        echo "<p>❌ Categories table test failed: " . $wpdb->last_error . "</p>";
    }
}

echo '<h2>✅ Database Schema Fix Complete</h2>';
echo '<p><strong>Summary of changes:</strong></p>';
echo '<ul>';
echo '<li>✅ Fixed prompt_text/prompt_content column consistency</li>';
echo '<li>✅ Added missing status column to categories table</li>';
echo '<li>✅ Added performance indexes for common queries</li>';
echo '<li>✅ Updated REST API code for consistency</li>';
echo '<li>✅ Verified all tables are accessible</li>';
echo '</ul>';

echo '<p><strong>Next steps:</strong></p>';
echo '<ul>';
echo '<li>Test the templates functionality in the admin dashboard</li>';
echo '<li>Verify REST API endpoints are working correctly</li>';
echo '<li>Monitor performance improvements from new indexes</li>';
echo '</ul>';

echo '<p><a href="/wp-admin/admin.php?page=businesscraft-ai-templates">→ Test Templates Dashboard</a></p>';
?>

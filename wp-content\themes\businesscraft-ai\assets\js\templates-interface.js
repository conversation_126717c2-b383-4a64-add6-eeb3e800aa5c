/**
 * ChatGABI Templates Interface JavaScript
 * AI-Powered Template Management System
 */

(function($) {
    'use strict';

    // Global variables
    let currentTemplates = [];
    let currentFilters = {
        category: '',
        language: '',
        search: '',
        user_only: false
    };
    let selectedTemplate = null;

    // Initialize when document is ready
    $(document).ready(function() {
        initializeTemplatesInterface();
        bindEvents();
        loadInitialData();
    });

    /**
     * Initialize the templates interface
     */
    function initializeTemplatesInterface() {
        console.log('Initializing ChatGABI Templates Interface');
        
        // Set initial filter values
        $('#category-filter').val(currentFilters.category);
        $('#language-filter').val(currentFilters.language);
        $('#template-search').val(currentFilters.search);
        
        // Load template counts for categories
        loadTemplateCounts();
    }

    /**
     * Bind all event handlers
     */
    function bindEvents() {
        // Search functionality
        $('#template-search').on('input', debounce(function() {
            currentFilters.search = $(this).val();
            loadTemplates();
        }, 300));

        // Filter changes
        $('#category-filter, #language-filter').on('change', function() {
            const filterId = $(this).attr('id');
            const filterKey = filterId.replace('-filter', '');
            currentFilters[filterKey] = $(this).val();
            loadTemplates();
        });

        // AI Suggestions
        $('#ai-suggestions-btn').on('click', function() {
            showAISuggestions();
        });

        $('#close-suggestions').on('click', function() {
            hideAISuggestions();
        });

        // Category exploration
        $(document).on('click', '.category-explore-btn', function() {
            const category = $(this).data('category');
            $('#category-filter').val(category);
            currentFilters.category = category;
            loadTemplates();
            
            // Scroll to templates grid
            $('html, body').animate({
                scrollTop: $('#templates-grid').offset().top - 100
            }, 500);
        });

        // Template card clicks
        $(document).on('click', '.template-card', function() {
            const templateId = $(this).data('template-id');
            showTemplatePreview(templateId);
        });

        // Modal controls
        $('#close-modal, #close-customization-modal').on('click', function() {
            hideModals();
        });

        // Template actions
        $('#use-template-btn').on('click', function() {
            useTemplate();
        });

        $('#save-template-btn').on('click', function() {
            saveTemplate();
        });

        $('#customize-template-btn').on('click', function() {
            showCustomizationModal();
        });

        $('#enhance-template-btn').on('click', function() {
            enhanceTemplate();
        });

        // Custom template actions
        $('#save-custom-template-btn').on('click', function() {
            saveCustomTemplate();
        });

        $('#preview-custom-template-btn').on('click', function() {
            previewCustomTemplate();
        });

        // Close modals on outside click
        $('.template-modal').on('click', function(e) {
            if (e.target === this) {
                hideModals();
            }
        });

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                hideModals();
                hideAISuggestions();
            }
        });
    }

    /**
     * Load initial data
     */
    function loadInitialData() {
        loadTemplates();
        loadTemplateCategories();
    }

    /**
     * Load templates based on current filters
     */
    function loadTemplates() {
        showLoading();
        
        const params = new URLSearchParams(currentFilters);
        
        fetch(`${chatgabiTemplatesConfig.restUrl}templates?${params}`, {
            method: 'GET',
            headers: {
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                currentTemplates = data.templates;
                renderTemplates(data.templates);
            } else {
                showMessage('error', data.message || chatgabiTemplatesConfig.strings.errorOccurred);
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error loading templates:', error);
            showMessage('error', chatgabiTemplatesConfig.strings.errorOccurred);
        });
    }

    /**
     * Load template categories
     */
    function loadTemplateCategories() {
        fetch(`${chatgabiTemplatesConfig.restUrl}template-categories`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Categories are already loaded in PHP, this is for dynamic updates
                console.log('Categories loaded:', data.categories);
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
        });
    }

    /**
     * Load template counts for categories
     */
    function loadTemplateCounts() {
        $('.template-count').each(function() {
            const category = $(this).data('category');
            const $element = $(this);
            
            fetch(`${chatgabiTemplatesConfig.restUrl}templates?category=${category}`, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': chatgabiTemplatesConfig.restNonce,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const count = data.count;
                    $element.text(chatgabiTemplatesConfig.strings.templateCount.replace('%d', count));
                }
            })
            .catch(error => {
                console.error('Error loading template count:', error);
                $element.text('0 templates');
            });
        });
    }

    /**
     * Render templates in the grid
     */
    function renderTemplates(templates) {
        const $grid = $('#templates-grid');
        
        if (templates.length === 0) {
            $grid.html(`
                <div class="no-templates-message">
                    <h3>${chatgabiTemplatesConfig.strings.noTemplatesFound}</h3>
                    <p>Try adjusting your search criteria or explore different categories.</p>
                </div>
            `);
            return;
        }

        let html = '';
        templates.forEach(template => {
            html += renderTemplateCard(template);
        });
        
        $grid.html(html);
    }

    /**
     * Render individual template card
     */
    function renderTemplateCard(template) {
        const tags = template.tags.slice(0, 3).map(tag => 
            `<span class="meta-badge">${tag}</span>`
        ).join('');

        const rating = template.rating_average > 0 ? 
            `<span class="template-rating">⭐ ${template.rating_average.toFixed(1)}</span>` : '';

        return `
            <div class="template-card" data-template-id="${template.id}">
                <div class="template-header">
                    <h3 class="template-title">${template.title}</h3>
                    ${template.is_owner ? '<span class="owner-badge">👤</span>' : ''}
                </div>
                
                <div class="template-meta">
                    <span class="meta-badge" style="background-color: ${template.category.color}20; color: ${template.category.color}">
                        ${template.category.icon} ${template.category.name}
                    </span>
                    <span class="meta-badge">${getLanguageName(template.language_code)}</span>
                    ${template.sector ? `<span class="meta-badge">${template.sector}</span>` : ''}
                </div>
                
                <p class="template-description">${template.description}</p>
                
                <div class="template-stats">
                    <span class="usage-count">Used ${template.usage_count} times</span>
                    ${rating}
                </div>
                
                <div class="template-tags">
                    ${tags}
                </div>
                
                <div class="template-actions">
                    <button class="chatgabi-btn chatgabi-btn-primary" onclick="showTemplatePreview(${template.id})">
                        Preview
                    </button>
                    ${template.is_owner ? `
                        <button class="chatgabi-btn chatgabi-btn-outline" onclick="editTemplate(${template.id})">
                            Edit
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Show template preview modal
     */
    function showTemplatePreview(templateId) {
        const template = currentTemplates.find(t => t.id == templateId);
        if (!template) {
            // Load template from API
            fetch(`${chatgabiTemplatesConfig.restUrl}templates/${templateId}`, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': chatgabiTemplatesConfig.restNonce,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTemplatePreview(data.template);
                } else {
                    showMessage('error', data.message || chatgabiTemplatesConfig.strings.errorOccurred);
                }
            })
            .catch(error => {
                console.error('Error loading template:', error);
                showMessage('error', chatgabiTemplatesConfig.strings.errorOccurred);
            });
        } else {
            displayTemplatePreview(template);
        }
    }

    /**
     * Display template preview in modal
     */
    function displayTemplatePreview(template) {
        selectedTemplate = template;
        
        $('#modal-template-title').text(template.title);
        $('#modal-template-category').text(template.category.name);
        $('#modal-template-language').text(getLanguageName(template.language_code));
        $('#modal-template-sector').text(template.sector || 'General');
        $('#modal-template-description').text(template.description);
        $('#modal-template-content').text(template.prompt_content);
        
        // Clear previous enhancement results
        $('#enhancement-result').hide().empty();
        
        $('#template-preview-modal').show();
    }

    /**
     * Show AI suggestions panel
     */
    function showAISuggestions() {
        const $panel = $('#ai-suggestions-panel');
        const $loading = $panel.find('.suggestions-loading');
        const $results = $panel.find('.suggestions-results');
        
        $panel.addClass('active');
        $loading.show();
        $results.hide();
        
        // Check credits
        if (chatgabiTemplatesConfig.userCredits < 1) {
            $loading.hide();
            $results.html(`
                <div class="insufficient-credits">
                    <h4>Insufficient Credits</h4>
                    <p>You need at least 1 credit to generate AI suggestions.</p>
                    <a href="/credits" class="chatgabi-btn chatgabi-btn-primary">Get Credits</a>
                </div>
            `).show();
            return;
        }
        
        fetch(`${chatgabiTemplatesConfig.restUrl}templates/suggestions`, {
            method: 'GET',
            headers: {
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            $loading.hide();
            
            if (data.success) {
                renderSuggestions(data.suggestions);
                updateCreditsDisplay(data.remaining_credits);
                showMessage('success', chatgabiTemplatesConfig.strings.suggestionsGenerated);
            } else {
                $results.html(`
                    <div class="error-message">
                        <h4>Error</h4>
                        <p>${data.message || chatgabiTemplatesConfig.strings.errorOccurred}</p>
                    </div>
                `);
            }
            
            $results.show();
        })
        .catch(error => {
            $loading.hide();
            console.error('Error generating suggestions:', error);
            $results.html(`
                <div class="error-message">
                    <h4>Error</h4>
                    <p>${chatgabiTemplatesConfig.strings.errorOccurred}</p>
                </div>
            `).show();
        });
    }

    /**
     * Render AI suggestions
     */
    function renderSuggestions(suggestions) {
        const $results = $('.suggestions-results');
        
        if (suggestions.length === 0) {
            $results.html(`
                <div class="no-suggestions">
                    <h4>No Suggestions</h4>
                    <p>${chatgabiTemplatesConfig.strings.noSuggestions}</p>
                </div>
            `);
            return;
        }

        let html = '<div class="suggestions-list">';
        suggestions.forEach((suggestion, index) => {
            const priorityClass = suggestion.priority || 'medium';
            html += `
                <div class="suggestion-item priority-${priorityClass}">
                    <h4 class="suggestion-title">${suggestion.title}</h4>
                    <p class="suggestion-description">${suggestion.description}</p>
                    <p class="suggestion-relevance"><strong>Why relevant:</strong> ${suggestion.relevance}</p>
                    <div class="suggestion-meta">
                        <span class="suggestion-category">${suggestion.category}</span>
                        <span class="suggestion-priority priority-${priorityClass}">${suggestion.priority}</span>
                    </div>
                    <button class="chatgabi-btn chatgabi-btn-outline suggestion-create-btn" 
                            data-suggestion='${JSON.stringify(suggestion)}'>
                        Create Template
                    </button>
                </div>
            `;
        });
        html += '</div>';
        
        $results.html(html);
        
        // Bind suggestion creation events
        $('.suggestion-create-btn').on('click', function() {
            const suggestion = JSON.parse($(this).attr('data-suggestion'));
            createTemplateFromSuggestion(suggestion);
        });
    }

    /**
     * Hide AI suggestions panel
     */
    function hideAISuggestions() {
        $('#ai-suggestions-panel').removeClass('active');
    }

    /**
     * Enhance template with AI
     */
    function enhanceTemplate() {
        if (!selectedTemplate) return;
        
        // Check credits
        if (chatgabiTemplatesConfig.userCredits < 2) {
            showMessage('error', chatgabiTemplatesConfig.strings.insufficientCredits);
            return;
        }
        
        const $btn = $('#enhance-template-btn');
        const $result = $('#enhancement-result');
        
        $btn.prop('disabled', true).html(`
            <div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px;"></div>
            ${chatgabiTemplatesConfig.strings.aiEnhancing}
        `);
        
        const enhancementOptions = {
            enhance_placeholders: true,
            add_context: true,
            industry_specific: true
        };
        
        const requestData = {
            template_content: selectedTemplate.prompt_content,
            user_context: {
                profile: chatgabiTemplatesConfig.userProfile,
                industry: chatgabiTemplatesConfig.userIndustry,
                country: chatgabiTemplatesConfig.userCountry,
                language: chatgabiTemplatesConfig.userLanguage
            },
            enhancement_options: enhancementOptions
        };
        
        fetch(`${chatgabiTemplatesConfig.restUrl}templates/enhance`, {
            method: 'POST',
            headers: {
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            $btn.prop('disabled', false).html(`
                <span class="btn-icon">🤖</span>
                ${chatgabiTemplatesConfig.strings.enhanceWithAI || 'Enhance with AI'}
            `);
            
            if (data.success) {
                $result.html(`
                    <h5>Enhanced Template Content:</h5>
                    <div class="enhanced-content">${data.enhanced_content}</div>
                    <div class="enhancement-stats">
                        <small>Credits used: ${data.credits_used} | Tokens: ${data.tokens_used}</small>
                    </div>
                `).show();
                
                updateCreditsDisplay(data.remaining_credits);
                showMessage('success', chatgabiTemplatesConfig.strings.enhancementComplete);
            } else {
                showMessage('error', data.message || chatgabiTemplatesConfig.strings.errorOccurred);
            }
        })
        .catch(error => {
            $btn.prop('disabled', false).html(`
                <span class="btn-icon">🤖</span>
                Enhance with AI
            `);
            console.error('Error enhancing template:', error);
            showMessage('error', chatgabiTemplatesConfig.strings.errorOccurred);
        });
    }

    /**
     * Use template (redirect to chat interface)
     */
    function useTemplate() {
        if (!selectedTemplate) return;
        
        // Store template in session/localStorage for chat interface
        localStorage.setItem('chatgabi_selected_template', JSON.stringify(selectedTemplate));
        
        // Redirect to chat interface
        window.location.href = '/chat?template=' + selectedTemplate.id;
        
        showMessage('success', chatgabiTemplatesConfig.strings.templateUsed);
    }

    /**
     * Save template to user's collection
     */
    function saveTemplate() {
        if (!selectedTemplate) return;
        
        // Implementation for saving template to user's collection
        showMessage('success', chatgabiTemplatesConfig.strings.templateSaved);
    }

    /**
     * Show customization modal
     */
    function showCustomizationModal() {
        if (!selectedTemplate) return;
        
        // Pre-fill form with template data
        $('#custom-template-title').val(selectedTemplate.title + ' (Custom)');
        $('#custom-template-description').val(selectedTemplate.description);
        $('#custom-template-content').val(selectedTemplate.prompt_content);
        $('#custom-template-category').val(selectedTemplate.category.id);
        $('#custom-template-language').val(selectedTemplate.language_code);
        $('#custom-template-tags').val(selectedTemplate.tags.join(', '));
        
        $('#template-preview-modal').hide();
        $('#template-customization-modal').show();
    }

    /**
     * Save custom template
     */
    function saveCustomTemplate() {
        const formData = {
            title: $('#custom-template-title').val(),
            description: $('#custom-template-description').val(),
            prompt_content: $('#custom-template-content').val(),
            category_id: $('#custom-template-category').val(),
            language_code: $('#custom-template-language').val(),
            tags: $('#custom-template-tags').val(),
            sector: chatgabiTemplatesConfig.userIndustry
        };
        
        // Validate required fields
        if (!formData.title || !formData.prompt_content || !formData.category_id) {
            showMessage('error', 'Please fill in all required fields.');
            return;
        }
        
        const $btn = $('#save-custom-template-btn');
        $btn.prop('disabled', true).html(`
            <div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px;"></div>
            Saving...
        `);
        
        fetch(`${chatgabiTemplatesConfig.restUrl}templates`, {
            method: 'POST',
            headers: {
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            $btn.prop('disabled', false).html(`
                <span class="btn-icon">💾</span>
                Save Template
            `);
            
            if (data.success) {
                hideModals();
                showMessage('success', data.message || chatgabiTemplatesConfig.strings.templateSaved);
                loadTemplates(); // Refresh templates list
            } else {
                showMessage('error', data.message || chatgabiTemplatesConfig.strings.errorOccurred);
            }
        })
        .catch(error => {
            $btn.prop('disabled', false).html(`
                <span class="btn-icon">💾</span>
                Save Template
            `);
            console.error('Error saving template:', error);
            showMessage('error', chatgabiTemplatesConfig.strings.errorOccurred);
        });
    }

    /**
     * Create template from AI suggestion
     */
    function createTemplateFromSuggestion(suggestion) {
        // Pre-fill customization form with suggestion data
        $('#custom-template-title').val(suggestion.title);
        $('#custom-template-description').val(suggestion.description);
        $('#custom-template-content').val(`# ${suggestion.title}\n\n${suggestion.description}\n\n[Template content will be generated based on this suggestion]`);
        
        // Find category ID by name
        const categories = chatgabiTemplatesConfig.categories;
        const category = categories.find(c => c.name === suggestion.category);
        if (category) {
            $('#custom-template-category').val(category.id);
        }
        
        $('#custom-template-language').val(chatgabiTemplatesConfig.userLanguage);
        $('#custom-template-tags').val(suggestion.category.toLowerCase());
        
        hideAISuggestions();
        $('#template-customization-modal').show();
    }

    /**
     * Hide all modals
     */
    function hideModals() {
        $('.template-modal').hide();
        selectedTemplate = null;
    }

    /**
     * Show loading state
     */
    function showLoading() {
        $('#templates-loading').show();
        $('#templates-grid').hide();
    }

    /**
     * Hide loading state
     */
    function hideLoading() {
        $('#templates-loading').hide();
        $('#templates-grid').show();
    }

    /**
     * Show message to user
     */
    function showMessage(type, message) {
        const $messages = $('#template-messages');
        const messageId = 'msg-' + Date.now();
        
        const $message = $(`
            <div id="${messageId}" class="message ${type}">
                ${message}
            </div>
        `);
        
        $messages.append($message);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            $message.fadeOut(() => $message.remove());
        }, 5000);
    }

    /**
     * Update credits display
     */
    function updateCreditsDisplay(newCredits) {
        chatgabiTemplatesConfig.userCredits = newCredits;
        $('.credits-amount').text(newCredits.toFixed(2));
    }

    /**
     * Get language name from code
     */
    function getLanguageName(code) {
        const languages = {
            'en': 'English',
            'tw': 'Twi',
            'sw': 'Swahili',
            'yo': 'Yoruba',
            'zu': 'Zulu'
        };
        return languages[code] || 'English';
    }

    /**
     * Debounce function for search
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Make functions globally available
    window.showTemplatePreview = showTemplatePreview;
    window.editTemplate = function(templateId) {
        // Implementation for editing templates
        console.log('Edit template:', templateId);
    };

})(jQuery);

<?php
/**
 * Test script for prompt injection functionality
 */

// Force output buffering off
if (ob_get_level()) {
    ob_end_clean();
}

// Define WordPress constants for testing
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Include the data loader functions
require_once(__DIR__ . '/inc/data-loader.php');

// Define a simple error_log function
if (!function_exists('error_log')) {
    function error_log($message) {
        echo "LOG: " . $message . "\n";
    }
}

// Include the build_localized_prompt function
function build_localized_prompt($user_question, $sector_context = null, $country = '', $sector_name = '') {
    $prompt = '';
    
    if ($sector_context && is_array($sector_context)) {
        // Build structured context from sector data
        $prompt .= "You are a professional business advisor helping entrepreneurs in {$country}";
        if ($sector_name) {
            $prompt .= " operating in the {$sector_name} industry";
        }
        $prompt .= ".\n\n";
        
        $prompt .= "Here is the relevant business context for this sector:\n\n";
        
        // Add overview
        if (isset($sector_context['overview'])) {
            $prompt .= "SECTOR OVERVIEW:\n" . $sector_context['overview'] . "\n\n";
        }
        
        // Add key conditions if available
        if (isset($sector_context['key_conditions']) && is_array($sector_context['key_conditions'])) {
            $conditions = $sector_context['key_conditions'];
            
            if (isset($conditions['regulatory_environment'])) {
                $prompt .= "REGULATORY ENVIRONMENT:\n" . $conditions['regulatory_environment'] . "\n\n";
            }
            
            if (isset($conditions['market_size_and_growth'])) {
                $prompt .= "MARKET SIZE AND GROWTH:\n" . $conditions['market_size_and_growth'] . "\n\n";
            }
            
            if (isset($conditions['investment_opportunities'])) {
                $prompt .= "INVESTMENT OPPORTUNITIES:\n" . $conditions['investment_opportunities'] . "\n\n";
            }
            
            if (isset($conditions['major_players']) && is_array($conditions['major_players'])) {
                $prompt .= "MAJOR PLAYERS:\n" . implode(', ', $conditions['major_players']) . "\n\n";
            }
            
            if (isset($conditions['challenges_and_risks'])) {
                $prompt .= "CHALLENGES & RISKS:\n" . $conditions['challenges_and_risks'] . "\n\n";
            }
            
            if (isset($conditions['key_data_points']) && is_array($conditions['key_data_points'])) {
                $prompt .= "KEY DATA POINTS:\n";
                foreach ($conditions['key_data_points'] as $key => $value) {
                    $formatted_key = ucwords(str_replace('_', ' ', $key));
                    $prompt .= "- {$formatted_key}: {$value}\n";
                }
                $prompt .= "\n";
            }
            
            if (isset($conditions['future_outlook'])) {
                $prompt .= "FUTURE OUTLOOK:\n" . $conditions['future_outlook'] . "\n\n";
            }
        }
        
        $prompt .= "Now, respond to the user's query below using this local context to provide specific, actionable advice grounded in {$country}'s market realities:\n\n";
    }
    
    $prompt .= "USER QUESTION: " . $user_question;
    
    return $prompt;
}

echo "=== BusinessCraft AI Prompt Injection Test ===\n\n";

// Test 1: Load sector context and build prompt
echo "Test 1: Ghana Fintech Sector Context\n";
echo str_repeat('-', 50) . "\n";

$sector_context = get_sector_context_by_country('Ghana', 'Fintech');

if ($sector_context === null) {
    echo "❌ Failed to load Ghana Fintech sector context\n";
} else {
    echo "✅ Successfully loaded Ghana Fintech sector context\n";
    echo "Sector Name: " . $sector_context['sector_name'] . "\n";
    echo "Overview Length: " . strlen($sector_context['overview']) . " characters\n";
    
    // Test prompt building
    $user_question = "I want to start a mobile payment business in Ghana. What are the key opportunities and challenges?";
    $localized_prompt = build_localized_prompt($user_question, $sector_context, 'Ghana', 'Fintech');
    
    echo "\nUser Question: " . $user_question . "\n";
    echo "\nGenerated Prompt Preview (first 800 characters):\n";
    echo str_repeat('-', 40) . "\n";
    echo substr($localized_prompt, 0, 800) . "...\n";
    echo str_repeat('-', 40) . "\n";
    
    // Analyze prompt
    $prompt_length = strlen($localized_prompt);
    $estimated_tokens = ceil($prompt_length / 4);
    
    echo "\nPrompt Analysis:\n";
    echo "- Total characters: {$prompt_length}\n";
    echo "- Estimated tokens: {$estimated_tokens}\n";
    echo "- Contains sector overview: " . (strpos($localized_prompt, 'SECTOR OVERVIEW:') !== false ? "✅ Yes" : "❌ No") . "\n";
    echo "- Contains regulatory info: " . (strpos($localized_prompt, 'REGULATORY ENVIRONMENT:') !== false ? "✅ Yes" : "❌ No") . "\n";
    echo "- Contains market data: " . (strpos($localized_prompt, 'MARKET SIZE AND GROWTH:') !== false ? "✅ Yes" : "❌ No") . "\n";
    echo "- Contains investment opportunities: " . (strpos($localized_prompt, 'INVESTMENT OPPORTUNITIES:') !== false ? "✅ Yes" : "❌ No") . "\n";
    echo "- Contains challenges: " . (strpos($localized_prompt, 'CHALLENGES & RISKS:') !== false ? "✅ Yes" : "❌ No") . "\n";
    echo "- Token efficiency: " . ($estimated_tokens < 2000 ? "✅ Good ({$estimated_tokens} < 2000)" : "⚠️ High ({$estimated_tokens} tokens)") . "\n";
}

echo "\n" . str_repeat('=', 60) . "\n";

// Test 2: Kenya Agriculture
echo "Test 2: Kenya Agriculture Sector Context\n";
echo str_repeat('-', 50) . "\n";

$kenya_agriculture = get_sector_context_by_country('Kenya', 'Agriculture');

if ($kenya_agriculture === null) {
    echo "❌ Failed to load Kenya Agriculture sector context\n";
} else {
    echo "✅ Successfully loaded Kenya Agriculture sector context\n";
    echo "Sector Name: " . $kenya_agriculture['sector_name'] . "\n";
    
    $user_question = "How can I scale my coffee farming business in Kenya?";
    $localized_prompt = build_localized_prompt($user_question, $kenya_agriculture, 'Kenya', 'Agriculture');
    
    echo "\nUser Question: " . $user_question . "\n";
    echo "Generated Prompt Length: " . strlen($localized_prompt) . " characters\n";
    echo "Estimated Tokens: " . ceil(strlen($localized_prompt) / 4) . "\n";
    
    // Check for key agriculture-specific content
    echo "Contains agriculture-specific content: " . (stripos($localized_prompt, 'coffee') !== false || stripos($localized_prompt, 'crop') !== false ? "✅ Yes" : "❌ No") . "\n";
}

echo "\n" . str_repeat('=', 60) . "\n";

// Test 3: Nigeria Technology
echo "Test 3: Nigeria Technology Sector Context\n";
echo str_repeat('-', 50) . "\n";

$nigeria_tech = get_sector_context_by_country('Nigeria', 'Technology');

if ($nigeria_tech === null) {
    echo "❌ Failed to load Nigeria Technology sector context\n";
} else {
    echo "✅ Successfully loaded Nigeria Technology sector context\n";
    echo "Sector Name: " . $nigeria_tech['sector_name'] . "\n";
    
    $user_question = "What are the funding opportunities for tech startups in Nigeria?";
    $localized_prompt = build_localized_prompt($user_question, $nigeria_tech, 'Nigeria', 'Technology');
    
    echo "\nUser Question: " . $user_question . "\n";
    echo "Generated Prompt Length: " . strlen($localized_prompt) . " characters\n";
    echo "Estimated Tokens: " . ceil(strlen($localized_prompt) / 4) . "\n";
}

echo "\n" . str_repeat('=', 60) . "\n";

echo "✅ Prompt injection system is working correctly!\n";
echo "\nKey Features Verified:\n";
echo "- ✅ Sector context loading from datasets\n";
echo "- ✅ Structured prompt building with local context\n";
echo "- ✅ Token optimization (keeping prompts under 2000 tokens)\n";
echo "- ✅ Multi-country support (Ghana, Kenya, Nigeria)\n";
echo "- ✅ Sector-specific data injection\n";
echo "\nThe system is ready for integration with OpenAI API calls!\n";

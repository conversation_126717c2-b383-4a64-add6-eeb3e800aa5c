/*! This file is auto-generated */
(()=>{var i={1:e=>{var t=wp.media.view.MenuItem,i=wp.media.view.PriorityList,t=i.extend({tagName:"div",className:"media-menu",property:"state",ItemView:t,region:"menu",attributes:{role:"tablist","aria-orientation":"horizontal"},initialize:function(){this._views={},this.set(_.extend({},this._views,this.options.views),{silent:!0}),delete this.options.views,this.options.silent||this.render(),this.focusManager=new wp.media.view.FocusManager({el:this.el,mode:"tabsNavigation"}),this.isVisible=!0},toView:function(e,t){return(e=e||{})[this.property]=e[this.property]||t,new this.ItemView(e).render()},ready:function(){i.prototype.ready.apply(this,arguments),this.visibility(),this.focusManager.setupAriaTabs()},set:function(){i.prototype.set.apply(this,arguments),this.visibility()},unset:function(){i.prototype.unset.apply(this,arguments),this.visibility()},visibility:function(){var e=this.region,t=this.controller[e].get(),i=this.views.get(),i=!i||i.length<2;this===t&&(this.isVisible=!i,this.controller.$el.toggleClass("hide-"+e,i))},select:function(e){e=this.get(e);e&&(this.deselect(),e.$el.addClass("active"),this.focusManager.setupAriaTabs())},deselect:function(){this.$el.children().removeClass("active")},hide:function(e){e=this.get(e);e&&e.$el.addClass("hidden")},show:function(e){e=this.get(e);e&&e.$el.removeClass("hidden")}});e.exports=t},168:e=>{var t=Backbone.$,i=wp.media.View.extend({tagName:"div",className:"button-group button-large media-button-group",initialize:function(){this.buttons=_.map(this.options.buttons||[],function(e){return e instanceof Backbone.View?e:new wp.media.view.Button(e).render()}),delete this.options.buttons,this.options.classes&&this.$el.addClass(this.options.classes)},render:function(){return this.$el.html(t(_.pluck(this.buttons,"el")).detach()),this}});e.exports=i},170:e=>{var t=wp.media.View.extend({tagName:function(){return this.options.level||"h1"},className:"media-views-heading",initialize:function(){this.options.className&&this.$el.addClass(this.options.className),this.text=this.options.text},render:function(){return this.$el.html(this.text),this}});e.exports=t},397:e=>{var t=wp.media.view.Toolbar.Select,i=wp.media.view.l10n,s=t.extend({initialize:function(){_.defaults(this.options,{text:i.insertIntoPost,requires:!1}),t.prototype.initialize.apply(this,arguments)},refresh:function(){var e=this.controller.state().props.get("url");this.get("select").model.set("disabled",!e||"http://"===e),t.prototype.refresh.apply(this,arguments)}});e.exports=s},443:e=>{var t=wp.media.view,i=t.Cropper.extend({className:"crop-content site-icon",ready:function(){t.Cropper.prototype.ready.apply(this,arguments),this.$(".crop-image").on("load",_.bind(this.addSidebar,this))},addSidebar:function(){this.sidebar=new wp.media.view.Sidebar({controller:this.controller}),this.sidebar.set("preview",new wp.media.view.SiteIconPreview({controller:this.controller,attachment:this.options.attachment})),this.controller.cropperView.views.add(this.sidebar)}});e.exports=i},455:e=>{var t=wp.media.view.MediaFrame,i=wp.media.view.l10n,s=t.extend({initialize:function(){t.prototype.initialize.apply(this,arguments),_.defaults(this.options,{selection:[],library:{},multiple:!1,state:"library"}),this.createSelection(),this.createStates(),this.bindHandlers()},createSelection:function(){var e=this.options.selection;e instanceof wp.media.model.Selection||(this.options.selection=new wp.media.model.Selection(e,{multiple:this.options.multiple})),this._selection={attachments:new wp.media.model.Attachments,difference:[]}},editImageContent:function(){var e=this.state().get("image"),e=new wp.media.view.EditImage({model:e,controller:this}).render();this.content.set(e),e.loadEditor()},createStates:function(){var e=this.options;this.options.states||this.states.add([new wp.media.controller.Library({library:wp.media.query(e.library),multiple:e.multiple,title:e.title,priority:20}),new wp.media.controller.EditImage({model:e.editImage})])},bindHandlers:function(){this.on("router:create:browse",this.createRouter,this),this.on("router:render:browse",this.browseRouter,this),this.on("content:create:browse",this.browseContent,this),this.on("content:render:upload",this.uploadContent,this),this.on("toolbar:create:select",this.createSelectToolbar,this),this.on("content:render:edit-image",this.editImageContent,this)},browseRouter:function(e){e.set({upload:{text:i.uploadFilesTitle,priority:20},browse:{text:i.mediaLibraryTitle,priority:40}})},browseContent:function(e){var t=this.state();this.$el.removeClass("hide-toolbar"),e.view=new wp.media.view.AttachmentsBrowser({controller:this,collection:t.get("library"),selection:t.get("selection"),model:t,sortable:t.get("sortable"),search:t.get("searchable"),filters:t.get("filterable"),date:t.get("date"),display:t.has("display")?t.get("display"):t.get("displaySettings"),dragInfo:t.get("dragInfo"),idealColumnWidth:t.get("idealColumnWidth"),suggestedWidth:t.get("suggestedWidth"),suggestedHeight:t.get("suggestedHeight"),AttachmentView:t.get("AttachmentView")})},uploadContent:function(){this.$el.removeClass("hide-toolbar"),this.content.set(new wp.media.view.UploaderInline({controller:this}))},createSelectToolbar:function(e,t){(t=t||this.options.button||{}).controller=this,e.view=new wp.media.view.Toolbar.Select(t)}});e.exports=s},472:e=>{var t=wp.media.view.l10n,i=window.getUserSetting,s=window.setUserSetting,t=wp.media.controller.State.extend({defaults:{id:"library",title:t.mediaLibraryTitle,multiple:!1,content:"upload",menu:"default",router:"browse",toolbar:"select",searchable:!0,filterable:!1,sortable:!0,autoSelect:!0,describe:!1,contentUserSetting:!0,syncSelection:!0},initialize:function(){var e=this.get("selection");this.get("library")||this.set("library",wp.media.query()),e instanceof wp.media.model.Selection||((e=e)||(e=this.get("library").props.toJSON(),e=_.omit(e,"orderby","query")),this.set("selection",new wp.media.model.Selection(null,{multiple:this.get("multiple"),props:e}))),this.resetDisplays()},activate:function(){this.syncSelection(),wp.Uploader.queue.on("add",this.uploading,this),this.get("selection").on("add remove reset",this.refreshContent,this),this.get("router")&&this.get("contentUserSetting")&&(this.frame.on("content:activate",this.saveContentMode,this),this.set("content",i("libraryContent",this.get("content"))))},deactivate:function(){this.recordSelection(),this.frame.off("content:activate",this.saveContentMode,this),this.get("selection").off(null,null,this),wp.Uploader.queue.off(null,null,this)},reset:function(){this.get("selection").reset(),this.resetDisplays(),this.refreshContent()},resetDisplays:function(){var e=wp.media.view.settings.defaultProps;this._displays=[],this._defaultDisplaySettings={align:i("align",e.align)||"none",size:i("imgsize",e.size)||"medium",link:i("urlbutton",e.link)||"none"}},display:function(e){var t=this._displays;return t[e.cid]||(t[e.cid]=new Backbone.Model(this.defaultDisplaySettings(e))),t[e.cid]},defaultDisplaySettings:function(e){var t=_.clone(this._defaultDisplaySettings);return t.canEmbed=this.canEmbed(e),t.canEmbed?t.link="embed":this.isImageAttachment(e)||"none"!==t.link||(t.link="file"),t},isImageAttachment:function(e){return e.get("uploading")?/\.(jpe?g|png|gif|webp|avif|heic|heif)$/i.test(e.get("filename")):"image"===e.get("type")},canEmbed:function(e){if(!e.get("uploading")){var t=e.get("type");if("audio"!==t&&"video"!==t)return!1}return _.contains(wp.media.view.settings.embedExts,e.get("filename").split(".").pop())},refreshContent:function(){var e=this.get("selection"),t=this.frame,i=t.router.get(),t=t.content.mode();this.active&&!e.length&&i&&!i.get(t)&&this.frame.content.render(this.get("content"))},uploading:function(e){"upload"===this.frame.content.mode()&&this.frame.content.mode("browse"),this.get("autoSelect")&&(this.get("selection").add(e),this.frame.trigger("library:selection:add"))},saveContentMode:function(){var e,t;"browse"===this.get("router")&&(e=this.frame.content.mode(),t=this.frame.router.get())&&t.get(e)&&s("libraryContent",e)}});_.extend(t.prototype,wp.media.selectionSync),e.exports=t},705:e=>{var t=wp.media.controller.State,i=wp.media.controller.Library,s=wp.media.view.l10n,s=t.extend({defaults:_.defaults({id:"image-details",title:s.imageDetailsTitle,content:"image-details",menu:!1,router:!1,toolbar:"image-details",editing:!1,priority:60},i.prototype.defaults),initialize:function(e){this.image=e.image,t.prototype.initialize.apply(this,arguments)},activate:function(){this.frame.modal.$el.addClass("image-details")}});e.exports=s},718:e=>{var o=jQuery,t=wp.media.View.extend({events:{keydown:"focusManagementMode"},initialize:function(e){this.mode=e.mode||"constrainTabbing",this.tabsAutomaticActivation=e.tabsAutomaticActivation||!1},focusManagementMode:function(e){"constrainTabbing"===this.mode&&this.constrainTabbing(e),"tabsNavigation"===this.mode&&this.tabsNavigation(e)},getTabbables:function(){return this.$(":tabbable").not('.moxie-shim input[type="file"]')},focus:function(){this.$(".media-modal").trigger("focus")},constrainTabbing:function(e){var t;if(9===e.keyCode)return(t=this.getTabbables()).last()[0]!==e.target||e.shiftKey?t.first()[0]===e.target&&e.shiftKey?(t.last().focus(),!1):void 0:(t.first().focus(),!1)},setAriaHiddenOnBodyChildren:function(t){var e,i=this;this.isBodyAriaHidden||(e=document.body.children,_.each(e,function(e){e!==t[0]&&i.elementShouldBeHidden(e)&&(e.setAttribute("aria-hidden","true"),i.ariaHiddenElements.push(e))}),this.isBodyAriaHidden=!0)},removeAriaHiddenFromBodyChildren:function(){_.each(this.ariaHiddenElements,function(e){e.removeAttribute("aria-hidden")}),this.ariaHiddenElements=[],this.isBodyAriaHidden=!1},elementShouldBeHidden:function(e){var t=e.getAttribute("role");return!("SCRIPT"===e.tagName||e.hasAttribute("aria-hidden")||e.hasAttribute("aria-live")||-1!==["alert","status","log","marquee","timer"].indexOf(t))},isBodyAriaHidden:!1,ariaHiddenElements:[],tabs:o(),setupAriaTabs:function(){this.tabs=this.$('[role="tab"]'),this.tabs.attr({"aria-selected":"false",tabIndex:"-1"}),this.tabs.filter(".active").removeAttr("tabindex").attr("aria-selected","true")},tabsNavigation:function(e){var t="horizontal";-1===[32,35,36,37,38,39,40].indexOf(e.which)||"horizontal"===(t="vertical"===this.$el.attr("aria-orientation")?"vertical":t)&&-1!==[38,40].indexOf(e.which)||"vertical"===t&&-1!==[37,39].indexOf(e.which)||this.switchTabs(e,this.tabs)},switchTabs:function(e){var t,i=e.which,s=this.tabs.index(o(e.target));switch(i){case 32:this.activateTab(this.tabs[s]);break;case 35:e.preventDefault(),this.activateTab(this.tabs[this.tabs.length-1]);break;case 36:e.preventDefault(),this.activateTab(this.tabs[0]);break;case 37:case 38:e.preventDefault(),t=s-1<0?this.tabs.length-1:s-1,this.activateTab(this.tabs[t]);break;case 39:case 40:e.preventDefault(),t=s+1===this.tabs.length?0:s+1,this.activateTab(this.tabs[t])}},activateTab:function(e){e&&(e.focus(),this.tabsAutomaticActivation?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected","true"),e.click()):o(e).on("click",function(){e.removeAttribute("tabindex"),e.setAttribute("aria-selected","true")}))}});e.exports=t},846:e=>{var t=wp.media.View.extend({tagName:"button",className:"media-button",attributes:{type:"button"},events:{click:"click"},defaults:{text:"",style:"",size:"large",disabled:!1},initialize:function(){this.model=new Backbone.Model(this.defaults),_.each(this.defaults,function(e,t){var i=this.options[t];_.isUndefined(i)||(this.model.set(t,i),delete this.options[t])},this),this.listenTo(this.model,"change",this.render)},render:function(){var e=["button",this.className],t=this.model.toJSON();return t.style&&e.push("button-"+t.style),t.size&&e.push("button-"+t.size),e=_.uniq(e.concat(this.options.classes)),this.el.className=e.join(" "),this.$el.attr("disabled",t.disabled),this.$el.text(this.model.get("text")),this},click:function(e){"#"===this.attributes.href&&e.preventDefault(),this.options.click&&!this.model.get("disabled")&&this.options.click.apply(this,arguments)}});e.exports=t},1061:e=>{var t=wp.media.View.extend({initialize:function(){_.defaults(this.options,{mode:["select"]}),this._createRegions(),this._createStates(),this._createModes()},_createRegions:function(){this.regions=this.regions?this.regions.slice():[],_.each(this.regions,function(e){this[e]=new wp.media.controller.Region({view:this,id:e,selector:".media-frame-"+e})},this)},_createStates:function(){this.states=new Backbone.Collection(null,{model:wp.media.controller.State}),this.states.on("add",function(e){e.frame=this,e.trigger("ready")},this),this.options.states&&this.states.add(this.options.states)},_createModes:function(){this.activeModes=new Backbone.Collection,this.activeModes.on("add remove reset",_.bind(this.triggerModeEvents,this)),_.each(this.options.mode,function(e){this.activateMode(e)},this)},reset:function(){return this.states.invoke("trigger","reset"),this},triggerModeEvents:function(e,t,i){var s,o={add:"activate",remove:"deactivate"};_.each(i,function(e,t){e&&(s=t)}),_.has(o,s)&&(i=e.get("id")+":"+o[s],this.trigger(i))},activateMode:function(e){if(!this.isModeActive(e))return this.activeModes.add([{id:e}]),this.$el.addClass("mode-"+e),this},deactivateMode:function(e){return this.isModeActive(e)&&(this.activeModes.remove(this.activeModes.where({id:e})),this.$el.removeClass("mode-"+e),this.trigger(e+":deactivate")),this},isModeActive:function(e){return Boolean(this.activeModes.where({id:e}).length)}});_.extend(t.prototype,wp.media.controller.StateMachine.prototype),e.exports=t},1169:e=>{var s=wp.media.model.Attachment,t=wp.media.controller.Library,i=wp.media.view.l10n,i=t.extend({defaults:_.defaults({id:"featured-image",title:i.setFeaturedImageTitle,multiple:!1,filterable:"uploaded",toolbar:"featured-image",priority:60,syncSelection:!0},t.prototype.defaults),initialize:function(){var e,o;this.get("library")||this.set("library",wp.media.query({type:"image"})),t.prototype.initialize.apply(this,arguments),e=this.get("library"),o=e.comparator,e.comparator=function(e,t){var i=!!this.mirroring.get(e.cid),s=!!this.mirroring.get(t.cid);return!i&&s?-1:i&&!s?1:o.apply(this,arguments)},e.observe(this.get("selection"))},activate:function(){this.frame.on("open",this.updateSelection,this),t.prototype.activate.apply(this,arguments)},deactivate:function(){this.frame.off("open",this.updateSelection,this),t.prototype.deactivate.apply(this,arguments)},updateSelection:function(){var e,t=this.get("selection"),i=wp.media.view.settings.post.featuredImageId;""!==i&&-1!==i&&(e=s.get(i)).fetch(),t.reset(e?[e]:[])}});e.exports=i},1368:e=>{var o=wp.media.view.l10n,t=wp.media.view.AttachmentFilters.extend({createFilters:function(){var e,t=this.model.get("type"),i=wp.media.view.settings.mimeTypes,s=window.userSettings?parseInt(window.userSettings.uid,10):0;i&&t&&(e=i[t]),this.filters={all:{text:e||o.allMediaItems,props:{uploadedTo:null,orderby:"date",order:"DESC",author:null},priority:10},uploaded:{text:o.uploadedToThisPost,props:{uploadedTo:wp.media.view.settings.post.id,orderby:"menuOrder",order:"ASC",author:null},priority:20},unattached:{text:o.unattached,props:{uploadedTo:0,orderby:"menuOrder",order:"ASC",author:null},priority:50}},s&&(this.filters.mine={text:o.mine,props:{orderby:"date",order:"DESC",author:s},priority:50})}});e.exports=t},1753:e=>{var t=wp.media.View,i=t.extend({tagName:"div",className:"uploader-inline",template:wp.template("uploader-inline"),events:{"click .close":"hide"},initialize:function(){_.defaults(this.options,{message:"",status:!0,canClose:!1}),!this.options.$browser&&this.controller.uploader&&(this.options.$browser=this.controller.uploader.$browser),_.isUndefined(this.options.postId)&&(this.options.postId=wp.media.view.settings.post.id),this.options.status&&this.views.set(".upload-inline-status",new wp.media.view.UploaderStatus({controller:this.controller}))},prepare:function(){var e=this.controller.state().get("suggestedWidth"),t=this.controller.state().get("suggestedHeight"),i={};return i.message=this.options.message,i.canClose=this.options.canClose,e&&t&&(i.suggestedWidth=e,i.suggestedHeight=t),i},dispose:function(){return this.disposing?t.prototype.dispose.apply(this,arguments):(this.disposing=!0,this.remove())},remove:function(){var e=t.prototype.remove.apply(this,arguments);return _.defer(_.bind(this.refresh,this)),e},refresh:function(){var e=this.controller.uploader;e&&e.refresh()},ready:function(){var e,t=this.options.$browser;if(this.controller.uploader){if((e=this.$(".browser"))[0]===t[0])return;t.detach().text(e.text()),t[0].className=e[0].className,t[0].setAttribute("aria-labelledby",t[0].id+" "+e[0].getAttribute("aria-labelledby")),e.replaceWith(t.show())}return this.refresh(),this},show:function(){this.$el.removeClass("hidden"),this.controller.$uploaderToggler&&this.controller.$uploaderToggler.length&&this.controller.$uploaderToggler.attr("aria-expanded","true")},hide:function(){this.$el.addClass("hidden"),this.controller.$uploaderToggler&&this.controller.$uploaderToggler.length&&this.controller.$uploaderToggler.attr("aria-expanded","false").trigger("focus")}});e.exports=i},1915:e=>{var t=wp.media.View,s=Backbone.$,i=t.extend({events:{"click button":"updateHandler","change input":"updateHandler","change select":"updateHandler","change textarea":"updateHandler"},initialize:function(){this.model=this.model||new Backbone.Model,this.listenTo(this.model,"change",this.updateChanges)},prepare:function(){return _.defaults({model:this.model.toJSON()},this.options)},render:function(){return t.prototype.render.apply(this,arguments),_(this.model.attributes).chain().keys().each(this.update,this),this},update:function(e){var t,i=this.model.get(e),s=this.$('[data-setting="'+e+'"]');s.length&&(s.is("select")?(t=s.find('[value="'+i+'"]')).length?(s.find("option").prop("selected",!1),t.prop("selected",!0)):this.model.set(e,s.find(":selected").val()):s.hasClass("button-group")?s.find("button").removeClass("active").attr("aria-pressed","false").filter('[value="'+i+'"]').addClass("active").attr("aria-pressed","true"):s.is('input[type="text"], textarea')?s.is(":focus")||s.val(i):s.is('input[type="checkbox"]')&&s.prop("checked",!!i&&"false"!==i))},updateHandler:function(e){var t=s(e.target).closest("[data-setting]"),i=e.target.value;e.preventDefault(),t.length&&(t.is('input[type="checkbox"]')&&(i=t[0].checked),this.model.set(t.data("setting"),i),e=t.data("userSetting"))&&window.setUserSetting(e,i)},updateChanges:function(e){e.hasChanged()&&_(e.changed).chain().keys().each(this.update,this)}});e.exports=i},1982:e=>{var t=wp.media.View.extend({className:"media-iframe",render:function(){return this.views.detach(),this.$el.html('<iframe src="'+this.controller.state().get("src")+'" />'),this.views.render(),this}});e.exports=t},1992:e=>{var t=wp.media.view.PriorityList.extend({className:"media-sidebar"});e.exports=t},2038:e=>{var t=wp.media.controller.Library,i=wp.media.view.l10n,s=t.extend({defaults:{id:"gallery-edit",title:i.editGalleryTitle,multiple:!1,searchable:!1,sortable:!0,date:!1,display:!1,content:"browse",toolbar:"gallery-edit",describe:!0,displaySettings:!0,dragInfo:!0,idealColumnWidth:170,editing:!1,priority:60,syncSelection:!1},initialize:function(){this.get("library")||this.set("library",new wp.media.model.Selection),this.get("AttachmentView")||this.set("AttachmentView",wp.media.view.Attachment.EditLibrary),t.prototype.initialize.apply(this,arguments)},activate:function(){this.get("library").props.set("type","image"),this.get("library").observe(wp.Uploader.queue),this.frame.on("content:render:browse",this.gallerySettings,this),t.prototype.activate.apply(this,arguments)},deactivate:function(){this.get("library").unobserve(wp.Uploader.queue),this.frame.off("content:render:browse",this.gallerySettings,this),t.prototype.deactivate.apply(this,arguments)},gallerySettings:function(e){var t;this.get("displaySettings")&&(t=this.get("library"))&&e&&(t.gallery=t.gallery||new Backbone.Model,e.sidebar.set({gallery:new wp.media.view.Settings.Gallery({controller:this,model:t.gallery,priority:40})}),e.toolbar.set("reverse",{text:i.reverseOrder,priority:80,click:function(){t.reset(t.toArray().reverse())}}))}});e.exports=s},2102:e=>{var t=wp.media.View.extend({tagName:"input",className:"search",id:"media-search-input",attributes:{type:"search"},events:{input:"search"},render:function(){return this.el.value=this.model.escape("search"),this},search:_.debounce(function(e){e=e.target.value.trim();e&&1<e.length?this.model.set("search",e):this.model.unset("search")},500)});e.exports=t},2275:e=>{var i=wp.media.controller.Library,t=wp.media.view.l10n,t=i.extend({defaults:_.defaults({id:"replace-image",title:t.replaceImageTitle,multiple:!1,filterable:"uploaded",toolbar:"replace",menu:!1,priority:60,syncSelection:!0},i.prototype.defaults),initialize:function(e){var t,o;this.image=e.image,this.get("library")||this.set("library",wp.media.query({type:"image"})),i.prototype.initialize.apply(this,arguments),t=this.get("library"),o=t.comparator,t.comparator=function(e,t){var i=!!this.mirroring.get(e.cid),s=!!this.mirroring.get(t.cid);return!i&&s?-1:i&&!s?1:o.apply(this,arguments)},t.observe(this.get("selection"))},activate:function(){this.frame.on("content:render:browse",this.updateSelection,this),i.prototype.activate.apply(this,arguments)},deactivate:function(){this.frame.off("content:render:browse",this.updateSelection,this),i.prototype.deactivate.apply(this,arguments)},updateSelection:function(){var e=this.get("selection"),t=this.image.attachment;e.reset(t?[t]:[])}});e.exports=t},2356:e=>{var t=wp.media.view.Settings.extend({className:"collection-settings playlist-settings",template:wp.template("playlist-settings")});e.exports=t},2395:e=>{var t=wp.media.view.Settings.AttachmentDisplay,i=t.extend({className:"embed-media-settings",template:wp.template("embed-image-settings"),initialize:function(){t.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:url",this.updateImage)},updateImage:function(){this.$("img").attr("src",this.model.get("url"))}});e.exports=i},2621:e=>{var i=jQuery,t=wp.media.View.extend({tagName:"div",template:wp.template("media-modal"),events:{"click .media-modal-backdrop, .media-modal-close":"escapeHandler",keydown:"keydown"},clickedOpenerEl:null,initialize:function(){_.defaults(this.options,{container:document.body,title:"",propagate:!0,hasCloseButton:!0}),this.focusManager=new wp.media.view.FocusManager({el:this.el})},prepare:function(){return{title:this.options.title,hasCloseButton:this.options.hasCloseButton}},attach:function(){return this.views.attached?this:(this.views.rendered||this.render(),this.$el.appendTo(this.options.container),this.views.attached=!0,this.views.ready(),this.propagate("attach"))},detach:function(){return this.$el.is(":visible")&&this.close(),this.$el.detach(),this.views.attached=!1,this.propagate("detach")},open:function(){var e,t=this.$el;return t.is(":visible")?this:(this.clickedOpenerEl=document.activeElement,this.views.attached||this.attach(),i("body").addClass("modal-open"),t.show(),"ontouchend"in document&&(e=window.tinymce&&window.tinymce.activeEditor)&&!e.isHidden()&&e.iframeElement&&(e.iframeElement.focus(),e.iframeElement.blur(),setTimeout(function(){e.iframeElement.blur()},100)),this.$(".media-modal").trigger("focus"),this.focusManager.setAriaHiddenOnBodyChildren(t),this.propagate("open"))},close:function(e){return this.views.attached&&this.$el.is(":visible")&&(i(".mejs-pause button").trigger("click"),i("body").removeClass("modal-open"),this.$el.hide(),this.focusManager.removeAriaHiddenFromBodyChildren(),null!==this.clickedOpenerEl?this.clickedOpenerEl.focus():i("#wpbody-content").attr("tabindex","-1").trigger("focus"),this.propagate("close"),e)&&e.escape&&this.propagate("escape"),this},escape:function(){return this.close({escape:!0})},escapeHandler:function(e){e.preventDefault(),this.escape()},selectHandler:function(e){var t=this.controller.state().get("selection");t.length<=0||("insert"===this.controller.options.state?this.controller.trigger("insert",t):(this.controller.trigger("select",t),e.preventDefault(),this.escape()))},content:function(e){return this.views.set(".media-modal-content",e),this},propagate:function(e){return this.trigger(e),this.options.propagate&&this.controller.trigger(e),this},keydown:function(e){27===e.which&&this.$el.is(":visible")&&(this.escape(),e.stopImmediatePropagation()),13!==e.which&&10!==e.which||!e.metaKey&&!e.ctrlKey||(this.selectHandler(e),e.stopImmediatePropagation())}});e.exports=t},2650:e=>{var t=wp.media.view.Settings.AttachmentDisplay,o=jQuery,i=t.extend({className:"image-details",template:wp.template("image-details"),events:_.defaults(t.prototype.events,{"click .edit-attachment":"editAttachment","click .replace-attachment":"replaceAttachment","click .advanced-toggle":"onToggleAdvanced",'change [data-setting="customWidth"]':"onCustomSize",'change [data-setting="customHeight"]':"onCustomSize",'keyup [data-setting="customWidth"]':"onCustomSize",'keyup [data-setting="customHeight"]':"onCustomSize"}),initialize:function(){this.options.attachment=this.model.attachment,this.listenTo(this.model,"change:url",this.updateUrl),this.listenTo(this.model,"change:link",this.toggleLinkSettings),this.listenTo(this.model,"change:size",this.toggleCustomSize),t.prototype.initialize.apply(this,arguments)},prepare:function(){var e=!1;return this.model.attachment&&(e=this.model.attachment.toJSON()),_.defaults({model:this.model.toJSON(),attachment:e},this.options)},render:function(){var e=arguments;return this.model.attachment&&"pending"===this.model.dfd.state()?this.model.dfd.done(_.bind(function(){t.prototype.render.apply(this,e),this.postRender()},this)).fail(_.bind(function(){this.model.attachment=!1,t.prototype.render.apply(this,e),this.postRender()},this)):(t.prototype.render.apply(this,arguments),this.postRender()),this},postRender:function(){setTimeout(_.bind(this.scrollToTop,this),10),this.toggleLinkSettings(),"show"===window.getUserSetting("advImgDetails")&&this.toggleAdvanced(!0),this.trigger("post-render")},scrollToTop:function(){this.$(".embed-media-settings").scrollTop(0)},updateUrl:function(){this.$(".image img").attr("src",this.model.get("url")),this.$(".url").val(this.model.get("url"))},toggleLinkSettings:function(){"none"===this.model.get("link")?this.$(".link-settings").addClass("hidden"):this.$(".link-settings").removeClass("hidden")},toggleCustomSize:function(){"custom"!==this.model.get("size")?this.$(".custom-size").addClass("hidden"):this.$(".custom-size").removeClass("hidden")},onCustomSize:function(e){var t,i=o(e.target).data("setting"),s=o(e.target).val();!/^\d+/.test(s)||parseInt(s,10)<1?e.preventDefault():("customWidth"===i?(t=Math.round(1/this.model.get("aspectRatio")*s),this.model.set("customHeight",t,{silent:!0}),this.$('[data-setting="customHeight"]')):(t=Math.round(this.model.get("aspectRatio")*s),this.model.set("customWidth",t,{silent:!0}),this.$('[data-setting="customWidth"]'))).val(t)},onToggleAdvanced:function(e){e.preventDefault(),this.toggleAdvanced()},toggleAdvanced:function(e){var t=this.$el.find(".advanced-section"),e=t.hasClass("advanced-visible")||!1===e?(t.removeClass("advanced-visible"),t.find(".advanced-settings").addClass("hidden"),"hide"):(t.addClass("advanced-visible"),t.find(".advanced-settings").removeClass("hidden"),"show");window.setUserSetting("advImgDetails",e)},editAttachment:function(e){var t=this.controller.states.get("edit-image");window.imageEdit&&t&&(e.preventDefault(),t.set("image",this.model.attachment),this.controller.setState("edit-image"))},replaceAttachment:function(e){e.preventDefault(),this.controller.setState("replace-image")}});e.exports=i},2836:e=>{var t=wp.media.view.Frame,i=wp.media.view.l10n,o=jQuery,s=t.extend({className:"media-frame",template:wp.template("media-frame"),regions:["menu","title","content","toolbar","router"],events:{"click .media-frame-menu-toggle":"toggleMenu"},initialize:function(){t.prototype.initialize.apply(this,arguments),_.defaults(this.options,{title:i.mediaFrameDefaultTitle,modal:!0,uploader:!0}),this.$el.addClass("wp-core-ui"),this.options.modal&&(this.modal=new wp.media.view.Modal({controller:this,title:this.options.title}),this.modal.content(this)),!wp.Uploader.limitExceeded&&wp.Uploader.browser.supported||(this.options.uploader=!1),this.options.uploader&&(this.uploader=new wp.media.view.UploaderWindow({controller:this,uploader:{dropzone:(this.modal||this).$el,container:this.$el}}),this.views.set(".media-frame-uploader",this.uploader)),this.on("attach",_.bind(this.views.ready,this.views),this),this.on("title:create:default",this.createTitle,this),this.title.mode("default"),this.on("menu:create:default",this.createMenu,this),this.on("open",this.setMenuTabPanelAriaAttributes,this),this.on("open",this.setRouterTabPanelAriaAttributes,this),this.on("content:render",this.setMenuTabPanelAriaAttributes,this),this.on("content:render",this.setRouterTabPanelAriaAttributes,this)},setMenuTabPanelAriaAttributes:function(){var e=this.state().get("id"),t=this.$el.find(".media-frame-tab-panel");t.removeAttr("role aria-labelledby tabindex"),this.state().get("menu")&&this.menuView&&this.menuView.isVisible&&t.attr({role:"tabpanel","aria-labelledby":"menu-item-"+e,tabIndex:"0"})},setRouterTabPanelAriaAttributes:function(){var e,t=this.$el.find(".media-frame-content");t.removeAttr("role aria-labelledby tabindex"),this.state().get("router")&&this.routerView&&this.routerView.isVisible&&this.content._mode&&(e="menu-item-"+this.content._mode,t.attr({role:"tabpanel","aria-labelledby":e,tabIndex:"0"}))},render:function(){return!this.state()&&this.options.state&&this.setState(this.options.state),t.prototype.render.apply(this,arguments)},createTitle:function(e){e.view=new wp.media.View({controller:this,tagName:"h1"})},createMenu:function(e){e.view=new wp.media.view.Menu({controller:this,attributes:{role:"tablist","aria-orientation":"vertical"}}),this.menuView=e.view},toggleMenu:function(e){var t=this.$el.find(".media-menu");t.toggleClass("visible"),o(e.target).attr("aria-expanded",t.hasClass("visible"))},createToolbar:function(e){e.view=new wp.media.view.Toolbar({controller:this})},createRouter:function(e){e.view=new wp.media.view.Router({controller:this,attributes:{role:"tablist","aria-orientation":"horizontal"}}),this.routerView=e.view},createIframeStates:function(i){var e=wp.media.view.settings,t=e.tabs,s=e.tabUrl;t&&s&&((e=o("#post_ID")).length&&(s+="&post_id="+e.val()),_.each(t,function(e,t){this.state("iframe:"+t).set(_.defaults({tab:t,src:s+"&tab="+t,title:e,content:"iframe",menu:"default"},i))},this),this.on("content:create:iframe",this.iframeContent,this),this.on("content:deactivate:iframe",this.iframeContentCleanup,this),this.on("menu:render:default",this.iframeMenu,this),this.on("open",this.hijackThickbox,this),this.on("close",this.restoreThickbox,this))},iframeContent:function(e){this.$el.addClass("hide-toolbar"),e.view=new wp.media.view.Iframe({controller:this})},iframeContentCleanup:function(){this.$el.removeClass("hide-toolbar")},iframeMenu:function(e){var i={};e&&(_.each(wp.media.view.settings.tabs,function(e,t){i["iframe:"+t]={text:this.state("iframe:"+t).get("title"),priority:200}},this),e.set(i))},hijackThickbox:function(){var e=this;window.tb_remove&&!this._tb_remove&&(this._tb_remove=window.tb_remove,window.tb_remove=function(){e.close(),e.reset(),e.setState(e.options.state),e._tb_remove.call(window)})},restoreThickbox:function(){this._tb_remove&&(window.tb_remove=this._tb_remove,delete this._tb_remove)}});_.each(["open","close","attach","detach","escape"],function(e){s.prototype[e]=function(){return this.modal&&this.modal[e].apply(this.modal,arguments),this}}),e.exports=s},2982:e=>{var t=wp.media.View,i=t.extend({tagName:"form",className:"compat-item",events:{submit:"preventDefault","change input":"save","change select":"save","change textarea":"save"},initialize:function(){this.listenTo(this.model,"add",this.render)},dispose:function(){return this.$(":focus").length&&this.save(),t.prototype.dispose.apply(this,arguments)},render:function(){var e=this.model.get("compat");if(e&&e.item)return this.views.detach(),this.$el.html(e.item),this.views.render(),this},preventDefault:function(e){e.preventDefault()},save:function(e){var t={};e&&e.preventDefault(),_.each(this.$el.serializeArray(),function(e){t[e.name]=e.value}),this.controller.trigger("attachment:compat:waiting",["waiting"]),this.model.saveCompat(t).always(_.bind(this.postSave,this))},postSave:function(){this.controller.trigger("attachment:compat:ready",["ready"])}});e.exports=i},3443:e=>{var t=wp.media.view.Attachment.extend({buttons:{check:!0}});e.exports=t},3479:e=>{var t=wp.media.view.Attachments,i=t.extend({events:{},initialize:function(){return _.defaults(this.options,{sortable:!1,resize:!1,AttachmentView:wp.media.view.Attachment.Selection}),t.prototype.initialize.apply(this,arguments)}});e.exports=i},3674:e=>{var t=wp.media.View,i=wp.media.view.l10n,s=jQuery,o=t.extend({tagName:"div",className:"uploader-editor",template:wp.template("uploader-editor"),localDrag:!1,overContainer:!1,overDropzone:!1,draggingFile:null,initialize:function(){return this.initialized=!1,window.tinyMCEPreInit&&window.tinyMCEPreInit.dragDropUpload&&this.browserSupport()&&(this.$document=s(document),this.dropzones=[],this.files=[],this.$document.on("drop",".uploader-editor",_.bind(this.drop,this)),this.$document.on("dragover",".uploader-editor",_.bind(this.dropzoneDragover,this)),this.$document.on("dragleave",".uploader-editor",_.bind(this.dropzoneDragleave,this)),this.$document.on("click",".uploader-editor",_.bind(this.click,this)),this.$document.on("dragover",_.bind(this.containerDragover,this)),this.$document.on("dragleave",_.bind(this.containerDragleave,this)),this.$document.on("dragstart dragend drop",_.bind(function(e){this.localDrag="dragstart"===e.type,"drop"===e.type&&this.containerDragleave()},this)),this.initialized=!0),this},browserSupport:function(){var e=document.createElement("div");return("draggable"in e||"ondragstart"in e&&"ondrop"in e)&&!!(window.File&&window.FileList&&window.FileReader)},isDraggingFile:function(e){if(null===this.draggingFile){if(_.isUndefined(e.originalEvent)||_.isUndefined(e.originalEvent.dataTransfer))return!1;this.draggingFile=-1<_.indexOf(e.originalEvent.dataTransfer.types,"Files")&&-1===_.indexOf(e.originalEvent.dataTransfer.types,"text/plain")}return this.draggingFile},refresh:function(e){for(var t in this.dropzones)this.dropzones[t].toggle(this.overContainer||this.overDropzone);return _.isUndefined(e)||s(e.target).closest(".uploader-editor").toggleClass("droppable",this.overDropzone),this.overContainer||this.overDropzone||(this.draggingFile=null),this},render:function(){return this.initialized&&(t.prototype.render.apply(this,arguments),s(".wp-editor-wrap").each(_.bind(this.attach,this))),this},attach:function(e,t){var i=this.$el.clone();return this.dropzones.push(i),s(t).append(i),this},drop:function(e){if(this.containerDragleave(e),this.dropzoneDragleave(e),this.files=e.originalEvent.dataTransfer.files,!(this.files.length<1))return 0<(e=s(e.target).parents(".wp-editor-wrap")).length&&e[0].id&&(window.wpActiveEditor=e[0].id.slice(3,-5)),this.workflow?(this.workflow.state().reset(),this.addFiles.apply(this),this.workflow.open()):(this.workflow=wp.media.editor.open(window.wpActiveEditor,{frame:"post",state:"insert",title:i.addMedia,multiple:!0}),(e=this.workflow.uploader).uploader&&e.uploader.ready?this.addFiles.apply(this):this.workflow.on("uploader:ready",this.addFiles,this)),!1},addFiles:function(){return this.files.length&&(this.workflow.uploader.uploader.uploader.addFile(_.toArray(this.files)),this.files=[]),this},containerDragover:function(e){!this.localDrag&&this.isDraggingFile(e)&&(this.overContainer=!0,this.refresh())},containerDragleave:function(){this.overContainer=!1,_.delay(_.bind(this.refresh,this),50)},dropzoneDragover:function(e){if(!this.localDrag&&this.isDraggingFile(e))return this.overDropzone=!0,this.refresh(e),!1},dropzoneDragleave:function(e){this.overDropzone=!1,_.delay(_.bind(this.refresh,this,e),50)},click:function(e){this.containerDragleave(e),this.dropzoneDragleave(e),this.localDrag=!1}});e.exports=o},3962:e=>{var t=wp.media.view.Attachment.extend({className:"attachment selection",toggleSelection:function(){this.options.selection.single(this.model)}});e.exports=t},4075:e=>{var t=wp.media.View,o=jQuery,i=t.extend({tagName:"li",className:"attachment",template:wp.template("attachment"),attributes:function(){return{tabIndex:0,role:"checkbox","aria-label":this.model.get("title"),"aria-checked":!1,"data-id":this.model.get("id")}},events:{click:"toggleSelectionHandler","change [data-setting]":"updateSetting","change [data-setting] input":"updateSetting","change [data-setting] select":"updateSetting","change [data-setting] textarea":"updateSetting","click .attachment-close":"removeFromLibrary","click .check":"checkClickHandler",keydown:"toggleSelectionHandler"},buttons:{},initialize:function(){var e=this.options.selection;_.defaults(this.options,{rerenderOnModelChange:!0}).rerenderOnModelChange?this.listenTo(this.model,"change",this.render):this.listenTo(this.model,"change:percent",this.progress),this.listenTo(this.model,"change:title",this._syncTitle),this.listenTo(this.model,"change:caption",this._syncCaption),this.listenTo(this.model,"change:artist",this._syncArtist),this.listenTo(this.model,"change:album",this._syncAlbum),this.listenTo(this.model,"add",this.select),this.listenTo(this.model,"remove",this.deselect),e&&(e.on("reset",this.updateSelect,this),this.listenTo(this.model,"selection:single selection:unsingle",this.details),this.details(this.model,this.controller.state().get("selection"))),this.listenTo(this.controller.states,"attachment:compat:waiting attachment:compat:ready",this.updateSave)},dispose:function(){var e=this.options.selection;return this.updateAll(),e&&e.off(null,null,this),t.prototype.dispose.apply(this,arguments),this},render:function(){var e=_.defaults(this.model.toJSON(),{orientation:"landscape",uploading:!1,type:"",subtype:"",icon:"",filename:"",caption:"",title:"",dateFormatted:"",width:"",height:"",compat:!1,alt:"",description:""},this.options);return e.buttons=this.buttons,e.describe=this.controller.state().get("describe"),"image"===e.type&&(e.size=this.imageSize()),e.can={},e.nonces&&(e.can.remove=!!e.nonces.delete,e.can.save=!!e.nonces.update),this.controller.state().get("allowLocalEdits")&&!e.uploading&&(e.allowLocalEdits=!0),e.uploading&&!e.percent&&(e.percent=0),this.views.detach(),this.$el.html(this.template(e)),this.$el.toggleClass("uploading",e.uploading),e.uploading?this.$bar=this.$(".media-progress-bar div"):delete this.$bar,this.updateSelect(),this.updateSave(),this.views.render(),this},progress:function(){this.$bar&&this.$bar.length&&this.$bar.width(this.model.get("percent")+"%")},toggleSelectionHandler:function(e){var t;if("INPUT"!==e.target.nodeName&&"BUTTON"!==e.target.nodeName)if(37===e.keyCode||38===e.keyCode||39===e.keyCode||40===e.keyCode)this.controller.trigger("attachment:keydown:arrow",e);else if("keydown"!==e.type||13===e.keyCode||32===e.keyCode){if(e.preventDefault(),this.controller.isModeActive("grid")){if(this.controller.isModeActive("edit"))return void this.controller.trigger("edit:attachment",this.model,e.currentTarget);this.controller.isModeActive("select")&&(t="toggle")}e.shiftKey?t="between":(e.ctrlKey||e.metaKey)&&(t="toggle"),(!e.metaKey&&!e.ctrlKey||13!==e.keyCode&&10!==e.keyCode)&&(this.toggleSelection({method:t}),this.controller.trigger("selection:toggle"))}},toggleSelection:function(e){var t,i,s,o=this.collection,a=this.options.selection,n=this.model,e=e&&e.method;if(a){if(t=a.single(),"between"===(e=_.isUndefined(e)?a.multiple:e)&&t&&a.multiple)return t===n?void 0:(o=(i=o.indexOf(t))<(s=o.indexOf(this.model))?o.models.slice(i,s+1):o.models.slice(s,i+1),a.add(o),void a.single(n));"toggle"===e?(a[this.selected()?"remove":"add"](n),a.single(n)):"add"===e?(a.add(n),a.single(n)):("add"!==(e=e||"add")&&(e="reset"),this.selected()?a[t===n?"remove":"single"](n):(a[e](n),a.single(n)))}},updateSelect:function(){this[this.selected()?"select":"deselect"]()},selected:function(){var e=this.options.selection;if(e)return!!e.get(this.model.cid)},select:function(e,t){var i=this.options.selection,s=this.controller;!i||t&&t!==i||this.$el.hasClass("selected")||(this.$el.addClass("selected").attr("aria-checked",!0),s.isModeActive("grid")&&s.isModeActive("select"))||this.$(".check").attr("tabindex","0")},deselect:function(e,t){var i=this.options.selection;!i||t&&t!==i||this.$el.removeClass("selected").attr("aria-checked",!1).find(".check").attr("tabindex","-1")},details:function(e,t){var i=this.options.selection;i===t&&(t=i.single(),this.$el.toggleClass("details",t===this.model))},imageSize:function(e){var t=this.model.get("sizes"),i=!1;return e=e||"medium",t&&(t[e]?i=t[e]:t.large?i=t.large:t.thumbnail?i=t.thumbnail:t.full&&(i=t.full),i)?_.clone(i):{url:this.model.get("url"),width:this.model.get("width"),height:this.model.get("height"),orientation:this.model.get("orientation")}},updateSetting:function(e){var t=o(e.target).closest("[data-setting]");t.length&&(t=t.data("setting"),e=e.target.value,this.model.get(t)!==e)&&this.save(t,e)},save:function(){var e=this,t=this._save=this._save||{status:"ready"},i=this.model.save.apply(this.model,arguments),s=t.requests?o.when(i,t.requests):i;t.savedTimer&&clearTimeout(t.savedTimer),this.updateSave("waiting"),(t.requests=s).always(function(){t.requests===s&&(e.updateSave("resolved"===s.state()?"complete":"error"),t.savedTimer=setTimeout(function(){e.updateSave("ready"),delete t.savedTimer},2e3))})},updateSave:function(e){var t=this._save=this._save||{status:"ready"};return e&&e!==t.status&&(this.$el.removeClass("save-"+t.status),t.status=e),this.$el.addClass("save-"+t.status),this},updateAll:function(){var e=this.$("[data-setting]"),i=this.model,e=_.chain(e).map(function(e){var t=o("input, textarea, select, [value]",e);if(t.length)return e=o(e).data("setting"),t=t.val(),i.get(e)!==t?[e,t]:void 0}).compact().object().value();_.isEmpty(e)||i.save(e)},removeFromLibrary:function(e){"keydown"===e.type&&13!==e.keyCode&&32!==e.keyCode||(e.stopPropagation(),this.collection.remove(this.model))},checkClickHandler:function(e){var t=this.options.selection;t&&(e.stopPropagation(),t.where({id:this.model.get("id")}).length?(t.remove(this.model),this.$el.focus()):t.add(this.model),this.controller.trigger("selection:toggle"))}});_.each({caption:"_syncCaption",title:"_syncTitle",artist:"_syncArtist",album:"_syncAlbum"},function(e,s){i.prototype[e]=function(e,t){var i=this.$('[data-setting="'+s+'"]');return!i.length||t===i.find("input, textarea, select, [value]").val()?this:this.render()}}),e.exports=i},4181:e=>{e.exports={syncSelection:function(){var e=this.get("selection"),t=this.frame._selection;this.get("syncSelection")&&t&&e&&(e.multiple&&(e.reset([],{silent:!0}),e.validateAll(t.attachments),t.difference=_.difference(t.attachments.models,e.models)),e.single(t.single))},recordSelection:function(){var e=this.get("selection"),t=this.frame._selection;this.get("syncSelection")&&t&&e&&(e.multiple?(t.attachments.reset(e.toArray().concat(t.difference)),t.difference=[]):t.attachments.add(e.toArray()),t.single=e._single)}}},4274:e=>{var t=wp.media.view.MediaFrame.Select,i=wp.media.controller.Library,o=wp.media.view.l10n,s=t.extend({initialize:function(){this.counts={audio:{count:wp.media.view.settings.attachmentCounts.audio,state:"playlist"},video:{count:wp.media.view.settings.attachmentCounts.video,state:"video-playlist"}},_.defaults(this.options,{multiple:!0,editing:!1,state:"insert",metadata:{}}),t.prototype.initialize.apply(this,arguments),this.createIframeStates()},createStates:function(){var e=this.options;this.states.add([new i({id:"insert",title:o.insertMediaTitle,priority:20,toolbar:"main-insert",filterable:"all",library:wp.media.query(e.library),multiple:!!e.multiple&&"reset",editable:!0,allowLocalEdits:!0,displaySettings:!0,displayUserSettings:!0}),new i({id:"gallery",title:o.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:wp.media.query(_.defaults({type:"image"},e.library))}),new wp.media.controller.Embed({metadata:e.metadata}),new wp.media.controller.EditImage({model:e.editImage}),new wp.media.controller.GalleryEdit({library:e.selection,editing:e.editing,menu:"gallery"}),new wp.media.controller.GalleryAdd,new i({id:"playlist",title:o.createPlaylistTitle,priority:60,toolbar:"main-playlist",filterable:"uploaded",multiple:"add",editable:!1,library:wp.media.query(_.defaults({type:"audio"},e.library))}),new wp.media.controller.CollectionEdit({type:"audio",collectionType:"playlist",title:o.editPlaylistTitle,SettingsView:wp.media.view.Settings.Playlist,library:e.selection,editing:e.editing,menu:"playlist",dragInfoText:o.playlistDragInfo,dragInfo:!1}),new wp.media.controller.CollectionAdd({type:"audio",collectionType:"playlist",title:o.addToPlaylistTitle}),new i({id:"video-playlist",title:o.createVideoPlaylistTitle,priority:60,toolbar:"main-video-playlist",filterable:"uploaded",multiple:"add",editable:!1,library:wp.media.query(_.defaults({type:"video"},e.library))}),new wp.media.controller.CollectionEdit({type:"video",collectionType:"playlist",title:o.editVideoPlaylistTitle,SettingsView:wp.media.view.Settings.Playlist,library:e.selection,editing:e.editing,menu:"video-playlist",dragInfoText:o.videoPlaylistDragInfo,dragInfo:!1}),new wp.media.controller.CollectionAdd({type:"video",collectionType:"playlist",title:o.addToVideoPlaylistTitle})]),wp.media.view.settings.post.featuredImageId&&this.states.add(new wp.media.controller.FeaturedImage)},bindHandlers:function(){t.prototype.bindHandlers.apply(this,arguments),this.on("activate",this.activate,this),void 0!==_.find(this.counts,function(e){return 0===e.count})&&this.listenTo(wp.media.model.Attachments.all,"change:type",this.mediaTypeCounts),this.on("menu:create:gallery",this.createMenu,this),this.on("menu:create:playlist",this.createMenu,this),this.on("menu:create:video-playlist",this.createMenu,this),this.on("toolbar:create:main-insert",this.createToolbar,this),this.on("toolbar:create:main-gallery",this.createToolbar,this),this.on("toolbar:create:main-playlist",this.createToolbar,this),this.on("toolbar:create:main-video-playlist",this.createToolbar,this),this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.on("toolbar:create:main-embed",this.mainEmbedToolbar,this),_.each({menu:{default:"mainMenu",gallery:"galleryMenu",playlist:"playlistMenu","video-playlist":"videoPlaylistMenu"},content:{embed:"embedContent","edit-image":"editImageContent","edit-selection":"editSelectionContent"},toolbar:{"main-insert":"mainInsertToolbar","main-gallery":"mainGalleryToolbar","gallery-edit":"galleryEditToolbar","gallery-add":"galleryAddToolbar","main-playlist":"mainPlaylistToolbar","playlist-edit":"playlistEditToolbar","playlist-add":"playlistAddToolbar","main-video-playlist":"mainVideoPlaylistToolbar","video-playlist-edit":"videoPlaylistEditToolbar","video-playlist-add":"videoPlaylistAddToolbar"}},function(e,i){_.each(e,function(e,t){this.on(i+":render:"+t,this[e],this)},this)},this)},activate:function(){_.each(this.counts,function(e){e.count<1&&this.menuItemVisibility(e.state,"hide")},this)},mediaTypeCounts:function(e,t){void 0!==this.counts[t]&&this.counts[t].count<1&&(this.counts[t].count++,this.menuItemVisibility(this.counts[t].state,"show"))},mainMenu:function(e){e.set({"library-separator":new wp.media.View({className:"separator",priority:100,attributes:{role:"presentation"}})})},menuItemVisibility:function(e,t){var i=this.menu.get();"hide"===t?i.hide(e):"show"===t&&i.show(e)},galleryMenu:function(e){var t=this.lastState(),i=t&&t.id,s=this;e.set({cancel:{text:o.cancelGalleryTitle,priority:20,click:function(){i?s.setState(i):s.close(),this.controller.modal.focusManager.focus()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},playlistMenu:function(e){var t=this.lastState(),i=t&&t.id,s=this;e.set({cancel:{text:o.cancelPlaylistTitle,priority:20,click:function(){i?s.setState(i):s.close(),this.controller.modal.focusManager.focus()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},videoPlaylistMenu:function(e){var t=this.lastState(),i=t&&t.id,s=this;e.set({cancel:{text:o.cancelVideoPlaylistTitle,priority:20,click:function(){i?s.setState(i):s.close(),this.controller.modal.focusManager.focus()}},separateCancel:new wp.media.View({className:"separator",priority:40})})},embedContent:function(){var e=new wp.media.view.Embed({controller:this,model:this.state()}).render();this.content.set(e)},editSelectionContent:function(){var e=this.state(),t=e.get("selection"),t=new wp.media.view.AttachmentsBrowser({controller:this,collection:t,selection:t,model:e,sortable:!0,search:!1,date:!1,dragInfo:!0,AttachmentView:wp.media.view.Attachments.EditSelection}).render();t.toolbar.set("backToLibrary",{text:o.returnToLibrary,priority:-100,click:function(){this.controller.content.mode("browse"),this.controller.modal.focusManager.focus()}}),this.content.set(t),this.trigger("edit:selection",this)},editImageContent:function(){var e=this.state().get("image"),e=new wp.media.view.EditImage({model:e,controller:this}).render();this.content.set(e),e.loadEditor()},selectionStatusToolbar:function(e){var t=this.state().get("editable");e.set("selection",new wp.media.view.Selection({controller:this,collection:this.state().get("selection"),priority:-40,editable:t&&function(){this.controller.content.mode("edit-selection")}}).render())},mainInsertToolbar:function(e){var i=this;this.selectionStatusToolbar(e),e.set("insert",{style:"primary",priority:80,text:o.insertIntoPost,requires:{selection:!0},click:function(){var e=i.state(),t=e.get("selection");i.close(),e.trigger("insert",t).reset()}})},mainGalleryToolbar:function(e){var s=this;this.selectionStatusToolbar(e),e.set("gallery",{style:"primary",text:o.createNewGallery,priority:60,requires:{selection:!0},click:function(){var e=s.state().get("selection"),t=s.state("gallery-edit"),i=e.where({type:"image"});t.set("library",new wp.media.model.Selection(i,{props:e.props.toJSON(),multiple:!0})),this.controller.setState("gallery-edit"),this.controller.modal.focusManager.focus()}})},mainPlaylistToolbar:function(e){var s=this;this.selectionStatusToolbar(e),e.set("playlist",{style:"primary",text:o.createNewPlaylist,priority:100,requires:{selection:!0},click:function(){var e=s.state().get("selection"),t=s.state("playlist-edit"),i=e.where({type:"audio"});t.set("library",new wp.media.model.Selection(i,{props:e.props.toJSON(),multiple:!0})),this.controller.setState("playlist-edit"),this.controller.modal.focusManager.focus()}})},mainVideoPlaylistToolbar:function(e){var s=this;this.selectionStatusToolbar(e),e.set("video-playlist",{style:"primary",text:o.createNewVideoPlaylist,priority:100,requires:{selection:!0},click:function(){var e=s.state().get("selection"),t=s.state("video-playlist-edit"),i=e.where({type:"video"});t.set("library",new wp.media.model.Selection(i,{props:e.props.toJSON(),multiple:!0})),this.controller.setState("video-playlist-edit"),this.controller.modal.focusManager.focus()}})},featuredImageToolbar:function(e){this.createSelectToolbar(e,{text:o.setFeaturedImage,state:this.options.state})},mainEmbedToolbar:function(e){e.view=new wp.media.view.Toolbar.Embed({controller:this})},galleryEditToolbar:function(){var e=this.state().get("editing");this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:e?o.updateGallery:o.insertGallery,priority:80,requires:{library:!0,uploadingComplete:!0},click:function(){var e=this.controller,t=e.state();e.close(),t.trigger("update",t.get("library")),e.setState(e.options.state),e.reset()}}}}))},galleryAddToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:o.addToGallery,priority:80,requires:{selection:!0},click:function(){var e=this.controller,t=e.state();e.state("gallery-edit").get("library").add(t.get("selection").models),t.trigger("reset"),e.setState("gallery-edit"),this.controller.modal.focusManager.focus()}}}}))},playlistEditToolbar:function(){var e=this.state().get("editing");this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:e?o.updatePlaylist:o.insertPlaylist,priority:80,requires:{library:!0},click:function(){var e=this.controller,t=e.state();e.close(),t.trigger("update",t.get("library")),e.setState(e.options.state),e.reset()}}}}))},playlistAddToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:o.addToPlaylist,priority:80,requires:{selection:!0},click:function(){var e=this.controller,t=e.state();e.state("playlist-edit").get("library").add(t.get("selection").models),t.trigger("reset"),e.setState("playlist-edit"),this.controller.modal.focusManager.focus()}}}}))},videoPlaylistEditToolbar:function(){var e=this.state().get("editing");this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:e?o.updateVideoPlaylist:o.insertVideoPlaylist,priority:140,requires:{library:!0},click:function(){var e=this.controller,t=e.state(),i=t.get("library");i.type="video",e.close(),t.trigger("update",i),e.setState(e.options.state),e.reset()}}}}))},videoPlaylistAddToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:o.addToVideoPlaylist,priority:140,requires:{selection:!0},click:function(){var e=this.controller,t=e.state();e.state("video-playlist-edit").get("library").add(t.get("selection").models),t.trigger("reset"),e.setState("video-playlist-edit"),this.controller.modal.focusManager.focus()}}}}))}});e.exports=s},4338:e=>{var t=wp.media.View.extend({tagName:"label",className:"screen-reader-text",initialize:function(){this.value=this.options.value},render:function(){return this.$el.html(this.value),this}});e.exports=t},4593:e=>{var t=wp.media.view.Attachment.Selection.extend({buttons:{close:!0}});e.exports=t},4747:e=>{var t=wp.Backbone.View.extend({constructor:function(e){e&&e.controller&&(this.controller=e.controller),wp.Backbone.View.apply(this,arguments)},dispose:function(){return this.undelegateEvents(),this.model&&this.model.off&&this.model.off(null,null,this),this.collection&&this.collection.off&&this.collection.off(null,null,this),this.controller&&this.controller.off&&this.controller.off(null,null,this),this},remove:function(){return this.dispose(),wp.Backbone.View.prototype.remove.apply(this,arguments)}});e.exports=t},4783:e=>{var t=wp.media.view.Menu,i=t.extend({tagName:"div",className:"media-router",property:"contentMode",ItemView:wp.media.view.RouterItem,region:"router",attributes:{role:"tablist","aria-orientation":"horizontal"},initialize:function(){this.controller.on("content:render",this.update,this),t.prototype.initialize.apply(this,arguments)},update:function(){var e=this.controller.content.mode();e&&this.select(e)}});e.exports=i},4910:e=>{var t=wp.media.view.l10n,n=Backbone.$,t=wp.media.controller.State.extend({defaults:{id:"embed",title:t.insertFromUrlTitle,content:"embed",menu:"default",toolbar:"main-embed",priority:120,type:"link",url:"",metadata:{}},sensitivity:400,initialize:function(e){this.metadata=e.metadata,this.debouncedScan=_.debounce(_.bind(this.scan,this),this.sensitivity),this.props=new Backbone.Model(this.metadata||{url:""}),this.props.on("change:url",this.debouncedScan,this),this.props.on("change:url",this.refresh,this),this.on("scan",this.scanImage,this)},scan:function(){var e,t=this,i={type:"link",scanners:[]};this.props.get("url")&&this.trigger("scan",i),i.scanners.length?(e=i.scanners=n.when.apply(n,i.scanners)).always(function(){t.get("scanners")===e&&t.set("loading",!1)}):i.scanners=null,i.loading=!!i.scanners,this.set(i)},scanImage:function(e){var t=this.frame,i=this,s=this.props.get("url"),o=new Image,a=n.Deferred();e.scanners.push(a.promise()),o.onload=function(){a.resolve(),i===t.state()&&s===i.props.get("url")&&(i.set({type:"image"}),i.props.set({width:o.width,height:o.height}))},o.onerror=a.reject,o.src=s},refresh:function(){this.frame.toolbar.get().refresh()},reset:function(){this.props.clear().set({url:""}),this.active&&this.refresh()}});e.exports=t},5232:e=>{var t=wp.media.view.Attachment.extend({buttons:{close:!0}});e.exports=t},5275:e=>{var t=wp.media.View,i=t.extend({tagName:"div",className:"media-toolbar",initialize:function(){var e=this.controller.state(),t=this.selection=e.get("selection"),e=this.library=e.get("library");this._views={},this.primary=new wp.media.view.PriorityList,this.secondary=new wp.media.view.PriorityList,this.tertiary=new wp.media.view.PriorityList,this.primary.$el.addClass("media-toolbar-primary search-form"),this.secondary.$el.addClass("media-toolbar-secondary"),this.tertiary.$el.addClass("media-bg-overlay"),this.views.set([this.secondary,this.primary,this.tertiary]),this.options.items&&this.set(this.options.items,{silent:!0}),this.options.silent||this.render(),t&&t.on("add remove reset",this.refresh,this),e&&e.on("add remove reset",this.refresh,this)},dispose:function(){return this.selection&&this.selection.off(null,null,this),this.library&&this.library.off(null,null,this),t.prototype.dispose.apply(this,arguments)},ready:function(){this.refresh()},set:function(e,t,i){return i=i||{},_.isObject(e)?_.each(e,function(e,t){this.set(t,e,{silent:!0})},this):(t instanceof Backbone.View||(t.classes=["media-button-"+e].concat(t.classes||[]),t=new wp.media.view.Button(t).render()),t.controller=t.controller||this.controller,this._views[e]=t,this[t.options.priority<0?"secondary":"primary"].set(e,t,i)),i.silent||this.refresh(),this},get:function(e){return this._views[e]},unset:function(e,t){return delete this._views[e],this.primary.unset(e,t),this.secondary.unset(e,t),this.tertiary.unset(e,t),t&&t.silent||this.refresh(),this},refresh:function(){var e=this.controller.state(),o=e.get("library"),a=e.get("selection");_.each(this._views,function(e){var t,i,s;e.model&&e.options&&e.options.requires&&(t=e.options.requires,i=!1,s=o&&!_.isEmpty(o.findWhere({uploading:!0})),a&&a.models&&(i=_.some(a.models,function(e){return!0===e.get("uploading")})),t.uploadingComplete&&s&&(i=!0),(t.selection&&a&&!a.length||t.library&&o&&!o.length)&&(i=!0),e.model.set("disabled",i))})}});e.exports=i},5422:e=>{var a=wp.media.view.l10n,t=wp.media.controller.State.extend({defaults:{id:"cropper",title:a.cropImage,toolbar:"crop",content:"crop",router:!1,canSkipCrop:!1,doCropArgs:{}},activate:function(){this.frame.on("content:create:crop",this.createCropContent,this),this.frame.on("close",this.removeCropper,this),this.set("selection",new Backbone.Collection(this.frame._selection.single))},deactivate:function(){this.frame.toolbar.mode("browse")},createCropContent:function(){this.cropperView=new wp.media.view.Cropper({controller:this,attachment:this.get("selection").first()}),this.cropperView.on("image-loaded",this.createCropToolbar,this),this.frame.content.set(this.cropperView)},removeCropper:function(){this.imgSelect.cancelSelection(),this.imgSelect.setOptions({remove:!0}),this.imgSelect.update(),this.cropperView.remove()},createCropToolbar:function(){var i=this.get("suggestedCropSize"),s=this.get("hasRequiredAspectRatio"),o=this.get("canSkipCrop")||!1,e={controller:this.frame,items:{insert:{style:"primary",text:a.cropImage,priority:80,requires:{library:!1,selection:!1},click:function(){var t=this.controller,e=t.state().get("selection").first();e.set({cropDetails:t.state().imgSelect.getSelection()}),this.$el.text(a.cropping),this.$el.attr("disabled",!0),t.state().doCrop(e).done(function(e){t.trigger("cropped",e),t.close()}).fail(function(){t.trigger("content:error:crop")})}}}};(o||s)&&_.extend(e.items,{skip:{style:"secondary",text:a.skipCropping,priority:70,requires:{library:!1,selection:!1},click:function(){var t=this.controller,e=t.state().get("selection").first();t.state().cropperView.remove(),s&&!o?(e.set({cropDetails:i}),t.state().doCrop(e).done(function(e){t.trigger("cropped",e),t.close()}).fail(function(){t.trigger("content:error:crop")})):(t.trigger("skippedcrop",e),t.close())}}}),this.frame.toolbar.set(new wp.media.view.Toolbar(e))},doCrop:function(e){return wp.ajax.post("custom-header-crop",_.extend({},this.defaults.doCropArgs,{nonce:e.get("nonces").edit,id:e.get("id"),cropDetails:e.get("cropDetails")}))}});e.exports=t},5424:e=>{var t=wp.media.view.MediaFrame.Select,s=wp.media.view.l10n,i=t.extend({defaults:{id:"image",url:"",menu:"image-details",content:"image-details",toolbar:"image-details",type:"link",title:s.imageDetailsTitle,priority:120},initialize:function(e){this.image=new wp.media.model.PostImage(e.metadata),this.options.selection=new wp.media.model.Selection(this.image.attachment,{multiple:!1}),t.prototype.initialize.apply(this,arguments)},bindHandlers:function(){t.prototype.bindHandlers.apply(this,arguments),this.on("menu:create:image-details",this.createMenu,this),this.on("content:create:image-details",this.imageDetailsContent,this),this.on("content:render:edit-image",this.editImageContent,this),this.on("toolbar:render:image-details",this.renderImageDetailsToolbar,this),this.on("toolbar:render:replace",this.renderReplaceImageToolbar,this)},createStates:function(){this.states.add([new wp.media.controller.ImageDetails({image:this.image,editable:!1}),new wp.media.controller.ReplaceImage({id:"replace-image",library:wp.media.query({type:"image"}),image:this.image,multiple:!1,title:s.imageReplaceTitle,toolbar:"replace",priority:80,displaySettings:!0}),new wp.media.controller.EditImage({image:this.image,selection:this.options.selection})])},imageDetailsContent:function(e){e.view=new wp.media.view.ImageDetails({controller:this,model:this.state().image,attachment:this.state().image.attachment})},editImageContent:function(){var e=this.state().get("image");e&&(e=new wp.media.view.EditImage({model:e,controller:this}).render(),this.content.set(e),e.loadEditor())},renderImageDetailsToolbar:function(){this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{select:{style:"primary",text:s.update,priority:80,click:function(){var e=this.controller,t=e.state();e.close(),t.trigger("update",e.image.toJSON()),e.setState(e.options.state),e.reset()}}}}))},renderReplaceImageToolbar:function(){var e=this,t=e.lastState(),i=t&&t.id;this.toolbar.set(new wp.media.view.Toolbar({controller:this,items:{back:{text:s.back,priority:80,click:function(){i?e.setState(i):e.close()}},replace:{style:"primary",text:s.replace,priority:20,requires:{selection:!0},click:function(){var e=this.controller,t=e.state(),i=t.get("selection").single();e.close(),e.image.changeAttachment(i,t.display(i)),t.trigger("replace",e.image.toJSON()),e.setState(e.options.state),e.reset()}}}}))}});e.exports=i},5663:e=>{var s=wp.media.view.l10n,t=wp.media.controller.State.extend({defaults:{id:"edit-image",title:s.editImage,menu:!1,toolbar:"edit-image",content:"edit-image",url:""},activate:function(){this.frame.on("toolbar:render:edit-image",_.bind(this.toolbar,this))},deactivate:function(){this.frame.off("toolbar:render:edit-image")},toolbar:function(){var e=this.frame,t=e.lastState(),i=t&&t.id;e.toolbar.set(new wp.media.view.Toolbar({controller:e,items:{back:{style:"primary",text:s.back,priority:20,click:function(){i?e.setState(i):e.close()}}}}))}});e.exports=t},5694:e=>{var i=Backbone.Model.extend({constructor:function(){this.on("activate",this._preActivate,this),this.on("activate",this.activate,this),this.on("activate",this._postActivate,this),this.on("deactivate",this._deactivate,this),this.on("deactivate",this.deactivate,this),this.on("reset",this.reset,this),this.on("ready",this._ready,this),this.on("ready",this.ready,this),Backbone.Model.apply(this,arguments),this.on("change:menu",this._updateMenu,this)},ready:function(){},activate:function(){},deactivate:function(){},reset:function(){},_ready:function(){this._updateMenu()},_preActivate:function(){this.active=!0},_postActivate:function(){this.on("change:menu",this._menu,this),this.on("change:titleMode",this._title,this),this.on("change:content",this._content,this),this.on("change:toolbar",this._toolbar,this),this.frame.on("title:render:default",this._renderTitle,this),this._title(),this._menu(),this._toolbar(),this._content(),this._router()},_deactivate:function(){this.active=!1,this.frame.off("title:render:default",this._renderTitle,this),this.off("change:menu",this._menu,this),this.off("change:titleMode",this._title,this),this.off("change:content",this._content,this),this.off("change:toolbar",this._toolbar,this)},_title:function(){this.frame.title.render(this.get("titleMode")||"default")},_renderTitle:function(e){e.$el.text(this.get("title")||"")},_router:function(){var e=this.frame.router,t=this.get("router");this.frame.$el.toggleClass("hide-router",!t),t&&(this.frame.router.render(t),t=e.get())&&t.select&&t.select(this.frame.content.mode())},_menu:function(){var e,t=this.frame.menu,i=this.get("menu");this.frame.menu&&(e=(e=this.frame.menu.get("views"))?e.views.get().length:0,this.frame.$el.toggleClass("hide-menu",!i||e<2)),i&&(t.mode(i),e=t.get())&&e.select&&e.select(this.id)},_updateMenu:function(){var e=this.previous("menu"),t=this.get("menu");e&&this.frame.off("menu:render:"+e,this._renderMenu,this),t&&this.frame.on("menu:render:"+t,this._renderMenu,this)},_renderMenu:function(e){var t=this.get("menuItem"),i=this.get("title"),s=this.get("priority");!t&&i&&(t={text:i},s)&&(t.priority=s),t&&e.set(this.id,t)}});_.each(["toolbar","content"],function(t){i.prototype["_"+t]=function(){var e=this.get(t);e&&this.frame[t].render(e)}}),e.exports=i},5741:e=>{var t=wp.media.View.extend({className:"media-embed",initialize:function(){this.url=new wp.media.view.EmbedUrl({controller:this.controller,model:this.model.props}).render(),this.views.set([this.url]),this.refresh(),this.listenTo(this.model,"change:type",this.refresh),this.listenTo(this.model,"change:loading",this.loading)},settings:function(e){this._settings&&this._settings.remove(),this._settings=e,this.views.add(e)},refresh:function(){var e,t=this.model.get("type");if("image"===t)e=wp.media.view.EmbedImage;else{if("link"!==t)return;e=wp.media.view.EmbedLink}this.settings(new e({controller:this.controller,model:this.model.props,priority:40}))},loading:function(){this.$el.toggleClass("embed-loading",this.model.get("loading"))}});e.exports=t},6090:e=>{var t=wp.media.view.Attachment,i=wp.media.view.l10n,o=jQuery,a=wp.i18n.__,s=t.extend({tagName:"div",className:"attachment-details",template:wp.template("attachment-details"),attributes:{},events:{"change [data-setting]":"updateSetting","change [data-setting] input":"updateSetting","change [data-setting] select":"updateSetting","change [data-setting] textarea":"updateSetting","click .delete-attachment":"deleteAttachment","click .trash-attachment":"trashAttachment","click .untrash-attachment":"untrashAttachment","click .edit-attachment":"editAttachment",keydown:"toggleSelectionHandler"},copyAttachmentDetailsURLClipboard:function(){var s;new ClipboardJS(".copy-attachment-url").on("success",function(e){var t=o(e.trigger),i=o(".success",t.closest(".copy-to-clipboard-container"));e.clearSelection(),clearTimeout(s),i.removeClass("hidden"),s=setTimeout(function(){i.addClass("hidden")},3e3),wp.a11y.speak(a("The file URL has been copied to your clipboard"))})},initialize:function(){this.options=_.defaults(this.options,{rerenderOnModelChange:!1}),t.prototype.initialize.apply(this,arguments),this.copyAttachmentDetailsURLClipboard()},getFocusableElements:function(){var e=o('li[data-id="'+this.model.id+'"]');this.previousAttachment=e.prev(),this.nextAttachment=e.next()},moveFocus:function(){this.previousAttachment.length?this.previousAttachment.trigger("focus"):this.nextAttachment.length?this.nextAttachment.trigger("focus"):this.controller.uploader&&this.controller.uploader.$browser?this.controller.uploader.$browser.trigger("focus"):this.moveFocusToLastFallback()},moveFocusToLastFallback:function(){o(".media-frame").attr("tabindex","-1").trigger("focus")},deleteAttachment:function(e){e.preventDefault(),this.getFocusableElements(),window.confirm(i.warnDelete)&&(this.model.destroy({wait:!0,error:function(){window.alert(i.errorDeleting)}}),this.moveFocus())},trashAttachment:function(e){var t=this.controller.library,i=this;e.preventDefault(),this.getFocusableElements(),wp.media.view.settings.mediaTrash&&"edit-metadata"===this.controller.content.mode()?(this.model.set("status","trash"),this.model.save().done(function(){t._requery(!0),i.moveFocusToLastFallback()})):(this.model.destroy(),this.moveFocus())},untrashAttachment:function(e){var t=this.controller.library;e.preventDefault(),this.model.set("status","inherit"),this.model.save().done(function(){t._requery(!0)})},editAttachment:function(e){var t=this.controller.states.get("edit-image");window.imageEdit&&t?(e.preventDefault(),t.set("image",this.model),this.controller.setState("edit-image")):this.$el.addClass("needs-refresh")},toggleSelectionHandler:function(e){if("keydown"===e.type&&9===e.keyCode&&e.shiftKey&&e.target===this.$(":tabbable").get(0))return this.controller.trigger("attachment:details:shift-tab",e),!1},render:function(){t.prototype.render.apply(this,arguments),wp.media.mixin.removeAllPlayers(),this.$("audio, video").each(function(e,t){t=wp.media.view.MediaDetails.prepareSrc(t);new window.MediaElementPlayer(t,wp.media.mixin.mejsSettings)})}});e.exports=s},6126:e=>{var t=wp.media.View,i=t.extend({className:"image-editor",template:wp.template("image-editor"),initialize:function(e){this.editor=window.imageEdit,this.controller=e.controller,t.prototype.initialize.apply(this,arguments)},prepare:function(){return this.model.toJSON()},loadEditor:function(){this.editor.open(this.model.get("id"),this.model.get("nonces").edit,this)},back:function(){var e=this.controller.lastState();this.controller.setState(e)},refresh:function(){this.model.fetch()},save:function(){var e=this.controller.lastState();this.model.fetch().done(_.bind(function(){this.controller.setState(e)},this))}});e.exports=i},6150:e=>{function t(){return{extend:Backbone.Model.extend}}_.extend(t.prototype,Backbone.Events,{state:function(e){return this.states=this.states||new Backbone.Collection,(e=e||this._state)&&!this.states.get(e)&&this.states.add({id:e}),this.states.get(e)},setState:function(e){var t=this.state();return t&&e===t.id||!this.states||!this.states.get(e)||(t&&(t.trigger("deactivate"),this._lastState=t.id),this._state=e,this.state().trigger("activate")),this},lastState:function(){if(this._lastState)return this.state(this._lastState)}}),_.each(["on","off","trigger"],function(e){t.prototype[e]=function(){return this.states=this.states||new Backbone.Collection,this.states[e].apply(this.states,arguments),this}}),e.exports=t},6172:e=>{var t=wp.media.controller.Cropper.extend({activate:function(){this.frame.on("content:create:crop",this.createCropContent,this),this.frame.on("close",this.removeCropper,this),this.set("selection",new Backbone.Collection(this.frame._selection.single))},createCropContent:function(){this.cropperView=new wp.media.view.SiteIconCropper({controller:this,attachment:this.get("selection").first()}),this.cropperView.on("image-loaded",this.createCropToolbar,this),this.frame.content.set(this.cropperView)},doCrop:function(e){var t=e.get("cropDetails"),i=this.get("control");return t.dst_width=i.params.width,t.dst_height=i.params.height,wp.ajax.post("crop-image",{nonce:e.get("nonces").edit,id:e.get("id"),context:"site-icon",cropDetails:t})}});e.exports=t},6327:e=>{var t=wp.media.view.MenuItem.extend({click:function(){var e=this.options.contentMode;e&&this.controller.content.mode(e)}});e.exports=t},6442:e=>{var t=wp.media.View.extend({className:"upload-error",template:wp.template("uploader-status-error")});e.exports=t},6472:e=>{var t=wp.media.view.l10n,i=wp.media.view.AttachmentFilters.extend({id:"media-attachment-date-filters",createFilters:function(){var i={};_.each(wp.media.view.settings.months||{},function(e,t){i[t]={text:e.text,props:{year:e.year,monthnum:e.month}}}),i.all={text:t.allDates,props:{monthnum:!1,year:!1},priority:10},this.filters=i}});e.exports=i},6829:e=>{var s=wp.media.View,o=wp.media.view.settings.mediaTrash,a=wp.media.view.l10n,n=jQuery,i=wp.media.view.settings.infiniteScrolling,r=wp.i18n.__,t=wp.i18n.sprintf,l=s.extend({tagName:"div",className:"attachments-browser",initialize:function(){_.defaults(this.options,{filters:!1,search:!0,date:!0,display:!1,sidebar:!0,AttachmentView:wp.media.view.Attachment.Library}),this.controller.on("toggle:upload:attachment",this.toggleUploader,this),this.controller.on("edit:selection",this.editSelection),this.options.sidebar&&"errors"===this.options.sidebar&&this.createSidebar(),this.controller.isModeActive("grid")?(this.createUploader(),this.createToolbar()):(this.createToolbar(),this.createUploader()),this.createAttachmentsHeading(),this.createAttachmentsWrapperView(),i||(this.$el.addClass("has-load-more"),this.createLoadMoreView()),this.options.sidebar&&"errors"!==this.options.sidebar&&this.createSidebar(),this.updateContent(),i||this.updateLoadMoreView(),this.options.sidebar&&"errors"!==this.options.sidebar||(this.$el.addClass("hide-sidebar"),"errors"===this.options.sidebar&&this.$el.addClass("sidebar-for-errors")),this.collection.on("add remove reset",this.updateContent,this),i||this.collection.on("add remove reset",this.updateLoadMoreView,this),this.collection.on("attachments:received",this.announceSearchResults,this)},announceSearchResults:_.debounce(function(){var e,t=r("Number of media items displayed: %d. Click load more for more results.");i&&(t=r("Number of media items displayed: %d. Scroll the page for more results.")),this.collection.mirroring&&this.collection.mirroring.args.s&&(0===(e=this.collection.length)?wp.a11y.speak(a.noMediaTryNewSearch):this.collection.hasMore()?wp.a11y.speak(t.replace("%d",e)):wp.a11y.speak(a.mediaFound.replace("%d",e)))},200),editSelection:function(e){e.$(".media-button-backToLibrary").focus()},dispose:function(){return this.options.selection.off(null,null,this),s.prototype.dispose.apply(this,arguments),this},createToolbar:function(){var e,t=-1!==n.inArray(this.options.filters,["uploaded","all"]),i={controller:this.controller};this.controller.isModeActive("grid")&&(i.className="media-toolbar wp-filter"),this.toolbar=new wp.media.view.Toolbar(i),this.views.add(this.toolbar),this.toolbar.set("spinner",new wp.media.view.Spinner({priority:-20})),(t||this.options.date)&&this.toolbar.set("filters-heading",new wp.media.view.Heading({priority:-100,text:a.filterAttachments,level:"h2",className:"media-attachments-filter-heading"}).render()),t&&(this.toolbar.set("filtersLabel",new wp.media.view.Label({value:a.filterByType,attributes:{for:"media-attachment-filters"},priority:-80}).render()),"uploaded"===this.options.filters?this.toolbar.set("filters",new wp.media.view.AttachmentFilters.Uploaded({controller:this.controller,model:this.collection.props,priority:-80}).render()):(e=new wp.media.view.AttachmentFilters.All({controller:this.controller,model:this.collection.props,priority:-80}),this.toolbar.set("filters",e.render()))),this.controller.isModeActive("grid")?(i=s.extend({className:"view-switch media-grid-view-switch",template:wp.template("media-library-view-switcher")}),this.toolbar.set("libraryViewSwitcher",new i({controller:this.controller,priority:-90}).render()),this.toolbar.set("dateFilterLabel",new wp.media.view.Label({value:a.filterByDate,attributes:{for:"media-attachment-date-filters"},priority:-75}).render()),this.toolbar.set("dateFilter",new wp.media.view.DateFilter({controller:this.controller,model:this.collection.props,priority:-75}).render()),this.toolbar.set("selectModeToggleButton",new wp.media.view.SelectModeToggleButton({text:a.bulkSelect,controller:this.controller,priority:-70}).render()),this.toolbar.set("deleteSelectedButton",new wp.media.view.DeleteSelectedButton({filters:e,style:"primary",disabled:!0,text:o?a.trashSelected:a.deletePermanently,controller:this.controller,priority:-80,click:function(){var t=[],i=[],e=this.controller.state().get("selection"),s=this.controller.state().get("library");!e.length||!o&&!window.confirm(a.warnBulkDelete)||o&&"trash"!==e.at(0).get("status")&&!window.confirm(a.warnBulkTrash)||(e.each(function(e){e.get("nonces").delete?o&&"trash"===e.get("status")?(e.set("status","inherit"),t.push(e.save()),i.push(e)):o?(e.set("status","trash"),t.push(e.save()),i.push(e)):e.destroy({wait:!0}):i.push(e)}),t.length?(e.remove(i),n.when.apply(null,t).then(_.bind(function(){s._requery(!0),this.controller.trigger("selection:action:done")},this))):this.controller.trigger("selection:action:done"))}}).render()),o&&this.toolbar.set("deleteSelectedPermanentlyButton",new wp.media.view.DeleteSelectedPermanentlyButton({filters:e,style:"link button-link-delete",disabled:!0,text:a.deletePermanently,controller:this.controller,priority:-55,click:function(){var t=[],i=[],e=this.controller.state().get("selection");e.length&&window.confirm(a.warnBulkDelete)&&(e.each(function(e){(e.get("nonces").delete?i:t).push(e)}),t.length&&e.remove(t),i.length)&&n.when.apply(null,i.map(function(e){return e.destroy()})).then(_.bind(function(){this.controller.trigger("selection:action:done")},this))}}).render())):this.options.date&&(this.toolbar.set("dateFilterLabel",new wp.media.view.Label({value:a.filterByDate,attributes:{for:"media-attachment-date-filters"},priority:-75}).render()),this.toolbar.set("dateFilter",new wp.media.view.DateFilter({controller:this.controller,model:this.collection.props,priority:-75}).render())),this.options.search&&(this.toolbar.set("searchLabel",new wp.media.view.Label({value:a.searchLabel,className:"media-search-input-label",attributes:{for:"media-search-input"},priority:60}).render()),this.toolbar.set("search",new wp.media.view.Search({controller:this.controller,model:this.collection.props,priority:60}).render())),this.options.dragInfo&&this.toolbar.set("dragInfo",new s({el:n('<div class="instructions">'+a.dragInfo+"</div>")[0],priority:-40})),this.options.suggestedWidth&&this.options.suggestedHeight&&this.toolbar.set("suggestedDimensions",new s({el:n('<div class="instructions">'+a.suggestedDimensions.replace("%1$s",this.options.suggestedWidth).replace("%2$s",this.options.suggestedHeight)+"</div>")[0],priority:-40}))},updateContent:function(){var e=this,t=this.controller.isModeActive("grid")?e.attachmentsNoResults:e.uploader;this.collection.length?(t.$el.addClass("hidden"),e.toolbar.get("spinner").hide(),this.toolbar.$(".media-bg-overlay").hide()):(this.toolbar.get("spinner").show(),this.toolbar.$(".media-bg-overlay").show(),this.dfd=this.collection.more().done(function(){e.collection.length?t.$el.addClass("hidden"):t.$el.removeClass("hidden"),e.toolbar.get("spinner").hide(),e.toolbar.$(".media-bg-overlay").hide()}))},createUploader:function(){this.uploader=new wp.media.view.UploaderInline({controller:this.controller,status:!1,message:this.controller.isModeActive("grid")?"":a.noItemsFound,canClose:this.controller.isModeActive("grid")}),this.uploader.$el.addClass("hidden"),this.views.add(this.uploader)},toggleUploader:function(){this.uploader.$el.hasClass("hidden")?this.uploader.show():this.uploader.hide()},createAttachmentsWrapperView:function(){this.attachmentsWrapper=new wp.media.View({className:"attachments-wrapper"}),this.views.add(this.attachmentsWrapper),this.createAttachments()},createAttachments:function(){this.attachments=new wp.media.view.Attachments({controller:this.controller,collection:this.collection,selection:this.options.selection,model:this.model,sortable:this.options.sortable,scrollElement:this.options.scrollElement,idealColumnWidth:this.options.idealColumnWidth,AttachmentView:this.options.AttachmentView}),this.controller.on("attachment:keydown:arrow",_.bind(this.attachments.arrowEvent,this.attachments)),this.controller.on("attachment:details:shift-tab",_.bind(this.attachments.restoreFocus,this.attachments)),this.views.add(".attachments-wrapper",this.attachments),this.controller.isModeActive("grid")&&(this.attachmentsNoResults=new s({controller:this.controller,tagName:"p"}),this.attachmentsNoResults.$el.addClass("hidden no-media"),this.attachmentsNoResults.$el.html(a.noMedia),this.views.add(this.attachmentsNoResults))},createLoadMoreView:function(){var e=this;this.loadMoreWrapper=new s({controller:this.controller,className:"load-more-wrapper"}),this.loadMoreCount=new s({controller:this.controller,tagName:"p",className:"load-more-count hidden"}),this.loadMoreButton=new wp.media.view.Button({text:r("Load more"),className:"load-more hidden",style:"primary",size:"",click:function(){e.loadMoreAttachments()}}),this.loadMoreSpinner=new wp.media.view.Spinner,this.loadMoreJumpToFirst=new wp.media.view.Button({text:r("Jump to first loaded item"),className:"load-more-jump hidden",size:"",click:function(){e.jumpToFirstAddedItem()}}),this.views.add(".attachments-wrapper",this.loadMoreWrapper),this.views.add(".load-more-wrapper",this.loadMoreSpinner),this.views.add(".load-more-wrapper",this.loadMoreCount),this.views.add(".load-more-wrapper",this.loadMoreButton),this.views.add(".load-more-wrapper",this.loadMoreJumpToFirst)},updateLoadMoreView:_.debounce(function(){this.loadMoreButton.$el.addClass("hidden"),this.loadMoreCount.$el.addClass("hidden"),this.loadMoreJumpToFirst.$el.addClass("hidden").prop("disabled",!0),this.collection.getTotalAttachments()&&(this.collection.length&&(this.loadMoreCount.$el.text(t(r("Showing %1$s of %2$s media items"),this.collection.length,this.collection.getTotalAttachments())),this.loadMoreCount.$el.removeClass("hidden")),this.collection.hasMore()&&this.loadMoreButton.$el.removeClass("hidden"),this.firstAddedMediaItem=this.$el.find(".attachment").eq(this.firstAddedMediaItemIndex),this.firstAddedMediaItem.length&&(this.firstAddedMediaItem.addClass("new-media"),this.loadMoreJumpToFirst.$el.removeClass("hidden").prop("disabled",!1)),this.firstAddedMediaItem.length)&&!this.collection.hasMore()&&this.loadMoreJumpToFirst.$el.trigger("focus")},10),loadMoreAttachments:function(){var e=this;this.collection.hasMore()&&(this.firstAddedMediaItemIndex=this.collection.length,this.$el.addClass("more-loaded"),this.collection.each(function(e){e=e.attributes.id;n('[data-id="'+e+'"]').addClass("found-media")}),e.loadMoreSpinner.show(),this.collection.once("attachments:received",function(){e.loadMoreSpinner.hide()}),this.collection.more())},jumpToFirstAddedItem:function(){this.firstAddedMediaItem.focus()},createAttachmentsHeading:function(){this.attachmentsHeading=new wp.media.view.Heading({text:a.attachmentsList,level:"h2",className:"media-views-heading screen-reader-text"}),this.views.add(this.attachmentsHeading)},createSidebar:function(){var e=this.options.selection,t=this.sidebar=new wp.media.view.Sidebar({controller:this.controller});this.views.add(t),this.controller.uploader&&t.set("uploads",new wp.media.view.UploaderStatus({controller:this.controller,priority:40})),e.on("selection:single",this.createSingle,this),e.on("selection:unsingle",this.disposeSingle,this),e.single()&&this.createSingle()},createSingle:function(){var e=this.sidebar,t=this.options.selection.single();e.set("details",new wp.media.view.Attachment.Details({controller:this.controller,model:t,priority:80})),e.set("compat",new wp.media.view.AttachmentCompat({controller:this.controller,model:t,priority:120})),this.options.display&&e.set("display",new wp.media.view.Settings.AttachmentDisplay({controller:this.controller,model:this.model.display(t),attachment:t,priority:160,userSettings:this.model.get("displayUserSettings")})),"insert"===this.model.id&&e.$el.addClass("visible")},disposeSingle:function(){var e=this.sidebar;e.unset("details"),e.unset("compat"),e.unset("display"),e.$el.removeClass("visible")}});e.exports=l},7127:e=>{var i=wp.media.model.Selection,s=wp.media.controller.Library,t=wp.media.view.l10n,t=s.extend({defaults:_.defaults({id:"gallery-library",title:t.addToGalleryTitle,multiple:"add",filterable:"uploaded",menu:"gallery",toolbar:"gallery-add",priority:100,syncSelection:!1},s.prototype.defaults),initialize:function(){this.get("library")||this.set("library",wp.media.query({type:"image"})),s.prototype.initialize.apply(this,arguments)},activate:function(){var e=this.get("library"),t=this.frame.state("gallery-edit").get("library");this.editLibrary&&this.editLibrary!==t&&e.unobserve(this.editLibrary),e.validator=function(e){return!!this.mirroring.get(e.cid)&&!t.get(e.cid)&&i.prototype.validator.apply(this,arguments)},e.reset(e.mirroring.models,{silent:!0}),e.observe(t),this.editLibrary=t,s.prototype.activate.apply(this,arguments)}});e.exports=t},7145:e=>{var s=wp.media.model.Selection,o=wp.media.controller.Library,t=o.extend({defaults:_.defaults({multiple:"add",filterable:"uploaded",priority:100,syncSelection:!1},o.prototype.defaults),initialize:function(){var e=this.get("collectionType");"video"===this.get("type")&&(e="video-"+e),this.set("id",e+"-library"),this.set("toolbar",e+"-add"),this.set("menu",e),this.get("library")||this.set("library",wp.media.query({type:this.get("type")})),o.prototype.initialize.apply(this,arguments)},activate:function(){var e=this.get("library"),t=this.get("editLibrary"),i=this.frame.state(this.get("collectionType")+"-edit").get("library");t&&t!==i&&e.unobserve(t),e.validator=function(e){return!!this.mirroring.get(e.cid)&&!i.get(e.cid)&&s.prototype.validator.apply(this,arguments)},e.reset(e.mirroring.models,{silent:!0}),e.observe(i),this.set("editLibrary",i),o.prototype.activate.apply(this,arguments)}});e.exports=t},7266:e=>{var t=wp.media.view.Settings.extend({className:"collection-settings gallery-settings",template:wp.template("gallery-settings")});e.exports=t},7327:e=>{var t=wp.media.View,i=jQuery,s=wp.media.view.l10n,o=t.extend({tagName:"span",className:"embed-url",events:{input:"url"},initialize:function(){this.$input=i('<input id="embed-url-field" type="url" />').attr("aria-label",s.insertFromUrlTitle).val(this.model.get("url")),this.input=this.$input[0],this.spinner=i('<span class="spinner" />')[0],this.$el.append([this.input,this.spinner]),this.listenTo(this.model,"change:url",this.render),this.model.get("url")&&_.delay(_.bind(function(){this.model.trigger("change:url")},this),500)},render:function(){var e=this.$input;if(!e.is(":focus"))return this.model.get("url")?this.input.value=this.model.get("url"):this.input.setAttribute("placeholder","https://"),t.prototype.render.apply(this,arguments),this},url:function(e){e=e.target.value||"";this.model.set("url",e.trim())}});e.exports=o},7349:e=>{var t=wp.media.view.l10n,i=wp.media.view.AttachmentFilters.extend({createFilters:function(){var i={},e=window.userSettings?parseInt(window.userSettings.uid,10):0;_.each(wp.media.view.settings.mimeTypes||{},function(e,t){i[t]={text:e,props:{status:null,type:t,uploadedTo:null,orderby:"date",order:"DESC",author:null}}}),i.all={text:t.allMediaItems,props:{status:null,type:null,uploadedTo:null,orderby:"date",order:"DESC",author:null},priority:10},wp.media.view.settings.post.id&&(i.uploaded={text:t.uploadedToThisPost,props:{status:null,type:null,uploadedTo:wp.media.view.settings.post.id,orderby:"menuOrder",order:"ASC",author:null},priority:20}),i.unattached={text:t.unattached,props:{status:null,uploadedTo:0,type:null,orderby:"menuOrder",order:"ASC",author:null},priority:50},e&&(i.mine={text:t.mine,props:{status:null,type:null,uploadedTo:null,orderby:"date",order:"DESC",author:e},priority:50}),wp.media.view.settings.mediaTrash&&this.controller.isModeActive("grid")&&(i.trash={text:t.trash,props:{uploadedTo:null,status:"trash",type:null,orderby:"date",order:"DESC",author:null},priority:50}),this.filters=i}});e.exports=i},7637:e=>{var t=wp.media.View,i=wp.media.view.UploaderStatus,s=wp.media.view.l10n,o=jQuery,a=t.extend({className:"crop-content",template:wp.template("crop-content"),initialize:function(){_.bindAll(this,"onImageLoad")},ready:function(){this.controller.frame.on("content:error:crop",this.onError,this),this.$image=this.$el.find(".crop-image"),this.$image.on("load",this.onImageLoad),o(window).on("resize.cropper",_.debounce(this.onImageLoad,250))},remove:function(){o(window).off("resize.cropper"),this.$el.remove(),this.$el.off(),t.prototype.remove.apply(this,arguments)},prepare:function(){return{title:s.cropYourImage,url:this.options.attachment.get("url")}},onImageLoad:function(){var i,e=this.controller.get("imgSelectOptions");"function"==typeof e&&(e=e(this.options.attachment,this.controller)),e=_.extend(e,{parent:this.$el,onInit:function(){var t=i.getOptions().aspectRatio;this.parent.children().on("mousedown touchstart",function(e){!t&&e.shiftKey&&i.setOptions({aspectRatio:"1:1"})}),this.parent.children().on("mouseup touchend",function(){i.setOptions({aspectRatio:t||!1})})}}),this.trigger("image-loaded"),i=this.controller.imgSelect=this.$image.imgAreaSelect(e)},onError:function(){var e=this.options.attachment.get("filename");this.views.add(".upload-errors",new wp.media.view.UploaderStatusError({filename:i.prototype.filename(e),message:window._wpMediaViewsL10n.cropError}),{at:0})}});e.exports=a},7656:e=>{var t=wp.media.view.Settings,i=t.extend({className:"attachment-display-settings",template:wp.template("attachment-display-settings"),initialize:function(){var e=this.options.attachment;_.defaults(this.options,{userSettings:!1}),t.prototype.initialize.apply(this,arguments),this.listenTo(this.model,"change:link",this.updateLinkTo),e&&e.on("change:uploading",this.render,this)},dispose:function(){var e=this.options.attachment;e&&e.off(null,null,this),t.prototype.dispose.apply(this,arguments)},render:function(){var e=this.options.attachment;return e&&_.extend(this.options,{sizes:e.get("sizes"),type:e.get("type")}),t.prototype.render.call(this),this.updateLinkTo(),this},updateLinkTo:function(){var e=this.model.get("link"),t=this.$(".link-to-custom"),i=this.options.attachment;"none"===e||"embed"===e||!i&&"custom"!==e?t.closest(".setting").addClass("hidden"):(i&&("post"===e?t.val(i.get("link")):"file"===e?t.val(i.get("url")):this.model.get("linkUrl")||t.val("http://"),t.prop("readonly","custom"!==e)),t.closest(".setting").removeClass("hidden"),t.length&&t[0].scrollIntoView())}});e.exports=i},7709:e=>{var i=jQuery,t=wp.media.View.extend({tagName:"select",className:"attachment-filters",id:"media-attachment-filters",events:{change:"change"},keys:[],initialize:function(){this.createFilters(),_.extend(this.filters,this.options.filters),this.$el.html(_.chain(this.filters).map(function(e,t){return{el:i("<option></option>").val(t).html(e.text)[0],priority:e.priority||50}},this).sortBy("priority").pluck("el").value()),this.listenTo(this.model,"change",this.select),this.select()},createFilters:function(){this.filters={}},change:function(){var e=this.filters[this.el.value];e&&this.model.set(e.props)},select:function(){var e=this.model,i="all",s=e.toJSON();_.find(this.filters,function(e,t){if(_.all(e.props,function(e,t){return e===(_.isUndefined(s[t])?null:s[t])}))return i=t}),this.$el.val(i)}});e.exports=t},7810:e=>{var t=wp.media.View,n=jQuery,t=t.extend({className:"site-icon-preview-crop-modal",template:wp.template("site-icon-preview-crop"),ready:function(){this.controller.imgSelect.setOptions({onInit:this.updatePreview,onSelectChange:this.updatePreview})},prepare:function(){return{url:this.options.attachment.get("url")}},updatePreview:function(e,t){var i=64/t.width,s=64/t.height,o=24/t.width,a=24/t.height;n("#preview-app-icon").css({width:Math.round(i*this.imageWidth)+"px",height:Math.round(s*this.imageHeight)+"px",marginLeft:"-"+Math.round(i*t.x1)+"px",marginTop:"-"+Math.round(s*t.y1)+"px"}),n("#preview-favicon").css({width:Math.round(o*this.imageWidth)+"px",height:Math.round(a*this.imageHeight)+"px",marginLeft:"-"+Math.round(o*t.x1)+"px",marginTop:"-"+Math.floor(a*t.y1)+"px"})}});e.exports=t},8065:e=>{var t=wp.media.controller.Library,i=t.extend({defaults:_.defaults({filterable:"uploaded",displaySettings:!1,priority:80,syncSelection:!1},t.prototype.defaults),initialize:function(e){this.media=e.media,this.type=e.type,this.set("library",wp.media.query({type:this.type})),t.prototype.initialize.apply(this,arguments)},activate:function(){wp.media.frame.lastMime&&(this.set("library",wp.media.query({type:wp.media.frame.lastMime})),delete wp.media.frame.lastMime),t.prototype.activate.apply(this,arguments)}});e.exports=i},8142:e=>{var t=wp.media.View,a=jQuery,i=wp.media.view.settings.infiniteScrolling,s=t.extend({tagName:"ul",className:"attachments",attributes:{tabIndex:-1},initialize:function(){this.el.id=_.uniqueId("__attachments-view-"),_.defaults(this.options,{infiniteScrolling:i||!1,refreshSensitivity:wp.media.isTouchDevice?300:200,refreshThreshold:3,AttachmentView:wp.media.view.Attachment,sortable:!1,resize:!0,idealColumnWidth:a(window).width()<640?135:150}),this._viewsByCid={},this.$window=a(window),this.resizeEvent="resize.media-modal-columns",this.collection.on("add",function(e){this.views.add(this.createAttachmentView(e),{at:this.collection.indexOf(e)})},this),this.collection.on("remove",function(e){var t=this._viewsByCid[e.cid];delete this._viewsByCid[e.cid],t&&t.remove()},this),this.collection.on("reset",this.render,this),this.controller.on("library:selection:add",this.attachmentFocus,this),this.options.infiniteScrolling&&(this.scroll=_.chain(this.scroll).bind(this).throttle(this.options.refreshSensitivity).value(),this.options.scrollElement=this.options.scrollElement||this.el,a(this.options.scrollElement).on("scroll",this.scroll)),this.initSortable(),_.bindAll(this,"setColumns"),this.options.resize&&(this.on("ready",this.bindEvents),this.controller.on("open",this.setColumns),_.defer(this.setColumns,this))},bindEvents:function(){this.$window.off(this.resizeEvent).on(this.resizeEvent,_.debounce(this.setColumns,50))},attachmentFocus:function(){this.columns&&this.$el.focus()},restoreFocus:function(){this.$("li.selected:first").focus()},arrowEvent:function(e){var t=this.$el.children("li"),i=this.columns,s=t.filter(":focus").index(),o=s+1<=i?1:Math.ceil((s+1)/i);if(-1!==s){if(37===e.keyCode){if(0===s)return;t.eq(s-1).focus()}if(38===e.keyCode){if(1===o)return;t.eq(s-i).focus()}if(39===e.keyCode){if(t.length===s)return;t.eq(s+1).focus()}40===e.keyCode&&Math.ceil(t.length/i)!==o&&t.eq(s+i).focus()}},dispose:function(){this.collection.props.off(null,null,this),this.options.resize&&this.$window.off(this.resizeEvent),t.prototype.dispose.apply(this,arguments)},setColumns:function(){var e=this.columns,t=this.$el.width();t&&(this.columns=Math.min(Math.round(t/this.options.idealColumnWidth),12)||1,e&&e===this.columns||this.$el.closest(".media-frame-content").attr("data-columns",this.columns))},initSortable:function(){var o=this.collection;this.options.sortable&&a.fn.sortable&&(this.$el.sortable(_.extend({disabled:!!o.comparator,tolerance:"pointer",start:function(e,t){t.item.data("sortableIndexStart",t.item.index())},update:function(e,t){var i=o.at(t.item.data("sortableIndexStart")),s=o.comparator;delete o.comparator,o.remove(i,{silent:!0}),o.add(i,{silent:!0,at:t.item.index()}),o.comparator=s,o.trigger("reset",o),o.saveMenuOrder()}},this.options.sortable)),o.props.on("change:orderby",function(){this.$el.sortable("option","disabled",!!o.comparator)},this),this.collection.props.on("change:orderby",this.refreshSortable,this),this.refreshSortable())},refreshSortable:function(){var e;this.options.sortable&&a.fn.sortable&&(e="menuOrder"===(e=this.collection).props.get("orderby")||!e.comparator,this.$el.sortable("option","disabled",!e))},createAttachmentView:function(e){var t=new this.options.AttachmentView({controller:this.controller,model:e,collection:this.collection,selection:this.options.selection});return this._viewsByCid[e.cid]=t},prepare:function(){this.collection.length?this.views.set(this.collection.map(this.createAttachmentView,this)):(this.views.unset(),this.options.infiniteScrolling&&this.collection.more().done(this.scroll))},ready:function(){this.options.infiniteScrolling&&this.scroll()},scroll:function(){var e,t=this,i=this.options.scrollElement,s=i.scrollTop;i===document&&(i=document.body,s=a(document).scrollTop()),a(i).is(":visible")&&this.collection.hasMore()&&(e=this.views.parent.toolbar,i.scrollHeight-(s+i.clientHeight)<i.clientHeight/3&&e.get("spinner").show(),i.scrollHeight<s+i.clientHeight*this.options.refreshThreshold)&&this.collection.more().done(function(){t.scroll(),e.get("spinner").hide()})}});e.exports=s},8197:e=>{var t=wp.media.View,i=t.extend({className:"media-uploader-status",template:wp.template("uploader-status"),events:{"click .upload-dismiss-errors":"dismiss"},initialize:function(){this.queue=wp.Uploader.queue,this.queue.on("add remove reset",this.visibility,this),this.queue.on("add remove reset change:percent",this.progress,this),this.queue.on("add remove reset change:uploading",this.info,this),this.errors=wp.Uploader.errors,this.errors.reset(),this.errors.on("add remove reset",this.visibility,this),this.errors.on("add",this.error,this)},dispose:function(){return wp.Uploader.queue.off(null,null,this),t.prototype.dispose.apply(this,arguments),this},visibility:function(){this.$el.toggleClass("uploading",!!this.queue.length),this.$el.toggleClass("errors",!!this.errors.length),this.$el.toggle(!!this.queue.length||!!this.errors.length)},ready:function(){_.each({$bar:".media-progress-bar div",$index:".upload-index",$total:".upload-total",$filename:".upload-filename"},function(e,t){this[t]=this.$(e)},this),this.visibility(),this.progress(),this.info()},progress:function(){var e=this.queue,t=this.$bar;t&&e.length&&t.width(e.reduce(function(e,t){return t.get("uploading")?(t=t.get("percent"),e+(_.isNumber(t)?t:100)):e+100},0)/e.length+"%")},info:function(){var e,t=this.queue,i=0;t.length&&(e=this.queue.find(function(e,t){return i=t,e.get("uploading")}),this.$index)&&this.$total&&this.$filename&&(this.$index.text(i+1),this.$total.text(t.length),this.$filename.html(e?this.filename(e.get("filename")):""))},filename:function(e){return _.escape(e)},error:function(e){var t=new wp.media.view.UploaderStatusError({filename:this.filename(e.get("file").name),message:e.get("message")}),i=this.$el.find("button");this.views.add(".upload-errors",t,{at:0}),_.delay(function(){i.trigger("focus"),wp.a11y.speak(e.get("message"),"assertive")},1e3)},dismiss:function(){var e=this.views.get(".upload-errors");e&&_.invoke(e,"remove"),wp.Uploader.errors.reset(),this.controller.modal&&this.controller.modal.focusManager.focus()}});e.exports=i},8232:e=>{var i=jQuery,t=wp.media.view.Settings.extend({className:"embed-link-settings",template:wp.template("embed-link-settings"),initialize:function(){this.listenTo(this.model,"change:url",this.updateoEmbed)},updateoEmbed:_.debounce(function(){var e=this.model.get("url");this.$(".embed-container").hide().find(".embed-preview").empty(),this.$(".setting").hide(),e&&(e.length<11||!e.match(/^http(s)?:\/\//))||this.fetch()},wp.media.controller.Embed.sensitivity),fetch:function(){var e,t=this.model.get("url");i("#embed-url-field").val()===t&&(this.dfd&&"pending"===this.dfd.state()&&this.dfd.abort(),(e=/https?:\/\/www\.youtube\.com\/embed\/([^/]+)/.exec(t))&&(t="https://www.youtube.com/watch?v="+e[1]),this.dfd=wp.apiRequest({url:wp.media.view.settings.oEmbedProxyUrl,data:{url:t,maxwidth:this.model.get("width"),maxheight:this.model.get("height")},type:"GET",dataType:"json",context:this}).done(function(e){this.renderoEmbed({data:{body:e.html||""}})}).fail(this.renderFail))},renderFail:function(e,t){"abort"!==t&&this.$(".link-text").show()},renderoEmbed:function(e){e=e&&e.data&&e.data.body||"";e?this.$(".embed-container").show().find(".embed-preview").html(e):this.renderFail()}});e.exports=t},8282:e=>{var i=wp.i18n._n,s=wp.i18n.sprintf,t=wp.media.View.extend({tagName:"div",className:"media-selection",template:wp.template("media-selection"),events:{"click .edit-selection":"edit","click .clear-selection":"clear"},initialize:function(){_.defaults(this.options,{editable:!1,clearable:!0}),this.attachments=new wp.media.view.Attachments.Selection({controller:this.controller,collection:this.collection,selection:this.collection,model:new Backbone.Model}),this.views.set(".selection-view",this.attachments),this.collection.on("add remove reset",this.refresh,this),this.controller.on("content:activate",this.refresh,this)},ready:function(){this.refresh()},refresh:function(){var e,t;this.$el.children().length&&(e=this.collection,t="edit-selection"===this.controller.content.mode(),this.$el.toggleClass("empty",!e.length),this.$el.toggleClass("one",1===e.length),this.$el.toggleClass("editing",t),this.$(".count").text(s(i("%s item selected","%s items selected",e.length),e.length)))},edit:function(e){e.preventDefault(),this.options.editable&&this.options.editable.call(this,this.collection)},clear:function(e){e.preventDefault(),this.collection.reset(),this.controller.modal.focusManager.focus()}});e.exports=t},8291:e=>{var t=jQuery,i=wp.media.View.extend({tagName:"div",className:"uploader-window",template:wp.template("uploader-window"),initialize:function(){var e;this.$browser=t('<button type="button" class="browser" />').hide().appendTo("body"),!(e=this.options.uploader=_.defaults(this.options.uploader||{},{dropzone:this.$el,browser:this.$browser,params:{}})).dropzone||e.dropzone instanceof t||(e.dropzone=t(e.dropzone)),this.controller.on("activate",this.refresh,this),this.controller.on("detach",function(){this.$browser.remove()},this)},refresh:function(){this.uploader&&this.uploader.refresh()},ready:function(){var e=wp.media.view.settings.post.id;this.uploader||(e&&(this.options.uploader.params.post_id=e),this.uploader=new wp.Uploader(this.options.uploader),(e=this.uploader.dropzone).on("dropzone:enter",_.bind(this.show,this)),e.on("dropzone:leave",_.bind(this.hide,this)),t(this.uploader).on("uploader:ready",_.bind(this._ready,this)))},_ready:function(){this.controller.trigger("uploader:ready")},show:function(){var e=this.$el.show();_.defer(function(){e.css({opacity:1})})},hide:function(){var e=this.$el.css({opacity:0});wp.media.transition(e).done(function(){"0"===e.css("opacity")&&e.hide()}),_.delay(function(){"0"===e.css("opacity")&&e.is(":visible")&&e.hide()},500)}});e.exports=i},8612:e=>{var t=wp.media.controller.Library,n=wp.media.view.l10n,r=jQuery,i=t.extend({defaults:{multiple:!1,sortable:!0,date:!1,searchable:!1,content:"browse",describe:!0,dragInfo:!0,idealColumnWidth:170,editing:!1,priority:60,SettingsView:!1,syncSelection:!1},initialize:function(){var e=this.get("collectionType");"video"===this.get("type")&&(e="video-"+e),this.set("id",e+"-edit"),this.set("toolbar",e+"-edit"),this.get("library")||this.set("library",new wp.media.model.Selection),this.get("AttachmentView")||this.set("AttachmentView",wp.media.view.Attachment.EditLibrary),t.prototype.initialize.apply(this,arguments)},activate:function(){this.get("library").props.set("type",this.get("type")),this.get("library").observe(wp.Uploader.queue),this.frame.on("content:render:browse",this.renderSettings,this),t.prototype.activate.apply(this,arguments)},deactivate:function(){this.get("library").unobserve(wp.Uploader.queue),this.frame.off("content:render:browse",this.renderSettings,this),t.prototype.deactivate.apply(this,arguments)},renderSettings:function(e){var t=this.get("library"),i=this.get("collectionType"),s=this.get("dragInfoText"),o=this.get("SettingsView"),a={};t&&e&&(t[i]=t[i]||new Backbone.Model,a[i]=new o({controller:this,model:t[i],priority:40}),e.sidebar.set(a),s&&e.toolbar.set("dragInfo",new wp.media.View({el:r('<div class="instructions">'+s+"</div>")[0],priority:-40})),e.toolbar.set("reverse",{text:n.reverseOrder,priority:80,click:function(){t.reset(t.toArray().reverse())}}))}});e.exports=i},8815:e=>{var t=wp.media.View.extend({tagName:"div",initialize:function(){this._views={},this.set(_.extend({},this._views,this.options.views),{silent:!0}),delete this.options.views,this.options.silent||this.render()},set:function(e,t,i){var s,o;return i=i||{},_.isObject(e)?_.each(e,function(e,t){this.set(t,e)},this):((t=t instanceof Backbone.View?t:this.toView(t,e,i)).controller=t.controller||this.controller,this.unset(e),s=t.options.priority||10,i=this.views.get()||[],_.find(i,function(e,t){if(e.options.priority>s)return o=t,!0}),this._views[e]=t,this.views.add(t,{at:_.isNumber(o)?o:i.length||0})),this},get:function(e){return this._views[e]},unset:function(e){var t=this.get(e);return t&&t.remove(),delete this._views[e],this},toView:function(e){return new wp.media.View(e)}});e.exports=t},9013:e=>{var t=wp.media.View.extend({tagName:"button",className:"media-menu-item",attributes:{type:"button",role:"tab"},events:{click:"_click"},_click:function(){var e=this.options.click;e?e.call(this):this.click()},click:function(){var e=this.options.state;e&&(this.controller.setState(e),this.views.parent.$el.removeClass("visible"))},render:function(){var e=this.options,t=e.state||e.contentMode;return e.text?this.$el.text(e.text):e.html&&this.$el.html(e.html),this.$el.attr("id","menu-item-"+t),this}});e.exports=t},9141:e=>{var t=wp.media.View.extend({tagName:"span",className:"spinner",spinnerTimeout:!1,delay:400,show:function(){return this.spinnerTimeout||(this.spinnerTimeout=_.delay(function(e){e.addClass("is-active")},this.delay,this.$el)),this},hide:function(){return this.$el.removeClass("is-active"),this.spinnerTimeout=clearTimeout(this.spinnerTimeout),this}});e.exports=t},9458:e=>{var t=wp.media.view.Toolbar,i=wp.media.view.l10n,s=t.extend({initialize:function(){var e=this.options;_.bindAll(this,"clickSelect"),_.defaults(e,{event:"select",state:!1,reset:!0,close:!0,text:i.select,requires:{selection:!0}}),e.items=_.defaults(e.items||{},{select:{style:"primary",text:e.text,priority:80,click:this.clickSelect,requires:e.requires}}),t.prototype.initialize.apply(this,arguments)},clickSelect:function(){var e=this.options,t=this.controller;e.close&&t.close(),e.event&&t.state().trigger(e.event),e.state&&t.setState(e.state),e.reset&&t.reset()}});e.exports=s},9660:e=>{var t=wp.media.controller.Cropper.extend({doCrop:function(e){var t=e.get("cropDetails"),i=this.get("control"),s=t.width/t.height;return i.params.flex_width&&i.params.flex_height?(t.dst_width=t.width,t.dst_height=t.height):(t.dst_width=i.params.flex_width?i.params.height*s:i.params.width,t.dst_height=i.params.flex_height?i.params.width/s:i.params.height),wp.ajax.post("crop-image",{wp_customize:"on",nonce:e.get("nonces").edit,id:e.get("id"),context:i.id,cropDetails:t})}});e.exports=t},9875:e=>{function t(e){_.extend(this,_.pick(e||{},"id","view","selector"))}t.extend=Backbone.Model.extend,_.extend(t.prototype,{mode:function(e){return e?(e!==this._mode&&(this.trigger("deactivate"),this._mode=e,this.render(e),this.trigger("activate")),this):this._mode},render:function(e){return e&&e!==this._mode?this.mode(e):(this.trigger("create",e={view:null}),this.trigger("render",e=e.view),e&&this.set(e),this)},get:function(){return this.view.views.first(this.selector)},set:function(e,t){return t&&(t.add=!1),this.view.views.set(this.selector,e,t)},trigger:function(e){var t,i;if(this._mode)return i=_.toArray(arguments),t=this.id+":"+e,i[0]=t+":"+this._mode,this.view.trigger.apply(this.view,i),i[0]=t,this.view.trigger.apply(this.view,i),this}}),e.exports=t}},s={};function o(e){var t=s[e];return void 0!==t||(t=s[e]={exports:{}},i[e](t,t.exports,o)),t.exports}var t,e,a,n=wp.media,r=jQuery;n.isTouchDevice="ontouchend"in document,e=n.view.l10n=window._wpMediaViewsL10n||{},n.view.settings=e.settings||{},delete e.settings,n.model.settings.post=n.view.settings.post,r.support.transition=(t=document.documentElement.style,e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"},(a=_.find(_.keys(e),function(e){return!_.isUndefined(t[e])}))&&{end:e[a]}),n.events=_.extend({},Backbone.Events),n.transition=function(e,t){var i=r.Deferred();return t=t||2e3,r.support.transition?((e=e instanceof r?e:r(e)).first().one(r.support.transition.end,i.resolve),_.delay(i.resolve,t)):i.resolve(),i.promise()},n.controller.Region=o(9875),n.controller.StateMachine=o(6150),n.controller.State=o(5694),n.selectionSync=o(4181),n.controller.Library=o(472),n.controller.ImageDetails=o(705),n.controller.GalleryEdit=o(2038),n.controller.GalleryAdd=o(7127),n.controller.CollectionEdit=o(8612),n.controller.CollectionAdd=o(7145),n.controller.FeaturedImage=o(1169),n.controller.ReplaceImage=o(2275),n.controller.EditImage=o(5663),n.controller.MediaLibrary=o(8065),n.controller.Embed=o(4910),n.controller.Cropper=o(5422),n.controller.CustomizeImageCropper=o(9660),n.controller.SiteIconCropper=o(6172),n.View=o(4747),n.view.Frame=o(1061),n.view.MediaFrame=o(2836),n.view.MediaFrame.Select=o(455),n.view.MediaFrame.Post=o(4274),n.view.MediaFrame.ImageDetails=o(5424),n.view.Modal=o(2621),n.view.FocusManager=o(718),n.view.UploaderWindow=o(8291),n.view.EditorUploader=o(3674),n.view.UploaderInline=o(1753),n.view.UploaderStatus=o(8197),n.view.UploaderStatusError=o(6442),n.view.Toolbar=o(5275),n.view.Toolbar.Select=o(9458),n.view.Toolbar.Embed=o(397),n.view.Button=o(846),n.view.ButtonGroup=o(168),n.view.PriorityList=o(8815),n.view.MenuItem=o(9013),n.view.Menu=o(1),n.view.RouterItem=o(6327),n.view.Router=o(4783),n.view.Sidebar=o(1992),n.view.Attachment=o(4075),n.view.Attachment.Library=o(3443),n.view.Attachment.EditLibrary=o(5232),n.view.Attachments=o(8142),n.view.Search=o(2102),n.view.AttachmentFilters=o(7709),n.view.DateFilter=o(6472),n.view.AttachmentFilters.Uploaded=o(1368),n.view.AttachmentFilters.All=o(7349),n.view.AttachmentsBrowser=o(6829),n.view.Selection=o(8282),n.view.Attachment.Selection=o(3962),n.view.Attachments.Selection=o(3479),n.view.Attachment.EditSelection=o(4593),n.view.Settings=o(1915),n.view.Settings.AttachmentDisplay=o(7656),n.view.Settings.Gallery=o(7266),n.view.Settings.Playlist=o(2356),n.view.Attachment.Details=o(6090),n.view.AttachmentCompat=o(2982),n.view.Iframe=o(1982),n.view.Embed=o(5741),n.view.Label=o(4338),n.view.EmbedUrl=o(7327),n.view.EmbedLink=o(8232),n.view.EmbedImage=o(2395),n.view.ImageDetails=o(2650),n.view.Cropper=o(7637),n.view.SiteIconCropper=o(443),n.view.SiteIconPreview=o(7810),n.view.EditImage=o(6126),n.view.Spinner=o(9141),n.view.Heading=o(170)})();
/**
 * Enhanced Mobile Responsiveness and Accessibility Framework
 * 
 * Comprehensive responsive design and accessibility features for BusinessCraft AI
 */

/* === ACCESSIBILITY FOUNDATION === */

/* Screen Reader Only Content */
.sr-only,
.screen-reader-text {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only:focus,
.screen-reader-text:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 8px 16px !important;
    margin: 0 !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
    background: #000 !important;
    color: #fff !important;
    text-decoration: none !important;
    border-radius: 4px !important;
    z-index: 999999 !important;
}

/* Skip Links */
.skip-links {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 999999;
}

.skip-link {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: #000;
    color: #fff;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.skip-link:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    left: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Focus Management */
.focus-trap {
    position: relative;
}

.focus-trap::before,
.focus-trap::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
}

/* Enhanced Focus Indicators */
*:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    border-radius: 4px;
}

.btn:focus,
button:focus,
[role="button"]:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
    box-shadow: 0 0 0 1px #fff, 0 0 0 4px #667eea;
}

input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #667eea;
    outline-offset: 1px;
    border-color: #667eea;
    box-shadow: 0 0 0 1px #667eea;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .analytics-card,
    .notification-item,
    .template-card {
        border: 2px solid;
    }
    
    .btn {
        border: 2px solid;
        font-weight: bold;
    }
    
    .chart-container {
        border: 2px solid;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid #667eea;
        border-radius: 50%;
    }
    
    .loading-spinner::after {
        content: "Loading...";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.8em;
        color: #667eea;
    }
}

/* === RESPONSIVE FOUNDATION === */

/* Fluid Typography */
html {
    font-size: clamp(14px, 2.5vw, 18px);
}

h1 {
    font-size: clamp(1.8rem, 4vw, 3rem);
}

h2 {
    font-size: clamp(1.5rem, 3.5vw, 2.5rem);
}

h3 {
    font-size: clamp(1.3rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
}

/* Responsive Containers */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 clamp(1rem, 4vw, 2rem);
}

.container-fluid {
    width: 100%;
    padding: 0 clamp(0.5rem, 2vw, 1rem);
}

/* Responsive Grid System */
.grid {
    display: grid;
    gap: clamp(1rem, 3vw, 2rem);
}

.grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
}

/* Flexible Layouts */
.flex {
    display: flex;
    gap: clamp(0.5rem, 2vw, 1rem);
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-column {
    flex-direction: column;
}

.flex-center {
    align-items: center;
    justify-content: center;
}

/* === COMPONENT RESPONSIVENESS === */

/* Dashboard Components */
.bcai-component {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
}

.bcai-component.is-mobile {
    padding: clamp(0.5rem, 3vw, 1rem);
}

.bcai-component.is-desktop {
    padding: clamp(1rem, 4vw, 2rem);
}

/* Analytics Dashboard */
.analytics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 250px), 1fr));
    gap: clamp(1rem, 3vw, 1.5rem);
}

.analytics-card {
    min-height: 120px;
    padding: clamp(1rem, 4vw, 1.5rem);
}

.analytics-charts {
    display: grid;
    grid-template-columns: 1fr;
    gap: clamp(1rem, 3vw, 2rem);
}

@media (min-width: 768px) {
    .analytics-charts {
        grid-template-columns: 2fr 1fr;
    }
}

.chart-container {
    min-height: 300px;
    position: relative;
}

.chart-container canvas {
    max-height: 400px;
}

/* Notification Center */
.notification-preferences {
    padding: clamp(1rem, 4vw, 2rem);
}

.preference-items {
    display: flex;
    flex-direction: column;
    gap: clamp(0.75rem, 2vw, 1rem);
}

.notification-toggle {
    padding: clamp(0.75rem, 3vw, 1rem);
    border-radius: clamp(6px, 1vw, 12px);
}

.notifications-list {
    max-height: 60vh;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.notification-item {
    padding: clamp(0.75rem, 3vw, 1rem);
    border-radius: clamp(6px, 1vw, 8px);
}

/* Form Elements */
.form-group {
    margin-bottom: clamp(1rem, 3vw, 1.5rem);
}

.form-control {
    width: 100%;
    padding: clamp(0.5rem, 2vw, 0.75rem);
    font-size: clamp(0.9rem, 2vw, 1rem);
    border-radius: clamp(4px, 1vw, 8px);
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Buttons */
.btn {
    padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 4vw, 1.5rem);
    font-size: clamp(0.9rem, 2vw, 1rem);
    border-radius: clamp(4px, 1vw, 8px);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    line-height: 1.2;
    min-height: 44px; /* Touch target size */
    min-width: 44px;
}

.btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* === MOBILE-SPECIFIC OPTIMIZATIONS === */

/* Touch Targets */
@media (pointer: coarse) {
    .btn,
    .form-control,
    .notification-toggle,
    .tab-button {
        min-height: 44px;
        min-width: 44px;
    }
    
    .chart-container {
        touch-action: pan-y;
    }
    
    .notifications-list {
        touch-action: pan-y;
    }
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .dashboard-tabs {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .dashboard-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .tab-button {
        flex: 0 0 auto;
        scroll-snap-align: start;
        white-space: nowrap;
        padding: clamp(0.75rem, 3vw, 1rem) clamp(1rem, 4vw, 1.5rem);
    }
    
    .tab-content {
        padding: clamp(1rem, 4vw, 1.5rem);
    }
}

/* Mobile Charts */
@media (max-width: 768px) {
    .analytics-charts {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        min-height: 250px;
    }
    
    .insights-grid {
        grid-template-columns: 1fr 1fr;
        gap: clamp(0.75rem, 3vw, 1rem);
    }
}

/* Mobile Forms */
@media (max-width: 768px) {
    .frequency-setting {
        flex-direction: column;
        align-items: stretch;
        gap: clamp(0.5rem, 2vw, 0.75rem);
    }
    
    .notification-actions {
        flex-direction: column;
        gap: clamp(0.75rem, 3vw, 1rem);
    }
    
    .notification-actions .btn {
        width: 100%;
    }
}

/* === PRINT STYLES === */
@media print {
    .skip-links,
    .btn,
    .notification-actions,
    .chart-controls {
        display: none !important;
    }
    
    .analytics-card,
    .notification-item {
        break-inside: avoid;
        border: 1px solid #000 !important;
        background: #fff !important;
        color: #000 !important;
    }
    
    .chart-container {
        break-inside: avoid;
        border: 1px solid #000;
    }
    
    .analytics-summary {
        grid-template-columns: 1fr 1fr;
    }
    
    .analytics-charts {
        grid-template-columns: 1fr;
    }
}

/* === DARK MODE SUPPORT === */
@media (prefers-color-scheme: dark) {
    .bcai-component {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .analytics-card {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }
    
    .notification-item {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .form-control {
        background: #2d3748;
        border-color: #4a5568;
        color: #e0e0e0;
    }
    
    .btn {
        background: #4a5568;
        color: #e0e0e0;
    }
    
    .chart-container {
        background: #2d3748;
    }
}

/* === UTILITY CLASSES === */

/* Spacing */
.m-0 { margin: 0 !important; }
.m-1 { margin: clamp(0.25rem, 1vw, 0.5rem) !important; }
.m-2 { margin: clamp(0.5rem, 2vw, 1rem) !important; }
.m-3 { margin: clamp(0.75rem, 3vw, 1.5rem) !important; }
.m-4 { margin: clamp(1rem, 4vw, 2rem) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: clamp(0.25rem, 1vw, 0.5rem) !important; }
.p-2 { padding: clamp(0.5rem, 2vw, 1rem) !important; }
.p-3 { padding: clamp(0.75rem, 3vw, 1.5rem) !important; }
.p-4 { padding: clamp(1rem, 4vw, 2rem) !important; }

/* Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

@media (max-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
}

@media (min-width: 769px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
}

/* Text */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Accessibility Helpers */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.aria-expanded[aria-expanded="true"] + .collapse {
    display: block;
}

.aria-expanded[aria-expanded="false"] + .collapse {
    display: none;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.error {
    border-color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success States */
.success {
    border-color: #28a745 !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.success-message {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

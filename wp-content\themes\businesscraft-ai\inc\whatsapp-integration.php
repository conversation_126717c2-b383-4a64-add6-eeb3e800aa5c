<?php
/**
 * WhatsApp Business API Integration for ChatGABI AI
 * 
 * Provides WhatsApp messaging capabilities with:
 * - Multi-language support (English, Twi, Swahili, Yoruba, Zulu)
 * - African business context integration
 * - Credit system integration
 * - User authentication and management
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize WhatsApp integration
 */
function chatgabi_init_whatsapp_integration() {
    // Register REST API routes
    add_action('rest_api_init', 'chatgabi_register_whatsapp_routes');
    
    // Create database tables if needed
    chatgabi_create_whatsapp_tables();
    
    // Schedule cleanup tasks
    if (!wp_next_scheduled('chatgabi_whatsapp_cleanup')) {
        wp_schedule_event(time(), 'daily', 'chatgabi_whatsapp_cleanup');
    }
}
add_action('init', 'chatgabi_init_whatsapp_integration');

/**
 * Register WhatsApp REST API routes
 */
function chatgabi_register_whatsapp_routes() {
    // WhatsApp webhook endpoint
    register_rest_route('chatgabi/v1', '/whatsapp/webhook', array(
        'methods' => array('GET', 'POST'),
        'callback' => 'chatgabi_whatsapp_webhook_handler',
        'permission_callback' => '__return_true', // Webhook doesn't need user auth
    ));
    
    // WhatsApp send message endpoint (for admin/testing)
    register_rest_route('chatgabi/v1', '/whatsapp/send', array(
        'methods' => 'POST',
        'callback' => 'chatgabi_whatsapp_send_message',
        'permission_callback' => 'chatgabi_check_admin_permission',
        'args' => array(
            'phone' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'message' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ),
            'language' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'en',
                'enum' => array('en', 'tw', 'sw', 'yo', 'zu'),
            ),
        ),
    ));
    
    // WhatsApp user management endpoint
    register_rest_route('chatgabi/v1', '/whatsapp/users', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_whatsapp_users',
        'permission_callback' => 'chatgabi_check_admin_permission',
    ));
    
    // WhatsApp analytics endpoint
    register_rest_route('chatgabi/v1', '/whatsapp/analytics', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_whatsapp_analytics',
        'permission_callback' => 'chatgabi_check_admin_permission',
    ));
}

/**
 * Create WhatsApp-specific database tables
 */
function chatgabi_create_whatsapp_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Check if tables already exist
    $whatsapp_users_table = $wpdb->prefix . 'chatgabi_whatsapp_users';
    $whatsapp_conversations_table = $wpdb->prefix . 'chatgabi_whatsapp_conversations';

    $users_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$whatsapp_users_table'") === $whatsapp_users_table;
    $conversations_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$whatsapp_conversations_table'") === $whatsapp_conversations_table;

    // Create users table first (without foreign key constraints)
    if (!$users_table_exists) {
        $whatsapp_users_sql = "CREATE TABLE $whatsapp_users_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            phone_number varchar(20) NOT NULL,
            display_name varchar(100),
            profile_name varchar(100),
            language varchar(5) DEFAULT 'en',
            country varchar(50),
            preferred_sector varchar(100),
            credits decimal(10,2) DEFAULT 0,
            total_messages int(11) DEFAULT 0,
            last_message_at datetime,
            registration_source varchar(50) DEFAULT 'whatsapp',
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_phone (phone_number),
            KEY idx_language (language),
            KEY idx_country (country),
            KEY idx_status (status),
            KEY idx_created_at (created_at)
        ) $charset_collate;";

        $wpdb->query($whatsapp_users_sql);
    }

    // Create conversations table (without foreign key for compatibility)
    if (!$conversations_table_exists) {
        $whatsapp_conversations_sql = "CREATE TABLE $whatsapp_conversations_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            whatsapp_user_id bigint(20) NOT NULL,
            phone_number varchar(20) NOT NULL,
            message_id varchar(100),
            message_type varchar(20) DEFAULT 'text',
            user_message longtext,
            ai_response longtext,
            language varchar(5) DEFAULT 'en',
            context_type varchar(50) DEFAULT 'general',
            detected_sector varchar(100),
            detected_country varchar(50),
            tokens_used int(11) DEFAULT 0,
            credits_used decimal(10,2) DEFAULT 0,
            response_time_ms int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'completed',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_whatsapp_user_id (whatsapp_user_id),
            KEY idx_phone_number (phone_number),
            KEY idx_message_id (message_id),
            KEY idx_language (language),
            KEY idx_context_type (context_type),
            KEY idx_detected_sector (detected_sector),
            KEY idx_created_at (created_at)
        ) $charset_collate;";

        $wpdb->query($whatsapp_conversations_sql);
    }

    // Verify tables were created
    $users_created = $wpdb->get_var("SHOW TABLES LIKE '$whatsapp_users_table'") === $whatsapp_users_table;
    $conversations_created = $wpdb->get_var("SHOW TABLES LIKE '$whatsapp_conversations_table'") === $whatsapp_conversations_table;

    if ($users_created && $conversations_created) {
        error_log('ChatGABI WhatsApp: Database tables created successfully');
        return true;
    } else {
        error_log('ChatGABI WhatsApp: Failed to create database tables');
        return false;
    }
}

/**
 * WhatsApp webhook handler
 */
function chatgabi_whatsapp_webhook_handler($request) {
    $method = $request->get_method();
    
    // Handle webhook verification (GET request)
    if ($method === 'GET') {
        return chatgabi_verify_whatsapp_webhook($request);
    }
    
    // Handle incoming messages (POST request)
    if ($method === 'POST') {
        return chatgabi_process_whatsapp_message($request);
    }
    
    return new WP_Error(
        'invalid_method',
        __('Invalid request method', 'chatgabi'),
        array('status' => 405)
    );
}

/**
 * Verify WhatsApp webhook (for initial setup)
 */
function chatgabi_verify_whatsapp_webhook($request) {
    $verify_token = get_option('chatgabi_whatsapp_verify_token', 'chatgabi_verify_token_2024');
    $mode = $request->get_param('hub_mode');
    $token = $request->get_param('hub_verify_token');
    $challenge = $request->get_param('hub_challenge');
    
    if ($mode === 'subscribe' && $token === $verify_token) {
        error_log('ChatGABI WhatsApp: Webhook verified successfully');
        return rest_ensure_response($challenge);
    }
    
    error_log('ChatGABI WhatsApp: Webhook verification failed');
    return new WP_Error(
        'verification_failed',
        __('Webhook verification failed', 'chatgabi'),
        array('status' => 403)
    );
}

/**
 * Process incoming WhatsApp message
 */
function chatgabi_process_whatsapp_message($request) {
    $start_time = microtime(true);
    
    try {
        $body = $request->get_body();
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['entry'])) {
            throw new Exception('Invalid webhook data');
        }
        
        foreach ($data['entry'] as $entry) {
            if (!isset($entry['changes'])) continue;
            
            foreach ($entry['changes'] as $change) {
                if ($change['field'] !== 'messages') continue;
                
                $value = $change['value'];
                
                // Process incoming messages
                if (isset($value['messages'])) {
                    foreach ($value['messages'] as $message) {
                        chatgabi_handle_incoming_message($message, $value);
                    }
                }
                
                // Process message status updates
                if (isset($value['statuses'])) {
                    foreach ($value['statuses'] as $status) {
                        chatgabi_handle_message_status($status);
                    }
                }
            }
        }
        
        $processing_time = (microtime(true) - $start_time) * 1000;
        error_log("ChatGABI WhatsApp: Message processed in {$processing_time}ms");
        
        return rest_ensure_response(array(
            'status' => 'success',
            'message' => 'Webhook processed successfully',
            'processing_time_ms' => round($processing_time, 2)
        ));
        
    } catch (Exception $e) {
        error_log('ChatGABI WhatsApp Error: ' . $e->getMessage());
        
        return new WP_Error(
            'processing_error',
            $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * Handle incoming WhatsApp message
 */
function chatgabi_handle_incoming_message($message, $value) {
    $phone_number = $message['from'];
    $message_id = $message['id'];
    $timestamp = $message['timestamp'];
    
    // Get or create WhatsApp user
    $whatsapp_user = chatgabi_get_or_create_whatsapp_user($phone_number, $value);
    
    // Extract message content
    $message_text = '';
    $message_type = 'text';

    if (isset($message['text'])) {
        $message_text = $message['text']['body'];
        $message_type = 'text';
    } elseif (isset($message['interactive'])) {
        $message_text = chatgabi_extract_interactive_message($message['interactive']);
        $message_type = 'interactive';
    } else {
        // Handle other message types (image, audio, etc.)
        $message_text = '[Unsupported message type]';
        $message_type = $message['type'] ?? 'unknown';
    }

    // 🔄 TRANSLATION INTEGRATION: Translate incoming message if needed
    $translation_result = null;
    $processed_message = $message_text;

    if (function_exists('chatgabi_smart_translate') && !empty($message_text) && $message_text !== '[Unsupported message type]') {
        $translation_result = chatgabi_smart_translate($message_text, $whatsapp_user['country'], $whatsapp_user['id']);

        if ($translation_result && $translation_result['was_translated']) {
            $processed_message = $translation_result['translated_text'];

            // Update user's detected language preference
            if ($translation_result['original_language'] !== 'en') {
                chatgabi_update_whatsapp_user_language($whatsapp_user['id'], $translation_result['original_language']);
                $whatsapp_user['language'] = $translation_result['original_language'];
            }

            error_log("ChatGABI Translation: {$translation_result['original_language']} → en | Original: '$message_text' | Translated: '$processed_message'");
        }
    }

    // Process the message with AI (using translated text if available)
    $ai_response = chatgabi_process_whatsapp_ai_request(
        $processed_message,
        $whatsapp_user['language'],
        $whatsapp_user['country'],
        $whatsapp_user['preferred_sector'],
        $whatsapp_user['id']
    );
    
    // Send response back to WhatsApp
    if (!is_wp_error($ai_response)) {
        chatgabi_send_whatsapp_response($phone_number, $ai_response['response'], $whatsapp_user['language']);
        
        // Log the conversation with translation data
        chatgabi_log_whatsapp_conversation_with_translation(
            $whatsapp_user['id'],
            $phone_number,
            $message_id,
            $message_type,
            $message_text,
            $ai_response['response'],
            $whatsapp_user['language'],
            $ai_response['context_type'] ?? 'general',
            $ai_response['detected_sector'] ?? '',
            $ai_response['detected_country'] ?? '',
            $ai_response['tokens_used'] ?? 0,
            $ai_response['credits_used'] ?? 0,
            $translation_result
        );
        
        // Update user statistics
        chatgabi_update_whatsapp_user_stats($whatsapp_user['id']);
    } else {
        // Send error message
        $error_message = chatgabi_get_error_message($whatsapp_user['language']);
        chatgabi_send_whatsapp_response($phone_number, $error_message, $whatsapp_user['language']);
    }
}

/**
 * Get or create WhatsApp user
 */
function chatgabi_get_or_create_whatsapp_user($phone_number, $webhook_data = null) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_users';

    // Try to get existing user
    $user = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE phone_number = %s",
        $phone_number
    ), ARRAY_A);

    if ($user) {
        return $user;
    }

    // Create new user
    $display_name = '';
    $profile_name = '';

    if ($webhook_data && isset($webhook_data['contacts'])) {
        foreach ($webhook_data['contacts'] as $contact) {
            if ($contact['wa_id'] === $phone_number) {
                $profile_name = $contact['profile']['name'] ?? '';
                break;
            }
        }
    }

    // Detect country from phone number
    $detected_country = chatgabi_detect_country_from_phone($phone_number);

    $user_data = array(
        'phone_number' => $phone_number,
        'display_name' => $display_name,
        'profile_name' => $profile_name,
        'language' => 'en', // Default, will be updated based on interaction
        'country' => $detected_country,
        'credits' => 10, // Welcome credits
        'status' => 'active',
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    );

    $wpdb->insert($table_name, $user_data);
    $user_id = $wpdb->insert_id;

    if ($user_id) {
        $user_data['id'] = $user_id;

        // Send welcome message
        chatgabi_send_welcome_message($phone_number, $detected_country);

        error_log("ChatGABI WhatsApp: New user created - ID: $user_id, Phone: $phone_number, Country: $detected_country");

        return $user_data;
    }

    return false;
}

/**
 * Detect country from phone number
 */
function chatgabi_detect_country_from_phone($phone_number) {
    // Remove any non-digit characters
    $clean_number = preg_replace('/[^0-9]/', '', $phone_number);

    // Country code mapping for target countries
    $country_codes = array(
        '233' => 'Ghana',      // +233
        '254' => 'Kenya',      // +254
        '234' => 'Nigeria',    // +234
        '27' => 'South Africa' // +27
    );

    foreach ($country_codes as $code => $country) {
        if (strpos($clean_number, $code) === 0) {
            return $country;
        }
    }

    return 'Ghana'; // Default fallback
}

/**
 * Process WhatsApp message with AI
 */
function chatgabi_process_whatsapp_ai_request($message, $language, $country, $sector, $user_id) {
    // Check if user has sufficient credits
    if (!chatgabi_check_whatsapp_user_credits($user_id)) {
        return new WP_Error(
            'insufficient_credits',
            __('Insufficient credits. Please purchase more credits to continue.', 'chatgabi')
        );
    }

    // Use existing OpenAI integration with WhatsApp context
    $context = 'whatsapp_chat';

    // Add WhatsApp-specific prompt prefix
    $whatsapp_prefix = chatgabi_get_whatsapp_prompt_prefix($language, $country);
    $enhanced_message = $whatsapp_prefix . "\n\nUser Message: " . $message;

    // Process with existing OpenAI function
    $ai_response = businesscraft_ai_process_openai_request(
        $enhanced_message,
        $language,
        $context,
        null // No WordPress user ID for WhatsApp users
    );

    if (!is_wp_error($ai_response)) {
        // Deduct credits from WhatsApp user
        $credits_used = chatgabi_calculate_whatsapp_credits($ai_response['tokens_used'] ?? 0);
        chatgabi_deduct_whatsapp_credits($user_id, $credits_used);

        // Format response for WhatsApp (max 4096 characters)
        $formatted_response = chatgabi_format_whatsapp_response($ai_response['response'], $language);

        $ai_response['response'] = $formatted_response;
        $ai_response['credits_used'] = $credits_used;
        $ai_response['context_type'] = $context;
    }

    return $ai_response;
}

/**
 * Get WhatsApp-specific prompt prefix
 */
function chatgabi_get_whatsapp_prompt_prefix($language, $country) {
    $language_strings = chatgabi_get_language_strings($language);

    $prefix = "You are ChatGABI AI, an African business intelligence assistant accessible via WhatsApp. ";
    $prefix .= "Provide concise, practical business advice for African entrepreneurs. ";
    $prefix .= "Keep responses under 1000 characters for WhatsApp readability. ";
    $prefix .= "Focus on actionable insights for $country market. ";
    $prefix .= "Respond in a conversational, friendly tone suitable for mobile messaging.";

    return $prefix;
}

/**
 * Format response for WhatsApp
 */
function chatgabi_format_whatsapp_response($response, $language) {
    // Limit response length for WhatsApp
    $max_length = 1500; // Conservative limit for readability

    if (strlen($response) > $max_length) {
        $response = substr($response, 0, $max_length - 50) . '...\n\n📱 For detailed analysis, visit our web platform.';
    }

    // Add WhatsApp-friendly formatting
    $response = str_replace('**', '*', $response); // Bold formatting
    $response = str_replace('##', '*', $response); // Header formatting

    // Add ChatGABI signature
    $signature = "\n\n🤖 _ChatGABI AI - African Business Intelligence_";

    return $response . $signature;
}

/**
 * Send WhatsApp response
 */
function chatgabi_send_whatsapp_response($phone_number, $message, $language = 'en') {
    $access_token = get_option('chatgabi_whatsapp_access_token');
    $phone_number_id = get_option('chatgabi_whatsapp_phone_number_id');

    if (empty($access_token) || empty($phone_number_id)) {
        error_log('ChatGABI WhatsApp: Missing access token or phone number ID');
        return false;
    }

    $url = "https://graph.facebook.com/v18.0/$phone_number_id/messages";

    $data = array(
        'messaging_product' => 'whatsapp',
        'to' => $phone_number,
        'type' => 'text',
        'text' => array(
            'body' => $message
        )
    );

    $response = wp_remote_post($url, array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $access_token,
            'Content-Type' => 'application/json',
        ),
        'body' => json_encode($data),
        'timeout' => 30,
    ));

    if (is_wp_error($response)) {
        error_log('ChatGABI WhatsApp Send Error: ' . $response->get_error_message());
        return false;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    if ($response_code === 200) {
        error_log("ChatGABI WhatsApp: Message sent successfully to $phone_number");
        return true;
    } else {
        error_log("ChatGABI WhatsApp Send Error: HTTP $response_code - $response_body");
        return false;
    }
}

/**
 * Send welcome message to new WhatsApp users
 */
function chatgabi_send_welcome_message($phone_number, $country) {
    $welcome_messages = array(
        'Ghana' => "🇬🇭 Akwaaba! Welcome to ChatGABI AI!\n\nI'm your African business intelligence assistant. I can help you with:\n\n• Business planning & strategy\n• Market analysis for Ghana\n• Financial forecasting\n• Funding opportunities\n• Industry insights\n\nYou have 10 free credits to get started! Just send me your business question.",
        'Kenya' => "🇰🇪 Karibu! Welcome to ChatGABI AI!\n\nI'm your African business intelligence assistant. I can help you with:\n\n• Business planning & strategy\n• Market analysis for Kenya\n• Financial forecasting\n• Funding opportunities\n• Industry insights\n\nYou have 10 free credits to get started! Just send me your business question.",
        'Nigeria' => "🇳🇬 Welcome to ChatGABI AI!\n\nI'm your African business intelligence assistant. I can help you with:\n\n• Business planning & strategy\n• Market analysis for Nigeria\n• Financial forecasting\n• Funding opportunities\n• Industry insights\n\nYou have 10 free credits to get started! Just send me your business question.",
        'South Africa' => "🇿🇦 Welcome to ChatGABI AI!\n\nI'm your African business intelligence assistant. I can help you with:\n\n• Business planning & strategy\n• Market analysis for South Africa\n• Financial forecasting\n• Funding opportunities\n• Industry insights\n\nYou have 10 free credits to get started! Just send me your business question."
    );

    $message = $welcome_messages[$country] ?? $welcome_messages['Ghana'];

    return chatgabi_send_whatsapp_response($phone_number, $message);
}

/**
 * Check if WhatsApp user has sufficient credits
 */
function chatgabi_check_whatsapp_user_credits($user_id, $required_credits = 1) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_users';

    $credits = $wpdb->get_var($wpdb->prepare(
        "SELECT credits FROM $table_name WHERE id = %d",
        $user_id
    ));

    return $credits >= $required_credits;
}

/**
 * Calculate credits needed for WhatsApp message
 */
function chatgabi_calculate_whatsapp_credits($tokens_used) {
    // Same credit calculation as web platform
    // 1 credit = ~1000 tokens (approximate)
    return max(1, ceil($tokens_used / 1000));
}

/**
 * Deduct credits from WhatsApp user
 */
function chatgabi_deduct_whatsapp_credits($user_id, $credits_to_deduct) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_users';

    return $wpdb->query($wpdb->prepare(
        "UPDATE $table_name SET credits = GREATEST(0, credits - %f), updated_at = %s WHERE id = %d",
        $credits_to_deduct,
        current_time('mysql'),
        $user_id
    ));
}

/**
 * Log WhatsApp conversation
 */
function chatgabi_log_whatsapp_conversation($user_id, $phone_number, $message_id, $message_type, $user_message, $ai_response, $language, $context_type, $detected_sector, $detected_country, $tokens_used, $credits_used) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_conversations';

    $conversation_data = array(
        'whatsapp_user_id' => $user_id,
        'phone_number' => $phone_number,
        'message_id' => $message_id,
        'message_type' => $message_type,
        'user_message' => $user_message,
        'ai_response' => $ai_response,
        'language' => $language,
        'context_type' => $context_type,
        'detected_sector' => $detected_sector,
        'detected_country' => $detected_country,
        'tokens_used' => $tokens_used,
        'credits_used' => $credits_used,
        'status' => 'completed',
        'created_at' => current_time('mysql')
    );

    return $wpdb->insert($table_name, $conversation_data);
}

/**
 * Update WhatsApp user statistics
 */
function chatgabi_update_whatsapp_user_stats($user_id) {
    global $wpdb;

    $users_table = $wpdb->prefix . 'chatgabi_whatsapp_users';

    return $wpdb->query($wpdb->prepare(
        "UPDATE $users_table SET
         total_messages = total_messages + 1,
         last_message_at = %s,
         updated_at = %s
         WHERE id = %d",
        current_time('mysql'),
        current_time('mysql'),
        $user_id
    ));
}

/**
 * Extract interactive message content
 */
function chatgabi_extract_interactive_message($interactive) {
    if (isset($interactive['button_reply'])) {
        return $interactive['button_reply']['title'];
    } elseif (isset($interactive['list_reply'])) {
        return $interactive['list_reply']['title'];
    }

    return '[Interactive message]';
}

/**
 * Handle message status updates
 */
function chatgabi_handle_message_status($status) {
    // Log message delivery status for analytics
    error_log("ChatGABI WhatsApp: Message status - ID: {$status['id']}, Status: {$status['status']}");
}

/**
 * Get error message in user's language
 */
function chatgabi_get_error_message($language) {
    $error_messages = array(
        'en' => "Sorry, I'm experiencing technical difficulties. Please try again in a moment.",
        'tw' => "Kafra, mewɔ adwuma mu haw. Yɛ srɛ wo sɔ hwɛ bio.",
        'sw' => "Samahani, nina matatizo ya kiufundi. Tafadhali jaribu tena baada ya muda.",
        'yo' => "Pẹlẹ o, mo n ni wahala pẹlu imọ-ẹrọ. Jọwọ gbiyanju lẹẹkansi.",
        'zu' => "Ngiyaxolisa, nginenkinga yobuchwepheshe. Sicela uzame futhi emva kwesikhathi."
    );

    return $error_messages[$language] ?? $error_messages['en'];
}

/**
 * Get WhatsApp users (admin endpoint)
 */
function chatgabi_get_whatsapp_users($request) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_users';
    $page = $request->get_param('page') ?: 1;
    $per_page = $request->get_param('per_page') ?: 20;
    $offset = ($page - 1) * $per_page;

    $users = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d OFFSET %d",
        $per_page,
        $offset
    ), ARRAY_A);

    $total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

    return rest_ensure_response(array(
        'users' => $users,
        'total' => (int) $total,
        'page' => (int) $page,
        'per_page' => (int) $per_page,
        'total_pages' => ceil($total / $per_page)
    ));
}

/**
 * Get WhatsApp analytics (admin endpoint)
 */
function chatgabi_get_whatsapp_analytics($request) {
    global $wpdb;

    $users_table = $wpdb->prefix . 'chatgabi_whatsapp_users';
    $conversations_table = $wpdb->prefix . 'chatgabi_whatsapp_conversations';

    // Basic statistics
    $total_users = $wpdb->get_var("SELECT COUNT(*) FROM $users_table");
    $active_users = $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE last_message_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $total_conversations = $wpdb->get_var("SELECT COUNT(*) FROM $conversations_table");
    $total_credits_used = $wpdb->get_var("SELECT SUM(credits_used) FROM $conversations_table");

    // Country breakdown
    $country_stats = $wpdb->get_results(
        "SELECT country, COUNT(*) as user_count FROM $users_table GROUP BY country ORDER BY user_count DESC",
        ARRAY_A
    );

    // Language breakdown
    $language_stats = $wpdb->get_results(
        "SELECT language, COUNT(*) as user_count FROM $users_table GROUP BY language ORDER BY user_count DESC",
        ARRAY_A
    );

    // Daily message volume (last 30 days)
    $daily_stats = $wpdb->get_results(
        "SELECT DATE(created_at) as date, COUNT(*) as message_count
         FROM $conversations_table
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         GROUP BY DATE(created_at)
         ORDER BY date DESC",
        ARRAY_A
    );

    return rest_ensure_response(array(
        'summary' => array(
            'total_users' => (int) $total_users,
            'active_users_7d' => (int) $active_users,
            'total_conversations' => (int) $total_conversations,
            'total_credits_used' => (float) $total_credits_used
        ),
        'country_breakdown' => $country_stats,
        'language_breakdown' => $language_stats,
        'daily_message_volume' => $daily_stats
    ));
}

/**
 * Send message endpoint (admin/testing)
 */
function chatgabi_whatsapp_send_message($request) {
    $phone = $request->get_param('phone');
    $message = $request->get_param('message');
    $language = $request->get_param('language') ?: 'en';

    $result = chatgabi_send_whatsapp_response($phone, $message, $language);

    if ($result) {
        return rest_ensure_response(array(
            'status' => 'success',
            'message' => 'Message sent successfully'
        ));
    } else {
        return new WP_Error(
            'send_failed',
            __('Failed to send message', 'chatgabi'),
            array('status' => 500)
        );
    }
}

/**
 * Update WhatsApp user's detected language
 */
function chatgabi_update_whatsapp_user_language($user_id, $language) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_users';

    return $wpdb->query($wpdb->prepare(
        "UPDATE $table_name SET language = %s, updated_at = %s WHERE id = %d",
        $language,
        current_time('mysql'),
        $user_id
    ));
}

/**
 * Log WhatsApp conversation with translation data
 */
function chatgabi_log_whatsapp_conversation_with_translation($user_id, $phone_number, $message_id, $message_type, $user_message, $ai_response, $language, $context_type, $detected_sector, $detected_country, $tokens_used, $credits_used, $translation_result) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_whatsapp_conversations';

    // Prepare translation data
    $original_language = null;
    $translated_message = null;
    $translation_confidence = null;
    $translation_service = null;
    $translation_cost = 0;
    $was_translated = false;

    if ($translation_result && is_array($translation_result)) {
        $original_language = $translation_result['original_language'] ?? null;
        $translated_message = $translation_result['translated_text'] ?? null;
        $translation_confidence = $translation_result['confidence'] ?? null;
        $translation_service = 'google';
        $translation_cost = $translation_result['cost'] ?? 0;
        $was_translated = $translation_result['was_translated'] ?? false;
    }

    $conversation_data = array(
        'whatsapp_user_id' => $user_id,
        'phone_number' => $phone_number,
        'message_id' => $message_id,
        'message_type' => $message_type,
        'user_message' => $user_message,
        'ai_response' => $ai_response,
        'language' => $language,
        'context_type' => $context_type,
        'detected_sector' => $detected_sector,
        'detected_country' => $detected_country,
        'tokens_used' => $tokens_used,
        'credits_used' => $credits_used,
        'original_message_language' => $original_language,
        'translated_message' => $translated_message,
        'translation_confidence' => $translation_confidence,
        'translation_service' => $translation_service,
        'translation_cost' => $translation_cost,
        'was_translated' => $was_translated ? 1 : 0,
        'status' => 'completed',
        'created_at' => current_time('mysql')
    );

    return $wpdb->insert($table_name, $conversation_data);
}

/**
 * Check admin permission for WhatsApp endpoints
 */
function chatgabi_check_admin_permission() {
    return current_user_can('manage_options');
}

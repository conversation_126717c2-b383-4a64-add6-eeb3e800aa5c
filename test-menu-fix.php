<?php
/**
 * Test ChatGABI Admin Menu Fix
 * This script verifies that the menu registration issues have been resolved
 */

// Load WordPress
require_once(dirname(__FILE__) . '/wp-config.php');

echo "<h1>🔧 ChatGABI Menu Fix Verification</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
.code-block { background: #f0f0f0; padding: 10px; border-left: 4px solid #0073aa; margin: 10px 0; font-family: monospace; }
</style>";

// Step 1: Force re-run admin menu hooks
echo "<div class='section'>";
echo "<h2>🔄 Step 1: Re-registering Admin Menus</h2>";

// Clear any existing menu registrations
global $menu, $submenu;
$original_menu = $menu;
$original_submenu = $submenu;

// Force re-run admin_menu action
do_action('admin_menu');

echo "<p class='success'>✅ Admin menu hooks re-executed</p>";
echo "</div>";

// Step 2: Check menu structure after fix
echo "<div class='section'>";
echo "<h2>📋 Step 2: Menu Structure Verification</h2>";

echo "<h3>Main Menu Items:</h3>";
if (isset($menu) && is_array($menu)) {
    $chatgabi_menu_found = false;
    foreach ($menu as $menu_item) {
        if (is_array($menu_item) && isset($menu_item[2])) {
            if (strpos($menu_item[2], 'chatgabi') !== false || strpos($menu_item[0], 'ChatGABI') !== false) {
                echo "<p class='success'>✅ Found ChatGABI menu: {$menu_item[0]} ({$menu_item[2]})</p>";
                $chatgabi_menu_found = true;
            }
        }
    }
    
    if (!$chatgabi_menu_found) {
        echo "<p class='error'>❌ No ChatGABI main menu found</p>";
    }
} else {
    echo "<p class='error'>❌ WordPress menu array not available</p>";
}

echo "<h3>Submenu Items:</h3>";
if (isset($submenu) && is_array($submenu)) {
    $engagement_analytics_found = false;
    
    // Check for submenus under tools.php
    if (isset($submenu['tools.php'])) {
        echo "<p class='info'>📋 Found Tools submenu with " . count($submenu['tools.php']) . " items</p>";
        
        foreach ($submenu['tools.php'] as $submenu_item) {
            if (is_array($submenu_item) && isset($submenu_item[0])) {
                echo "<p class='info'>  • {$submenu_item[0]} ({$submenu_item[2]})</p>";
                
                if (strpos($submenu_item[2], 'chatgabi') !== false) {
                    echo "<p class='success'>    ✅ ChatGABI submenu found!</p>";
                }
                
                if (strpos($submenu_item[2], 'engagement-analytics') !== false) {
                    echo "<p class='success'>    ✅ Engagement Analytics submenu found!</p>";
                    $engagement_analytics_found = true;
                }
            }
        }
    }
    
    // Also check for any chatgabi parent menus
    foreach ($submenu as $parent => $submenu_items) {
        if (strpos($parent, 'chatgabi') !== false) {
            echo "<p class='info'>📋 Found ChatGABI submenu parent: {$parent}</p>";
            foreach ($submenu_items as $submenu_item) {
                if (is_array($submenu_item) && isset($submenu_item[0])) {
                    echo "<p class='info'>  • {$submenu_item[0]} ({$submenu_item[2]})</p>";
                    if (strpos($submenu_item[2], 'engagement-analytics') !== false) {
                        echo "<p class='success'>    ✅ Engagement Analytics submenu found!</p>";
                        $engagement_analytics_found = true;
                    }
                }
            }
        }
    }
    
    if ($engagement_analytics_found) {
        echo "<p class='success'>🎉 Engagement Analytics submenu is properly registered!</p>";
    } else {
        echo "<p class='error'>❌ Engagement Analytics submenu still not found</p>";
    }
} else {
    echo "<p class='error'>❌ WordPress submenu array not available</p>";
}
echo "</div>";

// Step 3: Test user capabilities
echo "<div class='section'>";
echo "<h2>👤 Step 3: User Access Verification</h2>";

$current_user = wp_get_current_user();
echo "<p class='info'>👤 Current user: {$current_user->display_name} (ID: {$current_user->ID})</p>";

if (current_user_can('manage_options')) {
    echo "<p class='success'>✅ User has 'manage_options' capability</p>";
} else {
    echo "<p class='error'>❌ User missing 'manage_options' capability</p>";
}

// Check if user is administrator
if (in_array('administrator', $current_user->roles)) {
    echo "<p class='success'>✅ User has administrator role</p>";
} else {
    echo "<p class='warning'>⚠️ User is not an administrator</p>";
}
echo "</div>";

// Step 4: Test direct URL access
echo "<div class='section'>";
echo "<h2>🔗 Step 4: URL Access Testing</h2>";

$test_urls = array(
    'Main ChatGABI Dashboard' => admin_url('tools.php?page=chatgabi'),
    'Engagement Analytics' => admin_url('tools.php?page=chatgabi-engagement-analytics'),
    'ChatGABI Settings' => admin_url('tools.php?page=chatgabi-settings'),
    'ChatGABI Templates' => admin_url('tools.php?page=chatgabi-templates'),
    'ChatGABI Users' => admin_url('tools.php?page=chatgabi-users')
);

foreach ($test_urls as $name => $url) {
    echo "<p class='info'>🔗 <strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
}

echo "<p class='warning'>⚠️ Click the links above to test if they work without 'Access Denied' errors</p>";
echo "</div>";

// Step 5: Function verification
echo "<div class='section'>";
echo "<h2>🔧 Step 5: Function Verification</h2>";

$required_functions = array(
    'chatgabi_add_admin_menu' => 'Main admin menu registration',
    'chatgabi_add_engagement_analytics_menu' => 'Engagement analytics submenu registration',
    'chatgabi_engagement_analytics_page' => 'Analytics page callback',
    'chatgabi_admin_page' => 'Main admin page callback'
);

foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p class='success'>✅ {$description}: {$function}()</p>";
    } else {
        echo "<p class='error'>❌ Missing {$description}: {$function}()</p>";
    }
}
echo "</div>";

// Step 6: WordPress hooks verification
echo "<div class='section'>";
echo "<h2>🪝 Step 6: WordPress Hooks Verification</h2>";

global $wp_filter;

if (isset($wp_filter['admin_menu'])) {
    echo "<p class='success'>✅ admin_menu hook exists</p>";
    
    $chatgabi_hooks = 0;
    foreach ($wp_filter['admin_menu']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            $function_name = '';
            if (is_array($callback['function'])) {
                if (is_object($callback['function'][0])) {
                    $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                } else {
                    $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                }
            } elseif (is_string($callback['function'])) {
                $function_name = $callback['function'];
            }
            
            if (strpos($function_name, 'chatgabi') !== false) {
                echo "<p class='success'>✅ Found ChatGABI hook: {$function_name} (priority: {$priority})</p>";
                $chatgabi_hooks++;
            }
        }
    }
    
    echo "<p class='info'>📊 Total ChatGABI admin_menu hooks: {$chatgabi_hooks}</p>";
} else {
    echo "<p class='error'>❌ admin_menu hook not found</p>";
}
echo "</div>";

// Step 7: Database verification
echo "<div class='section'>";
echo "<h2>🗄️ Step 7: Database Table Verification</h2>";

global $wpdb;

$required_tables = array(
    'chatgabi_sector_logs' => 'Sector analytics data',
    'businesscraft_ai_analytics' => 'General analytics data',
    'businesscraft_ai_chat_logs' => 'Chat interaction logs'
);

foreach ($required_tables as $table_suffix => $description) {
    $table_name = $wpdb->prefix . $table_suffix;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        $row_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        echo "<p class='success'>✅ {$description}: {$table_name} ({$row_count} records)</p>";
    } else {
        echo "<p class='error'>❌ Missing {$description}: {$table_name}</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Summary & Next Steps</h2>";

echo "<h3>✅ Fixes Applied:</h3>";
echo "<ul>";
echo "<li>✅ Changed parent menu slug from 'chatgabi' to 'tools.php?page=chatgabi'</li>";
echo "<li>✅ Updated hook condition from 'chatgabi_page_' to 'tools_page_'</li>";
echo "<li>✅ Re-registered admin menu hooks</li>";
echo "</ul>";

echo "<h3>🧪 Test Results:</h3>";
if ($engagement_analytics_found) {
    echo "<p class='success'>✅ <strong>SUCCESS:</strong> Engagement Analytics submenu is now properly registered!</p>";
} else {
    echo "<p class='error'>❌ <strong>ISSUE:</strong> Engagement Analytics submenu still not found</p>";
}

echo "<h3>📍 Immediate Actions:</h3>";
echo "<ol>";
echo "<li><strong>Test Main Dashboard:</strong> <a href='" . admin_url('tools.php?page=chatgabi') . "' target='_blank'>Visit ChatGABI Dashboard</a></li>";
echo "<li><strong>Test Analytics Dashboard:</strong> <a href='" . admin_url('tools.php?page=chatgabi-engagement-analytics') . "' target='_blank'>Visit Engagement Analytics</a></li>";
echo "<li><strong>Check WordPress Admin Menu:</strong> Go to Tools menu and look for ChatGABI submenu items</li>";
echo "</ol>";

echo "<h3>🔧 If Issues Persist:</h3>";
echo "<ul>";
echo "<li>Clear any WordPress caching plugins</li>";
echo "<li>Deactivate and reactivate the theme</li>";
echo "<li>Check WordPress error logs for any PHP errors</li>";
echo "<li>Verify file permissions on theme files</li>";
echo "</ul>";
echo "</div>";
?>

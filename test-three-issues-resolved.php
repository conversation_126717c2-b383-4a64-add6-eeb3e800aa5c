<?php
/**
 * Test Three Template Issues Resolution
 * 
 * Comprehensive test to verify all three issues are resolved:
 * 1. Templates Page Navigation Failure
 * 2. Admin Dashboard Template Management
 * 3. User Dashboard Template Visibility
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates - Three Issues Resolution Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        .test-results { margin: 20px 0; }
        .test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f8f9fa; }
    </style>
</head>
<body>

<h1>🧪 ChatGABI Templates - Three Issues Resolution Test</h1>

<?php
echo '<div class="info">Test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$all_tests_passed = true;
$test_results = array();

// Test 1: Templates Page Navigation
echo '<h2>🚨 Test 1: Templates Page Navigation</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    $page_url = get_permalink($templates_page->ID);
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    
    echo '<div class="success">✅ Templates page exists (ID: ' . $templates_page->ID . ')</div>';
    echo '<div class="info">📍 Page URL: <a href="' . $page_url . '" target="_blank">' . $page_url . '</a></div>';
    
    if ($page_template === 'page-templates.php') {
        echo '<div class="success">✅ Correct page template assigned</div>';
        $test_results['page_navigation'] = 'PASSED';
    } else {
        echo '<div class="error">❌ Incorrect page template: ' . $page_template . '</div>';
        $test_results['page_navigation'] = 'FAILED';
        $all_tests_passed = false;
    }
    
    // Check if template file exists
    $template_file = get_template_directory() . '/page-templates.php';
    if (file_exists($template_file)) {
        echo '<div class="success">✅ Template file exists</div>';
    } else {
        echo '<div class="error">❌ Template file missing</div>';
        $test_results['page_navigation'] = 'FAILED';
        $all_tests_passed = false;
    }
    
} else {
    echo '<div class="error">❌ Templates page does not exist</div>';
    $test_results['page_navigation'] = 'FAILED';
    $all_tests_passed = false;
}

// Test 2: Admin Dashboard Template Management
echo '<h2>⚙️ Test 2: Admin Dashboard Template Management</h2>';

if (function_exists('chatgabi_templates_admin_page')) {
    echo '<div class="success">✅ Admin page function exists</div>';
    
    // Test CRUD functions
    $crud_functions = array(
        'chatgabi_ajax_duplicate_template' => 'Duplicate template',
        'chatgabi_ajax_delete_template' => 'Delete template'
    );
    
    $crud_passed = true;
    foreach ($crud_functions as $function => $description) {
        if (function_exists($function)) {
            echo '<div class="success">✅ ' . $function . ' - ' . $description . '</div>';
        } else {
            echo '<div class="warning">⚠️ ' . $function . ' missing - ' . $description . '</div>';
            $crud_passed = false;
        }
    }
    
    // Test admin page output
    ob_start();
    chatgabi_templates_admin_page();
    $admin_output = ob_get_clean();
    
    if (strpos($admin_output, 'editTemplate') !== false && strpos($admin_output, 'deleteTemplate') !== false) {
        echo '<div class="success">✅ Admin page has interactive management features</div>';
        $test_results['admin_management'] = $crud_passed ? 'PASSED' : 'PARTIAL';
    } else {
        echo '<div class="warning">⚠️ Admin page lacks interactive features</div>';
        $test_results['admin_management'] = 'FAILED';
        $all_tests_passed = false;
    }
    
} else {
    echo '<div class="error">❌ Admin page function missing</div>';
    $test_results['admin_management'] = 'FAILED';
    $all_tests_passed = false;
}

// Test 3: User Dashboard Template Visibility
echo '<h2>👤 Test 3: User Dashboard Template Visibility</h2>';

// Check if dashboard page exists
$dashboard_page = get_page_by_path('dashboard');
if ($dashboard_page) {
    echo '<div class="success">✅ Dashboard page exists</div>';
    
    // Check dashboard template file
    $dashboard_template = get_template_directory() . '/page-dashboard.php';
    if (file_exists($dashboard_template)) {
        echo '<div class="success">✅ Dashboard template file exists</div>';
        
        // Check if dashboard contains template functionality
        $dashboard_content = file_get_contents($dashboard_template);
        if (strpos($dashboard_content, 'templates') !== false && 
            strpos($dashboard_content, 'loadDashboardTemplates') !== false) {
            echo '<div class="success">✅ Dashboard has template integration</div>';
        } else {
            echo '<div class="warning">⚠️ Dashboard lacks template integration</div>';
        }
        
        // Check for template modal
        if (strpos($dashboard_content, 'template-preview-modal') !== false) {
            echo '<div class="success">✅ Dashboard has template preview modal</div>';
        } else {
            echo '<div class="warning">⚠️ Dashboard lacks template preview modal</div>';
        }
        
    } else {
        echo '<div class="error">❌ Dashboard template file missing</div>';
        $all_tests_passed = false;
    }
} else {
    echo '<div class="warning">⚠️ Dashboard page not found</div>';
}

// Test REST API endpoints
echo '<h3>REST API Endpoints Test</h3>';
$rest_endpoints = array(
    'chatgabi/v1/templates' => 'Templates listing',
    'chatgabi/v1/template-categories' => 'Template categories'
);

foreach ($rest_endpoints as $endpoint => $description) {
    $rest_url = rest_url($endpoint);
    echo '<div class="info">📡 Testing: ' . $rest_url . ' - ' . $description . '</div>';
}

// Test template data availability
echo '<h3>Template Data Test</h3>';
$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

$template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_public = 1");
$category_count = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");

if ($template_count > 0) {
    echo '<div class="success">✅ Public templates available (' . $template_count . ' templates)</div>';
} else {
    echo '<div class="warning">⚠️ No public templates found</div>';
}

if ($category_count > 0) {
    echo '<div class="success">✅ Template categories available (' . $category_count . ' categories)</div>';
    $test_results['user_dashboard'] = 'PASSED';
} else {
    echo '<div class="warning">⚠️ No template categories found</div>';
    $test_results['user_dashboard'] = 'PARTIAL';
}

// Overall Test Results
echo '<h2>📊 Overall Test Results</h2>';

if ($all_tests_passed && count(array_filter($test_results, function($result) { return $result === 'PASSED'; })) >= 2) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL MAJOR ISSUES RESOLVED!</h3>';
    echo '<p><strong>✅ The three template issues have been successfully fixed!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues May Remain</h3>';
    echo '<p>Please review the test results above for details.</p>';
    echo '</div>';
}

echo '<div class="test-results">';
echo '<h3>Detailed Results:</h3>';
foreach ($test_results as $test => $result) {
    $status_icon = $result === 'PASSED' ? '✅' : ($result === 'PARTIAL' ? '⚠️' : '❌');
    $test_name = ucwords(str_replace('_', ' ', $test));
    echo '<div class="test-item">' . $status_icon . ' ' . $test_name . ': ' . $result . '</div>';
}
echo '</div>';

// Action Buttons
echo '<h2>🚀 Test Actions</h2>';

echo '<div style="margin: 20px 0;">';

if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 Test Templates Page</a>';
}

if ($dashboard_page) {
    echo '<a href="' . get_permalink($dashboard_page->ID) . '" target="_blank" class="test-button">👤 Test User Dashboard</a>';
}

echo '<a href="' . admin_url('admin.php?page=chatgabi-templates') . '" target="_blank" class="test-button">⚙️ Test Admin Templates</a>';
echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Re-run Tests</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';

// Summary for user
echo '<div class="info">';
echo '<h3>📋 Summary</h3>';
echo '<p><strong>Issue 1 (Templates Page Navigation):</strong> ' . ($test_results['page_navigation'] ?? 'NOT TESTED') . '</p>';
echo '<p><strong>Issue 2 (Admin Dashboard Management):</strong> ' . ($test_results['admin_management'] ?? 'NOT TESTED') . '</p>';
echo '<p><strong>Issue 3 (User Dashboard Visibility):</strong> ' . ($test_results['user_dashboard'] ?? 'NOT TESTED') . '</p>';
echo '</div>';
?>

</body>
</html>

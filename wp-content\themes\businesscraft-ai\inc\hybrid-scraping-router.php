<?php
/**
 * Hybrid Scraping Router for ChatGABI
 * 
 * Smart routing system that optimizes cost-performance ratio by using
 * native PHP scraping for 75% of sources and commercial APIs for complex sites.
 *
 * @package ChatGABI
 * @since 1.4.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_Hybrid_Scraping_Router {
    
    private $monthly_budget = 950; // $950 target budget
    private $current_usage = array();
    private $api_handlers = array();
    
    // African high-value domains requiring commercial APIs
    // UPDATED: Replaced ScrapingBee with Bright Data for cost optimization
    private $priority_domains = array(
        // Ghana - Government & Financial Sites → Bright Data
        'bog.gov.gh' => array('complexity' => 9, 'api' => 'brightdata', 'value' => 'high', 'type' => 'government'),
        'gse.com.gh' => array('complexity' => 8, 'api' => 'brightdata', 'value' => 'high', 'type' => 'financial'),
        'gipc.gov.gh' => array('complexity' => 7, 'api' => 'brightdata', 'value' => 'medium', 'type' => 'government'),

        // Kenya - Government & Financial Sites → Bright Data
        'nse.co.ke' => array('complexity' => 9, 'api' => 'brightdata', 'value' => 'high', 'type' => 'financial'),
        'centralbank.go.ke' => array('complexity' => 8, 'api' => 'brightdata', 'value' => 'high', 'type' => 'government'),
        'kam.co.ke' => array('complexity' => 6, 'api' => 'scraperapi', 'value' => 'medium', 'type' => 'business'),

        // Nigeria - Government & Financial Sites → Bright Data
        'nse.com.ng' => array('complexity' => 9, 'api' => 'brightdata', 'value' => 'high', 'type' => 'financial'),
        'cbn.gov.ng' => array('complexity' => 9, 'api' => 'brightdata', 'value' => 'high', 'type' => 'government'),
        'nipc.gov.ng' => array('complexity' => 7, 'api' => 'scraperapi', 'value' => 'medium', 'type' => 'government'),

        // South Africa - Government & Financial Sites → Bright Data
        'jse.co.za' => array('complexity' => 9, 'api' => 'brightdata', 'value' => 'high', 'type' => 'financial'),
        'resbank.co.za' => array('complexity' => 8, 'api' => 'brightdata', 'value' => 'high', 'type' => 'government'),
        'thedti.gov.za' => array('complexity' => 6, 'api' => 'scraperapi', 'value' => 'medium', 'type' => 'government')
    );
    
    public function __construct() {
        $this->init_api_handlers();
        $this->load_usage_tracking();
    }

    /**
     * Load usage tracking data
     */
    private function load_usage_tracking() {
        $this->current_usage = $this->get_current_month_usage();
    }
    
    /**
     * Initialize API handlers
     * UPDATED: Replaced ScrapingBee with Bright Data
     */
    private function init_api_handlers() {
        $this->api_handlers = array(
            'brightdata' => new ChatGABI_BrightData_Handler(),
            'scraperapi' => new ChatGABI_ScraperAPI_Handler(),
            'native' => new ChatGABI_Native_Scraper()
        );
    }
    
    /**
     * Smart routing decision engine
     */
    public function route_scraping_request($source, $country, $sector) {
        $routing_decision = $this->analyze_routing_requirements($source, $country, $sector);
        
        // Check budget constraints
        if (!$this->is_within_budget($routing_decision['api'], $routing_decision['estimated_cost'])) {
            $routing_decision = $this->fallback_to_native($source);
        }
        
        // Execute scraping with selected method
        return $this->execute_scraping($source, $routing_decision, $country, $sector);
    }
    
    /**
     * Analyze routing requirements
     */
    private function analyze_routing_requirements($source, $country, $sector) {
        $domain = parse_url($source['url'], PHP_URL_HOST);
        $complexity_score = $this->calculate_complexity_score($source, $domain);
        
        // Check if domain is in priority list
        if (isset($this->priority_domains[$domain])) {
            $priority_config = $this->priority_domains[$domain];
            return array(
                'api' => $priority_config['api'],
                'reason' => 'priority_domain',
                'complexity' => $priority_config['complexity'],
                'estimated_cost' => $this->estimate_api_cost($priority_config['api'], $source),
                'value' => $priority_config['value']
            );
        }
        
        // Dynamic complexity analysis - UPDATED for Bright Data
        if ($complexity_score >= 8) {
            return array(
                'api' => 'brightdata',
                'reason' => 'high_complexity',
                'complexity' => $complexity_score,
                'estimated_cost' => $this->estimate_api_cost('brightdata', $source),
                'value' => 'medium'
            );
        } elseif ($complexity_score >= 6) {
            return array(
                'api' => 'scraperapi',
                'reason' => 'medium_complexity',
                'complexity' => $complexity_score,
                'estimated_cost' => $this->estimate_api_cost('scraperapi', $source),
                'value' => 'medium'
            );
        } else {
            return array(
                'api' => 'native',
                'reason' => 'low_complexity',
                'complexity' => $complexity_score,
                'estimated_cost' => 0,
                'value' => 'low'
            );
        }
    }
    
    /**
     * Calculate site complexity score
     */
    private function calculate_complexity_score($source, $domain) {
        $score = 0;
        
        // JavaScript requirements
        if (isset($source['requires_js']) && $source['requires_js']) {
            $score += 4;
        }
        
        // Anti-bot protection indicators
        if (strpos($domain, 'gov.') !== false) $score += 3; // Government sites
        if (strpos($domain, 'bank') !== false) $score += 2; // Banking sites
        if (strpos($domain, 'nse.') !== false) $score += 4; // Stock exchanges
        if (strpos($domain, 'jse.') !== false) $score += 4; // JSE
        
        // Known protection services
        if ($this->has_cloudflare_protection($domain)) $score += 3;
        if ($this->requires_authentication($source)) $score += 2;
        
        // AJAX content indicators
        if (isset($source['ajax_content']) && $source['ajax_content']) {
            $score += 2;
        }
        
        return min($score, 10); // Cap at 10
    }
    
    /**
     * Estimate API cost for request
     * UPDATED: Added Bright Data pay-per-use pricing
     */
    private function estimate_api_cost($api, $source) {
        switch ($api) {
            case 'brightdata':
                // Pay-per-use: $15-20/GB, estimate ~85KB average response
                $estimated_response_kb = $this->estimate_response_size($source);
                $estimated_gb = $estimated_response_kb / (1024 * 1024); // Convert KB to GB
                $cost_per_gb = 15; // $15/GB (conservative estimate)

                // Add compression factor (30% savings)
                $compressed_gb = $estimated_gb * 0.7;

                return $compressed_gb * $cost_per_gb;

            case 'scraperapi':
                $base_cost = 0.00030; // $0.30 per 1K credits
                $credits = 1;
                if (isset($source['requires_js']) && $source['requires_js']) {
                    $credits += 10; // JS rendering
                }
                if (strpos($source['url'], 'amazon') !== false) {
                    $credits = 5; // E-commerce
                }
                return $base_cost * $credits;

            default:
                return 0;
        }
    }

    /**
     * Estimate response size based on site type
     */
    private function estimate_response_size($source) {
        $domain = parse_url($source['url'], PHP_URL_HOST);

        // Government sites (heavy HTML, forms)
        if (strpos($domain, 'gov.') !== false) {
            return 150; // 150KB average
        }

        // Financial sites (data tables, charts)
        if (strpos($domain, 'nse.') !== false || strpos($domain, 'jse.') !== false) {
            return 80; // 80KB average
        }

        // Banking sites
        if (strpos($domain, 'bank') !== false) {
            return 120; // 120KB average
        }

        // Default business sites
        return 60; // 60KB average
    }
    
    /**
     * Check if request is within budget
     */
    private function is_within_budget($api, $estimated_cost) {
        if ($api === 'native') return true;
        
        $current_month_usage = $this->get_current_month_usage();
        $projected_monthly_cost = $current_month_usage + ($estimated_cost * 30 * 24); // Daily projection
        
        return $projected_monthly_cost <= $this->monthly_budget;
    }
    
    /**
     * Execute scraping with selected method
     */
    private function execute_scraping($source, $routing_decision, $country, $sector) {
        $api = $routing_decision['api'];
        $start_time = microtime(true);
        
        try {
            // Execute scraping
            $result = $this->api_handlers[$api]->scrape($source, $country, $sector);
            
            // Track usage and cost with enhanced data for Bright Data
            $this->track_usage($api, $routing_decision['estimated_cost'], true, $source);

            // Log successful routing
            $this->log_routing_decision($source, $routing_decision, true, microtime(true) - $start_time);

            return $result;

        } catch (Exception $e) {
            // Track failed attempt
            $this->track_usage($api, $routing_decision['estimated_cost'], false, $source);
            
            // Try fallback if commercial API failed
            if ($api !== 'native') {
                $fallback_result = $this->api_handlers['native']->scrape($source, $country, $sector);
                $this->log_routing_decision($source, $routing_decision, false, microtime(true) - $start_time, 'fallback_used');
                return $fallback_result;
            }
            
            throw $e;
        }
    }
    
    /**
     * Track API usage and costs
     * UPDATED: Enhanced tracking for Bright Data pay-per-use
     */
    private function track_usage($api, $cost, $success, $source = null, $response_size_kb = 0) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'chatgabi_api_usage_tracking';

        // Calculate data volume for Bright Data
        $data_volume_mb = 0;
        if ($api === 'brightdata' && $source) {
            $estimated_kb = $this->estimate_response_size($source);
            $data_volume_mb = $estimated_kb / 1024; // Convert to MB
        }

        $wpdb->insert(
            $table_name,
            array(
                'api_provider' => $api,
                'request_url' => $source ? $source['url'] : '',
                'cost_usd' => $cost,
                'success' => $success ? 1 : 0,
                'data_volume_mb' => $data_volume_mb,
                'response_time_ms' => 0, // Will be updated by caller
                'timestamp' => current_time('mysql'),
                'month_year' => date('Y-m')
            ),
            array('%s', '%s', '%f', '%d', '%f', '%d', '%s', '%s')
        );
    }
    
    /**
     * Get current month usage
     */
    private function get_current_month_usage() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_api_usage_tracking';
        $current_month = date('Y-m');
        
        $total_cost = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(cost_usd) FROM $table_name WHERE month_year = %s",
            $current_month
        ));
        
        return floatval($total_cost);
    }
    
    /**
     * Get usage statistics
     */
    public function get_usage_statistics() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_api_usage_tracking';
        $current_month = date('Y-m');
        
        $stats = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                api_provider,
                COUNT(*) as total_requests,
                SUM(cost_usd) as total_cost,
                AVG(cost_usd) as avg_cost,
                SUM(success) as successful_requests,
                (SUM(success) / COUNT(*)) * 100 as success_rate
             FROM $table_name 
             WHERE month_year = %s 
             GROUP BY api_provider",
            $current_month
        ), ARRAY_A);
        
        return $stats;
    }

    /**
     * Check if domain has Cloudflare protection
     */
    private function has_cloudflare_protection($domain) {
        // Simple check - in production, this could be more sophisticated
        $headers = @get_headers("https://$domain", 1);
        if (!$headers || !isset($headers['Server'])) {
            return false;
        }

        $server = is_array($headers['Server']) ? $headers['Server'][0] : $headers['Server'];
        return strpos(strtolower($server), 'cloudflare') !== false;
    }

    /**
     * Check if source requires authentication
     */
    private function requires_authentication($source) {
        return isset($source['requires_auth']) && $source['requires_auth'];
    }

    /**
     * Fallback to native scraping
     */
    private function fallback_to_native($source) {
        return array(
            'api' => 'native',
            'reason' => 'budget_exceeded',
            'complexity' => 0,
            'estimated_cost' => 0,
            'value' => 'low'
        );
    }

    /**
     * Log routing decision
     */
    private function log_routing_decision($source, $routing_decision, $success, $response_time, $notes = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . 'chatgabi_advanced_scraping_logs';

        $wpdb->insert(
            $table_name,
            array(
                'source_url' => $source['url'],
                'api_used' => $routing_decision['api'],
                'success' => $success ? 1 : 0,
                'response_time_ms' => round($response_time * 1000),
                'cost_usd' => $routing_decision['estimated_cost'],
                'routing_reason' => $routing_decision['reason'],
                'complexity_score' => $routing_decision['complexity'],
                'notes' => $notes,
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%d', '%d', '%f', '%s', '%d', '%s', '%s')
        );
    }
}

/**
 * Bright Data API Handler
 * Pay-per-use model with advanced proxy network for African sites
 */
class ChatGABI_BrightData_Handler {

    private $api_key;
    private $zone_id;

    public function __construct() {
        $this->api_key = get_option('chatgabi_brightdata_api_key');
        $this->zone_id = get_option('chatgabi_brightdata_zone_id', 'zone_chatgabi_african');
    }

    public function scrape($source, $country, $sector) {
        // Use Bright Data API endpoint (updated format)
        $api_endpoint = 'https://api.brightdata.com/request';

        $request_data = array(
            'zone' => $this->zone_id,
            'url' => $source['url'],
            'format' => 'raw',
            'country' => $this->get_country_code($country)
        );

        // Add JavaScript rendering if needed
        if (isset($source['requires_js']) && $source['requires_js']) {
            $request_data['render_js'] = true;
        }

        // Make API request
        $response = $this->make_brightdata_api_request($api_endpoint, $request_data);

        if (is_wp_error($response)) {
            throw new Exception('Bright Data API request failed: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);

        // Track data consumption for pay-per-use billing
        $this->track_data_consumption($body, $source);

        return $this->process_scraped_content($body, $source, $country, $sector);
    }

    private function make_brightdata_api_request($api_endpoint, $request_data) {
        // Make POST request to Bright Data API
        $response = wp_remote_post($api_endpoint, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->api_key,
                'User-Agent' => 'ChatGABI-African-Scraper/1.0'
            ),
            'body' => json_encode($request_data),
            'timeout' => 60,
            'sslverify' => true
        ));

        return $response;
    }

    private function track_data_consumption($content, $source) {
        $data_size_kb = strlen($content) / 1024;

        // Log data consumption for cost tracking
        global $wpdb;
        $table_name = $wpdb->prefix . 'chatgabi_brightdata_usage';

        $wpdb->insert(
            $table_name,
            array(
                'url' => $source['url'],
                'data_size_kb' => $data_size_kb,
                'timestamp' => current_time('mysql'),
                'month_year' => date('Y-m')
            ),
            array('%s', '%f', '%s', '%s')
        );
    }

    private function get_country_code($country) {
        $country_codes = array(
            'Ghana' => 'GH',
            'Kenya' => 'KE',
            'Nigeria' => 'NG',
            'South Africa' => 'ZA'
        );

        return $country_codes[$country] ?? 'US';
    }

    private function process_scraped_content($content, $source, $country, $sector) {
        // Use existing ChatGABI data extraction logic
        if (class_exists('ChatGABI_Advanced_Web_Scraper')) {
            $scraper = new ChatGABI_Advanced_Web_Scraper();
            return $scraper->extract_with_advanced_selectors($content, $source, $country, $sector);
        }

        return array();
    }
}

/**
 * ScraperAPI Handler
 */
class ChatGABI_ScraperAPI_Handler {
    
    private $api_key;
    
    public function __construct() {
        $this->api_key = get_option('chatgabi_scraperapi_api_key');
    }
    
    public function scrape($source, $country, $sector) {
        $params = array(
            'api_key' => $this->api_key,
            'url' => $source['url'],
            'country_code' => $this->get_country_code($country)
        );
        
        if (isset($source['requires_js']) && $source['requires_js']) {
            $params['render'] = 'true';
        }
        
        $api_url = 'http://api.scraperapi.com/?' . http_build_query($params);
        
        $response = wp_remote_get($api_url, array(
            'timeout' => 60
        ));
        
        if (is_wp_error($response)) {
            throw new Exception('ScraperAPI request failed: ' . $response->get_error_message());
        }
        
        $body = wp_remote_retrieve_body($response);
        return $this->process_scraped_content($body, $source, $country, $sector);
    }
    
    private function get_country_code($country) {
        $country_codes = array(
            'Ghana' => 'gh',
            'Kenya' => 'ke',
            'Nigeria' => 'ng', 
            'South Africa' => 'za'
        );
        
        return $country_codes[$country] ?? 'us';
    }
    
    private function process_scraped_content($content, $source, $country, $sector) {
        // Use existing ChatGABI data extraction logic
        if (class_exists('ChatGABI_Advanced_Web_Scraper')) {
            $scraper = new ChatGABI_Advanced_Web_Scraper();
            return $scraper->extract_with_advanced_selectors($content, $source, $country, $sector);
        }
        
        return array();
    }
}

/**
 * Native PHP Scraper Handler
 */
class ChatGABI_Native_Scraper {
    
    public function scrape($source, $country, $sector) {
        // Use existing ChatGABI native scraping
        if (class_exists('ChatGABI_Advanced_Web_Scraper')) {
            $scraper = new ChatGABI_Advanced_Web_Scraper();
            return $scraper->advanced_scrape_source($source, $country, $sector);
        }
        
        return array();
    }
}
?>

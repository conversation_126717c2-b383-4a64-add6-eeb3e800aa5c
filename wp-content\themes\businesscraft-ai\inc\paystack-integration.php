<?php
/**
 * Paystack Integration for BusinessCraft AI
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get user's country based on IP address
 */
function businesscraft_ai_get_user_country() {
    // Check if country is already stored in session
    if (isset($_SESSION['businesscraft_ai_country'])) {
        return $_SESSION['businesscraft_ai_country'];
    }

    // Try to get country from user meta if logged in
    $user_id = get_current_user_id();
    if ($user_id) {
        $stored_country = get_user_meta($user_id, 'businesscraft_ai_country', true);
        if ($stored_country) {
            return $stored_country;
        }
    }

    // Get user's IP address
    $ip_address = businesscraft_ai_get_user_ip();

    // Try to detect country from IP
    $country = businesscraft_ai_detect_country_from_ip($ip_address);

    // Store in session
    if (!session_id()) {
        session_start();
    }
    $_SESSION['businesscraft_ai_country'] = $country;

    // Store in user meta if logged in
    if ($user_id) {
        update_user_meta($user_id, 'businesscraft_ai_country', $country);
    }

    return $country;
}

/**
 * Get user's IP address
 */
function businesscraft_ai_get_user_ip() {
    // Check for various headers that might contain the real IP
    $ip_headers = array(
        'HTTP_CF_CONNECTING_IP',     // Cloudflare
        'HTTP_CLIENT_IP',            // Proxy
        'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
        'HTTP_X_FORWARDED',          // Proxy
        'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
        'HTTP_FORWARDED_FOR',        // Proxy
        'HTTP_FORWARDED',            // Proxy
        'REMOTE_ADDR'                // Standard
    );

    foreach ($ip_headers as $header) {
        if (!empty($_SERVER[$header])) {
            $ip = $_SERVER[$header];
            // Handle comma-separated IPs (take the first one)
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            // Validate IP address
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    // Fallback to REMOTE_ADDR even if it's private (for local development)
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Detect country from IP address
 */
function businesscraft_ai_detect_country_from_ip($ip_address) {
    // Default to Ghana if detection fails
    $default_country = 'GH';

    // Skip detection for local/private IPs
    if (!filter_var($ip_address, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
        return $default_country;
    }

    // Try multiple IP geolocation services
    $country = businesscraft_ai_try_ip_geolocation($ip_address);

    // Validate that it's one of our supported countries
    $supported_countries = array('GH', 'NG', 'KE', 'ZA');
    if (!in_array($country, $supported_countries)) {
        return $default_country;
    }

    return $country;
}

/**
 * Try IP geolocation services
 */
function businesscraft_ai_try_ip_geolocation($ip_address) {
    // Check cache first
    $cache_key = 'bcai_country_' . md5($ip_address);
    $cached_country = get_transient($cache_key);
    if ($cached_country) {
        return $cached_country;
    }

    $country = null;

    // Try ip-api.com (free, no API key required)
    $response = wp_remote_get("http://ip-api.com/json/{$ip_address}?fields=countryCode", array(
        'timeout' => 5,
        'user-agent' => 'BusinessCraft AI WordPress Theme'
    ));

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
        $data = json_decode(wp_remote_retrieve_body($response), true);
        if (isset($data['countryCode'])) {
            $country = $data['countryCode'];
        }
    }

    // Fallback to ipinfo.io if first service fails
    if (!$country) {
        $response = wp_remote_get("https://ipinfo.io/{$ip_address}/country", array(
            'timeout' => 5,
            'user-agent' => 'BusinessCraft AI WordPress Theme'
        ));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $country = trim(wp_remote_retrieve_body($response));
        }
    }

    // Cache the result for 24 hours
    if ($country) {
        set_transient($cache_key, $country, 24 * HOUR_IN_SECONDS);
    }

    return $country ?: 'GH'; // Default to Ghana
}

/**
 * Get currency configuration for supported countries
 */
function businesscraft_ai_get_currency_config() {
    return array(
        'GH' => array(
            'currency' => 'GHS',
            'symbol' => '₵',
            'name' => 'Ghanaian Cedi',
            'paystack_supported' => true
        ),
        'NG' => array(
            'currency' => 'NGN',
            'symbol' => '₦',
            'name' => 'Nigerian Naira',
            'paystack_supported' => true
        ),
        'KE' => array(
            'currency' => 'KES',
            'symbol' => 'KSh',
            'name' => 'Kenyan Shilling',
            'paystack_supported' => true
        ),
        'ZA' => array(
            'currency' => 'ZAR',
            'symbol' => 'R',
            'name' => 'South African Rand',
            'paystack_supported' => true
        ),
        'DEFAULT' => array(
            'currency' => 'USD',
            'symbol' => '$',
            'name' => 'US Dollar',
            'paystack_supported' => false
        )
    );
}

/**
 * Get currency for user's country
 */
function businesscraft_ai_get_user_currency() {
    $country = businesscraft_ai_get_user_country();
    $currency_config = businesscraft_ai_get_currency_config();

    if (isset($currency_config[$country])) {
        return $currency_config[$country];
    }

    return $currency_config['DEFAULT'];
}

/**
 * Get exchange rates from USD to local currencies
 */
function businesscraft_ai_get_exchange_rates() {
    // Check cache first
    $cache_key = 'bcai_exchange_rates';
    $cached_rates = get_transient($cache_key);
    if ($cached_rates) {
        return $cached_rates;
    }

    // Default exchange rates (fallback)
    $default_rates = array(
        'GHS' => 12.0,   // 1 USD = 12 GHS
        'NGN' => 750.0,  // 1 USD = 750 NGN
        'KES' => 130.0,  // 1 USD = 130 KES
        'ZAR' => 18.0,   // 1 USD = 18 ZAR
        'USD' => 1.0
    );

    // Try to get real-time rates from a free API
    $rates = businesscraft_ai_fetch_live_exchange_rates();

    // If live rates failed, use default rates
    if (!$rates) {
        $rates = $default_rates;
    }

    // Cache rates for 6 hours
    set_transient($cache_key, $rates, 6 * HOUR_IN_SECONDS);

    return $rates;
}

/**
 * Fetch live exchange rates
 */
function businesscraft_ai_fetch_live_exchange_rates() {
    // Try exchangerate-api.com (free tier: 1500 requests/month)
    $response = wp_remote_get('https://api.exchangerate-api.com/v4/latest/USD', array(
        'timeout' => 10,
        'user-agent' => 'BusinessCraft AI WordPress Theme'
    ));

    if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
        return false;
    }

    $data = json_decode(wp_remote_retrieve_body($response), true);

    if (!isset($data['rates'])) {
        return false;
    }

    // Extract only the currencies we need
    $needed_currencies = array('GHS', 'NGN', 'KES', 'ZAR', 'USD');
    $rates = array();

    foreach ($needed_currencies as $currency) {
        if (isset($data['rates'][$currency])) {
            $rates[$currency] = $data['rates'][$currency];
        }
    }

    // Ensure USD is always 1.0
    $rates['USD'] = 1.0;

    return count($rates) >= 4 ? $rates : false;
}

/**
 * Convert USD amount to local currency
 */
function businesscraft_ai_convert_to_local_currency($usd_amount, $target_currency) {
    if ($target_currency === 'USD') {
        return $usd_amount;
    }

    $exchange_rates = businesscraft_ai_get_exchange_rates();

    if (isset($exchange_rates[$target_currency])) {
        return round($usd_amount * $exchange_rates[$target_currency], 2);
    }

    return $usd_amount;
}

/**
 * Get package pricing in user's local currency
 */
function businesscraft_ai_get_localized_package_pricing() {
    $user_currency = businesscraft_ai_get_user_currency();
    $currency_code = $user_currency['currency'];
    $currency_symbol = $user_currency['symbol'];

    // Base USD prices
    $usd_packages = array(
        'starter' => array('credits' => 500, 'price_usd' => 5.00),
        'growth' => array('credits' => 1500, 'price_usd' => 15.00),
        'business' => array('credits' => 3000, 'price_usd' => 30.00),
    );

    $localized_packages = array();

    foreach ($usd_packages as $package_key => $package_data) {
        $local_price = businesscraft_ai_convert_to_local_currency($package_data['price_usd'], $currency_code);

        $localized_packages[$package_key] = array(
            'credits' => $package_data['credits'],
            'price_usd' => $package_data['price_usd'],
            'price_local' => $local_price,
            'currency' => $currency_code,
            'currency_symbol' => $currency_symbol,
            'formatted_price' => $currency_symbol . number_format($local_price, 2)
        );
    }

    return $localized_packages;
}

/**
 * Initiate Paystack payment
 */
function businesscraft_ai_initiate_paystack_payment($email, $package, $user_id) {
    error_log('BusinessCraft AI: Paystack payment function called');

    try {
        $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ? BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY : get_option('businesscraft_ai_paystack_secret_key');

        if (empty($secret_key)) {
            error_log('BusinessCraft AI: Paystack secret key not configured');
            throw new Exception(__('Paystack secret key not configured', 'businesscraft-ai'));
        }

        // Check if required functions exist
        if (!function_exists('businesscraft_ai_get_localized_package_pricing')) {
            error_log('BusinessCraft AI: Function businesscraft_ai_get_localized_package_pricing not found');
            throw new Exception(__('Pricing function not available', 'businesscraft-ai'));
        }

        // Get localized pricing
        error_log('BusinessCraft AI: Getting localized pricing');
        $localized_packages = businesscraft_ai_get_localized_package_pricing();

        if (!isset($localized_packages[$package])) {
            error_log('BusinessCraft AI: Package not found in localized packages: ' . $package);
            throw new Exception(__('Invalid package selected', 'businesscraft-ai'));
        }

        $package_data = $localized_packages[$package];
        $user_currency = businesscraft_ai_get_user_currency();

        error_log('BusinessCraft AI: Package data: ' . json_encode($package_data));
        error_log('BusinessCraft AI: User currency: ' . json_encode($user_currency));

        // Log server IP for debugging
        $server_ip = businesscraft_ai_get_server_ip();
        error_log('BusinessCraft AI: Server IP making Paystack call: ' . $server_ip);
        error_log('BusinessCraft AI: Payment currency: ' . $package_data['currency'] . ', Amount: ' . $package_data['price_local']);

        $reference = 'bcai_' . $user_id . '_' . time() . '_' . wp_rand(1000, 9999);

        // Convert amount to smallest currency unit (kobo for NGN, pesewas for GHS, etc.)
        $amount_in_subunit = $package_data['price_local'] * 100;

        $request_data = array(
            'email' => $email,
            'amount' => $amount_in_subunit,
            'currency' => $package_data['currency'],
            'reference' => $reference,
            'callback_url' => home_url('/payment-success/'),
            'metadata' => array(
                'user_id' => $user_id,
                'package' => $package,
                'credits' => $package_data['credits'],
                'original_currency' => $package_data['currency'],
                'original_amount' => $package_data['price_local'],
                'usd_amount' => $package_data['price_usd'],
            ),
        );

        error_log('BusinessCraft AI: Making Paystack API call');
        $response = wp_remote_post('https://api.paystack.co/transaction/initialize', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $secret_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($request_data),
            'timeout' => 30,
        ));

        if (is_wp_error($response)) {
            error_log('BusinessCraft AI: WP Error: ' . $response->get_error_message());
            throw new Exception(__('Failed to connect to Paystack API', 'businesscraft-ai'));
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body, true);

        error_log('BusinessCraft AI: Paystack response code: ' . $response_code);
        error_log('BusinessCraft AI: Paystack response body: ' . $response_body);

        if ($response_code !== 200 || !$response_data['status']) {
            $error_message = isset($response_data['message'])
                ? $response_data['message']
                : __('Payment initialization failed', 'businesscraft-ai');

            // Log detailed error for IP whitelist issues
            if (strpos($error_message, 'IP address') !== false || strpos($error_message, 'not allowed') !== false) {
                error_log('BusinessCraft AI Paystack IP Error: ' . $error_message);
                error_log('Server IP: ' . businesscraft_ai_get_server_ip());
                error_log('Response Code: ' . $response_code);
                error_log('Full Response: ' . $response_body);
            }

            throw new Exception($error_message);
        }

        // Log the payment initiation
        businesscraft_ai_log_payment_initiation($user_id, $reference, $package, $amount_in_subunit);

        error_log('BusinessCraft AI: Payment initiation successful');
        return array(
            'status' => 'success',
            'data' => $response_data['data'],
            'reference' => $reference,
        );

    } catch (Exception $e) {
        error_log('BusinessCraft AI: Exception in payment function: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Verify Paystack payment
 */
function businesscraft_ai_verify_paystack_payment($reference) {
    $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ? BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY : get_option('businesscraft_ai_paystack_secret_key');

    if (empty($secret_key)) {
        return new WP_Error('missing_secret_key', 'Paystack secret key not configured');
    }

    $response = wp_remote_get("https://api.paystack.co/transaction/verify/{$reference}", array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $secret_key,
        ),
        'timeout' => 30,
    ));

    if (is_wp_error($response)) {
        return new WP_Error('api_error', 'Failed to verify payment');
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    $response_data = json_decode($response_body, true);

    if ($response_code !== 200 || !$response_data['status']) {
        return new WP_Error('verification_failed', 'Payment verification failed');
    }

    return $response_data['data'];
}

/**
 * Verify Paystack webhook signature
 */
function businesscraft_ai_verify_paystack_signature($payload, $signature) {
    $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ? BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY : get_option('businesscraft_ai_paystack_secret_key');

    if (empty($secret_key)) {
        return false;
    }

    $computed_signature = hash_hmac('sha512', $payload, $secret_key);

    return hash_equals($signature, $computed_signature);
}

/**
 * Get package credits
 */
function businesscraft_ai_get_package_credits($package) {
    $packages = array(
        'starter' => 500,
        'growth' => 1500,
        'business' => 3000,
    );

    return isset($packages[$package]) ? $packages[$package] : 0;
}

/**
 * Get package details
 */
function businesscraft_ai_get_package_details($package) {
    $packages = array(
        'starter' => array(
            'name' => __('Starter Pack', 'businesscraft-ai'),
            'credits' => 500,
            'price' => 5.00,
            'currency' => 'USD',
            'features' => array(
                __('500 AI Credits', 'businesscraft-ai'),
                __('GPT-3.5 Turbo Access', 'businesscraft-ai'),
                __('Basic Templates', 'businesscraft-ai'),
                __('Email Support', 'businesscraft-ai'),
            ),
        ),
        'growth' => array(
            'name' => __('Growth Pack', 'businesscraft-ai'),
            'credits' => 1500,
            'price' => 15.00,
            'currency' => 'USD',
            'tier' => 'ultra',
            'features' => array(
                __('1,500 AI Credits', 'businesscraft-ai'),
                __('GPT-4 Turbo Access', 'businesscraft-ai'),
                __('Premium Templates', 'businesscraft-ai'),
                __('Priority Support', 'businesscraft-ai'),
                __('Advanced Analytics', 'businesscraft-ai'),
            ),
        ),
        'business' => array(
            'name' => __('Business Pack', 'businesscraft-ai'),
            'credits' => 3000,
            'price' => 30.00,
            'currency' => 'USD',
            'tier' => 'ultra',
            'features' => array(
                __('3,000 AI Credits', 'businesscraft-ai'),
                __('GPT-4 Turbo Access', 'businesscraft-ai'),
                __('All Templates & Tools', 'businesscraft-ai'),
                __('Priority Support', 'businesscraft-ai'),
                __('Advanced Analytics', 'businesscraft-ai'),
                __('Custom Integrations', 'businesscraft-ai'),
            ),
        ),
    );

    return isset($packages[$package]) ? $packages[$package] : null;
}

/**
 * Log payment initiation
 */
function businesscraft_ai_log_payment_initiation($user_id, $reference, $package, $amount) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_transactions';

    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'reference' => $reference,
            'package' => $package,
            'amount' => $amount,
            'status' => 'initiated',
            'created_at' => current_time('mysql'),
        ),
        array('%d', '%s', '%s', '%f', '%s', '%s')
    );
}

/**
 * Update payment status
 */
function businesscraft_ai_update_payment_status($reference, $status, $transaction_data = null) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_transactions';

    $update_data = array(
        'status' => $status,
        'updated_at' => current_time('mysql'),
    );

    if ($transaction_data) {
        $update_data['paystack_data'] = json_encode($transaction_data);
    }

    $wpdb->update(
        $table_name,
        $update_data,
        array('reference' => $reference),
        array('%s', '%s', '%s'),
        array('%s')
    );
}

/**
 * Get user transactions
 */
function businesscraft_ai_get_user_transactions($user_id, $limit = 10) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_transactions';

    $results = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE user_id = %d ORDER BY created_at DESC LIMIT %d",
            $user_id,
            $limit
        )
    );

    return $results;
}

/**
 * Check if Paystack is configured
 */
function businesscraft_ai_is_paystack_configured() {
    $public_key = defined('BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY') ? BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY : get_option('businesscraft_ai_paystack_public_key');
    $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ? BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY : get_option('businesscraft_ai_paystack_secret_key');

    return !empty($public_key) && !empty($secret_key);
}

/**
 * Test Paystack API connection
 */
function businesscraft_ai_test_paystack_connection() {
    $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ? BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY : get_option('businesscraft_ai_paystack_secret_key');

    if (empty($secret_key)) {
        return new WP_Error('missing_secret_key', 'Secret key not configured');
    }

    $response = wp_remote_get('https://api.paystack.co/bank', array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $secret_key,
        ),
        'timeout' => 10,
    ));

    if (is_wp_error($response)) {
        return $response;
    }

    $response_code = wp_remote_retrieve_response_code($response);

    if ($response_code === 200) {
        return true;
    } else {
        return new WP_Error('api_error', 'API connection failed');
    }
}

/**
 * Get supported currencies
 */
function businesscraft_ai_get_supported_currencies() {
    return array(
        'USD' => array(
            'name' => 'US Dollar',
            'symbol' => '$',
            'countries' => array('Ghana', 'Kenya', 'Nigeria', 'South Africa'),
        ),
        'NGN' => array(
            'name' => 'Nigerian Naira',
            'symbol' => '₦',
            'countries' => array('Nigeria'),
        ),
        'GHS' => array(
            'name' => 'Ghanaian Cedi',
            'symbol' => '₵',
            'countries' => array('Ghana'),
        ),
        'KES' => array(
            'name' => 'Kenyan Shilling',
            'symbol' => 'KSh',
            'countries' => array('Kenya'),
        ),
        'ZAR' => array(
            'name' => 'South African Rand',
            'symbol' => 'R',
            'countries' => array('South Africa'),
        ),
    );
}

/**
 * Convert amount to local currency
 */
function businesscraft_ai_convert_currency($amount, $from_currency, $to_currency) {
    // This is a simplified conversion - in production, you'd use a real exchange rate API
    $exchange_rates = array(
        'USD_NGN' => 750,
        'USD_GHS' => 12,
        'USD_KES' => 130,
        'USD_ZAR' => 18,
    );

    $rate_key = $from_currency . '_' . $to_currency;

    if (isset($exchange_rates[$rate_key])) {
        return $amount * $exchange_rates[$rate_key];
    }

    return $amount; // Return original amount if no conversion rate found
}

/**
 * Get server's public IP address
 */
function businesscraft_ai_get_server_ip() {
    // Try multiple methods to get server IP
    $ip_services = array(
        'https://ipinfo.io/ip',
        'https://api.ipify.org',
        'https://ifconfig.me/ip',
        'https://icanhazip.com',
    );

    foreach ($ip_services as $service) {
        $response = wp_remote_get($service, array(
            'timeout' => 10,
            'user-agent' => 'BusinessCraft-AI/1.0'
        ));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $ip = trim(wp_remote_retrieve_body($response));
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    // Fallback to server variables
    $server_ips = array(
        $_SERVER['SERVER_ADDR'] ?? '',
        $_SERVER['LOCAL_ADDR'] ?? '',
        $_SERVER['HTTP_X_FORWARDED_FOR'] ?? '',
        $_SERVER['HTTP_X_REAL_IP'] ?? '',
        $_SERVER['REMOTE_ADDR'] ?? '',
    );

    foreach ($server_ips as $ip) {
        if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return $ip;
        }
    }

    return 'Unable to determine server IP';
}

/**
 * Check and display IP whitelist status
 */
function businesscraft_ai_check_ip_whitelist_status() {
    $server_ip = businesscraft_ai_get_server_ip();
    $secret_key = get_option('businesscraft_ai_paystack_secret_key');

    if (empty($secret_key)) {
        return array(
            'status' => 'error',
            'message' => 'Paystack secret key not configured',
            'server_ip' => $server_ip
        );
    }

    // Test API call to check IP whitelist
    $response = wp_remote_get('https://api.paystack.co/bank', array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $secret_key,
        ),
        'timeout' => 10,
    ));

    if (is_wp_error($response)) {
        return array(
            'status' => 'error',
            'message' => 'Network error: ' . $response->get_error_message(),
            'server_ip' => $server_ip
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    if ($response_code === 200) {
        return array(
            'status' => 'success',
            'message' => 'IP address is whitelisted',
            'server_ip' => $server_ip
        );
    } else {
        $error_data = json_decode($response_body, true);
        $error_message = isset($error_data['message']) ? $error_data['message'] : 'Unknown error';

        return array(
            'status' => 'error',
            'message' => $error_message,
            'server_ip' => $server_ip,
            'response_code' => $response_code
        );
    }
}

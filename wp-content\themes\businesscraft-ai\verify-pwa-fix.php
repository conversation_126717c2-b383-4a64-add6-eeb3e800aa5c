<?php
/**
 * Quick PWA Fix Verification
 * 
 * This script quickly verifies that the PWA manifest fix is working
 */

// Load WordPress
require_once(dirname(dirname(dirname(__DIR__))) . '/wp-load.php');

echo "<h1>PWA Fix Verification</h1>\n";

// Test 1: Check if functions exist
echo "<h2>Functions Check</h2>\n";
if (function_exists('chatgabi_add_pwa_meta_tags')) {
    echo "✅ chatgabi_add_pwa_meta_tags function exists\n";
} else {
    echo "❌ chatgabi_add_pwa_meta_tags function missing\n";
}

// Test 2: Check hook registration
echo "<h2>Hook Registration</h2>\n";
if (has_action('wp_head', 'chatgabi_add_pwa_meta_tags')) {
    echo "✅ PWA meta tags function is hooked to wp_head\n";
} else {
    echo "❌ PWA meta tags function is NOT hooked to wp_head\n";
}

// Test 3: Test function output
echo "<h2>Function Output Test</h2>\n";
if (function_exists('chatgabi_add_pwa_meta_tags')) {
    echo "Function output:\n";
    echo "<pre>";
    ob_start();
    chatgabi_add_pwa_meta_tags();
    $output = ob_get_clean();
    echo htmlspecialchars($output);
    echo "</pre>";
    
    if (strpos($output, 'rel="manifest"') !== false) {
        echo "✅ Manifest link found in output\n";
    } else {
        echo "❌ Manifest link NOT found in output\n";
    }
} else {
    echo "❌ Cannot test - function not available\n";
}

// Test 4: Check manifest file
echo "<h2>Manifest File Check</h2>\n";
$manifest_path = get_template_directory() . '/manifest.json';
if (file_exists($manifest_path)) {
    echo "✅ Manifest file exists\n";
    echo "File size: " . filesize($manifest_path) . " bytes\n";
    
    $manifest_url = get_template_directory_uri() . '/manifest.json';
    echo "Manifest URL: $manifest_url\n";
} else {
    echo "❌ Manifest file missing\n";
}

// Test 5: Check header.php fallback
echo "<h2>Header.php Fallback Check</h2>\n";
$header_path = get_template_directory() . '/header.php';
if (file_exists($header_path)) {
    $header_content = file_get_contents($header_path);
    if (strpos($header_content, 'rel="manifest"') !== false) {
        echo "✅ Fallback manifest link found in header.php\n";
    } else {
        echo "❌ Fallback manifest link NOT found in header.php\n";
    }
} else {
    echo "❌ header.php file not found\n";
}

echo "\n<h2>Summary</h2>\n";
echo "If all tests pass, the PWA manifest should now be properly linked in all pages.\n";
echo "You can verify by viewing the page source of any WordPress page and looking for the manifest link.\n";
?>

{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "settings": {"appearanceTools": true, "useRootPaddingAwareAlignments": true, "color": {"defaultPalette": false, "defaultGradients": false, "palette": [{"color": "#ffffff", "name": "Base", "slug": "base"}, {"color": "#f8f9fa", "name": "Base Light", "slug": "base-light"}, {"color": "#2c3e50", "name": "Contrast", "slug": "contrast"}, {"color": "#667eea", "name": "Primary", "slug": "primary"}, {"color": "#764ba2", "name": "Secondary", "slug": "secondary"}, {"color": "#e74c3c", "name": "Accent", "slug": "accent"}, {"color": "#27ae60", "name": "Success", "slug": "success"}, {"color": "#f39c12", "name": "Warning", "slug": "warning"}, {"color": "#95a5a6", "name": "Muted", "slug": "muted"}], "gradients": [{"gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "name": "Primary Gradient", "slug": "primary-gradient"}, {"gradient": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "name": "A<PERSON><PERSON>", "slug": "accent-gradient"}, {"gradient": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "name": "<PERSON>", "slug": "cool-gradient"}]}, "typography": {"defaultFontSizes": false, "fontSizes": [{"size": "0.75rem", "slug": "small", "name": "Small"}, {"size": "1rem", "slug": "medium", "name": "Medium"}, {"size": "1.25rem", "slug": "large", "name": "Large"}, {"size": "1.5rem", "slug": "x-large", "name": "Extra Large"}, {"size": "2rem", "slug": "xx-large", "name": "Extra Extra Large"}], "fontFamilies": [{"fontFamily": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif", "name": "System Font", "slug": "system"}, {"fontFamily": "Georgia, serif", "name": "<PERSON><PERSON>", "slug": "serif"}]}, "spacing": {"padding": true, "margin": true, "blockGap": true, "spacingScale": {"steps": 0}, "spacingSizes": [{"size": "0.5rem", "slug": "20", "name": "1"}, {"size": "1rem", "slug": "30", "name": "2"}, {"size": "1.5rem", "slug": "40", "name": "3"}, {"size": "2rem", "slug": "50", "name": "4"}, {"size": "3rem", "slug": "60", "name": "5"}, {"size": "4rem", "slug": "70", "name": "6"}, {"size": "5rem", "slug": "80", "name": "7"}]}, "layout": {"contentSize": "800px", "wideSize": "1200px"}, "border": {"radius": true, "color": true, "style": true, "width": true}, "shadow": {"presets": [{"name": "Natural", "slug": "natural", "shadow": "0 4px 6px rgba(0, 0, 0, 0.1)"}, {"name": "Deep", "slug": "deep", "shadow": "0 10px 25px rgba(0, 0, 0, 0.15)"}, {"name": "<PERSON>", "slug": "sharp", "shadow": "0 2px 10px rgba(0, 0, 0, 0.2)"}]}}, "styles": {"color": {"background": "var(--wp--preset--color--base)", "text": "var(--wp--preset--color--contrast)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--system)", "fontSize": "var(--wp--preset--font-size--medium)", "lineHeight": "1.6"}, "spacing": {"blockGap": "var(--wp--preset--spacing--30)"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--primary)"}, ":hover": {"color": {"text": "var(--wp--preset--color--secondary)"}}}, "button": {"color": {"background": "var(--wp--preset--gradient--primary-gradient)", "text": "var(--wp--preset--color--base)"}, "border": {"radius": "6px"}, "spacing": {"padding": {"top": "0.75rem", "right": "1.5rem", "bottom": "0.75rem", "left": "1.5rem"}}, "typography": {"fontWeight": "500"}}, "h1": {"typography": {"fontSize": "var(--wp--preset--font-size--xx-large)", "fontWeight": "700", "lineHeight": "1.2"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--40)"}}}, "h2": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)", "fontWeight": "600", "lineHeight": "1.3"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--30)"}}}, "h3": {"typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontWeight": "600", "lineHeight": "1.4"}}}, "blocks": {"core/paragraph": {"spacing": {"margin": {"bottom": "var(--wp--preset--spacing--30)"}}}, "core/group": {"spacing": {"padding": {"top": "var(--wp--preset--spacing--50)", "bottom": "var(--wp--preset--spacing--50)"}}}, "core/columns": {"spacing": {"margin": {"bottom": "var(--wp--preset--spacing--50)"}}}}}, "customTemplates": [{"name": "chat-page", "title": "<PERSON><PERSON>", "postTypes": ["page"]}, {"name": "pricing-page", "title": "Pricing Page", "postTypes": ["page"]}], "templateParts": [{"name": "header", "title": "Header", "area": "header"}, {"name": "footer", "title": "Footer", "area": "footer"}]}
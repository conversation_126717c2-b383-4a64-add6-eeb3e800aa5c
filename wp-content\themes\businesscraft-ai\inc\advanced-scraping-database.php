<?php
/**
 * Advanced Scraping Database Schema
 * 
 * Database tables and management for the advanced web scraping system
 * including logging, performance metrics, and data quality tracking.
 *
 * @package ChatGABI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Advanced Scraping Database Manager
 */
class ChatGABI_Advanced_Scraping_Database {
    
    private $table_prefix;
    
    public function __construct() {
        global $wpdb;
        $this->table_prefix = $wpdb->prefix . 'chatgabi_';
    }
    
    /**
     * Create all advanced scraping tables
     */
    public function create_tables() {
        $this->create_advanced_scraping_logs_table();
        $this->create_ai_agent_logs_table();
        $this->create_performance_metrics_table();
        $this->create_data_quality_logs_table();
        $this->create_source_reliability_table();
        $this->create_scraped_data_archive_table();
        $this->create_anomaly_detection_logs_table();
        $this->create_cross_validation_results_table();
        $this->create_api_usage_tracking_table();
        $this->create_brightdata_usage_table();
    }
    
    /**
     * Create advanced scraping logs table
     */
    private function create_advanced_scraping_logs_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'advanced_scraping_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            worker_id varchar(100) DEFAULT NULL,
            action varchar(100) NOT NULL,
            message text NOT NULL,
            status varchar(20) NOT NULL,
            country varchar(50) DEFAULT NULL,
            sector varchar(255) DEFAULT NULL,
            source_name varchar(255) DEFAULT NULL,
            source_type varchar(50) DEFAULT NULL,
            scraping_method varchar(50) DEFAULT NULL,
            data_points_extracted int DEFAULT 0,
            processing_time_ms int DEFAULT 0,
            memory_usage_mb float DEFAULT 0,
            error_details text DEFAULT NULL,
            performance_data json DEFAULT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY worker_id (worker_id),
            KEY action (action),
            KEY status (status),
            KEY country (country),
            KEY sector (sector),
            KEY source_name (source_name),
            KEY timestamp (timestamp),
            KEY performance_idx (status, timestamp, country)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create AI agent logs table
     */
    private function create_ai_agent_logs_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'ai_agent_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            agent_type varchar(50) NOT NULL,
            action varchar(100) NOT NULL,
            message text NOT NULL,
            input_data json DEFAULT NULL,
            output_data json DEFAULT NULL,
            processing_time_ms int DEFAULT 0,
            tokens_used int DEFAULT 0,
            api_cost_usd decimal(10,6) DEFAULT 0,
            success boolean DEFAULT true,
            error_details text DEFAULT NULL,
            performance_data json DEFAULT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY agent_type (agent_type),
            KEY action (action),
            KEY success (success),
            KEY timestamp (timestamp),
            KEY cost_tracking (agent_type, timestamp, api_cost_usd)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create performance metrics table
     */
    private function create_performance_metrics_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'performance_metrics';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            cycle_type varchar(50) NOT NULL,
            start_time datetime NOT NULL,
            end_time datetime DEFAULT NULL,
            duration_seconds int DEFAULT NULL,
            data_points_processed int DEFAULT 0,
            sources_scraped int DEFAULT 0,
            sources_successful int DEFAULT 0,
            sources_failed int DEFAULT 0,
            success_rate decimal(5,2) DEFAULT 0,
            data_points_per_hour decimal(10,2) DEFAULT 0,
            accuracy_score decimal(5,2) DEFAULT 0,
            confidence_score decimal(5,2) DEFAULT 0,
            anomalies_detected int DEFAULT 0,
            memory_peak_mb float DEFAULT 0,
            cpu_usage_percent float DEFAULT 0,
            network_requests int DEFAULT 0,
            api_calls_made int DEFAULT 0,
            api_cost_total decimal(10,6) DEFAULT 0,
            errors_encountered int DEFAULT 0,
            warnings_generated int DEFAULT 0,
            countries_processed json DEFAULT NULL,
            sectors_processed json DEFAULT NULL,
            metrics_data json DEFAULT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY cycle_type (cycle_type),
            KEY start_time (start_time),
            KEY success_rate (success_rate),
            KEY data_points_per_hour (data_points_per_hour),
            KEY timestamp (timestamp),
            KEY performance_overview (cycle_type, timestamp, success_rate)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create data quality logs table
     */
    private function create_data_quality_logs_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'data_quality_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            country varchar(50) NOT NULL,
            sector varchar(255) NOT NULL,
            data_type varchar(100) NOT NULL,
            source_count int NOT NULL,
            sources_used json NOT NULL,
            verified_value text DEFAULT NULL,
            confidence_score decimal(5,2) NOT NULL,
            validation_method varchar(100) DEFAULT NULL,
            anomalies_detected json DEFAULT NULL,
            cross_validation_results json DEFAULT NULL,
            quality_flags json DEFAULT NULL,
            processing_notes text DEFAULT NULL,
            verification_timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY country_sector (country, sector),
            KEY data_type (data_type),
            KEY confidence_score (confidence_score),
            KEY verification_timestamp (verification_timestamp),
            KEY quality_overview (country, sector, data_type, confidence_score)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create source reliability table
     */
    private function create_source_reliability_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'source_reliability';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            source_name varchar(255) NOT NULL,
            source_url varchar(500) NOT NULL,
            source_type varchar(50) NOT NULL,
            country varchar(50) NOT NULL,
            base_reliability_score decimal(3,2) NOT NULL,
            current_reliability_score decimal(3,2) NOT NULL,
            total_scrapes int DEFAULT 0,
            successful_scrapes int DEFAULT 0,
            failed_scrapes int DEFAULT 0,
            success_rate decimal(5,2) DEFAULT 0,
            avg_response_time_ms int DEFAULT 0,
            last_successful_scrape datetime DEFAULT NULL,
            last_failed_scrape datetime DEFAULT NULL,
            consecutive_failures int DEFAULT 0,
            data_quality_score decimal(3,2) DEFAULT 0,
            anomaly_rate decimal(5,2) DEFAULT 0,
            update_frequency varchar(50) DEFAULT NULL,
            scraping_method varchar(50) DEFAULT NULL,
            sectors_covered json DEFAULT NULL,
            reliability_notes text DEFAULT NULL,
            last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY source_country (source_name, country),
            KEY source_type (source_type),
            KEY country (country),
            KEY current_reliability_score (current_reliability_score),
            KEY success_rate (success_rate),
            KEY last_successful_scrape (last_successful_scrape),
            KEY reliability_overview (country, source_type, current_reliability_score)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create scraped data archive table
     */
    private function create_scraped_data_archive_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'scraped_data_archive';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            country varchar(50) NOT NULL,
            sector varchar(255) NOT NULL,
            source_name varchar(255) NOT NULL,
            source_type varchar(50) NOT NULL,
            data_type varchar(100) NOT NULL,
            raw_value text NOT NULL,
            normalized_value text DEFAULT NULL,
            extraction_method varchar(100) DEFAULT NULL,
            confidence_score decimal(5,2) DEFAULT NULL,
            data_quality_flags json DEFAULT NULL,
            scraping_metadata json DEFAULT NULL,
            scraped_at datetime NOT NULL,
            processed_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY country_sector (country, sector),
            KEY source_name (source_name),
            KEY data_type (data_type),
            KEY scraped_at (scraped_at),
            KEY confidence_score (confidence_score),
            KEY archive_lookup (country, sector, data_type, scraped_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create anomaly detection logs table
     */
    private function create_anomaly_detection_logs_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'anomaly_detection_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            country varchar(50) NOT NULL,
            sector varchar(255) NOT NULL,
            data_type varchar(100) NOT NULL,
            anomaly_type varchar(100) NOT NULL,
            detector_type varchar(50) NOT NULL,
            severity varchar(20) NOT NULL,
            anomaly_value text NOT NULL,
            expected_range text DEFAULT NULL,
            source_name varchar(255) NOT NULL,
            statistical_measures json DEFAULT NULL,
            contextual_data json DEFAULT NULL,
            resolution_status varchar(50) DEFAULT 'pending',
            resolution_notes text DEFAULT NULL,
            false_positive boolean DEFAULT false,
            detected_at datetime DEFAULT CURRENT_TIMESTAMP,
            resolved_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY country_sector (country, sector),
            KEY anomaly_type (anomaly_type),
            KEY detector_type (detector_type),
            KEY severity (severity),
            KEY resolution_status (resolution_status),
            KEY detected_at (detected_at),
            KEY anomaly_overview (country, sector, anomaly_type, severity, detected_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create cross validation results table
     */
    private function create_cross_validation_results_table() {
        global $wpdb;
        
        $table_name = $this->table_prefix . 'cross_validation_results';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            country varchar(50) NOT NULL,
            sector varchar(255) NOT NULL,
            data_type varchar(100) NOT NULL,
            validation_type varchar(100) NOT NULL,
            current_value text NOT NULL,
            reference_value text DEFAULT NULL,
            variance_percentage decimal(5,2) DEFAULT NULL,
            consistency_score decimal(5,2) NOT NULL,
            validation_status varchar(50) NOT NULL,
            reference_source varchar(255) DEFAULT NULL,
            reference_date datetime DEFAULT NULL,
            validation_notes text DEFAULT NULL,
            validation_metadata json DEFAULT NULL,
            validated_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY country_sector (country, sector),
            KEY data_type (data_type),
            KEY validation_type (validation_type),
            KEY consistency_score (consistency_score),
            KEY validation_status (validation_status),
            KEY validated_at (validated_at),
            KEY validation_overview (country, sector, validation_type, consistency_score)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create API usage tracking table
     */
    private function create_api_usage_tracking_table() {
        global $wpdb;

        $table_name = $this->table_prefix . 'api_usage_tracking';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            api_provider varchar(50) NOT NULL,
            request_url varchar(500) NOT NULL,
            country varchar(50) NOT NULL,
            sector varchar(255) DEFAULT NULL,
            source_name varchar(255) DEFAULT NULL,
            request_type varchar(50) DEFAULT 'standard',
            credits_used int DEFAULT 1,
            cost_usd decimal(10,6) DEFAULT 0,
            success boolean DEFAULT true,
            response_time_ms int DEFAULT 0,
            error_message text DEFAULT NULL,
            routing_reason varchar(100) DEFAULT NULL,
            fallback_used boolean DEFAULT false,
            month_year varchar(7) NOT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY api_provider (api_provider),
            KEY country (country),
            KEY success (success),
            KEY month_year (month_year),
            KEY cost_tracking (api_provider, month_year, cost_usd),
            KEY performance_tracking (api_provider, success, response_time_ms),
            KEY usage_overview (api_provider, month_year, success, cost_usd)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create Bright Data usage tracking table
     */
    private function create_brightdata_usage_table() {
        global $wpdb;

        $table_name = $this->table_prefix . 'brightdata_usage';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            url varchar(500) NOT NULL,
            country varchar(50) NOT NULL,
            sector varchar(255) DEFAULT NULL,
            data_size_kb decimal(10,3) NOT NULL,
            data_size_mb decimal(10,6) NOT NULL,
            cost_usd decimal(10,6) NOT NULL,
            success boolean DEFAULT true,
            response_time_ms int DEFAULT 0,
            compression_ratio decimal(5,2) DEFAULT 0,
            request_type varchar(50) DEFAULT 'standard',
            zone_id varchar(100) DEFAULT NULL,
            session_id varchar(100) DEFAULT NULL,
            month_year varchar(7) NOT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY url (url(255)),
            KEY country (country),
            KEY sector (sector),
            KEY month_year (month_year),
            KEY success (success),
            KEY cost_tracking (month_year, cost_usd),
            KEY data_volume (month_year, data_size_mb),
            KEY performance_tracking (success, response_time_ms)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get database statistics
     */
    public function get_database_statistics() {
        global $wpdb;
        
        $stats = array();
        
        $tables = array(
            'advanced_scraping_logs',
            'ai_agent_logs',
            'performance_metrics',
            'data_quality_logs',
            'source_reliability',
            'scraped_data_archive',
            'anomaly_detection_logs',
            'cross_validation_results',
            'api_usage_tracking',
            'brightdata_usage'
        );
        
        foreach ($tables as $table) {
            $table_name = $this->table_prefix . $table;
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            $stats[$table] = intval($count);
        }
        
        return $stats;
    }
    
    /**
     * Clean old data based on retention policies
     */
    public function clean_old_data() {
        global $wpdb;
        
        // Clean logs older than 90 days
        $log_tables = array(
            'advanced_scraping_logs',
            'ai_agent_logs'
        );
        
        foreach ($log_tables as $table) {
            $table_name = $this->table_prefix . $table;
            $wpdb->query(
                "DELETE FROM $table_name WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)"
            );
        }

        // Clean archived data older than 1 year
        $archive_table = $this->table_prefix . 'scraped_data_archive';
        $wpdb->query(
            "DELETE FROM $archive_table WHERE scraped_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)"
        );

        // Clean resolved anomalies older than 6 months
        $anomaly_table = $this->table_prefix . 'anomaly_detection_logs';
        $wpdb->query(
            "DELETE FROM $anomaly_table
             WHERE resolution_status = 'resolved'
             AND resolved_at < DATE_SUB(NOW(), INTERVAL 6 MONTH)"
        );
    }
}

/**
 * Initialize advanced scraping database
 */
function chatgabi_init_advanced_scraping_database() {
    $db_manager = new ChatGABI_Advanced_Scraping_Database();
    $db_manager->create_tables();
}

// Hook into theme activation
add_action('after_switch_theme', 'chatgabi_init_advanced_scraping_database');

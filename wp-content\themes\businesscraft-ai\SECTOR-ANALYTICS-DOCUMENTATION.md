# BusinessCraft AI Sector Analytics System

## Overview

The Sector Analytics System provides comprehensive logging and analytics for the BusinessCraft AI sector context injection feature. It tracks how effectively the system is providing localized business intelligence and which sectors are most popular among users.

## 🗄️ Database Schema

### Table: `wp_businesscraft_ai_sector_logs`

```sql
CREATE TABLE wp_businesscraft_ai_sector_logs (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    timestamp datetime DEFAULT CURRENT_TIMESTAMP,
    country varchar(50) NOT NULL,
    detected_sector varchar(255) DEFAULT NULL,
    sector_context_found tinyint(1) DEFAULT 0,
    prompt_tokens_estimated int(11) DEFAULT 0,
    user_message_preview text,
    response_quality_rating int(1) DEFAULT NULL,
    PRIMARY KEY (id),
    KEY user_id (user_id),
    KEY timestamp (timestamp),
    KEY country (country),
    KEY detected_sector (detected_sector)
);
```

### Column Descriptions

- **id**: Auto-increment primary key
- **user_id**: Foreign key to wp_users table
- **timestamp**: When the request was made
- **country**: User's country (Ghana, Kenya, Nigeria, South Africa)
- **detected_sector**: Business sector detected from user query
- **sector_context_found**: Boolean - whether sector context was successfully loaded
- **prompt_tokens_estimated**: Estimated token count for the generated prompt
- **user_message_preview**: First 100 characters of user's message
- **response_quality_rating**: Future feature for user feedback (1-5 rating)

## 📊 Analytics Features

### 1. Summary Statistics
- Total requests processed
- Sector context success rate
- Average tokens per request
- Number of active countries

### 2. Chart Visualizations
- **Sectors by Country**: Bar chart showing most requested sectors per country
- **Success Rate Timeline**: Line chart showing context injection success over time
- **Token Distribution**: Doughnut chart showing token usage patterns
- **Daily Trends**: Line chart showing request volume trends

### 3. Data Table
- Filterable table of recent sector context requests
- Pagination support
- Export to CSV functionality

## 🔧 Implementation Files

### Core Files
- `/inc/admin-sector-analytics.php` - Main analytics dashboard
- `/assets/js/admin-sector-analytics.js` - Frontend JavaScript
- `/assets/css/admin-sector-analytics.css` - Dashboard styles

### Integration Points
- `/inc/openai-integration.php` - Logging integration in prompt building
- `/functions.php` - Database table creation and cron jobs

## 📈 Key Functions

### Logging Functions
```php
// Log sector context injection attempt
businesscraft_ai_log_sector_context($user_id, $country, $detected_sector, $sector_context_found, $prompt_tokens, $user_message);

// Create database table
businesscraft_ai_create_sector_logs_table();

// Cleanup old logs (90+ days)
businesscraft_ai_cleanup_sector_logs();
```

### Analytics Functions
```php
// Get summary statistics
businesscraft_ai_get_sector_analytics_summary();

// Get chart data
businesscraft_ai_get_sectors_by_country_data();
businesscraft_ai_get_success_rate_timeline();
businesscraft_ai_get_token_distribution();
businesscraft_ai_get_daily_trends();

// Get table data with filtering
businesscraft_ai_get_recent_logs();
```

### AJAX Handlers
```php
// Main data endpoint
wp_ajax_get_sector_analytics_data

// CSV export endpoint
wp_ajax_export_sector_analytics
```

## 🎯 Performance Optimizations

### Caching Strategy
- **Transient Caching**: Chart data cached for 10 minutes
- **Summary Stats**: Cached for 5 minutes
- **Cache Keys**: Prefixed with `bcai_` for easy identification

### Database Optimization
- **Indexes**: Added on user_id, timestamp, country, detected_sector
- **Data Retention**: Automatic cleanup of logs older than 90 days
- **Query Limits**: Export limited to 1000 records for performance

### Frontend Optimization
- **Lazy Loading**: Charts load data via AJAX
- **Pagination**: Table data paginated (20 records per page)
- **Responsive Design**: Mobile-friendly dashboard

## 🔐 Security Features

### Access Control
- **Capability Check**: `manage_options` required for dashboard access
- **Nonce Verification**: All AJAX requests verified with nonces
- **Data Sanitization**: All user inputs sanitized

### Data Privacy
- **Message Preview**: Only first 100 characters stored
- **User Data**: Linked to WordPress users but anonymizable
- **Export Control**: Admin-only CSV export with audit trail

## 📱 Admin Dashboard

### Navigation
- **Location**: WordPress Admin → BusinessCraft AI → Sector Analytics
- **Menu Hook**: `businesscraft-ai_page_businesscraft-ai-sector-analytics`

### Dashboard Sections
1. **Summary Cards**: Key metrics at a glance
2. **Charts Section**: 4 interactive Chart.js visualizations
3. **Data Table**: Filterable, paginated log entries
4. **Export Tools**: CSV download with filtering

### Filters Available
- **Country**: Ghana, Kenya, Nigeria, South Africa
- **Sector**: Dynamic list based on detected sectors
- **Success**: Success/Failure of context injection

## 🚀 Usage Examples

### Accessing Analytics Data
```javascript
// Load chart data
$.ajax({
    url: sectorAnalytics.ajaxUrl,
    type: 'POST',
    data: {
        action: 'get_sector_analytics_data',
        data_type: 'sectors_by_country',
        nonce: sectorAnalytics.nonce
    },
    success: function(response) {
        updateChart('sectors_by_country', response.data);
    }
});
```

### Manual Logging
```php
// Log a sector context attempt
businesscraft_ai_log_sector_context(
    get_current_user_id(),
    'Ghana',
    'Fintech',
    true,
    350,
    'I want to start a mobile payment business'
);
```

## 🔄 Automated Processes

### Cron Jobs
- **Daily Cleanup**: Removes logs older than 90 days
- **Hook**: `businesscraft_ai_cleanup_sector_logs`
- **Schedule**: Daily at server's configured time

### Cache Management
- **Auto-Refresh**: Transient caches expire automatically
- **Manual Clear**: Admin can clear caches via WordPress admin
- **Cache Keys**: Easily identifiable with `bcai_` prefix

## 📊 Metrics Tracked

### Success Metrics
- **Context Success Rate**: Percentage of requests with successful sector context loading
- **Token Efficiency**: Average tokens per request (target: <500)
- **Sector Coverage**: Number of different sectors being requested

### Usage Metrics
- **Daily Request Volume**: Trend analysis of system usage
- **Country Distribution**: Which countries are most active
- **Sector Popularity**: Most requested business sectors

### Performance Metrics
- **Response Times**: Indirectly tracked via token counts
- **Error Rates**: Failed context injections
- **User Engagement**: Repeat usage patterns

## 🛠️ Maintenance

### Regular Tasks
1. **Monitor Success Rates**: Aim for >80% sector context success
2. **Review Token Usage**: Optimize prompts if average exceeds 500 tokens
3. **Analyze Sector Trends**: Update datasets based on popular sectors
4. **Performance Monitoring**: Check dashboard load times

### Troubleshooting
- **Missing Data**: Check if logging functions are being called
- **Slow Dashboard**: Verify caching is working properly
- **Export Issues**: Check file permissions and server limits

## 🔮 Future Enhancements

### Planned Features
1. **User Feedback Integration**: Response quality ratings
2. **Advanced Filtering**: Date ranges, user types
3. **Real-time Dashboard**: WebSocket updates
4. **Predictive Analytics**: Sector trend forecasting
5. **API Endpoints**: External access to analytics data

### Scalability Considerations
- **Database Partitioning**: For high-volume installations
- **External Analytics**: Integration with Google Analytics
- **Data Warehousing**: Long-term trend analysis
- **Machine Learning**: Automated sector detection improvement

---

## 📞 Support

For technical support or questions about the Sector Analytics System:
- Check the test script: `/test-logging-system.php`
- Review WordPress error logs
- Verify database table structure
- Test AJAX endpoints manually

**System Status**: ✅ Production Ready
**Last Updated**: December 2024
**Version**: 1.0.0

<?php
/**
 * Database Query Optimization for ChatGABI
 * Implements query caching, optimization, and performance monitoring
 *
 * @package BusinessCraft_AI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_Database_Optimizer {
    
    private $cache;
    private $query_cache_ttl = 1800; // 30 minutes
    private $slow_query_threshold = 1.0; // 1 second
    private $query_stats = array();
    
    public function __construct() {
        $this->init_cache();
        $this->init_hooks();
    }
    
    /**
     * Initialize cache system
     */
    private function init_cache() {
        require_once get_template_directory() . '/inc/redis-caching.php';
        global $chatgabi_cache;
        $this->cache = $chatgabi_cache;
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Hook into WordPress query system
        add_filter('query', array($this, 'optimize_query'), 10, 1);
        add_action('shutdown', array($this, 'log_query_stats'));
    }
    
    /**
     * Get cached query result
     */
    public function get_cached_query($sql, $cache_key = null) {
        if (!$cache_key) {
            $cache_key = 'db_query:' . md5($sql);
        }
        
        $start_time = microtime(true);
        $cached_result = $this->cache->get($cache_key);
        
        if ($cached_result !== false) {
            $this->record_query_stat('cache_hit', microtime(true) - $start_time, $sql);
            return $cached_result;
        }
        
        return false;
    }
    
    /**
     * Cache query result
     */
    public function cache_query_result($sql, $result, $ttl = null, $cache_key = null) {
        if (!$cache_key) {
            $cache_key = 'db_query:' . md5($sql);
        }
        
        if ($ttl === null) {
            $ttl = $this->query_cache_ttl;
        }
        
        return $this->cache->set($cache_key, $result, $ttl);
    }
    
    /**
     * Execute optimized query with caching
     */
    public function execute_cached_query($sql, $cache_ttl = null, $cache_key = null) {
        global $wpdb;
        
        // Try cache first
        $cached_result = $this->get_cached_query($sql, $cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        // Execute query with timing
        $start_time = microtime(true);
        $result = $wpdb->get_results($sql);
        $execution_time = microtime(true) - $start_time;
        
        // Record stats
        $this->record_query_stat('cache_miss', $execution_time, $sql);
        
        // Cache result if successful
        if ($result !== false && !$wpdb->last_error) {
            $this->cache_query_result($sql, $result, $cache_ttl, $cache_key);
        }
        
        return $result;
    }
    
    /**
     * Get optimized conversations for user
     */
    public function get_user_conversations($user_id, $limit = 20, $offset = 0) {
        global $wpdb;

        $cache_key = "user_conversations:{$user_id}:{$limit}:{$offset}";

        // Check if conversations table exists
        $conversations_table = $wpdb->prefix . 'chatgabi_conversations';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$conversations_table'");

        if (!$table_exists) {
            // Try alternative table
            $alt_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
            if ($wpdb->get_var("SHOW TABLES LIKE '$alt_table'")) {
                // Use alternative table structure
                $sql = $wpdb->prepare(
                    "SELECT id, 'general' as conversation_type, created_at, created_at as updated_at,
                            1 as message_count, created_at as last_message_time
                     FROM $alt_table
                     WHERE user_id = %d
                     ORDER BY created_at DESC
                     LIMIT %d OFFSET %d",
                    $user_id, $limit, $offset
                );
            } else {
                return array(); // No conversations table found
            }
        } else {
            // Check if messages table exists
            $messages_table = $wpdb->prefix . 'chatgabi_messages';
            $messages_exists = $wpdb->get_var("SHOW TABLES LIKE '$messages_table'");

            if ($messages_exists) {
                // Full query with messages
                $sql = $wpdb->prepare(
                    "SELECT c.id, c.conversation_type, c.created_at, c.created_at as updated_at,
                            COUNT(m.id) as message_count,
                            MAX(m.created_at) as last_message_time
                     FROM $conversations_table c
                     LEFT JOIN $messages_table m ON c.id = m.conversation_id
                     WHERE c.user_id = %d
                     GROUP BY c.id
                     ORDER BY c.created_at DESC
                     LIMIT %d OFFSET %d",
                    $user_id, $limit, $offset
                );
            } else {
                // Simple query without messages
                $sql = $wpdb->prepare(
                    "SELECT id, conversation_type, created_at, created_at as updated_at,
                            1 as message_count, created_at as last_message_time
                     FROM $conversations_table
                     WHERE user_id = %d
                     ORDER BY created_at DESC
                     LIMIT %d OFFSET %d",
                    $user_id, $limit, $offset
                );
            }
        }

        return $this->execute_cached_query($sql, 900, $cache_key); // Cache for 15 minutes
    }
    
    /**
     * Get optimized prompt templates
     */
    public function get_prompt_templates($category = null, $language = 'en', $limit = 50) {
        global $wpdb;
        
        $cache_key = "prompt_templates:{$category}:{$language}:{$limit}";
        
        // Check which columns exist in the templates table first
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        $columns = $wpdb->get_results("DESCRIBE $templates_table");
        $column_names = array_column($columns, 'Field');

        // Determine correct column names based on actual schema
        $prompt_column = in_array('prompt_content', $column_names) ? 'prompt_content' : 'prompt_text';
        $language_column = in_array('language_code', $column_names) ? 'language_code' : (in_array('language', $column_names) ? 'language' : "'en'");

        $where_conditions = array();
        $params = array();

        // Default filters - specify table alias to avoid ambiguity
        if (in_array('status', $column_names)) {
            $where_conditions[] = "t.status = 'active'";
        }
        $where_conditions[] = "t.is_public = 1";

        if ($category) {
            $where_conditions[] = "t.category_id = %d";
            $params[] = $category;
        }

        if ($language !== 'en') {
            $where_conditions[] = "($language_column = %s OR $language_column = 'en')";
            $params[] = $language;
        }
        
        $params[] = $limit;

        $sql = $wpdb->prepare(
            "SELECT t.id, t.title, t.description, t.$prompt_column as prompt_content, t.category_id,
                    t.usage_count, t.rating_average, $language_column as language_code,
                    c.name as category_name
             FROM {$wpdb->prefix}chatgabi_prompt_templates t
             LEFT JOIN {$wpdb->prefix}chatgabi_template_categories c ON t.category_id = c.id
             WHERE " . implode(' AND ', $where_conditions) . "
             ORDER BY t.is_featured DESC, t.rating_average DESC, t.usage_count DESC
             LIMIT %d",
            ...$params
        );
        
        return $this->execute_cached_query($sql, 3600, $cache_key); // Cache for 1 hour
    }
    
    /**
     * Get optimized feedback analytics
     */
    public function get_feedback_analytics($date_range = 30, $country = null) {
        global $wpdb;
        
        $cache_key = "feedback_analytics:{$date_range}:" . ($country ?: 'all');
        
        $where_conditions = array("created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)");
        $params = array($date_range);
        
        if ($country) {
            $where_conditions[] = "user_country = %s";
            $params[] = $country;
        }
        
        // Check which columns exist in the feedback table
        $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
        $feedback_columns = $wpdb->get_results("DESCRIBE $feedback_table");
        $feedback_column_names = array_column($feedback_columns, 'Field');

        // Determine correct column names based on actual schema
        $helpfulness_col = in_array('helpfulness_score', $feedback_column_names) ? 'helpfulness_score' : 'category_helpfulness';
        $accuracy_col = in_array('accuracy_score', $feedback_column_names) ? 'accuracy_score' : 'category_accuracy';
        $relevance_col = in_array('relevance_score', $feedback_column_names) ? 'relevance_score' : 'category_relevance';
        $clarity_col = in_array('clarity_score', $feedback_column_names) ? 'clarity_score' : 'category_clarity';

        $sql = $wpdb->prepare(
            "SELECT
                AVG(rating_score) as avg_rating,
                COUNT(*) as total_feedback,
                SUM(CASE WHEN rating_score >= 4 THEN 1 ELSE 0 END) as positive_feedback,
                SUM(CASE WHEN rating_score <= 2 THEN 1 ELSE 0 END) as negative_feedback,
                AVG($helpfulness_col) as avg_helpfulness,
                AVG($accuracy_col) as avg_accuracy,
                AVG($relevance_col) as avg_relevance,
                AVG($clarity_col) as avg_clarity,
                user_country,
                COUNT(DISTINCT user_id) as unique_users
             FROM {$wpdb->prefix}chatgabi_feedback
             WHERE " . implode(' AND ', $where_conditions) . "
             GROUP BY user_country
             ORDER BY total_feedback DESC",
            ...$params
        );
        
        return $this->execute_cached_query($sql, 1800, $cache_key); // Cache for 30 minutes
    }
    
    /**
     * Get optimized credit usage statistics
     */
    public function get_credit_usage_stats($user_id = null, $days = 30) {
        global $wpdb;

        $cache_key = "credit_stats:" . ($user_id ?: 'all') . ":{$days}";

        // Check which credit table exists
        $credit_table = $wpdb->prefix . 'chatgabi_credit_usage_logs';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$credit_table'");

        if (!$table_exists) {
            // Try alternative table names
            $alternative_tables = array(
                $wpdb->prefix . 'businesscraft_ai_credit_logs',
                $wpdb->prefix . 'chatgabi_credit_transactions'
            );

            foreach ($alternative_tables as $alt_table) {
                if ($wpdb->get_var("SHOW TABLES LIKE '$alt_table'")) {
                    $credit_table = $alt_table;
                    break;
                }
            }
        }

        // Check if table still doesn't exist
        if (!$wpdb->get_var("SHOW TABLES LIKE '$credit_table'")) {
            return array(); // Return empty array if no credit table found
        }

        // Check column names in the credit table
        $credit_columns = $wpdb->get_results("DESCRIBE $credit_table");
        $credit_column_names = array_column($credit_columns, 'Field');

        // Determine correct column names
        $timestamp_col = in_array('timestamp', $credit_column_names) ? 'timestamp' : 'created_at';
        $credits_col = in_array('credits_used', $credit_column_names) ? 'credits_used' : 'credits_amount';
        $tokens_col = in_array('tokens_used', $credit_column_names) ? 'tokens_used' : '0';
        $operation_col = in_array('operation_type', $credit_column_names) ? 'operation_type' : "'general'";

        $where_conditions = array("$timestamp_col >= DATE_SUB(NOW(), INTERVAL %d DAY)");
        $params = array($days);

        if ($user_id) {
            $where_conditions[] = "user_id = %d";
            $params[] = $user_id;
        }

        $sql = $wpdb->prepare(
            "SELECT
                DATE($timestamp_col) as date,
                SUM($credits_col) as daily_credits,
                SUM($tokens_col) as daily_tokens,
                COUNT(*) as daily_requests,
                $operation_col as operation_type,
                COUNT(DISTINCT user_id) as unique_users
             FROM $credit_table
             WHERE " . implode(' AND ', $where_conditions) . "
             GROUP BY DATE($timestamp_col), $operation_col
             ORDER BY date DESC, daily_credits DESC",
            ...$params
        );

        return $this->execute_cached_query($sql, 1800, $cache_key); // Cache for 30 minutes
    }
    
    /**
     * Optimize query hook
     */
    public function optimize_query($query) {
        global $wpdb;

        // Add query hints for specific patterns, but check if indexes exist first
        if (strpos($query, 'chatgabi_conversations') !== false) {
            // Check if table and index exist before using it
            $table_name = $wpdb->prefix . 'chatgabi_conversations';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

            if ($table_exists) {
                $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name WHERE Key_name = 'idx_user_date'");

                if (!empty($indexes)) {
                    // Only add USE INDEX if the table alias is not used
                    if (strpos($query, $table_name . ' c') === false) {
                        $query = str_replace(
                            'FROM ' . $table_name,
                            'FROM ' . $table_name . ' USE INDEX (idx_user_date)',
                            $query
                        );
                    }
                }
            }
        }

        if (strpos($query, 'chatgabi_feedback') !== false) {
            // Check if table and index exist before using it
            $table_name = $wpdb->prefix . 'chatgabi_feedback';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

            if ($table_exists) {
                $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name WHERE Key_name = 'idx_rating_country'");

                if (!empty($indexes)) {
                    $query = str_replace(
                        'FROM ' . $table_name,
                        'FROM ' . $table_name . ' USE INDEX (idx_rating_country)',
                        $query
                    );
                }
            }
        }

        return $query;
    }
    
    /**
     * Record query statistics
     */
    private function record_query_stat($type, $execution_time, $sql) {
        $this->query_stats[] = array(
            'type' => $type,
            'execution_time' => $execution_time,
            'sql' => substr($sql, 0, 200), // Truncate for logging
            'timestamp' => microtime(true)
        );
        
        // Log slow queries
        if ($execution_time > $this->slow_query_threshold) {
            error_log("ChatGABI Slow Query ({$execution_time}s): " . substr($sql, 0, 500));
        }
    }
    
    /**
     * Log query statistics on shutdown
     */
    public function log_query_stats() {
        if (empty($this->query_stats)) {
            return;
        }
        
        $total_queries = count($this->query_stats);
        $cache_hits = count(array_filter($this->query_stats, function($stat) {
            return $stat['type'] === 'cache_hit';
        }));
        $cache_misses = $total_queries - $cache_hits;
        $cache_hit_rate = $total_queries > 0 ? ($cache_hits / $total_queries) * 100 : 0;
        
        $total_time = array_sum(array_column($this->query_stats, 'execution_time'));
        $avg_time = $total_queries > 0 ? $total_time / $total_queries : 0;
        
        // Log performance stats
        error_log(sprintf(
            'ChatGABI DB Stats: %d queries, %.1f%% cache hit rate, %.3fs avg time',
            $total_queries,
            $cache_hit_rate,
            $avg_time
        ));
        
        // Store stats in cache for dashboard
        $stats = array(
            'total_queries' => $total_queries,
            'cache_hits' => $cache_hits,
            'cache_misses' => $cache_misses,
            'cache_hit_rate' => $cache_hit_rate,
            'total_time' => $total_time,
            'avg_time' => $avg_time,
            'timestamp' => current_time('mysql')
        );
        
        $this->cache->set('db_performance_stats', $stats, 300); // Cache for 5 minutes
    }
    
    /**
     * Get performance statistics
     */
    public function get_performance_stats() {
        return $this->cache->get('db_performance_stats') ?: array(
            'total_queries' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0,
            'cache_hit_rate' => 0,
            'total_time' => 0,
            'avg_time' => 0,
            'timestamp' => current_time('mysql')
        );
    }
    
    /**
     * Clear query cache
     */
    public function clear_query_cache($pattern = null) {
        if ($pattern) {
            // Clear specific pattern (would need Redis SCAN for full implementation)
            return true;
        } else {
            // Clear all query cache
            return $this->cache->flush();
        }
    }
    
    /**
     * Warm up cache with common queries
     */
    public function warm_up_cache() {
        // Pre-load common queries
        $this->get_prompt_templates(null, 'en', 50);
        $this->get_feedback_analytics(30);
        $this->get_credit_usage_stats(null, 7);
        
        error_log('ChatGABI: Database cache warmed up');
    }
}

// Initialize database optimizer
global $chatgabi_db_optimizer;
$chatgabi_db_optimizer = new BusinessCraft_Database_Optimizer();

/**
 * Helper functions
 */
if (!function_exists('chatgabi_get_cached_query')) {
    function chatgabi_get_cached_query($sql, $cache_ttl = null, $cache_key = null) {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->execute_cached_query($sql, $cache_ttl, $cache_key);
    }
}

if (!function_exists('chatgabi_get_user_conversations')) {
    function chatgabi_get_user_conversations($user_id, $limit = 20, $offset = 0) {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->get_user_conversations($user_id, $limit, $offset);
    }
}

if (!function_exists('chatgabi_get_prompt_templates')) {
    function chatgabi_get_prompt_templates($category = null, $language = 'en', $limit = 50) {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->get_prompt_templates($category, $language, $limit);
    }
}

if (!function_exists('chatgabi_get_feedback_analytics')) {
    function chatgabi_get_feedback_analytics($date_range = 30, $country = null) {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->get_feedback_analytics($date_range, $country);
    }
}

if (!function_exists('chatgabi_get_credit_usage_stats')) {
    function chatgabi_get_credit_usage_stats($user_id = null, $days = 30) {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->get_credit_usage_stats($user_id, $days);
    }
}

if (!function_exists('chatgabi_clear_query_cache')) {
    function chatgabi_clear_query_cache($pattern = null) {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->clear_query_cache($pattern);
    }
}

if (!function_exists('chatgabi_warm_up_cache')) {
    function chatgabi_warm_up_cache() {
        global $chatgabi_db_optimizer;
        return $chatgabi_db_optimizer->warm_up_cache();
    }
}

// Warm up cache on theme activation (disabled to prevent memory issues during testing)
// add_action('after_setup_theme', 'chatgabi_warm_up_cache');

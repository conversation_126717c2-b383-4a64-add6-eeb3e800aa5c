<?php
/**
 * Final Templates Functionality Test
 * 
 * Complete test of all template functionality
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Templates Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f8f9fa; }
    </style>
</head>
<body>

<h1>🏁 Final Templates Functionality Test</h1>

<?php
echo '<div class="info">Final test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$all_tests_passed = true;

// Test 1: Database Status
echo '<h2>💾 Database Status</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

$template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table}");
$public_template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE is_public = 1");
$category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");

echo '<div class="test-result">';
echo '<strong>Database Summary:</strong><br>';
echo '✅ Total templates: ' . $template_count . '<br>';
echo '✅ Public templates: ' . $public_template_count . '<br>';
echo '✅ Categories: ' . $category_count . '<br>';
echo '</div>';

if ($public_template_count >= 3 && $category_count >= 5) {
    echo '<div class="success">✅ Database has sufficient data for testing</div>';
} else {
    echo '<div class="error">❌ Database needs more data</div>';
    $all_tests_passed = false;
}

// Test 2: Functions Availability
echo '<h2>⚙️ Functions Availability</h2>';

$required_functions = array(
    'chatgabi_get_template_categories' => 'Template categories function',
    'chatgabi_get_templates' => 'Templates REST handler',
    'chatgabi_get_template_categories_api' => 'Categories REST handler'
);

$functions_ok = true;
foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo '<div class="success">✅ ' . $function . '</div>';
    } else {
        echo '<div class="error">❌ ' . $function . ' missing</div>';
        $functions_ok = false;
        $all_tests_passed = false;
    }
}

// Test 3: REST API Test
echo '<h2>🌐 REST API Test</h2>';

$rest_tests = array();

// Test templates endpoint
$templates_url = rest_url('chatgabi/v1/templates');
$response = wp_remote_get($templates_url, array(
    'timeout' => 10,
    'headers' => array('X-WP-Nonce' => wp_create_nonce('wp_rest'))
));

if (!is_wp_error($response)) {
    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if ($status_code === 200 && isset($data['success']) && $data['success']) {
        echo '<div class="success">✅ Templates API: Working (' . count($data['templates']) . ' templates)</div>';
        $rest_tests['templates'] = true;
    } else {
        echo '<div class="error">❌ Templates API: Failed (Status: ' . $status_code . ')</div>';
        $rest_tests['templates'] = false;
        $all_tests_passed = false;
    }
} else {
    echo '<div class="error">❌ Templates API: Error - ' . $response->get_error_message() . '</div>';
    $rest_tests['templates'] = false;
    $all_tests_passed = false;
}

// Test categories endpoint
$categories_url = rest_url('chatgabi/v1/template-categories');
$response = wp_remote_get($categories_url, array('timeout' => 10));

if (!is_wp_error($response)) {
    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if ($status_code === 200 && isset($data['categories'])) {
        echo '<div class="success">✅ Categories API: Working (' . count($data['categories']) . ' categories)</div>';
        $rest_tests['categories'] = true;
    } else {
        echo '<div class="error">❌ Categories API: Failed (Status: ' . $status_code . ')</div>';
        $rest_tests['categories'] = false;
        $all_tests_passed = false;
    }
} else {
    echo '<div class="error">❌ Categories API: Error - ' . $response->get_error_message() . '</div>';
    $rest_tests['categories'] = false;
    $all_tests_passed = false;
}

// Test 4: Page Template Status
echo '<h2>📄 Page Template Status</h2>';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    $page_url = get_permalink($templates_page->ID);
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    
    echo '<div class="success">✅ Templates page exists</div>';
    echo '<div class="info">📍 URL: <a href="' . $page_url . '" target="_blank">' . $page_url . '</a></div>';
    echo '<div class="info">🎨 Template: ' . ($page_template ?: 'default') . '</div>';
    
    // Check template file
    $template_file = get_template_directory() . '/page-templates.php';
    if (file_exists($template_file)) {
        echo '<div class="success">✅ Template file exists</div>';
        
        // Check file content
        $template_content = file_get_contents($template_file);
        $required_elements = array(
            'templates-grid' => 'Templates grid',
            'category-filter' => 'Category filter',
            'template-search' => 'Search input',
            'templates-fallback' => 'Fallback content',
            'chatgabiTemplatesConfig' => 'JavaScript config'
        );
        
        $elements_found = 0;
        foreach ($required_elements as $element => $description) {
            if (strpos($template_content, $element) !== false) {
                echo '<div class="success">✅ Has: ' . $description . '</div>';
                $elements_found++;
            } else {
                echo '<div class="warning">⚠️ Missing: ' . $description . '</div>';
            }
        }
        
        if ($elements_found >= 4) {
            echo '<div class="success">✅ Template file has required elements</div>';
        } else {
            echo '<div class="warning">⚠️ Template file missing some elements</div>';
        }
        
    } else {
        echo '<div class="error">❌ Template file missing</div>';
        $all_tests_passed = false;
    }
} else {
    echo '<div class="error">❌ Templates page not found</div>';
    $all_tests_passed = false;
}

// Test 5: Asset Files
echo '<h2>📁 Asset Files</h2>';

$asset_files = array(
    'assets/js/templates-interface.js' => 'JavaScript file',
    'assets/css/templates.css' => 'CSS file'
);

foreach ($asset_files as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        $file_size = filesize($file_path);
        echo '<div class="success">✅ ' . $description . ' exists (' . number_format($file_size) . ' bytes)</div>';
        
        if ($file_size < 1000) {
            echo '<div class="warning">⚠️ File seems small, may be incomplete</div>';
        }
    } else {
        echo '<div class="error">❌ ' . $description . ' missing</div>';
        $all_tests_passed = false;
    }
}

// Final Summary
echo '<h2>🏆 Final Summary</h2>';

if ($all_tests_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL TESTS PASSED!</h3>';
    echo '<p><strong>✅ The ChatGABI Templates system is fully functional!</strong></p>';
    echo '<ul>';
    echo '<li>✅ Database contains templates and categories</li>';
    echo '<li>✅ REST API endpoints are working</li>';
    echo '<li>✅ Template page is properly configured</li>';
    echo '<li>✅ Asset files are present</li>';
    echo '<li>✅ All required functions exist</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues Found</h3>';
    echo '<p>The system is mostly functional but some issues remain. Check the details above.</p>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Test Actions</h2>';

echo '<div style="margin: 20px 0;">';

if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 Open Templates Page</a>';
}

echo '<a href="debug-templates-page.php" target="_blank" class="test-button">🐛 Debug Page</a>';
echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="test-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/template-categories') . '" target="_blank" class="test-button">📂 Test Categories API</a>';
echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Re-run Test</a>';
echo '</div>';

// Sample Data Display
if ($public_template_count > 0) {
    echo '<h2>📋 Sample Templates</h2>';
    
    $sample_templates = $wpdb->get_results("
        SELECT t.id, t.title, t.description, c.name as category_name 
        FROM {$templates_table} t 
        LEFT JOIN {$categories_table} c ON t.category_id = c.id 
        WHERE t.is_public = 1 
        LIMIT 5
    ");
    
    echo '<div class="test-result">';
    foreach ($sample_templates as $template) {
        echo '<strong>' . esc_html($template->title) . '</strong><br>';
        echo '<em>' . esc_html($template->category_name ?: 'General') . '</em><br>';
        echo esc_html(wp_trim_words($template->description, 15)) . '<br><br>';
    }
    echo '</div>';
}

echo '<hr>';
echo '<div class="info">Final test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';

// Instructions for user
echo '<div class="info">';
echo '<h3>📖 Next Steps</h3>';
echo '<p>If all tests passed, your ChatGABI Templates system is ready to use!</p>';
echo '<p><strong>To use the templates:</strong></p>';
echo '<ol>';
echo '<li>Visit the <a href="' . ($templates_page ? get_permalink($templates_page->ID) : '#') . '" target="_blank">Templates Page</a></li>';
echo '<li>Browse templates by category or search</li>';
echo '<li>Click on templates to preview and use them</li>';
echo '<li>Templates will integrate with your chat interface</li>';
echo '</ol>';
echo '</div>';
?>

</body>
</html>

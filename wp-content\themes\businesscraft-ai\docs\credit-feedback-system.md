# ChatGABI Real-Time Token/Credit Feedback System

## Overview

The Real-Time Token/Credit Feedback System provides users with transparent, real-time information about their credit usage, token consumption, and account balance. This system enhances user trust and helps them make informed decisions about their AI interactions.

## Features

### 🔢 Real-Time Token Estimation
- **Live Token Counting**: Estimates tokens as users type their prompts
- **Context-Aware Estimation**: Considers country, sector, and language context
- **Credit Cost Preview**: Shows estimated credit cost before submission
- **Accuracy Tracking**: Compares estimated vs actual token usage

### 💰 Credit Balance Management
- **Live Balance Display**: Shows current credit balance with refresh capability
- **Real-Time Updates**: Updates balance immediately after each interaction
- **Low Credit Warnings**: Alerts users when credits are running low
- **Purchase Integration**: Direct links to credit purchase system

### 📊 Usage Analytics
- **Token Usage Statistics**: Tracks tokens used over time periods
- **Credit Spending Analysis**: Shows credit usage patterns
- **Conversation Metrics**: Displays conversation count and averages
- **Visual Charts**: Interactive charts showing usage trends

### ⚡ Performance Monitoring
- **Processing Time Tracking**: Monitors AI response times
- **Efficiency Metrics**: Tracks token-to-credit conversion rates
- **Usage Optimization**: Helps users optimize their prompt efficiency

## Technical Implementation

### Database Schema

#### `wp_chatgabi_token_usage` Table
```sql
CREATE TABLE wp_chatgabi_token_usage (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    session_id varchar(100) NOT NULL,
    conversation_id varchar(100),
    prompt_text longtext,
    response_text longtext,
    estimated_tokens int(11) NOT NULL DEFAULT 0,
    actual_tokens int(11) NOT NULL DEFAULT 0,
    credits_used decimal(10,2) NOT NULL DEFAULT 0.00,
    credits_before decimal(10,2) NOT NULL DEFAULT 0.00,
    credits_after decimal(10,2) NOT NULL DEFAULT 0.00,
    operation_type varchar(50) NOT NULL DEFAULT 'chat',
    language_code varchar(5) NOT NULL DEFAULT 'en',
    country_code varchar(5),
    sector varchar(100),
    processing_time decimal(8,3) NOT NULL DEFAULT 0.000,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY user_id (user_id),
    KEY session_id (session_id),
    KEY conversation_id (conversation_id),
    KEY created_at (created_at),
    KEY operation_type (operation_type)
);
```

### Core Functions

#### Token Estimation
```php
// Estimate tokens for a given text
chatgabi_estimate_tokens($text)

// Get comprehensive token estimate with context
chatgabi_get_prompt_token_estimate($prompt, $context_data)

// Calculate credit cost from tokens
chatgabi_calculate_credit_cost($tokens)
```

#### Credit Management
```php
// Enhanced credit deduction with tracking
chatgabi_deduct_credits_with_tracking($user_id, $credits, $token_data)

// Track token usage for analytics
chatgabi_track_token_usage($user_id, $session_id, $data)

// Get user token statistics
chatgabi_get_user_token_stats($user_id, $days)
```

### AJAX Endpoints

#### Token Estimation
- **Endpoint**: `chatgabi_estimate_tokens`
- **Purpose**: Real-time token estimation for prompts
- **Parameters**: `prompt`, `language`, `country`, `sector`
- **Response**: Token count, credit cost, balance check

#### Usage Statistics
- **Endpoint**: `chatgabi_get_token_stats`
- **Purpose**: Retrieve user usage analytics
- **Parameters**: `days` (period for statistics)
- **Response**: Usage summary and daily breakdown

#### Credit Balance
- **Endpoint**: `chatgabi_get_user_credits`
- **Purpose**: Get current user credit balance
- **Response**: Current credits and formatted display

### Frontend Components

#### Credit Feedback Widget
- **File**: `template-parts/credit-feedback-widget.php`
- **Features**: Balance display, token estimation, usage stats
- **Integration**: Embedded in dashboard and chat interfaces

#### JavaScript Integration
- **File**: `assets/js/credit-feedback.js`
- **Features**: Real-time updates, AJAX handling, event management
- **Events**: `chatgabi:promptChanged`, `chatgabi:responseCompleted`

#### CSS Styling
- **File**: `assets/css/credit-feedback.css`
- **Features**: Responsive design, animations, status indicators
- **Themes**: Light theme with gradient accents

## Usage Examples

### Basic Integration
```php
// Include credit feedback widget in any template
<?php get_template_part('template-parts/credit-feedback-widget'); ?>
```

### JavaScript Events
```javascript
// Listen for prompt changes
$(document).on('chatgabi:promptChanged', function(event, prompt, context) {
    // Trigger real-time estimation
});

// Handle response completion
$(document).on('chatgabi:responseCompleted', function(event, data) {
    // Update balance and statistics
});
```

### Custom Token Estimation
```php
// Get token estimate for custom content
$estimation = chatgabi_get_prompt_token_estimate($prompt, array(
    'country_context' => $country_data,
    'sector_context' => $sector_data
));

echo "Estimated tokens: " . $estimation['total_estimated_tokens'];
echo "Estimated credits: " . $estimation['estimated_credits'];
```

## Configuration

### Token-to-Credit Ratio
```php
// Adjust tokens per credit (default: 1000)
add_filter('chatgabi_tokens_per_credit', function($tokens) {
    return 1500; // 1 credit = 1500 tokens
});
```

### Low Credit Threshold
```php
// Set custom low credit warning threshold
add_filter('chatgabi_low_credit_threshold', function($threshold) {
    return 10; // Warn when credits < 10
});
```

## Performance Considerations

### Caching Strategy
- Token estimations are cached for identical prompts
- User statistics cached for 10 minutes
- Database queries optimized with proper indexing

### Rate Limiting
- Token estimation requests debounced (800ms)
- Maximum 1 estimation per second per user
- Background processing for heavy analytics

### Database Optimization
- Automatic cleanup of old token usage records (90+ days)
- Indexed queries for fast retrieval
- Efficient aggregation for statistics

## Security Features

### Data Protection
- All AJAX requests protected with nonces
- User data isolated by user_id
- Sensitive prompt data truncated for storage

### Access Control
- Credit operations require user authentication
- Admin-only access to global statistics
- Rate limiting prevents abuse

## Monitoring & Analytics

### Admin Dashboard Integration
- Global token usage statistics
- User engagement metrics
- Credit consumption patterns
- Performance monitoring

### Error Handling
- Graceful degradation when APIs fail
- Fallback mechanisms for estimation
- Comprehensive error logging

## Future Enhancements

### Planned Features
- WebSocket integration for real-time updates
- Advanced usage predictions
- Credit usage optimization suggestions
- Integration with mobile apps via REST API

### Scalability Improvements
- Redis caching for high-traffic sites
- Background job processing
- Database sharding for large user bases

## Support & Troubleshooting

### Common Issues
1. **Token estimation not working**: Check AJAX endpoints and nonces
2. **Balance not updating**: Verify credit deduction integration
3. **Statistics not loading**: Check database table creation

### Debug Mode
Enable debug logging by adding to wp-config.php:
```php
define('CHATGABI_DEBUG_TOKENS', true);
```

### Performance Monitoring
Monitor slow operations in the performance log:
```
/wp-content/chatgabi-performance.log
```

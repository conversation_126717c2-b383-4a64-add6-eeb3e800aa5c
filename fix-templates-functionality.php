<?php
/**
 * Fix ChatGABI Templates Functionality Issues
 * 
 * Comprehensive fix for:
 * 1. Navigation Issues
 * 2. Category Filter Problems
 * 3. REST API Connectivity
 * 4. JavaScript Functionality
 * 5. Database Data Availability
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates - Functionality Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .fix-button:hover { background: #005a87; }
    </style>
</head>
<body>

<h1>🔧 ChatGABI Templates - Functionality Fix</h1>

<?php
echo '<div class="info">Fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$fixes_applied = array();
$errors_encountered = array();

// Fix 1: Ensure Database Tables and Data
echo '<h2>💾 Fix 1: Database Tables and Data</h2>';

try {
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    
    // Check and create tables if needed
    if ($wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") !== $templates_table) {
        echo '<div class="info">🔧 Creating templates table...</div>';
        
        $sql = "CREATE TABLE {$templates_table} (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL DEFAULT 0,
            title varchar(255) NOT NULL,
            description text,
            prompt_content longtext NOT NULL,
            category_id int(11) DEFAULT NULL,
            language_code varchar(10) DEFAULT 'en',
            tags text,
            sector varchar(100),
            country varchar(10),
            placeholders text,
            is_public tinyint(1) DEFAULT 0,
            is_featured tinyint(1) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            usage_count int(11) DEFAULT 0,
            rating_average decimal(3,2) DEFAULT 0.00,
            rating_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY category_id (category_id),
            KEY is_public (is_public),
            KEY status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        echo '<div class="success">✅ Templates table created</div>';
        $fixes_applied[] = 'Created templates table';
    }
    
    if ($wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") !== $categories_table) {
        echo '<div class="info">🔧 Creating categories table...</div>';
        
        $sql = "CREATE TABLE {$categories_table} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            slug varchar(100) NOT NULL,
            description text,
            icon varchar(50) DEFAULT '📋',
            color varchar(7) DEFAULT '#667eea',
            sort_order int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        echo '<div class="success">✅ Categories table created</div>';
        $fixes_applied[] = 'Created categories table';
    }
    
    // Check data availability and create if needed
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
    if ($category_count == 0) {
        echo '<div class="info">🔧 Creating default categories...</div>';
        
        $default_categories = array(
            array('Business Planning', 'business-planning', 'Comprehensive business plans and strategies', '📊', '#667eea'),
            array('Marketing & Sales', 'marketing-sales', 'Marketing strategies and sales templates', '📈', '#28a745'),
            array('Financial Analysis', 'financial-analysis', 'Financial forecasts and analysis templates', '💰', '#ffc107'),
            array('Market Research', 'market-research', 'Market analysis and research templates', '🔍', '#17a2b8'),
            array('Operations', 'operations', 'Operational planning and management', '⚙️', '#6f42c1'),
            array('Legal & Compliance', 'legal-compliance', 'Legal documents and compliance templates', '⚖️', '#dc3545'),
            array('General Business', 'general-business', 'General business document templates', '📋', '#6c757d'),
            array('Custom Templates', 'custom-templates', 'User-created custom templates', '✨', '#fd7e14')
        );
        
        foreach ($default_categories as $cat) {
            $wpdb->insert($categories_table, array(
                'name' => $cat[0],
                'slug' => $cat[1],
                'description' => $cat[2],
                'icon' => $cat[3],
                'color' => $cat[4],
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ));
        }
        
        echo '<div class="success">✅ Created ' . count($default_categories) . ' default categories</div>';
        $fixes_applied[] = 'Created default categories';
    }
    
    $template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table}");
    if ($template_count == 0) {
        echo '<div class="info">🔧 Creating sample templates...</div>';
        
        // Get category IDs
        $categories = $wpdb->get_results("SELECT id, slug FROM {$categories_table}", OBJECT_K);
        
        $sample_templates = array(
            array(
                'title' => 'Business Plan for African Startups',
                'description' => 'Comprehensive business plan template tailored for African entrepreneurs',
                'prompt_content' => 'Create a detailed business plan for {business_name} in {country}. Include executive summary, market analysis, financial projections, and growth strategy specific to African markets.',
                'category' => 'business-planning',
                'tags' => 'business plan,startup,africa,strategy',
                'sector' => 'General'
            ),
            array(
                'title' => 'Digital Marketing Strategy for SMEs',
                'description' => 'Digital marketing strategy template for small and medium enterprises',
                'prompt_content' => 'Develop a comprehensive digital marketing strategy for {business_name} targeting {target_audience} in {country}. Include social media, content marketing, and local SEO strategies.',
                'category' => 'marketing-sales',
                'tags' => 'marketing,digital,sme,strategy',
                'sector' => 'Technology'
            ),
            array(
                'title' => 'Financial Forecast Template',
                'description' => 'Financial forecasting template for African businesses',
                'prompt_content' => 'Create a 3-year financial forecast for {business_name} in {country}. Include revenue projections, expense analysis, cash flow, and break-even analysis considering local economic factors.',
                'category' => 'financial-analysis',
                'tags' => 'finance,forecast,planning,analysis',
                'sector' => 'Finance'
            ),
            array(
                'title' => 'Market Research Framework',
                'description' => 'Market research template for African markets',
                'prompt_content' => 'Conduct comprehensive market research for {product_service} in {country}. Analyze target demographics, competition, pricing strategies, and market opportunities specific to African consumers.',
                'category' => 'market-research',
                'tags' => 'research,market,analysis,africa',
                'sector' => 'Research'
            )
        );
        
        foreach ($sample_templates as $template) {
            $category_id = null;
            foreach ($categories as $cat) {
                if ($cat->slug === $template['category']) {
                    $category_id = $cat->id;
                    break;
                }
            }
            
            if ($category_id) {
                $wpdb->insert($templates_table, array(
                    'user_id' => 0, // System template
                    'title' => $template['title'],
                    'description' => $template['description'],
                    'prompt_content' => $template['prompt_content'],
                    'category_id' => $category_id,
                    'language_code' => 'en',
                    'tags' => $template['tags'],
                    'sector' => $template['sector'],
                    'is_public' => 1,
                    'is_featured' => 1,
                    'status' => 'active',
                    'usage_count' => rand(5, 50),
                    'rating_average' => rand(35, 50) / 10,
                    'rating_count' => rand(3, 15),
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ));
            }
        }
        
        echo '<div class="success">✅ Created ' . count($sample_templates) . ' sample templates</div>';
        $fixes_applied[] = 'Created sample templates';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Database error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Database error: ' . $e->getMessage();
}

// Fix 2: Ensure REST API Functions Exist
echo '<h2>🌐 Fix 2: REST API Functions</h2>';

try {
    // Check if REST API functions exist, create if missing
    if (!function_exists('chatgabi_get_templates')) {
        echo '<div class="info">🔧 Creating REST API functions...</div>';
        
        // Include the REST API file
        $rest_api_file = get_template_directory() . '/inc/rest-api.php';
        if (file_exists($rest_api_file)) {
            require_once($rest_api_file);
            echo '<div class="success">✅ REST API functions loaded</div>';
            $fixes_applied[] = 'Loaded REST API functions';
        } else {
            echo '<div class="warning">⚠️ REST API file not found</div>';
        }
    } else {
        echo '<div class="success">✅ REST API functions already exist</div>';
    }
    
    // Force re-register REST routes
    if (function_exists('chatgabi_register_rest_routes')) {
        echo '<div class="info">🔧 Re-registering REST routes...</div>';
        chatgabi_register_rest_routes();
        echo '<div class="success">✅ REST routes registered</div>';
        $fixes_applied[] = 'Re-registered REST routes';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ REST API error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'REST API error: ' . $e->getMessage();
}

// Fix 3: Ensure Template Categories Function
echo '<h2>📂 Fix 3: Template Categories Function</h2>';

try {
    if (!function_exists('chatgabi_get_template_categories')) {
        echo '<div class="info">🔧 Creating template categories function...</div>';
        
        // Create the function inline
        eval('
        function chatgabi_get_template_categories() {
            global $wpdb;
            $table = $wpdb->prefix . "chatgabi_template_categories";
            
            $categories = $wpdb->get_results("
                SELECT id, name, slug, description, icon, color, sort_order
                FROM $table 
                WHERE status = \"active\" 
                ORDER BY sort_order ASC, name ASC
            ");
            
            return $categories ?: array();
        }
        ');
        
        echo '<div class="success">✅ Template categories function created</div>';
        $fixes_applied[] = 'Created template categories function';
    } else {
        echo '<div class="success">✅ Template categories function already exists</div>';
    }
    
    // Test the function
    $categories = chatgabi_get_template_categories();
    echo '<div class="info">📊 Found ' . count($categories) . ' categories</div>';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Categories function error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Categories function error: ' . $e->getMessage();
}

// Fix 4: Ensure JavaScript Configuration
echo '<h2>📜 Fix 4: JavaScript Configuration</h2>';

try {
    // Check if templates page exists and has proper configuration
    $templates_page = get_page_by_path('templates');
    if ($templates_page) {
        echo '<div class="success">✅ Templates page exists</div>';
        
        // Check if JavaScript is properly configured
        $template_file = get_template_directory() . '/page-templates.php';
        if (file_exists($template_file)) {
            $template_content = file_get_contents($template_file);
            
            if (strpos($template_content, 'chatgabiTemplatesConfig') !== false) {
                echo '<div class="success">✅ JavaScript configuration found</div>';
            } else {
                echo '<div class="warning">⚠️ JavaScript configuration missing</div>';
                $errors_encountered[] = 'JavaScript configuration missing';
            }
            
            if (strpos($template_content, 'wp_enqueue_script') !== false) {
                echo '<div class="success">✅ Scripts are enqueued</div>';
            } else {
                echo '<div class="warning">⚠️ Scripts not properly enqueued</div>';
                $errors_encountered[] = 'Scripts not properly enqueued';
            }
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ JavaScript configuration error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'JavaScript configuration error: ' . $e->getMessage();
}

// Fix 5: Test REST API Endpoints
echo '<h2>🧪 Fix 5: Test REST API Endpoints</h2>';

try {
    $rest_endpoints = array(
        'chatgabi/v1/templates' => 'Templates listing',
        'chatgabi/v1/template-categories' => 'Template categories'
    );
    
    foreach ($rest_endpoints as $endpoint => $description) {
        $rest_url = rest_url($endpoint);
        echo '<div class="info">🔗 Testing: ' . $rest_url . '</div>';
        
        // Simple test to see if endpoint responds
        $response = wp_remote_get($rest_url, array(
            'timeout' => 10,
            'headers' => array(
                'X-WP-Nonce' => wp_create_nonce('wp_rest')
            )
        ));
        
        if (!is_wp_error($response)) {
            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code === 200) {
                echo '<div class="success">✅ Endpoint responding: ' . $endpoint . '</div>';
            } else {
                echo '<div class="warning">⚠️ Endpoint status ' . $status_code . ': ' . $endpoint . '</div>';
            }
        } else {
            echo '<div class="error">❌ Endpoint error: ' . $response->get_error_message() . '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Endpoint testing error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Endpoint testing error: ' . $e->getMessage();
}

// Summary
echo '<h2>📋 Fix Summary</h2>';

if (empty($errors_encountered)) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL FUNCTIONALITY ISSUES FIXED!</h3>';
    echo '<p><strong>✅ The templates page should now be fully functional!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues May Remain</h3>';
    echo '<ul>';
    foreach ($errors_encountered as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Fixes Applied: ' . count($fixes_applied) . '</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Test Actions
echo '<h2>🧪 Test Your Fixes</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="fix-button">🎯 Test Templates Page</a>';
}

echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="fix-button">🌐 Test REST API</a>';
echo '<a href="diagnose-templates-functionality.php" class="fix-button">🔍 Re-run Diagnosis</a>';
echo '<a href="javascript:window.location.reload()" class="fix-button">🔄 Re-run Fixes</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Fix completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

<?php
/**
 * Plugin Name: ChatGABI Menu Fix
 * Description: Fixes WordPress menu registration for ChatGABI
 * Version: 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Fixed menu registration
function chatgabi_menu_fix() {
    add_menu_page(
        'ChatGABI',
        'ChatGABI',
        'manage_options',
        'chatgabi-fixed',
        'chatgabi_main_page_callback',
        'dashicons-admin-comments',
        30
    );
    
    add_submenu_page(
        'chatgabi-fixed',
        'Engagement Analytics',
        'Engagement Analytics',
        'manage_options',
        'chatgabi-engagement-analytics',
        'chatgabi_engagement_analytics_callback'
    );
}
add_action('admin_menu', 'chatgabi_menu_fix', 5);

function chatgabi_main_page_callback() {
    if (function_exists('businesscraft_ai_admin_page')) {
        businesscraft_ai_admin_page();
    } else {
        echo '<div class="wrap"><h1>ChatGABI Dashboard</h1><p>Main dashboard functionality will be loaded here.</p></div>';
    }
}

function chatgabi_engagement_analytics_callback() {
    if (function_exists('chatgabi_engagement_analytics_page')) {
        chatgabi_engagement_analytics_page();
    } else {
        echo '<div class="wrap"><h1>Engagement Analytics</h1><p>Analytics functionality will be loaded here.</p></div>';
    }
}
?>
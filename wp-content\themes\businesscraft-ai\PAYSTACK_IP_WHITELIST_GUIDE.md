# Paystack IP Whitelist Issue - Troubleshooting Guide

## 🚨 Error Message
```
Failed to initialize payment: Your IP address is not allowed to make this call
```

## 🔍 What This Means
This error occurs when Paystack has IP whitelisting enabled on your account, and your server's IP address is not in the allowed list. This is a security feature that prevents unauthorized API calls.

## 🛠️ Solutions

### Option 1: Add Your Server IP to Paystack Whitelist (Recommended)

#### Step 1: Find Your Server's IP Address
1. Go to WordPress Admin → Tools → BusinessCraft AI → Settings
2. Look for the "IP Whitelist Status" section
3. Note the "Server IP" displayed there

**Alternative methods to find your IP:**
- SSH into your server and run: `curl ifconfig.me`
- Use online tools like whatismyipaddress.com from your server
- Check your hosting provider's control panel

#### Step 2: Add IP to Paystack Dashboard
1. **Login to Paystack Dashboard**
   - Go to [dashboard.paystack.com](https://dashboard.paystack.com)
   - Login with your Paystack credentials

2. **Navigate to API Settings**
   - Click on "Settings" in the left sidebar
   - Select "API Keys & Webhooks"

3. **Find IP Whitelisting Section**
   - Scroll down to find "IP Whitelisting" or "Allowed IPs"
   - This section controls which IP addresses can make API calls

4. **Add Your Server IP**
   - Click "Add IP Address" or similar button
   - Enter your server's IP address (from Step 1)
   - Add a description like "BusinessCraft AI Server"
   - Save the changes

5. **Test the Connection**
   - Go back to WordPress Admin → Tools → BusinessCraft AI → Settings
   - Click "Check IP Whitelist" button
   - You should see a success message

### Option 2: Disable IP Whitelisting (Less Secure)

⚠️ **Warning:** This makes your account less secure but may be necessary for some hosting environments.

1. Go to Paystack Dashboard → Settings → API Keys & Webhooks
2. Find the "IP Whitelisting" section
3. Disable or turn off IP whitelisting
4. Save changes

### Option 3: Use Paystack Test Mode

If you're still in development:
1. Ensure you're using Paystack test keys (they start with `pk_test_` and `sk_test_`)
2. Test mode usually has more relaxed IP restrictions
3. Switch to live mode only when ready for production

## 🔧 Advanced Troubleshooting

### Multiple Server IPs
If your hosting uses multiple servers or load balancers:
1. Contact your hosting provider to get all possible IP addresses
2. Add all IPs to Paystack whitelist
3. Consider using a dedicated IP address

### Dynamic IP Addresses
If your server IP changes frequently:
1. Consider upgrading to a dedicated IP address
2. Use a VPS or dedicated server with static IP
3. Contact your hosting provider about IP stability

### Proxy/CDN Issues
If using Cloudflare or similar services:
1. Whitelist Cloudflare's IP ranges in Paystack
2. Or bypass CDN for payment endpoints
3. Check if your CDN is modifying request headers

## 🧪 Testing Your Fix

### Method 1: WordPress Admin
1. Go to Tools → BusinessCraft AI → Settings
2. Click "Check IP Whitelist" button
3. Should show "IP address is whitelisted"

### Method 2: Try a Payment
1. Go to your website's pricing section
2. Try purchasing a credit pack
3. Payment popup should appear without errors

### Method 3: Check Logs
1. Enable WordPress debug logging in `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```
2. Check `/wp-content/debug.log` for detailed error messages

## 📞 Getting Help

### Paystack Support
- Email: <EMAIL>
- Documentation: https://paystack.com/docs/
- Status page: https://status.paystack.com/

### Hosting Provider
- Contact your hosting provider if you can't determine your server IP
- Ask about static IP addresses or dedicated IPs
- Inquire about firewall or proxy configurations

### BusinessCraft AI Support
- Check the theme's README.md file
- Review WordPress error logs
- Test with Paystack test keys first

## 🔒 Security Best Practices

1. **Always use IP whitelisting in production** - Only disable temporarily for testing
2. **Use strong API keys** - Regenerate keys if compromised
3. **Monitor transactions** - Check Paystack dashboard regularly
4. **Keep backups** - Backup your site before making changes
5. **Use HTTPS** - Ensure all payment pages use SSL

## 📝 Common Hosting Providers

### Shared Hosting
- Often uses dynamic IPs
- May require contacting support for IP information
- Consider upgrading to VPS for static IP

### VPS/Cloud Hosting
- Usually provides static IP addresses
- Check your hosting control panel for IP information
- May have multiple IPs for different services

### Managed WordPress Hosting
- Contact support for IP whitelist requirements
- Some providers handle this automatically
- Check if they have Paystack integration guides

## ✅ Verification Checklist

- [ ] Server IP address identified
- [ ] IP added to Paystack whitelist
- [ ] Paystack test connection successful
- [ ] Payment test completed successfully
- [ ] Error logs cleared of IP-related errors
- [ ] Production testing completed

---

**Last Updated:** December 2024
**Version:** 1.0.0

<?php
/**
 * Test Credit Purchase Flow
 * 
 * Tests the complete credit purchase flow after function conflict resolution
 */

// Include WordPress
require_once 'wp-config.php';

// Set up WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Purchase Flow Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; font-weight: 600; }
        .error { color: #dc3545; font-weight: 600; }
        .warning { color: #ffc107; font-weight: 600; }
        .info { color: #17a2b8; font-weight: 600; }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .demo-section {
            background: #e8f4fd;
            border: 2px solid #007cba;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        .demo-section h3 { color: #007cba; margin-top: 0; }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .endpoint-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .endpoint-test h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .endpoint-result {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 Credit Purchase Flow Test</h1>
            <p>Testing the complete credit purchase flow after function conflict resolution</p>
            <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- Test 1: AJAX Endpoint Test -->
        <div class="test-section">
            <h2>🔗 Test 1: AJAX Endpoint Test</h2>
            
            <div class="test-item">
                <h3>Dashboard AJAX Credit Purchase</h3>
                <p>This tests the AJAX endpoint used by the dashboard credit purchase modal.</p>
                
                <div class="endpoint-test">
                    <h4>AJAX Action: businesscraft_ai_initiate_payment</h4>
                    <p><strong>Function:</strong> businesscraft_ai_initiate_payment()</p>
                    <p><strong>File:</strong> wp-content/themes/businesscraft-ai/inc/credit-purchase-handlers.php</p>
                    <p><strong>Used by:</strong> Dashboard credit purchase modal</p>
                    
                    <button class="btn btn-primary" onclick="testAjaxEndpoint()">Test AJAX Endpoint</button>
                    <div id="ajax-result" class="endpoint-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Test 2: REST API Endpoint Test -->
        <div class="test-section">
            <h2>🌐 Test 2: REST API Endpoint Test</h2>
            
            <div class="test-item">
                <h3>Frontend REST API Credit Purchase</h3>
                <p>This tests the REST API endpoint used by the frontend payments.js system.</p>
                
                <div class="endpoint-test">
                    <h4>REST Endpoint: /wp-json/bcai/v1/initiate-payment</h4>
                    <p><strong>Function:</strong> businesscraft_ai_rest_initiate_payment()</p>
                    <p><strong>File:</strong> wp-content/themes/businesscraft-ai/inc/rest-api.php</p>
                    <p><strong>Used by:</strong> Frontend payments.js system</p>
                    
                    <button class="btn btn-primary" onclick="testRestEndpoint()">Test REST Endpoint</button>
                    <div id="rest-result" class="endpoint-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Test 3: Package Loading Test -->
        <div class="test-section">
            <h2>📦 Test 3: Package Loading Test</h2>
            
            <div class="test-item">
                <h3>Credit Package Information</h3>
                <p>This tests the credit package loading functionality.</p>
                
                <div class="endpoint-test">
                    <h4>AJAX Action: businesscraft_ai_get_credit_packages</h4>
                    <button class="btn btn-secondary" onclick="testPackageLoading()">Test Package Loading</button>
                    <div id="packages-result" class="endpoint-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Demo Section -->
        <div class="demo-section">
            <h3>🎯 Live Demo: Credit Purchase Modal</h3>
            <p>Test the actual credit purchase modal from the dashboard:</p>
            
            <button class="btn btn-primary" onclick="openDashboardModal()">
                Open Dashboard Credit Modal
            </button>
            
            <p><small><strong>Note:</strong> This will redirect you to the dashboard where you can test the actual credit purchase modal.</small></p>
        </div>

        <!-- Results Summary -->
        <div class="test-section">
            <h2>📊 Test Results Summary</h2>
            <div id="summary-results">
                <p>Run the tests above to see results here.</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            ajax: null,
            rest: null,
            packages: null
        };

        function testAjaxEndpoint() {
            const resultDiv = document.getElementById('ajax-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing AJAX endpoint...';

            // Test AJAX endpoint with proper parameters
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=businesscraft_ai_initiate_payment&nonce=test&package=starter&email=<EMAIL>'
            })
            .then(response => response.text())
            .then(data => {
                let status = 'unknown';
                let message = '';

                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success === false) {
                        if (jsonData.data && jsonData.data.includes('Security check failed')) {
                            status = 'security_check';
                            message = 'Security check failed (expected - nonce validation working)';
                        } else if (jsonData.data && jsonData.data.includes('User not logged in')) {
                            status = 'auth_required';
                            message = 'Authentication required (expected - endpoint accessible)';
                        } else {
                            status = 'other_error';
                            message = jsonData.data || 'Unknown error';
                        }
                    } else {
                        status = 'success';
                        message = 'Unexpected success';
                    }
                } catch (e) {
                    if (data.includes('businesscraft_ai_initiate_payment')) {
                        status = 'function_exists';
                        message = 'Function exists and is callable';
                    } else {
                        status = 'parse_error';
                        message = 'Could not parse response';
                    }
                }

                testResults.ajax = (status === 'security_check' || status === 'auth_required' || status === 'function_exists') ? 'success' : 'error';

                resultDiv.innerHTML = `
                    <strong>AJAX Endpoint Test:</strong><br>
                    Status: ${status}<br>
                    Message: ${message}<br>
                    Raw Response: ${data.substring(0, 300)}${data.length > 300 ? '...' : ''}<br>
                    <span style="color: ${testResults.ajax === 'success' ? '#28a745' : '#dc3545'};">
                        ${testResults.ajax === 'success' ? '✅' : '❌'} AJAX endpoint ${testResults.ajax === 'success' ? 'working correctly' : 'has issues'}
                    </span>
                `;
                updateSummary();
            })
            .catch(error => {
                testResults.ajax = 'error';
                resultDiv.innerHTML = `
                    <strong>AJAX Endpoint Error:</strong><br>
                    ${error.message}<br>
                    <span style="color: #dc3545;">❌ AJAX endpoint failed</span>
                `;
                updateSummary();
            });
        }

        function testRestEndpoint() {
            const resultDiv = document.getElementById('rest-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing REST endpoint...';

            // Test REST endpoint
            fetch('<?php echo rest_url('bcai/v1/initiate-payment'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    package: 'starter'
                })
            })
            .then(response => {
                return response.json().then(data => ({
                    status: response.status,
                    data: data
                }));
            })
            .then(result => {
                let status = 'unknown';
                let message = '';

                if (result.status === 401) {
                    status = 'auth_required';
                    message = 'Authentication required (expected - endpoint accessible)';
                    testResults.rest = 'success';
                } else if (result.status === 403) {
                    status = 'forbidden';
                    message = 'Access forbidden (endpoint exists but requires login)';
                    testResults.rest = 'success';
                } else if (result.status === 400) {
                    status = 'bad_request';
                    message = 'Bad request (endpoint accessible, validation working)';
                    testResults.rest = 'success';
                } else if (result.status === 500) {
                    status = 'server_error';
                    message = 'Server error: ' + (result.data.message || 'Unknown error');
                    testResults.rest = 'error';
                } else if (result.status === 200) {
                    status = 'success';
                    message = 'Unexpected success (should require authentication)';
                    testResults.rest = 'warning';
                } else {
                    status = 'unexpected';
                    message = 'Unexpected status code: ' + result.status;
                    testResults.rest = 'error';
                }

                resultDiv.innerHTML = `
                    <strong>REST Endpoint Test:</strong><br>
                    HTTP Status: ${result.status}<br>
                    Status: ${status}<br>
                    Message: ${message}<br>
                    Response: ${JSON.stringify(result.data, null, 2)}<br>
                    <span style="color: ${testResults.rest === 'success' ? '#28a745' : testResults.rest === 'warning' ? '#ffc107' : '#dc3545'};">
                        ${testResults.rest === 'success' ? '✅' : testResults.rest === 'warning' ? '⚠️' : '❌'}
                        REST endpoint ${testResults.rest === 'success' ? 'working correctly' : testResults.rest === 'warning' ? 'has warnings' : 'has issues'}
                    </span>
                `;
                updateSummary();
            })
            .catch(error => {
                testResults.rest = 'error';
                resultDiv.innerHTML = `
                    <strong>REST Endpoint Error:</strong><br>
                    ${error.message}<br>
                    <span style="color: #dc3545;">❌ REST endpoint failed</span>
                `;
                updateSummary();
            });
        }

        function testPackageLoading() {
            const resultDiv = document.getElementById('packages-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing package loading...';

            // Test package loading endpoint
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=businesscraft_ai_get_credit_packages&nonce=test'
            })
            .then(response => response.text())
            .then(data => {
                testResults.packages = 'success';
                resultDiv.innerHTML = `
                    <strong>Package Loading Response:</strong><br>
                    Response: ${data.substring(0, 300)}${data.length > 300 ? '...' : ''}<br>
                    <span style="color: #28a745;">✅ Package loading endpoint is working</span>
                `;
                updateSummary();
            })
            .catch(error => {
                testResults.packages = 'error';
                resultDiv.innerHTML = `
                    <strong>Package Loading Error:</strong><br>
                    ${error.message}<br>
                    <span style="color: #dc3545;">❌ Package loading failed</span>
                `;
                updateSummary();
            });
        }

        function openDashboardModal() {
            window.open('<?php echo home_url('/dashboard'); ?>', '_blank');
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary-results');
            const totalTests = Object.keys(testResults).length;
            const completedTests = Object.values(testResults).filter(result => result !== null).length;
            const successfulTests = Object.values(testResults).filter(result => result === 'success').length;

            summaryDiv.innerHTML = `
                <h3>Test Progress: ${completedTests}/${totalTests} completed</h3>
                <ul>
                    <li>AJAX Endpoint: ${getStatusIcon(testResults.ajax)} ${getStatusText(testResults.ajax)}</li>
                    <li>REST Endpoint: ${getStatusIcon(testResults.rest)} ${getStatusText(testResults.rest)}</li>
                    <li>Package Loading: ${getStatusIcon(testResults.packages)} ${getStatusText(testResults.packages)}</li>
                </ul>
                <p><strong>Success Rate:</strong> ${completedTests > 0 ? Math.round((successfulTests / completedTests) * 100) : 0}%</p>
                ${completedTests === totalTests ? '<p style="color: #28a745; font-weight: 600;">🎉 All tests completed!</p>' : ''}
            `;
        }

        function getStatusIcon(status) {
            switch(status) {
                case 'success': return '✅';
                case 'warning': return '⚠️';
                case 'error': return '❌';
                default: return '⏳';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'success': return 'Working';
                case 'warning': return 'Warning';
                case 'error': return 'Failed';
                default: return 'Pending';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Credit Purchase Flow Test Page Loaded');
            updateSummary();
        });
    </script>
</body>
</html>

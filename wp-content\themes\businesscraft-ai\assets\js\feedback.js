/**
 * Feedback Widget JavaScript
 * 
 * Handles user feedback interactions and submissions
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Global feedback data storage
window.feedbackData = {};

/**
 * Toggle detailed feedback form
 */
function toggleDetailedFeedback(widgetId) {
    const widget = document.getElementById(widgetId);
    const detailedForm = widget.querySelector('.feedback-detailed');
    
    if (detailedForm.style.display === 'none') {
        detailedForm.style.display = 'block';
        // Initialize feedback data for this widget
        if (!window.feedbackData[widgetId]) {
            window.feedbackData[widgetId] = {
                overall_rating: 0,
                thumbs_rating: null,
                category_ratings: {},
                feedback_text: '',
                training_consent: false
            };
        }
    } else {
        detailedForm.style.display = 'none';
    }
}

/**
 * Handle quick feedback (thumbs up/down)
 */
function handleQuickFeedback(widgetId, rating, type) {
    const widget = document.getElementById(widgetId);
    
    // Initialize feedback data if not exists
    if (!window.feedbackData[widgetId]) {
        window.feedbackData[widgetId] = {
            overall_rating: 0,
            thumbs_rating: null,
            category_ratings: {},
            feedback_text: '',
            training_consent: false
        };
    }
    
    // Update feedback data
    if (type === 'thumbs') {
        window.feedbackData[widgetId].thumbs_rating = rating;
        
        // Update UI
        const thumbsButtons = widget.querySelectorAll('.feedback-btn[data-type="thumbs"]');
        thumbsButtons.forEach(btn => btn.classList.remove('active'));
        
        const clickedButton = widget.querySelector(`.feedback-btn[data-rating="${rating}"][data-type="thumbs"]`);
        if (clickedButton) {
            clickedButton.classList.add('active');
        }
        
        // Auto-submit for quick feedback
        setTimeout(() => {
            submitFeedback(widgetId, true);
        }, 500);
    }
}

/**
 * Handle star rating
 */
function handleStarRating(widgetId, rating, type) {
    const widget = document.getElementById(widgetId);
    
    if (!window.feedbackData[widgetId]) {
        window.feedbackData[widgetId] = {
            overall_rating: 0,
            thumbs_rating: null,
            category_ratings: {},
            feedback_text: '',
            training_consent: false
        };
    }
    
    if (type === 'star') {
        window.feedbackData[widgetId].overall_rating = rating;
        
        // Update star UI
        const starButtons = widget.querySelectorAll('.star-btn');
        starButtons.forEach((btn, index) => {
            if (index < rating) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
}

/**
 * Handle category rating
 */
function handleCategoryRating(widgetId, category, rating) {
    const widget = document.getElementById(widgetId);
    
    if (!window.feedbackData[widgetId]) {
        window.feedbackData[widgetId] = {
            overall_rating: 0,
            thumbs_rating: null,
            category_ratings: {},
            feedback_text: '',
            training_consent: false
        };
    }
    
    // Update category rating data
    window.feedbackData[widgetId].category_ratings[category] = rating;
    
    // Update category stars UI
    const categoryStars = widget.querySelector(`.category-stars[data-category="${category}"]`);
    if (categoryStars) {
        const stars = categoryStars.querySelectorAll('.category-star');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }
}

/**
 * Submit feedback
 */
function submitFeedback(widgetId, isQuickFeedback = false) {
    const widget = document.getElementById(widgetId);
    const submitBtn = widget.querySelector('.feedback-submit-btn');
    
    if (!window.feedbackData[widgetId]) {
        console.error('No feedback data found for widget:', widgetId);
        return;
    }
    
    // Get additional data from widget
    const messageId = widget.dataset.messageId || '';
    const conversationId = widget.dataset.conversationId || '';
    const sessionId = widget.dataset.sessionId || '';
    const context = widget.dataset.context || 'general';
    
    // Get text feedback and consent if detailed form is shown
    if (!isQuickFeedback) {
        const textArea = widget.querySelector('.feedback-textarea');
        const consentCheckbox = widget.querySelector('.training-consent');
        
        if (textArea) {
            window.feedbackData[widgetId].feedback_text = textArea.value;
        }
        
        if (consentCheckbox) {
            window.feedbackData[widgetId].training_consent = consentCheckbox.checked;
        }
    }
    
    // Prepare submission data
    const submissionData = {
        action: 'chatgabi_submit_feedback',
        nonce: chatgabi_ajax.nonce,
        message_id: messageId,
        conversation_id: conversationId,
        session_id: sessionId,
        context: context,
        feedback_data: window.feedbackData[widgetId]
    };
    
    // Show loading state
    if (submitBtn) {
        submitBtn.textContent = 'Submitting...';
        submitBtn.disabled = true;
    }
    
    // Submit via AJAX
    jQuery.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: submissionData,
        success: function(response) {
            if (response.success) {
                showFeedbackSuccess(widgetId);
            } else {
                showFeedbackError(widgetId, response.data || 'Failed to submit feedback');
            }
        },
        error: function(xhr, status, error) {
            console.error('Feedback submission error:', error);
            showFeedbackError(widgetId, 'Network error occurred');
        },
        complete: function() {
            if (submitBtn) {
                submitBtn.textContent = 'Submit Feedback';
                submitBtn.disabled = false;
            }
        }
    });
}

/**
 * Show feedback success message
 */
function showFeedbackSuccess(widgetId) {
    const widget = document.getElementById(widgetId);
    const quickFeedback = widget.querySelector('.feedback-quick');
    const detailedFeedback = widget.querySelector('.feedback-detailed');
    const statusDiv = widget.querySelector('.feedback-status');
    
    // Hide feedback forms
    if (quickFeedback) quickFeedback.style.display = 'none';
    if (detailedFeedback) detailedFeedback.style.display = 'none';
    
    // Show success message
    if (statusDiv) {
        statusDiv.style.display = 'block';
    }
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        if (statusDiv) {
            statusDiv.style.display = 'none';
        }
        if (quickFeedback) quickFeedback.style.display = 'flex';
    }, 3000);
}

/**
 * Show feedback error message
 */
function showFeedbackError(widgetId, message) {
    const widget = document.getElementById(widgetId);
    
    // Create or update error message
    let errorDiv = widget.querySelector('.feedback-error');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'feedback-error';
        errorDiv.style.cssText = 'color: #dc3545; text-align: center; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; margin-top: 10px;';
        widget.appendChild(errorDiv);
    }
    
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    
    // Auto-hide error after 5 seconds
    setTimeout(() => {
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }, 5000);
}

/**
 * Initialize feedback widgets on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all feedback widgets
    const feedbackWidgets = document.querySelectorAll('.feedback-widget');
    
    feedbackWidgets.forEach(widget => {
        const widgetId = widget.id;
        
        // Quick feedback buttons
        const quickButtons = widget.querySelectorAll('.feedback-btn[data-type="thumbs"]');
        quickButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                handleQuickFeedback(widgetId, rating, 'thumbs');
            });
        });
        
        // Star rating buttons
        const starButtons = widget.querySelectorAll('.star-btn');
        starButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                handleStarRating(widgetId, rating, 'star');
            });
        });
        
        // Category rating buttons
        const categoryStars = widget.querySelectorAll('.category-star');
        categoryStars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                const category = this.closest('.category-stars').dataset.category;
                handleCategoryRating(widgetId, category, rating);
            });
        });
    });
});

/**
 * Add feedback widget to chat messages
 */
function addFeedbackToMessage(messageElement, messageId, conversationId, sessionId) {
    // Check if feedback widget already exists
    if (messageElement.querySelector('.feedback-widget')) {
        return;
    }
    
    // Create feedback widget
    const widgetId = 'feedback-widget-' + messageId;
    const feedbackHTML = `
        <div class="feedback-widget compact" id="${widgetId}" 
             data-message-id="${messageId}"
             data-conversation-id="${conversationId}"
             data-session-id="${sessionId}"
             data-context="chat">
            <div class="feedback-quick">
                <button class="feedback-btn feedback-thumbs-up" data-rating="1" data-type="thumbs">
                    <span class="feedback-icon">👍</span>
                </button>
                <button class="feedback-btn feedback-thumbs-down" data-rating="0" data-type="thumbs">
                    <span class="feedback-icon">👎</span>
                </button>
                <button class="feedback-btn feedback-detailed-btn" onclick="toggleDetailedFeedback('${widgetId}')">
                    <span class="feedback-icon">💬</span>
                </button>
            </div>
            <div class="feedback-status" style="display: none;">
                <div class="feedback-success">
                    <span class="feedback-icon">✅</span>
                    <span class="feedback-message">Thank you!</span>
                </div>
            </div>
        </div>
    `;
    
    // Add to message
    messageElement.insertAdjacentHTML('beforeend', feedbackHTML);
    
    // Initialize event listeners for the new widget
    const newWidget = document.getElementById(widgetId);
    if (newWidget) {
        const quickButtons = newWidget.querySelectorAll('.feedback-btn[data-type="thumbs"]');
        quickButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                handleQuickFeedback(widgetId, rating, 'thumbs');
            });
        });
    }
}

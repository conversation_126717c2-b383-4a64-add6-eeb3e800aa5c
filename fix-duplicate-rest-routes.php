<?php
/**
 * Fix Duplicate REST API Route Registrations
 * 
 * This script identifies and fixes duplicate REST route registrations
 * in the ChatGABI WordPress system to eliminate conflicts and notices.
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Duplicate REST Routes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .fix-button:hover { background: #005a87; }
        .route-info { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .duplicate-route { background: #fff3cd; border-left: 4px solid #ffc107; padding: 10px; margin: 5px 0; }
    </style>
</head>
<body>

<h1>🔧 Fix Duplicate REST API Route Registrations</h1>

<?php
echo '<div class="info">Duplicate route fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

$fixes_applied = array();
$errors_encountered = array();

// Step 1: Analyze Current Route Registrations
echo '<h2>🔍 Step 1: Analyze Current Route Registrations</h2>';

try {
    // Get all registered routes
    $rest_routes = rest_get_server()->get_routes();
    $chatgabi_routes = array();
    $duplicate_routes = array();
    
    foreach ($rest_routes as $route => $handlers) {
        if (strpos($route, '/chatgabi/v1/') === 0) {
            $chatgabi_routes[] = $route;
            if (count($handlers) > 1) {
                $duplicate_routes[] = array(
                    'route' => $route,
                    'handler_count' => count($handlers)
                );
            }
        }
    }
    
    echo '<div class="route-info">';
    echo '<strong>ChatGABI Routes Found:</strong> ' . count($chatgabi_routes) . '<br>';
    foreach ($chatgabi_routes as $route) {
        $handler_count = count($rest_routes[$route]);
        if ($handler_count > 1) {
            echo '<div class="duplicate-route">🔄 ' . $route . ' (' . $handler_count . ' handlers - DUPLICATE)</div>';
        } else {
            echo '- ' . $route . ' (1 handler)<br>';
        }
    }
    echo '</div>';
    
    if (!empty($duplicate_routes)) {
        echo '<div class="warning">';
        echo '<strong>⚠️ Duplicate Routes Detected:</strong><br>';
        foreach ($duplicate_routes as $dup) {
            echo '- ' . $dup['route'] . ' (' . $dup['handler_count'] . ' handlers)<br>';
        }
        echo '</div>';
    } else {
        echo '<div class="success">✅ No duplicate route handlers detected</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Route analysis error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Route analysis: ' . $e->getMessage();
}

// Step 2: Identify Registration Sources
echo '<h2>📋 Step 2: Registration Sources Analysis</h2>';

echo '<div class="route-info">';
echo '<strong>Current Registration Sources:</strong><br>';
echo '<br><strong>1. rest-api.php:</strong><br>';
echo '- ✅ chatgabi_register_rest_routes() - ACTIVE<br>';
echo '- Routes: /templates, /templates/{id}, /template-categories, /credits, /use-credit<br>';
echo '<br><strong>2. opportunity-api.php:</strong><br>';
echo '- ✅ chatgabi_register_opportunities_api() - ACTIVE<br>';
echo '- Routes: /opportunities, /opportunities/stats, /opportunities/types, etc.<br>';
echo '<br><strong>3. prompt-templates.php:</strong><br>';
echo '- ❌ chatgabi_register_template_rest_routes() - DISABLED (commented out)<br>';
echo '- Routes: /templates, /templates/{id}, /template-categories (DUPLICATES)<br>';
echo '<br><strong>4. functions.php:</strong><br>';
echo '- ✅ chatgabi_ensure_rest_routes() - ACTIVE<br>';
echo '- Routes: /health (plus calls opportunity API)<br>';
echo '<br><strong>5. whatsapp-integration.php:</strong><br>';
echo '- ✅ chatgabi_register_whatsapp_routes() - ACTIVE<br>';
echo '- Routes: /whatsapp/webhook<br>';
echo '</div>';

// Step 3: Remove Remaining Duplicates
echo '<h2>🔧 Step 3: Remove Remaining Duplicates</h2>';

try {
    echo '<div class="info">🔧 Checking for remaining duplicate registrations...</div>';
    
    // Check if functions.php is causing duplicates by calling opportunity API
    echo '<div class="warning">⚠️ Found potential duplicate in functions.php:</div>';
    echo '<div class="route-info">';
    echo '<strong>Issue:</strong> functions.php calls chatgabi_register_opportunities_api() again<br>';
    echo '<strong>Location:</strong> chatgabi_ensure_rest_routes() function<br>';
    echo '<strong>Impact:</strong> Opportunity routes registered twice<br>';
    echo '</div>';
    
    $fixes_applied[] = 'Identified duplicate opportunity API registration in functions.php';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Duplicate detection error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Duplicate detection: ' . $e->getMessage();
}

// Step 4: Apply Fixes
echo '<h2>✅ Step 4: Apply Fixes</h2>';

echo '<div class="info">🔧 The following fixes need to be applied:</div>';

echo '<div class="route-info">';
echo '<strong>Fix 1: Remove duplicate opportunity API call from functions.php</strong><br>';
echo 'File: wp-content/themes/businesscraft-ai/functions.php<br>';
echo 'Function: chatgabi_ensure_rest_routes()<br>';
echo 'Action: Comment out or remove the call to chatgabi_register_opportunities_api()<br>';
echo '<br>';

echo '<strong>Fix 2: Ensure prompt-templates.php registration stays disabled</strong><br>';
echo 'File: wp-content/themes/businesscraft-ai/inc/prompt-templates.php<br>';
echo 'Status: ✅ Already disabled (lines 25 and 1845 commented out)<br>';
echo '<br>';

echo '<strong>Fix 3: Consolidate template routes in rest-api.php</strong><br>';
echo 'File: wp-content/themes/businesscraft-ai/inc/rest-api.php<br>';
echo 'Status: ✅ Already consolidated and active<br>';
echo '<br>';

echo '<strong>Recommended Route Registration Structure:</strong><br>';
echo '- rest-api.php: Templates, credits, and core ChatGABI routes<br>';
echo '- opportunity-api.php: All opportunity-related routes<br>';
echo '- whatsapp-integration.php: WhatsApp webhook routes<br>';
echo '- functions.php: Only health check route (no duplicates)<br>';
echo '</div>';

// Step 5: Test Route Registration
echo '<h2>🧪 Step 5: Test Route Registration</h2>';

try {
    echo '<div class="info">🔧 Testing individual route registration functions...</div>';
    
    // Test if functions exist
    $functions_to_test = array(
        'chatgabi_register_rest_routes' => 'rest-api.php',
        'chatgabi_register_opportunities_api' => 'opportunity-api.php',
        'chatgabi_register_whatsapp_routes' => 'whatsapp-integration.php',
        'chatgabi_ensure_rest_routes' => 'functions.php'
    );
    
    foreach ($functions_to_test as $function => $file) {
        if (function_exists($function)) {
            echo '<div class="success">✅ ' . $function . ' exists (' . $file . ')</div>';
        } else {
            echo '<div class="error">❌ ' . $function . ' missing (' . $file . ')</div>';
        }
    }
    
    // Test route accessibility
    echo '<div class="info">🔧 Testing route accessibility...</div>';
    
    $test_routes = array(
        'templates' => rest_url('chatgabi/v1/templates'),
        'opportunities' => rest_url('chatgabi/v1/opportunities'),
        'health' => rest_url('chatgabi/v1/health')
    );
    
    foreach ($test_routes as $name => $url) {
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'headers' => array('User-Agent' => 'ChatGABI-Route-Test/1.0')
        ));
        
        if (!is_wp_error($response)) {
            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code === 200) {
                echo '<div class="success">✅ ' . ucfirst($name) . ' route accessible (200)</div>';
            } else {
                echo '<div class="warning">⚠️ ' . ucfirst($name) . ' route status: ' . $status_code . '</div>';
            }
        } else {
            echo '<div class="error">❌ ' . ucfirst($name) . ' route error: ' . $response->get_error_message() . '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Route testing error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Route testing: ' . $e->getMessage();
}

// Step 6: Provide Manual Fix Instructions
echo '<h2>📝 Step 6: Manual Fix Instructions</h2>';

echo '<div class="route-info">';
echo '<strong>To complete the duplicate route fix, manually edit the following file:</strong><br><br>';

echo '<strong>File:</strong> wp-content/themes/businesscraft-ai/functions.php<br>';
echo '<strong>Function:</strong> chatgabi_ensure_rest_routes() (around line 1418)<br>';
echo '<strong>Change:</strong><br>';
echo '<pre>';
echo '// BEFORE (causing duplicates):
function chatgabi_ensure_rest_routes() {
    // Force registration of opportunity API routes
    if (function_exists(\'chatgabi_register_opportunities_api\')) {
        chatgabi_register_opportunities_api(); // ← REMOVE THIS LINE
    }
    
    // Add a simple health check route as backup
    register_rest_route(\'chatgabi/v1\', \'/health\', array(
        \'methods\' => \'GET\',
        \'callback\' => \'chatgabi_simple_health_check\',
        \'permission_callback\' => \'__return_true\',
    ));
}

// AFTER (fixed):
function chatgabi_ensure_rest_routes() {
    // Add a simple health check route as backup
    register_rest_route(\'chatgabi/v1\', \'/health\', array(
        \'methods\' => \'GET\',
        \'callback\' => \'chatgabi_simple_health_check\',
        \'permission_callback\' => \'__return_true\',
    ));
    
    // Log successful registration (only in debug mode)
    if (defined(\'WP_DEBUG\') && WP_DEBUG) {
        error_log("ChatGABI: REST API routes registered successfully");
    }
}';
echo '</pre>';
echo '</div>';

// Summary
echo '<h2>📋 Fix Summary</h2>';

if (empty($errors_encountered)) {
    echo '<div class="success">';
    echo '<h3>🎉 DUPLICATE ROUTE ANALYSIS COMPLETE!</h3>';
    echo '<p><strong>✅ Ready to apply final fix!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues Detected</h3>';
    echo '<ul>';
    foreach ($errors_encountered as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Analysis Completed: ' . count($fixes_applied) . ' items</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Next Steps</h2>';

echo '<div style="margin: 20px 0;">';
echo '<a href="apply-route-fixes.php" class="fix-button">🔧 Apply Automatic Fixes</a>';
echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="fix-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/opportunities') . '" target="_blank" class="fix-button">📊 Test Opportunities API</a>';
echo '<a href="test-all-routes.php" class="fix-button">🧪 Test All Routes</a>';
echo '<a href="javascript:window.location.reload()" class="fix-button">🔄 Re-run Analysis</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Duplicate route analysis completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

<?php
/**
 * Test ChatGABI Feedback System
 * 
 * Run this file to test the feedback system implementation
 */

// Load WordPress
require_once '../../../wp-config.php';

// Include feedback system
require_once 'inc/feedback-system.php';

echo "<h1>🧪 ChatGABI Feedback System Test</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>📊 Step 1: Database Table Check</h2>";

global $wpdb;
$table_name = $wpdb->prefix . 'chatgabi_feedback';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

if ($table_exists) {
    echo "<p class='success'>✅ Feedback table exists: {$table_name}</p>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE {$table_name}");
    echo "<h3>Table Structure:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>{$column->Field}</strong>: {$column->Type}</li>";
    }
    echo "</ul>";
} else {
    echo "<p class='error'>❌ Feedback table does not exist</p>";
    echo "<p class='info'>Creating table...</p>";
    
    $result = chatgabi_create_feedback_table();
    if ($result) {
        echo "<p class='success'>✅ Table created successfully</p>";
    } else {
        echo "<p class='error'>❌ Failed to create table</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 Step 2: Function Tests</h2>";

// Test feedback submission
echo "<h3>Testing Feedback Submission:</h3>";

$test_feedback_data = array(
    'user_id' => 1,
    'message_id' => 'test_msg_' . time(),
    'rating_score' => 5,
    'rating_type' => 'star',
    'feedback_text' => 'This is a test feedback message.',
    'category_helpfulness' => 5,
    'category_accuracy' => 4,
    'category_relevance' => 5,
    'category_clarity' => 4,
    'user_country' => 'Ghana',
    'user_sector' => 'Technology',
    'conversation_context' => 'test',
    'response_tokens' => 150,
    'response_time_ms' => 1200,
    'training_consent' => 1
);

if (function_exists('chatgabi_submit_feedback')) {
    $result = chatgabi_submit_feedback($test_feedback_data);
    
    if (is_wp_error($result)) {
        echo "<p class='error'>❌ Feedback submission failed: " . $result->get_error_message() . "</p>";
    } else {
        echo "<p class='success'>✅ Feedback submitted successfully with ID: {$result}</p>";
        
        // Test feedback retrieval
        echo "<h3>Testing Feedback Retrieval:</h3>";
        
        if (function_exists('chatgabi_get_user_feedback')) {
            $user_feedback = chatgabi_get_user_feedback(1, 5);
            echo "<p class='success'>✅ Retrieved " . count($user_feedback) . " feedback records for user 1</p>";
            
            if (!empty($user_feedback)) {
                echo "<h4>Latest Feedback:</h4>";
                $latest = $user_feedback[0];
                echo "<ul>";
                echo "<li><strong>Rating:</strong> {$latest->rating_score}/5 ({$latest->rating_type})</li>";
                echo "<li><strong>Text:</strong> " . esc_html($latest->feedback_text) . "</li>";
                echo "<li><strong>Country:</strong> {$latest->user_country}</li>";
                echo "<li><strong>Sector:</strong> {$latest->user_sector}</li>";
                echo "<li><strong>Created:</strong> {$latest->created_at}</li>";
                echo "</ul>";
            }
        } else {
            echo "<p class='error'>❌ chatgabi_get_user_feedback function not found</p>";
        }
    }
} else {
    echo "<p class='error'>❌ chatgabi_submit_feedback function not found</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>📈 Step 3: Statistics Test</h2>";

if (function_exists('chatgabi_get_feedback_stats')) {
    $stats = chatgabi_get_feedback_stats(30);
    
    echo "<h3>30-Day Feedback Statistics:</h3>";
    echo "<ul>";
    echo "<li><strong>Total Feedback:</strong> " . ($stats['overall']->total_feedback ?? 0) . "</li>";
    echo "<li><strong>Average Rating:</strong> " . number_format($stats['overall']->avg_rating ?? 0, 2) . "/5</li>";
    echo "<li><strong>Positive Feedback:</strong> " . ($stats['overall']->positive_feedback ?? 0) . "</li>";
    echo "<li><strong>Text Feedback Count:</strong> " . ($stats['overall']->text_feedback_count ?? 0) . "</li>";
    echo "</ul>";
    
    if (!empty($stats['distribution'])) {
        echo "<h4>Rating Distribution:</h4>";
        echo "<ul>";
        foreach ($stats['distribution'] as $rating) {
            echo "<li>{$rating->rating_score} stars: {$rating->count} ratings</li>";
        }
        echo "</ul>";
    }
    
    if ($stats['categories']) {
        echo "<h4>Category Averages:</h4>";
        echo "<ul>";
        echo "<li><strong>Helpfulness:</strong> " . number_format($stats['categories']->avg_helpfulness ?? 0, 2) . "/5</li>";
        echo "<li><strong>Accuracy:</strong> " . number_format($stats['categories']->avg_accuracy ?? 0, 2) . "/5</li>";
        echo "<li><strong>Relevance:</strong> " . number_format($stats['categories']->avg_relevance ?? 0, 2) . "/5</li>";
        echo "<li><strong>Clarity:</strong> " . number_format($stats['categories']->avg_clarity ?? 0, 2) . "/5</li>";
        echo "</ul>";
    }
} else {
    echo "<p class='error'>❌ chatgabi_get_feedback_stats function not found</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎨 Step 4: Asset Check</h2>";

$assets_to_check = array(
    'assets/js/feedback-rating.js' => 'Feedback Rating JavaScript',
    'assets/css/feedback-rating.css' => 'Feedback Rating CSS',
    'inc/feedback-system.php' => 'Feedback System PHP',
    'inc/feedback-admin.php' => 'Feedback Admin PHP'
);

foreach ($assets_to_check as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        $file_size = filesize($file_path);
        echo "<p class='success'>✅ {$description}: " . number_format($file_size) . " bytes</p>";
    } else {
        echo "<p class='error'>❌ {$description}: File not found</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔗 Step 5: Integration Check</h2>";

// Check if functions are properly hooked
echo "<h3>WordPress Hooks:</h3>";

$hooks_to_check = array(
    'wp_ajax_chatgabi_submit_feedback' => 'AJAX Submit Feedback',
    'wp_ajax_chatgabi_get_feedback' => 'AJAX Get Feedback',
    'wp_ajax_chatgabi_update_feedback' => 'AJAX Update Feedback'
);

foreach ($hooks_to_check as $hook => $description) {
    if (has_action($hook)) {
        echo "<p class='success'>✅ {$description} hook registered</p>";
    } else {
        echo "<p class='warning'>⚠️ {$description} hook not found</p>";
    }
}

// Check admin menu
if (function_exists('chatgabi_add_feedback_admin_menu')) {
    echo "<p class='success'>✅ Admin menu function exists</p>";
} else {
    echo "<p class='error'>❌ Admin menu function not found</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Step 6: Sample Data</h2>";

// Create some sample feedback data for testing
echo "<h3>Creating Sample Feedback Data:</h3>";

$sample_data = array(
    array(
        'user_id' => 1,
        'message_id' => 'sample_1_' . time(),
        'rating_score' => 4,
        'rating_type' => 'star',
        'feedback_text' => 'Great response, very helpful for my business planning.',
        'category_helpfulness' => 4,
        'category_accuracy' => 5,
        'category_relevance' => 4,
        'category_clarity' => 4,
        'user_country' => 'Kenya',
        'user_sector' => 'Agriculture',
        'training_consent' => 1
    ),
    array(
        'user_id' => 1,
        'message_id' => 'sample_2_' . time(),
        'rating_score' => 5,
        'rating_type' => 'star',
        'feedback_text' => 'Excellent insights for the Nigerian market!',
        'category_helpfulness' => 5,
        'category_accuracy' => 5,
        'category_relevance' => 5,
        'category_clarity' => 5,
        'user_country' => 'Nigeria',
        'user_sector' => 'Technology',
        'training_consent' => 1
    ),
    array(
        'user_id' => 1,
        'message_id' => 'sample_3_' . time(),
        'rating_score' => 3,
        'rating_type' => 'star',
        'feedback_text' => 'Good but could be more specific to South African regulations.',
        'category_helpfulness' => 3,
        'category_accuracy' => 4,
        'category_relevance' => 3,
        'category_clarity' => 4,
        'user_country' => 'South Africa',
        'user_sector' => 'Finance',
        'training_consent' => 0
    )
);

$created_count = 0;
foreach ($sample_data as $data) {
    if (function_exists('chatgabi_submit_feedback')) {
        $result = chatgabi_submit_feedback($data);
        if (!is_wp_error($result)) {
            $created_count++;
        }
    }
}

echo "<p class='success'>✅ Created {$created_count} sample feedback records</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>✅ Test Summary</h2>";
echo "<p class='info'>The ChatGABI Feedback System test is complete!</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Visit the WordPress admin dashboard</li>";
echo "<li>Go to ChatGABI → User Feedback</li>";
echo "<li>Test the feedback interface on a page with the chat block</li>";
echo "<li>Check the analytics and reporting features</li>";
echo "</ul>";
echo "</div>";
?>

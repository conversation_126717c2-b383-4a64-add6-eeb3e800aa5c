!function(m){m.PluginManager.add("wpemoji",function(n){var t,o=window.wp,e=window._wpemojiSettings,i=m.Env,a=window.navigator.userAgent,w=-1<a.indexOf("Windows"),a=!!((a=a.match(/Windows NT 6\.(\d)/))&&1<a[1]);function d(e){o.emoji.parse(e,{imgAttr:{"data-mce-resize":"false","data-mce-placeholder":"1","data-wp-emoji":"1"}})}function c(e){var t,o;e&&window.twemoji&&window.twemoji.test(e.textContent||e.innerText)&&(i.webkit&&(o=(t=n.selection).getBookmark()),d(e),i.webkit)&&t.moveToBookmark(o)}o&&o.emoji&&!e.supports.everything&&(a?n.on("keyup",function(e){231===e.keyCode&&c(n.selection.getNode())}):w||(n.on("keydown keyup",function(e){t="keydown"===e.type}),n.on("input",function(){t||c(n.selection.getNode())})),n.on("setcontent",function(e){var t=n.selection,o=t.getNode();window.twemoji&&window.twemoji.test(o.textContent||o.innerText)&&(d(o),i.ie)&&i.ie<9&&e.load&&o&&"BODY"===o.nodeName&&t.collapse(!0)}),n.on("PastePostProcess",function(e){window.twemoji&&m.each(n.dom.$("img.emoji",e.node),function(e){e.alt&&window.twemoji.test(e.alt)&&((e=e).className="emoji",e.setAttribute("data-mce-resize","false"),e.setAttribute("data-mce-placeholder","1"),e.setAttribute("data-wp-emoji","1"))})}),n.on("postprocess",function(e){e.content&&(e.content=e.content.replace(/<img[^>]+data-wp-emoji="[^>]+>/g,function(e){var t=e.match(/alt="([^"]+)"/);return t&&t[1]?t[1]:e}))}),n.on("resolvename",function(e){"IMG"===e.target.nodeName&&n.dom.getAttrib(e.target,"data-wp-emoji")&&e.preventDefault()}))})}(window.tinymce);
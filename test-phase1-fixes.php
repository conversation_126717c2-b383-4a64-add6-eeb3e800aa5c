<?php
/**
 * Test Phase 1 Critical Fixes Implementation
 * 
 * This file tests the implementation of:
 * 1. Fixed broken navigation links
 * 2. Resolved template schema mismatch
 * 3. Implemented user feedback interface
 */

// Include WordPress
require_once 'wp-config.php';

// Set up WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Start output buffering for clean display
ob_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 1 Critical Fixes - Live Test Results</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .success {
            color: #28a745;
            font-weight: 600;
        }
        .error {
            color: #dc3545;
            font-weight: 600;
        }
        .warning {
            color: #ffc107;
            font-weight: 600;
        }
        .info {
            color: #17a2b8;
            font-weight: 600;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .file-path {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }
        .summary h2 {
            margin-top: 0;
            color: white;
        }
        .demo-section {
            background: #e8f4fd;
            border: 2px solid #007cba;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        .demo-section h3 {
            color: #007cba;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Phase 1 Critical Fixes - Live Test Results</h1>
            <p>Testing implementation of broken navigation links fix, template schema resolution, and user feedback interface</p>
            <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <?php
        $tests_passed = 0;
        $tests_total = 0;
        $issues_found = array();
        ?>

        <!-- Test 1: Navigation Links Fix -->
        <div class="test-section">
            <h2>🔗 Test 1: Navigation Links Fix</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Dashboard Navigation Links</h3>';
            
            // Check if dashboard page exists and has correct navigation
            $dashboard_file = get_template_directory() . '/page-dashboard.php';
            if (file_exists($dashboard_file)) {
                $dashboard_content = file_get_contents($dashboard_file);
                
                // Check for credit purchase modal function
                if (strpos($dashboard_content, 'openCreditPurchaseModal()') !== false) {
                    echo '<p class="success">✅ Credit purchase link fixed with modal function</p>';
                    $tests_passed++;
                } else {
                    echo '<p class="error">❌ Credit purchase link not properly fixed</p>';
                    $issues_found[] = 'Credit purchase link missing modal function';
                }
                
                // Check for wizards page verification
                if (strpos($dashboard_content, 'get_page_by_path(\'wizards\')') !== false) {
                    echo '<p class="success">✅ Wizards link has proper page verification</p>';
                } else {
                    echo '<p class="warning">⚠️ Wizards link verification not found</p>';
                }
                
                // Check for credit purchase modal HTML
                if (strpos($dashboard_content, 'credit-purchase-modal') !== false) {
                    echo '<p class="success">✅ Credit purchase modal HTML present</p>';
                } else {
                    echo '<p class="error">❌ Credit purchase modal HTML missing</p>';
                    $issues_found[] = 'Credit purchase modal HTML not found';
                }
                
            } else {
                echo '<p class="error">❌ Dashboard file not found</p>';
                $issues_found[] = 'Dashboard file missing';
            }
            echo '</div>';
            ?>
        </div>

        <!-- Test 2: Template Schema Mismatch Resolution -->
        <div class="test-section">
            <h2>🗄️ Test 2: Template Schema Mismatch Resolution</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Database Schema Consistency</h3>';
            
            // Check REST API file for correct column mapping
            $rest_api_file = get_template_directory() . '/inc/rest-api.php';
            if (file_exists($rest_api_file)) {
                $rest_content = file_get_contents($rest_api_file);
                
                // Check for correct column usage
                if (strpos($rest_content, 'prompt_text') !== false && 
                    strpos($rest_content, "'prompt_content' => \$template->prompt_text") !== false) {
                    echo '<p class="success">✅ REST API correctly maps prompt_text to prompt_content</p>';
                    $tests_passed++;
                } else {
                    echo '<p class="error">❌ REST API column mapping not correct</p>';
                    $issues_found[] = 'REST API column mapping issue';
                }
                
                // Check database table schema
                global $wpdb;
                $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';
                $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'prompt_%'");
                
                if (!empty($columns)) {
                    foreach ($columns as $column) {
                        if ($column->Field === 'prompt_text') {
                            echo '<p class="success">✅ Database has prompt_text column</p>';
                        } elseif ($column->Field === 'prompt_content') {
                            echo '<p class="info">ℹ️ Database also has prompt_content column</p>';
                        }
                    }
                } else {
                    echo '<p class="error">❌ No prompt columns found in database</p>';
                    $issues_found[] = 'Database prompt columns missing';
                }
                
            } else {
                echo '<p class="error">❌ REST API file not found</p>';
                $issues_found[] = 'REST API file missing';
            }
            echo '</div>';
            ?>
        </div>

        <!-- Test 3: User Feedback Interface Implementation -->
        <div class="test-section">
            <h2>💬 Test 3: User Feedback Interface Implementation</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Feedback Widget Component</h3>';
            
            // Check feedback widget component
            $feedback_widget = get_template_directory() . '/template-parts/components/feedback-widget.php';
            if (file_exists($feedback_widget)) {
                echo '<p class="success">✅ Feedback widget component created</p>';
                
                $widget_content = file_get_contents($feedback_widget);
                if (strpos($widget_content, 'feedback-quick') !== false && 
                    strpos($widget_content, 'feedback-detailed') !== false) {
                    echo '<p class="success">✅ Widget has both quick and detailed feedback options</p>';
                } else {
                    echo '<p class="warning">⚠️ Widget may be missing feedback options</p>';
                }
                
                $tests_passed++;
            } else {
                echo '<p class="error">❌ Feedback widget component not found</p>';
                $issues_found[] = 'Feedback widget component missing';
            }
            
            // Check feedback JavaScript
            $feedback_js = get_template_directory() . '/assets/js/feedback.js';
            if (file_exists($feedback_js)) {
                echo '<p class="success">✅ Feedback JavaScript file created</p>';
                
                $js_content = file_get_contents($feedback_js);
                if (strpos($js_content, 'submitFeedback') !== false && 
                    strpos($js_content, 'addFeedbackToMessage') !== false) {
                    echo '<p class="success">✅ JavaScript has required feedback functions</p>';
                } else {
                    echo '<p class="warning">⚠️ JavaScript may be missing required functions</p>';
                }
            } else {
                echo '<p class="error">❌ Feedback JavaScript file not found</p>';
                $issues_found[] = 'Feedback JavaScript file missing';
            }
            
            // Check dashboard feedback tab
            if (file_exists($dashboard_file)) {
                $dashboard_content = file_get_contents($dashboard_file);
                if (strpos($dashboard_content, 'data-tab="feedback"') !== false) {
                    echo '<p class="success">✅ Dashboard has feedback tab</p>';
                } else {
                    echo '<p class="error">❌ Dashboard feedback tab not found</p>';
                    $issues_found[] = 'Dashboard feedback tab missing';
                }
            }
            
            // Check script enqueuing
            $functions_file = get_template_directory() . '/functions.php';
            if (file_exists($functions_file)) {
                $functions_content = file_get_contents($functions_file);
                if (strpos($functions_content, 'chatgabi-feedback') !== false) {
                    echo '<p class="success">✅ Feedback script properly enqueued</p>';
                } else {
                    echo '<p class="error">❌ Feedback script not enqueued</p>';
                    $issues_found[] = 'Feedback script not enqueued';
                }
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Test 4: Chat Integration -->
        <div class="test-section">
            <h2>💬 Test 4: Chat Interface Integration</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Chat Block Feedback Integration</h3>';
            
            // Check chat block JavaScript modifications
            $chat_js = get_template_directory() . '/assets/js/chat-block.js';
            if (file_exists($chat_js)) {
                $chat_content = file_get_contents($chat_js);
                
                if (strpos($chat_content, 'addFeedbackToMessage') !== false) {
                    echo '<p class="success">✅ Chat block calls feedback function</p>';
                    $tests_passed++;
                } else {
                    echo '<p class="error">❌ Chat block not integrated with feedback</p>';
                    $issues_found[] = 'Chat block feedback integration missing';
                }
                
                if (strpos($chat_content, 'conversationId') !== false && 
                    strpos($chat_content, 'sessionId') !== false) {
                    echo '<p class="success">✅ Chat block passes conversation and session IDs</p>';
                } else {
                    echo '<p class="warning">⚠️ Chat block may not pass required IDs</p>';
                }
                
            } else {
                echo '<p class="error">❌ Chat block JavaScript not found</p>';
                $issues_found[] = 'Chat block JavaScript missing';
            }
            echo '</div>';
            ?>
        </div>

        <!-- Demo Section -->
        <div class="demo-section">
            <h3>🎯 Live Demo: Feedback Widget</h3>
            <p>Below is a live demonstration of the feedback widget component:</p>
            
            <?php
            // Include the feedback widget for demonstration
            if (file_exists($feedback_widget)) {
                echo '<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0;">';
                include $feedback_widget;
                echo '</div>';
            } else {
                echo '<p class="error">Cannot display demo - feedback widget not found</p>';
            }
            ?>
        </div>

        <!-- Summary -->
        <div class="summary">
            <h2>📊 Test Summary</h2>
            <p><strong>Tests Passed:</strong> <?php echo $tests_passed; ?> / <?php echo $tests_total; ?></p>
            <p><strong>Success Rate:</strong> <?php echo round(($tests_passed / $tests_total) * 100, 1); ?>%</p>
            
            <?php if (!empty($issues_found)): ?>
                <h3>🚨 Issues Found:</h3>
                <ul>
                    <?php foreach ($issues_found as $issue): ?>
                        <li><?php echo esc_html($issue); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p class="success">🎉 All critical fixes implemented successfully!</p>
            <?php endif; ?>
            
            <h3>✅ Implemented Features:</h3>
            <ul>
                <li>Fixed broken navigation links in dashboard</li>
                <li>Added credit purchase modal with proper functionality</li>
                <li>Resolved template schema mismatch with proper column mapping</li>
                <li>Created comprehensive feedback widget component</li>
                <li>Integrated feedback system with chat interface</li>
                <li>Added feedback tab to user dashboard</li>
                <li>Properly enqueued feedback JavaScript and AJAX handlers</li>
            </ul>
            
            <h3>🔄 Next Steps:</h3>
            <ul>
                <li>Test feedback submission functionality</li>
                <li>Verify credit purchase flow integration</li>
                <li>Test template operations with schema fixes</li>
                <li>Implement Phase 2 high-priority features</li>
            </ul>
        </div>
    </div>

    <script>
        // Add some interactivity to the demo
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Phase 1 Critical Fixes Test Page Loaded');
            console.log('Tests Passed: <?php echo $tests_passed; ?> / <?php echo $tests_total; ?>');
            
            // Test if feedback functions are available
            if (typeof addFeedbackToMessage === 'function') {
                console.log('✅ Feedback functions loaded successfully');
            } else {
                console.log('⚠️ Feedback functions not yet loaded');
            }
        });
    </script>
</body>
</html>

<?php
// Clean up output buffer
$output = ob_get_clean();
echo $output;
?>

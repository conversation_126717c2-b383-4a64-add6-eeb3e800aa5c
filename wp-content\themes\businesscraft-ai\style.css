/*
Theme Name: ChatGABI
Theme URI: https://chatgabi.ai
Description: Achieving General Africa Business Intelligence. AI-powered business intelligence theme for African entrepreneurs. Features credit-based OpenAI integration, Paystack payments, and multi-language support for English, Twi, Swahili, Yoruba, and Zulu.
Author: ChatGABI Team
Version: 1.0.0
Requires at least: 6.4
Tested up to: 6.8
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: chatgabi
Tags: ai, business, chat, gutenberg, blocks, payments, multi-language, african, intelligence
*/

/* --- ChatGABI African-Inspired Design System Variables --- */

:root {
    /* Light Theme (Default) - African-Inspired Palette */
    --color-background: #FFFFFF;
    --color-surface: #F8FAFC; /* Cards, secondary backgrounds */
    --color-primary-accent: #3D4E81; /* Deep blue inspired by African skies */
    --color-secondary-accent: #FFD700; /* Gold representing African prosperity */
    --color-tertiary-accent: #E67E22; /* Warm orange inspired by African sunsets */
    --color-earth-tone: #8B4513; /* Rich brown representing African earth */
    --color-nature-green: #27AE60; /* Vibrant green representing African nature */
    --color-text-primary: #1A202C;
    --color-text-secondary: #4A5568;
    --color-borders: #E2E8F0;
    --color-shadow-light: rgba(0, 0, 0, 0.05);
    --color-shadow-medium: rgba(0, 0, 0, 0.1);
    --color-shadow-strong: rgba(0, 0, 0, 0.15);

    /* African Cultural Colors */
    --color-kente-gold: #FFD700;
    --color-kente-red: #DC143C;
    --color-kente-green: #228B22;
    --color-kente-blue: #4169E1;
    --color-ubuntu-orange: #E95420;
    --color-safari-brown: #8B4513;
    --color-baobab-green: #6B8E23;

    /* General UI */
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-pill: 9999px;

    /* Transitions */
    /* African-inspired patterns */
    --pattern-kente: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23FFD700' fill-opacity='0.1'%3E%3Cpath d='M0 0h20v20H0V0zm20 20h20v20H20V20z'/%3E%3C/g%3E%3C/svg%3E");
    --pattern-mudcloth: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238B4513' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");

    --transition-speed: 0.3s ease;
}

/* Dark Theme Overrides */
body.theme-dark {
    --color-background: #0F172A;
    --color-surface: #1A243A; /* Slightly lighter for cards/surfaces */
    --color-primary-accent: #6E7FF3; /* End color of the new button gradient */
    --color-secondary-accent: #FFD700; /* Gold for icons */
    --color-text-primary: #FFFFFF; /* White text */
    --color-text-secondary: #F1F5F9; /* Lighter white for secondary text */
    --color-borders: #334155;
    --color-shadow-light: rgba(0, 0, 0, 0.2);
    --color-shadow-medium: rgba(0, 0, 0, 0.3);
    --color-shadow-strong: rgba(0, 0, 0, 0.4);
}

/* --- Reset and Base Styles --- */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Ubuntu', sans-serif;
    line-height: 1.7;
    color: var(--color-text-primary);
    background-color: var(--color-background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-speed), color var(--transition-speed);
    font-feature-settings: "liga" 1, "kern" 1;
}

/* Enhanced Typography Hierarchy with African Context */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Ubuntu', sans-serif;
    font-weight: 600;
    color: var(--color-text-primary);
    line-height: 1.3;
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    letter-spacing: -0.02em;
}

/* Improved heading scale for better hierarchy */
h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.03em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 650;
    line-height: 1.2;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2.25rem);
    font-weight: 600;
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.75rem);
    font-weight: 600;
}

h5 {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    font-weight: 600;
}

h6 {
    font-size: clamp(1rem, 1.5vw, 1.25rem);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

p {
    font-family: 'Inter', 'Segoe UI', 'Roboto', 'Ubuntu', sans-serif;
    font-weight: 400;
    margin-bottom: 1em;
    color: var(--color-text-secondary);
    font-size: clamp(1rem, 1.2vw, 1.125rem);
    line-height: 1.7;
}

/* African-inspired text styles */
.text-african-accent {
    color: var(--color-tertiary-accent);
    font-weight: 600;
}

.text-ubuntu-spirit {
    font-style: italic;
    color: var(--color-ubuntu-orange);
    font-weight: 500;
}

.text-prosperity {
    color: var(--color-kente-gold);
    font-weight: 700;
}

/* Enhanced readability for African languages */
.text-multilingual {
    font-feature-settings: "liga" 1, "kern" 1, "calt" 1;
    text-rendering: optimizeLegibility;
}

a {
    color: var(--color-primary-accent);
    text-decoration: none;
    transition: color var(--transition-speed);
}

a:hover {
    color: var(--color-secondary-accent);
}

button, input[type="submit"], input[type="text"], input[type="email"], input[type="password"], textarea, select {
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    transition: all var(--transition-speed);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-borders);
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    padding: 0.75rem 1rem;
}

button:hover, input[type="submit"]:hover {
    opacity: 0.9;
}

/* Utility Classes */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.text-center {
    text-align: center;
}

/* Enhanced Error and Warning Messages */
.error-message, .warning-message {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin: 10px 0;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    font-weight: 500;
    animation: slideInDown 0.3s ease-out;
}

.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
}

.warning-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.error-message .retry-btn, .error-message .close-btn, .warning-message .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.retry-btn {
    background: var(--color-primary-accent);
    color: white;
    font-weight: 600;
}

.retry-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.close-btn {
    font-size: 18px;
    font-weight: bold;
    opacity: 0.7;
}

.close-btn:hover {
    opacity: 1;
}

/* Enhanced Credit Display */
.credits-amount {
    font-weight: 600;
    margin-right: 8px;
}

.credits-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 600;
}

.credits-status.critical {
    background: #fee;
    color: #c33;
}

.credits-status.low {
    background: #fff3cd;
    color: #856404;
}

.credits-status.medium {
    background: #e3f2fd;
    color: #1976d2;
}

.credits-status.good {
    background: #e8f5e8;
    color: #2e7d32;
}

/* Credit Warning */
.credit-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius-md);
    padding: 12px;
    margin: 10px 0;
    animation: slideInDown 0.3s ease-out;
}

.warning-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.warning-icon {
    font-size: 16px;
}

.warning-text {
    flex: 1;
    font-weight: 500;
    color: #856404;
}

.buy-credits-btn {
    background: var(--color-primary-accent);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.buy-credits-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Cost Estimate */
.cost-estimate {
    background: var(--color-surface);
    border: 1px solid var(--color-borders);
    border-radius: var(--border-radius-md);
    padding: 8px 12px;
    margin: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: var(--color-text-secondary);
    animation: fadeIn 0.3s ease-out;
}

.estimate-icon {
    font-size: 14px;
}

.estimate-text {
    font-weight: 500;
}

/* Enhanced Credit Purchase Prompt */
.credit-purchase-prompt {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid var(--color-primary-accent);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    animation: slideInDown 0.4s ease-out;
    position: relative;
}

.prompt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.prompt-header h4 {
    margin: 0;
    color: var(--color-primary-accent);
    font-size: 18px;
    font-weight: 700;
}

.close-prompt {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-prompt:hover {
    background: #e9ecef;
    color: #495057;
}

.current-balance {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px 12px;
    margin: 12px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.balance-label {
    font-weight: 500;
    color: #6c757d;
}

.balance-amount {
    font-weight: 700;
    color: var(--color-primary-accent);
    font-size: 16px;
}

.credit-packages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
    margin: 16px 0;
}

.credit-package {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: var(--border-radius-md);
    padding: 16px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.credit-package:hover {
    border-color: var(--color-primary-accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.credit-package.growth-package::before {
    content: "POPULAR";
    position: absolute;
    top: 8px;
    right: -25px;
    background: var(--color-secondary-accent);
    color: white;
    padding: 2px 30px;
    font-size: 10px;
    font-weight: 700;
    transform: rotate(45deg);
}

.package-name {
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: 4px;
    font-size: 14px;
}

.package-price {
    font-size: 24px;
    font-weight: 900;
    color: var(--color-primary-accent);
    margin-bottom: 8px;
}

.package-details {
    font-size: 12px;
    color: var(--color-text-secondary);
    line-height: 1.4;
}

.package-details small {
    color: #6c757d;
    font-style: italic;
}

.prompt-footer {
    text-align: center;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
}

.prompt-footer small {
    color: #6c757d;
    font-size: 11px;
}

/* Mobile responsiveness for credit prompt */
@media (max-width: 768px) {
    .credit-packages {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .credit-package {
        padding: 12px;
    }

    .package-price {
        font-size: 20px;
    }
}

/* Enhanced Accessibility Styles */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Focus indicators */
button:focus,
input:focus,
select:focus,
textarea:focus,
.focusable:focus {
    outline: 3px solid #4A90E2;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn,
    .form-control,
    .card {
        border: 2px solid;
    }

    .btn-primary {
        background: #000;
        color: #fff;
        border-color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Mobile Chat Optimizations */
.mobile-chat {
    touch-action: pan-y;
}

.mobile-chat .chat-messages {
    padding: 10px;
    max-height: calc(100vh - 200px);
}

.mobile-chat .message {
    margin-bottom: 12px;
    max-width: 85%;
    word-wrap: break-word;
}

.mobile-chat .user-message {
    margin-left: auto;
    margin-right: 0;
}

.mobile-chat .ai-message {
    margin-left: 0;
    margin-right: auto;
}

.mobile-input {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 24px;
    min-height: 48px;
    resize: none;
    max-height: 120px;
}

.mobile-button {
    min-height: 48px;
    min-width: 48px;
    border-radius: 24px;
    font-size: 16px;
    touch-action: manipulation;
}

/* Virtual keyboard handling */
.keyboard-open .chat-container {
    height: calc(100vh - 300px);
}

.keyboard-open .chat-messages {
    max-height: calc(100vh - 350px);
}

/* Touch feedback */
.mobile-chat button:active,
.mobile-chat .touchable:active {
    transform: scale(0.98);
    opacity: 0.8;
}

/* Mobile-specific layout adjustments */
@media (max-width: 768px) {
    .chat-container {
        padding: 10px;
        margin: 0;
    }

    .chat-input-container {
        padding: 15px 10px;
        position: sticky;
        bottom: 0;
        background: var(--color-background);
        border-top: 1px solid var(--color-borders);
    }

    .chat-input {
        width: calc(100% - 60px);
        margin-right: 10px;
    }

    .chat-submit {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Mobile dashboard optimizations */
    .dashboard-container {
        padding: 10px;
    }

    .dashboard-tabs {
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .dashboard-tabs::-webkit-scrollbar {
        display: none;
    }

    .tab-button {
        flex: 0 0 auto;
        scroll-snap-align: start;
        white-space: nowrap;
        min-width: 120px;
    }

    /* Mobile forms */
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        font-size: 16px;
        padding: 12px 16px;
        min-height: 48px;
    }

    /* Mobile cards */
    .card {
        margin-bottom: 15px;
        border-radius: 12px;
    }

    .card-body {
        padding: 15px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .mobile-chat .message {
        max-width: 90%;
        font-size: 14px;
        padding: 10px 12px;
    }

    .mobile-input {
        font-size: 16px;
        padding: 10px 14px;
    }

    .mobile-button {
        min-height: 44px;
        min-width: 44px;
        font-size: 14px;
    }
}

/* === African Cultural Visual Elements === */

/* African-inspired decorative elements */
.african-pattern-accent {
    position: relative;
    overflow: hidden;
}

.african-pattern-accent::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: var(--pattern-kente);
    opacity: 0.05;
    pointer-events: none;
}

.mudcloth-pattern {
    background-image: var(--pattern-mudcloth);
    background-size: 60px 60px;
}

/* African-inspired section dividers */
.section-divider-african {
    height: 4px;
    background: linear-gradient(
        90deg,
        var(--color-kente-red) 0%,
        var(--color-kente-gold) 25%,
        var(--color-kente-green) 50%,
        var(--color-kente-blue) 75%,
        var(--color-kente-red) 100%
    );
    margin: 3rem 0;
    border-radius: 2px;
}

/* Ubuntu philosophy inspired elements */
.ubuntu-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-left: 4px solid var(--color-ubuntu-orange);
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 12px var(--color-shadow-light);
    position: relative;
}

.ubuntu-card::before {
    content: '🤝';
    position: absolute;
    top: -10px;
    left: 20px;
    background: var(--color-ubuntu-orange);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

/* African success story cards */
.success-story-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: 0 8px 24px var(--color-shadow-medium);
    position: relative;
    overflow: hidden;
    transition: transform var(--transition-speed);
}

.success-story-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(
        90deg,
        var(--color-kente-gold) 0%,
        var(--color-kente-green) 50%,
        var(--color-kente-blue) 100%
    );
}

.success-story-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px var(--color-shadow-strong);
}

/* Country flag inspired accents */
.country-accent-gh {
    border-left: 4px solid var(--color-kente-red);
    background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
}

.country-accent-ke {
    border-left: 4px solid var(--color-nature-green);
    background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
}

.country-accent-ng {
    border-left: 4px solid var(--color-nature-green);
    background: linear-gradient(135deg, #fff 0%, #f0fff0 100%);
}

.country-accent-za {
    border-left: 4px solid var(--color-tertiary-accent);
    background: linear-gradient(135deg, #fff 0%, #fff8f0 100%);
}

/* African business wisdom quotes */
.african-wisdom-quote {
    background: linear-gradient(135deg, var(--color-earth-tone) 0%, var(--color-safari-brown) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    position: relative;
    margin: 2rem 0;
}

.african-wisdom-quote::before {
    content: '"';
    font-size: 4rem;
    position: absolute;
    top: -10px;
    left: 20px;
    color: var(--color-kente-gold);
    font-family: serif;
}

.african-wisdom-quote::after {
    content: '"';
    font-size: 4rem;
    position: absolute;
    bottom: -30px;
    right: 20px;
    color: var(--color-kente-gold);
    font-family: serif;
}

.wisdom-text {
    font-size: 1.2rem;
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.wisdom-attribution {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 600;
}

/* African market indicators */
.market-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--color-surface);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid var(--color-borders);
}

.market-indicator.growing {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    color: var(--color-nature-green);
    border-color: var(--color-nature-green);
}

.market-indicator.opportunity {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: var(--color-earth-tone);
    border-color: var(--color-kente-gold);
}

.market-indicator.challenge {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-color: #dc3545;
}

/* Cultural celebration elements */
.celebration-banner {
    background: linear-gradient(
        45deg,
        var(--color-kente-red) 0%,
        var(--color-kente-gold) 25%,
        var(--color-kente-green) 50%,
        var(--color-kente-blue) 75%,
        var(--color-kente-red) 100%
    );
    background-size: 200% 200%;
    animation: kente-flow 4s ease-in-out infinite;
    color: white;
    padding: 1rem 2rem;
    text-align: center;
    font-weight: 600;
    border-radius: var(--border-radius-md);
}

@keyframes kente-flow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* African business icons */
.african-business-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-tertiary-accent) 0%, var(--color-earth-tone) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Buttons */
.button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md); /* More rounded buttons */
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-speed);
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 8px var(--color-shadow-light); /* Subtle shadow */
}

.button.primary-button {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3); /* New gradient for primary buttons */
    color: #FFFFFF; /* White text */
    border: none;
}

.button.primary-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px var(--color-shadow-medium);
}

.button.secondary-button {
    background-color: var(--color-surface);
    color: var(--color-primary-accent);
    border: 1px solid var(--color-borders);
    box-shadow: var(--color-shadow-light);
}

.button.secondary-button:hover {
    background-color: var(--color-primary-accent);
    color: var(--color-background);
    transform: translateY(-3px);
    box-shadow: var(--color-shadow-medium);
}

/* --- Header Styles --- */
.site-header {
    background: var(--color-background);
    box-shadow: 0 2px 8px var(--color-shadow-light);
    padding: 1rem 0;
    border-bottom: 1px solid var(--color-borders);
}

.site-header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.swiftmind-logo {
    font-size: 2rem;
    font-weight: 900;
    color: var(--color-primary-accent);
    margin: 0;
    letter-spacing: -0.05em;
}

.swiftmind-logo a {
    color: inherit;
    text-decoration: none;
}

.main-navigation ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 2rem;
}

.main-navigation li a {
    font-weight: 600;
    color: var(--color-text-secondary);
    padding: 0.5rem 0;
    position: relative;
}

.main-navigation li a::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-primary-accent);
    transition: width var(--transition-speed);
}

.main-navigation li a:hover::after {
    width: 100%;
}

.header-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--color-text-secondary);
}

.header-user-info .user-greeting {
    font-weight: 500;
}

.header-user-info .user-credits {
    background: var(--color-secondary-accent);
    color: var(--color-background);
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius-pill);
    font-weight: 600;
}

.header-user-info .logout-link {
    color: var(--color-text-secondary);
    text-decoration: underline;
}

.header-user-info .logout-link:hover {
    color: var(--color-primary-accent);
}

.header-user-info .login-link {
    color: var(--color-primary-accent);
    font-weight: 600;
}

.header-user-info .register-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.theme-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text-secondary);
    margin-left: 1rem;
    transition: color var(--transition-speed);
}

.theme-toggle:hover {
    color: var(--color-primary-accent);
}

.menu-toggle {
    display: none; /* Hidden by default, shown on mobile */
}

/* --- Main Content & Sections --- */
.site-main {
    padding: 0;
    min-height: 70vh;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3); /* Gradient background for branding */
    color: #FFFFFF; /* White text for contrast */
    padding: 5rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg);
    margin-bottom: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.hero-section .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 2rem;
}

.hero-content {
    flex: 1;
    min-width: 300px;
    text-align: left;
}

.hero-section h1 {
    color: #FFFFFF; /* White text for contrast */
    font-size: 3.8rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 1.1;
    text-shadow: 0 2px 4px var(--color-shadow-medium);
}

.hero-section .hero-subtitle {
    font-size: 1.3rem;
    max-width: 600px;
    margin-bottom: 2.5rem;
    color: #FFFFFF; /* White text for contrast */
    opacity: 0.9;
}

.hero-cta {
    background: var(--color-surface); /* Use surface color for CTA for contrast */
    color: var(--color-primary-accent);
    border: 2px solid var(--color-surface);
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: var(--border-radius-pill);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 4px 8px var(--color-shadow-medium);
}

.hero-cta:hover {
    background-color: var(--color-primary-accent);
    color: var(--color-background);
    border-color: var(--color-primary-accent);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px var(--color-shadow-strong);
}

.hero-illustration {
    flex: 1;
    min-width: 300px;
    text-align: right;
}

.hero-illustration img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 24px var(--color-shadow-strong);
}

/* Tabbed Tool Interface (Dashboard-style) */
.tools-dashboard-section {
    padding: 4rem 0;
    margin-bottom: 4rem;
}

.dashboard-wrapper {
    background-color: var(--color-surface);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 24px var(--color-shadow-medium);
    padding: 2rem;
}

.tabs-navigation {
    display: flex;
    border-bottom: 1px solid var(--color-borders);
    margin-bottom: 1.5rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed);
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.tab-button:hover {
    color: var(--color-primary-accent);
}

.tab-button.active {
    color: var(--color-primary-accent);
    border-bottom-color: var(--color-primary-accent);
}

.tabs-content {
    padding-top: 1.5rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
}

.tab-features {
    flex: 1;
    min-width: 300px;
    padding-right: 1rem;
}

.tab-features h3 {
    font-size: 1.8rem;
    margin-top: 0;
    margin-bottom: 1rem;
}

.tab-features ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 1.5rem;
}

.tab-features li {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: var(--color-text-secondary);
    position: relative;
    padding-left: 1.5rem;
}

.tab-features li::before {
    content: '✓';
    color: var(--color-secondary-accent); /* Gold checkmark */
    position: absolute;
    left: 0;
    font-weight: bold;
}

.tab-tool-interface {
    flex: 1;
    min-width: 350px;
    max-width: 500px;
    margin-left: auto;
}

/* Re-style existing chat block for dashboard integration */
.chatgabi-chat {
    background: var(--color-background); /* Use background for chat box */
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    box-shadow: 0 2px 8px var(--color-shadow-light);
    margin: 0;
    max-width: none;
}

.chatgabi-chat .chat-header {
    margin-bottom: 1rem;
}

.chatgabi-chat .chat-title {
    font-size: 1.2rem;
    color: var(--color-text-primary);
}

.chatgabi-chat .credit-display {
    background: var(--color-primary-accent);
    color: var(--color-background);
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.8rem;
}

.chatgabi-chat .chat-input {
    min-height: 80px;
    padding: 0.8rem;
    font-size: 0.9rem;
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border: 1px solid var(--color-borders);
}

.businesscraft-ai-chat .chat-input::placeholder {
    color: var(--color-text-secondary);
    opacity: 0.7;
}

.businesscraft-ai-chat .chat-input:focus {
    outline: none;
    border-color: var(--color-primary-accent);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-accent), 0.2);
}

.businesscraft-ai-chat .chat-submit {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3); /* New gradient for chat submit button */
    color: #FFFFFF; /* White text */
    border: none;
}

.businesscraft-ai-chat .chat-submit:hover {
    background-color: var(--color-primary-accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--color-shadow-medium);
}

.businesscraft-ai-chat .prompt-examples {
    padding: 1rem;
    margin-top: 1rem;
    background: var(--color-background);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-borders);
}

.businesscraft-ai-chat .prompt-examples h4 {
    color: var(--color-text-secondary);
    opacity: 1;
}

.businesscraft-ai-chat .example-prompt {
    background: var(--color-surface);
    border: 1px solid var(--color-borders);
    padding: 0.5rem 0.8rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.75rem;
    color: var(--color-text-secondary);
    box-shadow: none;
}

.businesscraft-ai-chat .example-prompt:hover {
    background: var(--color-primary-accent);
    color: var(--color-background);
    border-color: var(--color-primary-accent);
}

.chat-message.user {
    background: var(--color-surface);
    color: var(--color-text-primary);
    border-bottom-right-radius: var(--border-radius-sm);
}

.chat-message.ai {
    background: var(--color-background);
    color: var(--color-text-primary);
    border-bottom-left-radius: var(--border-radius-sm);
}

.message-meta {
    color: var(--color-text-secondary);
}

/* Chat Templates Access */
.chat-templates-access {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-borders);
}

.templates-quick-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
    padding: 0.7rem 1.2rem;
    border-radius: var(--border-radius-md);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all var(--transition-speed);
    border: none;
    cursor: pointer;
    white-space: nowrap;
}

.templates-quick-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
    color: white;
    text-decoration: none;
}

.templates-hint {
    color: var(--color-text-secondary);
    font-size: 0.85rem;
    font-style: italic;
    flex: 1;
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 24px var(--color-shadow-medium);
    margin: 4rem auto;
    max-width: 1200px;
}

.features-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--color-text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    background-color: var(--color-surface);
    padding: 2rem;
    border-radius: var(--border-radius-md);
    text-align: center;
    box-shadow: 0 2px 8px var(--color-shadow-light);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px var(--color-shadow-medium);
}

.feature-item .feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    color: var(--color-secondary-accent); /* Gold icon color */
}

.feature-item h3 {
    font-size: 1.5rem;
    color: var(--color-text-primary);
    margin-top: 0;
    margin-bottom: 0.75rem;
}

.feature-item p {
    font-size: 1rem;
    color: var(--color-text-secondary);
}

/* Social Proof Section */
.social-proof-section {
    padding: 4rem 0;
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3); /* Gradient background for branding */
    color: #FFFFFF; /* White text for contrast */
    text-align: center;
    margin-bottom: 4rem;
}

.social-proof-section h2 {
    color: #FFFFFF; /* White text for contrast */
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 24px var(--color-shadow-medium);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
    border: 3px solid var(--color-secondary-accent); /* Gold border */
}

.testimonial-quote {
    font-size: 1.1rem;
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
}

.testimonial-author {
    font-weight: 700;
    color: var(--color-primary-accent);
    font-size: 0.95rem;
}

/* Pricing Teaser Section */
.pricing-teaser-section {
    padding: 4rem 0;
    margin-bottom: 4rem;
    text-align: center;
}

.pricing-teaser-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--color-text-primary);
}

.pricing-intro {
    font-size: 1.1rem;
    color: var(--color-text-secondary);
    margin-bottom: 3rem;
}

.pricing-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.pricing-card {
    background-color: var(--color-surface);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 24px var(--color-shadow-medium);
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.pricing-card.featured {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3); /* New gradient for featured pricing card */
    color: #FFFFFF; /* White text */
    transform: scale(1.03);
    box-shadow: 0 12px 30px var(--color-shadow-strong);
}

.pricing-card.featured h3,
.pricing-card.featured .price,
.pricing-card.featured .benefits-list li {
    color: #FFFFFF; /* White text */
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px var(--color-shadow-strong);
}

.pricing-card h3 {
    font-size: 1.8rem;
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--color-text-primary);
}

.pricing-card .price {
    font-size: 3rem;
    font-weight: 900;
    color: var(--color-primary-accent);
    margin-bottom: 1.5rem;
}

.pricing-card.featured .price {
    color: #FFFFFF; /* White text */
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
    width: 100%;
}

.benefits-list li {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: var(--color-text-secondary);
    position: relative;
    padding-left: 1.5rem;
}

.benefits-list li::before {
    content: '✔';
    color: var(--color-secondary-accent); /* Gold checkmark */
    position: absolute;
    left: 0;
    font-weight: bold;
}

.pricing-card.featured .benefits-list li::before {
    color: #FFFFFF; /* White checkmark */
}

.pricing-card .button {
    width: 100%;
    margin-top: auto;
}

/* Footer */
.site-footer {
    background: var(--color-surface);
    color: var(--color-text-secondary);
    text-align: center;
    padding: 3rem 0;
    font-size: 0.9rem;
    border-top: 1px solid var(--color-borders);
}

.site-footer .container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
}

.footer-section {
    flex: 1;
    min-width: 200px;
    text-align: left;
}

.footer-section h3, .footer-section h4 {
    color: var(--color-text-primary);
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-section p {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

.footer-menu, .support-links, .language-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-menu li, .support-links li, .language-links li {
    margin-bottom: 0.5rem;
}

.footer-menu a, .support-links a, .language-links a {
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: color var(--transition-speed);
}

.footer-menu a:hover, .support-links a:hover, .language-links a:hover {
    color: var(--color-primary-accent);
}

.footer-bottom {
    width: 100%;
    border-top: 1px solid var(--color-borders);
    padding-top: 1.5rem;
    margin-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright, .powered-by {
    color: var(--color-text-secondary);
    margin: 0;
}

.powered-by a {
    color: var(--color-primary-accent);
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    color: var(--color-text-secondary);
    font-size: 1.5rem;
    transition: color var(--transition-speed);
}

.social-icons a:hover {
    color: var(--color-primary-accent);
}

/* --- Responsive Design --- */
@media (max-width: 992px) {
    .site-header-inner {
        flex-direction: column;
        text-align: center;
    }

    .main-navigation ul {
        flex-direction: column;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .header-user-info {
        margin-top: 1rem;
        justify-content: center;
    }

    .hero-section .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        text-align: center;
    }

    .hero-illustration {
        text-align: center;
    }

    .hero-section h1 {
        font-size: 3rem;
    }

    .hero-section .hero-subtitle {
        font-size: 1.2rem;
    }

    .dashboard-wrapper {
        padding: 1.5rem;
    }

    .tab-pane-inner {
        flex-direction: column;
    }

    .tab-features {
        padding-right: 0;
    }

    .tab-tool-interface {
        margin-left: 0;
        max-width: 100%;
    }

    .features-section, .social-proof-section, .pricing-teaser-section {
        padding: 3rem 0;
        margin: 3rem auto;
    }

    .features-section h2, .social-proof-section h2, .pricing-teaser-section h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .features-grid, .testimonials-grid, .pricing-cards-grid {
        grid-template-columns: 1fr;
    }

    .footer-section {
        text-align: center;
        min-width: unset;
        width: 100%;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .site-header {
        padding: 0.75rem 0;
    }

    .swiftmind-logo {
        font-size: 1.8rem;
    }

    .main-navigation ul {
        display: none; /* Hide navigation by default on smaller screens */
    }

    .menu-toggle {
        display: block; /* Show menu toggle button */
        background: none;
        border: 1px solid var(--color-borders);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius-md);
        color: var(--color-text-primary);
        font-weight: 600;
    }

    .menu-toggle:hover {
        background-color: var(--color-surface);
    }

    .hero-section {
        padding: 3rem 0;
    }

    .hero-section h1 {
        font-size: 2.2rem;
    }

    .hero-section .hero-subtitle {
        font-size: 1rem;
    }

    .hero-cta {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .dashboard-wrapper {
        padding: 1rem;
    }

    .tab-button {
        font-size: 1rem;
        padding: 0.8rem 1rem;
    }

    .businesscraft-ai-chat {
        padding: 1rem;
    }

    .businesscraft-ai-chat .chat-input {
        min-height: 60px;
    }

    .features-section, .social-proof-section, .pricing-teaser-section {
        padding: 2rem 0;
        margin: 2rem auto;
    }

    .features-section h2, .social-proof-section h2, .pricing-teaser-section h2 {
        font-size: 1.8rem;
    }

    .testimonial-card {
        padding: 1.5rem;
    }

    .pricing-card {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .swiftmind-logo {
        font-size: 1.5rem;
    }

    .hero-section h1 {
        font-size: 1.8rem;
    }

    .hero-section .hero-subtitle {
        font-size: 0.9rem;
    }

    .hero-cta {
        width: 100%;
        max-width: 250px;
    }

    .tab-button {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }

    .businesscraft-ai-chat .chat-title {
        font-size: 1rem;
    }

    .businesscraft-ai-chat .credit-display {
        font-size: 0.7rem;
    }

    .businesscraft-ai-chat .chat-input {
        font-size: 0.8rem;
    }

    .businesscraft-ai-chat .chat-submit {
        font-size: 0.8rem;
    }

    .features-section h2, .social-proof-section h2, .pricing-teaser-section h2 {
        font-size: 1.5rem;
    }

    .testimonial-quote {
        font-size: 1rem;
    }

    .pricing-card .price {
        font-size: 2.5rem;
    }
}

/* Enhanced Pricing Section Styles */
.pricing-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.pricing-header {
    text-align: center;
    margin-bottom: 3rem;
}

.pricing-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.pricing-section .section-subtitle {
    font-size: 1.125rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.pricing-section .pricing-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pricing-section .pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.pricing-section .pricing-card.popular {
    border: 2px solid #667eea;
    transform: scale(1.05);
}

.pricing-section .pricing-card.current-plan {
    border: 2px solid #27ae60;
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.plan-price {
    margin-bottom: 1rem;
}

.plan-price .currency {
    font-size: 1.25rem;
    color: #666;
    vertical-align: top;
}

.plan-price .amount {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
}

.plan-price .period {
    font-size: 1rem;
    color: #666;
}

.plan-description {
    color: #666;
    font-size: 0.875rem;
    line-height: 1.5;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: #555;
}

.plan-features .feature-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #27ae60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.plan-features .feature-icon:contains("⭐") {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.plan-action {
    margin-top: 2rem;
}

.plan-button {
    width: 100%;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
}

.plan-button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.plan-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.plan-button.current {
    background: #27ae60;
    color: white;
    cursor: default;
}

.plan-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.current-status {
    text-align: center;
    margin-bottom: 3rem;
}

.status-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: inline-block;
}

.tier-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.pricing-faq {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.pricing-faq h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.faq-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.faq-item p {
    color: #666;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.9);
}

/* Language Support Section */
.language-support-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.language-support-section h2 {
    text-align: center;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.language-support-section .section-subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.language-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.language-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.language-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.language-flag {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.language-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.language-card p {
    color: #666;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* CTA Section */
.cta-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.cta-buttons .button {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cta-buttons .button.primary {
    background: white;
    color: #667eea;
}

.cta-buttons .button.primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.cta-buttons .button.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-buttons .button.secondary:hover {
    background: white;
    color: #667eea;
}

.cta-features {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-feature {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Responsive adjustments for new sections */
@media (max-width: 768px) {
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .language-grid {
        grid-template-columns: 1fr;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .button {
        width: 100%;
        max-width: 300px;
    }

    .cta-features {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* IP Whitelist Status Styles */
.ip-whitelist-status {
    margin-top: 1rem;
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 6px;
    border-left: 4px solid #ddd;
}

.ip-whitelist-status h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1rem;
}

.ip-status-success {
    color: #155724;
}

.ip-status-success .dashicons {
    color: #28a745;
}

.ip-status-error {
    color: #721c24;
}

.ip-status-error .dashicons {
    color: #dc3545;
}

.ip-whitelist-status .notice {
    margin: 1rem 0 0 0;
}

.ip-whitelist-status ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.ip-whitelist-status code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    color: #d63384;
}

/* === Dynamic Currency Pricing Enhancements === */

/* Pricing Section Enhancements */
.pricing-card .price-note {
    margin-top: 8px;
    opacity: 0.75;
    font-style: italic;
    text-align: center;
}

.pricing-card .price-note small {
    font-size: 0.85em;
    color: var(--color-text-secondary);
    background: var(--color-surface);
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-borders);
}

/* Currency display improvements */
.pricing-card .plan-price {
    margin-bottom: 15px;
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 2px;
}

.pricing-card .plan-price .currency {
    font-size: 1.2em;
    font-weight: 600;
    vertical-align: top;
    color: var(--color-primary-accent);
}

.pricing-card .plan-price .amount {
    font-size: 3em;
    font-weight: 900;
    line-height: 1;
    color: var(--color-text-primary);
}

.pricing-card .plan-price .period {
    font-size: 0.9em;
    color: var(--color-text-secondary);
    margin-left: 4px;
    font-weight: 500;
}

/* Location indicator for debugging */
.location-debug {
    position: fixed;
    top: 10px;
    right: 10px;
    background: var(--color-surface);
    border: 1px solid var(--color-borders);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8em;
    color: var(--color-text-secondary);
    z-index: 1000;
    box-shadow: 0 2px 8px var(--color-shadow-light);
}

/* Enhanced pricing cards for multi-currency */
.pricing-card {
    position: relative;
    transition: all var(--transition-speed);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px var(--color-shadow-medium);
}

.pricing-card.popular {
    border: 2px solid var(--color-primary-accent);
}

.pricing-card.popular .plan-badge {
    background: linear-gradient(to right, var(--color-primary-accent), var(--color-secondary-accent));
    color: white;
    padding: 6px 16px;
    border-radius: var(--border-radius-pill);
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Template Discovery Section */
.template-discovery-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius-lg);
    margin: 2rem 0;
}

.template-discovery-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.discovery-header {
    text-align: center;
    margin-bottom: 3rem;
}

.discovery-header h2 {
    font-size: 2.5rem;
    color: var(--color-text-primary);
    margin-bottom: 1rem;
    font-weight: 700;
}

.discovery-header p {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.template-preview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.template-preview-card {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 12px var(--color-shadow-light);
    border: 2px solid transparent;
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
}

.template-preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    transform: scaleX(0);
    transition: transform var(--transition-speed);
}

.template-preview-card:hover::before {
    transform: scaleX(1);
}

.template-preview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--color-shadow-medium);
    border-color: var(--color-primary-accent);
}

.template-preview-icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    display: block;
}

.template-preview-card h3 {
    font-size: 1.5rem;
    color: var(--color-text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.template-preview-card p {
    color: var(--color-text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.template-stats {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.template-stats .stat {
    background: var(--color-surface);
    color: var(--color-text-secondary);
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.85rem;
    font-weight: 500;
}

.template-preview-btn {
    display: inline-block;
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius-md);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all var(--transition-speed);
    border: none;
    cursor: pointer;
}

.template-preview-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
    color: white;
    text-decoration: none;
}

.discovery-cta {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--color-borders);
}

.discovery-note {
    margin-top: 1rem;
    color: var(--color-text-secondary);
    font-size: 0.95rem;
    font-style: italic;
}

/* Navigation Enhancement for Templates */
.templates-nav-link {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all var(--transition-speed);
}

.templates-nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
    color: white !important;
}

.templates-nav-link::after {
    display: none;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .chat-templates-access {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        padding: 0.8rem;
    }

    .templates-quick-btn {
        justify-content: center;
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
    }

    .templates-hint {
        text-align: center;
        font-size: 0.8rem;
    }

    .template-discovery-section {
        padding: 3rem 0;
        margin: 1rem 0;
    }

    .discovery-header h2 {
        font-size: 2rem;
    }

    .discovery-header p {
        font-size: 1.1rem;
    }

    .template-preview-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .template-preview-card {
        padding: 1.5rem;
    }

    .template-preview-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .template-stats {
        flex-direction: column;
        gap: 0.5rem;
    }

    .hero-section .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        text-align: center;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .hero-section .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-illustration {
        text-align: center;
    }

    .tab-pane-inner {
        flex-direction: column;
    }

    .tab-features {
        padding-right: 0;
    }

    .main-navigation ul {
        flex-direction: column;
        gap: 1rem;
    }

    .site-header-inner {
        flex-direction: column;
        align-items: flex-start;
    }

    .header-user-info {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .menu-toggle {
        display: block;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--color-text-secondary);
    }

    .main-navigation {
        display: none; /* Hidden by default on mobile */
        width: 100%;
    }

    .main-navigation.active {
        display: block;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .language-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-features {
        flex-direction: column;
        gap: 0.5rem;
    }
}

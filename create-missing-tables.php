<?php
/**
 * Create missing ChatGABI database tables
 */

// Load WordPress
require_once 'wp-config.php';
require_once ABSPATH . 'wp-load.php';

echo "<h1>🔧 Creating Missing ChatGABI Tables</h1>\n";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; }</style>";

global $wpdb;

// Check if tables exist
$conversations_table = $wpdb->prefix . 'chatgabi_conversations';
$credit_transactions_table = $wpdb->prefix . 'chatgabi_credit_transactions';

$conversations_exists = $wpdb->get_var("SHOW TABLES LIKE '{$conversations_table}'") === $conversations_table;
$credit_transactions_exists = $wpdb->get_var("SHOW TABLES LIKE '{$credit_transactions_table}'") === $credit_transactions_table;

echo "<h2>📊 Current Table Status</h2>";
echo "<p>Conversations table ({$conversations_table}): " . ($conversations_exists ? "<span class='success'>EXISTS</span>" : "<span class='error'>MISSING</span>") . "</p>";
echo "<p>Credit transactions table ({$credit_transactions_table}): " . ($credit_transactions_exists ? "<span class='success'>EXISTS</span>" : "<span class='error'>MISSING</span>") . "</p>";

if (!$conversations_exists || !$credit_transactions_exists) {
    echo "<h2>🔧 Creating Missing Tables</h2>";
    
    // Include database functions
    require_once get_template_directory() . '/inc/database.php';
    
    if (function_exists('chatgabi_create_missing_tables')) {
        $result = chatgabi_create_missing_tables();
        
        if ($result) {
            echo "<p class='success'>✅ Table creation function executed successfully</p>";
            
            // Check again
            $conversations_exists_after = $wpdb->get_var("SHOW TABLES LIKE '{$conversations_table}'") === $conversations_table;
            $credit_transactions_exists_after = $wpdb->get_var("SHOW TABLES LIKE '{$credit_transactions_table}'") === $credit_transactions_table;
            
            echo "<h2>📊 Final Table Status</h2>";
            echo "<p>Conversations table: " . ($conversations_exists_after ? "<span class='success'>✅ CREATED</span>" : "<span class='error'>❌ FAILED</span>") . "</p>";
            echo "<p>Credit transactions table: " . ($credit_transactions_exists_after ? "<span class='success'>✅ CREATED</span>" : "<span class='error'>❌ FAILED</span>") . "</p>";
            
        } else {
            echo "<p class='error'>❌ Table creation failed</p>";
        }
    } else {
        echo "<p class='error'>❌ Table creation function not found</p>";
    }
} else {
    echo "<p class='success'>✅ All required tables already exist</p>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<p><a href='test-system-health.php'>Run System Health Test</a></p>";
echo "<p><a href='../'>Return to Website</a></p>";
?>

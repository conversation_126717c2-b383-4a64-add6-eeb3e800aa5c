<?php
/**
 * Token Optimization Test Script
 * 
 * This script tests the token optimization functionality
 * Run this from WordPress admin or via WP-CLI
 * 
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test token optimization functionality
 */
function businesscraft_ai_test_token_optimization() {
    echo "<h2>BusinessCraft AI Token Optimization Test</h2>\n";
    
    // Initialize components
    if (!class_exists('BusinessCraft_Token_Optimizer')) {
        echo "<p style='color: red;'>❌ Token Optimizer class not found!</p>\n";
        return;
    }
    
    if (!class_exists('BusinessCraft_African_Context_Engine')) {
        echo "<p style='color: red;'>❌ African Context Engine class not found!</p>\n";
        return;
    }
    
    $token_optimizer = new BusinessCraft_Token_Optimizer();
    $african_context = new BusinessCraft_African_Context_Engine();
    
    echo "<p style='color: green;'>✅ Classes loaded successfully</p>\n";
    
    // Test data
    $test_cases = array(
        array(
            'message' => 'I need help with market analysis for my tech startup in Ghana',
            'country' => 'GH',
            'business_type' => 'startup',
            'industry' => 'technology',
            'expected_type' => 'market_analysis'
        ),
        array(
            'message' => 'Create a financial plan for my small business',
            'country' => 'NG',
            'business_type' => 'sme',
            'industry' => 'retail',
            'expected_type' => 'financial_planning'
        ),
        array(
            'message' => 'Help me develop a marketing strategy for my restaurant',
            'country' => 'KE',
            'business_type' => 'sme',
            'industry' => 'hospitality',
            'expected_type' => 'marketing'
        )
    );
    
    echo "<h3>Testing Request Classification</h3>\n";
    
    foreach ($test_cases as $i => $test_case) {
        echo "<h4>Test Case " . ($i + 1) . ": " . esc_html($test_case['message']) . "</h4>\n";
        
        // Test request classification
        $reflection = new ReflectionClass($token_optimizer);
        $classify_method = $reflection->getMethod('classify_request');
        $classify_method->setAccessible(true);
        $classified_type = $classify_method->invoke($token_optimizer, $test_case['message']);
        
        echo "<p><strong>Expected Type:</strong> " . esc_html($test_case['expected_type']) . "</p>\n";
        echo "<p><strong>Classified Type:</strong> " . esc_html($classified_type) . "</p>\n";
        
        if ($classified_type === $test_case['expected_type']) {
            echo "<p style='color: green;'>✅ Classification correct</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ Classification different (may still be valid)</p>\n";
        }
        
        // Test context optimization
        $context_data = array(
            'country' => $test_case['country'],
            'business_type' => $test_case['business_type'],
            'industry' => $test_case['industry'],
            'language' => 'en',
            'context' => 'general'
        );
        
        // Add African context
        $country_context = $african_context->get_country_context($test_case['country']);
        $context_data = array_merge($context_data, $country_context);
        
        // Test optimization for different models
        $models = array('gpt-3.5-turbo', 'gpt-4');
        
        foreach ($models as $model) {
            echo "<h5>Testing with {$model}</h5>\n";
            
            try {
                $optimized_prompt = $token_optimizer->optimize_prompt(
                    $test_case['message'], 
                    $context_data, 
                    $model
                );
                
                $token_count = strlen($optimized_prompt) / 4; // Rough estimate
                $model_limits = $token_optimizer->get_model_limits($model);
                
                echo "<p><strong>Optimized Prompt Length:</strong> " . strlen($optimized_prompt) . " characters</p>\n";
                echo "<p><strong>Estimated Tokens:</strong> " . round($token_count) . "</p>\n";
                echo "<p><strong>Model Limit:</strong> " . $model_limits['optimal_prompt'] . " tokens</p>\n";
                
                if ($token_count <= $model_limits['optimal_prompt']) {
                    echo "<p style='color: green;'>✅ Within optimal token limit</p>\n";
                } else {
                    echo "<p style='color: red;'>❌ Exceeds optimal token limit</p>\n";
                }
                
                // Show first 200 characters of optimized prompt
                echo "<p><strong>Optimized Prompt Preview:</strong></p>\n";
                echo "<div style='background: #f5f5f5; padding: 10px; border-left: 3px solid #667eea; margin: 10px 0;'>";
                echo esc_html(substr($optimized_prompt, 0, 200)) . "...";
                echo "</div>\n";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error optimizing prompt: " . esc_html($e->getMessage()) . "</p>\n";
            }
        }
        
        echo "<hr>\n";
    }
    
    // Test caching functionality
    echo "<h3>Testing Context Caching</h3>\n";
    
    $cache_test_context = array(
        'country' => 'GH',
        'business_type' => 'sme',
        'industry' => 'agriculture'
    );
    
    // Generate cache key
    $reflection = new ReflectionClass($token_optimizer);
    $cache_key_method = $reflection->getMethod('generate_context_cache_key');
    $cache_key_method->setAccessible(true);
    $cache_key = $cache_key_method->invoke($token_optimizer, $cache_test_context, 'market_analysis');
    
    echo "<p><strong>Generated Cache Key:</strong> " . esc_html($cache_key) . "</p>\n";
    
    // Test cache miss (first time)
    $cached_context = get_transient($cache_key);
    if ($cached_context === false) {
        echo "<p style='color: green;'>✅ Cache miss detected (expected for first run)</p>\n";
    } else {
        echo "<p style='color: blue;'>ℹ️ Cache hit detected (context already cached)</p>\n";
    }
    
    // Test optimization metrics
    echo "<h3>Testing Optimization Metrics</h3>\n";
    
    try {
        $metrics = $token_optimizer->get_optimization_metrics(30);
        
        echo "<p><strong>Cache Hit Rate:</strong> " . $metrics['cache_hit_rate'] . "%</p>\n";
        echo "<p><strong>Cache Hits:</strong> " . $metrics['cache_hits'] . "</p>\n";
        echo "<p><strong>Cache Misses:</strong> " . $metrics['cache_misses'] . "</p>\n";
        echo "<p><strong>Prompt Compressions:</strong> " . $metrics['prompt_compressions'] . "</p>\n";
        echo "<p><strong>Total Optimizations:</strong> " . $metrics['total_optimizations'] . "</p>\n";
        
        echo "<p style='color: green;'>✅ Metrics retrieved successfully</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error retrieving metrics: " . esc_html($e->getMessage()) . "</p>\n";
    }
    
    // Test token statistics
    echo "<h3>Testing Token Statistics</h3>\n";
    
    try {
        $token_stats = $token_optimizer->get_token_stats(null, 30);
        
        if ($token_stats) {
            echo "<p><strong>Total Requests:</strong> " . $token_stats->total_requests . "</p>\n";
            echo "<p><strong>Average Tokens per Request:</strong> " . round($token_stats->avg_tokens_per_request, 2) . "</p>\n";
            echo "<p><strong>Total Tokens Used:</strong> " . number_format($token_stats->total_tokens) . "</p>\n";
            echo "<p><strong>Average Credits per Request:</strong> " . round($token_stats->avg_credits_per_request, 2) . "</p>\n";
            echo "<p><strong>Total Credits Used:</strong> " . number_format($token_stats->total_credits) . "</p>\n";
            
            echo "<p style='color: green;'>✅ Token statistics retrieved successfully</p>\n";
        } else {
            echo "<p style='color: blue;'>ℹ️ No token statistics available (no requests in database yet)</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error retrieving token statistics: " . esc_html($e->getMessage()) . "</p>\n";
    }
    
    echo "<h3>Test Summary</h3>\n";
    echo "<p style='color: green;'>✅ Token optimization system is functional and ready for use!</p>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Monitor the admin dashboard for optimization metrics</li>\n";
    echo "<li>Test with real user requests to validate effectiveness</li>\n";
    echo "<li>Adjust optimization parameters based on usage patterns</li>\n";
    echo "<li>Track cost savings over time</li>\n";
    echo "</ul>\n";
}

// Run the test if accessed directly (for development)
if (defined('WP_DEBUG') && WP_DEBUG && isset($_GET['test_token_optimization'])) {
    businesscraft_ai_test_token_optimization();
}

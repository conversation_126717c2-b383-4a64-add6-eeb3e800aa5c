<?php
/**
 * User Dashboard Template Functions
 * Auto-generated by fix script
 */

function chatgabi_display_user_templates($user_id = null) {
    if (!$user_id) $user_id = get_current_user_id();
    global $wpdb;
    $table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $templates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table WHERE user_id = %d OR is_public = 1 ORDER BY created_at DESC",
        $user_id
    ));
    return $templates;
}

function chatgabi_get_user_template_categories($user_id = null) {
    if (function_exists('chatgabi_get_template_categories')) {
        return chatgabi_get_template_categories();
    }
    return array();
}

function chatgabi_render_user_templates_widget() {
    $templates = chatgabi_display_user_templates();
    $categories = chatgabi_get_user_template_categories();
    
    echo '<div class="user-templates-widget">';
    echo '<h3>My Templates</h3>';
    
    if (!empty($templates)) {
        echo '<div class="templates-list">';
        foreach ($templates as $template) {
            echo '<div class="template-item">';
            echo '<h4>' . esc_html($template->title) . '</h4>';
            if ($template->description) {
                echo '<p>' . esc_html(wp_trim_words($template->description, 15)) . '</p>';
            }
            echo '<div class="template-actions">';
            echo '<a href="#" class="use-template" data-id="' . $template->id . '">Use Template</a>';
            if ($template->user_id == get_current_user_id()) {
                echo ' | <a href="#" class="edit-template" data-id="' . $template->id . '">Edit</a>';
                echo ' | <a href="#" class="delete-template" data-id="' . $template->id . '">Delete</a>';
            }
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
    } else {
        echo '<p>No templates found. <a href="' . home_url('/templates') . '">Browse Templates</a></p>';
    }
    
    echo '</div>';
}

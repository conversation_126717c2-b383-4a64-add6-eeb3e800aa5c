<?php
/**
 * ChatGABI Real-Time Translation System Test
 * 
 * Comprehensive test suite for WhatsApp translation capabilities
 * Access: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-translation-system.php
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Ensure we're in the correct theme context
if (get_template() !== 'businesscraft-ai') {
    die('Error: This test must be run with the businesscraft-ai theme active.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Translation System Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 30px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 6px; border-left: 4px solid #0073aa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f1f1f1; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .translation-demo { background: #e8f4fd; padding: 15px; border-radius: 6px; margin: 10px 0; }
        .original-text { background: #fff3cd; padding: 8px; border-radius: 4px; margin: 5px 0; }
        .translated-text { background: #d4edda; padding: 8px; border-radius: 4px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 ChatGABI Real-Time Translation System Test</h1>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Theme:</strong> <?php echo get_template(); ?></p>

        <?php
        $test_results = array();
        $total_tests = 0;
        $passed_tests = 0;

        // Test 1: Check if translation service is loaded
        echo '<div class="test-section">';
        echo '<h2>📁 Test 1: Translation Service Loading</h2>';
        
        $translation_file = get_template_directory() . '/inc/translation-service.php';
        
        if (file_exists($translation_file)) {
            echo '<p class="success">✅ Translation service file exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Translation service file missing</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 2: Check if translation functions are loaded
        echo '<div class="test-section">';
        echo '<h2>🔧 Test 2: Translation Function Availability</h2>';
        
        $required_functions = array(
            'chatgabi_init_translation_service',
            'chatgabi_smart_translate',
            'chatgabi_detect_message_language',
            'chatgabi_is_english_message',
            'chatgabi_google_translate',
            'chatgabi_get_cached_translation',
            'chatgabi_get_translation_analytics'
        );
        
        foreach ($required_functions as $function) {
            if (function_exists($function)) {
                echo "<p class=\"success\">✅ Function exists: $function</p>";
                $passed_tests++;
            } else {
                echo "<p class=\"error\">❌ Function missing: $function</p>";
            }
            $total_tests++;
        }
        echo '</div>';

        // Test 3: Database Tables
        echo '<div class="test-section">';
        echo '<h2>🗄️ Test 3: Translation Database Tables</h2>';
        
        global $wpdb;
        
        // Create tables if they don't exist
        if (function_exists('chatgabi_create_translation_tables')) {
            chatgabi_create_translation_tables();
        }
        
        $cache_table = $wpdb->prefix . 'chatgabi_translation_cache';
        $analytics_table = $wpdb->prefix . 'chatgabi_translation_analytics';
        $conversations_table = $wpdb->prefix . 'chatgabi_whatsapp_conversations';
        
        $cache_exists = $wpdb->get_var("SHOW TABLES LIKE '$cache_table'") === $cache_table;
        $analytics_exists = $wpdb->get_var("SHOW TABLES LIKE '$analytics_table'") === $analytics_table;
        $conversations_exists = $wpdb->get_var("SHOW TABLES LIKE '$conversations_table'") === $conversations_table;
        
        if ($cache_exists) {
            echo '<p class="success">✅ Translation cache table exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Translation cache table missing</p>';
        }
        $total_tests++;
        
        if ($analytics_exists) {
            echo '<p class="success">✅ Translation analytics table exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Translation analytics table missing</p>';
        }
        $total_tests++;
        
        if ($conversations_exists) {
            // Check if translation columns exist
            $columns = $wpdb->get_results("SHOW COLUMNS FROM $conversations_table LIKE 'original_message_language'");
            if (!empty($columns)) {
                echo '<p class="success">✅ WhatsApp conversations table has translation columns</p>';
                $passed_tests++;
            } else {
                echo '<p class="warning">⚠️ WhatsApp conversations table missing translation columns</p>';
            }
        } else {
            echo '<p class="error">❌ WhatsApp conversations table missing</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 4: Language Detection
        echo '<div class="test-section">';
        echo '<h2>🔍 Test 4: Language Detection</h2>';
        
        if (function_exists('chatgabi_is_english_message')) {
            $test_messages = array(
                'Hello, I need help with my business plan' => true,
                'Can you help me with market analysis?' => true,
                'Mɛpɛ sɛ meyɛ adwuma' => false, // Twi
                'Ninahitaji msaada na biashara yangu' => false, // Swahili
                'Mo nilo iranlowo fun iṣowo mi' => false, // Yoruba
                'Ngidinga usizo nebhizinisi lami' => false, // Zulu
            );
            
            foreach ($test_messages as $message => $expected_english) {
                $is_english = chatgabi_is_english_message($message);
                if ($is_english === $expected_english) {
                    echo "<p class=\"success\">✅ '$message' → " . ($is_english ? 'English' : 'Non-English') . " (correct)</p>";
                    $passed_tests++;
                } else {
                    echo "<p class=\"error\">❌ '$message' → " . ($is_english ? 'English' : 'Non-English') . " (expected " . ($expected_english ? 'English' : 'Non-English') . ")</p>";
                }
                $total_tests++;
            }
        } else {
            echo '<p class="error">❌ Language detection function not available</p>';
            $total_tests++;
        }
        echo '</div>';

        // Test 5: Translation Configuration
        echo '<div class="test-section">';
        echo '<h2>⚙️ Test 5: Translation Configuration</h2>';
        
        $translation_enabled = get_option('chatgabi_translation_enabled', 1);
        $google_api_key = get_option('chatgabi_google_translate_api_key');
        
        if ($translation_enabled) {
            echo '<p class="success">✅ Translation is enabled</p>';
            $passed_tests++;
        } else {
            echo '<p class="warning">⚠️ Translation is disabled</p>';
        }
        $total_tests++;
        
        if (!empty($google_api_key)) {
            echo '<p class="success">✅ Google Translate API key configured</p>';
            $passed_tests++;
        } else {
            echo '<p class="warning">⚠️ Google Translate API key not configured</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 6: Translation Service Status
        echo '<div class="test-section">';
        echo '<h2>🌐 Test 6: Translation Service Status</h2>';
        
        if (function_exists('chatgabi_get_translation_service_status')) {
            $status = chatgabi_get_translation_service_status();
            
            echo '<table>';
            echo '<tr><th>Service</th><th>Status</th><th>Details</th></tr>';
            
            foreach ($status as $service => $info) {
                if (is_array($info)) {
                    $status_class = $info['configured'] ? 'success' : 'warning';
                    $status_text = $info['configured'] ? 'Configured' : 'Not Configured';
                    echo "<tr><td>" . ucfirst(str_replace('_', ' ', $service)) . "</td><td class=\"$status_class\">$status_text</td><td>{$info['status']}</td></tr>";
                } else {
                    echo "<tr><td>" . ucfirst(str_replace('_', ' ', $service)) . "</td><td class=\"success\">$info</td><td>-</td></tr>";
                }
            }
            echo '</table>';
            
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Translation service status function not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 7: Mock Translation Test
        echo '<div class="test-section">';
        echo '<h2>🔄 Test 7: Mock Translation Test</h2>';
        
        if (function_exists('chatgabi_smart_translate')) {
            $test_messages = array(
                'Hello, I need business advice' => 'Ghana',
                'Mɛpɛ sɛ meyɛ adwuma' => 'Ghana', // Twi: "I want to do business"
                'Ninahitaji msaada wa biashara' => 'Kenya', // Swahili: "I need business help"
            );
            
            foreach ($test_messages as $message => $country) {
                echo "<div class=\"translation-demo\">";
                echo "<strong>Testing:</strong> $message ($country)<br>";
                
                $result = chatgabi_smart_translate($message, $country, 1);
                
                if ($result && !isset($result['error'])) {
                    echo "<div class=\"original-text\"><strong>Original:</strong> {$result['original_text']} ({$result['original_language']})</div>";
                    echo "<div class=\"translated-text\"><strong>Translated:</strong> {$result['translated_text']}</div>";
                    echo "<strong>Was Translated:</strong> " . ($result['was_translated'] ? 'Yes' : 'No') . "<br>";
                    echo "<strong>Confidence:</strong> " . number_format($result['confidence'] * 100, 1) . "%<br>";
                    echo "<strong>Processing Time:</strong> {$result['processing_time_ms']}ms<br>";
                    echo "<strong>Cache Hit:</strong> " . ($result['cache_hit'] ? 'Yes' : 'No') . "<br>";
                    
                    $passed_tests++;
                } else {
                    echo "<p class=\"error\">Translation failed: " . ($result['error'] ?? 'Unknown error') . "</p>";
                }
                echo "</div>";
                $total_tests++;
            }
        } else {
            echo '<p class="error">❌ Smart translate function not available</p>';
            $total_tests++;
        }
        echo '</div>';

        // Test 8: Integration with WhatsApp
        echo '<div class="test-section">';
        echo '<h2>📱 Test 8: WhatsApp Integration</h2>';
        
        // Check if WhatsApp functions have translation integration
        if (function_exists('chatgabi_handle_incoming_message')) {
            echo '<p class="success">✅ WhatsApp message handler available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp message handler not available</p>';
        }
        $total_tests++;
        
        if (function_exists('chatgabi_log_whatsapp_conversation_with_translation')) {
            echo '<p class="success">✅ WhatsApp translation logging available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp translation logging not available</p>';
        }
        $total_tests++;
        
        if (function_exists('chatgabi_update_whatsapp_user_language')) {
            echo '<p class="success">✅ WhatsApp user language update available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp user language update not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test Summary
        echo '<div class="test-section">';
        echo '<h2>📊 Test Summary</h2>';
        
        $success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Tests</td><td>$total_tests</td></tr>";
        echo "<tr><td>Passed Tests</td><td class=\"success\">$passed_tests</td></tr>";
        echo "<tr><td>Failed Tests</td><td class=\"error\">" . ($total_tests - $passed_tests) . "</td></tr>";
        echo "<tr><td>Success Rate</td><td class=\"" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error')) . "\">$success_rate%</td></tr>";
        echo "</table>";
        
        if ($success_rate >= 80) {
            echo '<p class="success">🎉 <strong>Translation system is ready for deployment!</strong></p>';
        } elseif ($success_rate >= 60) {
            echo '<p class="warning">⚠️ <strong>Translation system needs configuration before deployment.</strong></p>';
        } else {
            echo '<p class="error">❌ <strong>Translation system has critical issues that need to be resolved.</strong></p>';
        }
        echo '</div>';

        // Configuration Instructions
        echo '<div class="test-section">';
        echo '<h2>📝 Configuration Instructions</h2>';
        echo '<p>To complete the translation system setup:</p>';
        echo '<ol>';
        echo '<li>Go to <strong>ChatGABI → WhatsApp</strong> in the WordPress admin</li>';
        echo '<li>Enable translation in the Translation Settings section</li>';
        echo '<li>Configure your Google Cloud Translation API key</li>';
        echo '<li>Test with real African language messages</li>';
        echo '</ol>';
        
        echo '<div class="code">';
        echo '<strong>Supported Languages:</strong><br>';
        echo '• English (en) - Base language<br>';
        echo '• Twi (tw) - Ghana 🇬🇭<br>';
        echo '• Swahili (sw) - Kenya 🇰🇪<br>';
        echo '• Yoruba (yo) - Nigeria 🇳🇬<br>';
        echo '• Zulu (zu) - South Africa 🇿🇦';
        echo '</div>';
        echo '</div>';
        ?>
    </div>
</body>
</html>

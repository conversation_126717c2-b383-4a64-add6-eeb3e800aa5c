<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">

    <!-- PWA Manifest - Fallback (will be overridden by wp_head if PWA support is active) -->
    <link rel="manifest" href="<?php echo get_template_directory_uri(); ?>/manifest.json">
    <meta name="theme-color" content="#2563eb">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="site-header-inner">
                <div class="site-logo">
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                        <h1 class="swiftmind-logo"><?php _e('Swiftmind', 'chatgabi'); ?></h1>
                    </a>
                </div>

                <nav id="site-navigation" class="main-navigation">
                    <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                        <?php _e('Menu', 'chatgabi'); ?>
                    </button>
                    <ul>
                        <li><a href="<?php echo esc_url(home_url('/')); ?>"><?php _e('Home', 'chatgabi'); ?></a></li>
                        <?php if (is_user_logged_in()): ?>
                            <li><a href="<?php echo esc_url(home_url('/wizards')); ?>" class="wizards-nav-link"><?php _e('🧙‍♂️ Document Wizards', 'chatgabi'); ?></a></li>
                            <li><a href="<?php echo esc_url(home_url('/templates')); ?>" class="templates-nav-link"><?php _e('🚀 Business Templates', 'chatgabi'); ?></a></li>
                            <li><a href="<?php echo esc_url(home_url('/dashboard')); ?>"><?php _e('Dashboard', 'chatgabi'); ?></a></li>
                        <?php endif; ?>
                        <li><a href="#tools-dashboard"><?php _e('Tools', 'chatgabi'); ?></a></li>
                        <li><a href="#pricing"><?php _e('Pricing', 'chatgabi'); ?></a></li>
                        <li><a href="#help-center"><?php _e('Help Center', 'chatgabi'); ?></a></li>
                    </ul>
                </nav>

                <div class="header-user-info">
                    <button id="theme-toggle" class="theme-toggle" aria-label="<?php esc_attr_e('Toggle light/dark theme', 'chatgabi'); ?>">
                        <span class="theme-icon light-icon">🌞</span>
                        <span class="theme-icon dark-icon">🌙</span>
                    </button>
                    <?php if (is_user_logged_in()) : ?>
                        <?php $current_user = wp_get_current_user(); ?>
                        <?php $credits = get_user_meta($current_user->ID, 'chatgabi_credits', true); ?>
                        <span class="user-greeting"><?php printf(__('Hello, %s', 'chatgabi'), $current_user->display_name); ?></span>
                        <span class="user-credits"><?php printf(__('Credits: %d', 'chatgabi'), $credits ? intval($credits) : 0); ?></span>
                        <a href="<?php echo wp_logout_url(home_url()); ?>" class="logout-link"><?php _e('Logout', 'chatgabi'); ?></a>
                    <?php else : ?>
                        <a href="<?php echo wp_login_url(get_permalink()); ?>" class="login-link"><?php _e('Login', 'chatgabi'); ?></a>
                        <a href="<?php echo wp_registration_url(); ?>" class="register-link button primary-button"><?php _e('Register', 'chatgabi'); ?></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

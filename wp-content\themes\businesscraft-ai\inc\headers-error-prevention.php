<?php
/**
 * ChatGABI Headers Error Prevention
 * 
 * Prevents "headers already sent" errors by implementing proper
 * output buffering and error handling.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize output buffering for ChatGABI operations
 */
function chatgabi_init_output_buffering() {
    // Start output buffering if not already started
    if (!ob_get_level()) {
        ob_start();
    }
}

/**
 * Clean output buffer and handle any premature output
 */
function chatgabi_clean_output_buffer() {
    // Get any output that may have been generated
    $output = '';
    if (ob_get_level()) {
        $output = ob_get_contents();
        ob_end_clean();
    }
    
    // Log any unexpected output for debugging
    if (!empty($output) && defined('WP_DEBUG') && WP_DEBUG) {
        error_log('ChatGABI: Unexpected output detected: ' . substr($output, 0, 200));
    }
    
    return $output;
}

/**
 * Safe AJAX response wrapper
 */
function chatgabi_safe_ajax_response($success, $data = array()) {
    // Clean any output buffer
    chatgabi_clean_output_buffer();
    
    // Start fresh output buffering
    if (!ob_get_level()) {
        ob_start();
    }
    
    if ($success) {
        wp_send_json_success($data);
    } else {
        wp_send_json_error($data);
    }
}

/**
 * Wrap template generation functions with error handling
 */
function chatgabi_safe_template_operation($callback, $args = array()) {
    // Start output buffering
    chatgabi_init_output_buffering();
    
    // Suppress warnings temporarily
    $old_error_reporting = error_reporting(E_ERROR | E_PARSE);
    
    try {
        $result = call_user_func_array($callback, $args);
        
        // Restore error reporting
        error_reporting($old_error_reporting);
        
        return $result;
        
    } catch (Exception $e) {
        // Restore error reporting
        error_reporting($old_error_reporting);
        
        // Clean output buffer
        chatgabi_clean_output_buffer();
        
        // Log the error
        error_log('ChatGABI: Template operation failed: ' . $e->getMessage());
        
        return false;
    }
}

/**
 * Check for headers already sent and log details
 */
function chatgabi_check_headers_status() {
    if (headers_sent($file, $line)) {
        $message = "Headers already sent at {$file}:{$line}";
        error_log('ChatGABI: ' . $message);
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            // In debug mode, also check output buffer
            if (ob_get_level()) {
                $output = ob_get_contents();
                if (!empty($output)) {
                    error_log('ChatGABI: Output buffer contains: ' . substr($output, 0, 200));
                }
            }
        }
        
        return array(
            'sent' => true,
            'file' => $file,
            'line' => $line,
            'message' => $message
        );
    }
    
    return array('sent' => false);
}

/**
 * Enhanced AJAX handler wrapper
 */
function chatgabi_ajax_handler_wrapper($callback, $nonce_action = null) {
    // Initialize output buffering
    chatgabi_init_output_buffering();
    
    // Check headers status before processing
    $headers_status = chatgabi_check_headers_status();
    if ($headers_status['sent']) {
        chatgabi_safe_ajax_response(false, array(
            'message' => 'Headers already sent error detected',
            'debug' => $headers_status
        ));
        return;
    }
    
    // Verify nonce if provided
    if ($nonce_action && !wp_verify_nonce($_POST['nonce'] ?? '', $nonce_action)) {
        chatgabi_safe_ajax_response(false, array(
            'message' => 'Security check failed'
        ));
        return;
    }
    
    // Execute the callback safely
    try {
        $result = call_user_func($callback);
        
        // If callback didn't send JSON response, send success
        if ($result !== null) {
            chatgabi_safe_ajax_response(true, $result);
        }
        
    } catch (Exception $e) {
        chatgabi_safe_ajax_response(false, array(
            'message' => $e->getMessage()
        ));
    }
}

/**
 * Prevent headers errors during theme initialization
 */
function chatgabi_prevent_headers_errors() {
    // Start output buffering early
    if (!ob_get_level()) {
        ob_start();
    }
    
    // Set error handler for warnings
    set_error_handler(function($severity, $message, $file, $line) {
        // Don't output warnings that could cause headers errors
        if ($severity === E_WARNING || $severity === E_NOTICE) {
            error_log("ChatGABI Warning: {$message} in {$file}:{$line}");
            return true; // Don't execute PHP internal error handler
        }
        return false; // Execute PHP internal error handler for other errors
    });
}

/**
 * Clean up output buffering on shutdown
 */
function chatgabi_cleanup_output_buffering() {
    // Clean any remaining output buffers
    while (ob_get_level()) {
        $output = ob_get_contents();
        ob_end_clean();
        
        // Log any unexpected output
        if (!empty($output) && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('ChatGABI: Cleaned output buffer on shutdown: ' . substr($output, 0, 100));
        }
    }
}

/**
 * Enhanced template generation with error prevention
 */
function chatgabi_safe_generate_template($template_data, $form_data) {
    return chatgabi_safe_template_operation('chatgabi_build_template_generation_prompt', array($template_data, $form_data));
}

/**
 * Enhanced sector loading with error prevention
 */
function chatgabi_safe_get_sectors($country) {
    return chatgabi_safe_template_operation('get_available_sectors_by_country', array($country));
}

/**
 * Enhanced dataset loading with error prevention
 */
function chatgabi_safe_load_dataset($country) {
    return chatgabi_safe_template_operation('load_business_dataset_by_country', array($country));
}

/**
 * Initialize headers error prevention
 */
function chatgabi_init_headers_prevention() {
    // Prevent headers errors during initialization
    chatgabi_prevent_headers_errors();
    
    // Register cleanup on shutdown
    register_shutdown_function('chatgabi_cleanup_output_buffering');
}

// Initialize headers error prevention early
add_action('init', 'chatgabi_init_headers_prevention', 1);

// Clean up on admin_init as well
add_action('admin_init', 'chatgabi_init_output_buffering', 1);

// Clean up before sending any headers
add_action('send_headers', 'chatgabi_clean_output_buffer', 1);

/**
 * Debug function to check current output buffer status
 */
function chatgabi_debug_output_buffer() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        $level = ob_get_level();
        $status = ob_get_status(true);
        
        error_log("ChatGABI Debug: Output buffer level: {$level}");
        
        if (!empty($status)) {
            foreach ($status as $i => $buffer) {
                error_log("ChatGABI Debug: Buffer {$i}: " . json_encode($buffer));
            }
        }
        
        if (headers_sent($file, $line)) {
            error_log("ChatGABI Debug: Headers sent at {$file}:{$line}");
        } else {
            error_log("ChatGABI Debug: Headers not yet sent");
        }
    }
}

/**
 * Emergency headers fix function
 */
function chatgabi_emergency_headers_fix() {
    // Clean all output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Start fresh
    ob_start();
    
    // Suppress all output
    ini_set('display_errors', 0);
    
    error_log('ChatGABI: Emergency headers fix applied');
}

// Add emergency fix hook for critical situations
add_action('wp_loaded', function() {
    if (headers_sent() && defined('CHATGABI_EMERGENCY_HEADERS_FIX') && CHATGABI_EMERGENCY_HEADERS_FIX) {
        chatgabi_emergency_headers_fix();
    }
});

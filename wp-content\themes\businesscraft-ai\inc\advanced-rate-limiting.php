<?php
/**
 * Advanced Rate Limiting System for ChatGABI
 * Implements distributed rate limiting with Redis support and user tiers
 *
 * @package BusinessCraft_AI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessCraft_Advanced_Rate_Limiter {
    
    private $cache;
    private $rate_limits;
    private $default_limits;
    
    public function __construct() {
        $this->init_cache();
        $this->init_rate_limits();
    }
    
    /**
     * Initialize cache system
     */
    private function init_cache() {
        // Load Redis caching if available
        require_once get_template_directory() . '/inc/redis-caching.php';
        global $chatgabi_cache;
        $this->cache = $chatgabi_cache;
    }
    
    /**
     * Initialize rate limit configurations
     */
    private function init_rate_limits() {
        $this->rate_limits = array(
            'basic' => array(
                'requests_per_minute' => 10,
                'requests_per_hour' => 100,
                'requests_per_day' => 500,
                'tokens_per_hour' => 10000,
                'concurrent_requests' => 2
            ),
            'premium' => array(
                'requests_per_minute' => 20,
                'requests_per_hour' => 300,
                'requests_per_day' => 2000,
                'tokens_per_hour' => 50000,
                'concurrent_requests' => 5
            ),
            'ultra' => array(
                'requests_per_minute' => 50,
                'requests_per_hour' => 1000,
                'requests_per_day' => 10000,
                'tokens_per_hour' => 200000,
                'concurrent_requests' => 10
            ),
            'admin' => array(
                'requests_per_minute' => 100,
                'requests_per_hour' => 5000,
                'requests_per_day' => 50000,
                'tokens_per_hour' => 1000000,
                'concurrent_requests' => 20
            )
        );
        
        $this->default_limits = $this->rate_limits['basic'];
    }
    
    /**
     * Check if request is allowed
     */
    public function is_request_allowed($user_id, $operation = 'chat', $tokens_estimate = 0) {
        $user_tier = $this->get_user_tier($user_id);
        $limits = $this->rate_limits[$user_tier] ?? $this->default_limits;
        
        $ip_address = $this->get_client_ip();
        $checks = array();
        
        // Check requests per minute
        $minute_key = "rate_limit:user:{$user_id}:minute:" . floor(time() / 60);
        $minute_requests = $this->cache->get($minute_key) ?: 0;
        $checks['minute'] = array(
            'current' => $minute_requests,
            'limit' => $limits['requests_per_minute'],
            'allowed' => $minute_requests < $limits['requests_per_minute']
        );
        
        // Check requests per hour
        $hour_key = "rate_limit:user:{$user_id}:hour:" . floor(time() / 3600);
        $hour_requests = $this->cache->get($hour_key) ?: 0;
        $checks['hour'] = array(
            'current' => $hour_requests,
            'limit' => $limits['requests_per_hour'],
            'allowed' => $hour_requests < $limits['requests_per_hour']
        );
        
        // Check requests per day
        $day_key = "rate_limit:user:{$user_id}:day:" . floor(time() / 86400);
        $day_requests = $this->cache->get($day_key) ?: 0;
        $checks['day'] = array(
            'current' => $day_requests,
            'limit' => $limits['requests_per_day'],
            'allowed' => $day_requests < $limits['requests_per_day']
        );
        
        // Check tokens per hour
        $tokens_hour_key = "rate_limit:user:{$user_id}:tokens:hour:" . floor(time() / 3600);
        $hour_tokens = $this->cache->get($tokens_hour_key) ?: 0;
        $checks['tokens_hour'] = array(
            'current' => $hour_tokens,
            'limit' => $limits['tokens_per_hour'],
            'allowed' => ($hour_tokens + $tokens_estimate) <= $limits['tokens_per_hour']
        );
        
        // Check concurrent requests
        $concurrent_key = "rate_limit:user:{$user_id}:concurrent";
        $concurrent_requests = $this->cache->get($concurrent_key) ?: 0;
        $checks['concurrent'] = array(
            'current' => $concurrent_requests,
            'limit' => $limits['concurrent_requests'],
            'allowed' => $concurrent_requests < $limits['concurrent_requests']
        );
        
        // Check IP-based rate limiting (anti-abuse)
        $ip_minute_key = "rate_limit:ip:{$ip_address}:minute:" . floor(time() / 60);
        $ip_minute_requests = $this->cache->get($ip_minute_key) ?: 0;
        $checks['ip_minute'] = array(
            'current' => $ip_minute_requests,
            'limit' => 60, // Max 60 requests per minute per IP
            'allowed' => $ip_minute_requests < 60
        );
        
        // Determine overall result
        $overall_allowed = true;
        $blocking_reason = '';
        
        foreach ($checks as $check_type => $check) {
            if (!$check['allowed']) {
                $overall_allowed = false;
                $blocking_reason = $check_type;
                break;
            }
        }
        
        return array(
            'allowed' => $overall_allowed,
            'reason' => $blocking_reason,
            'checks' => $checks,
            'user_tier' => $user_tier,
            'retry_after' => $this->calculate_retry_after($blocking_reason)
        );
    }
    
    /**
     * Record a request
     */
    public function record_request($user_id, $operation = 'chat', $tokens_used = 0) {
        $ip_address = $this->get_client_ip();
        $current_time = time();
        
        // Increment user counters
        $minute_key = "rate_limit:user:{$user_id}:minute:" . floor($current_time / 60);
        $hour_key = "rate_limit:user:{$user_id}:hour:" . floor($current_time / 3600);
        $day_key = "rate_limit:user:{$user_id}:day:" . floor($current_time / 86400);
        $tokens_hour_key = "rate_limit:user:{$user_id}:tokens:hour:" . floor($current_time / 3600);
        $concurrent_key = "rate_limit:user:{$user_id}:concurrent";
        
        // Increment counters with appropriate TTL
        $this->cache->increment($minute_key);
        $this->cache->expire($minute_key, 60);
        
        $this->cache->increment($hour_key);
        $this->cache->expire($hour_key, 3600);
        
        $this->cache->increment($day_key);
        $this->cache->expire($day_key, 86400);
        
        if ($tokens_used > 0) {
            $this->cache->increment($tokens_hour_key, $tokens_used);
            $this->cache->expire($tokens_hour_key, 3600);
        }
        
        // Increment concurrent counter
        $this->cache->increment($concurrent_key);
        $this->cache->expire($concurrent_key, 300); // 5 minutes max for concurrent
        
        // Increment IP counters
        $ip_minute_key = "rate_limit:ip:{$ip_address}:minute:" . floor($current_time / 60);
        $this->cache->increment($ip_minute_key);
        $this->cache->expire($ip_minute_key, 60);
        
        // Log rate limit usage
        $this->log_rate_limit_usage($user_id, $operation, $tokens_used, $ip_address);
    }
    
    /**
     * Release concurrent request slot
     */
    public function release_concurrent_slot($user_id) {
        $concurrent_key = "rate_limit:user:{$user_id}:concurrent";
        $current = $this->cache->get($concurrent_key) ?: 0;
        
        if ($current > 0) {
            $this->cache->set($concurrent_key, $current - 1, 300);
        }
    }
    
    /**
     * Get user tier
     */
    private function get_user_tier($user_id) {
        if (!$user_id) {
            return 'basic';
        }
        
        // Check if user is admin
        if (user_can($user_id, 'manage_options')) {
            return 'admin';
        }
        
        // Get user tier from meta
        $tier = get_user_meta($user_id, 'businesscraft_ai_tier', true);
        return in_array($tier, array('basic', 'premium', 'ultra')) ? $tier : 'basic';
    }
    
    /**
     * Calculate retry after seconds
     */
    private function calculate_retry_after($blocking_reason) {
        switch ($blocking_reason) {
            case 'minute':
                return 60 - (time() % 60);
            case 'hour':
                return 3600 - (time() % 3600);
            case 'day':
                return 86400 - (time() % 86400);
            case 'tokens_hour':
                return 3600 - (time() % 3600);
            case 'concurrent':
                return 30; // Suggest retry in 30 seconds
            case 'ip_minute':
                return 60 - (time() % 60);
            default:
                return 60;
        }
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Log rate limit usage
     */
    private function log_rate_limit_usage($user_id, $operation, $tokens_used, $ip_address) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'businesscraft_ai_rate_limit_logs';
        
        $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'operation' => $operation,
                'tokens_used' => $tokens_used,
                'ip_address' => $ip_address,
                'timestamp' => current_time('mysql'),
                'user_tier' => $this->get_user_tier($user_id)
            ),
            array('%d', '%s', '%d', '%s', '%s', '%s')
        );
    }
    
    /**
     * Get rate limit status for user
     */
    public function get_rate_limit_status($user_id) {
        $user_tier = $this->get_user_tier($user_id);
        $limits = $this->rate_limits[$user_tier] ?? $this->default_limits;
        
        $current_time = time();
        
        return array(
            'user_tier' => $user_tier,
            'limits' => $limits,
            'current_usage' => array(
                'minute' => $this->cache->get("rate_limit:user:{$user_id}:minute:" . floor($current_time / 60)) ?: 0,
                'hour' => $this->cache->get("rate_limit:user:{$user_id}:hour:" . floor($current_time / 3600)) ?: 0,
                'day' => $this->cache->get("rate_limit:user:{$user_id}:day:" . floor($current_time / 86400)) ?: 0,
                'tokens_hour' => $this->cache->get("rate_limit:user:{$user_id}:tokens:hour:" . floor($current_time / 3600)) ?: 0,
                'concurrent' => $this->cache->get("rate_limit:user:{$user_id}:concurrent") ?: 0
            )
        );
    }
    
    /**
     * Reset rate limits for user (admin function)
     */
    public function reset_user_limits($user_id) {
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        $current_time = time();
        $keys_to_delete = array(
            "rate_limit:user:{$user_id}:minute:" . floor($current_time / 60),
            "rate_limit:user:{$user_id}:hour:" . floor($current_time / 3600),
            "rate_limit:user:{$user_id}:day:" . floor($current_time / 86400),
            "rate_limit:user:{$user_id}:tokens:hour:" . floor($current_time / 3600),
            "rate_limit:user:{$user_id}:concurrent"
        );
        
        foreach ($keys_to_delete as $key) {
            $this->cache->delete($key);
        }
        
        return true;
    }
    
    /**
     * Create rate limit logs table
     */
    public static function create_rate_limit_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'businesscraft_ai_rate_limit_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            operation varchar(50) NOT NULL,
            tokens_used int(11) DEFAULT 0,
            ip_address varchar(45) NOT NULL,
            timestamp datetime NOT NULL,
            user_tier varchar(20) NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY timestamp (timestamp),
            KEY ip_address (ip_address),
            KEY user_tier (user_tier)
        ) {$charset_collate};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize rate limiter
global $chatgabi_rate_limiter;
$chatgabi_rate_limiter = new BusinessCraft_Advanced_Rate_Limiter();

// Create table on activation
add_action('after_setup_theme', array('BusinessCraft_Advanced_Rate_Limiter', 'create_rate_limit_table'));

/**
 * Helper functions
 */
function chatgabi_check_rate_limit($user_id, $operation = 'chat', $tokens_estimate = 0) {
    global $chatgabi_rate_limiter;
    return $chatgabi_rate_limiter->is_request_allowed($user_id, $operation, $tokens_estimate);
}

function chatgabi_record_request($user_id, $operation = 'chat', $tokens_used = 0) {
    global $chatgabi_rate_limiter;
    return $chatgabi_rate_limiter->record_request($user_id, $operation, $tokens_used);
}

function chatgabi_release_concurrent_slot($user_id) {
    global $chatgabi_rate_limiter;
    return $chatgabi_rate_limiter->release_concurrent_slot($user_id);
}

function chatgabi_get_rate_limit_status($user_id) {
    global $chatgabi_rate_limiter;
    return $chatgabi_rate_limiter->get_rate_limit_status($user_id);
}

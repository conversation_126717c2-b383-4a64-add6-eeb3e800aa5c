/**
 * BusinessCraft AI - Opportunity Alerts Dashboard Styles
 * 
 * Custom styles for the opportunity alerts management interface
 */

/* Alert Dashboard Layout */
.alerts-dashboard {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.alerts-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 10px;
}

.alerts-header h1 {
    font-weight: 300;
    margin-bottom: 10px;
}

.alerts-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

/* Statistics Cards */
.stats-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stats-card .card-body {
    padding: 25px;
}

.stats-card .card-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card .card-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.stats-card i {
    opacity: 0.7;
}

/* Alert Table */
.alerts-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alerts-table .table {
    margin-bottom: 0;
}

.alerts-table .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
}

.alerts-table .table td {
    padding: 15px;
    vertical-align: middle;
}

.alerts-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Alert Status Toggle */
.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
}

.form-switch .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 10px 10px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px 30px;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-check {
    margin-bottom: 10px;
}

.form-check-input {
    margin-top: 0.25em;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

/* Preview Section */
#preview-section {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    background-color: #f8f9fa;
    padding: 20px;
    margin-top: 20px;
}

#preview-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.preview-opportunity {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: box-shadow 0.3s ease;
}

.preview-opportunity:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.preview-opportunity h6 {
    color: #2c3e50;
    margin-bottom: 8px;
}

.preview-opportunity .opportunity-meta {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.preview-opportunity .match-score {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 600;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h5 {
    color: #6c757d;
    margin-bottom: 10px;
}

.empty-state p {
    color: #adb5bd;
    margin-bottom: 30px;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert i {
    margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .alerts-header {
        padding: 20px 0;
        margin-bottom: 20px;
    }
    
    .stats-card .card-title {
        font-size: 2rem;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        padding: 15px 20px;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 5px;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .stats-card .card-body {
        padding: 20px;
    }
    
    .alerts-table .table th,
    .alerts-table .table td {
        padding: 10px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .alerts-dashboard {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .alerts-table {
        background: #2d3748;
    }
    
    .alerts-table .table th {
        background-color: #4a5568;
        color: #e9ecef;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .form-control, .form-select {
        background-color: #4a5568;
        border-color: #718096;
        color: #e9ecef;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #4a5568;
        border-color: #3498db;
        color: #e9ecef;
    }
}

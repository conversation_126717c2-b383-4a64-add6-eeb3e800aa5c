<?php
/**
 * AI Agent Network for Advanced Web Scraping
 * 
 * Specialized AI agents for discovery, analysis, verification, cleaning,
 * and structuring of scraped business intelligence data.
 *
 * @package ChatGABI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AI Agent Network Manager
 */
class ChatGABI_AI_Agent_Network {
    
    private $openai_api_key;
    private $agent_configs;
    private $performance_tracker;
    
    public function __construct() {
        $this->openai_api_key = get_option('businesscraft_ai_openai_api_key');
        $this->performance_tracker = array();
        $this->load_agent_configs();
    }
    
    /**
     * Load AI agent configurations
     */
    public function load_agent_configs() {
        $this->agent_configs = array(
            'discovery' => array(
                'model' => 'gpt-4',
                'temperature' => 0.3,
                'max_tokens' => 800,
                'system_prompt' => 'You are a specialized discovery agent for African business intelligence. Your role is to identify new authoritative data sources, emerging publishers, and reliable information channels across Ghana, Kenya, Nigeria, and South Africa.'
            ),
            'interest_analysis' => array(
                'model' => 'gpt-4',
                'temperature' => 0.2,
                'max_tokens' => 600,
                'system_prompt' => 'You are an interest analysis agent that monitors user query patterns to identify trending business sectors and prioritize data collection efforts based on user demand.'
            ),
            'verification' => array(
                'model' => 'gpt-4',
                'temperature' => 0.1,
                'max_tokens' => 500,
                'system_prompt' => 'You are a data verification agent responsible for cross-referencing information across multiple sources, identifying discrepancies, and assigning confidence scores to data points.'
            ),
            'cleaning' => array(
                'model' => 'gpt-4',
                'temperature' => 0.2,
                'max_tokens' => 700,
                'system_prompt' => 'You are a data cleaning agent that standardizes formats, removes duplicates, handles missing values, and ensures data consistency across different sources and formats.'
            ),
            'structuring' => array(
                'model' => 'gpt-4',
                'temperature' => 0.3,
                'max_tokens' => 900,
                'system_prompt' => 'You are a data structuring agent that converts unstructured scraped data into ChatGABI\'s standardized JSON schema while preserving accuracy and completeness.'
            )
        );
    }
    
    /**
     * Run discovery agents to find new data sources
     */
    public function run_discovery_agents() {
        $this->log_agent_activity('discovery_started', 'Starting discovery agent scan for new data sources');
        
        $countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
        $discovered_sources = array();
        
        foreach ($countries as $country) {
            $country_sources = $this->discover_country_sources($country);
            if (!empty($country_sources)) {
                $discovered_sources[$country] = $country_sources;
            }
        }
        
        // Validate discovered sources
        $validated_sources = $this->validate_discovered_sources($discovered_sources);
        
        $this->log_agent_activity('discovery_completed', 
            'Discovery agents found ' . count($validated_sources) . ' new sources');
        
        return $validated_sources;
    }
    
    /**
     * Discover new data sources for a specific country
     */
    private function discover_country_sources($country) {
        $discovery_prompt = $this->build_discovery_prompt($country);
        
        $response = $this->call_agent_api('discovery', $discovery_prompt);
        
        if (!$response) {
            return array();
        }
        
        return $this->parse_discovery_response($response, $country);
    }
    
    /**
     * Build discovery prompt for finding new sources
     */
    private function build_discovery_prompt($country) {
        $current_date = date('Y-m-d');
        
        $prompt = "Identify new authoritative data sources for business intelligence in {$country} as of {$current_date}.\n\n";
        
        $prompt .= "FOCUS AREAS:\n";
        $prompt .= "1. Government agencies and regulatory bodies\n";
        $prompt .= "2. Financial institutions and central banks\n";
        $prompt .= "3. Industry associations and trade organizations\n";
        $prompt .= "4. Academic institutions and research centers\n";
        $prompt .= "5. International development organizations\n";
        $prompt .= "6. Stock exchanges and commodity markets\n";
        $prompt .= "7. Business intelligence platforms\n";
        $prompt .= "8. News and media organizations with business focus\n\n";
        
        $prompt .= "REQUIREMENTS:\n";
        $prompt .= "- Sources must be authoritative and regularly updated\n";
        $prompt .= "- Include both English and local language sources\n";
        $prompt .= "- Focus on sources with structured data or regular reports\n";
        $prompt .= "- Prioritize sources with APIs or data feeds\n";
        $prompt .= "- Include emerging fintech, agriculture, energy, and technology sources\n\n";
        
        $prompt .= "OUTPUT FORMAT:\n";
        $prompt .= "Return a JSON array of sources with the following structure:\n";
        $prompt .= "{\n";
        $prompt .= "  \"sources\": [\n";
        $prompt .= "    {\n";
        $prompt .= "      \"name\": \"Source Name\",\n";
        $prompt .= "      \"url\": \"https://example.com\",\n";
        $prompt .= "      \"type\": \"government|financial|industry|academic|media\",\n";
        $prompt .= "      \"data_types\": [\"market_data\", \"regulatory\", \"investment\"],\n";
        $prompt .= "      \"update_frequency\": \"daily|weekly|monthly\",\n";
        $prompt .= "      \"scraping_method\": \"standard|javascript|ajax|api\",\n";
        $prompt .= "      \"reliability_score\": 1-10,\n";
        $prompt .= "      \"sectors_covered\": [\"fintech\", \"agriculture\", \"energy\"]\n";
        $prompt .= "    }\n";
        $prompt .= "  ]\n";
        $prompt .= "}\n\n";
        
        $prompt .= "Find 10-15 high-quality sources for {$country}.";
        
        return $prompt;
    }
    
    /**
     * Analyze user interests to prioritize sectors
     */
    public function analyze_user_interests() {
        $this->log_agent_activity('interest_analysis_started', 'Starting user interest analysis');
        
        // Get user query data from the last 30 days
        $query_data = $this->get_user_query_analytics();
        
        $analysis_prompt = $this->build_interest_analysis_prompt($query_data);
        
        $response = $this->call_agent_api('interest_analysis', $analysis_prompt);
        
        if (!$response) {
            return $this->get_default_priority_sectors();
        }
        
        $priority_sectors = $this->parse_interest_analysis($response);
        
        $this->log_agent_activity('interest_analysis_completed', 
            'Identified ' . count($priority_sectors) . ' priority sectors');
        
        return $priority_sectors;
    }
    
    /**
     * Get user query analytics for interest analysis
     */
    private function get_user_query_analytics() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_sector_logs';
        
        $query_data = $wpdb->get_results($wpdb->prepare(
            "SELECT country, detected_sector, COUNT(*) as query_count,
                    AVG(response_time) as avg_response_time,
                    MAX(timestamp) as last_query
             FROM {$table_name}
             WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             AND detected_sector IS NOT NULL
             GROUP BY country, detected_sector
             ORDER BY query_count DESC, last_query DESC
             LIMIT 50"
        ), ARRAY_A);
        
        return $query_data ?: array();
    }
    
    /**
     * Verify scraped data across multiple sources
     */
    public function verify_multi_source_data($scraped_data) {
        $this->log_agent_activity('verification_started', 'Starting multi-source data verification');
        
        $verified_data = array();
        
        foreach ($scraped_data as $country => $country_data) {
            foreach ($country_data as $sector => $sector_sources) {
                if (count($sector_sources) >= 3) { // Minimum 3 sources for verification
                    $verification_result = $this->verify_sector_data($country, $sector, $sector_sources);
                    
                    if ($verification_result) {
                        $verified_data[$country][$sector] = $verification_result;
                    }
                }
            }
        }
        
        $this->log_agent_activity('verification_completed', 
            'Verified data for ' . count($verified_data) . ' country-sector combinations');
        
        return $verified_data;
    }
    
    /**
     * Verify data for a specific sector across sources
     */
    private function verify_sector_data($country, $sector, $sources) {
        $verification_prompt = $this->build_verification_prompt($country, $sector, $sources);
        
        $response = $this->call_agent_api('verification', $verification_prompt);
        
        if (!$response) {
            return null;
        }
        
        return $this->parse_verification_response($response, $country, $sector);
    }
    
    /**
     * Clean and standardize scraped data
     */
    public function clean_scraped_data($raw_data) {
        $this->log_agent_activity('cleaning_started', 'Starting data cleaning process');
        
        $cleaned_data = array();
        
        foreach ($raw_data as $country => $country_data) {
            foreach ($country_data as $sector => $sector_data) {
                $cleaning_result = $this->clean_sector_data($country, $sector, $sector_data);
                
                if ($cleaning_result) {
                    $cleaned_data[$country][$sector] = $cleaning_result;
                }
            }
        }
        
        $this->log_agent_activity('cleaning_completed', 
            'Cleaned data for ' . count($cleaned_data) . ' country-sector combinations');
        
        return $cleaned_data;
    }
    
    /**
     * Structure scraped data into ChatGABI format
     */
    public function structure_scraped_data($verified_data) {
        $this->log_agent_activity('structuring_started', 'Starting data structuring process');
        
        $structured_data = array();
        
        foreach ($verified_data as $country => $country_data) {
            foreach ($country_data as $sector => $sector_data) {
                $structuring_result = $this->structure_sector_data($country, $sector, $sector_data);
                
                if ($structuring_result) {
                    $structured_data[$country][$sector] = $structuring_result;
                }
            }
        }
        
        $this->log_agent_activity('structuring_completed', 
            'Structured data for ' . count($structured_data) . ' country-sector combinations');
        
        return $structured_data;
    }
    
    /**
     * Call AI agent API with specific configuration
     */
    private function call_agent_api($agent_type, $prompt) {
        if (empty($this->openai_api_key)) {
            $this->log_agent_activity('api_key_missing', "OpenAI API key not configured for {$agent_type} agent");
            return null;
        }
        
        $config = $this->agent_configs[$agent_type];
        
        $url = 'https://api.openai.com/v1/chat/completions';
        
        $data = array(
            'model' => $config['model'],
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => $config['system_prompt']
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => $config['max_tokens'],
            'temperature' => $config['temperature']
        );
        
        $headers = array(
            'Authorization: Bearer ' . $this->openai_api_key,
            'Content-Type: application/json'
        );
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code !== 200) {
            $this->log_agent_activity('api_error', "OpenAI API returned HTTP {$http_code} for {$agent_type} agent");
            return null;
        }
        
        $decoded_response = json_decode($response, true);
        
        if (!$decoded_response || !isset($decoded_response['choices'][0]['message']['content'])) {
            $this->log_agent_activity('api_response_invalid', "Invalid response from OpenAI API for {$agent_type} agent");
            return null;
        }
        
        return $decoded_response['choices'][0]['message']['content'];
    }
    
    /**
     * Log AI agent activities
     */
    private function log_agent_activity($action, $message, $agent_type = null) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_ai_agent_logs';
        
        $wpdb->insert(
            $table_name,
            array(
                'agent_type' => $agent_type,
                'action' => $action,
                'message' => $message,
                'timestamp' => current_time('mysql'),
                'performance_data' => json_encode($this->performance_tracker)
            ),
            array('%s', '%s', '%s', '%s', '%s')
        );
        
        // Also log to WordPress error log for debugging
        error_log("ChatGABI AI Agent [{$agent_type}]: {$message}");
    }
    
    /**
     * Get default priority sectors when analysis fails
     */
    private function get_default_priority_sectors() {
        return array(
            'Fintech', 'Agriculture', 'Energy', 'Technology', 'Manufacturing',
            'Healthcare', 'Education', 'Tourism', 'Mining', 'Telecommunications'
        );
    }
}

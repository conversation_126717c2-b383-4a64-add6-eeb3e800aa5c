<?php
/**
 * Secure API Key Manager for BusinessCraft AI
 * Implements secure storage, rotation, and monitoring of API keys
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get encrypted API key with fallback security
 */
function businesscraft_ai_get_encrypted_api_key($key_type) {
    // First try to get from secure encrypted storage
    $encrypted_key = get_option("businesscraft_ai_encrypted_{$key_type}_key");
    
    if ($encrypted_key) {
        $decrypted_key = businesscraft_ai_decrypt($encrypted_key);
        if ($decrypted_key) {
            return $decrypted_key;
        }
    }
    
    // Fallback to legacy storage (for migration)
    $legacy_keys = array(
        'openai' => get_option('businesscraft_ai_openai_api_key'),
        'paystack_public' => get_option('businesscraft_ai_paystack_public_key'),
        'paystack_secret' => get_option('businesscraft_ai_paystack_secret_key')
    );
    
    if (isset($legacy_keys[$key_type]) && !empty($legacy_keys[$key_type])) {
        // Migrate to encrypted storage
        businesscraft_ai_store_encrypted_api_key($key_type, $legacy_keys[$key_type]);
        
        // Remove from legacy storage
        delete_option("businesscraft_ai_{$key_type}_api_key");
        
        return $legacy_keys[$key_type];
    }
    
    // Log security event
    businesscraft_ai_log_security_event('api_key_missing', array(
        'key_type' => $key_type,
        'timestamp' => current_time('mysql'),
        'user_id' => get_current_user_id()
    ));
    
    return false;
}

/**
 * Store API key with encryption
 */
function businesscraft_ai_store_encrypted_api_key($key_type, $api_key) {
    if (empty($api_key)) {
        return false;
    }
    
    // Validate API key format
    if (!businesscraft_ai_validate_api_key_format($key_type, $api_key)) {
        return false;
    }
    
    // Encrypt the API key
    $encrypted_key = businesscraft_ai_encrypt($api_key);
    
    if ($encrypted_key === false) {
        return false;
    }
    
    // Store encrypted key
    $result = update_option("businesscraft_ai_encrypted_{$key_type}_key", $encrypted_key);
    
    // Log security event
    businesscraft_ai_log_security_event('api_key_stored', array(
        'key_type' => $key_type,
        'timestamp' => current_time('mysql'),
        'user_id' => get_current_user_id(),
        'key_length' => strlen($api_key)
    ));
    
    return $result;
}

/**
 * Validate API key format
 */
function businesscraft_ai_validate_api_key_format($key_type, $api_key) {
    $patterns = array(
        'openai' => '/^sk-[a-zA-Z0-9\-_]{20,}$/',
        'paystack_public' => '/^pk_(test|live)_[a-zA-Z0-9]{32}$/',
        'paystack_secret' => '/^sk_(test|live)_[a-zA-Z0-9]{32}$/'
    );
    
    if (!isset($patterns[$key_type])) {
        return false;
    }
    
    return preg_match($patterns[$key_type], $api_key);
}

/**
 * Rotate API key
 */
function businesscraft_ai_rotate_api_key($key_type, $new_api_key) {
    // Validate new key
    if (!businesscraft_ai_validate_api_key_format($key_type, $new_api_key)) {
        return new WP_Error('invalid_key_format', 'Invalid API key format');
    }
    
    // Get old key for backup
    $old_key = businesscraft_ai_get_encrypted_api_key($key_type);
    
    // Store new key
    $result = businesscraft_ai_store_encrypted_api_key($key_type, $new_api_key);
    
    if ($result) {
        // Store old key as backup (encrypted)
        if ($old_key) {
            $backup_key = "businesscraft_ai_backup_{$key_type}_key_" . time();
            update_option($backup_key, businesscraft_ai_encrypt($old_key));
            
            // Schedule cleanup of old backup keys (keep only last 3)
            wp_schedule_single_event(time() + 3600, 'businesscraft_ai_cleanup_backup_keys', array($key_type));
        }
        
        // Log rotation event
        businesscraft_ai_log_security_event('api_key_rotated', array(
            'key_type' => $key_type,
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'old_key_length' => $old_key ? strlen($old_key) : 0,
            'new_key_length' => strlen($new_api_key)
        ));
        
        return true;
    }
    
    return new WP_Error('rotation_failed', 'Failed to rotate API key');
}

/**
 * Monitor API key usage
 */
function businesscraft_ai_monitor_api_key_usage($key_type, $operation, $success = true) {
    if (!defined('BUSINESSCRAFT_AI_API_MONITORING_ENABLED') || !BUSINESSCRAFT_AI_API_MONITORING_ENABLED) {
        return;
    }
    
    $usage_data = array(
        'key_type' => $key_type,
        'operation' => $operation,
        'success' => $success,
        'timestamp' => current_time('mysql'),
        'user_id' => get_current_user_id(),
        'ip_address' => businesscraft_ai_get_client_ip(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    );
    
    // Store in database for analysis
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_api_usage_logs';
    
    $wpdb->insert(
        $table_name,
        $usage_data,
        array('%s', '%s', '%d', '%s', '%d', '%s', '%s')
    );
    
    // Check for suspicious activity
    businesscraft_ai_check_suspicious_api_activity($key_type, $usage_data);
}

/**
 * Check for suspicious API activity
 */
function businesscraft_ai_check_suspicious_api_activity($key_type, $usage_data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_api_usage_logs';
    
    // Check for rapid requests from same IP
    $recent_requests = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$table_name} 
         WHERE key_type = %s 
         AND ip_address = %s 
         AND timestamp > DATE_SUB(NOW(), INTERVAL 1 MINUTE)",
        $key_type,
        $usage_data['ip_address']
    ));
    
    if ($recent_requests > 60) { // More than 60 requests per minute
        businesscraft_ai_log_security_event('suspicious_api_activity', array(
            'key_type' => $key_type,
            'ip_address' => $usage_data['ip_address'],
            'request_count' => $recent_requests,
            'timestamp' => current_time('mysql')
        ));
        
        // Optionally trigger rate limiting or alerts
        do_action('businesscraft_ai_suspicious_activity_detected', $key_type, $usage_data);
    }
}

/**
 * Log security events
 */
function businesscraft_ai_log_security_event($event_type, $event_data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_security_logs';
    
    $log_entry = array(
        'event_type' => $event_type,
        'event_data' => json_encode($event_data),
        'timestamp' => current_time('mysql'),
        'user_id' => get_current_user_id(),
        'ip_address' => businesscraft_ai_get_client_ip()
    );
    
    $wpdb->insert(
        $table_name,
        $log_entry,
        array('%s', '%s', '%s', '%d', '%s')
    );
}

/**
 * Get client IP address
 * Enhanced version with comprehensive IP header checking and security features
 */
if (!function_exists('businesscraft_ai_get_client_ip')) {
    function businesscraft_ai_get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}

/**
 * Create security tables
 */
function businesscraft_ai_create_security_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // API usage logs table
    $usage_logs_table = $wpdb->prefix . 'businesscraft_ai_api_usage_logs';
    $usage_logs_sql = "CREATE TABLE IF NOT EXISTS {$usage_logs_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        key_type varchar(50) NOT NULL,
        operation varchar(100) NOT NULL,
        success tinyint(1) NOT NULL DEFAULT 1,
        timestamp datetime NOT NULL,
        user_id bigint(20) DEFAULT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        PRIMARY KEY (id),
        KEY key_type (key_type),
        KEY timestamp (timestamp),
        KEY ip_address (ip_address),
        KEY user_id (user_id)
    ) {$charset_collate};";
    
    // Security logs table
    $security_logs_table = $wpdb->prefix . 'businesscraft_ai_security_logs';
    $security_logs_sql = "CREATE TABLE IF NOT EXISTS {$security_logs_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        event_type varchar(50) NOT NULL,
        event_data longtext,
        timestamp datetime NOT NULL,
        user_id bigint(20) DEFAULT NULL,
        ip_address varchar(45) NOT NULL,
        PRIMARY KEY (id),
        KEY event_type (event_type),
        KEY timestamp (timestamp),
        KEY user_id (user_id)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($usage_logs_sql);
    dbDelta($security_logs_sql);
}

// Initialize security tables on activation
add_action('after_setup_theme', 'businesscraft_ai_create_security_tables');

/**
 * Cleanup old backup keys (keep only last 3)
 */
add_action('businesscraft_ai_cleanup_backup_keys', function($key_type) {
    global $wpdb;
    
    $backup_keys = $wpdb->get_results($wpdb->prepare(
        "SELECT option_name FROM {$wpdb->options} 
         WHERE option_name LIKE %s 
         ORDER BY option_name DESC",
        "businesscraft_ai_backup_{$key_type}_key_%"
    ));
    
    if (count($backup_keys) > 3) {
        $keys_to_delete = array_slice($backup_keys, 3);
        foreach ($keys_to_delete as $key) {
            delete_option($key->option_name);
        }
    }
});

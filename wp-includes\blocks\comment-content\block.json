{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/comment-content", "title": "Comment Content", "category": "theme", "ancestor": ["core/comment-template"], "description": "Displays the contents of a comment.", "textdomain": "default", "usesContext": ["commentId"], "attributes": {"textAlign": {"type": "string"}}, "supports": {"color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}, "spacing": {"padding": ["horizontal", "vertical"], "__experimentalDefaultControls": {"padding": true}}, "html": false}, "style": "wp-block-comment-content"}
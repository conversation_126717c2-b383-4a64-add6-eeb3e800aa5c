<?php
/**
 * Investigate broken South Africa JSON files
 */

// Define WordPress constants for testing
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

echo "=== South Africa JSON Investigation ===\n\n";

$dataset_dir = WP_CONTENT_DIR . '/datasets/south-africa-business-data/';
$broken_files = [
    'south_africa_business_data.json',
    'south_africa_business_data_v2.json',
    'south_africa_business_datav4.json'
];

foreach ($broken_files as $filename) {
    $file_path = $dataset_dir . $filename;

    echo "🔍 Investigating: {$filename}\n";
    echo str_repeat('-', 60) . "\n";

    if (!file_exists($file_path)) {
        echo "❌ File not found\n\n";
        continue;
    }

    $content = file_get_contents($file_path);
    $file_size = strlen($content);

    echo "📄 File size: {$file_size} bytes\n";

    // Check for common issues
    $issues = [];

    // 1. Check for BOM
    if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
        $issues[] = "BOM detected";
    }

    // 2. Check for control characters
    $control_chars = 0;
    for ($i = 0; $i < strlen($content); $i++) {
        $char = ord($content[$i]);
        if ($char < 32 && $char != 9 && $char != 10 && $char != 13) {
            $control_chars++;
        }
    }
    if ($control_chars > 0) {
        $issues[] = "{$control_chars} control characters";
    }

    // 3. Check for JavaScript comments
    if (strpos($content, '//') !== false) {
        $comment_count = substr_count($content, '//');
        $issues[] = "{$comment_count} JavaScript comments (//)";
    }

    // 4. Check for trailing commas
    if (preg_match('/,\s*[}\]]/', $content)) {
        $issues[] = "Possible trailing commas";
    }

    // 5. Check first and last few characters
    $first_50 = substr($content, 0, 50);
    $last_50 = substr($content, -50);

    echo "🔍 Issues found: " . (empty($issues) ? "None detected" : implode(', ', $issues)) . "\n";
    echo "📝 First 50 chars: " . json_encode($first_50) . "\n";
    echo "📝 Last 50 chars: " . json_encode($last_50) . "\n";

    // Try to decode and get specific error
    $decoded = json_decode($content, true);
    $json_error = json_last_error();
    $json_error_msg = json_last_error_msg();

    echo "❌ JSON Error: {$json_error_msg}\n";

    // Try to find the error location
    if ($json_error !== JSON_ERROR_NONE) {
        // Split into lines and check each section
        $lines = explode("\n", $content);
        echo "📊 Total lines: " . count($lines) . "\n";

        // Show first 10 lines
        echo "📄 First 10 lines:\n";
        for ($i = 0; $i < min(10, count($lines)); $i++) {
            $line = $lines[$i];
            $line_preview = strlen($line) > 80 ? substr($line, 0, 80) . '...' : $line;
            echo "   " . ($i + 1) . ": " . $line_preview . "\n";
        }

        // Check for specific patterns that cause issues
        $problematic_lines = [];
        foreach ($lines as $line_num => $line) {
            // Check for unescaped quotes
            if (preg_match('/[^\\\\]"[^"]*[^\\\\]"[^"]*"/', $line)) {
                $problematic_lines[] = ($line_num + 1) . ": Unescaped quotes";
            }
            // Check for smart quotes using hex codes
            if (preg_match('/[\xE2\x80\x9C\xE2\x80\x9D\xE2\x80\x98\xE2\x80\x99]/u', $line)) {
                $problematic_lines[] = ($line_num + 1) . ": Smart quotes";
            }
            // Check for trailing commas
            if (preg_match('/,\s*[}\]]/', $line)) {
                $problematic_lines[] = ($line_num + 1) . ": Trailing comma";
            }
        }

        if (!empty($problematic_lines)) {
            echo "🚨 Problematic lines (first 5):\n";
            foreach (array_slice($problematic_lines, 0, 5) as $issue) {
                echo "   - Line {$issue}\n";
            }
        }

        // Try a quick fix attempt
        echo "\n🔧 Attempting quick fix...\n";
        $fixed_content = $content;

        // Remove BOM
        $fixed_content = preg_replace('/^\xEF\xBB\xBF/', '', $fixed_content);

        // Remove JavaScript comments
        $fixed_content = preg_replace('/\/\/.*$/m', '', $fixed_content);

        // Remove trailing commas
        $fixed_content = preg_replace('/,(\s*[}\]])/', '$1', $fixed_content);

        // Replace smart quotes using hex codes
        $fixed_content = str_replace(["\xE2\x80\x9C", "\xE2\x80\x9D"], '"', $fixed_content);
        $fixed_content = str_replace(["\xE2\x80\x98", "\xE2\x80\x99"], "'", $fixed_content);

        // Test the fix
        $test_decode = json_decode($fixed_content, true);
        $test_error = json_last_error();

        if ($test_error === JSON_ERROR_NONE) {
            echo "✅ Quick fix successful!\n";

            // Save the fixed version
            $fixed_filename = str_replace('.json', '.fixed.json', $filename);
            $fixed_path = $dataset_dir . $fixed_filename;
            file_put_contents($fixed_path, $fixed_content);
            echo "💾 Fixed version saved as: {$fixed_filename}\n";

            if (isset($test_decode['sectors'])) {
                echo "📊 Sectors found: " . count($test_decode['sectors']) . "\n";
            }
        } else {
            echo "❌ Quick fix failed: " . json_last_error_msg() . "\n";
        }
    }

    echo "\n";
}

echo "=== Investigation Complete ===\n";

<?php
/**
 * Debug Templates Page
 * 
 * Simple debug version to test templates functionality
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

// Get user data for context-aware features
$user_id = get_current_user_id();
$user_profile = get_user_meta($user_id, 'bcai_profile_type', true) ?: 'sme';
$user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
$user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
$user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
$user_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;

// Get template categories
$template_categories = function_exists('chatgabi_get_template_categories') ? 
    chatgabi_get_template_categories() : array();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Templates Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .templates-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .template-card { background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
        .template-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; }
        .template-description { color: #666; margin-bottom: 10px; }
        .template-meta { font-size: 12px; color: #888; }
        .loading { text-align: center; padding: 40px; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .filters { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .filters select, .filters input { margin: 5px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

<h1>🐛 Debug Templates Page</h1>

<div class="debug-info">
    <h3>Debug Information</h3>
    <p><strong>User ID:</strong> <?php echo $user_id; ?></p>
    <p><strong>User Profile:</strong> <?php echo $user_profile; ?></p>
    <p><strong>User Country:</strong> <?php echo $user_country; ?></p>
    <p><strong>User Credits:</strong> <?php echo $user_credits; ?></p>
    <p><strong>Categories Found:</strong> <?php echo count($template_categories); ?></p>
    <p><strong>REST URL:</strong> <?php echo rest_url('chatgabi/v1/'); ?></p>
    <p><strong>Logged In:</strong> <?php echo is_user_logged_in() ? 'Yes' : 'No'; ?></p>
</div>

<?php if (!empty($template_categories)): ?>
<div class="debug-info">
    <h3>Available Categories</h3>
    <?php foreach ($template_categories as $category): ?>
        <p>- <?php echo esc_html($category->name); ?> (<?php echo esc_html($category->slug); ?>)</p>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<div class="filters">
    <h3>Filters</h3>
    <select id="category-filter">
        <option value="">All Categories</option>
        <?php foreach ($template_categories as $category): ?>
            <option value="<?php echo esc_attr($category->slug); ?>">
                <?php echo esc_html($category->name); ?>
            </option>
        <?php endforeach; ?>
    </select>
    
    <input type="text" id="template-search" placeholder="Search templates..." />
    
    <button onclick="loadTemplatesDebug()">Load Templates</button>
    <button onclick="testAPI()">Test API</button>
    <button onclick="clearResults()">Clear</button>
</div>

<div id="debug-output" class="debug-info">
    <h3>Debug Output</h3>
    <div id="debug-messages"></div>
</div>

<div id="templates-container">
    <div id="templates-loading" class="loading" style="display: none;">
        <p>Loading templates...</p>
    </div>
    
    <div id="templates-grid" class="templates-grid">
        <!-- Templates will be loaded here -->
    </div>
</div>

<script>
// Configuration
const debugConfig = {
    restUrl: '<?php echo rest_url('chatgabi/v1/'); ?>',
    restNonce: '<?php echo wp_create_nonce('wp_rest'); ?>',
    userId: <?php echo $user_id; ?>,
    userCredits: <?php echo $user_credits; ?>
};

function debugLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    $('#debug-messages').append('<p>[' + timestamp + '] ' + message + '</p>');
    console.log('[DEBUG] ' + message);
}

function showLoading() {
    $('#templates-loading').show();
    $('#templates-grid').empty();
}

function hideLoading() {
    $('#templates-loading').hide();
}

function clearResults() {
    $('#templates-grid').empty();
    $('#debug-messages').empty();
    hideLoading();
}

function testAPI() {
    debugLog('Testing API endpoints...');
    
    // Test templates endpoint
    $.ajax({
        url: debugConfig.restUrl + 'templates',
        method: 'GET',
        headers: {
            'X-WP-Nonce': debugConfig.restNonce
        },
        success: function(data) {
            debugLog('✅ Templates API Success: ' + JSON.stringify(data).substring(0, 100) + '...');
        },
        error: function(xhr, status, error) {
            debugLog('❌ Templates API Error: ' + error + ' (Status: ' + xhr.status + ')');
        }
    });
    
    // Test categories endpoint
    $.ajax({
        url: debugConfig.restUrl + 'template-categories',
        method: 'GET',
        success: function(data) {
            debugLog('✅ Categories API Success: ' + JSON.stringify(data).substring(0, 100) + '...');
        },
        error: function(xhr, status, error) {
            debugLog('❌ Categories API Error: ' + error + ' (Status: ' + xhr.status + ')');
        }
    });
}

function loadTemplatesDebug() {
    debugLog('Loading templates...');
    showLoading();
    
    const category = $('#category-filter').val();
    const search = $('#template-search').val();
    
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (search) params.append('search', search);
    
    const url = debugConfig.restUrl + 'templates?' + params.toString();
    debugLog('Request URL: ' + url);
    
    $.ajax({
        url: url,
        method: 'GET',
        headers: {
            'X-WP-Nonce': debugConfig.restNonce,
            'Content-Type': 'application/json'
        },
        success: function(data) {
            hideLoading();
            debugLog('✅ Templates loaded successfully');
            debugLog('Response: ' + JSON.stringify(data, null, 2));
            
            if (data.success && data.templates) {
                renderTemplates(data.templates);
                debugLog('Rendered ' + data.templates.length + ' templates');
            } else {
                debugLog('❌ No templates in response or success=false');
                $('#templates-grid').html('<div class="error">No templates found or API error</div>');
            }
        },
        error: function(xhr, status, error) {
            hideLoading();
            debugLog('❌ AJAX Error: ' + error);
            debugLog('Status: ' + xhr.status + ' - ' + xhr.statusText);
            debugLog('Response: ' + xhr.responseText);
            
            $('#templates-grid').html('<div class="error">Error loading templates: ' + error + '</div>');
        }
    });
}

function renderTemplates(templates) {
    const grid = $('#templates-grid');
    
    if (templates.length === 0) {
        grid.html('<div class="error">No templates found matching your criteria.</div>');
        return;
    }
    
    let html = '';
    templates.forEach(function(template) {
        html += renderTemplateCard(template);
    });
    
    grid.html(html);
}

function renderTemplateCard(template) {
    const category = template.category || { name: 'General', icon: '📋' };
    
    return `
        <div class="template-card">
            <div class="template-title">${template.title}</div>
            <div class="template-description">${template.description || 'No description'}</div>
            <div class="template-meta">
                Category: ${category.name} | 
                Language: ${getLanguageName(template.language_code || 'en')} | 
                Used: ${template.usage_count || 0} times
            </div>
        </div>
    `;
}

function getLanguageName(code) {
    const languages = {
        'en': 'English',
        'tw': 'Twi',
        'sw': 'Swahili',
        'yo': 'Yoruba',
        'zu': 'Zulu'
    };
    return languages[code] || 'English';
}

// Auto-load on page ready
$(document).ready(function() {
    debugLog('Page loaded, testing configuration...');
    debugLog('REST URL: ' + debugConfig.restUrl);
    debugLog('User ID: ' + debugConfig.userId);
    debugLog('Nonce: ' + debugConfig.restNonce.substring(0, 10) + '...');
    
    // Auto-test API
    setTimeout(testAPI, 1000);
    
    // Auto-load templates
    setTimeout(loadTemplatesDebug, 2000);
});

// Filter change handlers
$('#category-filter').change(function() {
    debugLog('Category filter changed to: ' + $(this).val());
});

$('#template-search').on('input', function() {
    debugLog('Search changed to: ' + $(this).val());
});
</script>

</body>
</html>

<?php
/**
 * Template Name: Document Creation Wizards
 * 
 * AI-powered step-by-step document creation wizards
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

get_header();

// Check if user is logged in
if (!is_user_logged_in()) {
    ?>
    <div class="login-required">
        <div class="container">
            <div class="login-prompt">
                <h2><?php _e('Login Required', 'businesscraft-ai'); ?></h2>
                <p><?php _e('Please log in to access the AI-powered document creation wizards.', 'businesscraft-ai'); ?></p>
                <a href="<?php echo wp_login_url(get_permalink()); ?>" class="btn-primary">
                    <?php _e('Login', 'businesscraft-ai'); ?>
                </a>
                <a href="<?php echo wp_registration_url(); ?>" class="btn-secondary">
                    <?php _e('Register', 'businesscraft-ai'); ?>
                </a>
            </div>
        </div>
    </div>
    <?php
    get_footer();
    return;
}

// Get wizard configurations
$wizard_configs = array(
    'business-plan' => businesscraft_ai_get_wizard_config('business-plan'),
    'marketing-strategy' => businesscraft_ai_get_wizard_config('marketing-strategy'),
    'financial-forecast' => businesscraft_ai_get_wizard_config('financial-forecast')
);
?>

<div class="wizards-page">
    <div class="container">
        <div class="wizard-container">
            
            <!-- Wizard Selection Screen -->
            <div class="wizard-selection">
                <h1><?php _e('🧙‍♂️ AI Document Creation Wizards', 'businesscraft-ai'); ?></h1>
                <p class="subtitle">
                    <?php _e('Create professional business documents with step-by-step AI guidance', 'businesscraft-ai'); ?>
                </p>
                
                <div class="wizards-grid">
                    <?php foreach ($wizard_configs as $wizard_type => $config): ?>
                        <div class="wizard-card" data-wizard-type="<?php echo esc_attr($wizard_type); ?>">
                            <span class="wizard-icon"><?php echo $config['icon']; ?></span>
                            <h3><?php echo esc_html($config['name']); ?></h3>
                            <p><?php echo esc_html($config['description']); ?></p>
                            
                            <div class="wizard-meta">
                                <span class="estimated-time">
                                    ⏱️ <?php echo esc_html($config['estimated_time']); ?>
                                </span>
                                <span class="step-count">
                                    📋 <?php echo count($config['steps']); ?> <?php _e('Steps', 'businesscraft-ai'); ?>
                                </span>
                            </div>
                            
                            <button class="start-wizard-btn" data-wizard-type="<?php echo esc_attr($wizard_type); ?>">
                                🚀 <?php _e('Start Wizard', 'businesscraft-ai'); ?>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="wizard-features">
                    <h3><?php _e('✨ What Makes Our Wizards Special', 'businesscraft-ai'); ?></h3>
                    <div class="features-grid">
                        <div class="feature-item">
                            <div class="feature-icon">🤖</div>
                            <h4><?php _e('AI-Powered Assistance', 'businesscraft-ai'); ?></h4>
                            <p><?php _e('Get intelligent suggestions and content generation at every step', 'businesscraft-ai'); ?></p>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🌍</div>
                            <h4><?php _e('African Market Focus', 'businesscraft-ai'); ?></h4>
                            <p><?php _e('Tailored for Ghana, Kenya, Nigeria, and South Africa markets', 'businesscraft-ai'); ?></p>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">💾</div>
                            <h4><?php _e('Auto-Save Progress', 'businesscraft-ai'); ?></h4>
                            <p><?php _e('Never lose your work with automatic progress saving', 'businesscraft-ai'); ?></p>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📄</div>
                            <h4><?php _e('Professional Export', 'businesscraft-ai'); ?></h4>
                            <p><?php _e('Export to PDF, Word, or share with your team', 'businesscraft-ai'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Wizard Interface (Hidden Initially) -->
            <div class="wizard-interface">
                
                <!-- Wizard Header -->
                <div class="wizard-header">
                    <h2 class="wizard-title"></h2>
                    <p class="wizard-description"></p>
                    <div class="wizard-meta">
                        <span class="wizard-estimated-time"></span>
                        <div class="auto-save-indicator"></div>
                    </div>
                </div>
                
                <!-- Progress Indicator -->
                <div class="wizard-progress">
                    <!-- Progress steps will be generated dynamically -->
                </div>
                
                <!-- Step Content -->
                <div class="wizard-step-content">
                    <!-- Step content will be loaded dynamically -->
                </div>
                
                <!-- Navigation -->
                <div class="wizard-navigation">
                    <div class="wizard-nav-left">
                        <button class="wizard-prev-btn" style="display: none;">
                            ← <?php _e('Previous Step', 'businesscraft-ai'); ?>
                        </button>
                        <button class="save-progress-btn">
                            💾 <?php _e('Save Progress', 'businesscraft-ai'); ?>
                        </button>
                    </div>
                    
                    <div class="wizard-nav-right">
                        <button class="exit-wizard-btn">
                            ❌ <?php _e('Exit Wizard', 'businesscraft-ai'); ?>
                        </button>
                        <button class="wizard-next-btn">
                            <?php _e('Next Step', 'businesscraft-ai'); ?> →
                        </button>
                        <button class="wizard-complete-btn" style="display: none;">
                            ✅ <?php _e('Complete Document', 'businesscraft-ai'); ?>
                        </button>
                    </div>
                </div>
                
            </div>
            
        </div>
    </div>
</div>

<style>
/* Additional page-specific styles */
.wizards-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 40px 0;
}

.wizard-features {
    margin-top: 60px;
    text-align: center;
}

.wizard-features h3 {
    color: #3D4E81;
    margin-bottom: 40px;
    font-size: 1.8em;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.feature-item {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.feature-item h4 {
    color: #3D4E81;
    margin: 0 0 10px 0;
    font-size: 1.2em;
}

.feature-item p {
    color: #666;
    margin: 0;
    line-height: 1.6;
}

.login-required {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-prompt {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
}

.login-prompt h2 {
    color: #3D4E81;
    margin: 0 0 15px 0;
}

.login-prompt p {
    color: #666;
    margin: 0 0 25px 0;
    line-height: 1.6;
}

.login-prompt .btn-primary,
.login-prompt .btn-secondary {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.login-prompt .btn-primary {
    background: linear-gradient(to right, #3D4E81, #5753C9, #6E7FF3);
    color: white;
}

.login-prompt .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(61, 78, 129, 0.3);
    color: white;
    text-decoration: none;
}

.login-prompt .btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.login-prompt .btn-secondary:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .wizards-page {
        padding: 20px 0;
    }
    
    .wizard-container {
        margin: 10px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .feature-item {
        padding: 20px;
    }
    
    .login-prompt {
        margin: 20px;
        padding: 30px;
    }
}
</style>

<script>
// Page-specific JavaScript initialization
jQuery(document).ready(function($) {
    // Check for resume wizard parameter
    const urlParams = new URLSearchParams(window.location.search);
    const resumeWizard = urlParams.get('resume_wizard');
    
    if (resumeWizard) {
        // Auto-start wizard resumption
        if (typeof BusinessCraftDocumentWizards !== 'undefined') {
            BusinessCraftDocumentWizards.loadExistingSession();
        }
    }
    
    // Add click handlers for wizard cards
    $('.wizard-card').on('click', function() {
        const wizardType = $(this).data('wizard-type');
        const $btn = $(this).find('.start-wizard-btn');
        $btn.trigger('click');
    });
    
    // Smooth scrolling for better UX
    $('html, body').animate({
        scrollTop: 0
    }, 500);
});
</script>

<?php get_footer(); ?>

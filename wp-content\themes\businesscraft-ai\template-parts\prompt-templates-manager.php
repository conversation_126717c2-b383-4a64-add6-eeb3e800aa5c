<?php
/**
 * Prompt Templates Manager Interface
 * 
 * Provides a comprehensive interface for managing saved prompt templates
 * including creation, editing, categorization, and sharing.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$user_id = get_current_user_id();
$categories = chatgabi_get_template_categories(true);
?>

<div class="chatgabi-templates-manager" id="templates-manager">
    <!-- Header Section -->
    <div class="templates-header">
        <div class="header-content">
            <h1 class="templates-title">
                <span class="title-icon">📝</span>
                <?php _e('Prompt Templates', 'chatgabi'); ?>
            </h1>
            <p class="templates-description">
                <?php _e('Create, organize, and share your AI prompt templates for better productivity.', 'chatgabi'); ?>
            </p>
        </div>
        <div class="header-actions">
            <button class="btn-primary" onclick="openTemplateModal()">
                <span class="btn-icon">➕</span>
                <?php _e('New Template', 'chatgabi'); ?>
            </button>
            <button class="btn-secondary" onclick="togglePublicTemplates()">
                <span class="btn-icon">🌐</span>
                <span id="public-toggle-text"><?php _e('Browse Public', 'chatgabi'); ?></span>
            </button>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="templates-filters">
        <div class="filter-group">
            <label for="category-filter"><?php _e('Category:', 'chatgabi'); ?></label>
            <select id="category-filter" onchange="filterTemplates()">
                <option value=""><?php _e('All Categories', 'chatgabi'); ?></option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?php echo esc_attr($category->id); ?>">
                        <?php echo esc_html($category->icon . ' ' . $category->name); ?>
                        (<?php echo intval($category->template_count ?? 0); ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="filter-group">
            <label for="search-templates"><?php _e('Search:', 'chatgabi'); ?></label>
            <input type="text" id="search-templates" placeholder="<?php _e('Search templates...', 'chatgabi'); ?>" onkeyup="debounceSearch()">
        </div>

        <div class="filter-group">
            <label for="sort-templates"><?php _e('Sort by:', 'chatgabi'); ?></label>
            <select id="sort-templates" onchange="filterTemplates()">
                <option value="updated_at-DESC"><?php _e('Recently Updated', 'chatgabi'); ?></option>
                <option value="created_at-DESC"><?php _e('Recently Created', 'chatgabi'); ?></option>
                <option value="usage_count-DESC"><?php _e('Most Used', 'chatgabi'); ?></option>
                <option value="rating_average-DESC"><?php _e('Highest Rated', 'chatgabi'); ?></option>
                <option value="title-ASC"><?php _e('Name A-Z', 'chatgabi'); ?></option>
            </select>
        </div>
    </div>

    <!-- Templates Grid -->
    <div class="templates-grid" id="templates-grid">
        <div class="loading-spinner" id="templates-loading">
            <div class="spinner"></div>
            <p><?php _e('Loading templates...', 'chatgabi'); ?></p>
        </div>
    </div>

    <!-- Load More Button -->
    <div class="load-more-section" id="load-more-section" style="display: none;">
        <button class="btn-secondary" id="load-more-btn" onclick="loadMoreTemplates()">
            <?php _e('Load More Templates', 'chatgabi'); ?>
        </button>
    </div>

    <!-- Template Modal -->
    <div class="template-modal" id="template-modal" style="display: none;">
        <div class="modal-overlay" onclick="closeTemplateModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title"><?php _e('Create New Template', 'chatgabi'); ?></h2>
                <button class="modal-close" onclick="closeTemplateModal()">&times;</button>
            </div>

            <form id="template-form" class="modal-body">
                <input type="hidden" id="template-id" value="">
                
                <div class="form-group">
                    <label for="template-title"><?php _e('Template Title', 'chatgabi'); ?> *</label>
                    <input type="text" id="template-title" required maxlength="255" 
                           placeholder="<?php _e('Enter a descriptive title...', 'chatgabi'); ?>">
                </div>

                <div class="form-group">
                    <label for="template-description"><?php _e('Description', 'chatgabi'); ?></label>
                    <textarea id="template-description" rows="3" maxlength="500"
                              placeholder="<?php _e('Describe what this template is for...', 'chatgabi'); ?>"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="template-category"><?php _e('Category', 'chatgabi'); ?></label>
                        <select id="template-category">
                            <option value=""><?php _e('Select Category', 'chatgabi'); ?></option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo esc_attr($category->id); ?>">
                                    <?php echo esc_html($category->icon . ' ' . $category->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="template-language"><?php _e('Language', 'chatgabi'); ?></label>
                        <select id="template-language">
                            <option value="en">🇺🇸 English</option>
                            <option value="tw">🇬🇭 Twi</option>
                            <option value="sw">🇰🇪 Swahili</option>
                            <option value="yo">🇳🇬 Yoruba</option>
                            <option value="zu">🇿🇦 Zulu</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="template-country"><?php _e('Target Country', 'chatgabi'); ?></label>
                        <select id="template-country">
                            <option value=""><?php _e('Any Country', 'chatgabi'); ?></option>
                            <option value="GH">🇬🇭 Ghana</option>
                            <option value="KE">🇰🇪 Kenya</option>
                            <option value="NG">🇳🇬 Nigeria</option>
                            <option value="ZA">🇿🇦 South Africa</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="template-sector"><?php _e('Business Sector', 'chatgabi'); ?></label>
                        <input type="text" id="template-sector" maxlength="100"
                               placeholder="<?php _e('e.g., Technology, Agriculture...', 'chatgabi'); ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="template-tags"><?php _e('Tags', 'chatgabi'); ?></label>
                    <input type="text" id="template-tags" maxlength="200"
                           placeholder="<?php _e('business plan, startup, marketing (comma separated)', 'chatgabi'); ?>">
                </div>

                <div class="form-group">
                    <label for="template-content"><?php _e('Prompt Content', 'chatgabi'); ?> *</label>
                    <textarea id="template-content" rows="8" required
                              placeholder="<?php _e('Enter your prompt template here. Use {variables} for dynamic content...', 'chatgabi'); ?>"></textarea>
                    <div class="form-help">
                        <?php _e('Tip: Use {business_name}, {industry}, {country} as placeholders for dynamic content.', 'chatgabi'); ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="template-public">
                        <span class="checkmark"></span>
                        <?php _e('Make this template public (others can use it)', 'chatgabi'); ?>
                    </label>
                </div>
            </form>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeTemplateModal()">
                    <?php _e('Cancel', 'chatgabi'); ?>
                </button>
                <button type="button" class="btn-primary" onclick="saveTemplate()">
                    <span class="btn-text"><?php _e('Save Template', 'chatgabi'); ?></span>
                    <span class="btn-loading" style="display: none;"><?php _e('Saving...', 'chatgabi'); ?></span>
                </button>
            </div>
        </div>
    </div>

    <!-- Template Preview Modal -->
    <div class="template-preview-modal" id="preview-modal" style="display: none;">
        <div class="modal-overlay" onclick="closePreviewModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="preview-title"></h2>
                <div class="preview-actions">
                    <button class="btn-icon" onclick="useTemplate()" title="<?php _e('Use Template', 'chatgabi'); ?>">
                        ▶️
                    </button>
                    <button class="btn-icon" onclick="editTemplate()" title="<?php _e('Edit Template', 'chatgabi'); ?>">
                        ✏️
                    </button>
                    <button class="modal-close" onclick="closePreviewModal()">&times;</button>
                </div>
            </div>

            <div class="modal-body">
                <div class="template-meta">
                    <span class="meta-item" id="preview-category"></span>
                    <span class="meta-item" id="preview-author"></span>
                    <span class="meta-item" id="preview-usage"></span>
                    <span class="meta-item" id="preview-rating"></span>
                </div>
                
                <div class="template-description" id="preview-description"></div>
                
                <div class="template-content">
                    <h4><?php _e('Prompt Content:', 'chatgabi'); ?></h4>
                    <pre id="preview-content"></pre>
                </div>

                <div class="template-tags" id="preview-tags"></div>
            </div>

            <div class="modal-footer">
                <div class="rating-section">
                    <span><?php _e('Rate this template:', 'chatgabi'); ?></span>
                    <div class="star-rating" id="star-rating">
                        <span class="star" data-rating="1">⭐</span>
                        <span class="star" data-rating="2">⭐</span>
                        <span class="star" data-rating="3">⭐</span>
                        <span class="star" data-rating="4">⭐</span>
                        <span class="star" data-rating="5">⭐</span>
                    </div>
                </div>
                
                <button class="btn-primary" onclick="useTemplate()">
                    <?php _e('Use This Template', 'chatgabi'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Messages Container -->
    <div id="templates-messages" class="templates-messages"></div>
</div>

<script>
// Global variables for template management
window.chatgabiTemplates = {
    currentView: 'my', // 'my' or 'public'
    currentPage: 0,
    hasMore: true,
    isLoading: false,
    searchTimeout: null,
    currentTemplate: null
};

// Initialize templates manager
jQuery(document).ready(function() {
    initializeTemplatesManager();
});
</script>

/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},d:(t,i)=>{for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{MediaUpload:()=>m,privateApis:()=>T,transformAttachment:()=>w,uploadMedia:()=>_,validateFileSize:()=>S,validateMimeType:()=>g,validateMimeTypeForUser:()=>b});const i=window.wp.element,o=window.wp.i18n,a=[],r=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Select.extend({featuredImageToolbar(t){this.createSelectToolbar(t,{text:e.media.view.l10n.setFeaturedImage,state:this.options.state})},editState(){const t=this.state("featured-image").get("selection"),i=new e.media.view.EditImage({model:t.single(),controller:this}).render();this.content.set(i),i.loadEditor()},createStates:function(){this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new e.media.controller.FeaturedImage,new e.media.controller.EditImage({model:this.options.editImage})])}})},s=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Select.extend({createStates(){const t=this.options;this.options.states||this.states.add([new e.media.controller.Library({library:e.media.query(t.library),multiple:t.multiple,title:t.title,priority:20,filterable:"uploaded"}),new e.media.controller.EditImage({model:t.editImage})])}})},n=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Post.extend({galleryToolbar(){const t=this.state().get("editing");this.toolbar.set(new e.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:t?e.media.view.l10n.updateGallery:e.media.view.l10n.insertGallery,priority:80,requires:{library:!0},click(){const e=this.controller,t=e.state();e.close(),t.trigger("update",t.get("library")),e.setState(e.options.state),e.reset()}}}}))},editState(){const t=this.state("gallery").get("selection"),i=new e.media.view.EditImage({model:t.single(),controller:this}).render();this.content.set(i),i.loadEditor()},createStates:function(){this.on("toolbar:create:main-gallery",this.galleryToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new e.media.controller.Library({id:"gallery",title:e.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:e.media.query({type:"image",...this.options.library})}),new e.media.controller.EditImage({model:this.options.editImage}),new e.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new e.media.controller.GalleryAdd])}})},l=e=>["sizes","mime","type","subtype","id","url","alt","link","caption"].reduce(((t,i)=>(e?.hasOwnProperty(i)&&(t[i]=e[i]),t)),{}),d=e=>{const{wp:t}=window;return t.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"})};class p extends i.Component{constructor(){super(...arguments),this.openModal=this.openModal.bind(this),this.onOpen=this.onOpen.bind(this),this.onSelect=this.onSelect.bind(this),this.onUpdate=this.onUpdate.bind(this),this.onClose=this.onClose.bind(this)}initializeListeners(){this.frame.on("select",this.onSelect),this.frame.on("update",this.onUpdate),this.frame.on("open",this.onOpen),this.frame.on("close",this.onClose)}buildAndSetGalleryFrame(){const{addToGallery:e=!1,allowedTypes:t,multiple:i=!1,value:o=a}=this.props;if(o===this.lastGalleryValue)return;const{wp:r}=window;let s;this.lastGalleryValue=o,this.frame&&this.frame.remove(),s=e?"gallery-library":o&&o.length?"gallery-edit":"gallery",this.GalleryDetailsMediaFrame||(this.GalleryDetailsMediaFrame=n());const l=d(o),p=new r.media.model.Selection(l.models,{props:l.props.toJSON(),multiple:i});this.frame=new this.GalleryDetailsMediaFrame({mimeType:t,state:s,multiple:i,selection:p,editing:!!o?.length}),r.media.frame=this.frame,this.initializeListeners()}buildAndSetFeatureImageFrame(){const{wp:e}=window,{value:t,multiple:i,allowedTypes:o}=this.props,a=r(),s=d(t),n=new e.media.model.Selection(s.models,{props:s.props.toJSON()});this.frame=new a({mimeType:o,state:"featured-image",multiple:i,selection:n,editing:t}),e.media.frame=this.frame,e.media.view.settings.post={...e.media.view.settings.post,featuredImageId:t||-1}}buildAndSetSingleMediaFrame(){const{wp:e}=window,{allowedTypes:t,multiple:i=!1,title:a=(0,o.__)("Select or Upload Media"),value:r}=this.props,n={title:a,multiple:i};t&&(n.library={type:t}),this.frame&&this.frame.remove();const l=s(),p=d(r),m=new e.media.model.Selection(p.models,{props:p.props.toJSON()});this.frame=new l({mimeType:t,multiple:i,selection:m,...n}),e.media.frame=this.frame}componentWillUnmount(){this.frame?.remove()}onUpdate(e){const{onSelect:t,multiple:i=!1}=this.props,o=this.frame.state(),a=e||o.get("selection");a&&a.models.length&&t(i?a.models.map((e=>l(e.toJSON()))):l(a.models[0].toJSON()))}onSelect(){const{onSelect:e,multiple:t=!1}=this.props,i=this.frame.state().get("selection").toJSON();e(t?i:i[0])}onOpen(){const{wp:e}=window,{value:t}=this.props;this.updateCollection(),this.props.mode&&this.frame.content.mode(this.props.mode);if(!(Array.isArray(t)?!!t?.length:!!t))return;const i=this.props.gallery,o=this.frame.state().get("selection"),a=Array.isArray(t)?t:[t];i||a.forEach((t=>{o.add(e.media.attachment(t))}));const r=d(a);r.more().done((function(){i&&r?.models?.length&&o.add(r.models)}))}onClose(){const{onClose:e}=this.props;e&&e(),this.frame.detach()}updateCollection(){const e=this.frame.content.get();if(e&&e.collection){const t=e.collection;t.toArray().forEach((e=>e.trigger("destroy",e))),t.mirroring._hasMore=!0,t.more()}}openModal(){const{gallery:e=!1,unstableFeaturedImageFlow:t=!1,modalClass:i}=this.props;e?this.buildAndSetGalleryFrame():this.buildAndSetSingleMediaFrame(),i&&this.frame.$el.addClass(i),t&&this.buildAndSetFeatureImageFrame(),this.initializeListeners(),this.frame.open()}render(){return this.props.render({open:this.openModal})}}const m=p,c=window.wp.blob,h=window.wp.apiFetch;var u=e.n(h);function f(e,t,i){if(function(e){return null!==e&&"object"==typeof e&&Object.getPrototypeOf(e)===Object.prototype}(i))for(const[o,a]of Object.entries(i))f(e,`${t}[${o}]`,a);else void 0!==i&&e.append(t,String(i))}function w(e){var t;const{alt_text:i,source_url:o,...a}=e;return{...a,alt:e.alt_text,caption:null!==(t=e.caption?.raw)&&void 0!==t?t:"",title:e.title.raw,url:e.source_url,poster:e._embedded?.["wp:featuredmedia"]?.[0]?.source_url||void 0}}class y extends Error{constructor({code:e,message:t,file:i,cause:o}){super(t,{cause:o}),Object.setPrototypeOf(this,new.target.prototype),this.code=e,this.file=i}}function g(e,t){if(!t)return;const i=t.some((t=>t.includes("/")?t===e.type:e.type.startsWith(`${t}/`)));if(e.type&&!i)throw new y({code:"MIME_TYPE_NOT_SUPPORTED",message:(0,o.sprintf)((0,o.__)("%s: Sorry, this file type is not supported here."),e.name),file:e})}function b(e,t){const i=(a=t)?Object.entries(a).flatMap((([e,t])=>{const[i]=t.split("/");return[t,...e.split("|").map((e=>`${i}/${e}`))]})):null;var a;if(!i)return;const r=i.includes(e.type);if(e.type&&!r)throw new y({code:"MIME_TYPE_NOT_ALLOWED_FOR_USER",message:(0,o.sprintf)((0,o.__)("%s: Sorry, you are not allowed to upload this file type."),e.name),file:e})}function S(e,t){if(e.size<=0)throw new y({code:"EMPTY_FILE",message:(0,o.sprintf)((0,o.__)("%s: This file is empty."),e.name),file:e});if(t&&e.size>t)throw new y({code:"SIZE_ABOVE_LIMIT",message:(0,o.sprintf)((0,o.__)("%s: This file exceeds the maximum upload size for this site."),e.name),file:e})}function _({wpAllowedMimeTypes:e,allowedTypes:t,additionalData:i={},filesList:a,maxUploadFileSize:r,onError:s,onFileChange:n,signal:l,multiple:d=!0}){if(!d&&a.length>1)return void s?.(new Error((0,o.__)("Only one file can be used here.")));const p=[],m=[],h=(e,t)=>{window.__experimentalMediaProcessing||m[e]?.url&&(0,c.revokeBlobURL)(m[e].url),m[e]=t,n?.(m.filter((e=>null!==e)))};for(const i of a){try{b(i,e)}catch(e){s?.(e);continue}try{g(i,t)}catch(e){s?.(e);continue}try{S(i,r)}catch(e){s?.(e);continue}p.push(i),window.__experimentalMediaProcessing||(m.push({url:(0,c.createBlobURL)(i)}),n?.(m))}p.map((async(e,t)=>{try{const o=await async function(e,t={},i){const o=new FormData;o.append("file",e,e.name||e.type.replace("/","."));for(const[e,i]of Object.entries(t))f(o,e,i);return w(await u()({path:"/wp/v2/media?_embed=wp:featuredmedia",body:o,method:"POST",signal:i}))}(e,i,l);h(t,o)}catch(i){let a;h(t,null),a="object"==typeof i&&null!==i&&"message"in i?"string"==typeof i.message?i.message:String(i.message):(0,o.sprintf)((0,o.__)("Error while uploading file %s to the media library."),e.name),s?.(new y({code:"GENERAL",message:a,file:e,cause:i instanceof Error?i:void 0}))}}))}const v=()=>{};const O=window.wp.privateApis,{lock:E,unlock:M}=(0,O.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/media-utils"),T={};E(T,{sideloadMedia:async function({file:e,attachmentId:t,additionalData:i={},signal:a,onFileChange:r,onError:s=v}){try{const o=await async function(e,t,i={},o){const a=new FormData;a.append("file",e,e.name||e.type.replace("/","."));for(const[e,t]of Object.entries(i))f(a,e,t);return w(await u()({path:`/wp/v2/media/${t}/sideload`,body:a,method:"POST",signal:o}))}(e,t,i,a);r?.([o])}catch(t){let i;i=t instanceof Error?t.message:(0,o.sprintf)((0,o.__)("Error while sideloading file %s to the server."),e.name),s(new y({code:"GENERAL",message:i,file:e,cause:t instanceof Error?t:void 0}))}}}),(window.wp=window.wp||{}).mediaUtils=t})();
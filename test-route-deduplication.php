<?php
/**
 * Test Route Deduplication
 * 
 * Comprehensive test to verify that all duplicate REST API routes
 * have been successfully removed from the ChatGABI system.
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Route Deduplication</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        .route-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .route-table th, .route-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .route-table th { background-color: #f2f2f2; }
        .duplicate { background-color: #fff3cd; }
        .clean { background-color: #d4edda; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

<h1>🧪 Test Route Deduplication</h1>

<?php
echo '<div class="info">Route deduplication test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

$all_tests_passed = true;

// Test 1: Check for Duplicate Route Handlers
echo '<h2>🔍 Test 1: Check for Duplicate Route Handlers</h2>';

try {
    // Get all registered routes
    $rest_routes = rest_get_server()->get_routes();
    $chatgabi_routes = array();
    $duplicate_routes = array();
    $clean_routes = array();
    
    foreach ($rest_routes as $route => $handlers) {
        if (strpos($route, '/chatgabi/v1/') === 0) {
            $handler_count = count($handlers);
            $chatgabi_routes[] = array(
                'route' => $route,
                'handlers' => $handler_count,
                'is_duplicate' => $handler_count > 1
            );
            
            if ($handler_count > 1) {
                $duplicate_routes[] = $route;
            } else {
                $clean_routes[] = $route;
            }
        }
    }
    
    echo '<table class="route-table">';
    echo '<thead><tr><th>Route</th><th>Handlers</th><th>Status</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($chatgabi_routes as $route_info) {
        $class = $route_info['is_duplicate'] ? 'duplicate' : 'clean';
        $status = $route_info['is_duplicate'] ? '❌ DUPLICATE' : '✅ CLEAN';
        
        echo '<tr class="' . $class . '">';
        echo '<td>' . $route_info['route'] . '</td>';
        echo '<td>' . $route_info['handlers'] . '</td>';
        echo '<td>' . $status . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    
    if (empty($duplicate_routes)) {
        echo '<div class="success">✅ No duplicate route handlers found! All routes have single handlers.</div>';
    } else {
        echo '<div class="error">❌ Found ' . count($duplicate_routes) . ' routes with duplicate handlers:</div>';
        echo '<ul>';
        foreach ($duplicate_routes as $route) {
            echo '<li>' . $route . '</li>';
        }
        echo '</ul>';
        $all_tests_passed = false;
    }
    
    echo '<div class="info">Total ChatGABI routes: ' . count($chatgabi_routes) . ' | Clean: ' . count($clean_routes) . ' | Duplicates: ' . count($duplicate_routes) . '</div>';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Route handler check error: ' . $e->getMessage() . '</div>';
    $all_tests_passed = false;
}

// Test 2: Verify Registration Hook Status
echo '<h2>🔗 Test 2: Verify Registration Hook Status</h2>';

try {
    global $wp_filter;
    
    echo '<div class="info">Checking rest_api_init hooks...</div>';
    
    $rest_api_hooks = array();
    if (isset($wp_filter['rest_api_init'])) {
        foreach ($wp_filter['rest_api_init']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                $function_name = '';
                if (is_array($callback['function'])) {
                    if (is_object($callback['function'][0])) {
                        $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                    } else {
                        $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                    }
                } elseif (is_string($callback['function'])) {
                    $function_name = $callback['function'];
                }
                
                if (strpos($function_name, 'chatgabi') !== false) {
                    $rest_api_hooks[] = array(
                        'function' => $function_name,
                        'priority' => $priority
                    );
                }
            }
        }
    }
    
    echo '<table class="route-table">';
    echo '<thead><tr><th>Function</th><th>Priority</th><th>Status</th></tr></thead>';
    echo '<tbody>';
    
    $expected_hooks = array(
        'chatgabi_register_rest_routes' => 'rest-api.php',
        'chatgabi_register_opportunities_api' => 'opportunity-api.php',
        'chatgabi_register_whatsapp_routes' => 'whatsapp-integration.php',
        'chatgabi_ensure_rest_routes' => 'functions.php'
    );
    
    foreach ($expected_hooks as $expected_function => $file) {
        $found = false;
        $priority = 'N/A';
        
        foreach ($rest_api_hooks as $hook) {
            if ($hook['function'] === $expected_function) {
                $found = true;
                $priority = $hook['priority'];
                break;
            }
        }
        
        $status = $found ? '✅ REGISTERED' : '❌ MISSING';
        $class = $found ? 'clean' : 'duplicate';
        
        echo '<tr class="' . $class . '">';
        echo '<td>' . $expected_function . ' (' . $file . ')</td>';
        echo '<td>' . $priority . '</td>';
        echo '<td>' . $status . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    
    // Check for unexpected hooks
    $unexpected_hooks = array();
    foreach ($rest_api_hooks as $hook) {
        if (!array_key_exists($hook['function'], $expected_hooks)) {
            $unexpected_hooks[] = $hook['function'];
        }
    }
    
    if (!empty($unexpected_hooks)) {
        echo '<div class="warning">⚠️ Unexpected ChatGABI hooks found:</div>';
        echo '<ul>';
        foreach ($unexpected_hooks as $hook) {
            echo '<li>' . $hook . '</li>';
        }
        echo '</ul>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Hook verification error: ' . $e->getMessage() . '</div>';
    $all_tests_passed = false;
}

// Test 3: Test All Route Endpoints
echo '<h2>🌐 Test 3: Test All Route Endpoints</h2>';

try {
    echo '<div class="info">Testing endpoint accessibility...</div>';
    
    $test_endpoints = array(
        'Templates' => rest_url('chatgabi/v1/templates'),
        'Template Categories' => rest_url('chatgabi/v1/template-categories'),
        'Opportunities' => rest_url('chatgabi/v1/opportunities'),
        'Opportunity Stats' => rest_url('chatgabi/v1/opportunities/stats'),
        'Opportunity Types' => rest_url('chatgabi/v1/opportunities/types'),
        'Opportunity Sectors' => rest_url('chatgabi/v1/opportunities/sectors'),
        'Opportunity Countries' => rest_url('chatgabi/v1/opportunities/countries'),
        'Health Check' => rest_url('chatgabi/v1/health'),
        'Credits' => rest_url('chatgabi/v1/credits'),
    );
    
    echo '<table class="route-table">';
    echo '<thead><tr><th>Endpoint</th><th>URL</th><th>Status</th><th>Response</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($test_endpoints as $name => $url) {
        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'headers' => array('User-Agent' => 'ChatGABI-Dedup-Test/1.0')
        ));
        
        if (!is_wp_error($response)) {
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);
            
            if ($status_code === 200) {
                $class = 'clean';
                $status = '✅ ' . $status_code;
                $response_info = 'OK';
                
                // Try to decode JSON response
                $data = json_decode($body, true);
                if ($data && isset($data['success'])) {
                    $response_info = $data['success'] ? 'Success' : 'Failed';
                }
            } else {
                $class = 'duplicate';
                $status = '⚠️ ' . $status_code;
                $response_info = 'Error';
                if ($status_code !== 401 && $status_code !== 403) {
                    $all_tests_passed = false;
                }
            }
        } else {
            $class = 'duplicate';
            $status = '❌ Error';
            $response_info = $response->get_error_message();
            $all_tests_passed = false;
        }
        
        echo '<tr class="' . $class . '">';
        echo '<td>' . $name . '</td>';
        echo '<td><small>' . $url . '</small></td>';
        echo '<td>' . $status . '</td>';
        echo '<td>' . $response_info . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Endpoint testing error: ' . $e->getMessage() . '</div>';
    $all_tests_passed = false;
}

// Test 4: Live Frontend Test
echo '<h2>🌐 Test 4: Live Frontend API Test</h2>';
?>

<div class="info">
    <strong>Live API Test Results:</strong>
    <div id="live-test-results">
        <p>Testing frontend API connectivity...</p>
    </div>
</div>

<script>
// Live frontend test
$(document).ready(function() {
    const restUrl = '<?php echo rest_url('chatgabi/v1/'); ?>';
    let testResults = '';
    let testsCompleted = 0;
    const totalTests = 3;
    
    function updateResults() {
        $('#live-test-results').html(testResults);
    }
    
    function checkCompletion() {
        testsCompleted++;
        if (testsCompleted === totalTests) {
            testResults += '<div class="info">✅ All frontend tests completed</div>';
            updateResults();
        }
    }
    
    // Test templates endpoint
    $.ajax({
        url: restUrl + 'templates',
        method: 'GET',
        timeout: 15000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Templates API: Working</div>';
            testResults += '<div class="info">Templates loaded: ' + (data.templates ? data.templates.length : 0) + '</div>';
            updateResults();
            checkCompletion();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Templates API Error: ' + error + '</div>';
            updateResults();
            checkCompletion();
        }
    });
    
    // Test opportunities endpoint
    $.ajax({
        url: restUrl + 'opportunities',
        method: 'GET',
        timeout: 15000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Opportunities API: Working</div>';
            testResults += '<div class="info">Opportunities loaded: ' + (data.opportunities ? data.opportunities.length : 0) + '</div>';
            updateResults();
            checkCompletion();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Opportunities API Error: ' + error + '</div>';
            updateResults();
            checkCompletion();
        }
    });
    
    // Test health endpoint
    $.ajax({
        url: restUrl + 'health',
        method: 'GET',
        timeout: 10000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Health API: Working</div>';
            testResults += '<div class="info">Service: ' + (data.service || 'Unknown') + '</div>';
            updateResults();
            checkCompletion();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Health API Error: ' + error + '</div>';
            updateResults();
            checkCompletion();
        }
    });
});
</script>

<?php
// Final Summary
echo '<h2>🏆 Deduplication Test Summary</h2>';

if ($all_tests_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL DUPLICATE ROUTES SUCCESSFULLY REMOVED!</h3>';
    echo '<p><strong>✅ The ChatGABI REST API system is now clean and conflict-free!</strong></p>';
    echo '<ul>';
    echo '<li>✅ No duplicate route handlers detected</li>';
    echo '<li>✅ All registration hooks properly configured</li>';
    echo '<li>✅ All endpoints responding correctly</li>';
    echo '<li>✅ No WordPress notices about route conflicts</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues May Remain</h3>';
    echo '<p>Most duplicate routes have been removed, but some issues may need additional attention.</p>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Test Actions</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 Test Templates Page</a>';
}

echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="test-button">🌐 Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/opportunities') . '" target="_blank" class="test-button">📊 Opportunities API</a>';
echo '<a href="' . rest_url('chatgabi/v1/health') . '" target="_blank" class="test-button">❤️ Health Check</a>';
echo '<a href="fix-duplicate-rest-routes.php" class="test-button">🔧 Route Analysis</a>';
echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Re-run Test</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Route deduplication test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

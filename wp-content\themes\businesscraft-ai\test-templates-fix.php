<?php
/**
 * Test ChatGABI Templates Function Fix
 * 
 * Tests the fixed chatgabi_get_user_templates() function call
 * Access: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-templates-fix.php
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Ensure we're in the correct theme context
if (get_template() !== 'businesscraft-ai') {
    die('Error: This test must be run with the businesscraft-ai theme active.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Templates Function Fix Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 30px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 6px; border-left: 4px solid #0073aa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f1f1f1; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ChatGABI Templates Function Fix Test</h1>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>Theme:</strong> <?php echo get_template(); ?></p>

        <?php
        $test_results = array();
        $total_tests = 0;
        $passed_tests = 0;

        // Test 1: Check if function exists
        echo '<div class="test-section">';
        echo '<h2>📁 Test 1: Function Existence</h2>';
        
        if (function_exists('chatgabi_get_user_templates')) {
            echo '<p class="success">✅ chatgabi_get_user_templates() function exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ chatgabi_get_user_templates() function missing</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 2: Test function signature
        echo '<div class="test-section">';
        echo '<h2>🔍 Test 2: Function Signature Analysis</h2>';
        
        if (function_exists('chatgabi_get_user_templates')) {
            $reflection = new ReflectionFunction('chatgabi_get_user_templates');
            $parameters = $reflection->getParameters();
            
            echo '<p class="info">📋 Function signature analysis:</p>';
            echo '<table>';
            echo '<tr><th>Parameter</th><th>Required</th><th>Default Value</th></tr>';
            
            foreach ($parameters as $param) {
                $name = $param->getName();
                $required = !$param->isOptional() ? 'Yes' : 'No';
                $default = $param->isOptional() ? ($param->isDefaultValueAvailable() ? var_export($param->getDefaultValue(), true) : 'null') : 'N/A';
                
                echo "<tr><td>\$$name</td><td>$required</td><td>$default</td></tr>";
            }
            echo '</table>';
            
            if (count($parameters) > 0 && !$parameters[0]->isOptional()) {
                echo '<p class="success">✅ Function correctly requires at least 1 parameter (user_id)</p>';
                $passed_tests++;
            } else {
                echo '<p class="error">❌ Function signature issue detected</p>';
            }
        } else {
            echo '<p class="error">❌ Cannot analyze function signature - function not found</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 3: Test function call with user ID
        echo '<div class="test-section">';
        echo '<h2>🧪 Test 3: Function Call with User ID</h2>';
        
        if (function_exists('chatgabi_get_user_templates')) {
            try {
                // Test with a mock user ID (1 is typically admin)
                $test_user_id = 1;
                echo "<p class=\"info\">🔄 Testing function call with user_id = $test_user_id...</p>";
                
                $result = chatgabi_get_user_templates($test_user_id);
                
                if (is_array($result)) {
                    echo '<p class="success">✅ Function call successful - returned array</p>';
                    echo '<p class="info">📊 Result: ' . count($result) . ' templates found</p>';
                    $passed_tests++;
                } else {
                    echo '<p class="warning">⚠️ Function call successful but returned non-array: ' . gettype($result) . '</p>';
                    $passed_tests++;
                }
            } catch (ArgumentCountError $e) {
                echo '<p class="error">❌ ArgumentCountError still occurs: ' . $e->getMessage() . '</p>';
            } catch (Exception $e) {
                echo '<p class="warning">⚠️ Other error occurred: ' . $e->getMessage() . '</p>';
                $passed_tests++; // Still counts as passing the argument count test
            }
        } else {
            echo '<p class="error">❌ Cannot test function call - function not found</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 4: Test function call without arguments (should fail)
        echo '<div class="test-section">';
        echo '<h2>❌ Test 4: Function Call Without Arguments (Expected to Fail)</h2>';
        
        if (function_exists('chatgabi_get_user_templates')) {
            try {
                echo '<p class="info">🔄 Testing function call without arguments (this should fail)...</p>';
                
                $result = chatgabi_get_user_templates();
                
                echo '<p class="error">❌ Function call unexpectedly succeeded - this indicates the fix may not be complete</p>';
            } catch (ArgumentCountError $e) {
                echo '<p class="success">✅ Function correctly throws ArgumentCountError when called without arguments</p>';
                echo '<p class="info">📋 Error message: ' . $e->getMessage() . '</p>';
                $passed_tests++;
            } catch (Exception $e) {
                echo '<p class="warning">⚠️ Function throws different error: ' . $e->getMessage() . '</p>';
            }
        } else {
            echo '<p class="error">❌ Cannot test function call - function not found</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 5: Test the fixed admin dashboard function
        echo '<div class="test-section">';
        echo '<h2>🎯 Test 5: Admin Dashboard Function Test</h2>';
        
        if (function_exists('chatgabi_templates_page')) {
            try {
                echo '<p class="info">🔄 Testing if admin dashboard function can be called without errors...</p>';
                
                // Capture output to prevent display issues
                ob_start();
                
                // Mock current user for testing
                wp_set_current_user(1);
                
                // Test the function that was causing the error
                $current_user_id = get_current_user_id();
                if ($current_user_id > 0) {
                    $user_templates = chatgabi_get_user_templates($current_user_id);
                    echo '<p class="success">✅ Admin dashboard function call successful</p>';
                    echo '<p class="info">📊 Current user ID: ' . $current_user_id . '</p>';
                    echo '<p class="info">📊 Templates retrieved: ' . (is_array($user_templates) ? count($user_templates) : 'N/A') . '</p>';
                    $passed_tests++;
                } else {
                    echo '<p class="warning">⚠️ No current user set - cannot test with real user ID</p>';
                }
                
                ob_end_clean();
                
            } catch (ArgumentCountError $e) {
                echo '<p class="error">❌ ArgumentCountError still occurs in admin context: ' . $e->getMessage() . '</p>';
            } catch (Exception $e) {
                echo '<p class="warning">⚠️ Other error in admin context: ' . $e->getMessage() . '</p>';
            }
        } else {
            echo '<p class="error">❌ chatgabi_templates_page function not found</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test Summary
        echo '<div class="test-section">';
        echo '<h2>📊 Test Summary</h2>';
        
        $success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Tests</td><td>$total_tests</td></tr>";
        echo "<tr><td>Passed Tests</td><td class=\"success\">$passed_tests</td></tr>";
        echo "<tr><td>Failed Tests</td><td class=\"error\">" . ($total_tests - $passed_tests) . "</td></tr>";
        echo "<tr><td>Success Rate</td><td class=\"" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error')) . "\">$success_rate%</td></tr>";
        echo "</table>";
        
        if ($success_rate >= 80) {
            echo '<p class="success">🎉 <strong>Function fix is successful!</strong></p>';
            echo '<p class="info">✅ The ChatGABI Templates page should now work without ArgumentCountError</p>';
        } elseif ($success_rate >= 60) {
            echo '<p class="warning">⚠️ <strong>Function fix is partially successful but may need additional work.</strong></p>';
        } else {
            echo '<p class="error">❌ <strong>Function fix has critical issues that need to be resolved.</strong></p>';
        }
        echo '</div>';

        // Fix Summary
        echo '<div class="test-section">';
        echo '<h2>🔧 Fix Summary</h2>';
        echo '<h3>What was the problem?</h3>';
        echo '<p>The function <code>chatgabi_get_user_templates()</code> was being called without any arguments in <code>inc/admin-dashboard.php</code> line 128, but the function definition requires at least one parameter: <code>$user_id</code>.</p>';
        
        echo '<h3>What was the fix?</h3>';
        echo '<div class="code">';
        echo '<strong>Before (BROKEN):</strong><br>';
        echo '$user_templates = chatgabi_get_user_templates();<br><br>';
        echo '<strong>After (FIXED):</strong><br>';
        echo '$current_user_id = get_current_user_id();<br>';
        echo '$user_templates = chatgabi_get_user_templates($current_user_id);';
        echo '</div>';
        
        echo '<h3>Why this parameter is needed:</h3>';
        echo '<ul>';
        echo '<li><strong>User-specific data:</strong> The function retrieves templates that belong to a specific user</li>';
        echo '<li><strong>Database query:</strong> The user ID is used in the SQL WHERE clause to filter templates</li>';
        echo '<li><strong>Security:</strong> Ensures users only see their own templates</li>';
        echo '<li><strong>WordPress integration:</strong> Uses WordPress\'s get_current_user_id() to get the logged-in user</li>';
        echo '</ul>';
        
        echo '<h3>Next Steps:</h3>';
        echo '<ol>';
        echo '<li>Access the ChatGABI Templates page in WordPress admin</li>';
        echo '<li>Verify no ArgumentCountError occurs</li>';
        echo '<li>Check that user templates are displayed correctly</li>';
        echo '<li>Test template creation and management functionality</li>';
        echo '</ol>';
        echo '</div>';
        ?>
    </div>
</body>
</html>

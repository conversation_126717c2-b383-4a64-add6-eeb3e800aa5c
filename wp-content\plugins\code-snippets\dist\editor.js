/*! For license information please see editor.js.LICENSE.txt */
(()=>{var t,e={2949:(t,e,i)=>{"use strict";const s=window.wp.CodeMirror;var n=i.n(s),r=i(3453),h=i(816);function o(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,(0,h.A)(s.key),s)}}var a=i(4776);function _(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return l(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?l(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var s=0,n=function(){};return{s:n,n:function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,h=!0,o=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return h=t.done,t},e:function(t){o=!0,r=t},f:function(){try{h||null==i.return||i.return()}finally{if(o)throw r}}}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=Array(e);i<e;i++)s[i]=t[i];return s}var c=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.code=e,this.annotations=[],this.function_names=new Set,this.class_names=new Set},e=[{key:"lint",value:function(){var t=new a.Engine({parser:{suppressErrors:!0,version:800},ast:{withPositions:!0}});try{var e=t.parseEval(this.code);if(0<e.errors.length){var i,s=_(e.errors);try{for(s.s();!(i=s.n()).done;){var n=i.value;this.annotate(n.message,n.loc)}}catch(t){s.e(t)}finally{s.f()}}this.visit(e)}catch(n){console.error(n)}}},{key:"visit",value:function(t){if(t.kind&&this.validate(t),"children"in t){var e,i=_(t.children);try{for(i.s();!(e=i.n()).done;){var s=e.value;this.visit(s)}}catch(t){i.e(t)}finally{i.f()}}}},{key:"checkDuplicateIdentifier",value:function(t,e,i){e.has(t.name)?this.annotate("Cannot redeclare ".concat(i," ").concat(t.name,"()"),t.loc):e.add(t.name)}},{key:"validate",value:function(t){var e=t,i=e.name;"name"in e&&"name"in i&&"identifier"===i.kind&&("function"===t.kind?this.checkDuplicateIdentifier(i,this.function_names,"function"):"class"===t.kind&&this.checkDuplicateIdentifier(i,this.class_names,"class"))}},{key:"annotate",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"error",s=e?e.end.offset<e.start.offset?[e.end,e.start]:[e.start,e.end]:[{line:0,column:0},{line:0,column:0}],h=(0,r.A)(s,2),o=h[0],a=h[1];this.annotations.push({message:t,severity:i,from:n().Pos(o.line-1,o.column),to:n().Pos(a.line-1,a.column)})}}],e&&o(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}(),u={name:"application/x-httpd-php",startOpen:!0};(0,s.defineMode)("php-snippet",(function(t){return(0,s.getMode)(t,u)})),(0,s.registerHelper)("lint","php",(function(t){var e=new c(t);return e.lint(),e.annotations}))},4776:t=>{var e;self,e=()=>(()=>{"use strict";var t={8938:(t,e,i)=>{var s=i(4778),n=i(8822),r=function(t,e){this.withPositions=t,this.withSource=e};r.precedence={},[["or"],["xor"],["and"],["="],["?"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","!=","===","!==","<=>"],["<","<=",">",">="],["<<",">>"],["+","-","."],["*","/","%"],["!"],["instanceof"],["cast","silent"],["**"]].forEach((function(t,e){t.forEach((function(t){r.precedence[t]=e+1}))})),r.prototype.isRightAssociative=function(t){return"**"===t||"??"===t},r.prototype.swapLocations=function(t,e,i,s){this.withPositions&&(t.loc.start=e.loc.start,t.loc.end=i.loc.end,this.withSource&&(t.loc.source=s.lexer._input.substring(t.loc.start.offset,t.loc.end.offset)))},r.prototype.resolveLocations=function(t,e,i,s){this.withPositions&&(t.loc.start.offset>e.loc.start.offset&&(t.loc.start=e.loc.start),t.loc.end.offset<i.loc.end.offset&&(t.loc.end=i.loc.end),this.withSource&&(t.loc.source=s.lexer._input.substring(t.loc.start.offset,t.loc.end.offset)))},r.prototype.resolvePrecedence=function(t,e){var i,s,n;return"call"===t.kind?this.resolveLocations(t,t.what,t,e):"propertylookup"===t.kind||"staticlookup"===t.kind||"offsetlookup"===t.kind&&t.offset?this.resolveLocations(t,t.what,t.offset,e):"bin"===t.kind?t.right&&!t.right.parenthesizedExpression&&("bin"===t.right.kind?(s=r.precedence[t.type],n=r.precedence[t.right.type],s&&n&&n<=s&&(t.type!==t.right.type||!this.isRightAssociative(t.type))&&(i=t.right,t.right=t.right.left,this.swapLocations(t,t.left,t.right,e),i.left=this.resolvePrecedence(t,e),this.swapLocations(i,i.left,i.right,e),t=i)):"retif"===t.right.kind&&(s=r.precedence[t.type],n=r.precedence["?"],s&&n&&n<=s&&(i=t.right,t.right=t.right.test,this.swapLocations(t,t.left,t.right,e),i.test=this.resolvePrecedence(t,e),this.swapLocations(i,i.test,i.falseExpr,e),t=i))):"silent"!==t.kind&&"cast"!==t.kind||!t.expr||t.expr.parenthesizedExpression?"unary"===t.kind?t.what&&!t.what.parenthesizedExpression&&("bin"===t.what.kind?(i=t.what,t.what=t.what.left,this.swapLocations(t,t,t.what,e),i.left=this.resolvePrecedence(t,e),this.swapLocations(i,i.left,i.right,e),t=i):"retif"===t.what.kind&&(i=t.what,t.what=t.what.test,this.swapLocations(t,t,t.what,e),i.test=this.resolvePrecedence(t,e),this.swapLocations(i,i.test,i.falseExpr,e),t=i)):"retif"===t.kind?t.falseExpr&&"retif"===t.falseExpr.kind&&!t.falseExpr.parenthesizedExpression&&(i=t.falseExpr,t.falseExpr=i.test,this.swapLocations(t,t.test,t.falseExpr,e),i.test=this.resolvePrecedence(t,e),this.swapLocations(i,i.test,i.falseExpr,e),t=i):"assign"===t.kind?t.right&&"bin"===t.right.kind&&!t.right.parenthesizedExpression&&(s=r.precedence["="],n=r.precedence[t.right.type],s&&n&&n<s&&(i=t.right,t.right=t.right.left,i.left=t,this.swapLocations(i,i.left,t.right,e),t=i)):"expressionstatement"===t.kind&&this.swapLocations(t,t.expression,t,e):"bin"===t.expr.kind?(i=t.expr,t.expr=t.expr.left,this.swapLocations(t,t,t.expr,e),i.left=this.resolvePrecedence(t,e),this.swapLocations(i,i.left,i.right,e),t=i):"retif"===t.expr.kind&&(i=t.expr,t.expr=t.expr.test,this.swapLocations(t,t,t.expr,e),i.test=this.resolvePrecedence(t,e),this.swapLocations(i,i.test,i.falseExpr,e),t=i),t},r.prototype.prepare=function(t,e,i){var r=null;(this.withPositions||this.withSource)&&(r=i.position());var h=this,o=function(){var a=null,_=Array.prototype.slice.call(arguments);if(_.push(e),h.withPositions||h.withSource){var l=null;h.withSource&&(l=i.lexer._input.substring(r.offset,i.prev[2])),a=new s(l,r,new n(i.prev[0],i.prev[1],i.prev[2])),_.push(a)}t||(t=_.shift());var c=h[t];if("function"!=typeof c)throw new Error('Undefined node "'+t+'"');var u=Object.create(c.prototype);return c.apply(u,_),o.instance=u,o.trailingComments&&(u.trailingComments=o.trailingComments),"function"==typeof o.postBuild&&o.postBuild(u),i.debug&&delete h.stack[o.stackUid],h.resolvePrecedence(u,i)};return i.debug&&(this.stack||(this.stack={},this.stackUid=1),this.stack[++this.stackUid]={position:r,stack:(new Error).stack.split("\n").slice(3,5)},o.stackUid=this.stackUid),o.setTrailingComments=function(t){o.instance?o.instance.setTrailingComments(t):o.trailingComments=t},o.destroy=function(t){e&&(t?t.leadingComments?t.leadingComments=e.concat(t.leadingComments):t.leadingComments=e:i._docIndex=i._docs.length-e.length),i.debug&&delete h.stack[o.stackUid]},o},r.prototype.checkNodes=function(){var t=[];for(var e in this.stack)Object.prototype.hasOwnProperty.call(this.stack,e)&&(this.stack[e].key=e,t.push(this.stack[e]));return this.stack={},t},[i(3160),i(1654),i(1240),i(3979),i(5553),i(2207),i(2916),i(4628),i(7509),i(2906),i(5723),i(7561),i(6473),i(9626),i(4782),i(8477),i(5045),i(900),i(4824),i(1020),i(9847),i(2790),i(1333),i(2112),i(9960),i(8533),i(5947),i(7786),i(5436),i(1136),i(380),i(6129),i(9723),i(5125),i(9632),i(4300),i(1515),i(3411),i(9781),i(839),i(8374),i(9754),i(4251),i(6553),i(8630),i(9786),i(9742),i(1234),i(6),i(8861),i(7860),i(9834),i(2724),i(6025),i(2687),i(7633),i(5514),i(7427),i(1122),i(7256),i(7416),i(8140),i(6258),i(9474),i(6827),i(4427),i(4065),i(4297),i(5859),i(6985),i(9302),i(8212),i(864),i(8268),i(7190),i(8519),i(4835),i(2056),i(4838),i(7869),i(1908),i(170),i(1091),i(8276),i(1842),i(5739),i(1274),i(4352),i(9672),i(711),i(1231),i(1865),i(1102),i(7472),i(6133),i(1197),i(6649),i(1837),i(2277),i(8010),i(7579),i(3460),i(2702),i(514),i(5684),i(8019),i(7721),i(4369),i(40),i(4919),i(7676),i(2596),i(6744)].forEach((function(t){r.prototype[t.kind]=t})),t.exports=r},3160:(t,e,i)=>{var s=i(839),n="array";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.items=e,this.shortForm=t}))},1654:(t,e,i)=>{var s=i(839),n="arrowfunc";t.exports=s.extends(n,(function(t,e,i,r,h,o,a,_){s.apply(this,[n,a,_]),this.arguments=t,this.byref=e,this.body=i,this.type=r,this.nullable=h,this.isStatic=o||!1}))},1240:(t,e,i)=>{var s=i(839),n="assign";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.left=t,this.right=e,this.operator=i}))},3979:(t,e,i)=>{var s=i(839),n="assignref";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.left=t,this.right=e}))},2207:(t,e,i)=>{var s=i(4065),n="attrgroup";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.attrs=t||[]}))},5553:(t,e,i)=>{var s=i(4065),n="attribute";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t,this.args=e}))},2916:(t,e,i)=>{var s=i(8268);t.exports=s.extends("bin",(function(t,e,i,n,r){s.apply(this,["bin",n,r]),this.type=t,this.left=e,this.right=i}))},4628:(t,e,i)=>{var s=i(9672),n="block";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[t||n,i,r]),this.children=e.filter(Boolean)}))},7509:(t,e,i)=>{var s=i(5514),n="boolean";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},2906:(t,e,i)=>{var s=i(9672),n="break";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.level=t}))},5723:(t,e,i)=>{var s=i(839),n="byref";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.what=t}))},7561:(t,e,i)=>{var s=i(839),n="call";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.what=t,this.arguments=e}))},6473:(t,e,i)=>{var s=i(9672),n="case";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.test=t,this.body=e}))},9626:(t,e,i)=>{var s=i(8268),n="cast";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.type=t,this.raw=e,this.expr=i}))},4782:(t,e,i)=>{var s=i(9672),n="catch";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.body=t,this.what=e,this.variable=i}))},8477:(t,e,i)=>{var s=i(8533),n="class";t.exports=s.extends(n,(function(t,e,i,r,h,o,a){s.apply(this,[n,t,o,a]),this.isAnonymous=!t,this.extends=e,this.implements=i,this.body=r,this.attrGroups=[],this.parseFlags(h)}))},5045:(t,e,i)=>{var s=i(2112),n="classconstant",r=s.extends(n,(function(t,e,i,r,h,o,a,_){s.apply(this,[t||n,e,a,_]),this.parseFlags(i),this.nullable=r,this.type=h,this.attrGroups=o}));r.prototype.parseFlags=function(t){-1===t[0]?this.visibility="":null===t[0]?this.visibility=null:0===t[0]?this.visibility="public":1===t[0]?this.visibility="protected":2===t[0]&&(this.visibility="private"),this.final=2===t[2]},t.exports=r},900:(t,e,i)=>{var s=i(839),n="clone";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.what=t}))},4824:(t,e,i)=>{var s=i(839),n="closure";t.exports=s.extends(n,(function(t,e,i,r,h,o,a,_){s.apply(this,[n,a,_]),this.uses=i,this.arguments=t,this.byref=e,this.type=r,this.nullable=h,this.isStatic=o||!1,this.body=null,this.attrGroups=[]}))},1020:(t,e,i)=>{var s=i(4065);t.exports=s.extends("comment",(function(t,e,i,n){s.apply(this,[t,i,n]),this.value=e}))},9847:(t,e,i)=>{var s=i(1020),n="commentblock";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,t,e,i])}))},2790:(t,e,i)=>{var s=i(1020),n="commentline";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,t,e,i])}))},1333:(t,e,i)=>{var s=i(4065),n="constant";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t,this.value=e}))},2112:(t,e,i)=>{var s=i(9672),n="constantstatement";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[t||n,i,r]),this.constants=e}))},9960:(t,e,i)=>{var s=i(9672),n="continue";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.level=t}))},8533:(t,e,i)=>{var s=i(9672),n="declaration",r=s.extends(n,(function(t,e,i,r){s.apply(this,[t||n,i,r]),this.name=e}));r.prototype.parseFlags=function(t){this.isAbstract=1===t[2],this.isFinal=2===t[2],this.isReadonly=1===t[3],"class"!==this.kind&&(-1===t[0]?this.visibility="":null===t[0]?this.visibility=null:0===t[0]?this.visibility="public":1===t[0]?this.visibility="protected":2===t[0]&&(this.visibility="private"),this.isStatic=1===t[1])},t.exports=r},5947:(t,e,i)=>{var s=i(4628),n="declare",r=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,e,r,h]),this.directives=t,this.mode=i}));r.MODE_SHORT="short",r.MODE_BLOCK="block",r.MODE_NONE="none",t.exports=r},7786:(t,e,i)=>{var s=i(4065),n="declaredirective";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.key=t,this.value=e}))},5436:(t,e,i)=>{var s=i(9672);t.exports=s.extends("do",(function(t,e,i,n){s.apply(this,["do",i,n]),this.test=t,this.body=e}))},1136:(t,e,i)=>{var s=i(9672),n="echo";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.shortForm=e,this.expressions=t}))},380:(t,e,i)=>{var s=i(839),n="empty";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.expression=t}))},6129:(t,e,i)=>{var s=i(5514),n="encapsed",r=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,t,e,r,h]),this.type=i}));r.TYPE_STRING="string",r.TYPE_SHELL="shell",r.TYPE_HEREDOC="heredoc",r.TYPE_OFFSET="offset",t.exports=r},9723:(t,e,i)=>{var s=i(839),n="encapsedpart";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.expression=t,this.syntax=e,this.curly=i}))},5125:(t,e,i)=>{var s=i(839),n="entry";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,h,o]),this.key=t,this.value=e,this.byRef=i,this.unpack=r}))},9632:(t,e,i)=>{var s=i(8533),n="enum";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,t,h,o]),this.valueType=e,this.implements=i,this.body=r,this.attrGroups=[]}))},4300:(t,e,i)=>{var s=i(4065),n="enumcase";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t,this.value=e}))},1515:(t,e,i)=>{var s=i(4065),n="error";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,h,o]),this.message=t,this.token=e,this.line=i,this.expected=r}))},3411:(t,e,i)=>{var s=i(839),n="eval";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.source=t}))},9781:(t,e,i)=>{var s=i(839),n="exit";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.expression=t,this.useDie=e}))},839:(t,e,i)=>{var s=i(4065),n="expression";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[t||n,e,i])}))},8374:(t,e,i)=>{var s=i(9672),n="expressionstatement";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.expression=t}))},9754:(t,e,i)=>{var s=i(9672);t.exports=s.extends("for",(function(t,e,i,n,r,h,o){s.apply(this,["for",h,o]),this.init=t,this.test=e,this.increment=i,this.shortForm=r,this.body=n}))},4251:(t,e,i)=>{var s=i(9672),n="foreach";t.exports=s.extends(n,(function(t,e,i,r,h,o,a){s.apply(this,[n,o,a]),this.source=t,this.key=e,this.value=i,this.shortForm=h,this.body=r}))},6553:(t,e,i)=>{var s=i(8533),n="function";t.exports=s.extends(n,(function(t,e,i,r,h,o,a){s.apply(this,[n,t,o,a]),this.arguments=e,this.byref=i,this.type=r,this.nullable=h,this.body=null,this.attrGroups=[]}))},8630:(t,e,i)=>{var s=i(9672),n="global";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.items=t}))},9786:(t,e,i)=>{var s=i(9672),n="goto";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.label=t}))},9742:(t,e,i)=>{var s=i(9672),n="halt";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.after=t}))},1234:(t,e,i)=>{var s=i(4065),n="identifier",r=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.name=t}));t.exports=r},6:(t,e,i)=>{var s=i(9672);t.exports=s.extends("if",(function(t,e,i,n,r,h){s.apply(this,["if",r,h]),this.test=t,this.body=e,this.alternate=i,this.shortForm=n}))},8861:(t,e,i)=>{var s=i(839),n="include";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.once=t,this.require=e,this.target=i}))},7860:(t,e,i)=>{var s=i(5514),n="inline";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},9834:(t,e,i)=>{var s=i(8533),n="interface";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,t,h,o]),this.extends=e,this.body=i,this.attrGroups=r}))},2724:(t,e,i)=>{var s=i(8533),n="intersectiontype";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,null,e,i]),this.types=t}))},6025:(t,e,i)=>{var s=i(839),n="isset";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.variables=t}))},2687:(t,e,i)=>{var s=i(9672),n="label";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.name=t}))},7633:(t,e,i)=>{var s=i(839),n="list";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.items=t,this.shortForm=e}))},5514:(t,e,i)=>{var s=i(839),n="literal";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[t||n,r,h]),this.value=e,i&&(this.raw=i)}))},4778:t=>{t.exports=function(t,e,i){this.source=t,this.start=e,this.end=i}},7427:(t,e,i)=>{var s=i(839),n="lookup";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[t||n,r,h]),this.what=e,this.offset=i}))},1122:(t,e,i)=>{var s=i(5514),n="magic";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},7256:(t,e,i)=>{var s=i(839),n="match";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.cond=t,this.arms=e}))},7416:(t,e,i)=>{var s=i(839),n="matcharm";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.conds=t,this.body=e}))},8140:(t,e,i)=>{var s=i(6553),n="method";t.exports=s.extends(n,(function(){s.apply(this,arguments),this.kind=n}))},6258:(t,e,i)=>{var s=i(8276),n="name",r=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t.replace(/\\$/,""),this.resolution=e}));r.UNQUALIFIED_NAME="uqn",r.QUALIFIED_NAME="qn",r.FULL_QUALIFIED_NAME="fqn",r.RELATIVE_NAME="rn",t.exports=r},6827:(t,e,i)=>{var s=i(839),n="namedargument";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t,this.value=e}))},9474:(t,e,i)=>{var s=i(4628),n="namespace";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,e,r,h]),this.name=t,this.withBrackets=i||!1}))},4427:(t,e,i)=>{var s=i(839);t.exports=s.extends("new",(function(t,e,i,n){s.apply(this,["new",i,n]),this.what=t,this.arguments=e}))},4065:t=>{var e=function(t,e,i){this.kind=t,e&&(this.leadingComments=e),i&&(this.loc=i)};e.prototype.setTrailingComments=function(t){this.trailingComments=t},e.prototype.destroy=function(t){if(!t)throw new Error("Node already initialized, you must swap with another node");return this.leadingComments&&(t.leadingComments?t.leadingComments=Array.concat(this.leadingComments,t.leadingComments):t.leadingComments=this.leadingComments),this.trailingComments&&(t.trailingComments?t.trailingComments=Array.concat(this.trailingComments,t.trailingComments):t.trailingComments=this.trailingComments),t},e.prototype.includeToken=function(t){return this.loc&&(this.loc.end&&(this.loc.end.line=t.lexer.yylloc.last_line,this.loc.end.column=t.lexer.yylloc.last_column,this.loc.end.offset=t.lexer.offset),t.ast.withSource&&(this.loc.source=t.lexer._input.substring(this.loc.start.offset,t.lexer.offset))),this},e.extends=function(t,e){return e.prototype=Object.create(this.prototype),e.extends=this.extends,e.prototype.constructor=e,e.kind=t,e},t.exports=e},4297:(t,e,i)=>{var s=i(4065),n="noop";t.exports=s.extends(n,(function(t,e){s.apply(this,[n,t,e])}))},5859:(t,e,i)=>{var s=i(5514),n="nowdoc";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,t,e,r,h]),this.label=i}))},6985:(t,e,i)=>{var s=i(4065),n="nullkeyword";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.raw=t}))},9302:(t,e,i)=>{var s=i(7427),n="nullsafepropertylookup";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},8212:(t,e,i)=>{var s=i(5514),n="number";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},864:(t,e,i)=>{var s=i(7427),n="offsetlookup";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},8268:(t,e,i)=>{var s=i(839),n="operation";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[t||n,e,i])}))},7190:(t,e,i)=>{var s=i(8533),n="parameter";t.exports=s.extends(n,(function(t,e,i,r,h,o,a,_,l,c){s.apply(this,[n,t,l,c]),this.value=i,this.type=e,this.byref=r,this.variadic=h,this.readonly=o,this.nullable=a,this.flags=_||0,this.attrGroups=[]}))},8519:(t,e,i)=>{var s=i(8276),n="parentreference",r=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.raw=t}));t.exports=r},8822:t=>{t.exports=function(t,e,i){this.line=t,this.column=e,this.offset=i}},4835:(t,e,i)=>{var s=i(8268),n="post";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.type=t,this.what=e}))},2056:(t,e,i)=>{var s=i(8268);t.exports=s.extends("pre",(function(t,e,i,n){s.apply(this,["pre",i,n]),this.type=t,this.what=e}))},4838:(t,e,i)=>{var s=i(839),n="print";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.expression=t}))},7869:(t,e,i)=>{var s=i(4628),n="program";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,t,h,o]),this.errors=e,i&&(this.comments=i),r&&(this.tokens=r)}))},1908:(t,e,i)=>{var s=i(9672),n="property";t.exports=s.extends(n,(function(t,e,i,r,h,o,a,_){s.apply(this,[n,a,_]),this.name=t,this.value=e,this.readonly=i,this.nullable=r,this.type=h,this.attrGroups=o}))},170:(t,e,i)=>{var s=i(7427),n="propertylookup";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},1091:(t,e,i)=>{var s=i(9672),n="propertystatement",r=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.properties=e,this.parseFlags(i)}));r.prototype.parseFlags=function(t){-1===t[0]?this.visibility="":null===t[0]?this.visibility=null:0===t[0]?this.visibility="public":1===t[0]?this.visibility="protected":2===t[0]&&(this.visibility="private"),this.isStatic=1===t[1]},t.exports=r},8276:(t,e,i)=>{var s=i(4065),n="reference",r=s.extends(n,(function(t,e,i){s.apply(this,[t||n,e,i])}));t.exports=r},1842:(t,e,i)=>{var s=i(839),n="retif";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.test=t,this.trueExpr=e,this.falseExpr=i}))},5739:(t,e,i)=>{var s=i(9672),n="return";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.expr=t}))},1274:(t,e,i)=>{var s=i(8276),n="selfreference",r=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.raw=t}));t.exports=r},4352:(t,e,i)=>{var s=i(839),n="silent";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.expr=t}))},9672:(t,e,i)=>{var s=i(4065),n="statement";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[t||n,e,i])}))},711:(t,e,i)=>{var s=i(9672),n="static";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.variables=t}))},1865:(t,e,i)=>{var s=i(7427),n="staticlookup";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,e,i,r])}))},1102:(t,e,i)=>{var s=i(8276),n="staticreference",r=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.raw=t}));t.exports=r},1231:(t,e,i)=>{var s=i(4065),n="staticvariable";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.variable=t,this.defaultValue=e}))},7472:(t,e,i)=>{var s=i(5514),n="string";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,e,r,h,o]),this.unicode=i,this.isDoubleQuote=t}))},6133:(t,e,i)=>{var s=i(9672),n="switch";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.test=t,this.body=e,this.shortForm=i}))},1197:(t,e,i)=>{var s=i(9672),n="throw";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.what=t}))},6649:(t,e,i)=>{var s=i(8533),n="trait";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,t,i,r]),this.body=e}))},1837:(t,e,i)=>{var s=i(4065),n="traitalias";t.exports=s.extends(n,(function(t,e,i,r,h,o){s.apply(this,[n,h,o]),this.trait=t,this.method=e,this.as=i,this.visibility="",r&&(0===r[0]?this.visibility="public":1===r[0]?this.visibility="protected":2===r[0]&&(this.visibility="private"))}))},2277:(t,e,i)=>{var s=i(4065),n="traitprecedence";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.trait=t,this.method=e,this.instead=i}))},8010:(t,e,i)=>{var s=i(4065),n="traituse";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.traits=t,this.adaptations=e}))},7579:(t,e,i)=>{var s=i(9672);t.exports=s.extends("try",(function(t,e,i,n,r){s.apply(this,["try",n,r]),this.body=t,this.catches=e,this.always=i}))},3460:(t,e,i)=>{var s=i(8276),n="typereference",r=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t,this.raw=e}));r.types=["int","float","string","bool","object","array","callable","iterable","void","static"],t.exports=r},2702:(t,e,i)=>{var s=i(8268),n="unary";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.type=t,this.what=e}))},514:(t,e,i)=>{var s=i(8533),n="uniontype";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,null,e,i]),this.types=t}))},5684:(t,e,i)=>{var s=i(9672),n="unset";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.variables=t}))},8019:(t,e,i)=>{var s=i(9672),n="usegroup";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.name=t,this.type=e,this.items=i}))},7721:(t,e,i)=>{var s=i(9672),n="useitem",r=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.name=t,this.alias=e,this.type=i}));r.TYPE_CONST="const",r.TYPE_FUNCTION="function",t.exports=r},4369:(t,e,i)=>{var s=i(839),n="variable";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.name=t,this.curly=e||!1}))},40:(t,e,i)=>{var s=i(839),n="variadic";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.what=t}))},4919:(t,e,i)=>{var s=i(4065),n="variadicplaceholder";t.exports=s.extends(n,(function(t,e){s.apply(this,[n,t,e])}))},7676:(t,e,i)=>{var s=i(9672),n="while";t.exports=s.extends(n,(function(t,e,i,r,h){s.apply(this,[n,r,h]),this.test=t,this.body=e,this.shortForm=i}))},2596:(t,e,i)=>{var s=i(839),n="yield";t.exports=s.extends(n,(function(t,e,i,r){s.apply(this,[n,i,r]),this.value=t,this.key=e}))},6744:(t,e,i)=>{var s=i(839),n="yieldfrom";t.exports=s.extends(n,(function(t,e,i){s.apply(this,[n,e,i]),this.value=t}))},5362:(t,e,i)=>{function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}var n=i(9108),r=i(7259),h=i(1906),o=i(8938);function a(t,e){for(var i=Object.keys(t),n=i.length;n--;){var r=i[n],h=t[r];null===h?delete e[r]:"function"==typeof h?e[r]=h.bind(e):Array.isArray(h)?e[r]=Array.isArray(e[r])?e[r].concat(h):h:"object"===s(h)?e[r]="object"===s(e[r])?a(h,e[r]):h:e[r]=h}return e}var _=function(t){if("function"==typeof this)return new this(t);if(this.tokens=h,this.lexer=new n(this),this.ast=new o,this.parser=new r(this.lexer,this.ast),t&&"object"===s(t)){if(t.parser&&(t.lexer||(t.lexer={}),t.parser.version)){if("string"==typeof t.parser.version){var e=t.parser.version.split(".");if(e=100*parseInt(e[0])+parseInt(e[1]),isNaN(e))throw new Error("Bad version number : "+t.parser.version);t.parser.version=e}else if("number"!=typeof t.parser.version)throw new Error("Expecting a number for version");if(t.parser.version<500||t.parser.version>900)throw new Error("Can only handle versions between 5.x to 8.x")}a(t,this),this.lexer.version=this.parser.version}},l=function(t){return"function"==typeof t.write?t.toString():t};_.create=function(t){return new _(t)},_.parseEval=function(t,e){return new _(e).parseEval(t)},_.prototype.parseEval=function(t){return this.lexer.mode_eval=!0,this.lexer.all_tokens=!1,t=l(t),this.parser.parse(t,"eval")},_.parseCode=function(t,e,i){return"object"!==s(e)||i||(i=e,e="unknown"),new _(i).parseCode(t,e)},_.prototype.parseCode=function(t,e){return this.lexer.mode_eval=!1,this.lexer.all_tokens=!1,t=l(t),this.parser.parse(t,e)},_.tokenGetAll=function(t,e){return new _(e).tokenGetAll(t)},_.prototype.tokenGetAll=function(t){this.lexer.mode_eval=!1,this.lexer.all_tokens=!0,t=l(t);var e=this.lexer.EOF,i=this.tokens.values;this.lexer.setInput(t);for(var s=this.lexer.lex()||e,n=[];s!=e;){var r=this.lexer.yytext;Object.prototype.hasOwnProperty.call(i,s)&&(r=[i[s],r,this.lexer.yylloc.first_line]),n.push(r),s=this.lexer.lex()||e}return n},t.exports=_,t.exports.tokens=h,t.exports.lexer=n,t.exports.AST=o,t.exports.parser=r,t.exports.combine=a,t.exports.Engine=_,t.exports.default=_},9108:(t,e,i)=>{function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}var n=function(t){this.engine=t,this.tok=this.engine.tokens.names,this.EOF=1,this.debug=!1,this.all_tokens=!0,this.comment_tokens=!1,this.mode_eval=!1,this.asp_tags=!1,this.short_tags=!1,this.version=803,this.yyprevcol=0,this.keywords={__class__:this.tok.T_CLASS_C,__trait__:this.tok.T_TRAIT_C,__function__:this.tok.T_FUNC_C,__method__:this.tok.T_METHOD_C,__line__:this.tok.T_LINE,__file__:this.tok.T_FILE,__dir__:this.tok.T_DIR,__namespace__:this.tok.T_NS_C,exit:this.tok.T_EXIT,die:this.tok.T_EXIT,function:this.tok.T_FUNCTION,const:this.tok.T_CONST,return:this.tok.T_RETURN,try:this.tok.T_TRY,catch:this.tok.T_CATCH,finally:this.tok.T_FINALLY,throw:this.tok.T_THROW,if:this.tok.T_IF,elseif:this.tok.T_ELSEIF,endif:this.tok.T_ENDIF,else:this.tok.T_ELSE,while:this.tok.T_WHILE,endwhile:this.tok.T_ENDWHILE,do:this.tok.T_DO,for:this.tok.T_FOR,endfor:this.tok.T_ENDFOR,foreach:this.tok.T_FOREACH,endforeach:this.tok.T_ENDFOREACH,declare:this.tok.T_DECLARE,enddeclare:this.tok.T_ENDDECLARE,instanceof:this.tok.T_INSTANCEOF,as:this.tok.T_AS,switch:this.tok.T_SWITCH,endswitch:this.tok.T_ENDSWITCH,case:this.tok.T_CASE,default:this.tok.T_DEFAULT,break:this.tok.T_BREAK,continue:this.tok.T_CONTINUE,goto:this.tok.T_GOTO,echo:this.tok.T_ECHO,print:this.tok.T_PRINT,class:this.tok.T_CLASS,interface:this.tok.T_INTERFACE,trait:this.tok.T_TRAIT,enum:this.tok.T_ENUM,extends:this.tok.T_EXTENDS,implements:this.tok.T_IMPLEMENTS,new:this.tok.T_NEW,clone:this.tok.T_CLONE,var:this.tok.T_VAR,eval:this.tok.T_EVAL,include:this.tok.T_INCLUDE,include_once:this.tok.T_INCLUDE_ONCE,require:this.tok.T_REQUIRE,require_once:this.tok.T_REQUIRE_ONCE,namespace:this.tok.T_NAMESPACE,use:this.tok.T_USE,insteadof:this.tok.T_INSTEADOF,global:this.tok.T_GLOBAL,isset:this.tok.T_ISSET,empty:this.tok.T_EMPTY,__halt_compiler:this.tok.T_HALT_COMPILER,static:this.tok.T_STATIC,abstract:this.tok.T_ABSTRACT,final:this.tok.T_FINAL,private:this.tok.T_PRIVATE,protected:this.tok.T_PROTECTED,public:this.tok.T_PUBLIC,unset:this.tok.T_UNSET,list:this.tok.T_LIST,array:this.tok.T_ARRAY,callable:this.tok.T_CALLABLE,or:this.tok.T_LOGICAL_OR,and:this.tok.T_LOGICAL_AND,xor:this.tok.T_LOGICAL_XOR,match:this.tok.T_MATCH,readonly:this.tok.T_READ_ONLY},this.castKeywords={int:this.tok.T_INT_CAST,integer:this.tok.T_INT_CAST,real:this.tok.T_DOUBLE_CAST,double:this.tok.T_DOUBLE_CAST,float:this.tok.T_DOUBLE_CAST,string:this.tok.T_STRING_CAST,binary:this.tok.T_STRING_CAST,array:this.tok.T_ARRAY_CAST,object:this.tok.T_OBJECT_CAST,bool:this.tok.T_BOOL_CAST,boolean:this.tok.T_BOOL_CAST,unset:this.tok.T_UNSET_CAST}};n.prototype.setInput=function(t){return this._input=t,this.size=t.length,this.yylineno=1,this.offset=0,this.yyprevcol=0,this.yytext="",this.yylloc={first_offset:0,first_line:1,first_column:0,prev_offset:0,prev_line:1,prev_column:0,last_line:1,last_column:0},this.tokens=[],this.version>703?this.keywords.fn=this.tok.T_FN:delete this.keywords.fn,this.done=this.offset>=this.size,!this.all_tokens&&this.mode_eval?(this.conditionStack=["INITIAL"],this.begin("ST_IN_SCRIPTING")):(this.conditionStack=[],this.begin("INITIAL")),this.heredoc_label={label:"",length:0,indentation:0,indentation_uses_spaces:!1,finished:!1,first_encaps_node:!1,toString:function(){this.label}},this},n.prototype.input=function(){var t=this._input[this.offset];return t?(this.yytext+=t,this.offset++,"\r"===t&&"\n"===this._input[this.offset]&&(this.yytext+="\n",this.offset++),"\n"===t||"\r"===t?(this.yylloc.last_line=++this.yylineno,this.yyprevcol=this.yylloc.last_column,this.yylloc.last_column=0):this.yylloc.last_column++,t):""},n.prototype.unput=function(t){if(1===t)this.offset--,"\n"===this._input[this.offset]&&"\r"===this._input[this.offset-1]&&(this.offset--,t++),"\r"===this._input[this.offset]||"\n"===this._input[this.offset]?(this.yylloc.last_line--,this.yylineno--,this.yylloc.last_column=this.yyprevcol):this.yylloc.last_column--,this.yytext=this.yytext.substring(0,this.yytext.length-t);else if(t>0)if(this.offset-=t,t<this.yytext.length){this.yytext=this.yytext.substring(0,this.yytext.length-t),this.yylloc.last_line=this.yylloc.first_line,this.yylloc.last_column=this.yyprevcol=this.yylloc.first_column;for(var e=0;e<this.yytext.length;e++){var i=this.yytext[e];"\r"===i?(i=this.yytext[++e],this.yyprevcol=this.yylloc.last_column,this.yylloc.last_line++,this.yylloc.last_column=0,"\n"!==i&&("\r"===i?this.yylloc.last_line++:this.yylloc.last_column++)):"\n"===i?(this.yyprevcol=this.yylloc.last_column,this.yylloc.last_line++,this.yylloc.last_column=0):this.yylloc.last_column++}this.yylineno=this.yylloc.last_line}else this.yytext="",this.yylloc.last_line=this.yylineno=this.yylloc.first_line,this.yylloc.last_column=this.yylloc.first_column;return this},n.prototype.tryMatch=function(t){return t===this.ahead(t.length)},n.prototype.tryMatchCaseless=function(t){return t===this.ahead(t.length).toLowerCase()},n.prototype.ahead=function(t){var e=this._input.substring(this.offset,this.offset+t);return"\r"===e[e.length-1]&&"\n"===this._input[this.offset+t+1]&&(e+="\n"),e},n.prototype.consume=function(t){for(var e=0;e<t;e++){var i=this._input[this.offset];if(!i)break;this.yytext+=i,this.offset++,"\r"===i&&"\n"===this._input[this.offset]&&(this.yytext+="\n",this.offset++,e++),"\n"===i||"\r"===i?(this.yylloc.last_line=++this.yylineno,this.yyprevcol=this.yylloc.last_column,this.yylloc.last_column=0):this.yylloc.last_column++}return this},n.prototype.getState=function(){return{yytext:this.yytext,offset:this.offset,yylineno:this.yylineno,yyprevcol:this.yyprevcol,yylloc:{first_offset:this.yylloc.first_offset,first_line:this.yylloc.first_line,first_column:this.yylloc.first_column,last_line:this.yylloc.last_line,last_column:this.yylloc.last_column},heredoc_label:this.heredoc_label}},n.prototype.setState=function(t){return this.yytext=t.yytext,this.offset=t.offset,this.yylineno=t.yylineno,this.yyprevcol=t.yyprevcol,this.yylloc=t.yylloc,t.heredoc_label&&(this.heredoc_label=t.heredoc_label),this},n.prototype.appendToken=function(t,e){return this.tokens.push([t,e]),this},n.prototype.lex=function(){this.yylloc.prev_offset=this.offset,this.yylloc.prev_line=this.yylloc.last_line,this.yylloc.prev_column=this.yylloc.last_column;var t=this.next()||this.lex();if(!this.all_tokens){for(;t===this.tok.T_WHITESPACE||!this.comment_tokens&&(t===this.tok.T_COMMENT||t===this.tok.T_DOC_COMMENT)||t===this.tok.T_OPEN_TAG;)t=this.next()||this.lex();if(t==this.tok.T_OPEN_TAG_WITH_ECHO)return this.tok.T_ECHO;if(t===this.tok.T_CLOSE_TAG)return";"}return this.yylloc.prev_offset||(this.yylloc.prev_offset=this.yylloc.first_offset,this.yylloc.prev_line=this.yylloc.first_line,this.yylloc.prev_column=this.yylloc.first_column),t},n.prototype.begin=function(t){if(this.conditionStack.push(t),this.curCondition=t,this.stateCb=this["match"+t],"function"!=typeof this.stateCb)throw new Error('Undefined condition state "'+t+'"');return this},n.prototype.popState=function(){var t=this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0];if(this.curCondition=this.conditionStack[this.conditionStack.length-1],this.stateCb=this["match"+this.curCondition],"function"!=typeof this.stateCb)throw new Error('Undefined condition state "'+this.curCondition+'"');return t},n.prototype.next=function(){var t;if(this._input||(this.done=!0),this.yylloc.first_offset=this.offset,this.yylloc.first_line=this.yylloc.last_line,this.yylloc.first_column=this.yylloc.last_column,this.yytext="",this.done)return this.yylloc.prev_offset=this.yylloc.first_offset,this.yylloc.prev_line=this.yylloc.first_line,this.yylloc.prev_column=this.yylloc.first_column,this.EOF;if(this.tokens.length>0?("object"===s((t=this.tokens.shift())[1])?this.setState(t[1]):this.consume(t[1]),t=t[0]):t=this.stateCb.apply(this,[]),this.offset>=this.size&&0===this.tokens.length&&(this.done=!0),this.debug){var e=t;e="number"==typeof e?this.engine.tokens.values[e]:'"'+e+'"';var i=new Error(e+"\tfrom "+this.yylloc.first_line+","+this.yylloc.first_column+"\t - to "+this.yylloc.last_line+","+this.yylloc.last_column+'\t"'+this.yytext+'"');console.error(i.stack)}return t},[i(9671),i(2429),i(3683),i(6545),i(3810),i(8510),i(4401),i(4349),i(8582)].forEach((function(t){for(var e in t)n.prototype[e]=t[e]})),t.exports=n},9671:t=>{t.exports={attributeIndex:0,attributeListDepth:{},matchST_ATTRIBUTE:function(){var t=this.input();if(this.is_WHITESPACE()){do{t=this.input()}while(this.is_WHITESPACE());return this.unput(1),null}switch(t){case"]":return 0===this.attributeListDepth[this.attributeIndex]?(delete this.attributeListDepth[this.attributeIndex],this.attributeIndex--,this.popState()):this.attributeListDepth[this.attributeIndex]--,"]";case"(":case")":case":":case"=":case"|":case"&":case"^":case"-":case"+":case"*":case"%":case"~":case"<":case">":case"!":case".":return this.consume_TOKEN();case"[":return this.attributeListDepth[this.attributeIndex]++,"[";case",":return",";case'"':return this.ST_DOUBLE_QUOTES();case"'":return this.T_CONSTANT_ENCAPSED_STRING();case"/":return"/"===this._input[this.offset]?this.T_COMMENT():"*"===this._input[this.offset]?(this.input(),this.T_DOC_COMMENT()):this.consume_TOKEN()}if(this.is_LABEL_START()||"\\"===t){for(;this.offset<this.size;){var e=this.input();if(!this.is_LABEL()&&"\\"!==e){e&&this.unput(1);break}}return this.T_STRING()}if(this.is_NUM())return this.consume_NUM();throw new Error('Bad terminal sequence "'.concat(t,'" at line ').concat(this.yylineno," (offset ").concat(this.offset,")"))}}},2429:t=>{t.exports={T_COMMENT:function(){for(;this.offset<this.size;){var t=this.input();if("\n"===t||"\r"===t)return this.tok.T_COMMENT;if("?"===t&&!this.aspTagMode&&">"===this._input[this.offset])return this.unput(1),this.tok.T_COMMENT;if("%"===t&&this.aspTagMode&&">"===this._input[this.offset])return this.unput(1),this.tok.T_COMMENT}return this.tok.T_COMMENT},T_DOC_COMMENT:function(){var t=this.input(),e=this.tok.T_COMMENT;if("*"===t){if(t=this.input(),this.is_WHITESPACE()&&(e=this.tok.T_DOC_COMMENT),"/"===t)return e;this.unput(1)}for(;this.offset<this.size;)if("*"===(t=this.input())&&"/"===this._input[this.offset]){this.input();break}return e}}},3683:t=>{t.exports={nextINITIAL:function(){return this.conditionStack.length>1&&"INITIAL"===this.conditionStack[this.conditionStack.length-1]?this.popState():this.begin("ST_IN_SCRIPTING"),this},matchINITIAL:function(){for(;this.offset<this.size;){var t=this.input();if("<"==t)if("?"==(t=this.ahead(1))){if(this.tryMatch("?=")){this.unput(1).appendToken(this.tok.T_OPEN_TAG_WITH_ECHO,3).nextINITIAL();break}if(this.tryMatchCaseless("?php")&&(" "===(t=this._input[this.offset+4])||"\t"===t||"\n"===t||"\r"===t)){this.unput(1).appendToken(this.tok.T_OPEN_TAG,6).nextINITIAL();break}if(this.short_tags){this.unput(1).appendToken(this.tok.T_OPEN_TAG,2).nextINITIAL();break}}else if(this.asp_tags&&"%"==t){if(this.tryMatch("%=")){this.aspTagMode=!0,this.unput(1).appendToken(this.tok.T_OPEN_TAG_WITH_ECHO,3).nextINITIAL();break}this.aspTagMode=!0,this.unput(1).appendToken(this.tok.T_OPEN_TAG,2).nextINITIAL();break}}return this.yytext.length>0&&this.tok.T_INLINE_HTML}}},6545:t=>{t.exports={consume_NUM:function(){var t=this.yytext[0],e="."===t;if("0"===t)if("x"===(t=this.input())||"X"===t){if("_"!==(t=this.input())&&this.is_HEX())return this.consume_HNUM();this.unput(t?2:1)}else if("b"===t||"B"===t){if("_"!==(t=this.input())&&"0"===t||"1"===t)return this.consume_BNUM();this.unput(t?2:1)}else if("o"===t||"O"===t){if("_"!==(t=this.input())&&this.is_OCTAL())return this.consume_ONUM();this.unput(t?2:1)}else this.is_NUM()||t&&this.unput(1);for(;this.offset<this.size;){var i=t;if("_"===(t=this.input())){if("_"===i){this.unput(2);break}if("."===i){this.unput(1);break}if("e"===i||"E"===i){this.unput(2);break}}else{if("."===t){if(e){this.unput(1);break}if("_"===i){this.unput(2);break}e=!0;continue}if("e"===t||"E"===t){if("_"===i){this.unput(1);break}var s=2;if("+"!==(t=this.input())&&"-"!==t||(s=3,t=this.input()),this.is_NUM_START())return this.consume_LNUM(),this.tok.T_DNUMBER;this.unput(t?s:s-1);break}}if(!this.is_NUM()){t&&this.unput(1);break}}return e?this.tok.T_DNUMBER:this.yytext.length<18||this.yytext.length<19||19==this.yytext.length&&this.yytext<"9223372036854775808"?this.tok.T_LNUMBER:this.tok.T_DNUMBER},consume_HNUM:function(){for(;this.offset<this.size;){var t=this.input();if(!this.is_HEX()){t&&this.unput(1);break}}return this.tok.T_LNUMBER},consume_LNUM:function(){for(;this.offset<this.size;){var t=this.input();if(!this.is_NUM()){t&&this.unput(1);break}}return this.tok.T_LNUMBER},consume_BNUM:function(){for(var t;this.offset<this.size;)if("0"!==(t=this.input())&&"1"!==t&&"_"!==t){t&&this.unput(1);break}return this.tok.T_LNUMBER},consume_ONUM:function(){for(;this.offset<this.size;){var t=this.input();if(!this.is_OCTAL()){t&&this.unput(1);break}}return this.tok.T_LNUMBER}}},3810:t=>{t.exports={matchST_LOOKING_FOR_PROPERTY:function(){var t=this.input();if("-"===t){if(">"===(t=this.input()))return this.tok.T_OBJECT_OPERATOR;t&&this.unput(1)}else{if(this.is_WHITESPACE())return this.tok.T_WHITESPACE;if(this.is_LABEL_START())return this.consume_LABEL(),this.popState(),this.tok.T_STRING}return this.popState(),t&&this.unput(1),!1},matchST_LOOKING_FOR_VARNAME:function(){var t=this.input();if(this.popState(),this.begin("ST_IN_SCRIPTING"),this.is_LABEL_START()){if(this.consume_LABEL(),"["===(t=this.input())||"}"===t)return this.unput(1),this.tok.T_STRING_VARNAME;this.unput(this.yytext.length)}else t&&this.unput(1);return!1},matchST_VAR_OFFSET:function(){var t=this.input();if(this.is_NUM_START())return this.consume_NUM(),this.tok.T_NUM_STRING;if("]"===t)return this.popState(),"]";if("$"===t){if(this.input(),this.is_LABEL_START())return this.consume_LABEL(),this.tok.T_VARIABLE;throw new Error("Unexpected terminal")}if(this.is_LABEL_START())return this.consume_LABEL(),this.tok.T_STRING;if(this.is_WHITESPACE()||"\\"===t||"'"===t||"#"===t)return this.tok.T_ENCAPSED_AND_WHITESPACE;if("["===t||"{"===t||"}"===t||'"'===t||"`"===t||this.is_TOKEN())return t;throw new Error("Unexpected terminal")}}},8510:t=>{t.exports={matchST_IN_SCRIPTING:function(){var t=this.input();switch(t){case" ":case"\t":case"\n":case"\r":case"\r\n":return this.T_WHITESPACE();case"#":return this.version>=800&&"["===this._input[this.offset]?(this.input(),this.attributeListDepth[++this.attributeIndex]=0,this.begin("ST_ATTRIBUTE"),this.tok.T_ATTRIBUTE):this.T_COMMENT();case"/":return"/"===this._input[this.offset]?this.T_COMMENT():"*"===this._input[this.offset]?(this.input(),this.T_DOC_COMMENT()):this.consume_TOKEN();case"'":return this.T_CONSTANT_ENCAPSED_STRING();case'"':return this.ST_DOUBLE_QUOTES();case"`":return this.begin("ST_BACKQUOTE"),"`";case"?":if(!this.aspTagMode&&this.tryMatch(">")){this.input();var e=this._input[this.offset];return"\n"!==e&&"\r"!==e||this.input(),this.conditionStack.length>1&&this.begin("INITIAL"),this.tok.T_CLOSE_TAG}return this.consume_TOKEN();case"%":return this.aspTagMode&&">"===this._input[this.offset]?(this.input(),"\n"!==(t=this._input[this.offset])&&"\r"!==t||this.input(),this.aspTagMode=!1,this.conditionStack.length>1&&this.begin("INITIAL"),this.tok.T_CLOSE_TAG):this.consume_TOKEN();case"{":return this.begin("ST_IN_SCRIPTING"),"{";case"}":return this.conditionStack.length>2&&this.popState(),"}";default:if("."===t){if(t=this.input(),this.is_NUM_START())return this.consume_NUM();t&&this.unput(1)}if(this.is_NUM_START())return this.consume_NUM();if(this.is_LABEL_START())return this.consume_LABEL().T_STRING();if(this.is_TOKEN())return this.consume_TOKEN()}throw new Error('Bad terminal sequence "'+t+'" at line '+this.yylineno+" (offset "+this.offset+")")},T_WHITESPACE:function(){for(;this.offset<this.size;){var t=this.input();if(" "!==t&&"\t"!==t&&"\n"!==t&&"\r"!==t){t&&this.unput(1);break}}return this.tok.T_WHITESPACE}}},4401:t=>{var e=["\n","\r"],i=["\n","\r",";"],s=i.concat(["\t"," ",",","]",")","/","=","!"]);t.exports={T_CONSTANT_ENCAPSED_STRING:function(){for(var t;this.offset<this.size;)if("\\"==(t=this.input()))this.input();else if("'"==t)break;return this.tok.T_CONSTANT_ENCAPSED_STRING},is_HEREDOC:function(){var t=this.offset;if("<"===this._input[this.offset-1]&&"<"===this._input[this.offset]&&"<"===this._input[this.offset+1]){if(this.offset+=3,this.is_TABSPACE())for(;this.offset<this.size&&(this.offset++,this.is_TABSPACE()););var i=this._input[this.offset-1];if("'"===i||'"'===i?this.offset++:i=null,this.is_LABEL_START()){for(var s=this.offset-1;this.offset<this.size&&(this.offset++,this.is_LABEL()););var n=this._input.substring(s,this.offset-1);if((!i||i===this._input[this.offset-1])&&(i&&this.offset++,e.includes(this._input[this.offset-1])))return this.heredoc_label.label=n,this.heredoc_label.length=n.length,this.heredoc_label.finished=!1,s=this.offset-t,this.offset=t,this.consume(s),"'"===i?this.begin("ST_NOWDOC"):this.begin("ST_HEREDOC"),this.prematch_ENDOFDOC(),this.tok.T_START_HEREDOC}}return this.offset=t,!1},ST_DOUBLE_QUOTES:function(){for(var t;this.offset<this.size;)if("\\"==(t=this.input()))this.input();else{if('"'==t)break;if("$"==t){if("{"==(t=this.input())||this.is_LABEL_START()){this.unput(2);break}t&&this.unput(1)}else if("{"==t){if("$"==(t=this.input())){this.unput(2);break}t&&this.unput(1)}}if('"'==t)return this.tok.T_CONSTANT_ENCAPSED_STRING;var e=1;return"b"!==this.yytext[0]&&"B"!==this.yytext[0]||(e=2),this.yytext.length>2&&this.appendToken(this.tok.T_ENCAPSED_AND_WHITESPACE,this.yytext.length-e),this.unput(this.yytext.length-e),this.begin("ST_DOUBLE_QUOTES"),this.yytext},isDOC_MATCH:function(t,n){var r=this._input[t-2];if(!e.includes(r))return!1;var h=!1,o=!1,a=0,_=this._input[t-1];if(this.version>=703){for(;"\t"===_||" "===_;)" "===_?h=!0:"\t"===_&&(o=!0),_=this._input[t+a],a++;if(t+=a,e.includes(this._input[t-1]))return!1}if(this._input.substring(t-1,t-1+this.heredoc_label.length)===this.heredoc_label.label){var l=this._input[t-1+this.heredoc_label.length];if((this.version>=703?s:i).includes(l)){if(n){if(this.consume(a),h&&o)throw new Error("Parse error:  mixing spaces and tabs in ending marker at line "+this.yylineno+" (offset "+this.offset+")")}else this.heredoc_label.indentation=a,this.heredoc_label.indentation_uses_spaces=h,this.heredoc_label.first_encaps_node=!0;return!0}}return!1},prematch_ENDOFDOC:function(){this.heredoc_label.indentation_uses_spaces=!1,this.heredoc_label.indentation=0,this.heredoc_label.first_encaps_node=!0;for(var t=this.offset+1;t<this._input.length;){if(this.isDOC_MATCH(t,!1))return;if(!e.includes(this._input[t-1]))for(;!e.includes(this._input[t++])&&t<this._input.length;);t++}},matchST_NOWDOC:function(){if(this.isDOC_MATCH(this.offset,!0))return this.consume(this.heredoc_label.length),this.popState(),this.tok.T_END_HEREDOC;for(var t=this._input[this.offset-1];this.offset<this.size;)if(e.includes(t)){if(t=this.input(),this.isDOC_MATCH(this.offset,!0))return this.unput(1).popState(),this.appendToken(this.tok.T_END_HEREDOC,this.heredoc_label.length),this.tok.T_ENCAPSED_AND_WHITESPACE}else t=this.input();return this.tok.T_ENCAPSED_AND_WHITESPACE},matchST_HEREDOC:function(){var t=this.input();if(this.isDOC_MATCH(this.offset,!0))return this.consume(this.heredoc_label.length-1),this.popState(),this.tok.T_END_HEREDOC;for(;this.offset<this.size;)if("\\"===t&&(t=this.input(),e.includes(t)||(t=this.input())),e.includes(t)){if(t=this.input(),this.isDOC_MATCH(this.offset,!0))return this.unput(1).popState(),this.appendToken(this.tok.T_END_HEREDOC,this.heredoc_label.length),this.tok.T_ENCAPSED_AND_WHITESPACE}else if("$"===t){if("{"===(t=this.input()))return this.begin("ST_LOOKING_FOR_VARNAME"),this.yytext.length>2?(this.appendToken(this.tok.T_DOLLAR_OPEN_CURLY_BRACES,2),this.unput(2),this.tok.T_ENCAPSED_AND_WHITESPACE):this.tok.T_DOLLAR_OPEN_CURLY_BRACES;if(this.is_LABEL_START()){var i=this.offset,s=this.consume_VARIABLE();return this.yytext.length>this.offset-i+2?(this.appendToken(s,this.offset-i+2),this.unput(this.offset-i+2),this.tok.T_ENCAPSED_AND_WHITESPACE):s}}else if("{"===t){if("$"===(t=this.input()))return this.begin("ST_IN_SCRIPTING"),this.yytext.length>2?(this.appendToken(this.tok.T_CURLY_OPEN,1),this.unput(2),this.tok.T_ENCAPSED_AND_WHITESPACE):(this.unput(1),this.tok.T_CURLY_OPEN)}else t=this.input();return this.tok.T_ENCAPSED_AND_WHITESPACE},consume_VARIABLE:function(){this.consume_LABEL();var t=this.input();if("["==t)return this.unput(1),this.begin("ST_VAR_OFFSET"),this.tok.T_VARIABLE;if("-"===t){if(">"===this.input())return this.input(),this.is_LABEL_START()&&this.begin("ST_LOOKING_FOR_PROPERTY"),this.unput(3),this.tok.T_VARIABLE;this.unput(2)}else t&&this.unput(1);return this.tok.T_VARIABLE},matchST_BACKQUOTE:function(){var t=this.input();if("$"===t){if("{"===(t=this.input()))return this.begin("ST_LOOKING_FOR_VARNAME"),this.tok.T_DOLLAR_OPEN_CURLY_BRACES;if(this.is_LABEL_START())return this.consume_VARIABLE()}else if("{"===t){if("$"===this._input[this.offset])return this.begin("ST_IN_SCRIPTING"),this.tok.T_CURLY_OPEN}else if("`"===t)return this.popState(),"`";for(;this.offset<this.size;){if("\\"===t)this.input();else{if("`"===t){this.unput(1),this.popState(),this.appendToken("`",1);break}if("$"===t){if("{"===(t=this.input()))return this.begin("ST_LOOKING_FOR_VARNAME"),this.yytext.length>2?(this.appendToken(this.tok.T_DOLLAR_OPEN_CURLY_BRACES,2),this.unput(2),this.tok.T_ENCAPSED_AND_WHITESPACE):this.tok.T_DOLLAR_OPEN_CURLY_BRACES;if(this.is_LABEL_START()){var e=this.offset,i=this.consume_VARIABLE();return this.yytext.length>this.offset-e+2?(this.appendToken(i,this.offset-e+2),this.unput(this.offset-e+2),this.tok.T_ENCAPSED_AND_WHITESPACE):i}continue}if("{"===t){if("$"===(t=this.input()))return this.begin("ST_IN_SCRIPTING"),this.yytext.length>2?(this.appendToken(this.tok.T_CURLY_OPEN,1),this.unput(2),this.tok.T_ENCAPSED_AND_WHITESPACE):(this.unput(1),this.tok.T_CURLY_OPEN);continue}}t=this.input()}return this.tok.T_ENCAPSED_AND_WHITESPACE},matchST_DOUBLE_QUOTES:function(){var t=this.input();if("$"===t){if("{"===(t=this.input()))return this.begin("ST_LOOKING_FOR_VARNAME"),this.tok.T_DOLLAR_OPEN_CURLY_BRACES;if(this.is_LABEL_START())return this.consume_VARIABLE()}else if("{"===t){if("$"===this._input[this.offset])return this.begin("ST_IN_SCRIPTING"),this.tok.T_CURLY_OPEN}else if('"'===t)return this.popState(),'"';for(;this.offset<this.size;){if("\\"===t)this.input();else{if('"'===t){this.unput(1),this.popState(),this.appendToken('"',1);break}if("$"===t){if("{"===(t=this.input()))return this.begin("ST_LOOKING_FOR_VARNAME"),this.yytext.length>2?(this.appendToken(this.tok.T_DOLLAR_OPEN_CURLY_BRACES,2),this.unput(2),this.tok.T_ENCAPSED_AND_WHITESPACE):this.tok.T_DOLLAR_OPEN_CURLY_BRACES;if(this.is_LABEL_START()){var e=this.offset,i=this.consume_VARIABLE();return this.yytext.length>this.offset-e+2?(this.appendToken(i,this.offset-e+2),this.unput(this.offset-e+2),this.tok.T_ENCAPSED_AND_WHITESPACE):i}t&&this.unput(1)}else if("{"===t){if("$"===(t=this.input()))return this.begin("ST_IN_SCRIPTING"),this.yytext.length>2?(this.appendToken(this.tok.T_CURLY_OPEN,1),this.unput(2),this.tok.T_ENCAPSED_AND_WHITESPACE):(this.unput(1),this.tok.T_CURLY_OPEN);t&&this.unput(1)}}t=this.input()}return this.tok.T_ENCAPSED_AND_WHITESPACE}}},4349:t=>{t.exports={T_STRING:function(){var t=this.yytext.toLowerCase(),e=this.keywords[t];if("number"!=typeof e)if("yield"===t)this.version>=700&&this.tryMatch(" from")?(this.consume(5),e=this.tok.T_YIELD_FROM):e=this.tok.T_YIELD;else if(e=this.tok.T_STRING,"b"===t||"B"===t){var i=this.input();if('"'===i)return this.ST_DOUBLE_QUOTES();if("'"===i)return this.T_CONSTANT_ENCAPSED_STRING();i&&this.unput(1)}if(e===this.tok.T_ENUM){if(this.version<801)return this.tok.T_STRING;for(var s=this.offset,n=this.input();" "==n;)n=this.input();var r=!1;if(this.is_LABEL_START()){for(;this.is_LABEL();)n+=this.input();var h=n.slice(0,-1).toLowerCase();r="extends"!==h&&"implements"!==h}return this.unput(this.offset-s),r?this.tok.T_ENUM:this.tok.T_STRING}if(this.offset<this.size&&e!==this.tok.T_YIELD_FROM){var o=this.input();if("\\"===o){e="namespace"===t?this.tok.T_NAME_RELATIVE:this.tok.T_NAME_QUALIFIED;do{if("{"===this._input[this.offset]){this.input();break}this.consume_LABEL(),o=this.input()}while("\\"===o)}o&&this.unput(1)}return e},consume_TOKEN:function(){var t=this._input[this.offset-1],e=this.tokenTerminals[t];return e?e.apply(this,[]):this.yytext},tokenTerminals:{$:function(){return this.offset++,this.is_LABEL_START()?(this.offset--,this.consume_LABEL(),this.tok.T_VARIABLE):(this.offset--,"$")},"-":function(){var t=this._input[this.offset];return">"===t?(this.begin("ST_LOOKING_FOR_PROPERTY").input(),this.tok.T_OBJECT_OPERATOR):"-"===t?(this.input(),this.tok.T_DEC):"="===t?(this.input(),this.tok.T_MINUS_EQUAL):"-"},"\\":function(){if(this.offset<this.size){if(this.input(),this.is_LABEL_START()){var t;do{if("{"===this._input[this.offset]){this.input();break}this.consume_LABEL(),t=this.input()}while("\\"===t);return this.unput(1),this.tok.T_NAME_FULLY_QUALIFIED}this.unput(1)}return this.tok.T_NS_SEPARATOR},"/":function(){return"="===this._input[this.offset]?(this.input(),this.tok.T_DIV_EQUAL):"/"},":":function(){return":"===this._input[this.offset]?(this.input(),this.tok.T_DOUBLE_COLON):":"},"(":function(){var t=this.offset;if(this.input(),this.is_TABSPACE()&&this.consume_TABSPACE().input(),this.is_LABEL_START()){var e=this.yytext.length;this.consume_LABEL();var i=this.yytext.substring(e-1).toLowerCase(),s=this.castKeywords[i];if("number"==typeof s&&(this.input(),this.is_TABSPACE()&&this.consume_TABSPACE().input(),")"===this._input[this.offset-1]))return s}return this.unput(this.offset-t),"("},"=":function(){var t=this._input[this.offset];return">"===t?(this.input(),this.tok.T_DOUBLE_ARROW):"="===t?"="===this._input[this.offset+1]?(this.consume(2),this.tok.T_IS_IDENTICAL):(this.input(),this.tok.T_IS_EQUAL):"="},"+":function(){var t=this._input[this.offset];return"+"===t?(this.input(),this.tok.T_INC):"="===t?(this.input(),this.tok.T_PLUS_EQUAL):"+"},"!":function(){return"="===this._input[this.offset]?"="===this._input[this.offset+1]?(this.consume(2),this.tok.T_IS_NOT_IDENTICAL):(this.input(),this.tok.T_IS_NOT_EQUAL):"!"},"?":function(){return this.version>=700&&"?"===this._input[this.offset]?this.version>=704&&"="===this._input[this.offset+1]?(this.consume(2),this.tok.T_COALESCE_EQUAL):(this.input(),this.tok.T_COALESCE):this.version>=800&&"-"===this._input[this.offset]&&">"===this._input[this.offset+1]?(this.consume(1),this.begin("ST_LOOKING_FOR_PROPERTY").input(),this.tok.T_NULLSAFE_OBJECT_OPERATOR):"?"},"<":function(){var t=this._input[this.offset];return"<"===t?"="===(t=this._input[this.offset+1])?(this.consume(2),this.tok.T_SL_EQUAL):"<"===t&&this.is_HEREDOC()?this.tok.T_START_HEREDOC:(this.input(),this.tok.T_SL):"="===t?(this.input(),this.version>=700&&">"===this._input[this.offset]?(this.input(),this.tok.T_SPACESHIP):this.tok.T_IS_SMALLER_OR_EQUAL):">"===t?(this.input(),this.tok.T_IS_NOT_EQUAL):"<"},">":function(){var t=this._input[this.offset];return"="===t?(this.input(),this.tok.T_IS_GREATER_OR_EQUAL):">"===t?"="===(t=this._input[this.offset+1])?(this.consume(2),this.tok.T_SR_EQUAL):(this.input(),this.tok.T_SR):">"},"*":function(){var t=this._input[this.offset];return"="===t?(this.input(),this.tok.T_MUL_EQUAL):"*"===t?(this.input(),"="===this._input[this.offset]?(this.input(),this.tok.T_POW_EQUAL):this.tok.T_POW):"*"},".":function(){var t=this._input[this.offset];return"="===t?(this.input(),this.tok.T_CONCAT_EQUAL):"."===t&&"."===this._input[this.offset+1]?(this.consume(2),this.tok.T_ELLIPSIS):"."},"%":function(){return"="===this._input[this.offset]?(this.input(),this.tok.T_MOD_EQUAL):"%"},"&":function(){var t=this._input[this.offset];return"="===t?(this.input(),this.tok.T_AND_EQUAL):"&"===t?(this.input(),this.tok.T_BOOLEAN_AND):"&"},"|":function(){var t=this._input[this.offset];return"="===t?(this.input(),this.tok.T_OR_EQUAL):"|"===t?(this.input(),this.tok.T_BOOLEAN_OR):"|"},"^":function(){return"="===this._input[this.offset]?(this.input(),this.tok.T_XOR_EQUAL):"^"}}}},8582:t=>{t.exports={is_NUM:function(){var t=this._input.charCodeAt(this.offset-1);return t>47&&t<58||95===t},is_NUM_START:function(){var t=this._input.charCodeAt(this.offset-1);return t>47&&t<58},is_LABEL:function(){var t=this._input.charCodeAt(this.offset-1);return t>96&&t<123||t>64&&t<91||95===t||t>47&&t<58||t>126},is_LABEL_START:function(){var t=this._input.charCodeAt(this.offset-1);return t>64&&t<91||t>96&&t<123||95===t||t>126},consume_LABEL:function(){for(;this.offset<this.size;){var t=this.input();if(!this.is_LABEL()){t&&this.unput(1);break}}return this},is_TOKEN:function(){var t=this._input[this.offset-1];return-1!==";:,.\\[]()|^&+-/*=%!~$<>?@".indexOf(t)},is_WHITESPACE:function(){var t=this._input[this.offset-1];return" "===t||"\t"===t||"\n"===t||"\r"===t},is_TABSPACE:function(){var t=this._input[this.offset-1];return" "===t||"\t"===t},consume_TABSPACE:function(){for(;this.offset<this.size;){var t=this.input();if(!this.is_TABSPACE()){t&&this.unput(1);break}}return this},is_HEX:function(){var t=this._input.charCodeAt(this.offset-1);return t>47&&t<58||t>64&&t<71||t>96&&t<103||95===t},is_OCTAL:function(){var t=this._input.charCodeAt(this.offset-1);return t>47&&t<56||95===t}}},7259:(t,e,i)=>{var s=i(8822);function n(t){return"."!=t&&","!=t&&!isNaN(parseFloat(t))&&isFinite(t)}var r=function(t,e){this.lexer=t,this.ast=e,this.tok=t.tok,this.EOF=t.EOF,this.token=null,this.prev=null,this.debug=!1,this.version=803,this.extractDoc=!1,this.extractTokens=!1,this.suppressErrors=!1;var i=function(t){return[t,null]};this.entries={IDENTIFIER:new Map([this.tok.T_ABSTRACT,this.tok.T_ARRAY,this.tok.T_AS,this.tok.T_BREAK,this.tok.T_CALLABLE,this.tok.T_CASE,this.tok.T_CATCH,this.tok.T_CLASS,this.tok.T_CLASS_C,this.tok.T_CLONE,this.tok.T_CONST,this.tok.T_CONTINUE,this.tok.T_DECLARE,this.tok.T_DEFAULT,this.tok.T_DIR,this.tok.T_DO,this.tok.T_ECHO,this.tok.T_ELSE,this.tok.T_ELSEIF,this.tok.T_EMPTY,this.tok.T_ENDDECLARE,this.tok.T_ENDFOR,this.tok.T_ENDFOREACH,this.tok.T_ENDIF,this.tok.T_ENDSWITCH,this.tok.T_ENDWHILE,this.tok.T_ENUM,this.tok.T_EVAL,this.tok.T_EXIT,this.tok.T_EXTENDS,this.tok.T_FILE,this.tok.T_FINAL,this.tok.T_FINALLY,this.tok.T_FN,this.tok.T_FOR,this.tok.T_FOREACH,this.tok.T_FUNC_C,this.tok.T_FUNCTION,this.tok.T_GLOBAL,this.tok.T_GOTO,this.tok.T_IF,this.tok.T_IMPLEMENTS,this.tok.T_INCLUDE,this.tok.T_INCLUDE_ONCE,this.tok.T_INSTANCEOF,this.tok.T_INSTEADOF,this.tok.T_INTERFACE,this.tok.T_ISSET,this.tok.T_LINE,this.tok.T_LIST,this.tok.T_LOGICAL_AND,this.tok.T_LOGICAL_OR,this.tok.T_LOGICAL_XOR,this.tok.T_MATCH,this.tok.T_METHOD_C,this.tok.T_NAMESPACE,this.tok.T_NEW,this.tok.T_NS_C,this.tok.T_PRINT,this.tok.T_PRIVATE,this.tok.T_PROTECTED,this.tok.T_PUBLIC,this.tok.T_READ_ONLY,this.tok.T_REQUIRE,this.tok.T_REQUIRE_ONCE,this.tok.T_RETURN,this.tok.T_STATIC,this.tok.T_SWITCH,this.tok.T_THROW,this.tok.T_TRAIT,this.tok.T_TRY,this.tok.T_UNSET,this.tok.T_USE,this.tok.T_VAR,this.tok.T_WHILE,this.tok.T_YIELD].map(i)),VARIABLE:new Map([this.tok.T_VARIABLE,"$","&",this.tok.T_STRING,this.tok.T_NAME_RELATIVE,this.tok.T_NAME_QUALIFIED,this.tok.T_NAME_FULLY_QUALIFIED,this.tok.T_NAMESPACE,this.tok.T_STATIC].map(i)),SCALAR:new Map([this.tok.T_CONSTANT_ENCAPSED_STRING,this.tok.T_START_HEREDOC,this.tok.T_LNUMBER,this.tok.T_DNUMBER,this.tok.T_ARRAY,"[",this.tok.T_CLASS_C,this.tok.T_TRAIT_C,this.tok.T_FUNC_C,this.tok.T_METHOD_C,this.tok.T_LINE,this.tok.T_FILE,this.tok.T_DIR,this.tok.T_NS_C,'"','b"','B"',"-",this.tok.T_NS_SEPARATOR].map(i)),T_MAGIC_CONST:new Map([this.tok.T_CLASS_C,this.tok.T_TRAIT_C,this.tok.T_FUNC_C,this.tok.T_METHOD_C,this.tok.T_LINE,this.tok.T_FILE,this.tok.T_DIR,this.tok.T_NS_C].map(i)),T_MEMBER_FLAGS:new Map([this.tok.T_PUBLIC,this.tok.T_PRIVATE,this.tok.T_PROTECTED,this.tok.T_STATIC,this.tok.T_ABSTRACT,this.tok.T_FINAL].map(i)),EOS:new Map([";",this.EOF,this.tok.T_INLINE_HTML].map(i)),EXPR:new Map(["@","-","+","!","~","(","`",this.tok.T_LIST,this.tok.T_CLONE,this.tok.T_INC,this.tok.T_DEC,this.tok.T_NEW,this.tok.T_ISSET,this.tok.T_EMPTY,this.tok.T_MATCH,this.tok.T_INCLUDE,this.tok.T_INCLUDE_ONCE,this.tok.T_REQUIRE,this.tok.T_REQUIRE_ONCE,this.tok.T_EVAL,this.tok.T_INT_CAST,this.tok.T_DOUBLE_CAST,this.tok.T_STRING_CAST,this.tok.T_ARRAY_CAST,this.tok.T_OBJECT_CAST,this.tok.T_BOOL_CAST,this.tok.T_UNSET_CAST,this.tok.T_EXIT,this.tok.T_PRINT,this.tok.T_YIELD,this.tok.T_STATIC,this.tok.T_FUNCTION,this.tok.T_FN,this.tok.T_VARIABLE,"$",this.tok.T_NS_SEPARATOR,this.tok.T_STRING,this.tok.T_NAME_RELATIVE,this.tok.T_NAME_QUALIFIED,this.tok.T_NAME_FULLY_QUALIFIED,this.tok.T_STRING,this.tok.T_CONSTANT_ENCAPSED_STRING,this.tok.T_START_HEREDOC,this.tok.T_LNUMBER,this.tok.T_DNUMBER,this.tok.T_ARRAY,"[",this.tok.T_CLASS_C,this.tok.T_TRAIT_C,this.tok.T_FUNC_C,this.tok.T_METHOD_C,this.tok.T_LINE,this.tok.T_FILE,this.tok.T_DIR,this.tok.T_NS_C,'"','b"','B"',"-",this.tok.T_NS_SEPARATOR].map(i))}};r.prototype.getTokenName=function(t){return n(t)?t==this.EOF?"the end of file (EOF)":this.lexer.engine.tokens.values[t]:"'"+t+"'"},r.prototype.parse=function(t,e){this._errors=[],this.filename=e||"eval",this.currentNamespace=[""],this.extractDoc?this._docs=[]:this._docs=null,this.extractTokens?this._tokens=[]:this._tokens=null,this._docIndex=0,this._lastNode=null,this.lexer.setInput(t),this.lexer.all_tokens=this.extractTokens,this.lexer.comment_tokens=this.extractDoc,this.length=this.lexer._input.length,this.innerList=!1,this.innerListForm=!1;var i=this.node("program"),s=[];for(this.next();this.token!=this.EOF;)s.push(this.read_start());0===s.length&&this.extractDoc&&this._docs.length>this._docIndex&&s.push(this.node("noop")()),this.prev=[this.lexer.yylloc.last_line,this.lexer.yylloc.last_column,this.lexer.offset];var n=i(s,this._errors,this._docs,this._tokens);if(this.debug){var r=this.ast.checkNodes();if(r.length>0)throw r.forEach((function(t){t.position&&console.log("Node at line "+t.position.line+", column "+t.position.column),console.log(t.stack.join("\n"))})),new Error("Some nodes are not closed")}return n},r.prototype.raiseError=function(t,e,i,s){if(t+=" on line "+this.lexer.yylloc.first_line,!this.suppressErrors){var n=new SyntaxError(t,this.filename,this.lexer.yylloc.first_line);throw n.lineNumber=this.lexer.yylloc.first_line,n.fileName=this.filename,n.columnNumber=this.lexer.yylloc.first_column,n}var r=this.ast.prepare("error",null,this)(t,s,this.lexer.yylloc.first_line,i);return this._errors.push(r),r},r.prototype.error=function(t){var e="Parse Error : syntax error",i=this.getTokenName(this.token),s="";if(this.token!==this.EOF){if(n(this.token)){var r=this.text();r.length>10&&(r=r.substring(0,7)+"..."),i="'"+r+"' ("+i+")"}e+=", unexpected "+i}return t&&!Array.isArray(t)&&((n(t)||1===t.length)&&(s=", expecting "+this.getTokenName(t)),e+=s),this.raiseError(e,s,t,i)},r.prototype.position=function(){return new s(this.lexer.yylloc.first_line,this.lexer.yylloc.first_column,this.lexer.yylloc.first_offset)},r.prototype.node=function(t){if(this.extractDoc){var e=null;this._docIndex<this._docs.length&&(e=this._docs.slice(this._docIndex),this._docIndex=this._docs.length,this.debug&&(console.log(new Error("Append docs on "+t)),console.log(e)));var i=this.ast.prepare(t,e,this);return i.postBuild=function(t){if(this._docIndex<this._docs.length)if(this._lastNode){for(var e=this.prev[2],i=this._docIndex;i<this._docs.length&&!(this._docs[i].offset>e);i++);i>this._docIndex&&(this._lastNode.setTrailingComments(this._docs.slice(this._docIndex,i)),this._docIndex=i)}else this.token===this.EOF&&(t.setTrailingComments(this._docs.slice(this._docIndex)),this._docIndex=this._docs.length);this._lastNode=t}.bind(this),i}return this.ast.prepare(t,null,this)},r.prototype.expectEndOfStatement=function(t){if(";"===this.token)t&&";"===this.lexer.yytext&&t.includeToken(this);else if(this.token!==this.tok.T_INLINE_HTML&&this.token!==this.EOF)return this.error(";"),!1;return this.next(),!0};var h=["parser.next","parser.node","parser.showlog"];r.prototype.showlog=function(){for(var t,e=(new Error).stack.split("\n"),i=2;i<e.length;i++){t=e[i].trim();for(var s=!1,n=0;n<h.length;n++)if(t.substring(3,3+h[n].length)===h[n]){s=!0;break}if(!s)break}return console.log("Line "+this.lexer.yylloc.first_line+" : "+this.getTokenName(this.token)+">"+this.lexer.yytext+"< @--\x3e"+t),this},r.prototype.expect=function(t){if(Array.isArray(t)){if(-1===t.indexOf(this.token))return this.error(t),!1}else if(this.token!=t)return this.error(t),!1;return!0},r.prototype.text=function(){return this.lexer.yytext},r.prototype.next=function(){if(";"===this.token&&";"!==this.lexer.yytext||(this.prev=[this.lexer.yylloc.last_line,this.lexer.yylloc.last_column,this.lexer.offset]),this.lex(),this.debug&&this.showlog(),this.extractDoc)for(;this.token===this.tok.T_COMMENT||this.token===this.tok.T_DOC_COMMENT;)this.token===this.tok.T_COMMENT?this._docs.push(this.read_comment()):this._docs.push(this.read_doc_comment());return this},r.prototype.peek=function(){var t=this.lexer.getState(),e=this.lexer.lex();return this.lexer.setState(t),e},r.prototype.lex=function(){if(this.extractTokens)do{if(this.token=this.lexer.lex()||this.EOF,this.token===this.EOF)return this;var t=this.lexer.yytext;if(t=Object.prototype.hasOwnProperty.call(this.lexer.engine.tokens.values,this.token)?[this.lexer.engine.tokens.values[this.token],t,this.lexer.yylloc.first_line,this.lexer.yylloc.first_offset,this.lexer.offset]:[null,t,this.lexer.yylloc.first_line,this.lexer.yylloc.first_offset,this.lexer.offset],this._tokens.push(t),this.token===this.tok.T_CLOSE_TAG)return this.token=";",this;if(this.token===this.tok.T_OPEN_TAG_WITH_ECHO)return this.token=this.tok.T_ECHO,this}while(this.token===this.tok.T_WHITESPACE||!this.extractDoc&&(this.token===this.tok.T_COMMENT||this.token===this.tok.T_DOC_COMMENT)||this.token===this.tok.T_OPEN_TAG);else this.token=this.lexer.lex()||this.EOF;return this},r.prototype.is=function(t){return Array.isArray(t)?-1!==t.indexOf(this.token):this.entries[t].has(this.token)},[i(5525),i(7072),i(3997),i(6477),i(979),i(8214),i(9461),i(5931),i(9147),i(9219),i(7170),i(6261),i(2478),i(77),i(6077),i(1130)].forEach((function(t){for(var e in t){if(Object.prototype.hasOwnProperty.call(r.prototype,e))throw new Error("Function "+e+" is already defined - collision");r.prototype[e]=t[e]}})),t.exports=r},5525:t=>{t.exports={read_array:function(){var t=null,e=!1,i=this.node("array");this.token===this.tok.T_ARRAY?(this.next().expect("("),t=")"):(e=!0,t="]");var s=[];return this.next().token!==t&&(s=this.read_array_pair_list(e)),this.expect(t),this.next(),i(e,s)},read_array_pair_list:function(t){var e=this;return this.read_list((function(){return e.read_array_pair(t)}),",",!0)},read_array_pair:function(t){if(!(!t&&")"===this.token||t&&"]"===this.token)){if(","===this.token)return this.node("noop")();var e=this.node("entry"),i=null,s=null,n=!1,r=!1;if("&"===this.token)this.next(),n=!0,s=this.read_variable(!0,!1);else if(this.token===this.tok.T_ELLIPSIS&&this.version>=704)this.next(),"&"===this.token&&this.error(),r=!0,s=this.read_expr();else{var h=this.read_expr();this.token===this.tok.T_DOUBLE_ARROW?(this.next(),i=h,"&"===this.token?(this.next(),n=!0,s=this.read_variable(!0,!1)):s=this.read_expr()):s=h}return e(i,s,n,r)}}}},7072:t=>{function e(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var s,n,r,h,o=[],a=!0,_=!1;try{if(r=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;a=!1}else for(;!(a=(s=r.call(i)).done)&&(o.push(s.value),o.length!==e);a=!0);}catch(t){_=!0,n=t}finally{try{if(!a&&null!=i.return&&(h=i.return(),Object(h)!==h))return}finally{if(_)throw n}}return o}}(t,e)||s(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||s(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){if(t){if("string"==typeof t)return n(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?n(t,e):void 0}}function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=Array(e);i<e;i++)s[i]=t[i];return s}t.exports={read_class_declaration_statement:function(t){var e=this.node("class"),i=this.read_class_modifiers();if(this.token!==this.tok.T_CLASS)return this.error(this.tok.T_CLASS),this.next(),null;this.next().expect(this.tok.T_STRING);var s=this.node("identifier"),n=this.text();this.next(),s=s(n);var r=this.read_extends_from(),h=this.read_implements_list();this.expect("{");var o=e(s,r,h,this.next().read_class_body(!0,!1),i);return t&&(o.attrGroups=t),o},read_class_modifiers:function(){var t=this.read_class_modifier({readonly:0,final_or_abstract:0});return[0,0,t.final_or_abstract,t.readonly]},read_class_modifier:function(t){return this.token===this.tok.T_READ_ONLY?(this.next(),t.readonly=1,t=this.read_class_modifier(t)):0===t.final_or_abstract&&this.token===this.tok.T_ABSTRACT?(this.next(),t.final_or_abstract=1,t=this.read_class_modifier(t)):0===t.final_or_abstract&&this.token===this.tok.T_FINAL&&(this.next(),t.final_or_abstract=2,t=this.read_class_modifier(t)),t},read_class_body:function(t,e){for(var s=[],n=[];this.token!==this.EOF&&"}"!==this.token;)if(this.token!==this.tok.T_COMMENT)if(this.token!==this.tok.T_DOC_COMMENT)if(this.token!==this.tok.T_USE)if(e&&this.token===this.tok.T_CASE){var r=this.read_enum_case();this.expect(";")&&this.next(),s=s.concat(r)}else{this.token===this.tok.T_ATTRIBUTE&&(n=this.read_attr_list());var h=this.position(),o=this.read_member_flags(!1);if(this.token!==this.tok.T_CONST)if(t&&this.token===this.tok.T_VAR&&(this.next().expect(this.tok.T_VARIABLE),o[0]=null,o[1]=0),this.token===this.tok.T_FUNCTION)s.push(this.read_function(!1,o,n,h)),n=[];else if(t&&(this.token===this.tok.T_VARIABLE||this.version>=801&&this.token===this.tok.T_READ_ONLY||this.version>=704&&("?"===this.token||this.token===this.tok.T_ARRAY||this.token===this.tok.T_CALLABLE||this.token===this.tok.T_NAMESPACE||this.token===this.tok.T_NAME_FULLY_QUALIFIED||this.token===this.tok.T_NAME_QUALIFIED||this.token===this.tok.T_NAME_RELATIVE||this.token===this.tok.T_NS_SEPARATOR||this.token===this.tok.T_STRING))){var a=this.read_variable_list(o,n);n=[],this.expect(";"),this.next(),s=s.concat(a)}else this.error([this.tok.T_CONST].concat(i(t?[this.tok.T_VARIABLE]:[]),i(e?[this.tok.T_CASE]:[]),[this.tok.T_FUNCTION])),this.next();else{var _=this.read_constant_list(o,n);this.expect(";")&&this.next(),s=s.concat(_)}}else s=s.concat(this.read_trait_use_statement());else s.push(this.read_doc_comment());else s.push(this.read_comment());return this.expect("}"),this.next(),s},read_variable_list:function(t,i){var s=this.node("propertystatement"),n=this.read_list((function(){var t=this.node("property"),s=!1;this.token===this.tok.T_READ_ONLY&&(s=!0,this.next());var n=e(this.read_optional_type(),2),r=n[0],h=n[1];this.expect(this.tok.T_VARIABLE);var o=this.node("identifier"),a=this.text().substring(1);this.next(),o=o(a);var _=null;return this.expect([",",";","="]),"="===this.token&&(_=this.next().read_expr()),t(o,_,s,r,h,i||[])}),",");return s(null,n,t)},read_constant_list:function(t,i){this.expect(this.tok.T_CONST)&&this.next();var s=e(this.version>=803?this.read_optional_type():[!1,null],2),n=s[0],r=s[1],h=this.node("classconstant"),o=this.read_list((function(){var t=this.node("constant"),e=null,i=null;if(this.token===this.tok.T_STRING||this.version>=700&&this.is("IDENTIFIER")){e=this.node("identifier");var s=this.text();this.next(),e=e(s)}else this.expect("IDENTIFIER");return this.expect("=")&&(i=this.next().read_expr()),t(e,i)}),",");return h(null,o,t,n,r,i||[])},read_member_flags:function(t){var e=[-1,-1,-1];if(this.is("T_MEMBER_FLAGS")){var i=0,s=0;do{switch(this.token){case this.tok.T_PUBLIC:i=0,s=0;break;case this.tok.T_PROTECTED:i=0,s=1;break;case this.tok.T_PRIVATE:i=0,s=2;break;case this.tok.T_STATIC:i=1,s=1;break;case this.tok.T_ABSTRACT:i=2,s=1;break;case this.tok.T_FINAL:i=2,s=2}t&&(0===i&&2===s?(this.expect([this.tok.T_PUBLIC,this.tok.T_PROTECTED]),s=-1):2===i&&1===s&&(this.error(),s=-1)),-1!==e[i]?this.error():-1!==s&&(e[i]=s)}while(this.next().is("T_MEMBER_FLAGS"))}return-1===e[1]&&(e[1]=0),-1===e[2]&&(e[2]=0),e},read_optional_type:function(){var t="?"===this.token;if(t&&this.next(),"="===this.peek())return[!1,null];var e=this.read_types();if(t&&!e&&this.raiseError("Expecting a type definition combined with nullable operator"),!t&&!e)return[!1,null];if("|"===this.token){e=[e];do{this.next();var i=this.read_type();if(!i){this.raiseError("Expecting a type definition");break}e.push(i)}while("|"===this.token)}return[t,e]},read_interface_declaration_statement:function(t){var e=this.node("interface");if(this.token!==this.tok.T_INTERFACE)return this.error(this.tok.T_INTERFACE),this.next(),null;this.next().expect(this.tok.T_STRING);var i=this.node("identifier"),s=this.text();this.next(),i=i(s);var n=this.read_interface_extends_list();return this.expect("{"),e(i,n,this.next().read_interface_body(),t||[])},read_interface_body:function(){for(var t=[],e=[];this.token!==this.EOF&&"}"!==this.token;)if(this.token!==this.tok.T_COMMENT)if(this.token!==this.tok.T_DOC_COMMENT){var i=this.position();e=this.read_attr_list();var s=this.read_member_flags(!0);if(this.token===this.tok.T_CONST){var n=this.read_constant_list(s,e);this.expect(";")&&this.next(),t=t.concat(n),e=[]}else if(this.token===this.tok.T_FUNCTION){var r=this.read_function_declaration(2,s,e,i);r.parseFlags(s),t.push(r),this.expect(";")&&this.next(),e=[]}else this.error([this.tok.T_CONST,this.tok.T_FUNCTION]),this.next()}else t.push(this.read_doc_comment());else t.push(this.read_comment());return this.expect("}")&&this.next(),t},read_trait_declaration_statement:function(){var t=this.node("trait");if(this.token!==this.tok.T_TRAIT)return this.error(this.tok.T_TRAIT),this.next(),null;this.next().expect(this.tok.T_STRING);var e=this.node("identifier"),i=this.text();return this.next(),e=e(i),this.expect("{"),t(e,this.next().read_class_body(!0,!1))},read_trait_use_statement:function(){var t=this.node("traituse");this.expect(this.tok.T_USE)&&this.next();for(var e=[this.read_namespace_name()],i=null;","===this.token;)e.push(this.next().read_namespace_name());if("{"===this.token){for(i=[];this.next().token!==this.EOF&&"}"!==this.token;)i.push(this.read_trait_use_alias()),this.expect(";");this.expect("}")&&this.next()}else this.expect(";")&&this.next();return t(e,i)},read_trait_use_alias:function(){var t,e=this.node(),i=null;if(this.is("IDENTIFIER")){t=this.node("identifier");var s=this.text();this.next(),t=t(s)}else if(t=this.read_namespace_name(),this.token===this.tok.T_DOUBLE_COLON)if(this.next(),this.token===this.tok.T_STRING||this.version>=700&&this.is("IDENTIFIER")){i=t,t=this.node("identifier");var n=this.text();this.next(),t=t(n)}else this.expect(this.tok.T_STRING);else t=t.name;if(this.token===this.tok.T_INSTEADOF)return e("traitprecedence",i,t,this.next().read_name_list());if(this.token===this.tok.T_AS){var r=null,h=null;if(this.next().is("T_MEMBER_FLAGS")&&(r=this.read_member_flags()),this.token===this.tok.T_STRING||this.version>=700&&this.is("IDENTIFIER")){h=this.node("identifier");var o=this.text();this.next(),h=h(o)}else!1===r&&this.expect(this.tok.T_STRING);return e("traitalias",i,t,h,r)}return this.expect([this.tok.T_AS,this.tok.T_INSTEADOF]),e("traitalias",i,t,null,null)}}},3997:t=>{t.exports={read_comment:function(){var t=this.text(),e=this.ast.prepare("/*"===t.substring(0,2)?"commentblock":"commentline",null,this),i=this.lexer.yylloc.first_offset,s=this.prev;return this.prev=[this.lexer.yylloc.last_line,this.lexer.yylloc.last_column,this.lexer.offset],this.lex(),(e=e(t)).offset=i,this.prev=s,e},read_doc_comment:function(){var t=this.ast.prepare("commentblock",null,this),e=this.lexer.yylloc.first_offset,i=this.text(),s=this.prev;return this.prev=[this.lexer.yylloc.last_line,this.lexer.yylloc.last_column,this.lexer.offset],this.lex(),(t=t(i)).offset=e,this.prev=s,t}}},979:t=>{t.exports={read_enum_declaration_statement:function(t){var e=this.node("enum");if(!this.expect(this.tok.T_ENUM))return null;this.next().expect(this.tok.T_STRING);var i=this.node("identifier"),s=this.text();this.next(),i=i(s);var n=this.read_enum_value_type(),r=this.read_implements_list();this.expect("{");var h=e(i,n,r,this.next().read_class_body(!1,!0));return t&&(h.attrGroups=t),h},read_enum_value_type:function(){return":"===this.token?this.next().read_namespace_name():null},read_enum_case:function(){this.expect(this.tok.T_CASE);var t=this.node("enumcase"),e=this.node("identifier"),i=this.next().text();this.next(),e=e(i);var s="="===this.token?this.next().read_expr():null;return this.expect(";"),t(e,s)}}},6477:t=>{t.exports={read_expr:function(t){var e=this.node();if("@"===this.token)return t||(t=this.next().read_expr()),e("silent",t);if(t||(t=this.read_expr_item()),"|"===this.token)return e("bin","|",t,this.next().read_expr());if("&"===this.token)return e("bin","&",t,this.next().read_expr());if("^"===this.token)return e("bin","^",t,this.next().read_expr());if("."===this.token)return e("bin",".",t,this.next().read_expr());if("+"===this.token)return e("bin","+",t,this.next().read_expr());if("-"===this.token)return e("bin","-",t,this.next().read_expr());if("*"===this.token)return e("bin","*",t,this.next().read_expr());if("/"===this.token)return e("bin","/",t,this.next().read_expr());if("%"===this.token)return e("bin","%",t,this.next().read_expr());if(this.token===this.tok.T_POW)return e("bin","**",t,this.next().read_expr());if(this.token===this.tok.T_SL)return e("bin","<<",t,this.next().read_expr());if(this.token===this.tok.T_SR)return e("bin",">>",t,this.next().read_expr());if(this.token===this.tok.T_BOOLEAN_OR)return e("bin","||",t,this.next().read_expr());if(this.token===this.tok.T_LOGICAL_OR)return e("bin","or",t,this.next().read_expr());if(this.token===this.tok.T_BOOLEAN_AND)return e("bin","&&",t,this.next().read_expr());if(this.token===this.tok.T_LOGICAL_AND)return e("bin","and",t,this.next().read_expr());if(this.token===this.tok.T_LOGICAL_XOR)return e("bin","xor",t,this.next().read_expr());if(this.token===this.tok.T_IS_IDENTICAL)return e("bin","===",t,this.next().read_expr());if(this.token===this.tok.T_IS_NOT_IDENTICAL)return e("bin","!==",t,this.next().read_expr());if(this.token===this.tok.T_IS_EQUAL)return e("bin","==",t,this.next().read_expr());if(this.token===this.tok.T_IS_NOT_EQUAL)return e("bin","!=",t,this.next().read_expr());if("<"===this.token)return e("bin","<",t,this.next().read_expr());if(">"===this.token)return e("bin",">",t,this.next().read_expr());if(this.token===this.tok.T_IS_SMALLER_OR_EQUAL)return e("bin","<=",t,this.next().read_expr());if(this.token===this.tok.T_IS_GREATER_OR_EQUAL)return e("bin",">=",t,this.next().read_expr());if(this.token===this.tok.T_SPACESHIP)return e("bin","<=>",t,this.next().read_expr());if(this.token===this.tok.T_INSTANCEOF&&(t=e("bin","instanceof",t,this.next().read_class_name_reference()),";"!==this.token&&this.token!==this.tok.T_INLINE_HTML&&this.token!==this.EOF&&(t=this.read_expr(t))),this.token===this.tok.T_COALESCE)return e("bin","??",t,this.next().read_expr());if("?"===this.token){var i=null;return":"!==this.next().token&&(i=this.read_expr()),this.expect(":")&&this.next(),e("retif",t,i,this.read_expr())}return e.destroy(t),t},read_expr_cast:function(t){return this.node("cast")(t,this.text(),this.next().read_expr())},read_isset_variable:function(){return this.read_expr()},read_isset_variables:function(){return this.read_function_list(this.read_isset_variable,",")},read_internal_functions_in_yacc:function(){var t=null;switch(this.token){case this.tok.T_ISSET:t=this.node("isset"),this.next().expect("(")&&this.next();var e=this.read_isset_variables();this.expect(")")&&this.next(),t=t(e);break;case this.tok.T_EMPTY:t=this.node("empty"),this.next().expect("(")&&this.next();var i=this.read_expr();this.expect(")")&&this.next(),t=t(i);break;case this.tok.T_INCLUDE:t=this.node("include")(!1,!1,this.next().read_expr());break;case this.tok.T_INCLUDE_ONCE:t=this.node("include")(!0,!1,this.next().read_expr());break;case this.tok.T_EVAL:t=this.node("eval"),this.next().expect("(")&&this.next();var s=this.read_expr();this.expect(")")&&this.next(),t=t(s);break;case this.tok.T_REQUIRE:t=this.node("include")(!1,!0,this.next().read_expr());break;case this.tok.T_REQUIRE_ONCE:t=this.node("include")(!0,!0,this.next().read_expr())}return t},read_optional_expr:function(t){return this.token!==t?this.read_expr():null},read_exit_expr:function(){var t=null;return"("===this.token&&(this.next(),t=this.read_optional_expr(")"),this.expect(")")&&this.next()),t},read_expr_item:function(){var t,e,i=[];if("+"===this.token)return this.node("unary")("+",this.next().read_expr());if("-"===this.token)return this.node("unary")("-",this.next().read_expr());if("!"===this.token)return this.node("unary")("!",this.next().read_expr());if("~"===this.token)return this.node("unary")("~",this.next().read_expr());if("("===this.token)return(e=this.next().read_expr()).parenthesizedExpression=!0,this.expect(")")&&this.next(),this.handleDereferencable(e);if("`"===this.token)return this.read_encapsed_string("`");if(this.token===this.tok.T_LIST){var s=null,n=this.innerList;t=this.node("list"),n||(s=this.node("assign")),this.next().expect("(")&&this.next(),this.innerList||(this.innerList=!0);var r=this.read_array_pair_list(!1);this.expect(")")&&this.next();for(var h=!1,o=0;o<r.length;o++)if(null!==r[o]&&"noop"!==r[o].kind){h=!0;break}return h||this.raiseError("Fatal Error :  Cannot use empty list on line "+this.lexer.yylloc.first_line),n?t(r,!1):(this.innerList=!1,this.expect("=")?s(t(r,!1),this.next().read_expr(),"="):t(r,!1))}if(this.token===this.tok.T_ATTRIBUTE&&(i=this.read_attr_list()),this.token===this.tok.T_CLONE)return this.node("clone")(this.next().read_expr());switch(this.token){case this.tok.T_INC:return this.node("pre")("+",this.next().read_variable(!1,!1));case this.tok.T_DEC:return this.node("pre")("-",this.next().read_variable(!1,!1));case this.tok.T_NEW:return this.read_new_expr();case this.tok.T_ISSET:case this.tok.T_EMPTY:case this.tok.T_INCLUDE:case this.tok.T_INCLUDE_ONCE:case this.tok.T_EVAL:case this.tok.T_REQUIRE:case this.tok.T_REQUIRE_ONCE:return this.read_internal_functions_in_yacc();case this.tok.T_MATCH:return this.read_match_expression();case this.tok.T_INT_CAST:return this.read_expr_cast("int");case this.tok.T_DOUBLE_CAST:return this.read_expr_cast("float");case this.tok.T_STRING_CAST:return this.read_expr_cast(-1!==this.text().indexOf("binary")?"binary":"string");case this.tok.T_ARRAY_CAST:return this.read_expr_cast("array");case this.tok.T_OBJECT_CAST:return this.read_expr_cast("object");case this.tok.T_BOOL_CAST:return this.read_expr_cast("bool");case this.tok.T_UNSET_CAST:return this.read_expr_cast("unset");case this.tok.T_THROW:return this.version<800&&this.raiseError("PHP 8+ is required to use throw as an expression"),this.node("throw")(this.next().read_expr());case this.tok.T_EXIT:var a="die"===this.lexer.yytext.toLowerCase();return t=this.node("exit"),this.next(),t(this.read_exit_expr(),a);case this.tok.T_PRINT:return this.node("print")(this.next().read_expr());case this.tok.T_YIELD:var _=null,l=null;return t=this.node("yield"),this.next().is("EXPR")&&(_=this.read_expr(),this.token===this.tok.T_DOUBLE_ARROW&&(l=_,_=this.next().read_expr())),t(_,l);case this.tok.T_YIELD_FROM:return(t=this.node("yieldfrom"))(e=this.next().read_expr());case this.tok.T_FN:case this.tok.T_FUNCTION:return this.read_inline_function(void 0,i);case this.tok.T_STATIC:var c=[this.token,this.lexer.getState()];if(this.next(),this.token===this.tok.T_FUNCTION||this.version>=704&&this.token===this.tok.T_FN)return this.read_inline_function([0,1,0],i);this.lexer.tokens.push(c),this.next()}if(this.is("VARIABLE")){t=this.node();var u="identifier"===(e=this.read_variable(!1,!1)).kind||"staticlookup"===e.kind&&"identifier"===e.offset.kind;switch(this.token){case"=":return u&&this.error("VARIABLE"),"&"==this.next().token?this.read_assignref(t,e):t("assign",e,this.read_expr(),"=");case this.tok.T_PLUS_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"+=");case this.tok.T_MINUS_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"-=");case this.tok.T_MUL_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"*=");case this.tok.T_POW_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"**=");case this.tok.T_DIV_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"/=");case this.tok.T_CONCAT_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),".=");case this.tok.T_MOD_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"%=");case this.tok.T_AND_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"&=");case this.tok.T_OR_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"|=");case this.tok.T_XOR_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"^=");case this.tok.T_SL_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"<<=");case this.tok.T_SR_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),">>=");case this.tok.T_COALESCE_EQUAL:return u&&this.error("VARIABLE"),t("assign",e,this.next().read_expr(),"??=");case this.tok.T_INC:return u&&this.error("VARIABLE"),this.next(),t("post","+",e);case this.tok.T_DEC:return u&&this.error("VARIABLE"),this.next(),t("post","-",e);default:t.destroy(e)}}else{if(this.is("SCALAR")){if(t=this.node(),"array"===(e=this.read_scalar()).kind&&e.shortForm&&"="===this.token){var p=this.convertToList(e);return e.loc&&(p.loc=e.loc),t("assign",p,this.next().read_expr(),"=")}return t.destroy(e),this.handleDereferencable(e)}this.error("EXPR"),this.next()}return e},convertToList:function(t){var e=this,i=t.items.map((function(t){return t.value&&"array"===t.value.kind&&t.value.shortForm&&(t.value=e.convertToList(t.value)),t})),s=this.node("list")(i,!0);return t.loc&&(s.loc=t.loc),t.leadingComments&&(s.leadingComments=t.leadingComments),t.trailingComments&&(s.trailingComments=t.trailingComments),s},read_assignref:function(t,e){var i;return this.next(),this.token===this.tok.T_NEW?(this.version>=700&&this.error(),i=this.read_new_expr()):i=this.read_variable(!1,!1),t("assignref",e,i)},read_inline_function:function(t,e){if(this.token===this.tok.T_FUNCTION){var i=this.read_function(!0,t,e);return i.attrGroups=e,i}!this.version>=704&&this.raiseError("Arrow Functions are not allowed");var s=this.node("arrowfunc");this.expect(this.tok.T_FN)&&this.next();var n=this.is_reference();this.expect("(")&&this.next();var r=this.read_parameter_list();this.expect(")")&&this.next();var h=!1,o=null;":"===this.token&&("?"===this.next().token&&(h=!0,this.next()),o=this.read_types()),this.expect(this.tok.T_DOUBLE_ARROW)&&this.next();var a=s(r,n,this.read_expr(),o,h,!!t);return a.attrGroups=e,a},read_match_expression:function(){var t,e,i=this.node("match");return this.expect(this.tok.T_MATCH)&&this.next(),this.version<800&&this.raiseError("Match statements are not allowed before PHP 8"),this.expect("(")&&this.next(),t=this.read_expr(),this.expect(")")&&this.next(),this.expect("{")&&this.next(),e=this.read_match_arms(),this.expect("}")&&this.next(),i(t,e)},read_match_arms:function(){var t=this;return this.read_list((function(){return t.read_match_arm()}),",",!0)},read_match_arm:function(){if("}"!==this.token)return this.node("matcharm")(this.read_match_arm_conds(),this.read_expr())},read_match_arm_conds:function(){var t=[];if(this.token===this.tok.T_DEFAULT)t=null,this.next();else for(t.push(this.read_expr());","===this.token;){if(this.next(),this.token===this.tok.T_DOUBLE_ARROW)return this.next(),t;t.push(this.read_expr())}return this.expect(this.tok.T_DOUBLE_ARROW)&&this.next(),t},read_attribute:function(){var t=this.text(),e=[];return this.next(),"("===this.token&&(e=this.read_argument_list()),this.node("attribute")(t,e)},read_attr_list:function(){var t=[];if(this.token===this.tok.T_ATTRIBUTE)do{var e=this.node("attrgroup")([]);for(this.next(),e.attrs.push(this.read_attribute());","===this.token;)this.next(),"]"!==this.token&&e.attrs.push(this.read_attribute());t.push(e),this.expect("]"),this.next()}while(this.token===this.tok.T_ATTRIBUTE);return t},read_new_expr:function(){var t=this.node("new");this.expect(this.tok.T_NEW)&&this.next();var e=[];if("("===this.token){this.next();var i=this.read_expr();return this.expect(")"),this.next(),"("===this.token&&(e=this.read_argument_list()),t(i,e)}var s=this.read_attr_list();if(this.token===this.tok.T_CLASS){var n=this.node("class");"("===this.next().token&&(e=this.read_argument_list());var r=this.read_extends_from(),h=this.read_implements_list(),o=null;this.expect("{")&&(o=this.next().read_class_body(!0,!1));var a=n(null,r,h,o,[0,0,0]);return a.attrGroups=s,t(a,e)}for(var _=this.read_new_class_name();"["===this.token;){var l=this.node("offsetlookup"),c=this.next().read_encaps_var_offset();this.expect("]")&&this.next(),_=l(_,c)}return"("===this.token&&(e=this.read_argument_list()),t(_,e)},read_new_class_name:function(){if(this.token===this.tok.T_NS_SEPARATOR||this.token===this.tok.T_NAME_RELATIVE||this.token===this.tok.T_NAME_QUALIFIED||this.token===this.tok.T_NAME_FULLY_QUALIFIED||this.token===this.tok.T_STRING||this.token===this.tok.T_NAMESPACE){var t=this.read_namespace_name(!0);return this.token===this.tok.T_DOUBLE_COLON&&(t=this.read_static_getter(t)),t}if(this.is("VARIABLE"))return this.read_variable(!0,!1);this.expect([this.tok.T_STRING,"VARIABLE"])},handleDereferencable:function(t){for(;this.token!==this.EOF;)if(this.token===this.tok.T_OBJECT_OPERATOR||this.token===this.tok.T_DOUBLE_COLON)t=this.recursive_variable_chain_scan(t,!1,!1,!0);else if(this.token===this.tok.T_CURLY_OPEN||"["===this.token)t=this.read_dereferencable(t);else{if("("!==this.token)return t;t=this.node("call")(t,this.read_argument_list())}return t}}},8214:t=>{t.exports={is_reference:function(){return"&"===this.token&&(this.next(),!0)},is_variadic:function(){return this.token===this.tok.T_ELLIPSIS&&(this.next(),!0)},read_function:function(t,e,i,s){var n=this.read_function_declaration(t?1:e?2:0,e&&1===e[1],i||[],s);return e&&1==e[2]?(n.parseFlags(e),this.expect(";")&&this.next()):(this.expect("{")&&(n.body=this.read_code_block(!1),n.loc&&n.body.loc&&(n.loc.end=n.body.loc.end)),!t&&e&&n.parseFlags(e)),n},read_function_declaration:function(t,e,i,s){var n=this,r="function";1===t?r="closure":2===t&&(r="method");var h=this.node(r);this.expect(this.tok.T_FUNCTION)&&this.next();var o=this.is_reference(),a=!1,_=[],l=null,c=!1;if(1!==t){var u=this.node("identifier");2===t?this.version>=700?this.token===this.tok.T_STRING||this.is("IDENTIFIER")?(a=this.text(),this.next()):this.version<704&&this.error("IDENTIFIER"):this.token===this.tok.T_STRING?(a=this.text(),this.next()):this.error("IDENTIFIER"):this.version>=700?this.token===this.tok.T_STRING?(a=this.text(),this.next()):this.version>=704?this.expect("(")||this.next():(this.error(this.tok.T_STRING),this.next()):(this.expect(this.tok.T_STRING)&&(a=this.text()),this.next()),a=u(a)}this.expect("(")&&this.next();var p=this.read_parameter_list("__construct"===a.name);this.expect(")")&&this.next(),1===t&&(_=this.read_lexical_vars()),":"===this.token&&("?"===this.next().token&&(c=!0,this.next()),l=this.read_types());var f;return(f=1===t?h(p,o,_,l,c,e):h(a,p,o,l,c)).attrGroups=i||[],s&&f.loc&&(f.loc.start=s,f.loc.source&&(f.loc.source=n.lexer._input.substr(f.loc.start.offset,f.loc.end.offset-f.loc.start.offset))),f},read_lexical_vars:function(){var t=[];return this.token===this.tok.T_USE&&(this.next(),this.expect("(")&&this.next(),t=this.read_lexical_var_list(),this.expect(")")&&this.next()),t},read_list_with_dangling_comma:function(t){for(var e=[];this.token!=this.EOF;){if(e.push(t()),","!=this.token){if(")"==this.token)break;this.error([",",")"]);break}if(this.next(),this.version>=800&&")"===this.token)return e}return e},read_lexical_var_list:function(){return this.read_list_with_dangling_comma(this.read_lexical_var.bind(this))},read_lexical_var:function(){if("&"===this.token)return this.read_byref(this.read_lexical_var.bind(this));var t=this.node("variable");this.expect(this.tok.T_VARIABLE);var e=this.text().substring(1);return this.next(),t(e,!1)},read_parameter_list:function(t){if(")"!==this.token){var e=!1;return this.read_list_with_dangling_comma(function(){var i=this.read_parameter(t);return i&&(e&&this.raiseError("Unexpected parameter after a variadic parameter"),i.variadic&&(e=!0)),i}.bind(this),",")}return[]},read_parameter:function(t){var e,i=this.node("parameter"),s=null,n=null,r=!1,h=!1,o=[];this.token===this.tok.T_ATTRIBUTE&&(o=this.read_attr_list()),this.version>=801&&this.token===this.tok.T_READ_ONLY&&(t?(this.next(),h=!0):this.raiseError("readonly properties can be used only on class constructor"));var a=this.read_promoted();!h&&this.version>=801&&this.token===this.tok.T_READ_ONLY&&(t?(this.next(),h=!0):this.raiseError("readonly properties can be used only on class constructor")),"?"===this.token&&(this.next(),r=!0),e=this.read_types(),r&&!e&&this.raiseError("Expecting a type definition combined with nullable operator");var _=this.is_reference(),l=this.is_variadic();if(this.expect(this.tok.T_VARIABLE)){s=this.node("identifier");var c=this.text().substring(1);this.next(),s=s(c)}"="==this.token&&(n=this.next().read_expr());var u=i(s,e,n,_,l,h,r,a);return o&&(u.attrGroups=o),u},read_types:function(){var t="unset",e="union",i="intersection",s=[],n=t,r=this.read_type();if(!r)return null;for(s.push(r);"|"===this.token||this.version>=801&&"&"===this.token;){var h=this.peek();if(h===this.tok.T_ELLIPSIS||h===this.tok.T_VARIABLE)break;n===t?n="|"===this.token?e:i:(n===e&&"|"!==this.token||n===i&&"&"!==this.token)&&this.raiseError('Unexpect token "'+this.token+'", "|" and "&" can not be mixed'),this.next(),s.push(this.read_type())}return 1===s.length?s[0]:n===i?this.node("intersectiontype")(s):this.node("uniontype")(s)},read_promoted:function(){return this.token===this.tok.T_PUBLIC?(this.next(),1):this.token===this.tok.T_PROTECTED?(this.next(),2):this.token===this.tok.T_PRIVATE?(this.next(),4):0},read_argument_list:function(){var t=[];return this.expect("(")&&this.next(),this.version>=801&&this.token===this.tok.T_ELLIPSIS&&")"===this.peek()?(t.push(this.node("variadicplaceholder")()),this.next()):")"!==this.token&&(t=this.read_non_empty_argument_list()),this.expect(")")&&this.next(),t},read_non_empty_argument_list:function(){var t=!1;return this.read_function_list(function(){var e=this.read_argument();if(e){var i="variadic"===e.kind;t&&!i&&this.raiseError("Unexpected non-variadic argument after a variadic argument"),i&&(t=!0)}return e}.bind(this),",")},read_argument:function(){return this.token===this.tok.T_ELLIPSIS?this.node("variadic")(this.next().read_expr()):this.token!==this.tok.T_STRING&&!Object.values(this.lexer.keywords).includes(this.token)||":"!==this.peek()?this.read_expr():(this.version<800&&this.raiseError("PHP 8+ is required to use named arguments"),this.node("namedargument")(this.text(),this.next().next().read_expr()))},read_type:function(){var t=this.node();if(this.token===this.tok.T_ARRAY||this.token===this.tok.T_CALLABLE){var e=this.text();return this.next(),t("typereference",e.toLowerCase(),e)}if(this.token===this.tok.T_NAME_RELATIVE||this.token===this.tok.T_NAME_QUALIFIED||this.token===this.tok.T_NAME_FULLY_QUALIFIED||this.token===this.tok.T_STRING||this.token===this.tok.T_STATIC){var i=this.text(),s=[this.token,this.lexer.getState()];return this.next(),this.token!==this.tok.T_NS_SEPARATOR&&this.ast.typereference.types.indexOf(i.toLowerCase())>-1?t("typereference",i.toLowerCase(),i):(this.lexer.tokens.push(s),this.next(),t.destroy(),this.read_namespace_name())}return t.destroy(),null}}},9461:t=>{t.exports={read_if:function(){var t=this.node("if"),e=this.next().read_if_expr(),i=null,s=null,n=!1;if(":"===this.token){n=!0,this.next(),i=this.node("block");for(var r=[];this.token!==this.EOF&&this.token!==this.tok.T_ENDIF;){if(this.token===this.tok.T_ELSEIF){s=this.read_elseif_short();break}if(this.token===this.tok.T_ELSE){s=this.read_else_short();break}r.push(this.read_inner_statement())}i=i(null,r),this.expect(this.tok.T_ENDIF)&&this.next(),this.expectEndOfStatement()}else i=this.read_statement(),this.token===this.tok.T_ELSEIF?s=this.read_if():this.token===this.tok.T_ELSE&&(s=this.next().read_statement());return t(e,i,s,n)},read_if_expr:function(){this.expect("(")&&this.next();var t=this.read_expr();return this.expect(")")&&this.next(),t},read_elseif_short:function(){var t=null,e=this.node("if"),i=this.next().read_if_expr();this.expect(":")&&this.next();for(var s=this.node("block"),n=[];this.token!=this.EOF&&this.token!==this.tok.T_ENDIF;){if(this.token===this.tok.T_ELSEIF){t=this.read_elseif_short();break}if(this.token===this.tok.T_ELSE){t=this.read_else_short();break}n.push(this.read_inner_statement())}return e(i,s(null,n),t,!0)},read_else_short:function(){this.next().expect(":")&&this.next();for(var t=this.node("block"),e=[];this.token!=this.EOF&&this.token!==this.tok.T_ENDIF;)e.push(this.read_inner_statement());return t(null,e)}}},5931:t=>{t.exports={read_while:function(){var t=this.node("while");this.expect(this.tok.T_WHILE)&&this.next();var e,i=null,s=!1;return this.expect("(")&&this.next(),e=this.read_expr(),this.expect(")")&&this.next(),":"===this.token?(s=!0,i=this.read_short_form(this.tok.T_ENDWHILE)):i=this.read_statement(),t(e,i,s)},read_do:function(){var t=this.node("do");this.expect(this.tok.T_DO)&&this.next();var e,i=null;return e=this.read_statement(),this.expect(this.tok.T_WHILE)&&(this.next().expect("(")&&this.next(),i=this.read_expr(),this.expect(")")&&this.next(),this.expect(";")&&this.next()),t(i,e)},read_for:function(){var t=this.node("for");this.expect(this.tok.T_FOR)&&this.next();var e=[],i=[],s=[],n=null,r=!1;return this.expect("(")&&this.next(),";"!==this.token?(e=this.read_list(this.read_expr,","),this.expect(";")&&this.next()):this.next(),";"!==this.token?(i=this.read_list(this.read_expr,","),this.expect(";")&&this.next()):this.next(),")"!==this.token?(s=this.read_list(this.read_expr,","),this.expect(")")&&this.next()):this.next(),":"===this.token?(r=!0,n=this.read_short_form(this.tok.T_ENDFOR)):n=this.read_statement(),t(e,i,s,n,r)},read_foreach:function(){var t=this.node("foreach");this.expect(this.tok.T_FOREACH)&&this.next();var e,i=null,s=null,n=null,r=!1;return this.expect("(")&&this.next(),e=this.read_expr(),this.expect(this.tok.T_AS)&&(this.next(),s=this.read_foreach_variable(),this.token===this.tok.T_DOUBLE_ARROW&&(i=s,s=this.next().read_foreach_variable())),i&&"list"===i.kind&&this.raiseError("Fatal Error : Cannot use list as key element"),this.expect(")")&&this.next(),":"===this.token?(r=!0,n=this.read_short_form(this.tok.T_ENDFOREACH)):n=this.read_statement(),t(e,i,s,n,r)},read_foreach_variable:function(){if(this.token===this.tok.T_LIST||"["===this.token){var t="["===this.token,e=this.node("list");this.next(),!t&&this.expect("(")&&this.next();var i=this.read_array_pair_list(t);return this.expect(t?"]":")")&&this.next(),e(i,t)}return this.read_variable(!1,!1)}}},9147:t=>{t.exports={read_start:function(){return this.token==this.tok.T_NAMESPACE?this.read_namespace():this.read_top_statement()}}},9219:t=>{t.exports={read_namespace:function(){var t,e,i=this.node("namespace");return this.expect(this.tok.T_NAMESPACE)&&this.next(),e="{"===this.token?{name:[""]}:this.read_namespace_name(),this.currentNamespace=e,";"===this.token?(this.currentNamespace=e,t=this.next().read_top_statements(),this.expect(this.EOF),i(e.name,t,!1)):"{"===this.token?(this.currentNamespace=e,t=this.next().read_top_statements(),this.expect("}")&&this.next(),0===t.length&&this.extractDoc&&this._docs.length>this._docIndex&&t.push(this.node("noop")()),i(e.name,t,!0)):(this.error(["{",";"]),this.currentNamespace=e,t=this.read_top_statements(),this.expect(this.EOF),i(e,t,!1))},read_namespace_name:function(t){var e,i=this.node(),s=this.text();switch(this.token){case this.tok.T_NAME_RELATIVE:e=this.ast.name.RELATIVE_NAME,s=s.replace(/^namespace\\/,"");break;case this.tok.T_NAME_QUALIFIED:e=this.ast.name.QUALIFIED_NAME;break;case this.tok.T_NAME_FULLY_QUALIFIED:e=this.ast.name.FULL_QUALIFIED_NAME;break;default:if(e=this.ast.name.UNQUALIFIED_NAME,!this.expect(this.tok.T_STRING))return i("name","",this.ast.name.FULL_QUALIFIED_NAME)}if(this.next(),t||"("!==this.token){if("parent"===s.toLowerCase())return i("parentreference",s);if("self"===s.toLowerCase())return i("selfreference",s)}return i("name",s,e)},read_use_statement:function(){var t=this.node("usegroup"),e=[],i=null;this.expect(this.tok.T_USE)&&this.next();var s=this.read_use_type();return e.push(this.read_use_declaration(!1)),","===this.token?e=e.concat(this.next().read_use_declarations(!1)):"{"===this.token&&(i=e[0].name,e=this.next().read_use_declarations(null===s),this.expect("}")&&this.next()),t=t(i,s,e),this.expect(";")&&this.next(),t},read_class_name_reference:function(){return this.read_variable(!0,!1)},read_use_declaration:function(t){var e=this.node("useitem"),i=null;t&&(i=this.read_use_type());var s=this.read_namespace_name(),n=this.read_use_alias();return e(s.name,n,i)},read_use_declarations:function(t){for(var e=[this.read_use_declaration(t)];","===this.token;){if(this.next(),t){if(this.token!==this.tok.T_NAME_RELATIVE&&this.token!==this.tok.T_NAME_QUALIFIED&&this.token!==this.tok.T_NAME_FULLY_QUALIFIED&&this.token!==this.tok.T_FUNCTION&&this.token!==this.tok.T_CONST&&this.token!==this.tok.T_STRING)break}else if(this.token!==this.tok.T_NAME_RELATIVE&&this.token!==this.tok.T_NAME_QUALIFIED&&this.token!==this.tok.T_NAME_FULLY_QUALIFIED&&this.token!==this.tok.T_STRING&&this.token!==this.tok.T_NS_SEPARATOR)break;e.push(this.read_use_declaration(t))}return e},read_use_alias:function(){var t=null;if(this.token===this.tok.T_AS&&this.next().expect(this.tok.T_STRING)){var e=this.node("identifier"),i=this.text();this.next(),t=e(i)}return t},read_use_type:function(){return this.token===this.tok.T_FUNCTION?(this.next(),this.ast.useitem.TYPE_FUNCTION):this.token===this.tok.T_CONST?(this.next(),this.ast.useitem.TYPE_CONST):null}}},7170:t=>{var e={"\\":"\\",$:"$",n:"\n",r:"\r",t:"\t",f:String.fromCharCode(12),v:String.fromCharCode(11),e:String.fromCharCode(27)};t.exports={resolve_special_chars:function(t,i){return i?t.replace(/\\"/,'"').replace(/\\([\\$nrtfve]|[xX][0-9a-fA-F]{1,2}|[0-7]{1,3}|u{([0-9a-fA-F]+)})/g,(function(t,i,s){return e[i]?e[i]:"x"===i[0]||"X"===i[0]?String.fromCodePoint(parseInt(i.substr(1),16)):"u"===i[0]?String.fromCodePoint(parseInt(s,16)):String.fromCodePoint(parseInt(i,8))})):t.replace(/\\\\/g,"\\").replace(/\\'/g,"'")},remove_heredoc_leading_whitespace_chars:function(t,e,i,s){if(0===e)return t;this.check_heredoc_indentation_level(t,e,i,s);var n=i?" ":"\t",r=new RegExp("\\n".concat(n,"{").concat(e,"}"),"g"),h=new RegExp("^".concat(n,"{").concat(e,"}"));return s&&(t=t.replace(h,"")),t.replace(r,"\n")},check_heredoc_indentation_level:function(t,e,i,s){var n=t.length,r=0,h=0,o=!0,a=i?" ":"\t",_=!1;if(!s){if(-1===(r=t.indexOf("\n")))return;r++}for(;r<n;)o?t[r]===a?h++:_=!0:o=!1,"\n"!==t[r]&&_&&h<e?this.raiseError("Invalid body indentation level (expecting an indentation at least ".concat(e,")")):_=!1,"\n"===t[r]&&(o=!0,h=0),r++},read_dereferencable_scalar:function(){var t=null;switch(this.token){case this.tok.T_CONSTANT_ENCAPSED_STRING:var e=this.node("string"),i=this.text(),s=0;"b"!==i[0]&&"B"!==i[0]||(s=1);var n='"'===i[s];this.next(),e=e(n,this.resolve_special_chars(i.substring(s+1,i.length-1),n),1===s,i),t=this.token===this.tok.T_DOUBLE_COLON?this.read_static_getter(e):e;break;case this.tok.T_ARRAY:case"[":t=this.read_array()}return t},read_scalar:function(){if(this.is("T_MAGIC_CONST"))return this.get_magic_constant();var t,e;switch(this.token){case this.tok.T_LNUMBER:case this.tok.T_DNUMBER:var i=this.node("number");return t=this.text(),this.next(),i(t,null);case this.tok.T_START_HEREDOC:if("ST_NOWDOC"===this.lexer.curCondition){var s=this.lexer.yylloc.first_offset;e=this.node("nowdoc"),t=this.next().text(),this.lexer.heredoc_label.indentation>0&&(t=t.substring(0,t.length-this.lexer.heredoc_label.indentation));var n=t[t.length-1];"\n"===n?t="\r"===t[t.length-2]?t.substring(0,t.length-2):t.substring(0,t.length-1):"\r"===n&&(t=t.substring(0,t.length-1)),this.expect(this.tok.T_ENCAPSED_AND_WHITESPACE)&&this.next(),this.expect(this.tok.T_END_HEREDOC)&&this.next();var r=this.lexer._input.substring(s,this.lexer.yylloc.first_offset);return e=e(this.remove_heredoc_leading_whitespace_chars(t,this.lexer.heredoc_label.indentation,this.lexer.heredoc_label.indentation_uses_spaces,this.lexer.heredoc_label.first_encaps_node),r,this.lexer.heredoc_label.label),this.lexer.heredoc_label.finished=!0,e}return this.read_encapsed_string(this.tok.T_END_HEREDOC);case'"':return this.read_encapsed_string('"');case'b"':case'B"':return this.read_encapsed_string('"',!0);case this.tok.T_CONSTANT_ENCAPSED_STRING:case this.tok.T_ARRAY:case"[":return this.read_dereferencable_scalar();default:var h=this.error("SCALAR");return this.next(),h}},read_dereferencable:function(t){var e,i,s=this.node("offsetlookup");return"["===this.token?(i=this.next().read_expr(),this.expect("]")&&this.next(),e=s(t,i)):this.token===this.tok.T_DOLLAR_OPEN_CURLY_BRACES&&(e=s(t,i=this.read_encapsed_string_item(!1))),e},read_encapsed_string_item:function(t){var e,i,s,n=this.node("encapsedpart"),r=null,h=!1,o=this.node();if(this.token===this.tok.T_ENCAPSED_AND_WHITESPACE){var a=this.text();this.next(),o=o("string",!1,this.version>=703&&!this.lexer.heredoc_label.finished?this.remove_heredoc_leading_whitespace_chars(this.resolve_special_chars(a,t),this.lexer.heredoc_label.indentation,this.lexer.heredoc_label.indentation_uses_spaces,this.lexer.heredoc_label.first_encaps_node):a,!1,a)}else if(this.token===this.tok.T_DOLLAR_OPEN_CURLY_BRACES){if(r="simple",h=!0,s=null,this.next().token===this.tok.T_STRING_VARNAME){s=this.node("variable");var _=this.text();this.next(),o.destroy(),"["===this.token?(s=s(_,!1),i=this.node("offsetlookup"),e=this.next().read_expr(),this.expect("]")&&this.next(),o=i(s,e)):o=s(_,!1)}else o=o("variable",this.read_expr(),!1);this.expect("}")&&this.next()}else if(this.token===this.tok.T_CURLY_OPEN)r="complex",o.destroy(),o=this.next().read_variable(!1,!1),this.expect("}")&&this.next();else if(this.token===this.tok.T_VARIABLE){if(r="simple",o.destroy(),o=this.read_simple_variable(),"["===this.token&&(i=this.node("offsetlookup"),e=this.next().read_encaps_var_offset(),this.expect("]")&&this.next(),o=i(o,e)),this.token===this.tok.T_OBJECT_OPERATOR){i=this.node("propertylookup"),this.next().expect(this.tok.T_STRING);var l=this.node("identifier");s=this.text(),this.next(),o=i(o,l(s))}}else{this.expect(this.tok.T_ENCAPSED_AND_WHITESPACE);var c=this.text();this.next(),o.destroy(),o=o("string",!1,c,!1,c)}return this.lexer.heredoc_label.first_encaps_node=!1,n(o,r,h)},read_encapsed_string:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.lexer.yylloc.first_offset,s=this.node("encapsed");this.next();var n,r=this.lexer.yylloc.prev_offset-(e?1:0),h=[];for(n="`"===t?this.ast.encapsed.TYPE_SHELL:'"'===t?this.ast.encapsed.TYPE_STRING:this.ast.encapsed.TYPE_HEREDOC;this.token!==t&&this.token!==this.EOF;)h.push(this.read_encapsed_string_item(!0));if(h.length>0&&"encapsedpart"===h[h.length-1].kind&&"string"===h[h.length-1].expression.kind){var o=h[h.length-1].expression,a=o.value[o.value.length-1];"\n"===a?"\r"===o.value[o.value.length-2]?o.value=o.value.substring(0,o.value.length-2):o.value=o.value.substring(0,o.value.length-1):"\r"===a&&(o.value=o.value.substring(0,o.value.length-1))}return this.expect(t)&&this.next(),s=s(h,this.lexer._input.substring("heredoc"===n?i:r-1,this.lexer.yylloc.first_offset),n),t===this.tok.T_END_HEREDOC&&(s.label=this.lexer.heredoc_label.label,this.lexer.heredoc_label.finished=!0),s},get_magic_constant:function(){var t=this.node("magic"),e=this.text();return this.next(),t(e.toUpperCase(),e)}}},6261:t=>{t.exports={read_top_statements:function(){for(var t=[];this.token!==this.EOF&&"}"!==this.token;){var e=this.read_top_statement();e&&(Array.isArray(e)?t=t.concat(e):t.push(e))}return t},read_top_statement:function(){var t=[];switch(this.token===this.tok.T_ATTRIBUTE&&(t=this.read_attr_list()),this.token){case this.tok.T_FUNCTION:return this.read_function(!1,!1,t);case this.tok.T_ABSTRACT:case this.tok.T_FINAL:case this.tok.T_READ_ONLY:case this.tok.T_CLASS:return this.read_class_declaration_statement(t);case this.tok.T_INTERFACE:return this.read_interface_declaration_statement(t);case this.tok.T_TRAIT:return this.read_trait_declaration_statement();case this.tok.T_ENUM:return this.read_enum_declaration_statement(t);case this.tok.T_USE:return this.read_use_statement();case this.tok.T_CONST:var e=this.node("constantstatement"),i=this.next().read_const_list();return this.expectEndOfStatement(),e(null,i);case this.tok.T_NAMESPACE:return this.read_namespace();case this.tok.T_HALT_COMPILER:var s=this.node("halt");return this.next().expect("(")&&this.next(),this.expect(")")&&this.next(),this.expect(";"),this.lexer.done=!0,s(this.lexer._input.substring(this.lexer.offset));default:return this.read_statement()}},read_inner_statements:function(){for(var t=[];this.token!=this.EOF&&"}"!==this.token;){var e=this.read_inner_statement();e&&(Array.isArray(e)?t=t.concat(e):t.push(e))}return t},read_const_list:function(){return this.read_list((function(){this.expect(this.tok.T_STRING);var t=this.node("constant"),e=this.node("identifier"),i=this.text();return this.next(),e=e(i),this.expect("=")?t(e,this.next().read_expr()):t(e,null)}),",",!1)},read_declare_list:function(){for(var t=[];this.token!=this.EOF&&")"!==this.token;){this.expect(this.tok.T_STRING);var e=this.node("declaredirective"),i=this.node("identifier"),s=this.text();this.next(),i=i(s);var n=null;if(this.expect("=")&&(n=this.next().read_expr()),t.push(e(i,n)),","!==this.token)break;this.next()}return t},read_inner_statement:function(){var t=[];switch(this.token===this.tok.T_ATTRIBUTE&&(t=this.read_attr_list()),this.token){case this.tok.T_FUNCTION:var e=this.read_function(!1,!1);return e.attrGroups=t,e;case this.tok.T_ABSTRACT:case this.tok.T_FINAL:case this.tok.T_CLASS:return this.read_class_declaration_statement();case this.tok.T_INTERFACE:return this.read_interface_declaration_statement();case this.tok.T_TRAIT:return this.read_trait_declaration_statement();case this.tok.T_ENUM:return this.read_enum_declaration_statement();case this.tok.T_HALT_COMPILER:this.raiseError("__HALT_COMPILER() can only be used from the outermost scope");var i=this.node("halt");return this.next().expect("(")&&this.next(),this.expect(")")&&this.next(),i=i(this.lexer._input.substring(this.lexer.offset)),this.expect(";")&&this.next(),i;default:return this.read_statement()}},read_statement:function(){switch(this.token){case"{":return this.read_code_block(!1);case this.tok.T_IF:return this.read_if();case this.tok.T_SWITCH:return this.read_switch();case this.tok.T_FOR:return this.read_for();case this.tok.T_FOREACH:return this.read_foreach();case this.tok.T_WHILE:return this.read_while();case this.tok.T_DO:return this.read_do();case this.tok.T_COMMENT:return this.read_comment();case this.tok.T_DOC_COMMENT:return this.read_doc_comment();case this.tok.T_RETURN:var t=this.node("return");this.next();var e=this.read_optional_expr(";");return this.expectEndOfStatement(),t(e);case this.tok.T_BREAK:case this.tok.T_CONTINUE:var i=this.node(this.token===this.tok.T_CONTINUE?"continue":"break");this.next();var s=this.read_optional_expr(";");return this.expectEndOfStatement(),i(s);case this.tok.T_GLOBAL:var n=this.node("global"),r=this.next().read_list(this.read_simple_variable,",");return this.expectEndOfStatement(),n(r);case this.tok.T_STATIC:var h=[this.token,this.lexer.getState()],o=this.node();if(this.next().token===this.tok.T_DOUBLE_COLON){this.lexer.tokens.push(h);var a=this.next().read_expr();return this.expectEndOfStatement(a),o("expressionstatement",a)}if(this.token===this.tok.T_FUNCTION)return this.read_function(!0,[0,1,0]);var _=this.read_variable_declarations();return this.expectEndOfStatement(),o("static",_);case this.tok.T_ECHO:var l=this.node("echo"),c=this.text(),u="<?="===c||"<%="===c,p=this.next().read_function_list(this.read_expr,",");return this.expectEndOfStatement(),l(p,u);case this.tok.T_INLINE_HTML:var f=this.text(),T=this.lexer.yylloc.first_offset>0?this.lexer._input[this.lexer.yylloc.first_offset-1]:null,d="\r"===T||"\n"===T;d&&"\n"===T&&this.lexer.yylloc.first_offset>1&&"\r"===this.lexer._input[this.lexer.yylloc.first_offset-2]&&(T="\r\n");var k=this.node("inline");return this.next(),k(f,d?T+f:f);case this.tok.T_UNSET:var x=this.node("unset");this.next().expect("(")&&this.next();var E=this.read_function_list(this.read_variable,",");return this.expect(")")&&this.next(),this.expect(";")&&this.next(),x(E);case this.tok.T_DECLARE:var y,A=this.node("declare"),v=[];this.next().expect("(")&&this.next();var m=this.read_declare_list();if(this.expect(")")&&this.next(),":"===this.token){for(this.next();this.token!=this.EOF&&this.token!==this.tok.T_ENDDECLARE;)v.push(this.read_top_statement());0===v.length&&this.extractDoc&&this._docs.length>this._docIndex&&v.push(this.node("noop")()),this.expect(this.tok.T_ENDDECLARE)&&this.next(),this.expectEndOfStatement(),y=this.ast.declare.MODE_SHORT}else if("{"===this.token){for(this.next();this.token!=this.EOF&&"}"!==this.token;)v.push(this.read_top_statement());0===v.length&&this.extractDoc&&this._docs.length>this._docIndex&&v.push(this.node("noop")()),this.expect("}")&&this.next(),y=this.ast.declare.MODE_BLOCK}else this.expect(";")&&this.next(),y=this.ast.declare.MODE_NONE;return A(m,v,y);case this.tok.T_TRY:return this.read_try();case this.tok.T_THROW:var N=this.node("throw"),I=this.next().read_expr();return this.expectEndOfStatement(),N(I);case";":return this.next(),null;case this.tok.T_STRING:var L=this.node(),S=[this.token,this.lexer.getState()],C=this.text(),O=this.node("identifier");if(":"===this.next().token)return O=O(C),this.next(),L("label",O);O.destroy(),L.destroy(),this.lexer.tokens.push(S);var b=this.node("expressionstatement"),R=this.next().read_expr();return this.expectEndOfStatement(R),b(R);case this.tok.T_GOTO:var g=this.node("goto"),D=null;if(this.next().expect(this.tok.T_STRING)){D=this.node("identifier");var U=this.text();this.next(),D=D(U),this.expectEndOfStatement()}return g(D);default:var P=this.node("expressionstatement"),M=this.read_expr();return this.expectEndOfStatement(M),P(M)}},read_code_block:function(t){var e=this.node("block");this.expect("{")&&this.next();var i=t?this.read_top_statements():this.read_inner_statements();return 0===i.length&&this.extractDoc&&this._docs.length>this._docIndex&&i.push(this.node("noop")()),this.expect("}")&&this.next(),e(null,i)}}},2478:t=>{t.exports={read_switch:function(){var t=this.node("switch");this.expect(this.tok.T_SWITCH)&&this.next(),this.expect("(")&&this.next();var e=this.read_expr();this.expect(")")&&this.next();var i=":"===this.token;return t(e,this.read_switch_case_list(),i)},read_switch_case_list:function(){var t=null,e=this.node("block"),i=[];for("{"===this.token?t="}":":"===this.token?t=this.tok.T_ENDSWITCH:this.expect(["{",":"]),this.next(),";"===this.token&&this.next();this.token!==this.EOF&&this.token!==t;)i.push(this.read_case_list(t));return 0===i.length&&this.extractDoc&&this._docs.length>this._docIndex&&i.push(this.node("noop")()),this.expect(t)&&this.next(),t===this.tok.T_ENDSWITCH&&this.expectEndOfStatement(),e(null,i)},read_case_list:function(t){var e=this.node("case"),i=null;this.token===this.tok.T_CASE?i=this.next().read_expr():this.token===this.tok.T_DEFAULT?this.next():this.expect([this.tok.T_CASE,this.tok.T_DEFAULT]),this.expect([":",";"])&&this.next();for(var s=this.node("block"),n=[];this.token!==this.EOF&&this.token!==t&&this.token!==this.tok.T_CASE&&this.token!==this.tok.T_DEFAULT;)n.push(this.read_inner_statement());return e(i,s(null,n))}}},77:t=>{t.exports={read_try:function(){this.expect(this.tok.T_TRY);for(var t=this.node("try"),e=null,i=[],s=this.next().read_statement();this.token===this.tok.T_CATCH;){var n=this.node("catch");this.next().expect("(")&&this.next();var r=this.read_list(this.read_namespace_name,"|",!1),h=null;(this.version<800||this.token===this.tok.T_VARIABLE)&&(h=this.read_variable(!0,!1)),this.expect(")"),i.push(n(this.next().read_statement(),r,h))}return this.token===this.tok.T_FINALLY&&(e=this.next().read_statement()),t(s,i,e)}}},6077:t=>{t.exports={read_short_form:function(t){var e=this.node("block"),i=[];for(this.expect(":")&&this.next();this.token!=this.EOF&&this.token!==t;)i.push(this.read_inner_statement());return 0===i.length&&this.extractDoc&&this._docs.length>this._docIndex&&i.push(this.node("noop")()),this.expect(t)&&this.next(),this.expectEndOfStatement(),e(null,i)},read_function_list:function(t,e){var i=[];do{if(this.token==e&&this.version>=703&&i.length>0){i.push(this.node("noop")());break}if(i.push(t.apply(this,[])),this.token!=e)break;if(")"==this.next().token&&this.version>=703)break}while(this.token!=this.EOF);return i},read_list:function(t,e,i){var s=[];if(this.token==e&&(i&&s.push("function"==typeof t?this.node("noop")():null),this.next()),"function"==typeof t)do{var n=t.apply(this,[]);if(n&&s.push(n),this.token!=e)break}while(this.next().token!=this.EOF);else{if(!this.expect(t))return[];for(s.push(this.text());this.next().token!=this.EOF&&this.token==e&&this.next().token==t;)s.push(this.text())}return s},read_name_list:function(){return this.read_list(this.read_namespace_name,",",!1)},read_byref:function(t){var e=this.node("byref");this.next(),e=e(null);var i=t();return i&&(this.ast.swapLocations(i,e,i,this),i.byref=!0),i},read_variable_declarations:function(){return this.read_list((function(){var t=this.node("staticvariable"),e=this.node("variable");if(this.expect(this.tok.T_VARIABLE)){var i=this.text().substring(1);this.next(),e=e(i,!1)}else e=e("#ERR",!1);return"="===this.token?t(e,this.next().read_expr()):e}),",")},read_extends_from:function(){return this.token===this.tok.T_EXTENDS?this.next().read_namespace_name():null},read_interface_extends_list:function(){return this.token===this.tok.T_EXTENDS?this.next().read_name_list():null},read_implements_list:function(){return this.token===this.tok.T_IMPLEMENTS?this.next().read_name_list():null}}},1130:t=>{t.exports={read_variable:function(t,e){var i;if("&"===this.token)return this.read_byref(this.read_variable.bind(this,t,e));if(this.is([this.tok.T_VARIABLE,"$"]))i=this.read_reference_variable(e);else if(this.is([this.tok.T_NS_SEPARATOR,this.tok.T_STRING,this.tok.T_NAME_RELATIVE,this.tok.T_NAME_QUALIFIED,this.tok.T_NAME_FULLY_QUALIFIED,this.tok.T_NAMESPACE])){i=this.node();var s=this.read_namespace_name();if(this.token!=this.tok.T_DOUBLE_COLON&&"("!=this.token&&-1===["parentreference","selfreference"].indexOf(s.kind)){var n=s.name.toLowerCase();"true"===n?i=s.destroy(i("boolean",!0,s.name)):"false"===n?i=s.destroy(i("boolean",!1,s.name)):"null"===n?i=s.destroy(i("nullkeyword",s.name)):(i.destroy(s),i=s)}else i.destroy(s),i=s}else if(this.token===this.tok.T_STATIC){i=this.node("staticreference");var r=this.text();this.next(),i=i(r)}else this.expect("VARIABLE");return this.token===this.tok.T_DOUBLE_COLON&&(i=this.read_static_getter(i,e)),this.recursive_variable_chain_scan(i,t,e)},read_static_getter:function(t,e){var i,s,n=this.node("staticlookup");return this.next().is([this.tok.T_VARIABLE,"$"])?i=this.read_reference_variable(e):this.token===this.tok.T_STRING||this.token===this.tok.T_CLASS||this.version>=700&&this.is("IDENTIFIER")?(i=this.node("identifier"),s=this.text(),this.next(),i=i(s)):"{"===this.token?(i=this.node("literal"),s=this.next().read_expr(),this.expect("}")&&this.next(),i=i("literal",s,null),this.expect("(")):(this.error([this.tok.T_VARIABLE,this.tok.T_STRING]),i=this.node("identifier"),s=this.text(),this.next(),i=i(s)),n(t,i)},read_what:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=null,i=null;switch(this.next().token){case this.tok.T_STRING:e=this.node("identifier"),i=this.text(),this.next(),e=e(i),t&&this.token===this.tok.T_OBJECT_OPERATOR&&this.error();break;case this.tok.T_VARIABLE:e=this.node("variable"),i=this.text().substring(1),this.next(),e=e(i,!1);break;case"$":e=this.node(),this.next().expect(["$","{",this.tok.T_VARIABLE]),"{"===this.token?(i=this.next().read_expr(),this.expect("}")&&this.next(),e=e("variable",i,!0)):e=e("variable",i=this.read_expr(),!1);break;case"{":e=this.node("encapsedpart"),i=this.next().read_expr(),this.expect("}")&&this.next(),e=e(i,"complex",!1);break;default:this.error([this.tok.T_STRING,this.tok.T_VARIABLE,"$","{"]),e=this.node("identifier"),i=this.text(),this.next(),e=e(i)}return e},recursive_variable_chain_scan:function(t,e,i){var s,n;t:for(;this.token!=this.EOF;)switch(this.token){case"(":if(e)return t;t=this.node("call")(t,this.read_argument_list());break;case"[":case"{":var r="["===this.token;s=this.node("offsetlookup"),this.next(),n=!1,i?(n=this.read_encaps_var_offset(),this.expect(r?"]":"}")&&this.next()):(r?"]"!==this.token:"}"!==this.token)?(n=this.read_expr(),this.expect(r?"]":"}")&&this.next()):this.next(),t=s(t,n);break;case this.tok.T_DOUBLE_COLON:"staticlookup"===t.kind&&"identifier"===t.offset.kind&&this.error(),t=(s=this.node("staticlookup"))(t,this.read_what(!0));break;case this.tok.T_OBJECT_OPERATOR:t=(s=this.node("propertylookup"))(t,this.read_what());break;case this.tok.T_NULLSAFE_OBJECT_OPERATOR:t=(s=this.node("nullsafepropertylookup"))(t,this.read_what());break;default:break t}return t},read_encaps_var_offset:function(){var t=this.node();if(this.token===this.tok.T_STRING){var e=this.text();this.next(),t=t("identifier",e)}else if(this.token===this.tok.T_NUM_STRING){var i=this.text();this.next(),t=t("number",i,null)}else if("-"===this.token){this.next();var s=-1*this.text();this.expect(this.tok.T_NUM_STRING)&&this.next(),t=t("number",s,null)}else if(this.token===this.tok.T_VARIABLE){var n=this.text().substring(1);this.next(),t=t("variable",n,!1)}else{this.expect([this.tok.T_STRING,this.tok.T_NUM_STRING,"-",this.tok.T_VARIABLE]);var r=this.text();this.next(),t=t("identifier",r)}return t},read_reference_variable:function(t){for(var e,i=this.read_simple_variable();this.token!=this.EOF;){var s=this.node();if("{"!=this.token||t){s.destroy();break}e=this.next().read_expr(),this.expect("}")&&this.next(),i=s("offsetlookup",i,e)}return i},read_simple_variable:function(){var t,e=this.node("variable");if(this.expect([this.tok.T_VARIABLE,"$"])&&this.token===this.tok.T_VARIABLE)t=this.text().substring(1),this.next(),e=e(t,!1);else switch("$"===this.token&&this.next(),this.token){case"{":var i=this.next().read_expr();this.expect("}")&&this.next(),e=e(i,!0);break;case"$":e=e(this.read_simple_variable(),!1);break;case this.tok.T_VARIABLE:t=this.text().substring(1);var s=this.node("variable");this.next(),e=e(s(t,!1),!1);break;default:this.error(["{","$",this.tok.T_VARIABLE]),t=this.text(),this.next(),e=e(t,!1)}return e}}},1906:t=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function i(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,s)}return i}function s(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?i(Object(s),!0).forEach((function(e){n(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):i(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function n(t,i,s){return(i=function(t){var i=function(t){if("object"!=e(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,"string");if("object"!=e(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(i)?i:i+""}(i))in t?Object.defineProperty(t,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[i]=s,t}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=Array(e);i<e;i++)s[i]=t[i];return s}var h={T_HALT_COMPILER:101,T_USE:102,T_ENCAPSED_AND_WHITESPACE:103,T_OBJECT_OPERATOR:104,T_STRING:105,T_DOLLAR_OPEN_CURLY_BRACES:106,T_STRING_VARNAME:107,T_CURLY_OPEN:108,T_NUM_STRING:109,T_ISSET:110,T_EMPTY:111,T_INCLUDE:112,T_INCLUDE_ONCE:113,T_EVAL:114,T_REQUIRE:115,T_REQUIRE_ONCE:116,T_NAMESPACE:117,T_NS_SEPARATOR:118,T_AS:119,T_IF:120,T_ENDIF:121,T_WHILE:122,T_DO:123,T_FOR:124,T_SWITCH:125,T_BREAK:126,T_CONTINUE:127,T_RETURN:128,T_GLOBAL:129,T_STATIC:130,T_ECHO:131,T_INLINE_HTML:132,T_UNSET:133,T_FOREACH:134,T_DECLARE:135,T_TRY:136,T_THROW:137,T_GOTO:138,T_FINALLY:139,T_CATCH:140,T_ENDDECLARE:141,T_LIST:142,T_CLONE:143,T_PLUS_EQUAL:144,T_MINUS_EQUAL:145,T_MUL_EQUAL:146,T_DIV_EQUAL:147,T_CONCAT_EQUAL:148,T_MOD_EQUAL:149,T_AND_EQUAL:150,T_OR_EQUAL:151,T_XOR_EQUAL:152,T_SL_EQUAL:153,T_SR_EQUAL:154,T_INC:155,T_DEC:156,T_BOOLEAN_OR:157,T_BOOLEAN_AND:158,T_LOGICAL_OR:159,T_LOGICAL_AND:160,T_LOGICAL_XOR:161,T_SL:162,T_SR:163,T_IS_IDENTICAL:164,T_IS_NOT_IDENTICAL:165,T_IS_EQUAL:166,T_IS_NOT_EQUAL:167,T_IS_SMALLER_OR_EQUAL:168,T_IS_GREATER_OR_EQUAL:169,T_INSTANCEOF:170,T_INT_CAST:171,T_DOUBLE_CAST:172,T_STRING_CAST:173,T_ARRAY_CAST:174,T_OBJECT_CAST:175,T_BOOL_CAST:176,T_UNSET_CAST:177,T_EXIT:178,T_PRINT:179,T_YIELD:180,T_YIELD_FROM:181,T_FUNCTION:182,T_DOUBLE_ARROW:183,T_DOUBLE_COLON:184,T_ARRAY:185,T_CALLABLE:186,T_CLASS:187,T_ABSTRACT:188,T_TRAIT:189,T_FINAL:190,T_EXTENDS:191,T_INTERFACE:192,T_IMPLEMENTS:193,T_VAR:194,T_PUBLIC:195,T_PROTECTED:196,T_PRIVATE:197,T_CONST:198,T_NEW:199,T_INSTEADOF:200,T_ELSEIF:201,T_ELSE:202,T_ENDSWITCH:203,T_CASE:204,T_DEFAULT:205,T_ENDFOR:206,T_ENDFOREACH:207,T_ENDWHILE:208,T_CONSTANT_ENCAPSED_STRING:209,T_LNUMBER:210,T_DNUMBER:211,T_LINE:212,T_FILE:213,T_DIR:214,T_TRAIT_C:215,T_METHOD_C:216,T_FUNC_C:217,T_NS_C:218,T_START_HEREDOC:219,T_END_HEREDOC:220,T_CLASS_C:221,T_VARIABLE:222,T_OPEN_TAG:223,T_OPEN_TAG_WITH_ECHO:224,T_CLOSE_TAG:225,T_WHITESPACE:226,T_COMMENT:227,T_DOC_COMMENT:228,T_ELLIPSIS:229,T_COALESCE:230,T_POW:231,T_POW_EQUAL:232,T_SPACESHIP:233,T_COALESCE_EQUAL:234,T_FN:235,T_NULLSAFE_OBJECT_OPERATOR:236,T_MATCH:237,T_ATTRIBUTE:238,T_ENUM:239,T_READ_ONLY:240,T_NAME_RELATIVE:241,T_NAME_QUALIFIED:242,T_NAME_FULLY_QUALIFIED:243},o={values:Object.entries(h).reduce((function(t,e){var i,h,o=(h=2,function(t){if(Array.isArray(t))return t}(i=e)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var s,n,r,h,o=[],a=!0,_=!1;try{if(r=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;a=!1}else for(;!(a=(s=r.call(i)).done)&&(o.push(s.value),o.length!==e);a=!0);}catch(t){_=!0,n=t}finally{try{if(!a&&null!=i.return&&(h=i.return(),Object(h)!==h))return}finally{if(_)throw n}}return o}}(i,h)||function(t,e){if(t){if("string"==typeof t)return r(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?r(t,e):void 0}}(i,h)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=o[0],_=o[1];return s(s({},t),{},n({},_,a))}),{}),names:h};t.exports=Object.freeze(o)}},e={},i=function i(s){var n=e[s];if(void 0!==n)return n.exports;var r=e[s]={exports:{}};return t[s](r,r.exports,i),r.exports}(5362);return i.default})(),t.exports=e()},3145:(t,e,i)=>{"use strict";function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=Array(e);i<e;i++)s[i]=t[i];return s}i.d(e,{A:()=>s})},6369:(t,e,i)=>{"use strict";function s(t){if(Array.isArray(t))return t}i.d(e,{A:()=>s})},6986:(t,e,i)=>{"use strict";function s(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var s,n,r,h,o=[],a=!0,_=!1;try{if(r=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;a=!1}else for(;!(a=(s=r.call(i)).done)&&(o.push(s.value),o.length!==e);a=!0);}catch(t){_=!0,n=t}finally{try{if(!a&&null!=i.return&&(h=i.return(),Object(h)!==h))return}finally{if(_)throw n}}return o}}i.d(e,{A:()=>s})},6562:(t,e,i)=>{"use strict";function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}i.d(e,{A:()=>s})},3453:(t,e,i)=>{"use strict";i.d(e,{A:()=>o});var s=i(6369),n=i(6986),r=i(7800),h=i(6562);function o(t,e){return(0,s.A)(t)||(0,n.A)(t,e)||(0,r.A)(t,e)||(0,h.A)()}},816:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});var s=i(2284);function n(t){var e=function(t){if("object"!=(0,s.A)(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,"string");if("object"!=(0,s.A)(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==(0,s.A)(e)?e:e+""}},2284:(t,e,i)=>{"use strict";function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}i.d(e,{A:()=>s})},7800:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});var s=i(3145);function n(t,e){if(t){if("string"==typeof t)return(0,s.A)(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?(0,s.A)(t,e):void 0}}}},i={};function s(t){var n=i[t];if(void 0!==n)return n.exports;var r=i[t]={exports:{}};return e[t](r,r.exports,s),r.exports}s.m=e,t=[],s.O=(e,i,n,r)=>{if(!i){var h=1/0;for(l=0;l<t.length;l++){for(var[i,n,r]=t[l],o=!0,a=0;a<i.length;a++)(!1&r||h>=r)&&Object.keys(s.O).every((t=>s.O[t](i[a])))?i.splice(a--,1):(o=!1,r<h&&(h=r));if(o){t.splice(l--,1);var _=n();void 0!==_&&(e=_)}}return e}r=r||0;for(var l=t.length;l>0&&t[l-1][2]>r;l--)t[l]=t[l-1];t[l]=[i,n,r]},s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e},s.d=(t,e)=>{for(var i in e)s.o(e,i)&&!s.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),s.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{var t={6008:0};s.O.j=e=>0===t[e];var e=(e,i)=>{var n,r,[h,o,a]=i,_=0;if(h.some((e=>0!==t[e]))){for(n in o)s.o(o,n)&&(s.m[n]=o[n]);if(a)var l=a(s)}for(e&&e(i);_<h.length;_++)r=h[_],s.o(t,r)&&t[r]&&t[r][0](),t[r]=0;return s.O(l)},i=self.webpackChunkcode_snippets=self.webpackChunkcode_snippets||[];i.forEach(e.bind(null,0)),i.push=e.bind(null,i.push.bind(i))})();var n=s(2949);n=s.O(n)})();
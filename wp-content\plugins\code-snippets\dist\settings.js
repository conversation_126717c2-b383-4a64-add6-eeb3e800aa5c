"use strict";(self.webpackChunkcode_snippets=self.webpackChunkcode_snippets||[]).push([[6472],{6658:(e,t,r)=>{var n=r(2284),o=r(5501),i=r(3662);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function c(){c=function(e,t){return new r(e,void 0,t)};var e=RegExp.prototype,t=new WeakMap;function r(e,n,o){var a=RegExp(e,n);return t.set(a,o||t.get(e)),(0,i.A)(a,r.prototype)}function a(e,r){var n=t.get(r);return Object.keys(n).reduce((function(t,r){var o=n[r];if("number"==typeof o)t[r]=e[o];else{for(var i=0;void 0===e[o[i]]&&i+1<o.length;)i++;t[r]=e[o[i]]}return t}),Object.create(null))}return(0,o.A)(r,RegExp),r.prototype.exec=function(t){var r=e.exec.call(this,t);if(r){r.groups=a(r,this);var n=r.indices;n&&(n.groups=a(n,this))}return r},r.prototype[Symbol.replace]=function(r,o){if("string"==typeof o){var i=t.get(this);return e[Symbol.replace].call(this,r,o.replace(/\$<([^>]+)>/g,(function(e,t){var r=i[t];return"$"+(Array.isArray(r)?r.join("$"):r)})))}if("function"==typeof o){var c=this;return e[Symbol.replace].call(this,r,(function(){var e=arguments;return"object"!=(0,n.A)(e[e.length-1])&&(e=[].slice.call(e)).push(a(e,c)),o.apply(this,e)}))}return e[Symbol.replace].call(this,r,o)},c.apply(this,arguments)}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}r(2949),function(){var e=document.getElementById("settings-sections-tabs");if(e){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return a(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,c=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return c=e.done,e},e:function(e){u=!0,i=e},f:function(){try{c||null==r.return||r.return()}finally{if(u)throw i}}}}(e.querySelectorAll(".nav-tab"));try{var n=function(){var r=t.value;r.addEventListener("click",(function(t){t.preventDefault();var n=r.getAttribute("data-section");n&&(function(e,t,r){var n,o;null===(n=e.querySelector(".nav-tab-active"))||void 0===n||n.classList.remove("nav-tab-active"),t.classList.add("nav-tab-active"),null===(o=e.closest(".wrap"))||void 0===o||o.setAttribute("data-active-tab",r)}(e,r,n),function(e){var t;"editor"===e&&(null===(t=window.code_snippets_editor_preview)||void 0===t||t.codemirror.refresh())}(n),function(e){var t=document.querySelector("input[name=_wp_http_referer]");if(t){var r=t.value.replace(c(/([&?]section=)[^&]+/,{base:1}),"$1".concat(e));t.value=r+(r===t.value?"&section=".concat(e):"")}else console.error("could not find http referer")}(n))}))};for(r.s();!(t=r.n()).done;)n()}catch(e){r.e(e)}finally{r.f()}}else console.error("Could not find snippets tabs")}(),function(){var e,t=function(){var e=window.wp.codeEditor,t=document.getElementById("code_snippets_editor_preview");if(t)return window.code_snippets_editor_preview=e.initialize(t),window.code_snippets_editor_preview.codemirror;console.error("Could not initialise CodeMirror on textarea.",t)}(),r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return u(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}(window.code_snippets_editor_settings);try{var n=function(){var r=e.value,n=document.querySelector('[name="code_snippets_settings[editor]['.concat(r.name,']"]'));null==n||n.addEventListener("change",(function(){var e=r.codemirror,o=function(){switch(r.type){case"select":return(e=n).options[e.selectedIndex].value;case"checkbox":return n.checked;case"number":return parseInt(n.value,10);default:return null}var e}();null!==o&&(null==t||t.setOption(e,o))}))};for(r.s();!(e=r.n()).done;)n()}catch(e){r.e(e)}finally{r.f()}}()},5501:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(3662);function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,n.A)(e,t)}},3662:(e,t,r)=>{function n(e,t){return n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},n(e,t)}r.d(t,{A:()=>n})}},e=>{e(e.s=6658)}]);
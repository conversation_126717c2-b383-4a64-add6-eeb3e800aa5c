/**
 * Notification Center Component
 * 
 * Handles notification preferences and notification display
 * Extends BaseComponent with notification-specific functionality
 */

class NotificationCenter extends window.BusinessCraftAI.BaseComponent {
    constructor(element, options = {}) {
        super(element, options);
        this.preferences = {};
        this.notifications = [];
        this.isLoading = false;
        this.isDirty = false;
    }
    
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            autoLoad: true,
            enablePreferences: true,
            enableHistory: true,
            enableTestNotifications: true,
            maxNotifications: 50,
            pollInterval: 60000 // 1 minute
        };
    }
    
    getDefaultRole() {
        return 'region';
    }
    
    isInteractive() {
        return true;
    }
    
    onInit() {
        this.setupNotificationContainer();
        this.setupPreferenceControls();
        this.setupNotificationList();
        this.setupActionButtons();
        
        if (this.options.autoLoad) {
            this.loadPreferences();
            this.loadNotifications();
        }
        
        this.startPolling();
    }
    
    setupNotificationContainer() {
        if (!this.element) return;
        
        this.element.setAttribute('aria-label', 'Notification Center');
        this.element.classList.add('notification-center', 'bcai-component');
        
        // Add live region for dynamic updates
        this.createNotificationLiveRegion();
    }
    
    createNotificationLiveRegion() {
        this.liveRegion = document.createElement('div');
        this.liveRegion.setAttribute('aria-live', 'polite');
        this.liveRegion.setAttribute('aria-atomic', 'false');
        this.liveRegion.className = 'notification-live-region sr-only';
        this.element.appendChild(this.liveRegion);
    }
    
    setupPreferenceControls() {
        if (!this.options.enablePreferences) return;
        
        // Notification toggles
        this.toggles = this.element.querySelectorAll('.notification-checkbox');
        this.toggles.forEach(toggle => {
            toggle.addEventListener('change', this.handleToggleChange.bind(this));
            
            // Add keyboard support for custom toggles
            const slider = toggle.nextElementSibling;
            if (slider && slider.classList.contains('toggle-slider')) {
                slider.addEventListener('keydown', this.handleToggleKeydown.bind(this));
                slider.setAttribute('tabindex', '0');
                slider.setAttribute('role', 'switch');
                slider.setAttribute('aria-checked', toggle.checked);
            }
        });
        
        // Frequency selectors
        this.frequencySelectors = this.element.querySelectorAll('.frequency-select');
        this.frequencySelectors.forEach(selector => {
            selector.addEventListener('change', this.handleFrequencyChange.bind(this));
        });
        
        // Threshold inputs
        this.thresholdInputs = this.element.querySelectorAll('.threshold-input');
        this.thresholdInputs.forEach(input => {
            input.addEventListener('input', this.handleThresholdChange.bind(this));
            input.addEventListener('blur', this.validateThreshold.bind(this));
        });
    }
    
    setupNotificationList() {
        if (!this.options.enableHistory) return;
        
        this.notificationsList = this.element.querySelector('#notifications-list');
        if (this.notificationsList) {
            this.notificationsList.setAttribute('role', 'log');
            this.notificationsList.setAttribute('aria-label', 'Notification history');
            this.notificationsList.setAttribute('aria-live', 'polite');
        }
    }
    
    setupActionButtons() {
        // Save preferences button
        this.saveButton = this.element.querySelector('.btn[onclick*="saveNotificationPreferences"]');
        if (this.saveButton) {
            this.saveButton.removeAttribute('onclick');
            this.saveButton.addEventListener('click', this.handleSavePreferences.bind(this));
            this.saveButton.setAttribute('aria-describedby', 'save-preferences-help');
        }
        
        // Test notification button
        this.testButton = this.element.querySelector('.btn[onclick*="testNotifications"]');
        if (this.testButton) {
            this.testButton.removeAttribute('onclick');
            this.testButton.addEventListener('click', this.handleTestNotification.bind(this));
            this.testButton.setAttribute('aria-describedby', 'test-notification-help');
        }
        
        // Add help text
        this.addHelpText();
    }
    
    addHelpText() {
        if (!document.getElementById('save-preferences-help')) {
            const saveHelp = document.createElement('div');
            saveHelp.id = 'save-preferences-help';
            saveHelp.className = 'sr-only';
            saveHelp.textContent = 'Save your notification preferences to apply changes';
            this.element.appendChild(saveHelp);
        }
        
        if (!document.getElementById('test-notification-help')) {
            const testHelp = document.createElement('div');
            testHelp.id = 'test-notification-help';
            testHelp.className = 'sr-only';
            testHelp.textContent = 'Send a test notification to verify your settings';
            this.element.appendChild(testHelp);
        }
    }
    
    async loadPreferences() {
        if (!this.options.enablePreferences) return;
        
        this.setLoading(true);
        
        try {
            const response = await this.fetchPreferences();
            
            if (response.success) {
                this.preferences = response.data;
                this.populatePreferences();
                this.emit('notifications:preferences-loaded', { preferences: this.preferences });
            } else {
                throw new Error(response.data || 'Failed to load preferences');
            }
            
        } catch (error) {
            this.handleError('Failed to load notification preferences', error);
        } finally {
            this.setLoading(false);
        }
    }
    
    async fetchPreferences() {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_get_notification_preferences');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    populatePreferences() {
        // Email notifications
        this.setToggleValue('email-opportunities', this.preferences.opportunities?.email?.enabled || false);
        this.setToggleValue('email-credits', this.preferences.credits?.email?.enabled || false);
        this.setToggleValue('email-templates', this.preferences.templates?.email?.enabled || false);
        this.setToggleValue('email-weekly', this.preferences.weekly_summary?.email?.enabled || false);
        
        // Push notifications
        this.setToggleValue('push-urgent', this.preferences.urgent_alerts?.push?.enabled || false);
        this.setToggleValue('push-opportunities', this.preferences.opportunities?.push?.enabled || false);
        
        // Frequency settings
        this.setSelectValue('opportunity-frequency', this.preferences.opportunities?.email?.frequency || 'daily');
        this.setInputValue('credit-threshold', this.preferences.credits?.email?.settings?.threshold || 50);
        
        this.isDirty = false;
    }
    
    setToggleValue(id, value) {
        const toggle = document.getElementById(id);
        if (toggle) {
            toggle.checked = value;
            
            const slider = toggle.nextElementSibling;
            if (slider && slider.classList.contains('toggle-slider')) {
                slider.setAttribute('aria-checked', value);
            }
        }
    }
    
    setSelectValue(id, value) {
        const select = document.getElementById(id);
        if (select) {
            select.value = value;
        }
    }
    
    setInputValue(id, value) {
        const input = document.getElementById(id);
        if (input) {
            input.value = value;
        }
    }
    
    async loadNotifications() {
        if (!this.options.enableHistory) return;
        
        try {
            const response = await this.fetchNotifications();
            
            if (response.success) {
                this.notifications = response.data;
                this.displayNotifications();
                this.emit('notifications:history-loaded', { notifications: this.notifications });
            } else {
                throw new Error(response.data || 'Failed to load notifications');
            }
            
        } catch (error) {
            this.handleError('Failed to load notification history', error);
        }
    }
    
    async fetchNotifications() {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_get_user_notifications');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        formData.append('limit', this.options.maxNotifications);
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    displayNotifications() {
        if (!this.notificationsList) return;
        
        if (!this.notifications || this.notifications.length === 0) {
            this.notificationsList.innerHTML = '<p class="no-notifications">No notifications yet.</p>';
            this.notificationsList.setAttribute('aria-label', 'No notifications available');
            return;
        }
        
        const notificationsHTML = this.notifications.map((notification, index) => `
            <div class="notification-item ${notification.status}" 
                 role="listitem" 
                 tabindex="0"
                 aria-label="Notification ${index + 1}: ${notification.title}"
                 data-notification-id="${notification.id}">
                <div class="notification-icon" aria-hidden="true">${notification.icon}</div>
                <div class="notification-content">
                    <div class="notification-title">${this.escapeHtml(notification.title)}</div>
                    <div class="notification-message">${this.escapeHtml(notification.message)}</div>
                    <div class="notification-time">${this.escapeHtml(notification.time_ago)}</div>
                </div>
                ${notification.status === 'unread' ? '<div class="notification-unread-indicator" aria-label="Unread"></div>' : ''}
            </div>
        `).join('');
        
        this.notificationsList.innerHTML = notificationsHTML;
        this.notificationsList.setAttribute('role', 'list');
        this.notificationsList.setAttribute('aria-label', `Notification history with ${this.notifications.length} items`);
        
        // Add click handlers for notifications
        this.notificationsList.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', this.handleNotificationClick.bind(this));
            item.addEventListener('keydown', this.handleNotificationKeydown.bind(this));
        });
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    handleToggleChange(event) {
        const toggle = event.target;
        const slider = toggle.nextElementSibling;
        
        if (slider && slider.classList.contains('toggle-slider')) {
            slider.setAttribute('aria-checked', toggle.checked);
        }
        
        this.markDirty();
        this.emit('notifications:preference-changed', { 
            id: toggle.id, 
            value: toggle.checked 
        });
    }
    
    handleToggleKeydown(event) {
        if (event.key === ' ' || event.key === 'Enter') {
            event.preventDefault();
            const toggle = event.target.previousElementSibling;
            if (toggle && toggle.type === 'checkbox') {
                toggle.checked = !toggle.checked;
                toggle.dispatchEvent(new Event('change'));
            }
        }
    }
    
    handleFrequencyChange(event) {
        this.markDirty();
        this.emit('notifications:frequency-changed', { 
            id: event.target.id, 
            value: event.target.value 
        });
    }
    
    handleThresholdChange(event) {
        this.markDirty();
        this.emit('notifications:threshold-changed', { 
            id: event.target.id, 
            value: event.target.value 
        });
    }
    
    validateThreshold(event) {
        const input = event.target;
        const value = parseInt(input.value);
        const min = parseInt(input.min) || 10;
        const max = parseInt(input.max) || 500;
        
        if (isNaN(value) || value < min || value > max) {
            input.value = Math.max(min, Math.min(max, value || min));
            this.announceToScreenReader(`Threshold value adjusted to ${input.value}`);
        }
    }
    
    markDirty() {
        this.isDirty = true;
        
        if (this.saveButton) {
            this.saveButton.classList.add('btn-warning');
            this.saveButton.textContent = 'Save Changes';
        }
    }
    
    markClean() {
        this.isDirty = false;
        
        if (this.saveButton) {
            this.saveButton.classList.remove('btn-warning');
            this.saveButton.textContent = 'Save Preferences';
        }
    }
    
    async handleSavePreferences(event) {
        event.preventDefault();
        
        if (!this.isDirty) {
            this.showMessage('No changes to save', 'info');
            return;
        }
        
        this.setLoading(true);
        
        try {
            const preferences = this.collectPreferences();
            const response = await this.savePreferences(preferences);
            
            if (response.success) {
                this.preferences = preferences;
                this.markClean();
                this.showMessage('Preferences saved successfully!', 'success');
                this.announceToScreenReader('Notification preferences saved successfully');
                this.emit('notifications:preferences-saved', { preferences });
            } else {
                throw new Error(response.data || 'Failed to save preferences');
            }
            
        } catch (error) {
            this.handleError('Failed to save preferences', error);
            this.showMessage('Failed to save preferences: ' + error.message, 'error');
        } finally {
            this.setLoading(false);
        }
    }
    
    collectPreferences() {
        return {
            opportunities: {
                email: {
                    enabled: document.getElementById('email-opportunities')?.checked || false,
                    frequency: document.getElementById('opportunity-frequency')?.value || 'daily'
                },
                push: {
                    enabled: document.getElementById('push-opportunities')?.checked || false,
                    frequency: 'immediate'
                }
            },
            credits: {
                email: {
                    enabled: document.getElementById('email-credits')?.checked || false,
                    frequency: 'immediate',
                    settings: {
                        threshold: parseInt(document.getElementById('credit-threshold')?.value) || 50
                    }
                },
                push: {
                    enabled: document.getElementById('email-credits')?.checked || false,
                    frequency: 'immediate'
                }
            },
            templates: {
                email: {
                    enabled: document.getElementById('email-templates')?.checked || false,
                    frequency: 'weekly'
                },
                push: {
                    enabled: false,
                    frequency: 'immediate'
                }
            },
            weekly_summary: {
                email: {
                    enabled: document.getElementById('email-weekly')?.checked || false,
                    frequency: 'weekly'
                },
                push: {
                    enabled: false,
                    frequency: 'weekly'
                }
            },
            urgent_alerts: {
                email: {
                    enabled: true,
                    frequency: 'immediate'
                },
                push: {
                    enabled: document.getElementById('push-urgent')?.checked || false,
                    frequency: 'immediate'
                }
            }
        };
    }
    
    async savePreferences(preferences) {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_save_notification_preferences');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        formData.append('preferences', JSON.stringify(preferences));
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    async handleTestNotification(event) {
        event.preventDefault();
        
        if (!this.options.enableTestNotifications) return;
        
        this.setLoading(true);
        
        try {
            const response = await this.sendTestNotification();
            
            if (response.success) {
                this.showMessage('Test notification sent! Check your email.', 'success');
                this.announceToScreenReader('Test notification sent successfully');
                this.emit('notifications:test-sent');
            } else {
                throw new Error(response.data || 'Failed to send test notification');
            }
            
        } catch (error) {
            this.handleError('Failed to send test notification', error);
            this.showMessage('Failed to send test notification: ' + error.message, 'error');
        } finally {
            this.setLoading(false);
        }
    }
    
    async sendTestNotification() {
        if (!window.chatgabi_ajax) {
            throw new Error('AJAX configuration not available');
        }
        
        const formData = new FormData();
        formData.append('action', 'businesscraft_ai_send_test_notification');
        formData.append('nonce', window.chatgabi_ajax.nonce);
        
        const response = await fetch(window.chatgabi_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    handleNotificationClick(event) {
        const notificationItem = event.currentTarget;
        const notificationId = notificationItem.dataset.notificationId;
        
        if (notificationItem.classList.contains('unread')) {
            this.markNotificationAsRead(notificationId, notificationItem);
        }
        
        this.emit('notifications:notification-clicked', { id: notificationId });
    }
    
    handleNotificationKeydown(event) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.handleNotificationClick(event);
        }
    }
    
    markNotificationAsRead(notificationId, element) {
        element.classList.remove('unread');
        const indicator = element.querySelector('.notification-unread-indicator');
        if (indicator) {
            indicator.remove();
        }
        
        // Update in data
        const notification = this.notifications.find(n => n.id == notificationId);
        if (notification) {
            notification.status = 'read';
        }
        
        this.emit('notifications:notification-read', { id: notificationId });
    }
    
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = this.element.querySelector('.notification-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'notification-message';
            const actions = this.element.querySelector('.notification-actions');
            if (actions) {
                actions.appendChild(messageEl);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `notification-message ${type}`;
        messageEl.style.display = 'block';
        messageEl.setAttribute('role', 'status');
        messageEl.setAttribute('aria-live', 'polite');
        
        // Hide after 5 seconds
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }
    
    setLoading(loading) {
        this.isLoading = loading;
        
        if (this.saveButton) {
            this.saveButton.disabled = loading;
        }
        
        if (this.testButton) {
            this.testButton.disabled = loading;
        }
        
        this.setState({ isLoading: loading });
    }
    
    startPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
        
        this.pollInterval = setInterval(() => {
            if (!this.isLoading) {
                this.loadNotifications();
            }
        }, this.options.pollInterval);
    }
    
    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }
    
    onDestroy() {
        this.stopPolling();
    }
}

// Register component
window.BusinessCraftAI = window.BusinessCraftAI || {};
window.BusinessCraftAI.NotificationCenter = NotificationCenter;

// Auto-register with component manager
if (window.BusinessCraftAI.componentManager) {
    window.BusinessCraftAI.componentManager.registerComponent('notification-center', NotificationCenter, {
        selector: '[data-component="notification-center"], .notifications-container'
    });
}

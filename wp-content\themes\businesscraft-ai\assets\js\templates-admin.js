/**
 * ChatGABI Templates Admin JavaScript
 * 
 * Handles template page functionality including tab switching,
 * modal interactions, and template management.
 */

jQuery(document).ready(function($) {
    console.log('ChatGABI Templates admin script loaded');

    // Template tab switching
    $('.template-tab').on('click', function(e) {
        e.preventDefault();
        console.log('Tab clicked:', $(this).data('category'));

        $('.template-tab').removeClass('active');
        $(this).addClass('active');

        $('.template-category-content').hide();
        $('#' + $(this).data('category')).show();
    });

    // Create new template modal
    $('.create-new-template').on('click', function() {
        const category = $(this).data('category');
        console.log('Create new template clicked for category:', category);
        $('#template-type').val(category.replace('-', '-'));
        $('#template-creation-modal').show();
    });

    // Use template button
    $('.use-template').on('click', function() {
        const templateId = $(this).data('template-id');
        console.log('Use template clicked for ID:', templateId);
        $('#template-creation-modal').show();
    });

    // Preview template button
    $('.preview-template').on('click', function() {
        const templateId = $(this).data('template-id');
        console.log('Preview template clicked for ID:', templateId);

        // Show preview modal
        showTemplatePreview(templateId);
    });

    // Use template from preview
    $('.use-template-from-preview').on('click', function() {
        $('#template-preview-modal').hide();
        $('#template-creation-modal').show();
    });

    // Close modal
    $('.modal-close, .cancel-template').on('click', function() {
        console.log('Modal close clicked');
        $('.template-modal').hide();
    });

    // Country change - update sectors
    $('#target-country').on('change', function() {
        const country = $(this).val();
        if (country) {
            chatgabiLoadSectors(country);
        }
    });

    // Language change - save preference and update preview
    $('#document-language').on('change', function() {
        const selectedLanguage = $(this).val();
        console.log('Language changed to:', selectedLanguage);

        // Save language preference
        saveLanguagePreference(selectedLanguage);

        // Load and display cultural context
        loadCulturalContext(selectedLanguage);

        // Update any open preview modal
        if ($('#template-preview-modal').is(':visible')) {
            const templateId = $('.preview-template:last-clicked').data('template-id') || 'tech-startup';
            const templateCard = $('[data-template-id="' + templateId + '"]').closest('.template-card');
            const templateName = templateCard.find('h3').text() || 'Business Template';
            const templateDescription = templateCard.find('.template-description').text() || 'Professional business template';

            loadTemplatePreviewContent(templateId, templateName, templateDescription, selectedLanguage);
        }
    });

    // Load cultural context on page load
    const initialLanguage = $('#document-language').val();
    if (initialLanguage) {
        loadCulturalContext(initialLanguage);
    }

    // Generate template
    $('#generate-template-btn').on('click', function() {
        const formData = {
            action: 'chatgabi_generate_template',
            nonce: $('#chatgabi_template_nonce').val(),
            template_type: $('#template-type').val(),
            business_idea: $('#business-idea').val(),
            target_country: $('#target-country').val(),
            industry_sector: $('#industry-sector').val(),
            business_stage: $('#business-stage').val(),
            document_language: $('#document-language').val()
        };

        $(this).prop('disabled', true).text(chatgabiTemplatesL10n.generating);

        $.post(ajaxurl, formData, function(response) {
            if (response.success) {
                window.location.href = response.data.redirect_url;
            } else {
                alert(response.data.message || chatgabiTemplatesL10n.error);
            }
        }).always(function() {
            $('#generate-template-btn').prop('disabled', false).text(chatgabiTemplatesL10n.generateDocument);
        });
    });
});

function showTemplatePreview(templateId) {
    console.log('Showing preview for template ID:', templateId);

    // Get template data from the card
    const templateCard = jQuery('[data-template-id="' + templateId + '"]').closest('.template-card');
    const templateName = templateCard.find('h3').text() || 'Business Template';
    const templateDescription = templateCard.find('.template-description').text() || 'Professional business template';

    // Get selected language from the form or user preference
    const selectedLanguage = jQuery('#document-language').val() || 'en';

    // Set modal title
    jQuery('#template-preview-modal .modal-title').text('Preview: ' + templateName);

    // Load preview content with language support
    loadTemplatePreviewContent(templateId, templateName, templateDescription, selectedLanguage);

    // Show modal
    jQuery('#template-preview-modal').show();
}

function loadTemplatePreviewContent(templateId, templateName, templateDescription, selectedLanguage) {
    const previewContent = jQuery('#template-preview-content');

    // Show loading state
    previewContent.html('<div class="loading" style="text-align: center; padding: 20px;"><span class="spinner is-active" style="float: none; margin: 0;"></span><br>' + chatgabiTemplatesL10n.loadingPreview + '</div>');

    // Load language-specific template content via AJAX
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'chatgabi_get_template_preview',
            template_id: templateId,
            language: selectedLanguage,
            nonce: chatgabiTemplatesL10n.previewNonce
        },
        success: function(response) {
            if (response.success && response.data.content) {
                previewContent.html(response.data.content);
            } else {
                // Fallback to static content
                const templateType = templateId.toString();
                let content = '';

                switch(templateType) {
                    case 'tech-startup':
                        content = generateTechStartupPreview(selectedLanguage);
                        break;
                    case 'agricultural':
                        content = generateAgriculturalPreview(selectedLanguage);
                        break;
                    case 'retail':
                        content = generateRetailPreview(selectedLanguage);
                        break;
                    case 'fintech':
                        content = generateFintechPreview(selectedLanguage);
                        break;
                    default:
                        content = generateDefaultPreview(templateName, templateDescription, selectedLanguage);
                }

                previewContent.html(content);
            }
        },
        error: function() {
            // Fallback to static content on error
            const templateType = templateId.toString();
            let content = generateDefaultPreview(templateName, templateDescription, selectedLanguage);
            previewContent.html(content);
        }
    });
}

function generateTechStartupPreview() {
    return `
        <div class="template-preview">
            <h3>${chatgabiTemplatesL10n.techStartupTitle}</h3>
            <p>${chatgabiTemplatesL10n.techStartupDesc}</p>
            <div class="preview-sections">
                <h4>${chatgabiTemplatesL10n.documentSections}</h4>
                <ul>
                    <li>${chatgabiTemplatesL10n.executiveSummary}</li>
                    <li>${chatgabiTemplatesL10n.marketAnalysis}</li>
                    <li>${chatgabiTemplatesL10n.technologyStrategy}</li>
                    <li>${chatgabiTemplatesL10n.financialProjections}</li>
                    <li>${chatgabiTemplatesL10n.fundingRequirements}</li>
                    <li>${chatgabiTemplatesL10n.implementationTimeline}</li>
                </ul>
            </div>
            <div class="preview-features">
                <h4>${chatgabiTemplatesL10n.features}</h4>
                <ul>
                    <li>${chatgabiTemplatesL10n.africanMarketContext}</li>
                    <li>${chatgabiTemplatesL10n.localRegulatory}</li>
                    <li>${chatgabiTemplatesL10n.sectorInsights}</li>
                    <li>${chatgabiTemplatesL10n.multiLanguageSupport}</li>
                </ul>
            </div>
        </div>
    `;
}

function generateDefaultPreview(templateName, templateDescription) {
    return `
        <div class="template-preview">
            <h3>${templateName}</h3>
            <p>${templateDescription}</p>
            <div class="preview-sections">
                <h4>${chatgabiTemplatesL10n.documentFeatures}</h4>
                <ul>
                    <li>${chatgabiTemplatesL10n.professionalTemplate}</li>
                    <li>${chatgabiTemplatesL10n.africanIntelligence}</li>
                    <li>${chatgabiTemplatesL10n.countryRegulatory}</li>
                    <li>${chatgabiTemplatesL10n.sectorData}</li>
                    <li>${chatgabiTemplatesL10n.multiLanguageGeneration}</li>
                    <li>${chatgabiTemplatesL10n.exportReady}</li>
                </ul>
            </div>
        </div>
    `;
}

function saveLanguagePreference(languageCode) {
    jQuery.post(ajaxurl, {
        action: 'chatgabi_save_language_preference',
        language: languageCode,
        nonce: chatgabiTemplatesL10n.saveLanguageNonce
    }, function(response) {
        if (response.success) {
            console.log('Language preference saved:', languageCode);
        } else {
            console.error('Failed to save language preference:', response.data.message);
        }
    });
}

function loadCulturalContext(language) {
    jQuery.post(ajaxurl, {
        action: 'chatgabi_get_cultural_context',
        language: language,
        nonce: chatgabiTemplatesL10n.culturalContextNonce
    }, function(response) {
        if (response.success && response.data) {
            displayCulturalContext(response.data);
        } else {
            hideCulturalContext();
        }
    }).fail(function() {
        console.error('Failed to load cultural context');
        hideCulturalContext();
    });
}

function displayCulturalContext(contextData) {
    const $preview = jQuery('#cultural-context-preview');

    if (!contextData.cultural_practices) {
        hideCulturalContext();
        return;
    }

    const practices = contextData.cultural_practices;

    // Update communication style
    jQuery('#communication-style').text(practices.communication_style || 'Not specified');

    // Update business philosophy (try ubuntu_philosophy or omoluabi_philosophy)
    const philosophy = practices.ubuntu_philosophy || practices.omoluabi_philosophy || practices.business_etiquette || 'Not specified';
    jQuery('#business-philosophy').text(philosophy);

    // Update decision making
    jQuery('#decision-making').text(practices.decision_making || 'Not specified');

    // Update local terms
    const $termsList = jQuery('#local-terms');
    $termsList.empty();

    if (contextData.local_business_terms) {
        const terms = Object.entries(contextData.local_business_terms).slice(0, 6);
        terms.forEach(([english, local]) => {
            $termsList.append(`<span class="term-item">${english} = ${local}</span>`);
        });
    }

    // Show the preview
    $preview.slideDown(300);
}

function hideCulturalContext() {
    jQuery('#cultural-context-preview').slideUp(300);
}

function chatgabiLoadSectors(country) {
    jQuery.post(ajaxurl, {
        action: 'chatgabi_get_sectors',
        country: country,
        nonce: chatgabiTemplatesL10n.getSectorsNonce
    }, function(response) {
        if (response.success) {
            const sectorSelect = jQuery('#industry-sector');
            sectorSelect.empty().append('<option value="">' + chatgabiTemplatesL10n.selectIndustry + '</option>');

            response.data.sectors.forEach(function(sector) {
                sectorSelect.append('<option value="' + sector + '">' + sector + '</option>');
            });
        }
    });
}

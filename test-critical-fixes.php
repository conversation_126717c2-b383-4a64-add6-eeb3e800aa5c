<?php
/**
 * Test Critical REST API and Asset Loading Fixes
 * 
 * Comprehensive test of all critical fixes applied
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Critical Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .test-button:hover { background: #005a87; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f8f9fa; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

<h1>🧪 Test Critical Fixes</h1>

<?php
echo '<div class="info">Critical fixes test started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$all_tests_passed = true;

// Test 1: Check for Duplicate REST Route Registrations
echo '<h2>🔍 Test 1: Duplicate REST Route Registrations</h2>';

$rest_routes = rest_get_server()->get_routes();
$chatgabi_routes = array();
$duplicate_routes = array();

foreach ($rest_routes as $route => $handlers) {
    if (strpos($route, '/chatgabi/v1/') === 0) {
        $chatgabi_routes[] = $route;
        if (count($handlers) > 1) {
            $duplicate_routes[] = $route;
        }
    }
}

echo '<div class="test-result">';
echo '<strong>ChatGABI Routes Found:</strong><br>';
foreach ($chatgabi_routes as $route) {
    echo '- ' . $route . '<br>';
}
echo '</div>';

if (empty($duplicate_routes)) {
    echo '<div class="success">✅ No duplicate route registrations found</div>';
} else {
    echo '<div class="error">❌ Duplicate routes found: ' . implode(', ', $duplicate_routes) . '</div>';
    $all_tests_passed = false;
}

// Test 2: REST API Permission Tests
echo '<h2>🔐 Test 2: REST API Permission Tests</h2>';

$permission_tests = array(
    'templates' => rest_url('chatgabi/v1/templates'),
    'categories' => rest_url('chatgabi/v1/template-categories')
);

foreach ($permission_tests as $endpoint => $url) {
    echo '<div class="info">Testing: ' . $url . '</div>';
    
    $response = wp_remote_get($url, array(
        'timeout' => 15,
        'headers' => array(
            'User-Agent' => 'ChatGABI-Test/1.0'
        )
    ));
    
    if (!is_wp_error($response)) {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code === 200) {
            $data = json_decode($body, true);
            if (isset($data['success']) && $data['success']) {
                echo '<div class="success">✅ ' . ucfirst($endpoint) . ' endpoint: Working (Status: 200)</div>';
                
                if ($endpoint === 'templates' && isset($data['templates'])) {
                    echo '<div class="info">📊 Found ' . count($data['templates']) . ' templates</div>';
                }
                
                if ($endpoint === 'categories' && isset($data['categories'])) {
                    echo '<div class="info">📂 Found ' . count($data['categories']) . ' categories</div>';
                }
            } else {
                echo '<div class="warning">⚠️ ' . ucfirst($endpoint) . ' endpoint: success=false</div>';
                echo '<div class="test-result">Response: <pre>' . esc_html(json_encode($data, JSON_PRETTY_PRINT)) . '</pre></div>';
            }
        } else {
            echo '<div class="error">❌ ' . ucfirst($endpoint) . ' endpoint: Status ' . $status_code . '</div>';
            echo '<div class="test-result">Response: <pre>' . esc_html($body) . '</pre></div>';
            $all_tests_passed = false;
        }
    } else {
        echo '<div class="error">❌ ' . ucfirst($endpoint) . ' endpoint: ' . $response->get_error_message() . '</div>';
        $all_tests_passed = false;
    }
}

// Test 3: API Response Time Test
echo '<h2>⏱️ Test 3: API Response Time Test</h2>';

$start_time = microtime(true);
$response = wp_remote_get(rest_url('chatgabi/v1/templates'), array(
    'timeout' => 30,
    'headers' => array('User-Agent' => 'ChatGABI-Speed-Test/1.0')
));
$end_time = microtime(true);

$response_time = round(($end_time - $start_time) * 1000, 2);

echo '<div class="test-result">';
echo '<strong>Templates API Response Time:</strong> ' . $response_time . ' ms<br>';
echo '</div>';

if ($response_time < 5000) { // Less than 5 seconds
    echo '<div class="success">✅ API response time is acceptable (' . $response_time . ' ms)</div>';
} else {
    echo '<div class="warning">⚠️ API response time is slow (' . $response_time . ' ms)</div>';
}

// Test 4: Asset File Availability
echo '<h2>📁 Test 4: Asset File Availability</h2>';

$asset_files = array(
    'JavaScript' => get_template_directory() . '/assets/js/templates-interface.js',
    'CSS' => get_template_directory() . '/assets/css/templates.css'
);

foreach ($asset_files as $type => $file_path) {
    if (file_exists($file_path)) {
        $file_size = filesize($file_path);
        echo '<div class="success">✅ ' . $type . ' file exists (' . number_format($file_size) . ' bytes)</div>';
        
        if ($file_size < 1000) {
            echo '<div class="warning">⚠️ ' . $type . ' file seems small, may be incomplete</div>';
        }
    } else {
        echo '<div class="error">❌ ' . $type . ' file missing: ' . $file_path . '</div>';
        $all_tests_passed = false;
    }
}

// Test 5: Database Data Availability
echo '<h2>💾 Test 5: Database Data Availability</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

$template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE is_public = 1 AND status = 'active'");
$category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table} WHERE status = 'active'");

echo '<div class="test-result">';
echo '<strong>Database Status:</strong><br>';
echo 'Public templates: ' . $template_count . '<br>';
echo 'Active categories: ' . $category_count . '<br>';
echo '</div>';

if ($template_count >= 3 && $category_count >= 5) {
    echo '<div class="success">✅ Sufficient database data available</div>';
} else {
    echo '<div class="warning">⚠️ Limited database data (may affect functionality)</div>';
}

// Test 6: Live Frontend Test
echo '<h2>🌐 Test 6: Live Frontend Test</h2>';
?>

<div class="test-result">
    <strong>Live API Test Results:</strong>
    <div id="live-test-results">
        <p>Testing frontend API connectivity...</p>
    </div>
</div>

<script>
// Live frontend test
$(document).ready(function() {
    const restUrl = '<?php echo rest_url('chatgabi/v1/'); ?>';
    let testResults = '';
    
    // Test templates endpoint from frontend
    $.ajax({
        url: restUrl + 'templates',
        method: 'GET',
        timeout: 15000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Templates API: Success</div>';
            testResults += '<div class="info">Templates loaded: ' + (data.templates ? data.templates.length : 0) + '</div>';
            
            if (data.templates && data.templates.length > 0) {
                testResults += '<div class="info">Sample template: ' + data.templates[0].title + '</div>';
            }
            
            updateResults();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Templates API Error: ' + error + '</div>';
            testResults += '<div class="warning">Status: ' + xhr.status + ' - ' + xhr.statusText + '</div>';
            if (xhr.responseText) {
                testResults += '<div class="test-result">Response: <pre>' + xhr.responseText.substring(0, 500) + '</pre></div>';
            }
            updateResults();
        }
    });
    
    // Test categories endpoint from frontend
    $.ajax({
        url: restUrl + 'template-categories',
        method: 'GET',
        timeout: 10000,
        success: function(data) {
            testResults += '<div class="success">✅ Frontend Categories API: Success</div>';
            testResults += '<div class="info">Categories loaded: ' + (data.categories ? data.categories.length : 0) + '</div>';
            updateResults();
        },
        error: function(xhr, status, error) {
            testResults += '<div class="error">❌ Frontend Categories API Error: ' + error + '</div>';
            testResults += '<div class="warning">Status: ' + xhr.status + ' - ' + xhr.statusText + '</div>';
            updateResults();
        }
    });
    
    function updateResults() {
        $('#live-test-results').html(testResults);
    }
    
    // Test with category filter
    setTimeout(function() {
        $.ajax({
            url: restUrl + 'templates?category=business-planning',
            method: 'GET',
            timeout: 10000,
            success: function(data) {
                testResults += '<div class="success">✅ Category Filter Test: Success</div>';
                testResults += '<div class="info">Filtered templates: ' + (data.templates ? data.templates.length : 0) + '</div>';
                updateResults();
            },
            error: function(xhr, status, error) {
                testResults += '<div class="error">❌ Category Filter Test: ' + error + '</div>';
                updateResults();
            }
        });
    }, 2000);
});
</script>

<?php
// Final Summary
echo '<h2>🏆 Final Summary</h2>';

if ($all_tests_passed) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL CRITICAL FIXES SUCCESSFUL!</h3>';
    echo '<p><strong>✅ The ChatGABI Templates system should now be fully functional!</strong></p>';
    echo '<ul>';
    echo '<li>✅ No duplicate REST route registrations</li>';
    echo '<li>✅ REST API endpoints responding correctly</li>';
    echo '<li>✅ Permission callbacks fixed (public access)</li>';
    echo '<li>✅ API response times acceptable</li>';
    echo '<li>✅ Asset files available</li>';
    echo '<li>✅ Database contains sufficient data</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues May Remain</h3>';
    echo '<p>Most critical fixes have been applied, but some issues may need additional attention.</p>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Test Actions</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<a href="' . get_permalink($templates_page->ID) . '" target="_blank" class="test-button">🎯 Test Templates Page</a>';
}

echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="test-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/template-categories') . '" target="_blank" class="test-button">📂 Test Categories API</a>';
echo '<a href="debug-templates-page.php" target="_blank" class="test-button">🐛 Debug Page</a>';
echo '<a href="javascript:window.location.reload()" class="test-button">🔄 Re-run Test</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Critical fixes test completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

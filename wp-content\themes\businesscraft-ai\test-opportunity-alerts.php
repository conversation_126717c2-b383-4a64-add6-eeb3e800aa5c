<?php
/**
 * BusinessCraft AI - Opportunity Alerts System Test
 * 
 * Test script to verify the opportunity alerts system functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

echo '<h1>BusinessCraft AI - Opportunity Alerts System Test</h1>';
echo '<p>Testing the opportunity alerts system components...</p>';

$tests_run = 0;
$tests_passed = 0;

// Test 1: Check if classes are loaded
echo '<h2>Test 1: Class Loading</h2>';
$tests_run++;

if (class_exists('ChatGABI_Opportunity_Alerts') && class_exists('ChatGABI_SendPulse_Integration')) {
    echo '✅ All required classes are loaded<br>';
    $tests_passed++;
} else {
    echo '❌ Required classes not found<br>';
    if (!class_exists('ChatGABI_Opportunity_Alerts')) {
        echo '   - ChatGABI_Opportunity_Alerts class missing<br>';
    }
    if (!class_exists('ChatGABI_SendPulse_Integration')) {
        echo '   - ChatGABI_SendPulse_Integration class missing<br>';
    }
}

// Test 2: Check database tables
echo '<h2>Test 2: Database Tables</h2>';
$tests_run++;

global $wpdb;
$required_tables = array(
    'chatgabi_opportunity_alerts',
    'chatgabi_alert_logs',
    'chatgabi_alert_matches'
);

$tables_exist = 0;
foreach ($required_tables as $table_suffix) {
    $table_name = $wpdb->prefix . $table_suffix;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    
    if ($table_exists) {
        echo "✅ Table exists: {$table_name}<br>";
        $tables_exist++;
    } else {
        echo "❌ Table missing: {$table_name}<br>";
    }
}

if ($tables_exist === count($required_tables)) {
    echo "<strong>✅ All {$tables_exist} database tables exist</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>❌ Only {$tables_exist} out of " . count($required_tables) . " tables exist</strong><br>";
}

// Test 3: Check opportunity data loading
echo '<h2>Test 3: Opportunity Data Loading</h2>';
$tests_run++;

if (function_exists('load_opportunities_by_country_sector')) {
    $test_opportunities = load_opportunities_by_country_sector('Ghana');
    if (!empty($test_opportunities)) {
        echo "✅ Opportunity data loading works (" . count($test_opportunities) . " opportunities found for Ghana)<br>";
        echo "   Sample opportunity: " . esc_html($test_opportunities[0]['title']) . "<br>";
        $tests_passed++;
    } else {
        echo "⚠️ No opportunity data found for Ghana<br>";
    }
} else {
    echo "❌ Opportunity loader function not available<br>";
}

// Test 4: Test alert subscription creation
echo '<h2>Test 4: Alert Subscription System</h2>';
$tests_run++;

try {
    $alerts_manager = chatgabi_get_opportunity_alerts();
    
    // Create a test user if needed
    $test_user_id = 1; // Use admin user for testing
    
    $test_filter_data = array(
        'filter_name' => 'Test Alert - Ghana Tech Grants',
        'countries' => array('Ghana'),
        'opportunity_types' => array('Grant'),
        'sectors' => array('Technology'),
        'keywords' => 'startup, innovation',
        'notification_frequency' => 'immediate'
    );
    
    $result = $alerts_manager->save_alert_subscription($test_user_id, $test_filter_data);
    
    if (!is_wp_error($result)) {
        echo "✅ Alert subscription created successfully (ID: {$result})<br>";
        
        // Test retrieving the subscription
        $user_alerts = $alerts_manager->get_user_alert_subscriptions($test_user_id);
        if (!empty($user_alerts)) {
            echo "✅ Alert subscription retrieval works (" . count($user_alerts) . " alerts found)<br>";
            
            // Clean up test alert
            $alerts_manager->delete_alert_subscription($test_user_id, $result);
            echo "✅ Test alert cleaned up<br>";
            
            $tests_passed++;
        } else {
            echo "❌ Failed to retrieve alert subscriptions<br>";
        }
    } else {
        echo "❌ Failed to create alert subscription: " . $result->get_error_message() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Alert subscription test failed: " . $e->getMessage() . "<br>";
}

// Test 5: Test opportunity matching
echo '<h2>Test 5: Opportunity Matching Algorithm</h2>';
$tests_run++;

try {
    $alerts_manager = chatgabi_get_opportunity_alerts();
    
    // Create a test alert for matching
    $test_alert = array(
        'countries' => array('Ghana'),
        'opportunity_types' => array('Grant', 'Loan'),
        'sectors' => array('Agriculture'),
        'keywords' => 'farming, agriculture',
        'amount_min' => null,
        'amount_max' => null,
        'deadline_days' => null
    );
    
    // Load opportunities and test matching
    $all_opportunities = array();
    $countries = array('ghana', 'kenya', 'nigeria', 'south_africa');
    
    foreach ($countries as $country) {
        $opportunities = load_opportunities_by_country_sector(ucfirst(str_replace('_', ' ', $country)));
        if (!empty($opportunities)) {
            $all_opportunities = array_merge($all_opportunities, $opportunities);
        }
    }
    
    if (!empty($all_opportunities)) {
        // Use reflection to access private method for testing
        $reflection = new ReflectionClass($alerts_manager);
        $method = $reflection->getMethod('find_matching_opportunities');
        $method->setAccessible(true);
        
        $matches = $method->invoke($alerts_manager, $test_alert, $all_opportunities);
        
        echo "✅ Opportunity matching algorithm works<br>";
        echo "   Total opportunities: " . count($all_opportunities) . "<br>";
        echo "   Matching opportunities: " . count($matches) . "<br>";
        
        if (!empty($matches)) {
            echo "   Top match: " . esc_html($matches[0]['title']) . " (Score: " . round($matches[0]['match_score'], 1) . "%)<br>";
        }
        
        $tests_passed++;
    } else {
        echo "⚠️ No opportunities available for matching test<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Opportunity matching test failed: " . $e->getMessage() . "<br>";
}

// Test 6: Check cron jobs
echo '<h2>Test 6: Cron Job Scheduling</h2>';
$tests_run++;

try {
    $alerts_manager = chatgabi_get_opportunity_alerts();
    $cron_status = $alerts_manager->get_cron_status();

    echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">';
    echo '<tr><th>Cron Job</th><th>Status</th><th>Schedule</th><th>Next Run</th></tr>';

    $crons_scheduled = 0;
    foreach ($cron_status as $hook => $status) {
        $status_icon = $status['scheduled'] ? '✅' : '❌';
        $status_text = $status['scheduled'] ? 'Scheduled' : 'Not Scheduled';

        echo "<tr>";
        echo "<td>{$status['description']}</td>";
        echo "<td>{$status_icon} {$status_text}</td>";
        echo "<td>{$status['schedule']}</td>";
        echo "<td>{$status['next_run']}</td>";
        echo "</tr>";

        if ($status['scheduled']) {
            $crons_scheduled++;
        }
    }

    echo '</table>';

    if ($crons_scheduled === count($cron_status)) {
        echo "<strong>✅ All {$crons_scheduled} cron jobs are scheduled</strong><br>";
        $tests_passed++;
    } else {
        echo "<strong>❌ Only {$crons_scheduled} out of " . count($cron_status) . " cron jobs are scheduled</strong><br>";

        // Check available schedules
        $available_schedules = wp_get_schedules();
        echo "<br><strong>Available schedules:</strong> " . implode(', ', array_keys($available_schedules)) . "<br>";

        // Check if monthly schedule is available
        if (!isset($available_schedules['monthly'])) {
            echo "⚠️ 'monthly' schedule is missing - this is likely the cause of the cleanup job failure<br>";
        }
    }

} catch (Exception $e) {
    echo "❌ Cron job test failed: " . $e->getMessage() . "<br>";
}

// Test 7: Check SendPulse integration
echo '<h2>Test 7: SendPulse Integration</h2>';
$tests_run++;

try {
    $sendpulse = chatgabi_get_sendpulse();
    
    if ($sendpulse) {
        echo "✅ SendPulse integration class available<br>";
        
        // Check if credentials are configured
        $user_id = get_option('chatgabi_sendpulse_user_id');
        $secret = get_option('chatgabi_sendpulse_secret');
        
        if (!empty($user_id) && !empty($secret)) {
            echo "✅ SendPulse credentials are configured<br>";
            
            // Test connection (only if credentials are set)
            $connection_test = $sendpulse->test_connection();
            if (!is_wp_error($connection_test)) {
                echo "✅ SendPulse connection test passed<br>";
                $tests_passed++;
            } else {
                echo "⚠️ SendPulse connection test failed: " . $connection_test->get_error_message() . "<br>";
                echo "   (This is expected if credentials are not properly configured)<br>";
            }
        } else {
            echo "⚠️ SendPulse credentials not configured<br>";
            echo "   Please configure in ChatGABI → Email Settings<br>";
        }
    } else {
        echo "❌ SendPulse integration not available<br>";
    }
    
} catch (Exception $e) {
    echo "❌ SendPulse integration test failed: " . $e->getMessage() . "<br>";
}

// Test 8: Check AJAX endpoints
echo '<h2>Test 8: AJAX Endpoints</h2>';
$tests_run++;

$ajax_actions = array(
    'chatgabi_save_alert_subscription',
    'chatgabi_delete_alert_subscription',
    'chatgabi_toggle_alert_subscription',
    'chatgabi_preview_alert_matches',
    'chatgabi_get_alert_statistics'
);

$ajax_registered = 0;
foreach ($ajax_actions as $action) {
    if (has_action("wp_ajax_{$action}")) {
        echo "✅ AJAX action registered: {$action}<br>";
        $ajax_registered++;
    } else {
        echo "❌ AJAX action not registered: {$action}<br>";
    }
}

if ($ajax_registered === count($ajax_actions)) {
    echo "<strong>✅ All {$ajax_registered} AJAX endpoints are registered</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>❌ Only {$ajax_registered} out of " . count($ajax_actions) . " AJAX endpoints are registered</strong><br>";
}

// Final Summary
echo '<hr>';
echo '<h2>🎯 Test Summary</h2>';
echo "<p><strong>Tests Passed: {$tests_passed} / {$tests_run}</strong></p>";

$success_rate = ($tests_passed / $tests_run) * 100;

if ($success_rate >= 90) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>🎉 Excellent! System is working properly</h3>';
    echo '<p>The opportunity alerts system is functioning correctly and ready for production use.</p>';
    echo '</div>';
} elseif ($success_rate >= 70) {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>⚠️ Good, but some issues need attention</h3>';
    echo '<p>Most components are working, but please address the failed tests above.</p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>❌ System needs significant fixes</h3>';
    echo '<p>Multiple components are not working properly. Please review and fix the issues above.</p>';
    echo '</div>';
}

echo '<h3>📋 Next Steps:</h3>';
echo '<ul>';
echo '<li><strong>Configure SendPulse:</strong> Set up your SendPulse API credentials in ChatGABI → Email Settings</li>';
echo '<li><strong>Test User Interface:</strong> Visit the <a href="' . home_url('/alerts/') . '">Alerts Dashboard</a> to test the user interface</li>';
echo '<li><strong>Create Test Alert:</strong> Create a test alert subscription and verify email delivery</li>';
echo '<li><strong>Monitor Logs:</strong> Check WordPress error logs for any issues</li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Test completed at ' . current_time('mysql') . '</em></p>';
echo '<p><a href="' . admin_url() . '">← Return to WordPress Admin</a></p>';
?>

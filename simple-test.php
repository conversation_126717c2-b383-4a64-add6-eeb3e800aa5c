<?php
/**
 * Simple Test File for BusinessCraft AI (No WordPress Loading)
 * Access via: http://localhost/swifmind-local/simple-test.php
 * 
 * This file tests basic functionality without loading WordPress
 */

?>
<!DOCTYPE html>
<html>
<head>
    <title>BusinessCraft AI - Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-left: 4px solid #0073aa; border-radius: 4px; }
        .success { color: #46b450; font-weight: bold; }
        .error { color: #dc3232; font-weight: bold; }
        .warning { color: #ffb900; font-weight: bold; }
        .button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
        h1 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 0; }
        pre { background: #fff; padding: 10px; border: 1px solid #ddd; overflow-x: auto; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 BusinessCraft AI - Simple Test</h1>
        <p><strong>Test URL:</strong> <code><?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?></code></p>
        <p><strong>Timestamp:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="test-section">
            <h2>📊 Server Information</h2>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
            <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
            <p><strong>Current Directory:</strong> <?php echo __DIR__; ?></p>
        </div>
        
        <div class="test-section">
            <h2>📁 File System Check</h2>
            <?php
            $paths_to_check = [
                'WordPress Root' => __DIR__,
                'wp-config.php' => __DIR__ . '/wp-config.php',
                'wp-load.php' => __DIR__ . '/wp-load.php',
                'BusinessCraft AI Theme' => __DIR__ . '/wp-content/themes/businesscraft-ai',
                'Theme functions.php' => __DIR__ . '/wp-content/themes/businesscraft-ai/functions.php',
                'Data Loader' => __DIR__ . '/wp-content/themes/businesscraft-ai/inc/data-loader.php',
                'Opportunity Loader' => __DIR__ . '/wp-content/themes/businesscraft-ai/inc/opportunity-loader.php',
                'Datasets Directory' => __DIR__ . '/wp-content/datasets',
                'Ghana Dataset' => __DIR__ . '/wp-content/datasets/ghana-business-data',
                'Kenya Dataset' => __DIR__ . '/wp-content/datasets/kenya-business-data',
                'Nigeria Dataset' => __DIR__ . '/wp-content/datasets/nigeria-business-data',
                'South Africa Dataset' => __DIR__ . '/wp-content/datasets/south-africa-business-data',
                'Opportunities Directory' => __DIR__ . '/wp-content/datasets/opportunities'
            ];
            
            foreach ($paths_to_check as $name => $path) {
                if (file_exists($path)) {
                    echo "<p class='success'>✅ {$name}: <code>{$path}</code></p>";
                } else {
                    echo "<p class='error'>❌ {$name}: <code>{$path}</code> NOT FOUND</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🌍 Dataset File Check</h2>
            <?php
            $dataset_countries = ['ghana', 'kenya', 'nigeria', 'south-africa'];
            foreach ($dataset_countries as $country) {
                $dataset_dir = __DIR__ . "/wp-content/datasets/{$country}-business-data";
                echo "<h3>" . ucfirst($country) . " Dataset</h3>";
                
                if (is_dir($dataset_dir)) {
                    $files = glob($dataset_dir . '/*.json');
                    if ($files) {
                        echo "<p class='success'>✅ Found " . count($files) . " JSON files</p>";
                        foreach ($files as $file) {
                            $filename = basename($file);
                            $filesize = round(filesize($file) / 1024, 2);
                            echo "<p>📄 {$filename} ({$filesize} KB)</p>";
                        }
                    } else {
                        echo "<p class='warning'>⚠️ Directory exists but no JSON files found</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Dataset directory not found</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>💼 Opportunities Check</h2>
            <?php
            $opportunities_dir = __DIR__ . '/wp-content/datasets/opportunities';
            if (is_dir($opportunities_dir)) {
                $opp_files = glob($opportunities_dir . '/*.json');
                if ($opp_files) {
                    echo "<p class='success'>✅ Found " . count($opp_files) . " opportunity files</p>";
                    foreach ($opp_files as $file) {
                        $filename = basename($file);
                        $filesize = round(filesize($file) / 1024, 2);
                        
                        // Try to read and count opportunities
                        $content = file_get_contents($file);
                        $data = json_decode($content, true);
                        $count = is_array($data) ? count($data) : 0;
                        
                        echo "<p>📄 {$filename} ({$filesize} KB) - {$count} opportunities</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠️ Opportunities directory exists but no files found</p>";
                }
            } else {
                echo "<p class='error'>❌ Opportunities directory not found</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🔧 WordPress Status</h2>
            <?php
            if (file_exists(__DIR__ . '/wp-config.php')) {
                echo "<p class='success'>✅ WordPress configuration found</p>";
                
                // Try to determine if WordPress is working
                $wp_load_exists = file_exists(__DIR__ . '/wp-load.php');
                echo "<p class='success'>✅ wp-load.php: " . ($wp_load_exists ? 'Found' : 'Not Found') . "</p>";
                
                // Check if we can access WordPress admin
                $admin_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/wp-admin/';
                echo "<p><strong>WordPress Admin URL:</strong> <a href='{$admin_url}' target='_blank'>{$admin_url}</a></p>";
                
                // Check if we can access the site
                $site_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/';
                echo "<p><strong>WordPress Site URL:</strong> <a href='{$site_url}' target='_blank'>{$site_url}</a></p>";
                
            } else {
                echo "<p class='error'>❌ WordPress configuration not found</p>";
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🎯 Next Steps</h2>
            <p>If all files are found above, try these options:</p>
            
            <h3>Option 1: WordPress Admin Dashboard</h3>
            <p>Go to WordPress admin and look for "BusinessCraft AI" in the sidebar menu.</p>
            <a href="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/wp-admin/'; ?>" class="button" target="_blank">🔗 WordPress Admin</a>
            
            <h3>Option 2: Try Loading WordPress Test</h3>
            <p>If the function redeclaration error is fixed, try the WordPress test file:</p>
            <a href="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/businesscraft-ai-test.php'; ?>" class="button" target="_blank">🔗 WordPress Test</a>
            
            <h3>Option 3: Direct Theme Access</h3>
            <p>Access the WordPress site directly:</p>
            <a href="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/'; ?>" class="button" target="_blank">🔗 WordPress Site</a>
        </div>
        
        <div class="test-section">
            <h2>📋 Summary</h2>
            <?php
            $total_checks = 0;
            $passed_checks = 0;
            
            // Count file checks
            foreach ($paths_to_check as $name => $path) {
                $total_checks++;
                if (file_exists($path)) $passed_checks++;
            }
            
            $success_rate = round(($passed_checks / $total_checks) * 100, 1);
            $status_class = $success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error');
            ?>
            <p class="<?php echo $status_class; ?>">
                <strong>File System Status:</strong> <?php echo $passed_checks; ?>/<?php echo $total_checks; ?> checks passed (<?php echo $success_rate; ?>%)
            </p>
            
            <?php if ($success_rate >= 80): ?>
                <p class="success">🎉 File system looks good! The issue is likely with WordPress function loading.</p>
                <p><strong>Recommendation:</strong> Try accessing WordPress admin dashboard directly.</p>
            <?php elseif ($success_rate >= 60): ?>
                <p class="warning">⚠️ Some files missing. Check the failed items above.</p>
            <?php else: ?>
                <p class="error">🚨 Multiple files missing. Please check your installation.</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

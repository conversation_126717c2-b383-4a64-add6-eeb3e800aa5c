/**
 * ChatGABI Prompt Templates Styles
 * 
 * Styles for the prompt templates management interface
 */

/* Main Container */
.chatgabi-templates-manager {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 30px;
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 124, 186, 0.2);
}

.header-content {
    flex: 1;
}

.templates-title {
    margin: 0 0 10px 0;
    font-size: 2.2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    font-size: 2.5rem;
}

.templates-description {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.header-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.95rem;
}

.btn-primary {
    background: #28a745;
    color: white;
    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.btn-primary:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1rem;
}

/* Filters Section */
.templates-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

/* Templates Grid */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.template-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f8f9fa;
}

.template-category {
    font-weight: 600;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.template-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.template-card:hover .template-actions {
    opacity: 1;
}

.template-action {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.template-action:hover {
    background: #f8f9fa;
}

.template-content {
    padding: 15px 20px;
}

.template-title {
    margin: 0 0 8px 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1.3;
}

.template-description {
    margin: 0 0 15px 0;
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #6c757d;
}

.meta-icon {
    font-size: 0.9rem;
}

.public-badge {
    background: #17a2b8;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
}

.tag {
    background: #f8f9fa;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    border: 1px solid #e9ecef;
}

.template-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #f8f9fa;
    font-size: 0.8rem;
    color: #6c757d;
}

.template-date {
    font-weight: 500;
}

.template-language {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Loading and Empty States */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6c757d;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-templates,
.error-message {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
    font-size: 1.1rem;
}

/* Load More Section */
.load-more-section {
    text-align: center;
    margin: 30px 0;
}

/* Modal Styles */
.template-modal,
.template-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: #f8f9fa;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    margin-top: 6px;
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    background: white;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #007cba;
    border-color: #007cba;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Preview Modal Specific Styles */
.preview-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.template-content h4 {
    margin: 20px 0 10px 0;
    color: #495057;
}

.template-content pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
}

.rating-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.star-rating {
    display: flex;
    gap: 2px;
}

.star {
    cursor: pointer;
    font-size: 1.2rem;
    transition: transform 0.2s ease;
}

.star:hover {
    transform: scale(1.2);
}

/* Messages */
.templates-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    max-width: 400px;
}

.template-message {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    font-weight: 500;
    animation: slideInRight 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.template-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.template-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message-icon {
    margin-right: 10px;
    font-size: 1.1rem;
}

.message-text {
    flex: 1;
}

.message-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;
    margin-left: 10px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-close:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatgabi-templates-manager {
        padding: 15px;
    }
    
    .templates-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .templates-title {
        font-size: 1.8rem;
    }
    
    .templates-filters {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .templates-messages {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .templates-title {
        font-size: 1.6rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .btn-primary,
    .btn-secondary {
        flex: 1;
        justify-content: center;
    }
}

/* Template Selection Modal for Chat Interface */
.chatgabi-template-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999999;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.chatgabi-template-modal.active {
    display: flex;
}

.template-modal-content {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.template-modal-header {
    padding: 24px;
    border-bottom: 1px solid #e1e5e9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.template-modal-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.template-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.template-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.template-modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* Template Filters */
.template-filters {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.template-search {
    flex: 1;
    min-width: 200px;
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.template-category-filter,
.template-complexity-filter {
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    min-width: 150px;
}

/* Template Categories */
.template-categories {
    display: flex;
    padding: 20px 24px;
    gap: 12px;
    overflow-x: auto;
    border-bottom: 1px solid #e1e5e9;
}

.category-tab {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    background: #fff;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    white-space: nowrap;
    transition: all 0.2s;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
}

.category-tab:hover {
    border-color: #007cba;
    color: #007cba;
}

.category-tab.active {
    background: #007cba;
    border-color: #007cba;
    color: white;
}

.category-icon {
    font-size: 16px;
}

/* Template Grid */
.templates-grid {
    padding: 24px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    min-height: 300px;
}

.template-card {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    background: #fff;
    transition: all 0.2s;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.template-card:hover {
    border-color: #007cba;
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.1);
    transform: translateY(-2px);
}

.template-card.featured {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
}

.template-card.featured::before {
    content: "⭐";
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 16px;
}

.template-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.template-category-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    flex-shrink: 0;
}

.template-info {
    flex: 1;
}

.template-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.template-category {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.template-description {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    margin: 0 0 16px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
    margin-bottom: 16px;
}

.template-complexity {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.template-complexity.beginner {
    background: #d4edda;
    color: #155724;
}

.template-complexity.intermediate {
    background: #fff3cd;
    color: #856404;
}

.template-complexity.advanced {
    background: #f8d7da;
    color: #721c24;
}

.template-actions {
    display: flex;
    gap: 8px;
}

.template-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    color: #666;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.template-btn:hover {
    border-color: #007cba;
    color: #007cba;
}

.template-btn.primary {
    background: #007cba;
    border-color: #007cba;
    color: white;
}

.template-btn.primary:hover {
    background: #005a87;
    border-color: #005a87;
}

/* Template Preview Modal */
.template-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.template-preview-modal.active {
    display: flex;
}

.template-preview-content {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.template-preview-header {
    padding: 24px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.template-preview-body {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

.template-preview-text {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    white-space: pre-wrap;
    margin-bottom: 20px;
}

.template-variables {
    margin-bottom: 20px;
}

.template-variables h4 {
    margin: 0 0 12px 0;
    color: #333;
}

.variable-input {
    margin-bottom: 12px;
}

.variable-input label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #333;
}

.variable-input input,
.variable-input select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.template-preview-footer {
    padding: 24px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Loading States */
.templates-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #666;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.templates-empty {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.templates-empty h3 {
    margin: 0 0 8px 0;
    color: #333;
}

/* Integration with Chat Interface */
.businesscraft-ai-chat .template-trigger {
    margin-bottom: 16px;
}

.template-trigger-btn {
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
    margin-bottom: 12px;
}

.template-trigger-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.template-trigger-btn:active {
    transform: translateY(0);
}

/* Template Modal Responsive Design */
@media (max-width: 768px) {
    .chatgabi-template-modal .template-modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }

    .chatgabi-template-modal .templates-grid {
        grid-template-columns: 1fr;
        padding: 16px;
    }

    .chatgabi-template-modal .template-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .chatgabi-template-modal .template-search,
    .chatgabi-template-modal .template-category-filter,
    .chatgabi-template-modal .template-complexity-filter {
        width: 100%;
    }

    .chatgabi-template-modal .template-categories {
        padding: 16px;
    }

    .template-preview-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }
}

<?php

/**
 * SimplePie
 *
 * A PHP-Based RSS and Atom Feed Framework.
 * Takes the hard work out of managing a complete RSS/Atom solution.
 *
 * Copyright (c) 2004-2022, <PERSON>, <PERSON>, <PERSON>, and contributors
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are
 * permitted provided that the following conditions are met:
 *
 * 	* Redistributions of source code must retain the above copyright notice, this list of
 * 	  conditions and the following disclaimer.
 *
 * 	* Redistributions in binary form must reproduce the above copyright notice, this list
 * 	  of conditions and the following disclaimer in the documentation and/or other materials
 * 	  provided with the distribution.
 *
 * 	* Neither the name of the SimplePie Team nor the names of its contributors may be used
 * 	  to endorse or promote products derived from this software without specific prior
 * 	  written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS
 * AND CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * @package SimplePie
 * @copyright 2004-2016 Ryan Parman, Sam Sneddon, Ryan McCue
 * <AUTHOR> Parman
 * <AUTHOR> Sneddon
 * <AUTHOR> McCue
 * @link http://simplepie.org/ SimplePie
 * @license http://www.opensource.org/licenses/bsd-license.php BSD License
 */

namespace SimplePie\Content\Type;

/**
 * Content-type sniffing
 *
 * Based on the rules in http://tools.ietf.org/html/draft-abarth-mime-sniff-06
 *
 * This is used since we can't always trust Content-Type headers, and is based
 * upon the HTML5 parsing rules.
 *
 *
 * This class can be overloaded with {@see \SimplePie\SimplePie::set_content_type_sniffer_class()}
 *
 * @package SimplePie
 * @subpackage HTTP
 */
class Sniffer
{
    /**
     * File object
     *
     * @var \SimplePie\File
     */
    public $file;

    /**
     * Create an instance of the class with the input file
     *
     * @param Sniffer $file Input file
     */
    public function __construct($file)
    {
        $this->file = $file;
    }

    /**
     * Get the Content-Type of the specified file
     *
     * @return string Actual Content-Type
     */
    public function get_type()
    {
        if (isset($this->file->headers['content-type'])) {
            if (!isset($this->file->headers['content-encoding'])
                && ($this->file->headers['content-type'] === 'text/plain'
                    || $this->file->headers['content-type'] === 'text/plain; charset=ISO-8859-1'
                    || $this->file->headers['content-type'] === 'text/plain; charset=iso-8859-1'
                    || $this->file->headers['content-type'] === 'text/plain; charset=UTF-8')) {
                return $this->text_or_binary();
            }

            if (($pos = strpos($this->file->headers['content-type'], ';')) !== false) {
                $official = substr($this->file->headers['content-type'], 0, $pos);
            } else {
                $official = $this->file->headers['content-type'];
            }
            $official = trim(strtolower($official));

            if ($official === 'unknown/unknown'
                || $official === 'application/unknown') {
                return $this->unknown();
            } elseif (substr($official, -4) === '+xml'
                || $official === 'text/xml'
                || $official === 'application/xml') {
                return $official;
            } elseif (substr($official, 0, 6) === 'image/') {
                if ($return = $this->image()) {
                    return $return;
                }

                return $official;
            } elseif ($official === 'text/html') {
                return $this->feed_or_html();
            }

            return $official;
        }

        return $this->unknown();
    }

    /**
     * Sniff text or binary
     *
     * @return string Actual Content-Type
     */
    public function text_or_binary()
    {
        if (substr($this->file->body, 0, 2) === "\xFE\xFF"
            || substr($this->file->body, 0, 2) === "\xFF\xFE"
            || substr($this->file->body, 0, 4) === "\x00\x00\xFE\xFF"
            || substr($this->file->body, 0, 3) === "\xEF\xBB\xBF") {
            return 'text/plain';
        } elseif (preg_match('/[\x00-\x08\x0E-\x1A\x1C-\x1F]/', $this->file->body)) {
            return 'application/octet-stream';
        }

        return 'text/plain';
    }

    /**
     * Sniff unknown
     *
     * @return string Actual Content-Type
     */
    public function unknown()
    {
        $ws = strspn($this->file->body, "\x09\x0A\x0B\x0C\x0D\x20");
        if (strtolower(substr($this->file->body, $ws, 14)) === '<!doctype html'
            || strtolower(substr($this->file->body, $ws, 5)) === '<html'
            || strtolower(substr($this->file->body, $ws, 7)) === '<script') {
            return 'text/html';
        } elseif (substr($this->file->body, 0, 5) === '%PDF-') {
            return 'application/pdf';
        } elseif (substr($this->file->body, 0, 11) === '%!PS-Adobe-') {
            return 'application/postscript';
        } elseif (substr($this->file->body, 0, 6) === 'GIF87a'
            || substr($this->file->body, 0, 6) === 'GIF89a') {
            return 'image/gif';
        } elseif (substr($this->file->body, 0, 8) === "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A") {
            return 'image/png';
        } elseif (substr($this->file->body, 0, 3) === "\xFF\xD8\xFF") {
            return 'image/jpeg';
        } elseif (substr($this->file->body, 0, 2) === "\x42\x4D") {
            return 'image/bmp';
        } elseif (substr($this->file->body, 0, 4) === "\x00\x00\x01\x00") {
            return 'image/vnd.microsoft.icon';
        }

        return $this->text_or_binary();
    }

    /**
     * Sniff images
     *
     * @return string Actual Content-Type
     */
    public function image()
    {
        if (substr($this->file->body, 0, 6) === 'GIF87a'
            || substr($this->file->body, 0, 6) === 'GIF89a') {
            return 'image/gif';
        } elseif (substr($this->file->body, 0, 8) === "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A") {
            return 'image/png';
        } elseif (substr($this->file->body, 0, 3) === "\xFF\xD8\xFF") {
            return 'image/jpeg';
        } elseif (substr($this->file->body, 0, 2) === "\x42\x4D") {
            return 'image/bmp';
        } elseif (substr($this->file->body, 0, 4) === "\x00\x00\x01\x00") {
            return 'image/vnd.microsoft.icon';
        }

        return false;
    }

    /**
     * Sniff HTML
     *
     * @return string Actual Content-Type
     */
    public function feed_or_html()
    {
        $len = strlen($this->file->body);
        $pos = strspn($this->file->body, "\x09\x0A\x0D\x20\xEF\xBB\xBF");

        while ($pos < $len) {
            switch ($this->file->body[$pos]) {
                case "\x09":
                case "\x0A":
                case "\x0D":
                case "\x20":
                    $pos += strspn($this->file->body, "\x09\x0A\x0D\x20", $pos);
                    continue 2;

                case '<':
                    $pos++;
                    break;

                default:
                    return 'text/html';
            }

            if (substr($this->file->body, $pos, 3) === '!--') {
                $pos += 3;
                if ($pos < $len && ($pos = strpos($this->file->body, '-->', $pos)) !== false) {
                    $pos += 3;
                } else {
                    return 'text/html';
                }
            } elseif (substr($this->file->body, $pos, 1) === '!') {
                if ($pos < $len && ($pos = strpos($this->file->body, '>', $pos)) !== false) {
                    $pos++;
                } else {
                    return 'text/html';
                }
            } elseif (substr($this->file->body, $pos, 1) === '?') {
                if ($pos < $len && ($pos = strpos($this->file->body, '?>', $pos)) !== false) {
                    $pos += 2;
                } else {
                    return 'text/html';
                }
            } elseif (substr($this->file->body, $pos, 3) === 'rss'
                || substr($this->file->body, $pos, 7) === 'rdf:RDF') {
                return 'application/rss+xml';
            } elseif (substr($this->file->body, $pos, 4) === 'feed') {
                return 'application/atom+xml';
            } else {
                return 'text/html';
            }
        }

        return 'text/html';
    }
}

class_alias('SimplePie\Content\Type\Sniffer', 'SimplePie_Content_Type_Sniffer');

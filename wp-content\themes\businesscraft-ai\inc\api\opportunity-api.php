<?php
/**
 * ChatGABI Opportunities REST API
 *
 * Provides REST endpoints for external access to business opportunities data
 * Enables mobile apps, frontend dashboards, and AI integrations
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register ChatGABI Opportunities REST API routes
 */
function chatgabi_register_opportunities_api() {
    // Main opportunities endpoint with filtering
    register_rest_route('chatgabi/v1', '/opportunities', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_get_opportunities',
        'permission_callback' => '__return_true',
        'args' => array(
            'country' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'description' => 'Filter by country (Ghana, Kenya, Nigeria, South Africa)',
            ),
            'sector' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'description' => 'Filter by business sector',
            ),
            'type' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'description' => 'Filter by opportunity type (Grant, Incubator, Loan, etc.)',
            ),
            'search' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'description' => 'Search in title and summary',
            ),
            'sort' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'soonest',
                'enum' => array('soonest', 'latest', 'alphabetical'),
                'description' => 'Sort order for results',
            ),
            'limit' => array(
                'required' => false,
                'type' => 'integer',
                'default' => 10,
                'minimum' => 1,
                'maximum' => 50,
                'description' => 'Maximum number of results to return',
            ),
            'offset' => array(
                'required' => false,
                'type' => 'integer',
                'default' => 0,
                'minimum' => 0,
                'description' => 'Number of results to skip (for pagination)',
            ),
        ),
    ));

    // Opportunity statistics endpoint
    register_rest_route('chatgabi/v1', '/opportunities/stats', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_get_opportunity_stats',
        'permission_callback' => '__return_true',
    ));

    // Available opportunity types endpoint
    register_rest_route('chatgabi/v1', '/opportunities/types', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_get_opportunity_types',
        'permission_callback' => '__return_true',
    ));

    // Available sectors endpoint
    register_rest_route('chatgabi/v1', '/opportunities/sectors', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_get_opportunity_sectors',
        'permission_callback' => '__return_true',
        'args' => array(
            'country' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'description' => 'Filter sectors by country',
            ),
        ),
    ));

    // Available countries endpoint
    register_rest_route('chatgabi/v1', '/opportunities/countries', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_get_opportunity_countries',
        'permission_callback' => '__return_true',
    ));

    // Single opportunity by ID endpoint
    register_rest_route('chatgabi/v1', '/opportunities/(?P<id>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_get_single_opportunity',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    // API health check endpoint
    register_rest_route('chatgabi/v1', '/opportunities/health', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_api_health_check',
        'permission_callback' => '__return_true',
    ));
}
add_action('rest_api_init', 'chatgabi_register_opportunities_api');

/**
 * Main opportunities endpoint - Get filtered opportunities
 */
function chatgabi_api_get_opportunities($request) {
    // Get parameters
    $country = $request->get_param('country');
    $sector = $request->get_param('sector');
    $type = $request->get_param('type');
    $search = $request->get_param('search');
    $sort = $request->get_param('sort') ?: 'soonest';
    $limit = intval($request->get_param('limit') ?: 10);
    $offset = intval($request->get_param('offset') ?: 0);

    // Create cache key based on parameters
    $cache_key = 'chatgabi_api_opps_' . md5(serialize($request->get_params()));
    $cached_result = get_transient($cache_key);

    if ($cached_result !== false) {
        // Add cache hit header
        $response = new WP_REST_Response($cached_result, 200);
        $response->header('X-ChatGABI-Cache', 'HIT');
        return $response;
    }

    try {
        // Start with all opportunities or filter by country
        if ($country) {
            $opportunities = load_opportunities_by_country_sector($country, $sector ?: '');
        } else {
            $opportunities = array();
            $countries = get_available_opportunity_countries();
            foreach ($countries as $country_name) {
                $country_opps = load_opportunities_by_country_sector($country_name, $sector ?: '');
                $opportunities = array_merge($opportunities, $country_opps);
            }
        }

        // Apply filters
        if ($type) {
            $opportunities = array_filter($opportunities, function($opp) use ($type) {
                return isset($opp['type']) && strtolower($opp['type']) === strtolower($type);
            });
        }

        if ($search) {
            $opportunities = array_filter($opportunities, function($opp) use ($search) {
                $searchable = ($opp['title'] ?? '') . ' ' . ($opp['summary'] ?? '') . ' ' . ($opp['description'] ?? '');
                return stripos($searchable, $search) !== false;
            });
        }

        // Apply sorting
        switch ($sort) {
            case 'soonest':
                usort($opportunities, function($a, $b) {
                    $date_a = isset($a['deadline']) ? strtotime($a['deadline']) : PHP_INT_MAX;
                    $date_b = isset($b['deadline']) ? strtotime($b['deadline']) : PHP_INT_MAX;
                    return $date_a - $date_b;
                });
                break;
            case 'latest':
                usort($opportunities, function($a, $b) {
                    $date_a = isset($a['deadline']) ? strtotime($a['deadline']) : 0;
                    $date_b = isset($b['deadline']) ? strtotime($b['deadline']) : 0;
                    return $date_b - $date_a;
                });
                break;
            case 'alphabetical':
                usort($opportunities, function($a, $b) {
                    return strcmp($a['title'] ?? '', $b['title'] ?? '');
                });
                break;
        }

        // Apply pagination
        $total_count = count($opportunities);
        $opportunities = array_slice(array_values($opportunities), $offset, $limit);

        // Prepare response
        $response_data = array(
            'opportunities' => $opportunities,
            'pagination' => array(
                'total' => $total_count,
                'count' => count($opportunities),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $total_count,
            ),
            'filters_applied' => array(
                'country' => $country,
                'sector' => $sector,
                'type' => $type,
                'search' => $search,
                'sort' => $sort,
            ),
            'timestamp' => current_time('c'),
        );

        // Cache for 5 minutes
        set_transient($cache_key, $response_data, 5 * MINUTE_IN_SECONDS);

        $response = new WP_REST_Response($response_data, 200);
        $response->header('X-ChatGABI-Cache', 'MISS');
        $response->header('X-ChatGABI-Total', $total_count);
        
        return $response;

    } catch (Exception $e) {
        return new WP_Error(
            'chatgabi_api_error',
            'Failed to fetch opportunities: ' . $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * Get opportunity statistics
 */
function chatgabi_api_get_opportunity_stats($request) {
    $cache_key = 'chatgabi_api_stats';
    $cached_stats = get_transient($cache_key);

    if ($cached_stats !== false) {
        $response = new WP_REST_Response($cached_stats, 200);
        $response->header('X-ChatGABI-Cache', 'HIT');
        return $response;
    }

    try {
        $stats = array(
            'total_opportunities' => 0,
            'by_country' => array(),
            'by_type' => array(),
            'by_sector' => array(),
            'upcoming_deadlines' => array(),
            'last_updated' => current_time('c'),
        );

        $countries = get_available_opportunity_countries();
        foreach ($countries as $country) {
            $opportunities = load_opportunities_by_country_sector($country, '');
            $country_count = count($opportunities);

            $stats['total_opportunities'] += $country_count;
            $stats['by_country'][$country] = $country_count;

            foreach ($opportunities as $opp) {
                // Count by type
                $type = $opp['type'] ?? 'Unknown';
                $stats['by_type'][$type] = ($stats['by_type'][$type] ?? 0) + 1;

                // Count by sector
                $sector = $opp['sector'] ?? 'General';
                $stats['by_sector'][$sector] = ($stats['by_sector'][$sector] ?? 0) + 1;

                // Track upcoming deadlines (next 30 days)
                if (isset($opp['deadline'])) {
                    $deadline = strtotime($opp['deadline']);
                    $now = time();
                    $thirty_days = 30 * 24 * 60 * 60;

                    if ($deadline > $now && $deadline <= ($now + $thirty_days)) {
                        $stats['upcoming_deadlines'][] = array(
                            'title' => $opp['title'],
                            'deadline' => $opp['deadline'],
                            'country' => $country,
                            'type' => $type,
                        );
                    }
                }
            }
        }

        // Sort upcoming deadlines by date
        usort($stats['upcoming_deadlines'], function($a, $b) {
            return strtotime($a['deadline']) - strtotime($b['deadline']);
        });

        // Limit upcoming deadlines to 10
        $stats['upcoming_deadlines'] = array_slice($stats['upcoming_deadlines'], 0, 10);

        // Cache for 10 minutes
        set_transient($cache_key, $stats, 10 * MINUTE_IN_SECONDS);

        $response = new WP_REST_Response($stats, 200);
        $response->header('X-ChatGABI-Cache', 'MISS');

        return $response;

    } catch (Exception $e) {
        return new WP_Error(
            'chatgabi_api_error',
            'Failed to fetch statistics: ' . $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * Get available opportunity types
 */
function chatgabi_api_get_opportunity_types($request) {
    $cache_key = 'chatgabi_api_types';
    $cached_types = get_transient($cache_key);

    if ($cached_types !== false) {
        $response = new WP_REST_Response($cached_types, 200);
        $response->header('X-ChatGABI-Cache', 'HIT');
        return $response;
    }

    try {
        $types = array();
        $countries = get_available_opportunity_countries();

        foreach ($countries as $country) {
            $opportunities = load_opportunities_by_country_sector($country, '');
            foreach ($opportunities as $opp) {
                if (isset($opp['type'])) {
                    $types[] = $opp['type'];
                }
            }
        }

        $types = array_unique($types);
        sort($types);

        $response_data = array(
            'types' => array_values($types),
            'count' => count($types),
            'last_updated' => current_time('c'),
        );

        // Cache for 30 minutes
        set_transient($cache_key, $response_data, 30 * MINUTE_IN_SECONDS);

        $response = new WP_REST_Response($response_data, 200);
        $response->header('X-ChatGABI-Cache', 'MISS');

        return $response;

    } catch (Exception $e) {
        return new WP_Error(
            'chatgabi_api_error',
            'Failed to fetch opportunity types: ' . $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * Get available sectors (optionally filtered by country)
 */
function chatgabi_api_get_opportunity_sectors($request) {
    $country = $request->get_param('country');
    $cache_key = 'chatgabi_api_sectors_' . ($country ?: 'all');
    $cached_sectors = get_transient($cache_key);

    if ($cached_sectors !== false) {
        $response = new WP_REST_Response($cached_sectors, 200);
        $response->header('X-ChatGABI-Cache', 'HIT');
        return $response;
    }

    try {
        $sectors = array();
        $countries = $country ? array($country) : get_available_opportunity_countries();

        foreach ($countries as $country_name) {
            $opportunities = load_opportunities_by_country_sector($country_name, '');
            foreach ($opportunities as $opp) {
                if (isset($opp['sector'])) {
                    $sectors[] = ucwords($opp['sector']);
                }
            }
        }

        $sectors = array_unique($sectors);
        sort($sectors);

        $response_data = array(
            'sectors' => array_values($sectors),
            'count' => count($sectors),
            'filtered_by_country' => $country,
            'last_updated' => current_time('c'),
        );

        // Cache for 30 minutes
        set_transient($cache_key, $response_data, 30 * MINUTE_IN_SECONDS);

        $response = new WP_REST_Response($response_data, 200);
        $response->header('X-ChatGABI-Cache', 'MISS');

        return $response;

    } catch (Exception $e) {
        return new WP_Error(
            'chatgabi_api_error',
            'Failed to fetch sectors: ' . $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * Get available countries
 */
function chatgabi_api_get_opportunity_countries($request) {
    $cache_key = 'chatgabi_api_countries';
    $cached_countries = get_transient($cache_key);

    if ($cached_countries !== false) {
        $response = new WP_REST_Response($cached_countries, 200);
        $response->header('X-ChatGABI-Cache', 'HIT');
        return $response;
    }

    try {
        $countries = get_available_opportunity_countries();
        $country_data = array();

        foreach ($countries as $country) {
            $opportunities = load_opportunities_by_country_sector($country, '');
            $country_data[] = array(
                'name' => $country,
                'opportunity_count' => count($opportunities),
                'slug' => strtolower(str_replace(' ', '-', $country)),
            );
        }

        $response_data = array(
            'countries' => $country_data,
            'count' => count($country_data),
            'last_updated' => current_time('c'),
        );

        // Cache for 1 hour
        set_transient($cache_key, $response_data, HOUR_IN_SECONDS);

        $response = new WP_REST_Response($response_data, 200);
        $response->header('X-ChatGABI-Cache', 'MISS');

        return $response;

    } catch (Exception $e) {
        return new WP_Error(
            'chatgabi_api_error',
            'Failed to fetch countries: ' . $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * Get single opportunity by ID
 */
function chatgabi_api_get_single_opportunity($request) {
    $opportunity_id = $request->get_param('id');
    $cache_key = 'chatgabi_api_opp_' . $opportunity_id;
    $cached_opportunity = get_transient($cache_key);

    if ($cached_opportunity !== false) {
        $response = new WP_REST_Response($cached_opportunity, 200);
        $response->header('X-ChatGABI-Cache', 'HIT');
        return $response;
    }

    try {
        $countries = get_available_opportunity_countries();
        $found_opportunity = null;

        foreach ($countries as $country) {
            $opportunities = load_opportunities_by_country_sector($country, '');
            foreach ($opportunities as $opp) {
                if (isset($opp['id']) && $opp['id'] === $opportunity_id) {
                    $found_opportunity = $opp;
                    $found_opportunity['country'] = $country;
                    break 2;
                }
            }
        }

        if (!$found_opportunity) {
            return new WP_Error(
                'chatgabi_opportunity_not_found',
                'Opportunity not found',
                array('status' => 404)
            );
        }

        $response_data = array(
            'opportunity' => $found_opportunity,
            'last_updated' => current_time('c'),
        );

        // Cache for 15 minutes
        set_transient($cache_key, $response_data, 15 * MINUTE_IN_SECONDS);

        $response = new WP_REST_Response($response_data, 200);
        $response->header('X-ChatGABI-Cache', 'MISS');

        return $response;

    } catch (Exception $e) {
        return new WP_Error(
            'chatgabi_api_error',
            'Failed to fetch opportunity: ' . $e->getMessage(),
            array('status' => 500)
        );
    }
}

/**
 * API health check endpoint - XAMPP compatible version
 */
function chatgabi_api_health_check($request) {
    $health_data = array(
        'status' => 'healthy',
        'version' => '1.0.0',
        'timestamp' => current_time('c'),
        'environment' => array(
            'php_version' => PHP_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        ),
        'endpoints' => array(
            'opportunities' => '/chatgabi/v1/opportunities',
            'stats' => '/chatgabi/v1/opportunities/stats',
            'types' => '/chatgabi/v1/opportunities/types',
            'sectors' => '/chatgabi/v1/opportunities/sectors',
            'countries' => '/chatgabi/v1/opportunities/countries',
            'single' => '/chatgabi/v1/opportunities/{id}',
        ),
        'cache_status' => array(
            'enabled' => true,
            'default_ttl' => '5-30 minutes',
        ),
        'data_sources' => array(
            'countries' => array(),
            'total_opportunities' => 0,
            'directory_status' => 'unknown',
            'files_status' => array(),
        ),
    );

    // Check data directory and files
    $opportunities_dir = WP_CONTENT_DIR . '/datasets/opportunities/';

    if (is_dir($opportunities_dir)) {
        $health_data['data_sources']['directory_status'] = 'exists';

        // Get available countries safely
        try {
            if (function_exists('get_available_opportunity_countries')) {
                $countries = get_available_opportunity_countries();
                $health_data['data_sources']['countries'] = $countries;

                // Test each country file without loading all data
                foreach ($countries as $country) {
                    $country_file = strtolower(str_replace(' ', '_', trim($country)));
                    $file_path = $opportunities_dir . $country_file . '.json';

                    if (file_exists($file_path) && is_readable($file_path)) {
                        $file_size = filesize($file_path);
                        $health_data['data_sources']['files_status'][$country] = array(
                            'exists' => true,
                            'readable' => true,
                            'size_bytes' => $file_size,
                        );

                        // Quick opportunity count without full loading
                        try {
                            $content = file_get_contents($file_path);
                            $json_data = json_decode($content, true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($json_data)) {
                                $count = count($json_data);
                                $health_data['data_sources']['files_status'][$country]['opportunity_count'] = $count;
                                $health_data['data_sources']['total_opportunities'] += $count;
                            } else {
                                $health_data['data_sources']['files_status'][$country]['json_error'] = json_last_error_msg();
                            }
                        } catch (Exception $e) {
                            $health_data['data_sources']['files_status'][$country]['error'] = $e->getMessage();
                        }
                    } else {
                        $health_data['data_sources']['files_status'][$country] = array(
                            'exists' => file_exists($file_path),
                            'readable' => false,
                        );
                    }
                }
            } else {
                $health_data['status'] = 'degraded';
                $health_data['error'] = 'get_available_opportunity_countries function not found';
            }
        } catch (Exception $e) {
            $health_data['status'] = 'degraded';
            $health_data['error'] = 'Error accessing countries: ' . $e->getMessage();
        }
    } else {
        $health_data['status'] = 'degraded';
        $health_data['data_sources']['directory_status'] = 'missing';
        $health_data['error'] = 'Opportunities directory not found: ' . $opportunities_dir;
    }

    return new WP_REST_Response($health_data, 200);
}

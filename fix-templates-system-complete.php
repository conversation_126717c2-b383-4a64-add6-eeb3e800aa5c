<?php
/**
 * Complete ChatGABI Templates System Fix
 * 
 * This script comprehensively fixes all template system issues:
 * - Database table creation and initialization
 * - WordPress page creation and routing
 * - Template data population
 * - Admin interface enhancement
 * - REST API registration
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates System Complete Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 5px; border-radius: 3px; font-family: monospace; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .fix-button:hover { background: #005a87; }
    </style>
</head>
<body>

<h1>🔧 ChatGABI Templates System Complete Fix</h1>

<?php
echo '<div class="info">Fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$fixes_applied = array();
$errors_encountered = array();

// Step 1: Initialize Database Tables
echo '<h2>📊 Step 1: Database Tables Initialization</h2>';

try {
    // Initialize prompt templates tables
    if (function_exists('chatgabi_create_prompt_templates_tables')) {
        echo '<div class="info">🔧 Creating prompt templates tables...</div>';
        $result = chatgabi_create_prompt_templates_tables();
        if ($result) {
            echo '<div class="success">✅ Prompt templates tables created successfully</div>';
            $fixes_applied[] = 'Created prompt templates tables';
        } else {
            echo '<div class="warning">⚠️ Prompt templates tables may already exist</div>';
        }
    }
    
    // Initialize template categories
    if (function_exists('chatgabi_init_template_categories')) {
        echo '<div class="info">🔧 Initializing template categories...</div>';
        chatgabi_init_template_categories();
        echo '<div class="success">✅ Template categories initialized</div>';
        $fixes_applied[] = 'Initialized template categories';
    }
    
    // Create default categories if needed
    if (function_exists('chatgabi_create_default_template_categories')) {
        $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
        $category_count = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");
        
        if ($category_count == 0) {
            echo '<div class="info">🔧 Creating default template categories...</div>';
            chatgabi_create_default_template_categories();
            $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");
            echo '<div class="success">✅ Created ' . $new_count . ' default categories</div>';
            $fixes_applied[] = 'Created default template categories';
        } else {
            echo '<div class="success">✅ Template categories already exist (' . $category_count . ' categories)</div>';
        }
    }
    
    // Create default templates if needed
    if (function_exists('chatgabi_initialize_default_templates')) {
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE user_id = 0");
        
        if ($template_count == 0) {
            echo '<div class="info">🔧 Creating default templates...</div>';
            chatgabi_initialize_default_templates();
            $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE user_id = 0");
            echo '<div class="success">✅ Created ' . $new_count . ' default templates</div>';
            $fixes_applied[] = 'Created default templates';
        } else {
            echo '<div class="success">✅ Default templates already exist (' . $template_count . ' templates)</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Database initialization error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Database initialization: ' . $e->getMessage();
}

// Step 2: Create WordPress Pages
echo '<h2>📄 Step 2: WordPress Pages Creation</h2>';

try {
    // Create Templates page
    $templates_page = get_page_by_path('templates');
    if (!$templates_page) {
        echo '<div class="info">🔧 Creating templates page...</div>';
        $page_id = wp_insert_post(array(
            'post_title' => 'ChatGABI Templates',
            'post_content' => 'AI-powered business templates for African entrepreneurs.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'templates'
        ));
        
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-templates.php');
            echo '<div class="success">✅ Templates page created (ID: ' . $page_id . ')</div>';
            echo '<div class="info">URL: <a href="' . get_permalink($page_id) . '" target="_blank">' . get_permalink($page_id) . '</a></div>';
            $fixes_applied[] = 'Created templates page';
        } else {
            echo '<div class="error">❌ Failed to create templates page</div>';
            $errors_encountered[] = 'Failed to create templates page';
        }
    } else {
        echo '<div class="success">✅ Templates page already exists (ID: ' . $templates_page->ID . ')</div>';
        echo '<div class="info">URL: <a href="' . get_permalink($templates_page->ID) . '" target="_blank">' . get_permalink($templates_page->ID) . '</a></div>';
        
        // Ensure correct template is set
        $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
        if ($page_template !== 'page-templates.php') {
            update_post_meta($templates_page->ID, '_wp_page_template', 'page-templates.php');
            echo '<div class="success">✅ Updated page template to page-templates.php</div>';
            $fixes_applied[] = 'Updated page template';
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Page creation error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Page creation: ' . $e->getMessage();
}

// Step 3: Register REST API Routes
echo '<h2>🌐 Step 3: REST API Routes Registration</h2>';

try {
    if (function_exists('chatgabi_register_template_rest_routes')) {
        echo '<div class="info">🔧 Registering REST API routes...</div>';
        chatgabi_register_template_rest_routes();
        echo '<div class="success">✅ REST API routes registered</div>';
        $fixes_applied[] = 'Registered REST API routes';
    } else {
        echo '<div class="warning">⚠️ REST API registration function not found</div>';
    }
    
    // Test REST API endpoints
    $rest_routes = rest_get_server()->get_routes();
    $chatgabi_routes = array();
    
    foreach ($rest_routes as $route => $handlers) {
        if (strpos($route, '/chatgabi/v1/') === 0) {
            $chatgabi_routes[] = $route;
        }
    }
    
    if (!empty($chatgabi_routes)) {
        echo '<div class="success">✅ Found ' . count($chatgabi_routes) . ' ChatGABI REST routes</div>';
        echo '<ul>';
        foreach ($chatgabi_routes as $route) {
            echo '<li><code>' . $route . '</code></li>';
        }
        echo '</ul>';
    } else {
        echo '<div class="warning">⚠️ No ChatGABI REST API routes found</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ REST API registration error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'REST API registration: ' . $e->getMessage();
}

// Step 4: Enhance Admin Page
echo '<h2>⚙️ Step 4: Admin Interface Enhancement</h2>';

try {
    // Check if admin functions exist
    if (function_exists('chatgabi_templates_admin_page')) {
        echo '<div class="success">✅ Admin page function exists</div>';
    } else {
        echo '<div class="warning">⚠️ Admin page function not found</div>';
    }
    
    // Check admin menu registration
    if (function_exists('chatgabi_add_templates_admin_menu')) {
        echo '<div class="success">✅ Admin menu function exists</div>';
    } else {
        echo '<div class="warning">⚠️ Admin menu function not found</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Admin interface error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Admin interface: ' . $e->getMessage();
}

// Step 5: Verify Template Data
echo '<h2>📝 Step 5: Template Data Verification</h2>';

try {
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    
    // Check categories
    $categories = $wpdb->get_results("SELECT * FROM $categories_table ORDER BY sort_order ASC");
    echo '<div class="success">✅ Found ' . count($categories) . ' template categories</div>';
    
    // Check templates
    $templates = $wpdb->get_results("SELECT * FROM $templates_table");
    echo '<div class="success">✅ Found ' . count($templates) . ' templates total</div>';
    
    $public_templates = $wpdb->get_results("SELECT * FROM $templates_table WHERE is_public = 1");
    echo '<div class="success">✅ Found ' . count($public_templates) . ' public templates</div>';
    
    // Test template retrieval function
    if (function_exists('chatgabi_get_template_categories')) {
        $retrieved_categories = chatgabi_get_template_categories();
        if (!empty($retrieved_categories)) {
            echo '<div class="success">✅ Template categories retrieval function working</div>';
        } else {
            echo '<div class="warning">⚠️ Template categories retrieval function returned empty</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Template data verification error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Template data verification: ' . $e->getMessage();
}

// Step 6: Test Template Page Access
echo '<h2>🌐 Step 6: Template Page Access Test</h2>';

try {
    $templates_page = get_page_by_path('templates');
    if ($templates_page) {
        $page_url = get_permalink($templates_page->ID);
        echo '<div class="success">✅ Templates page URL: <a href="' . $page_url . '" target="_blank">' . $page_url . '</a></div>';
        
        // Check if page template file exists
        $theme_dir = get_template_directory();
        $template_file = $theme_dir . '/page-templates.php';
        
        if (file_exists($template_file)) {
            echo '<div class="success">✅ Page template file exists: page-templates.php</div>';
        } else {
            echo '<div class="error">❌ Page template file missing: page-templates.php</div>';
            $errors_encountered[] = 'Page template file missing';
        }
        
        // Check CSS and JS files
        $css_file = $theme_dir . '/assets/css/templates.css';
        $js_file = $theme_dir . '/assets/js/templates-interface.js';
        
        if (file_exists($css_file)) {
            echo '<div class="success">✅ Templates CSS file exists</div>';
        } else {
            echo '<div class="warning">⚠️ Templates CSS file missing</div>';
        }
        
        if (file_exists($js_file)) {
            echo '<div class="success">✅ Templates JavaScript file exists</div>';
        } else {
            echo '<div class="warning">⚠️ Templates JavaScript file missing</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Template page access test error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Template page access test: ' . $e->getMessage();
}

// Summary
echo '<h2>📋 Fix Summary</h2>';

if (empty($errors_encountered)) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL FIXES COMPLETED SUCCESSFULLY!</h3>';
    echo '<p><strong>✅ ChatGABI Templates system is now fully operational!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues Remain</h3>';
    echo '<ul>';
    foreach ($errors_encountered as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Fixes Applied: ' . count($fixes_applied) . '</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Action Buttons
echo '<h2>🚀 Test Your Templates System</h2>';

echo '<div style="margin: 20px 0;">';

$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<button class="fix-button" onclick="window.open(\'' . get_permalink($templates_page->ID) . '\', \'_blank\')">🎯 Test Templates Page</button>';
}

echo '<button class="fix-button" onclick="window.open(\'' . admin_url('admin.php?page=chatgabi-templates') . '\', \'_blank\')">⚙️ Admin Templates</button>';
echo '<button class="fix-button" onclick="window.location.href=\'diagnose-templates-system.php\'">🔍 Run Diagnosis</button>';
echo '<button class="fix-button" onclick="window.location.reload()">🔄 Re-run Fix</button>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Fix completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

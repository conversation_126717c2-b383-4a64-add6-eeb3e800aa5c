<?php
/**
 * Debug Template Issues - Comprehensive Diagnostic
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check admin privileges
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions');
}

echo '<h1>🔍 Template System Debug Report</h1>';
echo '<style>
.debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
.success { color: #28a745; }
.error { color: #dc3545; }
.warning { color: #ffc107; }
.info { color: #007bff; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
th { background: #f8f9fa; }
</style>';

// Test 1: Database Tables
echo '<div class="debug-section">';
echo '<h2>1. Database Tables Status</h2>';

global $wpdb;
$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$usage_table = $wpdb->prefix . 'chatgabi_template_usage';

$tables = array(
    'Templates' => $templates_table,
    'Categories' => $categories_table,
    'Usage' => $usage_table
);

foreach ($tables as $name => $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table}");
        echo "<p class='success'>✅ {$name} table exists with {$count} records</p>";
        
        // Show table structure
        $columns = $wpdb->get_results("DESCRIBE {$table}");
        echo "<details><summary>Table structure for {$table}</summary>";
        echo "<table><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr><td>{$col->Field}</td><td>{$col->Type}</td><td>{$col->Null}</td><td>{$col->Key}</td><td>{$col->Default}</td></tr>";
        }
        echo "</table></details>";
    } else {
        echo "<p class='error'>❌ {$name} table missing: {$table}</p>";
    }
}
echo '</div>';

// Test 2: Default Templates Data
echo '<div class="debug-section">';
echo '<h2>2. Default Templates Analysis</h2>';

if ($wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") === $templates_table) {
    $default_templates = $wpdb->get_results("
        SELECT t.*, c.name as category_name 
        FROM {$templates_table} t 
        LEFT JOIN {$categories_table} c ON t.category_id = c.id 
        WHERE t.user_id = 0 
        ORDER BY t.id
    ");
    
    if (!empty($default_templates)) {
        echo "<p class='success'>✅ Found " . count($default_templates) . " default templates</p>";
        echo "<table><tr><th>ID</th><th>Title</th><th>Category</th><th>Status</th><th>Public</th><th>Featured</th></tr>";
        foreach ($default_templates as $template) {
            $status_class = $template->status === 'active' ? 'success' : 'warning';
            $public_icon = $template->is_public ? '🌐' : '🔒';
            $featured_icon = $template->is_featured ? '⭐' : '';
            echo "<tr>";
            echo "<td>{$template->id}</td>";
            echo "<td>{$template->title}</td>";
            echo "<td>{$template->category_name}</td>";
            echo "<td class='{$status_class}'>{$template->status}</td>";
            echo "<td>{$public_icon}</td>";
            echo "<td>{$featured_icon}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ No default templates found</p>";
        
        // Try to initialize default templates
        echo "<p class='info'>🔄 Attempting to initialize default templates...</p>";
        if (function_exists('chatgabi_initialize_default_templates')) {
            try {
                chatgabi_initialize_default_templates();
                echo "<p class='success'>✅ Default templates initialization completed</p>";
                
                // Check again
                $new_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0");
                echo "<p class='info'>📊 New default templates count: {$new_count}</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error initializing templates: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ chatgabi_initialize_default_templates function not found</p>";
        }
    }
} else {
    echo "<p class='error'>❌ Templates table does not exist</p>";
}
echo '</div>';

// Test 3: Categories Data
echo '<div class="debug-section">';
echo '<h2>3. Categories Analysis</h2>';

if ($wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table) {
    $categories = $wpdb->get_results("SELECT * FROM {$categories_table} ORDER BY sort_order");
    
    if (!empty($categories)) {
        echo "<p class='success'>✅ Found " . count($categories) . " categories</p>";
        echo "<table><tr><th>ID</th><th>Name</th><th>Slug</th><th>Icon</th><th>Color</th><th>Status</th></tr>";
        foreach ($categories as $category) {
            $status_class = $category->status === 'active' ? 'success' : 'warning';
            echo "<tr>";
            echo "<td>{$category->id}</td>";
            echo "<td>{$category->name}</td>";
            echo "<td>{$category->slug}</td>";
            echo "<td>{$category->icon}</td>";
            echo "<td style='background-color: {$category->color}; color: white;'>{$category->color}</td>";
            echo "<td class='{$status_class}'>{$category->status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ No categories found</p>";
    }
} else {
    echo "<p class='error'>❌ Categories table does not exist</p>";
}
echo '</div>';

// Test 4: REST API Endpoints
echo '<div class="debug-section">';
echo '<h2>4. REST API Endpoints Test</h2>';

$endpoints = array(
    'Templates List' => rest_url('chatgabi/v1/templates'),
    'Template Categories' => rest_url('chatgabi/v1/template-categories'),
    'Single Template' => rest_url('chatgabi/v1/templates/1')
);

foreach ($endpoints as $name => $url) {
    echo "<p class='info'><strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
}

// Test REST API functionality
echo "<h3>REST API Function Tests</h3>";

$rest_functions = array(
    'chatgabi_rest_get_templates',
    'chatgabi_rest_get_template',
    'chatgabi_rest_get_categories',
    'chatgabi_rest_check_auth'
);

foreach ($rest_functions as $func) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ {$func}() exists</p>";
    } else {
        echo "<p class='error'>❌ {$func}() missing</p>";
    }
}
echo '</div>';

// Test 5: Template Functions
echo '<div class="debug-section">';
echo '<h2>5. Template Functions Test</h2>';

$template_functions = array(
    'chatgabi_get_public_templates',
    'chatgabi_get_user_templates',
    'chatgabi_save_prompt_template',
    'chatgabi_get_template_categories'
);

foreach ($template_functions as $func) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ {$func}() exists</p>";
        
        // Test function calls
        try {
            switch ($func) {
                case 'chatgabi_get_public_templates':
                    $result = $func();
                    echo "<p class='info'>   📊 Returns " . count($result) . " public templates</p>";
                    break;
                case 'chatgabi_get_user_templates':
                    $result = $func(1); // Test with user ID 1
                    echo "<p class='info'>   📊 Returns " . count($result) . " user templates</p>";
                    break;
                case 'chatgabi_get_template_categories':
                    $result = $func();
                    echo "<p class='info'>   📊 Returns " . count($result) . " categories</p>";
                    break;
            }
        } catch (Exception $e) {
            echo "<p class='error'>   ❌ Error calling {$func}: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>❌ {$func}() missing</p>";
    }
}
echo '</div>';

// Test 6: JavaScript Integration
echo '<div class="debug-section">';
echo '<h2>6. JavaScript Integration</h2>';

$js_files = array(
    'Chat Block JS' => get_template_directory() . '/assets/js/chat-block.js',
    'Templates JS' => get_template_directory() . '/assets/js/prompt-templates.js',
    'Templates CSS' => get_template_directory() . '/assets/css/prompt-templates.css'
);

foreach ($js_files as $name => $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p class='success'>✅ {$name} exists ({$size} bytes)</p>";
    } else {
        echo "<p class='error'>❌ {$name} missing: " . basename($file) . "</p>";
    }
}
echo '</div>';

// Test 7: Admin Page Test
echo '<div class="debug-section">';
echo '<h2>7. Admin Page Integration</h2>';

if (function_exists('chatgabi_template_admin_page')) {
    echo "<p class='success'>✅ Admin page function exists</p>";
    echo "<p class='info'><a href='" . admin_url('admin.php?page=chatgabi-prompt-templates') . "' target='_blank'>→ Open Admin Page</a></p>";
} else {
    echo "<p class='error'>❌ Admin page function missing</p>";
}

// Check if admin menu is registered
global $submenu;
$admin_menu_found = false;
if (isset($submenu['chatgabi-main'])) {
    foreach ($submenu['chatgabi-main'] as $item) {
        if (isset($item[2]) && $item[2] === 'chatgabi-prompt-templates') {
            $admin_menu_found = true;
            echo "<p class='success'>✅ Admin menu registered: {$item[1]}</p>";
            break;
        }
    }
}

if (!$admin_menu_found) {
    echo "<p class='error'>❌ Admin menu not registered</p>";
}
echo '</div>';

// Test 8: PHP Errors Check
echo '<div class="debug-section">';
echo '<h2>8. PHP Errors & Warnings</h2>';

// Check for common PHP issues
$php_version = phpversion();
echo "<p class='info'>PHP Version: {$php_version}</p>";

if (version_compare($php_version, '7.4', '<')) {
    echo "<p class='warning'>⚠️ PHP version is below 7.4, some features may not work correctly</p>";
} else {
    echo "<p class='success'>✅ PHP version is compatible</p>";
}

// Check error reporting
$error_reporting = error_reporting();
echo "<p class='info'>Error Reporting Level: {$error_reporting}</p>";

// Check for deprecated function usage
$deprecated_functions = array('strpos', 'str_replace');
echo "<p class='info'>Checking for deprecated function usage...</p>";

foreach ($deprecated_functions as $func) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ {$func}() available</p>";
    }
}
echo '</div>';

// Test 9: Quick Fix Actions
echo '<div class="debug-section">';
echo '<h2>9. Quick Fix Actions</h2>';

echo '<p><strong>Available Actions:</strong></p>';
echo '<ul>';
echo '<li><a href="?action=recreate_tables">🔧 Recreate Database Tables</a></li>';
echo '<li><a href="?action=reinit_templates">🔄 Reinitialize Default Templates</a></li>';
echo '<li><a href="?action=clear_cache">🗑️ Clear Template Cache</a></li>';
echo '<li><a href="?action=test_rest_api">🌐 Test REST API</a></li>';
echo '</ul>';

// Handle actions
if (isset($_GET['action'])) {
    $action = sanitize_text_field($_GET['action']);
    
    switch ($action) {
        case 'recreate_tables':
            echo "<p class='info'>🔧 Recreating database tables...</p>";
            if (function_exists('chatgabi_create_prompt_templates_tables')) {
                try {
                    chatgabi_create_prompt_templates_tables();
                    echo "<p class='success'>✅ Tables recreated successfully</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
                }
            }
            break;
            
        case 'reinit_templates':
            echo "<p class='info'>🔄 Reinitializing default templates...</p>";
            if (function_exists('chatgabi_initialize_default_templates')) {
                try {
                    // Clear existing default templates first
                    $wpdb->query("DELETE FROM {$templates_table} WHERE user_id = 0");
                    chatgabi_initialize_default_templates();
                    echo "<p class='success'>✅ Default templates reinitialized</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
                }
            }
            break;
            
        case 'clear_cache':
            echo "<p class='info'>🗑️ Clearing template cache...</p>";
            wp_cache_flush();
            echo "<p class='success'>✅ Cache cleared</p>";
            break;
            
        case 'test_rest_api':
            echo "<p class='info'>🌐 Testing REST API...</p>";
            $test_url = rest_url('chatgabi/v1/templates?public=true&limit=5');
            echo "<p class='info'>Test URL: <a href='{$test_url}' target='_blank'>{$test_url}</a></p>";
            break;
    }
}
echo '</div>';

echo '<hr>';
echo '<p><em>Debug completed at ' . current_time('mysql') . '</em></p>';
echo '<p><a href="' . admin_url('admin.php?page=chatgabi-prompt-templates') . '">← Return to Templates Admin</a></p>';
?>

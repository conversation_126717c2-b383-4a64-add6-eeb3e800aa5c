<?php
/**
 * Template Name: User Preferences Dashboard
 *
 * @package BusinessCraft_AI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

$user_id = get_current_user_id();
$user = get_userdata($user_id);

// Get current preferences
$current_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
$current_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
$current_country = get_user_meta($user_id, 'bcai_country', true) ?: '';
$show_chat_history = get_user_meta($user_id, 'bcai_show_chat_history', true) !== '0';
$profile_type = get_user_meta($user_id, 'bcai_profile_type', true);
$business_stage = get_user_meta($user_id, 'bcai_business_stage', true);
$goals = get_user_meta($user_id, 'bcai_goals', true) ?: array();

// Handle form submission
if ($_POST && wp_verify_nonce($_POST['preferences_nonce'], 'bcai_preferences')) {
    $language = sanitize_text_field($_POST['language']);
    $industry = sanitize_text_field($_POST['industry']);
    $country = sanitize_text_field($_POST['country']);
    $show_history = isset($_POST['show_chat_history']) ? '1' : '0';

    // Update preferences
    update_user_meta($user_id, 'bcai_preferred_language', $language);
    update_user_meta($user_id, 'businesscraft_ai_industry', $industry);
    update_user_meta($user_id, 'bcai_country', $country);
    update_user_meta($user_id, 'bcai_show_chat_history', $show_history);

    // Log analytics
    businesscraft_ai_log_analytics($user_id, 'preferences_updated', array(
        'language' => $language,
        'industry' => $industry,
        'country' => $country,
        'show_history' => $show_history
    ));

    $success_message = __('Preferences updated successfully!', 'businesscraft-ai');
}

get_header();
?>

<div class="preferences-container">
    <div class="preferences-wrapper">
        <div class="preferences-header">
            <h1><?php _e('User Preferences', 'businesscraft-ai'); ?></h1>
            <p><?php _e('Customize your BusinessCraft AI experience', 'businesscraft-ai'); ?></p>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="success-notice">
                <?php echo esc_html($success_message); ?>
            </div>
        <?php endif; ?>

        <div class="preferences-content">
            <!-- Profile Overview -->
            <div class="profile-overview">
                <h2><?php _e('Profile Overview', 'businesscraft-ai'); ?></h2>
                <div class="profile-info">
                    <div class="info-item">
                        <label><?php _e('Profile Type:', 'businesscraft-ai'); ?></label>
                        <span class="profile-badge <?php echo esc_attr($profile_type); ?>">
                            <?php
                            if ($profile_type === 'sme') {
                                echo '🏢 ' . __('Small Business Owner', 'businesscraft-ai');
                            } elseif ($profile_type === 'creator') {
                                echo '🎨 ' . __('Content Creator', 'businesscraft-ai');
                            } else {
                                echo __('Not Set', 'businesscraft-ai');
                            }
                            ?>
                        </span>
                    </div>

                    <?php if ($business_stage): ?>
                    <div class="info-item">
                        <label><?php _e('Business Stage:', 'businesscraft-ai'); ?></label>
                        <span><?php echo esc_html(ucfirst($business_stage)); ?></span>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($goals)): ?>
                    <div class="info-item">
                        <label><?php _e('Goals:', 'businesscraft-ai'); ?></label>
                        <div class="goals-list">
                            <?php foreach ($goals as $goal): ?>
                                <span class="goal-tag"><?php echo esc_html(str_replace('_', ' ', ucfirst($goal))); ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="info-item">
                        <a href="<?php echo home_url('/onboarding/'); ?>" class="btn-secondary">
                            <?php _e('Update Profile', 'businesscraft-ai'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Preferences Form -->
            <form method="post" class="preferences-form">
                <?php wp_nonce_field('bcai_preferences', 'preferences_nonce'); ?>

                <div class="preferences-section">
                    <h2><?php _e('Language Preferences', 'businesscraft-ai'); ?></h2>
                    <div class="form-group">
                        <label for="language"><?php _e('Preferred Language:', 'businesscraft-ai'); ?></label>
                        <select name="language" id="language" class="form-control">
                            <option value="en" <?php selected($current_language, 'en'); ?>><?php _e('English', 'businesscraft-ai'); ?></option>
                            <option value="tw" <?php selected($current_language, 'tw'); ?>><?php _e('Twi (Ghana)', 'businesscraft-ai'); ?></option>
                            <option value="sw" <?php selected($current_language, 'sw'); ?>><?php _e('Swahili (Kenya)', 'businesscraft-ai'); ?></option>
                            <option value="yo" <?php selected($current_language, 'yo'); ?>><?php _e('Yoruba (Nigeria)', 'businesscraft-ai'); ?></option>
                            <option value="zu" <?php selected($current_language, 'zu'); ?>><?php _e('Zulu (South Africa)', 'businesscraft-ai'); ?></option>
                        </select>
                        <small class="form-help"><?php _e('This will be your default language for AI responses', 'businesscraft-ai'); ?></small>
                    </div>
                </div>

                <div class="preferences-section">
                    <h2><?php _e('Business Preferences', 'businesscraft-ai'); ?></h2>

                    <div class="form-group">
                        <label for="country"><?php _e('Country:', 'businesscraft-ai'); ?></label>
                        <select name="country" id="country" class="form-control">
                            <option value=""><?php _e('Select your country...', 'businesscraft-ai'); ?></option>
                            <option value="Ghana" <?php selected($current_country, 'Ghana'); ?>><?php _e('Ghana', 'businesscraft-ai'); ?></option>
                            <option value="Kenya" <?php selected($current_country, 'Kenya'); ?>><?php _e('Kenya', 'businesscraft-ai'); ?></option>
                            <option value="Nigeria" <?php selected($current_country, 'Nigeria'); ?>><?php _e('Nigeria', 'businesscraft-ai'); ?></option>
                            <option value="South Africa" <?php selected($current_country, 'South Africa'); ?>><?php _e('South Africa', 'businesscraft-ai'); ?></option>
                        </select>
                        <small class="form-help"><?php _e('This helps provide relevant opportunities and market insights', 'businesscraft-ai'); ?></small>
                    </div>

                    <div class="form-group">
                        <label for="industry"><?php _e('Primary Industry:', 'businesscraft-ai'); ?></label>
                        <select name="industry" id="industry" class="form-control">
                            <option value=""><?php _e('Select your industry...', 'businesscraft-ai'); ?></option>
                            <option value="agriculture" <?php selected($current_industry, 'agriculture'); ?>><?php _e('Agriculture & Farming', 'businesscraft-ai'); ?></option>
                            <option value="technology" <?php selected($current_industry, 'technology'); ?>><?php _e('Technology & Software', 'businesscraft-ai'); ?></option>
                            <option value="retail" <?php selected($current_industry, 'retail'); ?>><?php _e('Retail & E-commerce', 'businesscraft-ai'); ?></option>
                            <option value="hospitality" <?php selected($current_industry, 'hospitality'); ?>><?php _e('Hospitality & Tourism', 'businesscraft-ai'); ?></option>
                            <option value="healthcare" <?php selected($current_industry, 'healthcare'); ?>><?php _e('Healthcare & Wellness', 'businesscraft-ai'); ?></option>
                            <option value="education" <?php selected($current_industry, 'education'); ?>><?php _e('Education & Training', 'businesscraft-ai'); ?></option>
                            <option value="finance" <?php selected($current_industry, 'finance'); ?>><?php _e('Finance & Banking', 'businesscraft-ai'); ?></option>
                            <option value="manufacturing" <?php selected($current_industry, 'manufacturing'); ?>><?php _e('Manufacturing', 'businesscraft-ai'); ?></option>
                            <option value="construction" <?php selected($current_industry, 'construction'); ?>><?php _e('Construction & Real Estate', 'businesscraft-ai'); ?></option>
                            <option value="transportation" <?php selected($current_industry, 'transportation'); ?>><?php _e('Transportation & Logistics', 'businesscraft-ai'); ?></option>
                            <option value="media" <?php selected($current_industry, 'media'); ?>><?php _e('Media & Entertainment', 'businesscraft-ai'); ?></option>
                            <option value="consulting" <?php selected($current_industry, 'consulting'); ?>><?php _e('Consulting & Services', 'businesscraft-ai'); ?></option>
                            <option value="other" <?php selected($current_industry, 'other'); ?>><?php _e('Other', 'businesscraft-ai'); ?></option>
                        </select>
                        <small class="form-help"><?php _e('This helps provide more relevant business advice', 'businesscraft-ai'); ?></small>
                    </div>
                </div>

                <div class="preferences-section">
                    <h2><?php _e('Chat Preferences', 'businesscraft-ai'); ?></h2>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="show_chat_history" value="1" <?php checked($show_chat_history, true); ?>>
                            <span class="checkmark"></span>
                            <?php _e('Show chat history on homepage', 'businesscraft-ai'); ?>
                        </label>
                        <small class="form-help"><?php _e('Display your recent conversations when you visit the chat', 'businesscraft-ai'); ?></small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary"><?php _e('Save Preferences', 'businesscraft-ai'); ?></button>
                    <a href="<?php echo home_url(); ?>" class="btn-secondary"><?php _e('Back to Home', 'businesscraft-ai'); ?></a>
                </div>
            </form>

            <!-- Account Statistics -->
            <div class="account-stats">
                <h2><?php _e('Account Statistics', 'businesscraft-ai'); ?></h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo esc_html(get_user_meta($user_id, 'businesscraft_credits', true) ?: 0); ?></div>
                        <div class="stat-label"><?php _e('Credits Remaining', 'businesscraft-ai'); ?></div>
                    </div>

                    <?php
                    global $wpdb;
                    $chat_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}businesscraft_ai_chat_logs WHERE user_id = %d",
                        $user_id
                    ));
                    ?>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo esc_html($chat_count ?: 0); ?></div>
                        <div class="stat-label"><?php _e('Total Conversations', 'businesscraft-ai'); ?></div>
                    </div>

                    <?php
                    $templates_count = count(get_user_meta($user_id, 'businesscraft_ai_prompt_templates', true) ?: array());
                    ?>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo esc_html($templates_count); ?></div>
                        <div class="stat-label"><?php _e('Saved Templates', 'businesscraft-ai'); ?></div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-value"><?php echo esc_html(date('M j, Y', strtotime($user->user_registered))); ?></div>
                        <div class="stat-label"><?php _e('Member Since', 'businesscraft-ai'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preferences-container {
    max-width: 900px;
    margin: 40px auto;
    padding: 0 20px;
}

.preferences-wrapper {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.preferences-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.preferences-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
}

.preferences-header p {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
}

.preferences-content {
    padding: 40px;
}

.success-notice {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 30px;
    font-weight: 500;
}

.profile-overview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 40px;
}

.profile-overview h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.profile-info {
    display: grid;
    gap: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.info-item label {
    font-weight: 600;
    color: #555;
    min-width: 120px;
}

.profile-badge {
    background: #667eea;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.profile-badge.creator {
    background: #764ba2;
}

.goals-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.goal-tag {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.preferences-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.preferences-section:last-child {
    border-bottom: none;
}

.preferences-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
}

.form-help {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 14px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 600;
    color: #555;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 40px;
}

.btn-primary, .btn-secondary {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.account-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    margin-top: 40px;
}

.account-stats h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .info-item label {
        min-width: auto;
    }
}
</style>

<?php get_footer(); ?>

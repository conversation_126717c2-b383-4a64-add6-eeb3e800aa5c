<?php
/**
 * Template Name: User Dashboard
 * Template Post Type: page
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

$user_id = get_current_user_id();
$user = get_userdata($user_id);

// Get user preferences
$current_country = get_user_meta($user_id, 'bcai_country', true) ?: '';
$current_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
$current_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';

get_header();
?>

<div class="dashboard-container">
    <div class="dashboard-wrapper">
        <div class="dashboard-header">
            <h1><?php printf(__('Welcome back, %s!', 'businesscraft-ai'), esc_html($user->display_name)); ?></h1>
            <p><?php _e('Manage your BusinessCraft AI experience and discover new opportunities', 'businesscraft-ai'); ?></p>
        </div>

        <div class="dashboard-content">
            <div class="tabs-navigation" role="tablist" aria-label="<?php _e('Dashboard sections', 'businesscraft-ai'); ?>">
                <button class="tab-button active" data-tab="overview" role="tab" aria-selected="true" aria-controls="overview" id="tab-overview">
                    <?php _e('Overview', 'businesscraft-ai'); ?>
                </button>
                <button class="tab-button" data-tab="analytics" role="tab" aria-selected="false" aria-controls="analytics" id="tab-analytics">
                    <?php _e('Analytics', 'businesscraft-ai'); ?>
                </button>
                <button class="tab-button" data-tab="opportunities" role="tab" aria-selected="false" aria-controls="opportunities" id="tab-opportunities">
                    <?php _e('Live Opportunities', 'businesscraft-ai'); ?>
                </button>
                <button class="tab-button" data-tab="templates" role="tab" aria-selected="false" aria-controls="templates" id="tab-templates">
                    <?php _e('Templates', 'businesscraft-ai'); ?>
                </button>
                <button class="tab-button" data-tab="notifications" role="tab" aria-selected="false" aria-controls="notifications" id="tab-notifications">
                    <?php _e('Notifications', 'businesscraft-ai'); ?>
                </button>
                <button class="tab-button" data-tab="preferences" role="tab" aria-selected="false" aria-controls="preferences" id="tab-preferences">
                    <?php _e('Preferences', 'businesscraft-ai'); ?>
                </button>
                <button class="tab-button" data-tab="feedback" role="tab" aria-selected="false" aria-controls="feedback" id="tab-feedback">
                    <?php _e('Feedback', 'businesscraft-ai'); ?>
                </button>
            </div>

            <div class="tabs-content">
                <!-- Overview Tab -->
                <div id="overview" class="tab-pane active" role="tabpanel" aria-labelledby="tab-overview">
                    <!-- Credit Feedback Widget -->
                    <?php get_template_part('template-parts/credit-feedback-widget'); ?>

                    <!-- Business Templates Quick Access -->
                    <div class="dashboard-section templates-quick-access">
                        <div class="section-header">
                            <h2><?php _e('🚀 AI-Powered Business Templates', 'businesscraft-ai'); ?></h2>
                            <p class="section-description">
                                <?php _e('Generate professional business documents with AI assistance tailored for African markets', 'businesscraft-ai'); ?>
                            </p>
                        </div>

                        <div class="templates-showcase">
                            <div class="template-quick-card business-plan">
                                <div class="template-icon">📋</div>
                                <h3><?php _e('Business Plans', 'businesscraft-ai'); ?></h3>
                                <p><?php _e('AI-generated business plans with African market insights', 'businesscraft-ai'); ?></p>
                                <a href="<?php echo home_url('/templates?category=business-plans'); ?>" class="template-cta-btn">
                                    <?php _e('Create Business Plan', 'businesscraft-ai'); ?>
                                </a>
                            </div>

                            <div class="template-quick-card marketing-strategy">
                                <div class="template-icon">📈</div>
                                <h3><?php _e('Marketing Strategies', 'businesscraft-ai'); ?></h3>
                                <p><?php _e('Market analysis and strategies for African businesses', 'businesscraft-ai'); ?></p>
                                <a href="<?php echo home_url('/templates?category=marketing-strategies'); ?>" class="template-cta-btn">
                                    <?php _e('Build Marketing Strategy', 'businesscraft-ai'); ?>
                                </a>
                            </div>

                            <div class="template-quick-card financial-forecast">
                                <div class="template-icon">💰</div>
                                <h3><?php _e('Financial Forecasts', 'businesscraft-ai'); ?></h3>
                                <p><?php _e('Revenue projections with local market data', 'businesscraft-ai'); ?></p>
                                <a href="<?php echo home_url('/templates?category=financial-forecasts'); ?>" class="template-cta-btn">
                                    <?php _e('Create Financial Forecast', 'businesscraft-ai'); ?>
                                </a>
                            </div>
                        </div>

                        <div class="templates-main-cta">
                            <a href="<?php echo home_url('/templates'); ?>" class="btn-primary btn-large">
                                <?php _e('🎯 Explore All Business Templates', 'businesscraft-ai'); ?>
                            </a>
                            <p class="cta-description">
                                <?php _e('Access 50+ professional templates designed for African entrepreneurs', 'businesscraft-ai'); ?>
                            </p>
                        </div>
                    </div>

                    <div class="overview-grid">
                        <!-- Account Statistics -->
                        <div class="stats-section">
                            <h2><?php _e('Account Statistics', 'businesscraft-ai'); ?></h2>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo esc_html(get_user_meta($user_id, 'businesscraft_credits', true) ?: 0); ?></div>
                                    <div class="stat-label"><?php _e('Credits Remaining', 'businesscraft-ai'); ?></div>
                                </div>
                                
                                <?php
                                global $wpdb;
                                $chat_count = $wpdb->get_var($wpdb->prepare(
                                    "SELECT COUNT(*) FROM {$wpdb->prefix}businesscraft_ai_chat_logs WHERE user_id = %d",
                                    $user_id
                                ));
                                ?>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo esc_html($chat_count ?: 0); ?></div>
                                    <div class="stat-label"><?php _e('Total Conversations', 'businesscraft-ai'); ?></div>
                                </div>
                                
                                <?php
                                $templates_count = count(get_user_meta($user_id, 'businesscraft_ai_prompt_templates', true) ?: array());
                                ?>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo esc_html($templates_count); ?></div>
                                    <div class="stat-label"><?php _e('Saved Templates', 'businesscraft-ai'); ?></div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo esc_html(date('M j, Y', strtotime($user->user_registered))); ?></div>
                                    <div class="stat-label"><?php _e('Member Since', 'businesscraft-ai'); ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <h2><?php _e('Quick Actions', 'businesscraft-ai'); ?></h2>
                            <div class="actions-grid">
                                <a href="<?php echo home_url(); ?>" class="action-card">
                                    <span class="action-icon">💬</span>
                                    <h3><?php _e('Start Chat', 'businesscraft-ai'); ?></h3>
                                    <p><?php _e('Begin a new conversation with BusinessCraft AI', 'businesscraft-ai'); ?></p>
                                </a>

                                <?php
                                // Check if wizards page exists
                                $wizards_page = get_page_by_path('wizards');
                                $wizards_url = $wizards_page ? get_permalink($wizards_page->ID) : home_url('/templates/');
                                ?>
                                <a href="<?php echo esc_url($wizards_url); ?>" class="action-card">
                                    <span class="action-icon">🧙‍♂️</span>
                                    <h3><?php _e('Document Wizards', 'businesscraft-ai'); ?></h3>
                                    <p><?php _e('Create professional documents with step-by-step AI guidance', 'businesscraft-ai'); ?></p>
                                </a>

                                <a href="<?php echo home_url('/preferences/'); ?>" class="action-card">
                                    <span class="action-icon">⚙️</span>
                                    <h3><?php _e('Update Preferences', 'businesscraft-ai'); ?></h3>
                                    <p><?php _e('Customize your AI experience', 'businesscraft-ai'); ?></p>
                                </a>
                                
                                <a href="#" class="action-card" onclick="switchTab('opportunities')">
                                    <span class="action-icon">🎯</span>
                                    <h3><?php _e('View Opportunities', 'businesscraft-ai'); ?></h3>
                                    <p><?php _e('Discover business opportunities in your area', 'businesscraft-ai'); ?></p>
                                </a>
                                
                                <a href="#" class="action-card" onclick="openCreditPurchaseModal()">
                                    <span class="action-icon">💳</span>
                                    <h3><?php _e('Buy Credits', 'businesscraft-ai'); ?></h3>
                                    <p><?php _e('Purchase more credits to continue using AI tools', 'businesscraft-ai'); ?></p>
                                </a>
                            </div>
                        </div>

                        <!-- Export History -->
                        <div class="export-history-section">
                            <h2><?php _e('📄 Recent Document Exports', 'businesscraft-ai'); ?></h2>
                            <div class="export-history" id="export-history">
                                <div class="loading-exports">
                                    <div class="loading-spinner"></div>
                                    <p><?php _e('Loading export history...', 'businesscraft-ai'); ?></p>
                                </div>
                            </div>
                            <div class="export-actions-footer">
                                <a href="<?php echo home_url('/templates'); ?>" class="btn-secondary">
                                    <?php _e('Create New Document', 'businesscraft-ai'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-pane">
                    <div class="analytics-container" data-component="analytics-dashboard">
                        <div class="analytics-header">
                            <h2><?php _e('📊 Your Analytics Dashboard', 'businesscraft-ai'); ?></h2>
                            <p><?php _e('Track your usage patterns, productivity insights, and AI interaction analytics.', 'businesscraft-ai'); ?></p>
                        </div>

                        <!-- Analytics Summary Cards -->
                        <div class="analytics-summary">
                            <div class="analytics-card">
                                <div class="card-icon">💬</div>
                                <div class="card-content">
                                    <div class="card-value" id="total-conversations-analytics">-</div>
                                    <div class="card-label"><?php _e('Total Conversations', 'businesscraft-ai'); ?></div>
                                    <div class="card-trend" id="conversations-trend">-</div>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-icon">⚡</div>
                                <div class="card-content">
                                    <div class="card-value" id="credits-used-analytics">-</div>
                                    <div class="card-label"><?php _e('Credits Used', 'businesscraft-ai'); ?></div>
                                    <div class="card-trend" id="credits-trend">-</div>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-icon">📝</div>
                                <div class="card-content">
                                    <div class="card-value" id="templates-generated-analytics">-</div>
                                    <div class="card-label"><?php _e('Templates Generated', 'businesscraft-ai'); ?></div>
                                    <div class="card-trend" id="templates-trend">-</div>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-icon">🎯</div>
                                <div class="card-content">
                                    <div class="card-value" id="opportunities-viewed-analytics">-</div>
                                    <div class="card-label"><?php _e('Opportunities Viewed', 'businesscraft-ai'); ?></div>
                                    <div class="card-trend" id="opportunities-trend">-</div>
                                </div>
                            </div>
                        </div>

                        <!-- Analytics Charts -->
                        <div class="analytics-charts">
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h3><?php _e('Usage Over Time', 'businesscraft-ai'); ?></h3>
                                    <div class="chart-controls">
                                        <select id="usage-chart-period">
                                            <option value="7"><?php _e('Last 7 days', 'businesscraft-ai'); ?></option>
                                            <option value="30" selected><?php _e('Last 30 days', 'businesscraft-ai'); ?></option>
                                            <option value="90"><?php _e('Last 90 days', 'businesscraft-ai'); ?></option>
                                        </select>
                                    </div>
                                </div>
                                <canvas id="usage-chart" width="400" height="200"></canvas>
                            </div>

                            <div class="chart-container">
                                <div class="chart-header">
                                    <h3><?php _e('Feature Usage Distribution', 'businesscraft-ai'); ?></h3>
                                </div>
                                <canvas id="features-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Recent Activity Timeline -->
                        <div class="activity-timeline">
                            <h3><?php _e('Recent Activity', 'businesscraft-ai'); ?></h3>
                            <div id="activity-timeline-content" class="timeline-content">
                                <div class="loading-activity">
                                    <div class="loading-spinner"></div>
                                    <p><?php _e('Loading activity timeline...', 'businesscraft-ai'); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Insights -->
                        <div class="performance-insights">
                            <h3><?php _e('Performance Insights', 'businesscraft-ai'); ?></h3>
                            <div class="insights-grid">
                                <div class="insight-card">
                                    <div class="insight-icon">🚀</div>
                                    <div class="insight-content">
                                        <h4><?php _e('Most Productive Day', 'businesscraft-ai'); ?></h4>
                                        <p id="most-productive-day">-</p>
                                    </div>
                                </div>

                                <div class="insight-card">
                                    <div class="insight-icon">⭐</div>
                                    <div class="insight-content">
                                        <h4><?php _e('Favorite Feature', 'businesscraft-ai'); ?></h4>
                                        <p id="favorite-feature">-</p>
                                    </div>
                                </div>

                                <div class="insight-card">
                                    <div class="insight-icon">📈</div>
                                    <div class="insight-content">
                                        <h4><?php _e('Growth This Month', 'businesscraft-ai'); ?></h4>
                                        <p id="monthly-growth">-</p>
                                    </div>
                                </div>

                                <div class="insight-card">
                                    <div class="insight-icon">💡</div>
                                    <div class="insight-content">
                                        <h4><?php _e('AI Efficiency Score', 'businesscraft-ai'); ?></h4>
                                        <p id="efficiency-score">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Live Opportunities Tab -->
                <div id="opportunities" class="tab-pane">
                    <?php get_template_part('template-parts/dashboard-opportunities'); ?>
                </div>

                <!-- Notifications Tab -->
                <div id="notifications" class="tab-pane">
                    <div class="notifications-container" data-component="notification-center">
                        <div class="notifications-header">
                            <h2><?php _e('🔔 Notification Center', 'businesscraft-ai'); ?></h2>
                            <p><?php _e('Manage your notification preferences and view recent alerts.', 'businesscraft-ai'); ?></p>
                        </div>

                        <!-- Notification Preferences -->
                        <div class="notification-preferences">
                            <h3><?php _e('Notification Preferences', 'businesscraft-ai'); ?></h3>

                            <div class="preference-categories">
                                <!-- Email Notifications -->
                                <div class="preference-category">
                                    <h4><?php _e('📧 Email Notifications', 'businesscraft-ai'); ?></h4>
                                    <div class="preference-items">
                                        <label class="notification-toggle">
                                            <input type="checkbox" id="email-opportunities" class="notification-checkbox">
                                            <span class="toggle-slider"></span>
                                            <div class="toggle-content">
                                                <span class="toggle-title"><?php _e('New Opportunities', 'businesscraft-ai'); ?></span>
                                                <span class="toggle-description"><?php _e('Get notified when new business opportunities match your interests', 'businesscraft-ai'); ?></span>
                                            </div>
                                        </label>

                                        <label class="notification-toggle">
                                            <input type="checkbox" id="email-credits" class="notification-checkbox">
                                            <span class="toggle-slider"></span>
                                            <div class="toggle-content">
                                                <span class="toggle-title"><?php _e('Credit Alerts', 'businesscraft-ai'); ?></span>
                                                <span class="toggle-description"><?php _e('Receive alerts when your credits are running low', 'businesscraft-ai'); ?></span>
                                            </div>
                                        </label>

                                        <label class="notification-toggle">
                                            <input type="checkbox" id="email-templates" class="notification-checkbox">
                                            <span class="toggle-slider"></span>
                                            <div class="toggle-content">
                                                <span class="toggle-title"><?php _e('Template Updates', 'businesscraft-ai'); ?></span>
                                                <span class="toggle-description"><?php _e('Get notified about new templates and AI enhancements', 'businesscraft-ai'); ?></span>
                                            </div>
                                        </label>

                                        <label class="notification-toggle">
                                            <input type="checkbox" id="email-weekly" class="notification-checkbox">
                                            <span class="toggle-slider"></span>
                                            <div class="toggle-content">
                                                <span class="toggle-title"><?php _e('Weekly Summary', 'businesscraft-ai'); ?></span>
                                                <span class="toggle-description"><?php _e('Weekly analytics and insights about your AI usage', 'businesscraft-ai'); ?></span>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Push Notifications -->
                                <div class="preference-category">
                                    <h4><?php _e('🔔 Browser Notifications', 'businesscraft-ai'); ?></h4>
                                    <div class="preference-items">
                                        <label class="notification-toggle">
                                            <input type="checkbox" id="push-urgent" class="notification-checkbox">
                                            <span class="toggle-slider"></span>
                                            <div class="toggle-content">
                                                <span class="toggle-title"><?php _e('Urgent Alerts', 'businesscraft-ai'); ?></span>
                                                <span class="toggle-description"><?php _e('Critical notifications like account security alerts', 'businesscraft-ai'); ?></span>
                                            </div>
                                        </label>

                                        <label class="notification-toggle">
                                            <input type="checkbox" id="push-opportunities" class="notification-checkbox">
                                            <span class="toggle-slider"></span>
                                            <div class="toggle-content">
                                                <span class="toggle-title"><?php _e('Live Opportunities', 'businesscraft-ai'); ?></span>
                                                <span class="toggle-description"><?php _e('Real-time notifications for time-sensitive opportunities', 'businesscraft-ai'); ?></span>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Frequency Settings -->
                                <div class="preference-category">
                                    <h4><?php _e('⏰ Frequency Settings', 'businesscraft-ai'); ?></h4>
                                    <div class="preference-items">
                                        <div class="frequency-setting">
                                            <label><?php _e('Opportunity Alert Frequency', 'businesscraft-ai'); ?></label>
                                            <select id="opportunity-frequency" class="frequency-select">
                                                <option value="immediate"><?php _e('Immediate', 'businesscraft-ai'); ?></option>
                                                <option value="daily"><?php _e('Daily Digest', 'businesscraft-ai'); ?></option>
                                                <option value="weekly"><?php _e('Weekly Summary', 'businesscraft-ai'); ?></option>
                                            </select>
                                        </div>

                                        <div class="frequency-setting">
                                            <label><?php _e('Credit Alert Threshold', 'businesscraft-ai'); ?></label>
                                            <input type="number" id="credit-threshold" class="threshold-input" min="10" max="500" value="50">
                                            <span class="input-suffix"><?php _e('credits remaining', 'businesscraft-ai'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="notification-actions">
                                <button class="btn btn-primary" onclick="saveNotificationPreferences()">
                                    <?php _e('Save Preferences', 'businesscraft-ai'); ?>
                                </button>
                                <button class="btn btn-secondary" onclick="testNotifications()">
                                    <?php _e('Send Test Notification', 'businesscraft-ai'); ?>
                                </button>
                            </div>
                        </div>

                        <!-- Recent Notifications -->
                        <div class="recent-notifications">
                            <h3><?php _e('Recent Notifications', 'businesscraft-ai'); ?></h3>
                            <div id="notifications-list" class="notifications-list">
                                <div class="loading-notifications">
                                    <div class="loading-spinner"></div>
                                    <p><?php _e('Loading recent notifications...', 'businesscraft-ai'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences Tab -->
                <div id="preferences" class="tab-pane">
                    <div class="preferences-container">
                        <div class="preferences-header">
                            <h2><?php _e('Account Preferences', 'businesscraft-ai'); ?></h2>
                            <p><?php _e('Customize your BusinessCraft AI experience.', 'businesscraft-ai'); ?></p>
                        </div>

                        <div class="preferences-sections">
                            <!-- Export Preferences -->
                            <div class="preference-section">
                                <h3><?php _e('📄 Export Preferences', 'businesscraft-ai'); ?></h3>
                                <div class="preference-group">
                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Default Export Format', 'businesscraft-ai'); ?></span>
                                        <select id="default-export-format" class="preference-input">
                                            <option value="pdf"><?php _e('PDF', 'businesscraft-ai'); ?></option>
                                            <option value="docx"><?php _e('Word Document', 'businesscraft-ai'); ?></option>
                                            <option value="txt"><?php _e('Text File', 'businesscraft-ai'); ?></option>
                                        </select>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Auto-export after generation', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="auto-export" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Automatically create export when AI generates content', 'businesscraft-ai'); ?></span>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Email exports to me', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="email-exports" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Send export files to your email address', 'businesscraft-ai'); ?></span>
                                    </label>
                                </div>
                            </div>

                            <!-- Credit & Billing Preferences -->
                            <div class="preference-section">
                                <h3><?php _e('💳 Credit & Billing Preferences', 'businesscraft-ai'); ?></h3>
                                <div class="preference-group">
                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Low credit notifications', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="low-credit-notifications" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Notify me when credits are running low', 'businesscraft-ai'); ?></span>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Low credit threshold', 'businesscraft-ai'); ?></span>
                                        <input type="number" id="low-credit-threshold" class="preference-input" min="10" max="500" value="50">
                                        <span class="preference-description"><?php _e('Notify when credits fall below this amount', 'businesscraft-ai'); ?></span>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Purchase confirmation emails', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="purchase-confirmations" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Send email confirmations for credit purchases', 'businesscraft-ai'); ?></span>
                                    </label>
                                </div>
                            </div>

                            <!-- AI & Content Preferences -->
                            <div class="preference-section">
                                <h3><?php _e('🤖 AI & Content Preferences', 'businesscraft-ai'); ?></h3>
                                <div class="preference-group">
                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Response Style', 'businesscraft-ai'); ?></span>
                                        <select id="ai-response-style" class="preference-input">
                                            <option value="professional"><?php _e('Professional', 'businesscraft-ai'); ?></option>
                                            <option value="casual"><?php _e('Casual', 'businesscraft-ai'); ?></option>
                                            <option value="detailed"><?php _e('Detailed', 'businesscraft-ai'); ?></option>
                                            <option value="concise"><?php _e('Concise', 'businesscraft-ai'); ?></option>
                                        </select>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Include examples in responses', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="include-examples" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Add practical examples to AI responses', 'businesscraft-ai'); ?></span>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Auto-include opportunities', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="auto-include-opportunities" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Automatically include relevant opportunities in responses', 'businesscraft-ai'); ?></span>
                                    </label>
                                </div>
                            </div>

                            <!-- Notification Preferences -->
                            <div class="preference-section">
                                <h3><?php _e('🔔 Notification Preferences', 'businesscraft-ai'); ?></h3>
                                <div class="preference-group">
                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Email notifications', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="email-notifications" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Receive email notifications for important updates', 'businesscraft-ai'); ?></span>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Opportunity alerts', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="opportunity-notifications" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Get notified about new business opportunities', 'businesscraft-ai'); ?></span>
                                    </label>

                                    <label class="preference-item">
                                        <span class="preference-label"><?php _e('Weekly analytics email', 'businesscraft-ai'); ?></span>
                                        <input type="checkbox" id="weekly-analytics" class="preference-checkbox">
                                        <span class="preference-description"><?php _e('Receive weekly usage analytics and insights', 'businesscraft-ai'); ?></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="preferences-actions">
                            <button class="btn btn-primary" onclick="savePreferences()">
                                <?php _e('Save Preferences', 'businesscraft-ai'); ?>
                            </button>
                            <button class="btn btn-secondary" onclick="resetPreferences()">
                                <?php _e('Reset to Defaults', 'businesscraft-ai'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Templates Tab -->
                <div id="templates" class="tab-pane">
                    <div class="templates-section" data-component="template-enhancer">
                        <div class="templates-header">
                            <h2><?php _e('🤖 AI-Enhanced Templates', 'businesscraft-ai'); ?></h2>
                            <div class="templates-view-toggle">
                                <button class="view-toggle-btn active" data-view="saved"><?php _e('My Templates', 'businesscraft-ai'); ?></button>
                                <button class="view-toggle-btn" data-view="samples"><?php _e('Template Samples', 'businesscraft-ai'); ?></button>
                                <button class="view-toggle-btn" data-view="ai-suggestions"><?php _e('AI Suggestions', 'businesscraft-ai'); ?></button>
                            </div>
                        </div>

                        <!-- AI Enhancement Panel -->
                        <div class="ai-enhancement-panel">
                            <div class="enhancement-header">
                                <h3><?php _e('✨ AI Template Enhancement', 'businesscraft-ai'); ?></h3>
                                <p><?php _e('Let AI improve your templates with industry insights and best practices.', 'businesscraft-ai'); ?></p>
                            </div>

                            <div class="enhancement-tools">
                                <button class="enhancement-btn" onclick="suggestTemplateImprovements()">
                                    <span class="btn-icon">🎯</span>
                                    <?php _e('Get AI Suggestions', 'businesscraft-ai'); ?>
                                </button>

                                <button class="enhancement-btn" onclick="generateTemplateVariations()">
                                    <span class="btn-icon">🔄</span>
                                    <?php _e('Generate Variations', 'businesscraft-ai'); ?>
                                </button>

                                <button class="enhancement-btn" onclick="optimizeForCountry()">
                                    <span class="btn-icon">🌍</span>
                                    <?php _e('Optimize for Country', 'businesscraft-ai'); ?>
                                </button>

                                <button class="enhancement-btn" onclick="addIndustryInsights()">
                                    <span class="btn-icon">🏢</span>
                                    <?php _e('Add Industry Insights', 'businesscraft-ai'); ?>
                                </button>
                            </div>
                        </div>

                        <div class="templates-filters">
                            <div class="filter-group">
                                <select id="category-filter">
                                    <option value=""><?php _e('All Categories', 'businesscraft-ai'); ?></option>
                                    <option value="business-plan"><?php _e('Business Plans', 'businesscraft-ai'); ?></option>
                                    <option value="marketing"><?php _e('Marketing Strategies', 'businesscraft-ai'); ?></option>
                                    <option value="financial"><?php _e('Financial Forecasts', 'businesscraft-ai'); ?></option>
                                </select>
                                <input type="text" id="search-templates" placeholder="<?php _e('Search templates...', 'businesscraft-ai'); ?>">
                                <select id="sort-templates">
                                    <option value="updated_at-DESC"><?php _e('Recently Updated', 'businesscraft-ai'); ?></option>
                                    <option value="created_at-DESC"><?php _e('Recently Created', 'businesscraft-ai'); ?></option>
                                    <option value="name-ASC"><?php _e('Name A-Z', 'businesscraft-ai'); ?></option>
                                </select>
                            </div>
                        </div>

                        <div class="templates-content">
                            <div id="templates-grid" class="templates-grid">
                                <div class="loading-spinner">
                                    <div class="spinner"></div>
                                    <p><?php _e('Loading templates...', 'businesscraft-ai'); ?></p>
                                </div>
                            </div>

                            <div id="load-more-section" class="load-more-section" style="display: none;">
                                <button id="load-more-templates" class="btn-secondary"><?php _e('Load More', 'businesscraft-ai'); ?></button>
                            </div>

                            <!-- Template Preview Modal -->
                            <div id="template-preview-modal" class="template-modal" style="display: none;">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modal-template-title"></h3>
                                        <button id="close-template-modal" class="close-btn">&times;</button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="template-preview-content">
                                            <div class="template-meta">
                                                <div class="meta-item">
                                                    <span class="meta-label"><?php _e('Category:', 'businesscraft-ai'); ?></span>
                                                    <span id="modal-template-category"></span>
                                                </div>
                                                <div class="meta-item">
                                                    <span class="meta-label"><?php _e('Language:', 'businesscraft-ai'); ?></span>
                                                    <span id="modal-template-language"></span>
                                                </div>
                                            </div>

                                            <div class="template-description">
                                                <h4><?php _e('Description', 'businesscraft-ai'); ?></h4>
                                                <p id="modal-template-description"></p>
                                            </div>

                                            <div class="template-content">
                                                <h4><?php _e('Template Content', 'businesscraft-ai'); ?></h4>
                                                <div id="modal-template-content" class="template-content-display"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button id="use-template-btn" class="btn-primary">
                                            <?php _e('Use This Template', 'businesscraft-ai'); ?>
                                        </button>
                                        <button id="save-template-btn" class="btn-secondary">
                                            <?php _e('Save to My Templates', 'businesscraft-ai'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feedback Tab -->
                <div id="feedback" class="tab-pane">
                    <div class="feedback-section">
                        <div class="feedback-header">
                            <h2><?php _e('Your Feedback', 'businesscraft-ai'); ?></h2>
                            <p><?php _e('Help us improve BusinessCraft AI by sharing your experience and suggestions.', 'businesscraft-ai'); ?></p>
                        </div>

                        <!-- General Feedback Form -->
                        <div class="general-feedback-form">
                            <h3><?php _e('General Feedback', 'businesscraft-ai'); ?></h3>
                            <?php
                            // Include the feedback widget component
                            get_template_part('template-parts/components/feedback-widget', null, array(
                                'widget_id' => 'dashboard-general-feedback',
                                'context' => 'dashboard',
                                'show_detailed' => true,
                                'compact_mode' => false
                            ));
                            ?>
                        </div>

                        <!-- Feedback History -->
                        <div class="feedback-history-section">
                            <h3><?php _e('Your Feedback History', 'businesscraft-ai'); ?></h3>
                            <div id="feedback-history" class="feedback-history">
                                <div class="loading-feedback">
                                    <div class="loading-spinner"></div>
                                    <p><?php _e('Loading your feedback history...', 'businesscraft-ai'); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Feedback Statistics -->
                        <div class="feedback-stats-section">
                            <h3><?php _e('Your Impact', 'businesscraft-ai'); ?></h3>
                            <div class="feedback-stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">💬</div>
                                    <div class="stat-number" id="total-feedback-count">-</div>
                                    <div class="stat-label"><?php _e('Total Feedback', 'businesscraft-ai'); ?></div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">⭐</div>
                                    <div class="stat-number" id="average-rating">-</div>
                                    <div class="stat-label"><?php _e('Average Rating', 'businesscraft-ai'); ?></div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">🎯</div>
                                    <div class="stat-number" id="helpful-responses">-</div>
                                    <div class="stat-label"><?php _e('Helpful Responses', 'businesscraft-ai'); ?></div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">🚀</div>
                                    <div class="stat-number" id="improvement-suggestions">-</div>
                                    <div class="stat-label"><?php _e('Suggestions Made', 'businesscraft-ai'); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Credit Purchase Modal -->
<div id="credit-purchase-modal" class="credit-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><?php _e('Purchase Credits', 'businesscraft-ai'); ?></h3>
            <button id="close-credit-modal" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
            <div class="credit-packages" id="credit-packages-container">
                <div class="loading-packages">
                    <div class="loading-spinner"></div>
                    <p><?php _e('Loading packages...', 'businesscraft-ai'); ?></p>
                </div>
            </div>
            <div class="payment-info">
                <p><?php _e('Secure payment powered by Paystack. All major payment methods accepted.', 'businesscraft-ai'); ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.dashboard-wrapper {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.dashboard-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
}

.dashboard-header p {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
}

.dashboard-content {
    padding: 0;
}

/* Tab Navigation */
.tabs-navigation {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    overflow-x: auto;
}

.tab-button {
    background: none;
    border: none;
    padding: 20px 30px;
    font-size: 16px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.tab-button:hover {
    color: #667eea;
    background: #fff;
}

.tab-button.active {
    color: #667eea;
    background: #fff;
    border-bottom-color: #667eea;
}

/* Tab Content */
.tabs-content {
    min-height: 500px;
}

.tab-pane {
    display: none;
    padding: 40px;
}

.tab-pane.active {
    display: block;
}

/* Templates Quick Access */
.templates-quick-access {
    margin-bottom: 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 30px;
    border: 1px solid #e9ecef;
}

.templates-quick-access .section-header {
    text-align: center;
    margin-bottom: 30px;
}

.templates-quick-access .section-header h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.8em;
}

.templates-quick-access .section-description {
    color: #666;
    font-size: 1.1em;
    margin: 0;
}

.templates-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.template-quick-card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.template-quick-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.template-quick-card .template-icon {
    font-size: 3em;
    margin-bottom: 15px;
    display: block;
}

.template-quick-card h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.3em;
}

.template-quick-card p {
    color: #666;
    margin: 0 0 20px 0;
    font-size: 0.95em;
    line-height: 1.5;
}

.template-cta-btn {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: background 0.3s ease;
}

.template-cta-btn:hover {
    background: #5a67d8;
    color: white;
    text-decoration: none;
}

.templates-main-cta {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
}

.cta-description {
    margin: 15px 0 0 0;
    color: #666;
    font-size: 0.95em;
}

/* Export History Section */
.export-history-section {
    grid-column: 1 / -1;
    margin-top: 20px;
}

.export-history-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.export-history {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
}

.loading-exports {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: #666;
}

.loading-exports .loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.export-actions-footer {
    margin-top: 20px;
    text-align: center;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
    text-decoration: none;
}

/* Export History Styles */
.no-exports {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-exports-icon {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-exports h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.3em;
}

.no-exports p {
    margin: 0 0 25px 0;
    line-height: 1.6;
}

.export-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.export-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.export-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.export-icon {
    font-size: 2em;
    flex-shrink: 0;
}

.export-info {
    flex: 1;
}

.export-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.export-meta {
    font-size: 0.85em;
    color: #666;
}

.export-actions {
    flex-shrink: 0;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    background: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    font-weight: 600;
}

.btn-small:hover {
    background: #5a67d8;
    color: white;
    text-decoration: none;
}

.export-footer {
    margin-top: 20px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* Enhanced Export History Styles */
.loading-exports {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
}

.loading-exports .loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.export-error {
    text-align: center;
    padding: 40px 20px;
    color: #dc3545;
}

.export-error .no-exports-icon {
    font-size: 3em;
    margin-bottom: 15px;
    display: block;
}

.export-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.export-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.export-header {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    gap: 15px;
}

.export-icon {
    font-size: 2em;
    flex-shrink: 0;
    width: 50px;
    text-align: center;
}

.export-info {
    flex: 1;
    min-width: 0;
}

.export-name {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    line-height: 1.3;
}

.export-description {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 10px;
    line-height: 1.4;
}

.export-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 0.85em;
    color: #666;
}

.export-meta span {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.export-format {
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 600;
    color: #495057;
}

.export-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: capitalize;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.export-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-start;
}

.export-actions .btn {
    padding: 6px 12px;
    font-size: 0.85em;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.export-actions .btn-primary {
    background: #667eea;
    color: white;
}

.export-actions .btn-primary:hover {
    background: #5a67d8;
}

.export-actions .btn-secondary {
    background: #6c757d;
    color: white;
}

.export-actions .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.export-actions .btn-danger {
    background: #dc3545;
    color: white;
}

.export-actions .btn-danger:hover {
    background: #c82333;
}

.export-stats {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-size: 0.85em;
    color: #666;
}

.download-count {
    font-weight: 500;
}

.export-count {
    color: #666;
    font-size: 0.9em;
    margin: 0;
}

/* Responsive adjustments for exports */
@media (max-width: 768px) {
    .export-header {
        flex-direction: column;
        gap: 10px;
    }

    .export-actions {
        width: 100%;
        justify-content: space-between;
    }

    .export-meta {
        flex-direction: column;
        gap: 5px;
    }

    .export-actions .btn {
        flex: 1;
        justify-content: center;
    }
}

/* Preferences Styles */
.preferences-container {
    max-width: 800px;
    margin: 0 auto;
}

.preferences-header {
    text-align: center;
    margin-bottom: 40px;
}

.preferences-header h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 2em;
}

.preferences-header p {
    color: #666;
    font-size: 1.1em;
    margin: 0;
}

.preferences-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 40px;
}

.preference-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.preference-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.3em;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.preference-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.preference-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    cursor: pointer;
}

.preference-label {
    font-weight: 600;
    color: #333;
    font-size: 1em;
}

.preference-input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
    max-width: 300px;
}

.preference-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.preference-checkbox {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    cursor: pointer;
}

.preference-description {
    font-size: 0.9em;
    color: #666;
    font-style: italic;
    margin-top: 4px;
}

.preferences-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
}

.preferences-actions .btn {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.preferences-actions .btn-primary {
    background: #667eea;
    color: white;
}

.preferences-actions .btn-primary:hover {
    background: #5a67d8;
}

.preferences-actions .btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.preferences-actions .btn-secondary:hover {
    background: #e9ecef;
    color: #333;
}

/* Responsive preferences */
@media (max-width: 768px) {
    .preferences-container {
        padding: 0 10px;
    }

    .preference-section {
        padding: 20px;
    }

    .preferences-actions {
        flex-direction: column;
        align-items: center;
    }

    .preferences-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Credit Purchase Modal */
.credit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.credit-modal .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.credit-modal .modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.credit-modal .modal-header h3 {
    margin: 0;
    color: #333;
}

.credit-modal .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.credit-modal .close-btn:hover {
    color: #333;
}

.credit-modal .modal-body {
    padding: 30px;
}

.credit-packages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.package-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px 20px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.package-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.package-card.popular {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.package-card h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2em;
}

.package-card .credits {
    font-size: 1.5em;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 10px;
}

.package-card .price {
    font-size: 1.3em;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.package-card .savings {
    color: #28a745;
    font-weight: 600;
    font-size: 0.9em;
    margin-bottom: 20px;
}

.package-card .purchase-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    width: 100%;
}

.package-card .purchase-btn:hover {
    background: #5a67d8;
}

.payment-info {
    text-align: center;
    color: #666;
    font-size: 0.9em;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.payment-info p {
    margin: 0;
}

/* Feedback Tab Styles */
.feedback-section {
    max-width: 800px;
    margin: 0 auto;
}

.feedback-header {
    text-align: center;
    margin-bottom: 40px;
}

.feedback-header h2 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 2em;
}

.feedback-header p {
    margin: 0;
    color: #666;
    font-size: 1.1em;
    line-height: 1.6;
}

.general-feedback-form {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid #e9ecef;
}

.general-feedback-form h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.5em;
}

.feedback-history-section {
    margin-bottom: 40px;
}

.feedback-history-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.5em;
}

.feedback-history {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
}

.loading-feedback {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: #666;
}

.loading-feedback .loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.feedback-stats-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.5em;
}

.feedback-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.stat-card .stat-icon {
    font-size: 2.5em;
    margin-bottom: 15px;
    display: block;
}

.stat-card .stat-number {
    font-size: 2em;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 10px;
    display: block;
}

.stat-card .stat-label {
    color: #666;
    font-weight: 600;
    font-size: 0.9em;
}

.feedback-history-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.feedback-history-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.feedback-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.feedback-item-rating {
    display: flex;
    gap: 3px;
}

.feedback-item-rating .star {
    color: #ffc107;
    font-size: 16px;
}

.feedback-item-date {
    color: #666;
    font-size: 0.9em;
}

.feedback-item-content {
    color: #333;
    line-height: 1.6;
    margin-bottom: 10px;
}

.feedback-item-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85em;
    color: #666;
}

.feedback-item-context {
    background: #e9ecef;
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: 600;
}

/* Responsive adjustments for feedback */
@media (max-width: 768px) {
    .feedback-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .general-feedback-form {
        padding: 20px;
    }

    .feedback-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

.view-all-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9em;
}

.view-all-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.stats-section h2,
.quick-actions h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* Quick Actions */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    background: #fff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-icon {
    font-size: 2em;
    display: block;
    margin-bottom: 15px;
}

.action-card h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.1em;
}

.action-card p {
    margin: 0;
    color: #666;
    font-size: 0.9em;
}

/* Templates Section */
.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.templates-view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 6px;
    overflow: hidden;
}

.view-toggle-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-toggle-btn.active {
    background: #667eea;
    color: white;
}

.templates-filters {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.load-more-section {
    text-align: center;
    margin-top: 20px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Responsive */
@media (max-width: 768px) {
    .templates-quick-access {
        padding: 20px;
        margin-bottom: 30px;
    }

    .templates-quick-access .section-header h2 {
        font-size: 1.5em;
    }

    .templates-showcase {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 25px;
    }

    .template-quick-card {
        padding: 20px;
    }

    .template-quick-card .template-icon {
        font-size: 2.5em;
        margin-bottom: 12px;
    }

    .template-quick-card h3 {
        font-size: 1.2em;
    }

    .btn-large {
        padding: 14px 28px;
        font-size: 15px;
    }

    .overview-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }

    .actions-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .tabs-navigation {
        flex-wrap: wrap;
    }

    .tab-button {
        padding: 15px 20px;
        font-size: 14px;
    }

    .templates-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }
}

/* Template Cards */
.template-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.template-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.4;
}

.owner-badge {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.meta-badge {
    background: #f8f9fa;
    color: #666;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.template-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.template-stats {
    color: #888;
    font-size: 12px;
    margin-bottom: 16px;
}

.template-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.template-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Template Modal */
.template-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.template-preview-content .meta-item {
    display: flex;
    margin-bottom: 8px;
}

.meta-label {
    font-weight: 600;
    margin-right: 8px;
    min-width: 80px;
}

.template-content-display {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e1e5e9;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.no-templates-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-templates-message h3 {
    color: #333;
    margin-bottom: 12px;
}

.error-message {
    text-align: center;
    padding: 40px 20px;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 8px;
    margin: 20px 0;
}
</style>

<script>
function switchTab(tabId) {
    // Remove active class from all buttons and panes
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
    
    // Add active class to clicked button and corresponding pane
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    document.getElementById(tabId).classList.add('active');
}

// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            switchTab(tabId);

            // Initialize templates when templates tab is activated
            if (tabId === 'templates' && typeof window.chatgabiTemplates !== 'undefined') {
                initializeTemplatesTab();
            }
        });
    });

    // Initialize templates view toggle
    initializeTemplatesViewToggle();
});

function initializeTemplatesTab() {
    console.log('Initializing templates tab');

    // Initialize templates system if not already done
    if (typeof window.dashboardTemplates === 'undefined') {
        window.dashboardTemplates = {
            currentView: 'saved',
            currentTemplates: [],
            currentFilters: {
                category: '',
                search: '',
                sort: 'updated_at-DESC'
            }
        };
    }

    // Load initial templates (saved templates by default)
    loadDashboardTemplates(true);
}

function initializeTemplatesViewToggle() {
    document.querySelectorAll('.view-toggle-btn').forEach(button => {
        button.addEventListener('click', function() {
            const view = this.getAttribute('data-view');

            // Update active state
            document.querySelectorAll('.view-toggle-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Switch view
            if (typeof window.dashboardTemplates !== 'undefined') {
                window.dashboardTemplates.currentView = view;
                loadDashboardTemplates(true);
            }
        });
    });

    // Initialize filters
    document.getElementById('category-filter').addEventListener('change', function() {
        window.dashboardTemplates.currentFilters.category = this.value;
        loadDashboardTemplates(true);
    });

    document.getElementById('search-templates').addEventListener('input', debounce(function() {
        window.dashboardTemplates.currentFilters.search = this.value;
        loadDashboardTemplates(true);
    }, 300));

    document.getElementById('sort-templates').addEventListener('change', function() {
        window.dashboardTemplates.currentFilters.sort = this.value;
        loadDashboardTemplates(true);
    });

    // Modal close handlers
    document.getElementById('close-template-modal').addEventListener('click', function() {
        document.getElementById('template-preview-modal').style.display = 'none';
    });

    // Template action handlers
    document.getElementById('use-template-btn').addEventListener('click', function() {
        useSelectedTemplate();
    });

    document.getElementById('save-template-btn').addEventListener('click', function() {
        saveSelectedTemplate();
    });
}

function loadDashboardTemplates(reset = false) {
    const grid = document.getElementById('templates-grid');

    if (reset) {
        grid.innerHTML = '<div class="loading-spinner"><div class="spinner"></div><p>Loading templates...</p></div>';
    }

    const params = new URLSearchParams({
        user_only: window.dashboardTemplates.currentView === 'saved' ? 'true' : 'false',
        category: window.dashboardTemplates.currentFilters.category,
        search: window.dashboardTemplates.currentFilters.search,
        sort: window.dashboardTemplates.currentFilters.sort
    });

    fetch(`<?php echo rest_url('chatgabi/v1/templates'); ?>?${params}`, {
        method: 'GET',
        headers: {
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.dashboardTemplates.currentTemplates = data.templates;
            renderDashboardTemplates(data.templates);
        } else {
            grid.innerHTML = '<div class="error-message">Error loading templates: ' + (data.message || 'Unknown error') + '</div>';
        }
    })
    .catch(error => {
        console.error('Error loading templates:', error);
        grid.innerHTML = '<div class="error-message">Error loading templates. Please try again.</div>';
    });
}

function renderDashboardTemplates(templates) {
    const grid = document.getElementById('templates-grid');

    if (templates.length === 0) {
        const viewType = window.dashboardTemplates.currentView;
        const message = viewType === 'saved'
            ? 'You haven\'t saved any templates yet. Browse template samples to get started!'
            : 'No template samples found matching your criteria.';

        grid.innerHTML = `
            <div class="no-templates-message">
                <h3>No Templates Found</h3>
                <p>${message}</p>
                ${viewType === 'saved' ? '<button class="btn-primary" onclick="switchToSamples()">Browse Template Samples</button>' : ''}
            </div>
        `;
        return;
    }

    let html = '';
    templates.forEach(template => {
        html += renderDashboardTemplateCard(template);
    });

    grid.innerHTML = html;
}

function renderDashboardTemplateCard(template) {
    const isOwner = template.is_owner || false;
    const category = template.category || { name: 'General', icon: '📋' };

    return `
        <div class="template-card" data-template-id="${template.id}">
            <div class="template-header">
                <h3 class="template-title">${template.title}</h3>
                ${isOwner ? '<span class="owner-badge">👤</span>' : ''}
            </div>

            <div class="template-meta">
                <span class="meta-badge">${category.icon} ${category.name}</span>
                <span class="meta-badge">${getLanguageName(template.language_code || 'en')}</span>
            </div>

            <p class="template-description">${template.description || 'No description available'}</p>

            <div class="template-stats">
                <span class="usage-count">Used ${template.usage_count || 0} times</span>
            </div>

            <div class="template-actions">
                <button class="btn-primary" onclick="previewTemplate(${template.id})">
                    Preview
                </button>
                ${isOwner ? `
                    <button class="btn-secondary" onclick="editTemplate(${template.id})">
                        Edit
                    </button>
                    <button class="btn-danger" onclick="deleteTemplate(${template.id})">
                        Delete
                    </button>
                ` : ''}
            </div>
        </div>
    `;
}

function previewTemplate(templateId) {
    const template = window.dashboardTemplates.currentTemplates.find(t => t.id == templateId);
    if (!template) return;

    document.getElementById('modal-template-title').textContent = template.title;
    document.getElementById('modal-template-category').textContent = template.category?.name || 'General';
    document.getElementById('modal-template-language').textContent = getLanguageName(template.language_code || 'en');
    document.getElementById('modal-template-description').textContent = template.description || 'No description available';
    document.getElementById('modal-template-content').textContent = template.prompt_content || 'No content available';

    // Store selected template for actions
    window.dashboardTemplates.selectedTemplate = template;

    document.getElementById('template-preview-modal').style.display = 'block';
}

function useSelectedTemplate() {
    const template = window.dashboardTemplates.selectedTemplate;
    if (!template) return;

    // Redirect to chat interface with template
    const chatUrl = '<?php echo home_url('/chat'); ?>?template=' + template.id;
    window.location.href = chatUrl;
}

function saveSelectedTemplate() {
    const template = window.dashboardTemplates.selectedTemplate;
    if (!template) return;

    // Save template to user's collection
    fetch('<?php echo rest_url('chatgabi/v1/templates'); ?>', {
        method: 'POST',
        headers: {
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            title: template.title + ' (Copy)',
            description: template.description,
            prompt_content: template.prompt_content,
            category_id: template.category_id,
            language_code: template.language_code
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Template saved to your collection!');
            document.getElementById('template-preview-modal').style.display = 'none';
            if (window.dashboardTemplates.currentView === 'saved') {
                loadDashboardTemplates(true);
            }
        } else {
            alert('Error saving template: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error saving template:', error);
        alert('Error saving template. Please try again.');
    });
}

function switchToSamples() {
    document.querySelector('[data-view="samples"]').click();
}

function getLanguageName(code) {
    const languages = {
        'en': 'English',
        'tw': 'Twi',
        'sw': 'Swahili',
        'yo': 'Yoruba',
        'zu': 'Zulu'
    };
    return languages[code] || 'English';
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Load export history
function loadExportHistory() {
    const $container = $('#export-history');

    // Show loading state
    $container.html(`
        <div class="loading-exports">
            <div class="loading-spinner"></div>
            <p><?php _e("Loading your export history...", "businesscraft-ai"); ?></p>
        </div>
    `);

    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'businesscraft_ai_get_export_history',
            nonce: chatgabi_ajax.nonce
        },
        success: function(response) {
            if (response.success && response.data.exports) {
                renderExportHistory(response.data.exports);
            } else {
                $container.html(`
                    <div class="no-exports">
                        <div class="no-exports-icon">📄</div>
                        <h4><?php _e("No Documents Yet", "businesscraft-ai"); ?></h4>
                        <p><?php _e("Create your first professional business document.", "businesscraft-ai"); ?></p>
                        <button class="btn btn-primary" onclick="createSampleExport()">
                            <?php _e("Create Sample Export", "businesscraft-ai"); ?>
                        </button>
                    </div>
                `);
            }
        },
        error: function() {
            $container.html(`
                <div class="export-error">
                    <div class="no-exports-icon">❌</div>
                    <h4><?php _e("Failed to Load Exports", "businesscraft-ai"); ?></h4>
                    <p><?php _e("Please try again later.", "businesscraft-ai"); ?></p>
                    <button class="btn btn-secondary" onclick="loadExportHistory()">
                        <?php _e("Retry", "businesscraft-ai"); ?>
                    </button>
                </div>
            `);
        }
    });
}

// Render export history
function renderExportHistory(exports) {
    const $container = $('#export-history');

    if (exports.length === 0) {
        $container.html(`
            <div class="no-exports">
                <div class="no-exports-icon">📄</div>
                <h4><?php _e("No Documents Yet", "businesscraft-ai"); ?></h4>
                <p><?php _e("Create your first professional business document using our AI-powered templates.", "businesscraft-ai"); ?></p>
                <button class="btn btn-primary" onclick="createSampleExport()">
                    <?php _e("Create Sample Export", "businesscraft-ai"); ?>
                </button>
            </div>
        `);
        return;
    }

    let html = '<div class="export-list">';

    exports.slice(0, 10).forEach(function(exportItem) {
        const statusClass = exportItem.status === 'completed' ? 'status-completed' :
                           exportItem.status === 'processing' ? 'status-processing' : 'status-failed';

        html += `
            <div class="export-item" data-export-id="${exportItem.id}">
                <div class="export-header">
                    <div class="export-icon">${exportItem.icon}</div>
                    <div class="export-info">
                        <div class="export-name">${exportItem.title}</div>
                        <div class="export-description">${exportItem.description || ''}</div>
                        <div class="export-meta">
                            <span class="export-format">${exportItem.format.toUpperCase()}</span>
                            <span class="export-size">${exportItem.formatted_size}</span>
                            <span class="export-date">${exportItem.formatted_date}</span>
                            <span class="export-status ${statusClass}">${exportItem.status}</span>
                        </div>
                    </div>
                    <div class="export-actions">
                        ${exportItem.can_download ?
                            `<button class="btn btn-primary btn-sm" onclick="downloadExport(${exportItem.id})">
                                <span class="btn-icon">⬇️</span> <?php _e("Download", "businesscraft-ai"); ?>
                            </button>` :
                            `<button class="btn btn-secondary btn-sm" disabled>
                                <span class="btn-icon">⏳</span> ${exportItem.status}
                            </button>`
                        }
                        <button class="btn btn-danger btn-sm" onclick="deleteExport(${exportItem.id})">
                            <span class="btn-icon">🗑️</span> <?php _e("Delete", "businesscraft-ai"); ?>
                        </button>
                    </div>
                </div>
                ${exportItem.download_count > 0 ?
                    `<div class="export-stats">
                        <span class="download-count"><?php _e("Downloaded", "businesscraft-ai"); ?> ${exportItem.download_count} <?php _e("times", "businesscraft-ai"); ?></span>
                    </div>` : ''
                }
            </div>
        `;
    });

    html += '</div>';

    if (exports.length > 10) {
        html += `
            <div class="export-footer">
                <p class="export-count"><?php _e("Showing 10 of", "businesscraft-ai"); ?> ${exports.length} <?php _e("exports", "businesscraft-ai"); ?></p>
            </div>
        `;
    }

    $container.html(html);
}

// Create sample export for demonstration
function createSampleExport() {
    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'businesscraft_ai_create_export',
            nonce: chatgabi_ajax.nonce,
            export_type: 'sample',
            title: 'Sample Business Plan Export',
            description: 'A sample export to demonstrate the export functionality',
            content: '<h1>Sample Business Plan</h1><p>This is a sample business plan created for demonstration purposes.</p><h2>Executive Summary</h2><p>Our innovative business concept focuses on...</p>',
            file_format: 'pdf'
        },
        success: function(response) {
            if (response.success) {
                alert('<?php _e("Sample export created! It will appear in your history once processed.", "businesscraft-ai"); ?>');
                // Reload export history after a delay
                setTimeout(() => {
                    loadExportHistory();
                }, 2000);
            } else {
                alert('<?php _e("Failed to create sample export:", "businesscraft-ai"); ?> ' + (response.data || '<?php _e("Unknown error", "businesscraft-ai"); ?>'));
            }
        },
        error: function() {
            alert('<?php _e("Network error occurred while creating sample export.", "businesscraft-ai"); ?>');
        }
    });
}

// Download export
function downloadExport(exportId) {
    const downloadUrl = chatgabi_ajax.ajax_url +
        '?action=businesscraft_ai_download_export' +
        '&export_id=' + exportId +
        '&nonce=' + chatgabi_ajax.nonce;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Refresh export history to update download count
    setTimeout(() => {
        loadExportHistory();
    }, 1000);
}

// Delete export
function deleteExport(exportId) {
    if (!confirm('<?php _e("Are you sure you want to delete this export? This action cannot be undone.", "businesscraft-ai"); ?>')) {
        return;
    }

    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'businesscraft_ai_delete_export',
            nonce: chatgabi_ajax.nonce,
            export_id: exportId
        },
        success: function(response) {
            if (response.success) {
                // Remove export item from display
                $(`.export-item[data-export-id="${exportId}"]`).fadeOut(300, function() {
                    $(this).remove();

                    // Check if no exports left
                    if ($('.export-item').length === 0) {
                        loadExportHistory();
                    }
                });
            } else {
                alert('<?php _e("Failed to delete export:", "businesscraft-ai"); ?> ' + (response.data || '<?php _e("Unknown error", "businesscraft-ai"); ?>'));
            }
        },
        error: function() {
            alert('<?php _e("Network error occurred while deleting export.", "businesscraft-ai"); ?>');
        }
    });
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Initialize export history on page load
$(document).ready(function() {
    if ($('#export-history').length) {
        loadExportHistory();
    }
});

// Credit Purchase Modal Functions
function openCreditPurchaseModal() {
    document.getElementById('credit-purchase-modal').style.display = 'flex';
    loadCreditPackages();
}

function closeCreditPurchaseModal() {
    document.getElementById('credit-purchase-modal').style.display = 'none';
}

// Load credit packages with localized pricing
function loadCreditPackages() {
    const $container = $('#credit-packages-container');

    // Show loading state
    $container.html(`
        <div class="loading-packages">
            <div class="loading-spinner"></div>
            <p><?php _e('Loading packages...', 'businesscraft-ai'); ?></p>
        </div>
    `);

    // Make AJAX request to get localized packages
    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'businesscraft_ai_get_credit_packages',
            nonce: chatgabi_ajax.nonce
        },
        success: function(response) {
            if (response.success && response.data.packages) {
                displayCreditPackages(response.data.packages, response.data.currency);
            } else {
                $container.html(`
                    <div class="package-error">
                        <p><?php _e('Failed to load packages. Please try again.', 'businesscraft-ai'); ?></p>
                        <button class="btn btn-secondary" onclick="loadCreditPackages()">
                            <?php _e('Retry', 'businesscraft-ai'); ?>
                        </button>
                    </div>
                `);
            }
        },
        error: function() {
            $container.html(`
                <div class="package-error">
                    <p><?php _e('Network error. Please check your connection and try again.', 'businesscraft-ai'); ?></p>
                    <button class="btn btn-secondary" onclick="loadCreditPackages()">
                        <?php _e('Retry', 'businesscraft-ai'); ?>
                    </button>
                </div>
            `);
        }
    });
}

// Display credit packages
function displayCreditPackages(packages, currency) {
    const $container = $('#credit-packages-container');

    let packagesHTML = '<div class="packages-grid">';

    // Package order and labels
    const packageOrder = ['starter', 'growth', 'business'];
    const packageLabels = {
        'starter': '<?php _e('Starter Pack', 'businesscraft-ai'); ?>',
        'growth': '<?php _e('Growth Pack', 'businesscraft-ai'); ?>',
        'business': '<?php _e('Business Pack', 'businesscraft-ai'); ?>'
    };

    packageOrder.forEach((packageKey, index) => {
        if (packages[packageKey]) {
            const pkg = packages[packageKey];
            const isPopular = packageKey === 'growth';
            const savings = packageKey === 'growth' ? '<?php _e('Best Value', 'businesscraft-ai'); ?>' :
                           packageKey === 'business' ? '<?php _e('Most Credits', 'businesscraft-ai'); ?>' : '';

            packagesHTML += `
                <div class="package-card ${isPopular ? 'popular' : ''}"
                     data-package="${packageKey}"
                     data-credits="${pkg.credits}"
                     data-price="${pkg.price_local}"
                     data-currency="${pkg.currency}">
                    ${isPopular ? '<div class="popular-badge"><?php _e('Most Popular', 'businesscraft-ai'); ?></div>' : ''}
                    <h4>${packageLabels[packageKey]}</h4>
                    <div class="credits">${pkg.credits.toLocaleString()} <?php _e('Credits', 'businesscraft-ai'); ?></div>
                    <div class="price">${pkg.formatted_price}</div>
                    ${savings ? `<div class="savings">${savings}</div>` : ''}
                    <button class="purchase-btn" onclick="initiatePurchase('${packageKey}')">
                        <?php _e('Purchase', 'businesscraft-ai'); ?>
                    </button>
                </div>
            `;
        }
    });

    packagesHTML += '</div>';

    $container.html(packagesHTML);
}

// Initiate purchase process
function initiatePurchase(packageKey) {
    const $packageCard = $(`.package-card[data-package="${packageKey}"]`);
    const $purchaseBtn = $packageCard.find('.purchase-btn');

    // Show loading state
    $purchaseBtn.text('<?php _e('Processing...', 'businesscraft-ai'); ?>').prop('disabled', true);

    // Get user email
    const userEmail = '<?php echo wp_get_current_user()->user_email; ?>';

    if (!userEmail) {
        alert('<?php _e('Please log in to purchase credits.', 'businesscraft-ai'); ?>');
        $purchaseBtn.text('<?php _e('Purchase', 'businesscraft-ai'); ?>').prop('disabled', false);
        return;
    }

    // Make AJAX request to initiate Paystack payment
    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'businesscraft_ai_initiate_payment',
            nonce: chatgabi_ajax.nonce,
            package: packageKey,
            email: userEmail
        },
        success: function(response) {
            if (response.success && response.data.authorization_url) {
                // Redirect to Paystack payment page
                window.location.href = response.data.authorization_url;
            } else {
                alert('<?php _e('Payment initialization failed:', 'businesscraft-ai'); ?> ' + (response.data || '<?php _e('Unknown error', 'businesscraft-ai'); ?>'));
                $purchaseBtn.text('<?php _e('Purchase', 'businesscraft-ai'); ?>').prop('disabled', false);
            }
        },
        error: function() {
            alert('<?php _e('Network error occurred. Please try again.', 'businesscraft-ai'); ?>');
            $purchaseBtn.text('<?php _e('Purchase', 'businesscraft-ai'); ?>').prop('disabled', false);
        }
    });
}

// Close modal when clicking outside
$(document).on('click', '#credit-purchase-modal', function(e) {
    if (e.target === this) {
        closeCreditPurchaseModal();
    }
});

// Close modal button
$(document).on('click', '#close-credit-modal', function() {
    closeCreditPurchaseModal();
});

// Purchase button click handler (legacy - now handled by initiatePurchase function)
// This is kept for backward compatibility but the new system uses initiatePurchase()

// Feedback Tab Functions
function loadFeedbackHistory() {
    const $historyContainer = $('#feedback-history');

    // Show loading state
    $historyContainer.html(`
        <div class="loading-feedback">
            <div class="loading-spinner"></div>
            <p>Loading your feedback history...</p>
        </div>
    `);

    // Make AJAX request to get feedback history
    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'chatgabi_get_user_feedback_history',
            nonce: chatgabi_ajax.nonce
        },
        success: function(response) {
            if (response.success && response.data.feedback) {
                displayFeedbackHistory(response.data.feedback);
                updateFeedbackStats(response.data.stats);
            } else {
                $historyContainer.html(`
                    <div class="no-feedback">
                        <div class="no-feedback-icon">💬</div>
                        <h4>No feedback yet</h4>
                        <p>You haven't provided any feedback yet. Start using BusinessCraft AI and share your experience!</p>
                    </div>
                `);
            }
        },
        error: function() {
            $historyContainer.html(`
                <div class="feedback-error">
                    <p>Failed to load feedback history. Please try again later.</p>
                </div>
            `);
        }
    });
}

function displayFeedbackHistory(feedbackItems) {
    const $historyContainer = $('#feedback-history');

    if (feedbackItems.length === 0) {
        $historyContainer.html(`
            <div class="no-feedback">
                <div class="no-feedback-icon">💬</div>
                <h4>No feedback yet</h4>
                <p>You haven't provided any feedback yet. Start using BusinessCraft AI and share your experience!</p>
            </div>
        `);
        return;
    }

    let historyHTML = '<div class="feedback-history-list">';

    feedbackItems.forEach(item => {
        const stars = '★'.repeat(item.rating_score || 0) + '☆'.repeat(5 - (item.rating_score || 0));
        const date = new Date(item.created_at).toLocaleDateString();

        historyHTML += `
            <div class="feedback-history-item">
                <div class="feedback-item-header">
                    <div class="feedback-item-rating">
                        ${stars.split('').map(star => `<span class="star">${star}</span>`).join('')}
                    </div>
                    <div class="feedback-item-date">${date}</div>
                </div>
                <div class="feedback-item-content">
                    ${item.feedback_text || 'No text feedback provided'}
                </div>
                <div class="feedback-item-meta">
                    <span class="feedback-item-context">${item.context || 'General'}</span>
                    <span>Helpfulness: ${item.helpfulness_rating || 'N/A'}/5</span>
                    <span>Accuracy: ${item.accuracy_rating || 'N/A'}/5</span>
                </div>
            </div>
        `;
    });

    historyHTML += '</div>';
    $historyContainer.html(historyHTML);
}

function updateFeedbackStats(stats) {
    $('#total-feedback-count').text(stats.total_feedback || 0);
    $('#average-rating').text(stats.average_rating ? stats.average_rating.toFixed(1) : '0.0');
    $('#helpful-responses').text(stats.helpful_responses || 0);
    $('#improvement-suggestions').text(stats.improvement_suggestions || 0);
}

// Initialize feedback tab when it's activated
$(document).on('click', '.tab-button[data-tab="feedback"]', function() {
    // Load feedback history when feedback tab is first opened
    if ($('#feedback-history .loading-feedback').length > 0) {
        loadFeedbackHistory();
    }
});

// Initialize preferences tab when it's activated
$(document).on('click', '.tab-button[data-tab="preferences"]', function() {
    // Load user preferences when preferences tab is first opened
    loadUserPreferences();
});

// Load user preferences
function loadUserPreferences() {
    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'chatgabi_get_user_preferences',
            nonce: chatgabi_ajax.nonce
        },
        success: function(response) {
            if (response.success && response.data.preferences) {
                populatePreferencesForm(response.data.preferences);
            }
        },
        error: function() {
            console.log('Failed to load user preferences');
        }
    });
}

// Populate preferences form with user data
function populatePreferencesForm(preferences) {
    // Export preferences
    $('#default-export-format').val(preferences.default_export_format || 'pdf');
    $('#auto-export').prop('checked', preferences.auto_export || false);
    $('#email-exports').prop('checked', preferences.email_exports || false);

    // Credit preferences
    $('#low-credit-notifications').prop('checked', preferences.low_credit_notifications !== false);
    $('#low-credit-threshold').val(preferences.low_credit_threshold || 50);
    $('#purchase-confirmations').prop('checked', preferences.purchase_confirmations !== false);

    // AI preferences
    $('#ai-response-style').val(preferences.ai_response_style || 'professional');
    $('#include-examples').prop('checked', preferences.include_examples !== false);
    $('#auto-include-opportunities').prop('checked', preferences.auto_include_opportunities !== false);

    // Notification preferences
    $('#email-notifications').prop('checked', preferences.email_notifications !== false);
    $('#opportunity-notifications').prop('checked', preferences.opportunity_notifications !== false);
    $('#weekly-analytics').prop('checked', preferences.weekly_analytics_email || false);
}

// Save user preferences
function savePreferences() {
    const preferences = {
        // Export preferences
        default_export_format: $('#default-export-format').val(),
        auto_export: $('#auto-export').is(':checked'),
        email_exports: $('#email-exports').is(':checked'),

        // Credit preferences
        low_credit_notifications: $('#low-credit-notifications').is(':checked'),
        low_credit_threshold: parseInt($('#low-credit-threshold').val()),
        purchase_confirmations: $('#purchase-confirmations').is(':checked'),

        // AI preferences
        ai_response_style: $('#ai-response-style').val(),
        include_examples: $('#include-examples').is(':checked'),
        auto_include_opportunities: $('#auto-include-opportunities').is(':checked'),

        // Notification preferences
        email_notifications: $('#email-notifications').is(':checked'),
        opportunity_notifications: $('#opportunity-notifications').is(':checked'),
        weekly_analytics_email: $('#weekly-analytics').is(':checked')
    };

    // Show saving state
    const $saveBtn = $('.preferences-actions .btn-primary');
    const originalText = $saveBtn.text();
    $saveBtn.text('<?php _e('Saving...', 'businesscraft-ai'); ?>').prop('disabled', true);

    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'chatgabi_save_user_preferences',
            nonce: chatgabi_ajax.nonce,
            preferences: preferences
        },
        success: function(response) {
            if (response.success) {
                $saveBtn.text('<?php _e('Saved!', 'businesscraft-ai'); ?>');
                setTimeout(() => {
                    $saveBtn.text(originalText).prop('disabled', false);
                }, 2000);
            } else {
                alert('<?php _e('Failed to save preferences:', 'businesscraft-ai'); ?> ' + (response.data || '<?php _e('Unknown error', 'businesscraft-ai'); ?>'));
                $saveBtn.text(originalText).prop('disabled', false);
            }
        },
        error: function() {
            alert('<?php _e('Network error occurred while saving preferences.', 'businesscraft-ai'); ?>');
            $saveBtn.text(originalText).prop('disabled', false);
        }
    });
}

// Reset preferences to defaults
function resetPreferences() {
    if (!confirm('<?php _e('Are you sure you want to reset all preferences to their default values?', 'businesscraft-ai'); ?>')) {
        return;
    }

    $.ajax({
        url: chatgabi_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'chatgabi_reset_user_preferences',
            nonce: chatgabi_ajax.nonce
        },
        success: function(response) {
            if (response.success) {
                // Reload preferences
                loadUserPreferences();
                alert('<?php _e('Preferences have been reset to defaults.', 'businesscraft-ai'); ?>');
            } else {
                alert('<?php _e('Failed to reset preferences:', 'businesscraft-ai'); ?> ' + (response.data || '<?php _e('Unknown error', 'businesscraft-ai'); ?>'));
            }
        },
        error: function() {
            alert('<?php _e('Network error occurred while resetting preferences.', 'businesscraft-ai'); ?>');
        }
    });
}
</script>

<?php get_footer(); ?>

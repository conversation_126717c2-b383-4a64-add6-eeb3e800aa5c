<?php
/**
 * Diagnose Payment System
 * 
 * Comprehensive diagnostic tool for the credit purchase system
 */

// Include WordPress
require_once 'wp-config.php';

// Set up WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment System Diagnostics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .diagnostic-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; font-weight: 600; }
        .error { color: #dc3545; font-weight: 600; }
        .warning { color: #ffc107; font-weight: 600; }
        .info { color: #17a2b8; font-weight: 600; }
        .diagnostic-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .code {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Payment System Diagnostics</h1>
            <p>Comprehensive diagnostic check for the credit purchase system</p>
            <p><strong>Diagnostic Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- Function Availability Check -->
        <div class="diagnostic-section">
            <h2>🔧 Function Availability Check</h2>
            
            <?php
            $required_functions = [
                'businesscraft_ai_initiate_payment' => 'AJAX payment initiation',
                'businesscraft_ai_rest_initiate_payment' => 'REST payment initiation',
                'businesscraft_ai_initiate_paystack_payment' => 'Paystack integration',
                'businesscraft_ai_get_credit_packages' => 'Package loading',
                'businesscraft_ai_verify_paystack_payment' => 'Payment verification',
                'businesscraft_ai_log_credit_transaction' => 'Transaction logging'
            ];
            
            foreach ($required_functions as $function => $description) {
                echo '<div class="diagnostic-item">';
                if (function_exists($function)) {
                    echo '<p class="success">✅ <span class="code">' . $function . '()</span> - ' . $description . '</p>';
                } else {
                    echo '<p class="error">❌ <span class="code">' . $function . '()</span> - ' . $description . ' (MISSING)</p>';
                }
                echo '</div>';
            }
            ?>
        </div>

        <!-- Configuration Check -->
        <div class="diagnostic-section">
            <h2>⚙️ Configuration Check</h2>
            
            <?php
            echo '<div class="diagnostic-item">';
            
            // Check Paystack configuration
            $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ? 
                         BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY : 
                         get_option('businesscraft_ai_paystack_secret_key');
            
            if (!empty($secret_key)) {
                echo '<p class="success">✅ Paystack secret key configured</p>';
            } else {
                echo '<p class="error">❌ Paystack secret key not configured</p>';
            }
            
            $public_key = defined('BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY') ? 
                         BUSINESSCRAFT_AI_PAYSTACK_PUBLIC_KEY : 
                         get_option('businesscraft_ai_paystack_public_key');
            
            if (!empty($public_key)) {
                echo '<p class="success">✅ Paystack public key configured</p>';
            } else {
                echo '<p class="error">❌ Paystack public key not configured</p>';
            }
            
            // Check WordPress environment
            if (defined('WP_DEBUG') && WP_DEBUG) {
                echo '<p class="info">ℹ️ WordPress debug mode enabled</p>';
            } else {
                echo '<p class="warning">⚠️ WordPress debug mode disabled</p>';
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Database Check -->
        <div class="diagnostic-section">
            <h2>🗄️ Database Check</h2>
            
            <?php
            global $wpdb;
            
            echo '<div class="diagnostic-item">';
            
            // Check credit transactions table
            $transactions_table = $wpdb->prefix . 'businesscraft_ai_credit_transactions';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$transactions_table'") === $transactions_table;
            
            if ($table_exists) {
                echo '<p class="success">✅ Credit transactions table exists</p>';
                
                // Check table structure
                $columns = $wpdb->get_results("SHOW COLUMNS FROM $transactions_table");
                $required_columns = ['id', 'user_id', 'credits', 'transaction_type', 'reference', 'status'];
                $existing_columns = array_column($columns, 'Field');
                
                foreach ($required_columns as $column) {
                    if (in_array($column, $existing_columns)) {
                        echo '<p class="success">✅ Column <span class="code">' . $column . '</span> exists</p>';
                    } else {
                        echo '<p class="error">❌ Column <span class="code">' . $column . '</span> missing</p>';
                    }
                }
            } else {
                echo '<p class="warning">⚠️ Credit transactions table will be created on first use</p>';
            }
            
            // Check exports table
            $exports_table = $wpdb->prefix . 'businesscraft_ai_exports';
            $exports_exists = $wpdb->get_var("SHOW TABLES LIKE '$exports_table'") === $exports_table;
            
            if ($exports_exists) {
                echo '<p class="success">✅ Exports table exists</p>';
            } else {
                echo '<p class="warning">⚠️ Exports table will be created on first use</p>';
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- AJAX Actions Check -->
        <div class="diagnostic-section">
            <h2>🔗 AJAX Actions Check</h2>
            
            <?php
            global $wp_filter;
            
            echo '<div class="diagnostic-item">';
            
            $ajax_actions = [
                'wp_ajax_businesscraft_ai_initiate_payment' => 'Credit purchase initiation',
                'wp_ajax_businesscraft_ai_get_credit_packages' => 'Package loading',
                'wp_ajax_businesscraft_ai_verify_payment' => 'Payment verification',
                'wp_ajax_businesscraft_ai_get_export_history' => 'Export history',
                'wp_ajax_businesscraft_ai_create_export' => 'Export creation'
            ];
            
            foreach ($ajax_actions as $action => $description) {
                if (isset($wp_filter[$action])) {
                    echo '<p class="success">✅ <span class="code">' . $action . '</span> - ' . $description . '</p>';
                } else {
                    echo '<p class="error">❌ <span class="code">' . $action . '</span> - ' . $description . ' (NOT REGISTERED)</p>';
                }
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- REST Routes Check -->
        <div class="diagnostic-section">
            <h2>🌐 REST Routes Check</h2>
            
            <?php
            echo '<div class="diagnostic-item">';
            
            // Get REST routes
            $rest_server = rest_get_server();
            $routes = $rest_server->get_routes();
            
            $expected_routes = [
                '/bcai/v1/initiate-payment' => 'Payment initiation',
                '/bcai/v1/pricing' => 'Localized pricing',
                '/bcai/v1/test' => 'Test endpoint',
                '/chatgabi/v1/templates' => 'Template management'
            ];
            
            foreach ($expected_routes as $route => $description) {
                if (isset($routes[$route])) {
                    echo '<p class="success">✅ <span class="code">' . $route . '</span> - ' . $description . '</p>';
                } else {
                    echo '<p class="error">❌ <span class="code">' . $route . '</span> - ' . $description . ' (NOT REGISTERED)</p>';
                }
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- File Existence Check -->
        <div class="diagnostic-section">
            <h2>📁 File Existence Check</h2>
            
            <?php
            echo '<div class="diagnostic-item">';
            
            $required_files = [
                'inc/rest-api.php' => 'REST API endpoints',
                'inc/credit-purchase-handlers.php' => 'Credit purchase handlers',
                'inc/paystack-integration.php' => 'Paystack integration',
                'inc/export-system.php' => 'Export system',
                'assets/js/payments.js' => 'Frontend payment script',
                'page-dashboard.php' => 'Dashboard page'
            ];
            
            $theme_dir = get_template_directory();
            
            foreach ($required_files as $file => $description) {
                $file_path = $theme_dir . '/' . $file;
                if (file_exists($file_path)) {
                    echo '<p class="success">✅ <span class="code">' . $file . '</span> - ' . $description . '</p>';
                } else {
                    echo '<p class="error">❌ <span class="code">' . $file . '</span> - ' . $description . ' (MISSING)</p>';
                }
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Summary -->
        <div class="diagnostic-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h2 style="color: white;">📊 Diagnostic Summary</h2>
            
            <?php
            // Count issues
            $total_checks = 0;
            $passed_checks = 0;
            $issues = [];
            
            // This is a simplified summary - in a real implementation, 
            // you'd collect the results from each section
            
            echo '<p><strong>System Status:</strong> ';
            if (function_exists('businesscraft_ai_initiate_payment') && 
                function_exists('businesscraft_ai_rest_initiate_payment') &&
                !empty($secret_key)) {
                echo '<span style="color: #90EE90;">✅ Core functions operational</span>';
            } else {
                echo '<span style="color: #FFB6C1;">❌ Critical issues detected</span>';
            }
            echo '</p>';
            
            echo '<h3>🔄 Recommended Actions:</h3>';
            echo '<ul>';
            
            if (empty($secret_key)) {
                echo '<li>Configure Paystack API keys</li>';
            }
            
            if (!function_exists('businesscraft_ai_initiate_paystack_payment')) {
                echo '<li>Check Paystack integration file inclusion</li>';
            }
            
            echo '<li>Test payment flow with valid user authentication</li>';
            echo '<li>Monitor error logs for detailed debugging information</li>';
            echo '<li>Verify all AJAX and REST endpoints are accessible</li>';
            echo '</ul>';
            ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Payment System Diagnostics Loaded');
            
            // Add click handlers for expandable sections
            const sections = document.querySelectorAll('.diagnostic-section h2');
            sections.forEach(section => {
                section.style.cursor = 'pointer';
                section.addEventListener('click', function() {
                    const content = this.nextElementSibling;
                    content.style.display = content.style.display === 'none' ? 'block' : 'none';
                });
            });
        });
    </script>
</body>
</html>

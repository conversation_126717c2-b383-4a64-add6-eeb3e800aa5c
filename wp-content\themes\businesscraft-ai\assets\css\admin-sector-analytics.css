/**
 * Admin Sector Analytics Dashboard Styles
 */

/* Summary Cards */
.sector-analytics-summary {
    margin-bottom: 30px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.summary-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #50575e;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-card .stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #2271b1;
    line-height: 1;
}

/* Charts Section */
.sector-analytics-charts {
    margin-bottom: 40px;
}

.chart-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    position: relative;
    min-height: 400px;
}

.chart-container h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 1px solid #dcdcde;
    padding-bottom: 10px;
}

.chart-container canvas {
    max-height: 350px;
}

.chart-container.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.chart-container.loading::after {
    content: 'Loading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 11;
    font-weight: 600;
    color: #50575e;
}

.chart-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #d63638;
    font-weight: 600;
    text-align: center;
}

/* Table Section */
.sector-analytics-table {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
}

.sector-analytics-table h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

/* Table Filters */
.table-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
    align-items: center;
    flex-wrap: wrap;
}

.table-filters select {
    padding: 6px 8px;
    border: 1px solid #8c8f94;
    border-radius: 3px;
    background: #fff;
    min-width: 150px;
}

.table-filters .button {
    margin-left: auto;
}

/* Table Styles */
#sector-logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

#sector-logs-table th,
#sector-logs-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
}

#sector-logs-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #50575e;
    position: sticky;
    top: 0;
}

#sector-logs-table tbody tr:hover {
    background: #f6f7f7;
}

#sector-logs-table td:first-child {
    font-family: monospace;
    font-size: 12px;
}

#sector-logs-table td:nth-child(4) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#sector-logs-table td:nth-child(7) {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-style: italic;
    color: #646970;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-top: 20px;
}

.pagination a,
.pagination .current-page {
    padding: 8px 12px;
    border: 1px solid #c3c4c7;
    background: #fff;
    color: #2271b1;
    text-decoration: none;
    border-radius: 3px;
    font-size: 14px;
}

.pagination a:hover {
    background: #f6f7f7;
    border-color: #8c8f94;
}

.pagination .current-page {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
    font-weight: 600;
}

/* Success/Failure Indicators */
.success-indicator {
    color: #00a32a;
    font-weight: 600;
}

.failure-indicator {
    color: #d63638;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .chart-row {
        grid-template-columns: 1fr;
    }
    
    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .table-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-filters select {
        min-width: auto;
        width: 100%;
    }
    
    .table-filters .button {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .summary-cards {
        grid-template-columns: 1fr 1fr;
    }
    
    .summary-card .stat-number {
        font-size: 24px;
    }
    
    /* Make table horizontally scrollable on mobile */
    .sector-analytics-table {
        overflow-x: auto;
    }
    
    #sector-logs-table {
        min-width: 800px;
    }
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        padding: 15px;
        min-height: 300px;
    }
    
    .chart-container h3 {
        font-size: 14px;
    }
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2271b1;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.table-filters select:focus,
.pagination a:focus {
    outline: 2px solid #2271b1;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .table-filters,
    .pagination {
        display: none;
    }
    
    .chart-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .summary-cards {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

<?php
/**
 * Comprehensive test for all country datasets
 */

// Define WordPress constants for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/../../../');
}
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Include the data loader functions
require_once(__DIR__ . '/inc/data-loader.php');

echo "=== BusinessCraft AI - Complete Dataset Test ===\n\n";

$countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
$test_results = [];

// Test 1: Dataset Loading
echo "🔍 TEST 1: Dataset Loading\n";
echo str_repeat('=', 60) . "\n";

foreach ($countries as $country) {
    echo "\nTesting: {$country}\n";
    echo str_repeat('-', 30) . "\n";
    
    $data = load_business_dataset_by_country($country);
    
    if ($data === false) {
        echo "❌ Failed to load dataset\n";
        $test_results[$country] = ['status' => 'failed', 'sectors' => 0];
        continue;
    }
    
    // Validate structure
    if (!isset($data['country']) || !isset($data['sectors']) || !is_array($data['sectors'])) {
        echo "❌ Invalid dataset structure\n";
        $test_results[$country] = ['status' => 'invalid_structure', 'sectors' => 0];
        continue;
    }
    
    $sector_count = count($data['sectors']);
    echo "✅ Successfully loaded\n";
    echo "📊 Country: {$data['country']}\n";
    echo "📈 Sectors: {$sector_count}\n";
    
    // Validate first sector structure
    if (!empty($data['sectors'])) {
        $first_sector = $data['sectors'][0];
        $required_fields = ['sector_name', 'overview', 'key_conditions'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (!isset($first_sector[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (empty($missing_fields)) {
            echo "✅ Sector structure valid\n";
        } else {
            echo "⚠️ Missing fields: " . implode(', ', $missing_fields) . "\n";
        }
    }
    
    $test_results[$country] = [
        'status' => 'success',
        'sectors' => $sector_count,
        'country_name' => $data['country']
    ];
}

// Test 2: Sector Extraction
echo "\n\n🎯 TEST 2: Sector Extraction\n";
echo str_repeat('=', 60) . "\n";

$test_sectors = [
    'Ghana' => ['Digital Content', 'Gaming', 'NFTs', 'Creator'],
    'Kenya' => ['Agriculture', 'Technology', 'Finance', 'Tourism'],
    'Nigeria' => ['Agriculture', 'Technology', 'Oil', 'Finance'],
    'South Africa' => ['Mining', 'Agriculture', 'Technology', 'Finance']
];

foreach ($countries as $country) {
    if ($test_results[$country]['status'] !== 'success') {
        continue;
    }
    
    echo "\nTesting sector extraction for: {$country}\n";
    echo str_repeat('-', 40) . "\n";
    
    $sectors_to_test = $test_sectors[$country];
    $found_sectors = 0;
    
    foreach ($sectors_to_test as $sector) {
        $sector_data = get_sector_context_by_country($country, $sector);
        
        if ($sector_data !== null) {
            echo "✅ {$sector} → Found: {$sector_data['sector_name']}\n";
            $found_sectors++;
        } else {
            echo "❌ {$sector} → Not found\n";
        }
    }
    
    $test_results[$country]['sector_extraction'] = $found_sectors . '/' . count($sectors_to_test);
}

// Test 3: Available Sectors Listing
echo "\n\n📋 TEST 3: Available Sectors Listing\n";
echo str_repeat('=', 60) . "\n";

foreach ($countries as $country) {
    if ($test_results[$country]['status'] !== 'success') {
        continue;
    }
    
    echo "\n{$country} - Available Sectors:\n";
    echo str_repeat('-', 30) . "\n";
    
    $available_sectors = get_available_sectors_by_country($country);
    
    if ($available_sectors === false) {
        echo "❌ Failed to get available sectors\n";
        continue;
    }
    
    echo "📈 Total: " . count($available_sectors) . " sectors\n";
    
    // Show first 10 sectors
    $display_count = min(10, count($available_sectors));
    for ($i = 0; $i < $display_count; $i++) {
        echo "   " . ($i + 1) . ". {$available_sectors[$i]}\n";
    }
    
    if (count($available_sectors) > 10) {
        echo "   ... and " . (count($available_sectors) - 10) . " more\n";
    }
}

// Test 4: Helper Functions
echo "\n\n🛠️ TEST 4: Helper Functions\n";
echo str_repeat('=', 60) . "\n";

echo "\nTesting get_available_dataset_countries():\n";
$available_countries = get_available_dataset_countries();
echo "Available countries: " . implode(', ', $available_countries) . "\n";

echo "\nTesting is_dataset_country_available():\n";
foreach (['Ghana', 'Kenya', 'Nigeria', 'South Africa', 'InvalidCountry'] as $test_country) {
    $is_available = is_dataset_country_available($test_country);
    $status = $is_available ? '✅' : '❌';
    echo "{$status} {$test_country}: " . ($is_available ? 'Available' : 'Not Available') . "\n";
}

// Final Summary
echo "\n\n📊 FINAL SUMMARY\n";
echo str_repeat('=', 60) . "\n";

$total_countries = count($countries);
$working_countries = 0;
$total_sectors = 0;

foreach ($countries as $country) {
    $result = $test_results[$country];
    $status_icon = $result['status'] === 'success' ? '✅' : '❌';
    
    echo "\n{$status_icon} {$country}:\n";
    echo "   Status: " . ucfirst($result['status']) . "\n";
    echo "   Sectors: {$result['sectors']}\n";
    
    if (isset($result['sector_extraction'])) {
        echo "   Sector Extraction: {$result['sector_extraction']}\n";
    }
    
    if ($result['status'] === 'success') {
        $working_countries++;
        $total_sectors += $result['sectors'];
    }
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "🎯 OVERALL RESULTS:\n";
echo "   Working Countries: {$working_countries}/{$total_countries}\n";
echo "   Total Sectors Available: {$total_sectors}\n";
echo "   System Status: " . ($working_countries === $total_countries ? "✅ FULLY OPERATIONAL" : "⚠️ PARTIALLY OPERATIONAL") . "\n";

if ($working_countries === $total_countries) {
    echo "\n🎉 SUCCESS! All datasets are properly configured and ready for production use!\n";
} else {
    echo "\n⚠️ Some datasets need attention before full deployment.\n";
}

echo "\n=== Test Complete ===\n";

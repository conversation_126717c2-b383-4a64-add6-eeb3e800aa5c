/*! This file is auto-generated */
(()=>{"use strict";var t={d:(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{privateApis:()=>rt});var r=Object.create;function n(){var t=r(null);return t.__=void 0,delete t.__,t}var a=function(t,e,r){this.path=t,this.matcher=e,this.delegate=r};a.prototype.to=function(t,e){var r=this.delegate;if(r&&r.willAddRoute&&(t=r.willAddRoute(this.matcher.target,t)),this.matcher.add(this.path,t),e){if(0===e.length)throw new Error("You must have an argument in the function passed to `to`");this.matcher.addChild(this.path,t,e,this.delegate)}};var o=function(t){this.routes=n(),this.children=n(),this.target=t};function i(t,e,r){return function(n,o){var s=t+n;if(!o)return new a(s,e,r);o(i(s,e,r))}}function s(t,e,r){for(var n=0,a=0;a<t.length;a++)n+=t[a].path.length;var o={path:e=e.substr(n),handler:r};t.push(o)}function u(t,e,r,n){for(var a=e.routes,o=Object.keys(a),i=0;i<o.length;i++){var c=o[i],h=t.slice();s(h,c,a[c]);var l=e.children[c];l?u(h,l,r,n):r.call(n,h)}}o.prototype.add=function(t,e){this.routes[t]=e},o.prototype.addChild=function(t,e,r,n){var a=new o(e);this.children[t]=a;var s=i(t,a,n);n&&n.contextEntered&&n.contextEntered(e,s),r(s)};function c(t){return t.split("/").map(l).join("/")}var h=/%|\//g;function l(t){return t.length<3||-1===t.indexOf("%")?t:decodeURIComponent(t).replace(h,encodeURIComponent)}var p=/%(?:2(?:4|6|B|C)|3(?:B|D|A)|40)/g;function f(t){return encodeURIComponent(t).replace(p,decodeURIComponent)}var d=/(\/|\.|\*|\+|\?|\||\(|\)|\[|\]|\{|\}|\\)/g,v=Array.isArray,g=Object.prototype.hasOwnProperty;function m(t,e){if("object"!=typeof t||null===t)throw new Error("You must pass an object as the second argument to `generate`.");if(!g.call(t,e))throw new Error("You must provide param `"+e+"` to `generate`.");var r=t[e],n="string"==typeof r?r:""+r;if(0===n.length)throw new Error("You must provide a param `"+e+"`.");return n}var y=[];y[0]=function(t,e){for(var r=e,n=t.value,a=0;a<n.length;a++){var o=n.charCodeAt(a);r=r.put(o,!1,!1)}return r},y[1]=function(t,e){return e.put(47,!0,!0)},y[2]=function(t,e){return e.put(-1,!1,!0)},y[4]=function(t,e){return e};var w=[];w[0]=function(t){return t.value.replace(d,"\\$1")},w[1]=function(){return"([^/]+)"},w[2]=function(){return"(.+)"},w[4]=function(){return""};var b=[];b[0]=function(t){return t.value},b[1]=function(t,e){var r=m(e,t.value);return D.ENCODE_AND_DECODE_PATH_SEGMENTS?f(r):r},b[2]=function(t,e){return m(e,t.value)},b[4]=function(){return""};var E=Object.freeze({}),S=Object.freeze([]);function A(t,e,r){e.length>0&&47===e.charCodeAt(0)&&(e=e.substr(1));for(var n=e.split("/"),a=void 0,o=void 0,i=0;i<n.length;i++){var s,u=n[i],c=0;12&(s=2<<(c=""===u?4:58===u.charCodeAt(0)?1:42===u.charCodeAt(0)?2:0))&&(u=u.slice(1),(a=a||[]).push(u),(o=o||[]).push(!!(4&s))),14&s&&r[c]++,t.push({type:c,value:l(u)})}return{names:a||S,shouldDecodes:o||S}}function x(t,e,r){return t.char===e&&t.negate===r}var C=function(t,e,r,n,a){this.states=t,this.id=e,this.char=r,this.negate=n,this.nextStates=a?e:null,this.pattern="",this._regex=void 0,this.handlers=void 0,this.types=void 0};function O(t,e){return t.negate?t.char!==e&&-1!==t.char:t.char===e||-1===t.char}function P(t,e){for(var r=[],n=0,a=t.length;n<a;n++){var o=t[n];r=r.concat(o.match(e))}return r}C.prototype.regex=function(){return this._regex||(this._regex=new RegExp(this.pattern)),this._regex},C.prototype.get=function(t,e){var r=this.nextStates;if(null!==r)if(v(r))for(var n=0;n<r.length;n++){var a=this.states[r[n]];if(x(a,t,e))return a}else{var o=this.states[r];if(x(o,t,e))return o}},C.prototype.put=function(t,e,r){var n;if(n=this.get(t,e))return n;var a=this.states;return n=new C(a,a.length,t,e,r),a[a.length]=n,null==this.nextStates?this.nextStates=n.id:v(this.nextStates)?this.nextStates.push(n.id):this.nextStates=[this.nextStates,n.id],n},C.prototype.match=function(t){var e=this.nextStates;if(!e)return[];var r=[];if(v(e))for(var n=0;n<e.length;n++){var a=this.states[e[n]];O(a,t)&&r.push(a)}else{var o=this.states[e];O(o,t)&&r.push(o)}return r};var _=function(t){this.length=0,this.queryParams=t||{}};function R(t){var e;t=t.replace(/\+/gm,"%20");try{e=decodeURIComponent(t)}catch(t){e=""}return e}_.prototype.splice=Array.prototype.splice,_.prototype.slice=Array.prototype.slice,_.prototype.push=Array.prototype.push;var D=function(){this.names=n();var t=[],e=new C(t,0,-1,!0,!1);t[0]=e,this.states=t,this.rootState=e};D.prototype.add=function(t,e){for(var r,n=this.rootState,a="^",o=[0,0,0],i=new Array(t.length),s=[],u=!0,c=0,h=0;h<t.length;h++){for(var l=t[h],p=A(s,l.path,o),f=p.names,d=p.shouldDecodes;c<s.length;c++){var v=s[c];4!==v.type&&(u=!1,n=n.put(47,!1,!1),a+="/",n=y[v.type](v,n),a+=w[v.type](v))}i[h]={handler:l.handler,names:f,shouldDecodes:d}}u&&(n=n.put(47,!1,!1),a+="/"),n.handlers=i,n.pattern=a+"$",n.types=o,"object"==typeof e&&null!==e&&e.as&&(r=e.as),r&&(this.names[r]={segments:s,handlers:i})},D.prototype.handlersFor=function(t){var e=this.names[t];if(!e)throw new Error("There is no route named "+t);for(var r=new Array(e.handlers.length),n=0;n<e.handlers.length;n++){var a=e.handlers[n];r[n]=a}return r},D.prototype.hasRoute=function(t){return!!this.names[t]},D.prototype.generate=function(t,e){var r=this.names[t],n="";if(!r)throw new Error("There is no route named "+t);for(var a=r.segments,o=0;o<a.length;o++){var i=a[o];4!==i.type&&(n+="/",n+=b[i.type](i,e))}return"/"!==n.charAt(0)&&(n="/"+n),e&&e.queryParams&&(n+=this.generateQueryString(e.queryParams)),n},D.prototype.generateQueryString=function(t){var e=[],r=Object.keys(t);r.sort();for(var n=0;n<r.length;n++){var a=r[n],o=t[a];if(null!=o){var i=encodeURIComponent(a);if(v(o))for(var s=0;s<o.length;s++){var u=a+"[]="+encodeURIComponent(o[s]);e.push(u)}else i+="="+encodeURIComponent(o),e.push(i)}}return 0===e.length?"":"?"+e.join("&")},D.prototype.parseQueryString=function(t){for(var e=t.split("&"),r={},n=0;n<e.length;n++){var a=e[n].split("="),o=R(a[0]),i=o.length,s=!1,u=void 0;1===a.length?u="true":(i>2&&"[]"===o.slice(i-2)&&(s=!0,r[o=o.slice(0,i-2)]||(r[o]=[])),u=a[1]?R(a[1]):""),s?r[o].push(u):r[o]=u}return r},D.prototype.recognize=function(t){var e,r=[this.rootState],n={},a=!1,o=t.indexOf("#");-1!==o&&(t=t.substr(0,o));var i=t.indexOf("?");if(-1!==i){var s=t.substr(i+1,t.length);t=t.substr(0,i),n=this.parseQueryString(s)}"/"!==t.charAt(0)&&(t="/"+t);var u=t;D.ENCODE_AND_DECODE_PATH_SEGMENTS?t=c(t):(t=decodeURI(t),u=decodeURI(u));var h=t.length;h>1&&"/"===t.charAt(h-1)&&(t=t.substr(0,h-1),u=u.substr(0,u.length-1),a=!0);for(var l=0;l<t.length&&(r=P(r,t.charCodeAt(l))).length;l++);for(var p=[],f=0;f<r.length;f++)r[f].handlers&&p.push(r[f]);r=function(t){return t.sort((function(t,e){var r=t.types||[0,0,0],n=r[0],a=r[1],o=r[2],i=e.types||[0,0,0],s=i[0],u=i[1],c=i[2];if(o!==c)return o-c;if(o){if(n!==s)return s-n;if(a!==u)return u-a}return a!==u?a-u:n!==s?s-n:0}))}(p);var d=p[0];return d&&d.handlers&&(a&&d.pattern&&"(.+)$"===d.pattern.slice(-5)&&(u+="/"),e=function(t,e,r){var n=t.handlers,a=t.regex();if(!a||!n)throw new Error("state not initialized");var o=e.match(a),i=1,s=new _(r);s.length=n.length;for(var u=0;u<n.length;u++){var c=n[u],h=c.names,l=c.shouldDecodes,p=E,f=!1;if(h!==S&&l!==S)for(var d=0;d<h.length;d++){f=!0;var v=h[d],g=o&&o[i++];p===E&&(p={}),D.ENCODE_AND_DECODE_PATH_SEGMENTS&&l[d]?p[v]=g&&decodeURIComponent(g):p[v]=g}s[u]={handler:c.handler,params:p,isDynamic:f}}return s}(d,u,n)),e},D.VERSION="0.3.4",D.ENCODE_AND_DECODE_PATH_SEGMENTS=!0,D.Normalizer={normalizeSegment:l,normalizePath:c,encodePathSegment:f},D.prototype.map=function(t,e){var r=new o;t(i("",r,this.delegate)),u([],r,(function(t){e?e(this,t):this.add(t)}),this)};const j=D;function k(){return k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},k.apply(null,arguments)}var N;!function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"}(N||(N={}));var I=function(t){return t};var M="beforeunload",T="popstate";function q(t){t.preventDefault(),t.returnValue=""}function U(){var t=[];return{get length(){return t.length},push:function(e){return t.push(e),function(){t=t.filter((function(t){return t!==e}))}},call:function(e){t.forEach((function(t){return t&&t(e)}))}}}function L(){return Math.random().toString(36).substr(2,8)}function Q(t){var e=t.pathname,r=void 0===e?"/":e,n=t.search,a=void 0===n?"":n,o=t.hash,i=void 0===o?"":o;return a&&"?"!==a&&(r+="?"===a.charAt(0)?a:"?"+a),i&&"#"!==i&&(r+="#"===i.charAt(0)?i:"#"+i),r}function z(t){var e={};if(t){var r=t.indexOf("#");r>=0&&(e.hash=t.substr(r),t=t.substr(0,r));var n=t.indexOf("?");n>=0&&(e.search=t.substr(n),t=t.substr(0,n)),t&&(e.pathname=t)}return e}const H=window.wp.element,V=window.wp.url,$=window.wp.compose,G=window.ReactJSXRuntime,Y=function(t){void 0===t&&(t={});var e=t.window,r=void 0===e?document.defaultView:e,n=r.history;function a(){var t=r.location,e=t.pathname,a=t.search,o=t.hash,i=n.state||{};return[i.idx,I({pathname:e,search:a,hash:o,state:i.usr||null,key:i.key||"default"})]}var o=null;r.addEventListener(T,(function(){if(o)l.call(o),o=null;else{var t=N.Pop,e=a(),r=e[0],n=e[1];if(l.length){if(null!=r){var i=u-r;i&&(o={action:t,location:n,retry:function(){m(-1*i)}},m(i))}}else g(t)}}));var i=N.Pop,s=a(),u=s[0],c=s[1],h=U(),l=U();function p(t){return"string"==typeof t?t:Q(t)}function f(t,e){return void 0===e&&(e=null),I(k({pathname:c.pathname,hash:"",search:""},"string"==typeof t?z(t):t,{state:e,key:L()}))}function d(t,e){return[{usr:t.state,key:t.key,idx:e},p(t)]}function v(t,e,r){return!l.length||(l.call({action:t,location:e,retry:r}),!1)}function g(t){i=t;var e=a();u=e[0],c=e[1],h.call({action:i,location:c})}function m(t){n.go(t)}return null==u&&(u=0,n.replaceState(k({},n.state,{idx:u}),"")),{get action(){return i},get location(){return c},createHref:p,push:function t(e,a){var o=N.Push,i=f(e,a);if(v(o,i,(function(){t(e,a)}))){var s=d(i,u+1),c=s[0],h=s[1];try{n.pushState(c,"",h)}catch(t){r.location.assign(h)}g(o)}},replace:function t(e,r){var a=N.Replace,o=f(e,r);if(v(a,o,(function(){t(e,r)}))){var i=d(o,u),s=i[0],c=i[1];n.replaceState(s,"",c),g(a)}},go:m,back:function(){m(-1)},forward:function(){m(1)},listen:function(t){return h.push(t)},block:function(t){var e=l.push(t);return 1===l.length&&r.addEventListener(M,q),function(){e(),l.length||r.removeEventListener(M,q)}}}}(),B=(0,H.createContext)(null),F=(0,H.createContext)({pathArg:"p"}),W=new WeakMap;function J(){const t=Y.location;let e=W.get(t);return e||(e={...t,query:Object.fromEntries(new URLSearchParams(t.search))},W.set(t,e)),e}function X(){const{pathArg:t,beforeNavigate:e}=(0,H.useContext)(F),r=(0,$.useEvent)((async(r,n={})=>{var a;const o=(0,V.getQueryArgs)(r),i=null!==(a=(0,V.getPath)("http://domain.com/"+r))&&void 0!==a?a:"",s=()=>{const r=e?e({path:i,query:o}):{path:i,query:o};return Y.push({search:(0,V.buildQueryString)({[t]:r.path,...r.query})},n.state)};window.matchMedia("(min-width: 782px)").matches&&document.startViewTransition&&n.transition?await new Promise((t=>{var e;const r=null!==(e=n.transition)&&void 0!==e?e:"";document.documentElement.classList.add(r);document.startViewTransition((()=>s())).finished.finally((()=>{document.documentElement.classList.remove(r),t()}))})):s()}));return(0,H.useMemo)((()=>({navigate:r,back:Y.back})),[r])}function K(t,e={}){var r;const n=X(),{pathArg:a,beforeNavigate:o}=(0,H.useContext)(F);const i=(0,V.getQueryArgs)(t),s=null!==(r=(0,V.getPath)("http://domain.com/"+t))&&void 0!==r?r:"",u=(0,H.useMemo)((()=>o?o({path:s,query:i}):{path:s,query:i}),[s,i,o]),[c]=window.location.href.split("?");return{href:`${c}?${(0,V.buildQueryString)({[a]:u.path,...u.query})}`,onClick:function(r){r?.preventDefault(),n.navigate(t,e)}}}const Z=window.wp.privateApis,{lock:tt,unlock:et}=(0,Z.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/router"),rt={};tt(rt,{useHistory:X,useLocation:function(){const t=(0,H.useContext)(B);if(!t)throw new Error("useLocation must be used within a RouterProvider");return t},RouterProvider:function({routes:t,pathArg:e,beforeNavigate:r,children:n,matchResolverArgs:a}){const o=function(t,e,r,n){const{query:a={}}=t;return(0,H.useMemo)((()=>{const{[r]:t="/",...o}=a,i=e.recognize(t)?.[0];if(!i)return{name:"404",path:(0,V.addQueryArgs)(t,o),areas:{},widths:{},query:o,params:{}};const s=i.handler,u=(t={})=>Object.fromEntries(Object.entries(t).map((([t,e])=>"function"==typeof e?[t,e({query:o,params:i.params,...n})]:[t,e])));return{name:s.name,areas:u(s.areas),widths:u(s.widths),params:i.params,query:o,path:(0,V.addQueryArgs)(t,o)}}),[e,a,r,n])}((0,H.useSyncExternalStore)(Y.listen,J,J),(0,H.useMemo)((()=>{const e=new j;return t.forEach((t=>{e.add([{path:t.path,handler:t}],{as:t.name})})),e}),[t]),e,a),i=(0,H.useMemo)((()=>({beforeNavigate:r,pathArg:e})),[r,e]);return(0,G.jsx)(F.Provider,{value:i,children:(0,G.jsx)(B.Provider,{value:o,children:n})})},useLink:K,Link:function({to:t,options:e,children:r,...n}){const{href:a,onClick:o}=K(t,e);return(0,G.jsx)("a",{href:a,onClick:o,...n,children:r})}}),(window.wp=window.wp||{}).router=e})();
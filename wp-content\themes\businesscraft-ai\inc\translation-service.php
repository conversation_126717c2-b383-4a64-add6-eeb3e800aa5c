<?php
/**
 * Real-Time Translation Service for ChatGABI WhatsApp Integration
 * 
 * Provides automatic language detection and translation capabilities for
 * African languages (Twi, Yoruba, Swahili, Zulu) to English and vice versa.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize translation service
 */
function chatgabi_init_translation_service() {
    // Create translation tables if needed
    chatgabi_create_translation_tables();
    
    // Schedule cleanup tasks
    if (!wp_next_scheduled('chatgabi_translation_cleanup')) {
        wp_schedule_event(time(), 'daily', 'chatgabi_translation_cleanup');
    }
}
add_action('init', 'chatgabi_init_translation_service');

/**
 * Create translation-specific database tables
 */
function chatgabi_create_translation_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // Translation cache table
    $translation_cache_table = $wpdb->prefix . 'chatgabi_translation_cache';
    $cache_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$translation_cache_table'") === $translation_cache_table;
    
    if (!$cache_table_exists) {
        $translation_cache_sql = "CREATE TABLE $translation_cache_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            source_text_hash varchar(64) NOT NULL,
            source_language varchar(5) NOT NULL,
            target_language varchar(5) NOT NULL,
            source_text text NOT NULL,
            translated_text text NOT NULL,
            translation_service varchar(20) DEFAULT 'google',
            confidence_score decimal(3,2) DEFAULT 0.95,
            usage_count int(11) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_translation (source_text_hash, source_language, target_language),
            KEY idx_source_language (source_language),
            KEY idx_target_language (target_language),
            KEY idx_usage_count (usage_count),
            KEY idx_created_at (created_at)
        ) $charset_collate;";
        
        $wpdb->query($translation_cache_sql);
    }
    
    // Translation analytics table
    $translation_analytics_table = $wpdb->prefix . 'chatgabi_translation_analytics';
    $analytics_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$translation_analytics_table'") === $translation_analytics_table;
    
    if (!$analytics_table_exists) {
        $translation_analytics_sql = "CREATE TABLE $translation_analytics_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            whatsapp_user_id bigint(20),
            phone_number varchar(20),
            source_language varchar(5) NOT NULL,
            target_language varchar(5) NOT NULL,
            character_count int(11) NOT NULL,
            translation_service varchar(20) DEFAULT 'google',
            confidence_score decimal(3,2),
            processing_time_ms int(11),
            api_cost decimal(10,6) DEFAULT 0,
            cache_hit boolean DEFAULT FALSE,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_whatsapp_user_id (whatsapp_user_id),
            KEY idx_phone_number (phone_number),
            KEY idx_source_language (source_language),
            KEY idx_created_at (created_at),
            KEY idx_cache_hit (cache_hit)
        ) $charset_collate;";
        
        $wpdb->query($translation_analytics_sql);
    }
    
    // Update WhatsApp conversations table with translation fields
    $conversations_table = $wpdb->prefix . 'chatgabi_whatsapp_conversations';
    $conversations_exists = $wpdb->get_var("SHOW TABLES LIKE '$conversations_table'") === $conversations_table;
    
    if ($conversations_exists) {
        // Check if translation columns exist
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $conversations_table LIKE 'original_message_language'");
        
        if (empty($columns)) {
            $wpdb->query("ALTER TABLE $conversations_table ADD COLUMN original_message_language varchar(5) DEFAULT NULL");
            $wpdb->query("ALTER TABLE $conversations_table ADD COLUMN translated_message longtext DEFAULT NULL");
            $wpdb->query("ALTER TABLE $conversations_table ADD COLUMN translation_confidence decimal(3,2) DEFAULT NULL");
            $wpdb->query("ALTER TABLE $conversations_table ADD COLUMN translation_service varchar(20) DEFAULT NULL");
            $wpdb->query("ALTER TABLE $conversations_table ADD COLUMN translation_cost decimal(10,6) DEFAULT 0");
            $wpdb->query("ALTER TABLE $conversations_table ADD COLUMN was_translated boolean DEFAULT FALSE");
        }
    }
    
    return true;
}

/**
 * Detect language of incoming message
 */
function chatgabi_detect_message_language($text, $user_country = null) {
    // Quick English detection patterns
    if (chatgabi_is_english_message($text)) {
        return 'en';
    }
    
    // Country-based language hints
    $country_language_hints = array(
        'Ghana' => 'tw',
        'Kenya' => 'sw', 
        'Nigeria' => 'yo',
        'South Africa' => 'zu'
    );
    
    $likely_language = $country_language_hints[$user_country] ?? 'en';
    
    // Use Google Translate API for accurate detection
    $detected_language = chatgabi_google_detect_language($text);
    
    if ($detected_language && chatgabi_is_language_supported($detected_language)) {
        return $detected_language;
    }
    
    // Fallback to country-based hint
    return $likely_language;
}

/**
 * Check if message is in English using pattern matching
 */
function chatgabi_is_english_message($text) {
    // Common English business words
    $english_patterns = array(
        '/\b(business|plan|strategy|market|finance|help|please|thank|hello|hi)\b/i',
        '/\b(what|how|when|where|why|can|could|would|should)\b/i',
        '/\b(the|and|for|are|with|this|that|have|from)\b/i'
    );
    
    $english_matches = 0;
    $total_words = str_word_count($text);
    
    foreach ($english_patterns as $pattern) {
        if (preg_match_all($pattern, $text, $matches)) {
            $english_matches += count($matches[0]);
        }
    }
    
    // If more than 30% of words are English, consider it English
    return $total_words > 0 && ($english_matches / $total_words) > 0.3;
}

/**
 * Smart translation with caching and optimization
 */
function chatgabi_smart_translate($message, $user_country, $user_id = null) {
    $start_time = microtime(true);
    
    // Detect if message is already in English
    if (chatgabi_is_english_message($message)) {
        return array(
            'original_text' => $message,
            'translated_text' => $message,
            'original_language' => 'en',
            'target_language' => 'en',
            'was_translated' => false,
            'confidence' => 1.0,
            'processing_time_ms' => 0,
            'cache_hit' => false,
            'cost' => 0
        );
    }
    
    // Detect language
    $detected_language = chatgabi_detect_message_language($message, $user_country);
    
    // Check cache first
    $cached_translation = chatgabi_get_cached_translation($message, $detected_language, 'en');
    
    if ($cached_translation) {
        // Update cache usage
        chatgabi_update_translation_cache_usage($cached_translation['id']);
        
        $processing_time = (microtime(true) - $start_time) * 1000;
        
        // Log analytics
        chatgabi_log_translation_analytics(
            $user_id,
            null,
            $detected_language,
            'en',
            strlen($message),
            'cache',
            $cached_translation['confidence_score'],
            $processing_time,
            0,
            true
        );
        
        return array(
            'original_text' => $message,
            'translated_text' => $cached_translation['translated_text'],
            'original_language' => $detected_language,
            'target_language' => 'en',
            'was_translated' => true,
            'confidence' => $cached_translation['confidence_score'],
            'processing_time_ms' => round($processing_time, 2),
            'cache_hit' => true,
            'cost' => 0
        );
    }
    
    // Translate using Google Translate API
    $translation_result = chatgabi_google_translate($message, $detected_language, 'en');
    
    if (is_wp_error($translation_result)) {
        error_log('ChatGABI Translation Error: ' . $translation_result->get_error_message());
        
        return array(
            'original_text' => $message,
            'translated_text' => $message,
            'original_language' => $detected_language,
            'target_language' => 'en',
            'was_translated' => false,
            'confidence' => 0,
            'processing_time_ms' => 0,
            'cache_hit' => false,
            'cost' => 0,
            'error' => $translation_result->get_error_message()
        );
    }
    
    $processing_time = (microtime(true) - $start_time) * 1000;
    
    // Cache the translation
    chatgabi_cache_translation(
        $message,
        $detected_language,
        'en',
        $translation_result['translated_text'],
        'google',
        $translation_result['confidence']
    );
    
    // Log analytics
    chatgabi_log_translation_analytics(
        $user_id,
        null,
        $detected_language,
        'en',
        strlen($message),
        'google',
        $translation_result['confidence'],
        $processing_time,
        $translation_result['cost'],
        false
    );
    
    return array(
        'original_text' => $message,
        'translated_text' => $translation_result['translated_text'],
        'original_language' => $detected_language,
        'target_language' => 'en',
        'was_translated' => true,
        'confidence' => $translation_result['confidence'],
        'processing_time_ms' => round($processing_time, 2),
        'cache_hit' => false,
        'cost' => $translation_result['cost']
    );
}

/**
 * Google Translate API language detection
 */
function chatgabi_google_detect_language($text) {
    $api_key = get_option('chatgabi_google_translate_api_key');
    
    if (empty($api_key)) {
        return false;
    }
    
    $url = 'https://translation.googleapis.com/language/translate/v2/detect';
    
    $data = array(
        'key' => $api_key,
        'q' => $text
    );
    
    $response = wp_remote_post($url, array(
        'headers' => array(
            'Content-Type' => 'application/x-www-form-urlencoded',
        ),
        'body' => http_build_query($data),
        'timeout' => 10,
    ));
    
    if (is_wp_error($response)) {
        return false;
    }
    
    $response_body = wp_remote_retrieve_body($response);
    $result = json_decode($response_body, true);
    
    if (isset($result['data']['detections'][0][0]['language'])) {
        return $result['data']['detections'][0][0]['language'];
    }
    
    return false;
}

/**
 * Google Translate API translation
 */
function chatgabi_google_translate($text, $source_lang, $target_lang) {
    $api_key = get_option('chatgabi_google_translate_api_key');
    
    if (empty($api_key)) {
        return new WP_Error(
            'missing_api_key',
            __('Google Translate API key not configured', 'chatgabi')
        );
    }
    
    $url = 'https://translation.googleapis.com/language/translate/v2';
    
    $data = array(
        'key' => $api_key,
        'q' => $text,
        'source' => $source_lang,
        'target' => $target_lang,
        'format' => 'text'
    );
    
    $response = wp_remote_post($url, array(
        'headers' => array(
            'Content-Type' => 'application/x-www-form-urlencoded',
        ),
        'body' => http_build_query($data),
        'timeout' => 15,
    ));
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code !== 200) {
        return new WP_Error(
            'translation_failed',
            "Google Translate API error: HTTP $response_code - $response_body"
        );
    }
    
    $result = json_decode($response_body, true);
    
    if (!isset($result['data']['translations'][0]['translatedText'])) {
        return new WP_Error(
            'invalid_response',
            'Invalid response from Google Translate API'
        );
    }
    
    // Calculate cost (Google Translate pricing: $20 per 1M characters)
    $character_count = strlen($text);
    $cost = ($character_count / 1000000) * 20;
    
    return array(
        'translated_text' => $result['data']['translations'][0]['translatedText'],
        'confidence' => 0.95, // Google Translate generally high confidence
        'cost' => $cost
    );
}

/**
 * Get cached translation
 */
function chatgabi_get_cached_translation($text, $source_lang, $target_lang) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_translation_cache';
    $text_hash = hash('sha256', $text);

    $cached = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE source_text_hash = %s AND source_language = %s AND target_language = %s",
        $text_hash,
        $source_lang,
        $target_lang
    ), ARRAY_A);

    return $cached;
}

/**
 * Cache translation result
 */
function chatgabi_cache_translation($source_text, $source_lang, $target_lang, $translated_text, $service, $confidence) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_translation_cache';
    $text_hash = hash('sha256', $source_text);

    $cache_data = array(
        'source_text_hash' => $text_hash,
        'source_language' => $source_lang,
        'target_language' => $target_lang,
        'source_text' => $source_text,
        'translated_text' => $translated_text,
        'translation_service' => $service,
        'confidence_score' => $confidence,
        'usage_count' => 1,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    );

    return $wpdb->insert($table_name, $cache_data);
}

/**
 * Update translation cache usage count
 */
function chatgabi_update_translation_cache_usage($cache_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_translation_cache';

    return $wpdb->query($wpdb->prepare(
        "UPDATE $table_name SET usage_count = usage_count + 1, updated_at = %s WHERE id = %d",
        current_time('mysql'),
        $cache_id
    ));
}

/**
 * Log translation analytics
 */
function chatgabi_log_translation_analytics($user_id, $phone_number, $source_lang, $target_lang, $char_count, $service, $confidence, $processing_time, $cost, $cache_hit) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_translation_analytics';

    $analytics_data = array(
        'whatsapp_user_id' => $user_id,
        'phone_number' => $phone_number,
        'source_language' => $source_lang,
        'target_language' => $target_lang,
        'character_count' => $char_count,
        'translation_service' => $service,
        'confidence_score' => $confidence,
        'processing_time_ms' => round($processing_time, 2),
        'api_cost' => $cost,
        'cache_hit' => $cache_hit ? 1 : 0,
        'created_at' => current_time('mysql')
    );

    return $wpdb->insert($table_name, $analytics_data);
}

/**
 * Translate response back to user's language
 */
function chatgabi_translate_response($response_text, $target_language) {
    // If target language is English, no translation needed
    if ($target_language === 'en') {
        return array(
            'translated_text' => $response_text,
            'was_translated' => false,
            'cost' => 0
        );
    }

    // Check cache first
    $cached_translation = chatgabi_get_cached_translation($response_text, 'en', $target_language);

    if ($cached_translation) {
        chatgabi_update_translation_cache_usage($cached_translation['id']);

        return array(
            'translated_text' => $cached_translation['translated_text'],
            'was_translated' => true,
            'cache_hit' => true,
            'cost' => 0
        );
    }

    // Translate using Google Translate API
    $translation_result = chatgabi_google_translate($response_text, 'en', $target_language);

    if (is_wp_error($translation_result)) {
        return array(
            'translated_text' => $response_text,
            'was_translated' => false,
            'error' => $translation_result->get_error_message(),
            'cost' => 0
        );
    }

    // Cache the translation
    chatgabi_cache_translation(
        $response_text,
        'en',
        $target_language,
        $translation_result['translated_text'],
        'google',
        $translation_result['confidence']
    );

    return array(
        'translated_text' => $translation_result['translated_text'],
        'was_translated' => true,
        'cache_hit' => false,
        'cost' => $translation_result['cost']
    );
}

/**
 * Get translation analytics for admin dashboard
 */
function chatgabi_get_translation_analytics($days = 30) {
    global $wpdb;

    $analytics_table = $wpdb->prefix . 'chatgabi_translation_analytics';
    $cache_table = $wpdb->prefix . 'chatgabi_translation_cache';

    // Check if tables exist
    $analytics_exists = $wpdb->get_var("SHOW TABLES LIKE '$analytics_table'") === $analytics_table;
    $cache_exists = $wpdb->get_var("SHOW TABLES LIKE '$cache_table'") === $cache_table;

    if (!$analytics_exists || !$cache_exists) {
        return array(
            'total_translations' => 0,
            'cache_hit_rate' => 0,
            'total_cost' => 0,
            'avg_processing_time' => 0,
            'language_breakdown' => array(),
            'daily_volume' => array()
        );
    }

    $date_filter = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL $days DAY)";

    // Basic statistics
    $total_translations = $wpdb->get_var("SELECT COUNT(*) FROM $analytics_table $date_filter");
    $cache_hits = $wpdb->get_var("SELECT COUNT(*) FROM $analytics_table $date_filter AND cache_hit = 1");
    $total_cost = $wpdb->get_var("SELECT SUM(api_cost) FROM $analytics_table $date_filter");
    $avg_processing_time = $wpdb->get_var("SELECT AVG(processing_time_ms) FROM $analytics_table $date_filter");

    $cache_hit_rate = $total_translations > 0 ? ($cache_hits / $total_translations) * 100 : 0;

    // Language breakdown
    $language_breakdown = $wpdb->get_results(
        "SELECT source_language, COUNT(*) as translation_count, AVG(confidence_score) as avg_confidence
         FROM $analytics_table $date_filter
         GROUP BY source_language
         ORDER BY translation_count DESC",
        ARRAY_A
    );

    // Daily translation volume
    $daily_volume = $wpdb->get_results(
        "SELECT DATE(created_at) as date, COUNT(*) as translation_count, SUM(api_cost) as daily_cost
         FROM $analytics_table $date_filter
         GROUP BY DATE(created_at)
         ORDER BY date DESC
         LIMIT 30",
        ARRAY_A
    );

    return array(
        'total_translations' => (int) $total_translations,
        'cache_hit_rate' => round($cache_hit_rate, 1),
        'total_cost' => round((float) $total_cost, 4),
        'avg_processing_time' => round((float) $avg_processing_time, 2),
        'language_breakdown' => $language_breakdown,
        'daily_volume' => $daily_volume
    );
}

/**
 * Clean up old translation cache entries
 */
function chatgabi_cleanup_translation_cache() {
    global $wpdb;

    $cache_table = $wpdb->prefix . 'chatgabi_translation_cache';
    $analytics_table = $wpdb->prefix . 'chatgabi_translation_analytics';

    // Remove cache entries older than 90 days with low usage
    $wpdb->query(
        "DELETE FROM $cache_table
         WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
         AND usage_count < 3"
    );

    // Remove analytics older than 1 year
    $wpdb->query(
        "DELETE FROM $analytics_table
         WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)"
    );

    error_log('ChatGABI: Translation cache cleanup completed');
}
add_action('chatgabi_translation_cleanup', 'chatgabi_cleanup_translation_cache');

/**
 * Get translation service status
 */
function chatgabi_get_translation_service_status() {
    $google_api_key = get_option('chatgabi_google_translate_api_key');

    $status = array(
        'google_translate' => array(
            'configured' => !empty($google_api_key),
            'status' => !empty($google_api_key) ? 'active' : 'not_configured'
        ),
        'cache_enabled' => true,
        'supported_languages' => array('en', 'tw', 'sw', 'yo', 'zu')
    );

    return $status;
}

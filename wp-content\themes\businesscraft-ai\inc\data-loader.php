<?php
/**
 * BusinessCraft AI - Data Loader
 *
 * Handles loading of country-specific business intelligence datasets
 * for contextualizing AI responses with local market data.
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Load business dataset by country
 *
 * Accepts a country parameter and loads the latest versioned JSON file
 * containing business intelligence data for that country.
 *
 * @param string $country Country name (e.g., "Ghana", "Kenya", "Nigeria", "South Africa")
 * @return array|false Decoded JSON data as array, or false on failure
 */
function load_business_dataset_by_country($country) {
    // Validate input
    if (empty($country) || !is_string($country)) {
        error_log('BusinessCraft AI: Invalid country parameter provided to load_business_dataset_by_country()');
        return false;
    }

    // Use transient cache to avoid repeated file loading
    $cache_key = 'chatgabi_dataset_' . sanitize_key(strtolower($country));
    $cached_data = get_transient($cache_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    // Normalize country name and create folder mapping
    $country_folders = [
        'Ghana' => 'ghana-business-data',
        'Kenya' => 'kenya-business-data',
        'Nigeria' => 'nigeria-business-data',
        'South Africa' => 'south-africa-business-data'
    ];

    // Get the folder name for the country
    $folder_name = isset($country_folders[$country]) ? $country_folders[$country] : null;

    if (!$folder_name) {
        error_log("BusinessCraft AI: Unsupported country '{$country}' provided to load_business_dataset_by_country()");
        return false;
    }

    // Build the dataset directory path
    $dataset_dir = WP_CONTENT_DIR . '/datasets/' . $folder_name . '/';

    // Check if directory exists
    if (!is_dir($dataset_dir)) {
        error_log("BusinessCraft AI: Dataset directory not found: {$dataset_dir}");
        return false;
    }

    // Get all JSON files in the directory
    $json_files = glob($dataset_dir . '*.json');

    if (empty($json_files)) {
        error_log("BusinessCraft AI: No JSON files found in dataset directory: {$dataset_dir}");
        return false;
    }

    // Find the latest versioned file
    $latest_file = get_latest_versioned_file($json_files);

    // Special handling for Kenya - try cleaned version first if it exists and is valid
    if ($country === 'Kenya') {
        $cleaned_file = $dataset_dir . 'kenya_business_data.cleaned.json';
        if (file_exists($cleaned_file)) {
            $test_content = file_get_contents($cleaned_file);
            if ($test_content !== false) {
                $test_decode = json_decode($test_content, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($test_decode['sectors'])) {
                    $latest_file = $cleaned_file;
                    error_log("BusinessCraft AI: Using cleaned Kenya dataset file");
                } else {
                    error_log("BusinessCraft AI: Cleaned Kenya file is invalid, falling back to original");
                }
            }
        }
    }

    if (!$latest_file) {
        error_log("BusinessCraft AI: Could not determine latest versioned file in: {$dataset_dir}");
        return false;
    }

    // Load and decode the JSON file
    $file_contents = file_get_contents($latest_file);

    if ($file_contents === false) {
        error_log("BusinessCraft AI: Failed to read file: {$latest_file}");
        return false;
    }

    $decoded_data = json_decode($file_contents, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("BusinessCraft AI: JSON decode error in file {$latest_file}: " . json_last_error_msg());
        return false;
    }

    // Log successful load for debugging
    error_log("BusinessCraft AI: Successfully loaded dataset for {$country} from {$latest_file}");

    // Cache the dataset for 1 hour to improve performance
    set_transient($cache_key, $decoded_data, HOUR_IN_SECONDS);

    return $decoded_data;
}

/**
 * Determine the latest versioned file from an array of file paths
 *
 * Prioritizes files with version numbers (e.g., v7, v6, v5) and falls back
 * to the base file if no versioned files are found.
 *
 * @param array $files Array of file paths
 * @return string|false Path to the latest file, or false if none found
 */
function get_latest_versioned_file($files) {
    if (empty($files)) {
        return false;
    }

    $versioned_files = [];
    $base_file = null;

    foreach ($files as $file) {
        $filename = basename($file);

        // Check for versioned files (e.g., filename_datav7.json)
        if (preg_match('/v(\d+)\.json$/', $filename, $matches)) {
            $version = (int) $matches[1];
            $versioned_files[$version] = $file;
        }
        // Check for base file without version (e.g., filename_data.json)
        elseif (preg_match('/_data\.json$/', $filename)) {
            $base_file = $file;
        }
    }

    // Return the highest versioned file if available
    if (!empty($versioned_files)) {
        ksort($versioned_files);
        return end($versioned_files); // Get the highest version
    }

    // Fall back to base file
    if ($base_file) {
        return $base_file;
    }

    // If no pattern matches, return the first file
    return $files[0];
}

/**
 * Get available countries for dataset loading
 *
 * @return array Array of supported country names
 */
function get_available_dataset_countries() {
    return ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
}

/**
 * Validate if a country has an available dataset
 *
 * @param string $country Country name to validate
 * @return bool True if country dataset is available, false otherwise
 */
function is_dataset_country_available($country) {
    $available_countries = get_available_dataset_countries();
    return in_array($country, $available_countries, true);
}

/**
 * Get sector-specific context data by country and sector name
 *
 * Loads the country dataset and extracts data for a specific business sector.
 * Performs case-insensitive matching on sector names.
 *
 * @param string $country Country name (e.g., "Ghana", "Kenya", "Nigeria", "South Africa")
 * @param string $sector_name Sector name to search for (e.g., "Agriculture", "Fintech")
 * @return array|null Sector data array if found, null if not found or on error
 */
function get_sector_context_by_country($country, $sector_name) {
    // Validate input parameters
    if (empty($country) || !is_string($country)) {
        error_log('BusinessCraft AI: Invalid country parameter provided to get_sector_context_by_country()');
        return null;
    }

    if (empty($sector_name) || !is_string($sector_name)) {
        error_log('BusinessCraft AI: Invalid sector_name parameter provided to get_sector_context_by_country()');
        return null;
    }

    // Load the full country dataset
    $dataset = load_business_dataset_by_country($country);

    if ($dataset === false) {
        error_log("BusinessCraft AI: Failed to load dataset for country '{$country}' in get_sector_context_by_country()");
        return null;
    }

    // Validate dataset structure
    if (!isset($dataset['sectors']) || !is_array($dataset['sectors'])) {
        error_log("BusinessCraft AI: Invalid dataset structure for country '{$country}' - missing or invalid 'sectors' array");
        return null;
    }

    // Normalize sector name for case-insensitive comparison
    $normalized_sector_name = strtolower(trim($sector_name));

    // Search through sectors for a match
    foreach ($dataset['sectors'] as $sector) {
        if (!isset($sector['sector_name'])) {
            continue; // Skip sectors without a name
        }

        $current_sector_name = strtolower(trim($sector['sector_name']));

        // Exact match (case-insensitive)
        if ($current_sector_name === $normalized_sector_name) {
            error_log("BusinessCraft AI: Found exact sector match for '{$sector_name}' in {$country}");
            return $sector;
        }

        // Partial match - check if the search term is contained in the sector name
        if (strpos($current_sector_name, $normalized_sector_name) !== false) {
            error_log("BusinessCraft AI: Found partial sector match for '{$sector_name}' -> '{$sector['sector_name']}' in {$country}");
            return $sector;
        }
    }

    // No match found
    error_log("BusinessCraft AI: No sector match found for '{$sector_name}' in {$country}");
    return null;
}

/**
 * Get all available sectors for a specific country
 *
 * Returns an array of all sector names available in the country's dataset.
 * Useful for displaying available options or validation.
 *
 * @param string $country Country name
 * @return array|false Array of sector names, or false on error
 */
function get_available_sectors_by_country($country) {
    // Load the full country dataset
    $dataset = load_business_dataset_by_country($country);

    if ($dataset === false) {
        return false;
    }

    // Validate dataset structure
    if (!isset($dataset['sectors']) || !is_array($dataset['sectors'])) {
        return false;
    }

    $sector_names = [];

    foreach ($dataset['sectors'] as $sector) {
        if (isset($sector['sector_name'])) {
            $sector_names[] = $sector['sector_name'];
        }
    }

    return $sector_names;
}

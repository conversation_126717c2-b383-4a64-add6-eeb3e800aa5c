<?php
/**
 * Memory Optimization Test
 * 
 * This script tests the memory optimization fixes for ChatGABI
 * Access via: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-memory-optimization.php
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not load WordPress');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Memory Optimization Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 24px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 8px; }
        .test-section h2 { margin-top: 0; color: #2563eb; }
        .success { color: #059669; font-weight: 500; }
        .error { color: #dc2626; font-weight: 500; }
        .warning { color: #d97706; font-weight: 500; }
        .info { color: #2563eb; }
        .critical { color: #dc2626; background: #fee2e2; padding: 8px; border-radius: 4px; }
        .good { color: #059669; background: #d1fae5; padding: 8px; border-radius: 4px; }
        .moderate { color: #d97706; background: #fef3c7; padding: 8px; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto; font-size: 12px; border: 1px solid #e0e0e0; }
        .test-result { padding: 8px; border-radius: 4px; margin: 4px 0; }
        .test-result.pass { background: #d1fae5; border: 1px solid #10b981; }
        .test-result.fail { background: #fee2e2; border: 1px solid #ef4444; }
        .test-result.warn { background: #fef3c7; border: 1px solid #f59e0b; }
        .memory-bar { width: 100%; height: 20px; background: #e5e7eb; border-radius: 10px; overflow: hidden; margin: 8px 0; }
        .memory-fill { height: 100%; transition: width 0.3s ease; }
        .memory-fill.good { background: #10b981; }
        .memory-fill.moderate { background: #f59e0b; }
        .memory-fill.critical { background: #ef4444; }
        .summary { background: #f0f9ff; border: 1px solid #0ea5e9; padding: 16px; border-radius: 8px; margin-top: 24px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 ChatGABI Memory Optimization Test</h1>
        <p class="info">Testing memory optimization fixes and monitoring system performance.</p>
        
        <div class="test-section">
            <h2>1. Current Memory Status</h2>
            <?php
            $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
            $memory_used = memory_get_usage(true);
            $memory_peak = memory_get_peak_usage(true);
            $memory_available = $memory_limit - $memory_used;
            $memory_percentage = ($memory_used / $memory_limit) * 100;
            
            $status_class = $memory_percentage > 90 ? 'critical' : ($memory_percentage > 80 ? 'moderate' : 'good');
            $bar_class = $memory_percentage > 90 ? 'critical' : ($memory_percentage > 80 ? 'moderate' : 'good');
            ?>
            
            <div class="<?php echo $status_class; ?>">
                <strong>Memory Usage: <?php echo round($memory_percentage, 2); ?>%</strong>
            </div>
            
            <div class="memory-bar">
                <div class="memory-fill <?php echo $bar_class; ?>" style="width: <?php echo min($memory_percentage, 100); ?>%"></div>
            </div>
            
            <div class="info">Memory Limit: <strong><?php echo size_format($memory_limit); ?></strong></div>
            <div class="info">Memory Used: <strong><?php echo size_format($memory_used); ?></strong></div>
            <div class="info">Memory Peak: <strong><?php echo size_format($memory_peak); ?></strong></div>
            <div class="info">Memory Available: <strong><?php echo size_format($memory_available); ?></strong></div>
        </div>
        
        <div class="test-section">
            <h2>2. Memory Optimizer Functions</h2>
            <?php if (function_exists('chatgabi_get_memory_stats')): ?>
                <div class="test-result pass">✅ Memory optimizer loaded successfully</div>
                
                <?php
                $stats = chatgabi_get_memory_stats();
                ?>
                <div class="info">Status: <strong><?php echo ucfirst($stats['status']); ?></strong></div>
                <div class="info">Available Memory: <strong><?php echo $stats['available_formatted']; ?></strong></div>
                
            <?php else: ?>
                <div class="test-result fail">❌ Memory optimizer not loaded</div>
            <?php endif; ?>
            
            <?php if (function_exists('chatgabi_can_proceed_with_memory_check')): ?>
                <div class="test-result pass">✅ Memory check functions available</div>
            <?php else: ?>
                <div class="test-result fail">❌ Memory check functions not available</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>3. Conditional Module Loading Test</h2>
            <?php
            $loaded_modules = get_included_files();
            $chatgabi_modules = array_filter($loaded_modules, function($file) {
                return strpos($file, 'businesscraft-ai/inc/') !== false;
            });
            
            $module_count = count($chatgabi_modules);
            ?>
            
            <div class="info">Total PHP files loaded: <strong><?php echo count($loaded_modules); ?></strong></div>
            <div class="info">ChatGABI modules loaded: <strong><?php echo $module_count; ?></strong></div>
            
            <?php if ($module_count < 30): ?>
                <div class="test-result pass">✅ Conditional loading working - reduced module count</div>
            <?php elseif ($module_count < 40): ?>
                <div class="test-result warn">⚠️ Moderate module loading - some optimization needed</div>
            <?php else: ?>
                <div class="test-result fail">❌ Too many modules loaded - conditional loading not working</div>
            <?php endif; ?>
            
            <details>
                <summary>View loaded ChatGABI modules</summary>
                <pre><?php
                foreach ($chatgabi_modules as $module) {
                    echo basename($module) . "\n";
                }
                ?></pre>
            </details>
        </div>
        
        <div class="test-section">
            <h2>4. Template Loading Memory Test</h2>
            <?php if (function_exists('chatgabi_load_template_by_type')): ?>
                <div class="test-result pass">✅ Template loading function available</div>
                
                <?php
                $memory_before = memory_get_usage(true);
                $templates = chatgabi_load_template_by_type('business_plan', 'en');
                $memory_after = memory_get_usage(true);
                $memory_used_for_templates = $memory_after - $memory_before;
                ?>
                
                <div class="info">Memory used for template loading: <strong><?php echo size_format($memory_used_for_templates); ?></strong></div>
                
                <?php if ($templates !== false): ?>
                    <div class="test-result pass">✅ Template loading successful</div>
                    <div class="info">Templates loaded: <strong><?php echo is_array($templates) ? count($templates) : 'N/A'; ?></strong></div>
                <?php else: ?>
                    <div class="test-result warn">⚠️ Template loading returned false (may be memory protection)</div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="test-result fail">❌ Template loading function not available</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>5. Garbage Collection Test</h2>
            <?php
            $gc_enabled = gc_enabled();
            $cycles_before = function_exists('gc_collect_cycles') ? gc_collect_cycles() : 0;
            
            // Create some temporary data to test garbage collection
            $temp_data = array();
            for ($i = 0; $i < 1000; $i++) {
                $temp_data[] = str_repeat('test', 100);
            }
            unset($temp_data);
            
            $cycles_after = function_exists('gc_collect_cycles') ? gc_collect_cycles() : 0;
            ?>
            
            <div class="test-result <?php echo $gc_enabled ? 'pass' : 'warn'; ?>">
                <?php echo $gc_enabled ? '✅' : '⚠️'; ?> Garbage collection <?php echo $gc_enabled ? 'enabled' : 'disabled'; ?>
            </div>
            
            <?php if (function_exists('gc_collect_cycles')): ?>
                <div class="test-result pass">✅ Garbage collection functions available</div>
                <div class="info">Cycles collected in test: <strong><?php echo $cycles_after; ?></strong></div>
            <?php else: ?>
                <div class="test-result warn">⚠️ Garbage collection functions not available</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>6. Safe Loading Functions Test</h2>
            <?php if (function_exists('chatgabi_safe_json_decode')): ?>
                <div class="test-result pass">✅ Safe JSON decode function available</div>
                
                <?php
                $test_json = '{"test": "data", "array": [1, 2, 3]}';
                $decoded = chatgabi_safe_json_decode($test_json);
                ?>
                
                <?php if ($decoded !== false): ?>
                    <div class="test-result pass">✅ Safe JSON decode working</div>
                <?php else: ?>
                    <div class="test-result fail">❌ Safe JSON decode failed</div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="test-result fail">❌ Safe JSON decode function not available</div>
            <?php endif; ?>
            
            <?php if (function_exists('chatgabi_safe_file_get_contents')): ?>
                <div class="test-result pass">✅ Safe file loading function available</div>
            <?php else: ?>
                <div class="test-result fail">❌ Safe file loading function not available</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>7. Cache Optimization Test</h2>
            <?php
            // Test cache clearing
            if (function_exists('chatgabi_clear_caches')) {
                chatgabi_clear_caches();
                $memory_after_cache_clear = memory_get_usage(true);
            ?>
                <div class="test-result pass">✅ Cache clearing function available and executed</div>
                <div class="info">Memory after cache clear: <strong><?php echo size_format($memory_after_cache_clear); ?></strong></div>
            <?php else: ?>
                <div class="test-result fail">❌ Cache clearing function not available</div>
            <?php endif; ?>
            
            <?php
            // Test transient optimization
            global $wpdb;
            $transient_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE '_transient_chatgabi_%'");
            ?>
            <div class="info">ChatGABI transients in database: <strong><?php echo $transient_count; ?></strong></div>
        </div>
        
        <div class="test-section">
            <h2>8. Performance Recommendations</h2>
            <?php
            $recommendations = array();
            
            if ($memory_percentage > 80) {
                $recommendations[] = "🔴 High memory usage detected - consider increasing server memory limit";
            }
            
            if ($module_count > 35) {
                $recommendations[] = "🟡 Many modules loaded - verify conditional loading is working correctly";
            }
            
            if (!$gc_enabled) {
                $recommendations[] = "🟡 Enable garbage collection for better memory management";
            }
            
            if (!class_exists('Redis') && !extension_loaded('redis')) {
                $recommendations[] = "🟡 Consider installing Redis for improved caching performance";
            }
            
            if (empty($recommendations)) {
                $recommendations[] = "✅ All memory optimization checks passed - system is well optimized";
            }
            
            foreach ($recommendations as $recommendation) {
                echo "<div class='info'>{$recommendation}</div>";
            }
            ?>
        </div>
        
        <div class="summary">
            <h2>📋 Test Summary</h2>
            <?php
            $overall_status = 'good';
            if ($memory_percentage > 90) {
                $overall_status = 'critical';
            } elseif ($memory_percentage > 80 || $module_count > 40) {
                $overall_status = 'moderate';
            }
            ?>
            
            <div class="<?php echo $overall_status; ?>">
                <strong>Overall Status: <?php echo ucfirst($overall_status); ?></strong>
            </div>
            
            <div class="info">
                <strong>Key Metrics:</strong>
                <ul>
                    <li>Memory Usage: <?php echo round($memory_percentage, 2); ?>%</li>
                    <li>Modules Loaded: <?php echo $module_count; ?></li>
                    <li>Memory Available: <?php echo size_format($memory_available); ?></li>
                    <li>Optimization Status: <?php echo function_exists('chatgabi_get_memory_stats') ? 'Active' : 'Inactive'; ?></li>
                </ul>
            </div>
            
            <?php if ($overall_status === 'good'): ?>
                <div class="info">
                    <strong>✅ Memory optimization is working correctly!</strong><br>
                    The ChatGABI theme is operating within safe memory limits with optimized module loading.
                </div>
            <?php else: ?>
                <div class="info">
                    <strong>⚠️ Memory optimization needs attention.</strong><br>
                    Review the recommendations above to improve memory performance.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Auto-refresh memory stats every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>

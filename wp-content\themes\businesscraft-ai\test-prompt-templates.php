<?php
/**
 * BusinessCraft AI - Prompt Templates System Test
 * 
 * Test script to verify prompt template library functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

echo '<h1>BusinessCraft AI - Prompt Templates System Test</h1>';
echo '<p>Testing comprehensive prompt template library functionality...</p>';

$tests_run = 0;
$tests_passed = 0;

// Test 1: Check if prompt templates system is initialized
echo '<h2>Test 1: System Initialization</h2>';
$tests_run++;

if (function_exists('chatgabi_init_prompt_templates')) {
    echo '✅ chatgabi_init_prompt_templates() function exists<br>';
    
    if (function_exists('chatgabi_create_prompt_templates_tables')) {
        echo '✅ chatgabi_create_prompt_templates_tables() function exists<br>';
        $tests_passed++;
    } else {
        echo '❌ chatgabi_create_prompt_templates_tables() function missing<br>';
    }
} else {
    echo '❌ chatgabi_init_prompt_templates() function not found<br>';
}

// Test 2: Check database tables
echo '<h2>Test 2: Database Tables</h2>';
$tests_run++;

global $wpdb;
$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$usage_table = $wpdb->prefix . 'chatgabi_template_usage';

$tables_exist = 0;
$tables = array(
    'Templates' => $templates_table,
    'Categories' => $categories_table,
    'Usage Tracking' => $usage_table
);

foreach ($tables as $name => $table) {
    if ($wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table) {
        echo "✅ {$name} table exists: {$table}<br>";
        $tables_exist++;
    } else {
        echo "❌ {$name} table missing: {$table}<br>";
    }
}

if ($tables_exist === count($tables)) {
    echo "<strong>✅ All {$tables_exist} database tables exist</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>❌ Only {$tables_exist} out of " . count($tables) . " tables exist</strong><br>";
}

// Test 3: Check default categories
echo '<h2>Test 3: Default Categories</h2>';
$tests_run++;

$categories_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
if ($categories_count > 0) {
    echo "✅ {$categories_count} template categories found<br>";
    
    $categories = $wpdb->get_results("SELECT name, slug, icon FROM {$categories_table} ORDER BY sort_order");
    foreach ($categories as $category) {
        echo "   - {$category->icon} {$category->name} ({$category->slug})<br>";
    }
    $tests_passed++;
} else {
    echo "❌ No template categories found<br>";
}

// Test 4: Check default templates
echo '<h2>Test 4: Default Templates</h2>';
$tests_run++;

$templates_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table}");
if ($templates_count > 0) {
    echo "✅ {$templates_count} prompt templates found<br>";
    
    $templates = $wpdb->get_results("
        SELECT t.title, t.description, c.name as category_name, t.is_featured
        FROM {$templates_table} t
        LEFT JOIN {$categories_table} c ON t.category_id = c.id
        WHERE t.user_id = 0
        ORDER BY t.is_featured DESC, t.title
        LIMIT 10
    ");
    
    foreach ($templates as $template) {
        $featured = $template->is_featured ? '⭐' : '';
        echo "   {$featured} {$template->title} ({$template->category_name})<br>";
        if ($template->description) {
            echo "     <em>" . substr($template->description, 0, 100) . "...</em><br>";
        }
    }
    $tests_passed++;
} else {
    echo "❌ No prompt templates found<br>";
}

// Test 5: Check REST API endpoints
echo '<h2>Test 5: REST API Endpoints</h2>';
$tests_run++;

$rest_endpoints = array(
    'chatgabi/v1/templates',
    'chatgabi/v1/template-categories',
    'chatgabi/v1/templates/(?P<id>\d+)',
    'chatgabi/v1/templates/(?P<id>\d+)/use'
);

$endpoints_registered = 0;
foreach ($rest_endpoints as $endpoint) {
    $routes = rest_get_server()->get_routes();
    $found = false;
    
    foreach ($routes as $route => $handlers) {
        if (strpos($route, str_replace('(?P<id>\d+)', '\d+', $endpoint)) !== false) {
            $found = true;
            break;
        }
    }
    
    if ($found) {
        echo "✅ REST endpoint registered: {$endpoint}<br>";
        $endpoints_registered++;
    } else {
        echo "❌ REST endpoint not found: {$endpoint}<br>";
    }
}

if ($endpoints_registered === count($rest_endpoints)) {
    echo "<strong>✅ All {$endpoints_registered} REST endpoints are registered</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>❌ Only {$endpoints_registered} out of " . count($rest_endpoints) . " endpoints registered</strong><br>";
}

// Test 6: Check admin menu integration
echo '<h2>Test 6: Admin Menu Integration</h2>';
$tests_run++;

global $submenu;
$menu_found = false;

if (isset($submenu['chatgabi-main'])) {
    foreach ($submenu['chatgabi-main'] as $submenu_item) {
        if (isset($submenu_item[2]) && $submenu_item[2] === 'chatgabi-prompt-templates') {
            $menu_found = true;
            echo "✅ Prompt Templates admin menu found: {$submenu_item[1]}<br>";
            break;
        }
    }
}

if ($menu_found) {
    $tests_passed++;
} else {
    echo "❌ Prompt Templates admin menu not found<br>";
}

// Test 7: Check asset files
echo '<h2>Test 7: Asset Files</h2>';
$tests_run++;

$asset_files = array(
    'CSS' => get_template_directory() . '/assets/css/prompt-templates.css',
    'JavaScript' => get_template_directory() . '/assets/js/prompt-templates.js'
);

$assets_exist = 0;
foreach ($asset_files as $type => $file) {
    if (file_exists($file)) {
        echo "✅ {$type} file exists: " . basename($file) . "<br>";
        $assets_exist++;
    } else {
        echo "❌ {$type} file missing: " . basename($file) . "<br>";
    }
}

if ($assets_exist === count($asset_files)) {
    echo "<strong>✅ All {$assets_exist} asset files exist</strong><br>";
    $tests_passed++;
} else {
    echo "<strong>⚠️ {$assets_exist} out of " . count($asset_files) . " asset files exist</strong><br>";
}

// Test 8: Test template functionality
echo '<h2>Test 8: Template Functionality</h2>';
$tests_run++;

if (function_exists('chatgabi_get_template_categories')) {
    $categories = chatgabi_get_template_categories(true);
    if (!empty($categories)) {
        echo "✅ chatgabi_get_template_categories() returns " . count($categories) . " categories<br>";
        
        if (function_exists('chatgabi_get_user_templates')) {
            echo "✅ chatgabi_get_user_templates() function exists<br>";
            
            if (function_exists('chatgabi_save_prompt_template')) {
                echo "✅ chatgabi_save_prompt_template() function exists<br>";
                $tests_passed++;
            } else {
                echo "❌ chatgabi_save_prompt_template() function missing<br>";
            }
        } else {
            echo "❌ chatgabi_get_user_templates() function missing<br>";
        }
    } else {
        echo "❌ chatgabi_get_template_categories() returns empty result<br>";
    }
} else {
    echo "❌ chatgabi_get_template_categories() function not found<br>";
}

// Test 9: Test template variables extraction
echo '<h2>Test 9: Template Variables System</h2>';
$tests_run++;

$sample_template = "Analyze this business idea for the {country} market: {business_idea}. Consider the {sector} industry and target {customer_segment}.";
$expected_variables = array('country', 'business_idea', 'sector', 'customer_segment');

// Simulate variable extraction (this would be done in JavaScript)
preg_match_all('/\{([^}]+)\}/', $sample_template, $matches);
$extracted_variables = $matches[1];

if (count($extracted_variables) === count($expected_variables)) {
    echo "✅ Template variable extraction works correctly<br>";
    echo "   Variables found: " . implode(', ', $extracted_variables) . "<br>";
    $tests_passed++;
} else {
    echo "❌ Template variable extraction failed<br>";
    echo "   Expected: " . implode(', ', $expected_variables) . "<br>";
    echo "   Found: " . implode(', ', $extracted_variables) . "<br>";
}

// Final Summary
echo '<hr>';
echo '<h2>🎯 Test Summary</h2>';
echo "<p><strong>Tests Passed: {$tests_passed} / {$tests_run}</strong></p>";

$success_rate = ($tests_passed / $tests_run) * 100;

if ($success_rate >= 90) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>🎉 Excellent! Prompt Templates System is working properly</h3>';
    echo '<p>The comprehensive prompt template library is ready for use. Users can now access pre-built templates for African business scenarios.</p>';
    echo '</div>';
} elseif ($success_rate >= 70) {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>⚠️ Good, but some issues need attention</h3>';
    echo '<p>Most components are working, but please address the failed tests above.</p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>❌ Prompt Templates System needs fixes</h3>';
    echo '<p>Multiple components are not working properly. Please review and fix the issues above.</p>';
    echo '</div>';
}

echo '<h3>📋 System Features:</h3>';
echo '<ul>';
echo '<li><strong>Pre-built Templates:</strong> Business analysis, funding proposals, market research, financial forecasting</li>';
echo '<li><strong>African Focus:</strong> Templates tailored for Ghana, Kenya, Nigeria, South Africa markets</li>';
echo '<li><strong>Dynamic Variables:</strong> {country}, {sector}, {business_idea} placeholders</li>';
echo '<li><strong>Categorization:</strong> Business Planning, Marketing, Financial Analysis, Market Research, etc.</li>';
echo '<li><strong>Complexity Levels:</strong> Beginner, Intermediate, Advanced templates</li>';
echo '<li><strong>Usage Tracking:</strong> Analytics on template popularity and effectiveness</li>';
echo '<li><strong>REST API:</strong> Full API access for external integrations</li>';
echo '<li><strong>Admin Interface:</strong> WordPress admin for template management</li>';
echo '</ul>';

echo '<h3>🔗 Quick Links:</h3>';
echo '<ul>';
echo '<li><a href="' . admin_url('admin.php?page=chatgabi-prompt-templates') . '">→ Prompt Templates Admin</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=chatgabi-main') . '">→ ChatGABI Dashboard</a></li>';
echo '<li><a href="' . rest_url('chatgabi/v1/templates') . '">→ Templates REST API</a></li>';
echo '<li><a href="' . rest_url('chatgabi/v1/template-categories') . '">→ Categories REST API</a></li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Test completed at ' . current_time('mysql') . '</em></p>';
echo '<p><a href="' . admin_url() . '">← Return to WordPress Admin</a></p>';
?>

<?php
/**
 * BusinessCraft AI - Prompt Templates Demo
 * 
 * Demo page showing the prompt template library integration
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

get_header();
?>

<div class="demo-container" style="max-width: 1200px; margin: 0 auto; padding: 20px;">
    <div class="demo-header" style="text-align: center; margin-bottom: 40px;">
        <h1>🚀 BusinessCraft AI Prompt Templates Demo</h1>
        <p style="font-size: 18px; color: #666;">Experience the comprehensive prompt template library for African entrepreneurs</p>
    </div>

    <div class="demo-features" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 40px;">
        <div class="feature-card" style="background: #fff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <h3>📋 Pre-built Templates</h3>
            <p>Access professionally crafted prompt templates for common business scenarios in African markets.</p>
            <ul>
                <li>Business idea analysis</li>
                <li>Funding proposals</li>
                <li>Market entry strategies</li>
                <li>Competitive analysis</li>
                <li>Financial forecasting</li>
            </ul>
        </div>

        <div class="feature-card" style="background: #fff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <h3>🌍 African Market Focus</h3>
            <p>Templates specifically designed for business environments in Ghana, Kenya, Nigeria, and South Africa.</p>
            <ul>
                <li>Country-specific market analysis</li>
                <li>Local regulatory considerations</li>
                <li>Cultural context integration</li>
                <li>Regional business practices</li>
            </ul>
        </div>

        <div class="feature-card" style="background: #fff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <h3>⚡ Dynamic Variables</h3>
            <p>Smart templates with customizable variables that adapt to your specific business needs.</p>
            <ul>
                <li>{country} - Target market</li>
                <li>{sector} - Industry focus</li>
                <li>{business_idea} - Your concept</li>
                <li>{funding_amount} - Investment needs</li>
            </ul>
        </div>
    </div>

    <div class="demo-chat-section" style="background: #f8f9fa; padding: 40px; border-radius: 12px; margin-bottom: 40px;">
        <h2 style="text-align: center; margin-bottom: 30px;">Try the Chat Interface with Templates</h2>
        
        <?php
        // Display the chat block with template integration
        echo do_shortcode('[businesscraft_ai_chat show_history="true" show_examples="true" max_history="5"]');
        ?>
    </div>

    <div class="demo-categories" style="margin-bottom: 40px;">
        <h2 style="text-align: center; margin-bottom: 30px;">Template Categories</h2>
        
        <?php
        // Get template categories
        if (function_exists('chatgabi_get_template_categories')) {
            $categories = chatgabi_get_template_categories(true);
            
            if (!empty($categories)) {
                echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">';
                
                foreach ($categories as $category) {
                    $count = $category->template_count ?? 0;
                    echo '<div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">';
                    echo '<div style="font-size: 32px; margin-bottom: 10px;">' . ($category->icon ?? '📁') . '</div>';
                    echo '<h3 style="margin: 0 0 8px 0; color: ' . ($category->color ?? '#007cba') . ';">' . esc_html($category->name) . '</h3>';
                    echo '<p style="margin: 0 0 10px 0; color: #666; font-size: 14px;">' . esc_html($category->description ?? '') . '</p>';
                    echo '<span style="background: #f0f0f0; padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #666;">' . $count . ' templates</span>';
                    echo '</div>';
                }
                
                echo '</div>';
            } else {
                echo '<p style="text-align: center; color: #666;">No template categories found. Please run the initialization script.</p>';
            }
        } else {
            echo '<p style="text-align: center; color: #666;">Template system not initialized. Please check the installation.</p>';
        }
        ?>
    </div>

    <div class="demo-examples" style="background: #fff; padding: 40px; border-radius: 12px; margin-bottom: 40px;">
        <h2 style="text-align: center; margin-bottom: 30px;">Example Templates</h2>
        
        <div style="display: grid; gap: 30px;">
            <div class="example-template" style="border: 1px solid #e1e5e9; border-radius: 8px; overflow: hidden;">
                <div style="background: #007cba; color: white; padding: 15px;">
                    <h3 style="margin: 0;">⭐ Business Idea Analysis for African Markets</h3>
                    <p style="margin: 5px 0 0; opacity: 0.9; font-size: 14px;">Comprehensive analysis template for evaluating business ideas</p>
                </div>
                <div style="padding: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 13px; line-height: 1.5; margin-bottom: 15px;">
Analyze this business idea for the <strong>{country}</strong> market: <strong>{business_idea}</strong>

Please provide a detailed analysis covering:

1. **Market Opportunity**
   - Market size and growth potential in <strong>{country}</strong>
   - Target customer demographics and behavior
   - Local market trends and demands

2. **Competitive Landscape**
   - Existing competitors in <strong>{country}</strong>
   - Market gaps and opportunities
   - Competitive advantages needed

3. **Business Model Viability**
   - Revenue streams suitable for <strong>{country}</strong>
   - Cost structure considerations
   - Scalability potential
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <span style="background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Beginner</span>
                        <span style="background: #e7f3ff; color: #0066cc; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Business Planning</span>
                        <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Featured</span>
                    </div>
                </div>
            </div>

            <div class="example-template" style="border: 1px solid #e1e5e9; border-radius: 8px; overflow: hidden;">
                <div style="background: #28a745; color: white; padding: 15px;">
                    <h3 style="margin: 0;">💰 Funding Proposal for African Startups</h3>
                    <p style="margin: 5px 0 0; opacity: 0.9; font-size: 14px;">Template for creating compelling funding proposals</p>
                </div>
                <div style="padding: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 13px; line-height: 1.5; margin-bottom: 15px;">
Create a comprehensive funding proposal for my <strong>{sector}</strong> startup in <strong>{country}</strong>:

**Business Overview:**
<strong>{business_description}</strong>

**Funding Request:** <strong>{funding_amount}</strong>

Please structure the proposal with:

1. **Executive Summary**
   - Business concept and value proposition
   - Market opportunity in <strong>{country}</strong>
   - Funding requirements and use of funds
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Intermediate</span>
                        <span style="background: #e7f3ff; color: #0066cc; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Business Planning</span>
                        <span style="background: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Featured</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-instructions" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 12px; text-align: center;">
        <h2 style="margin: 0 0 20px 0;">How to Use Templates</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; margin-top: 30px;">
            <div>
                <div style="font-size: 48px; margin-bottom: 10px;">1️⃣</div>
                <h3>Click "Choose Template"</h3>
                <p style="opacity: 0.9;">Open the template library from the chat interface</p>
            </div>
            <div>
                <div style="font-size: 48px; margin-bottom: 10px;">2️⃣</div>
                <h3>Browse & Filter</h3>
                <p style="opacity: 0.9;">Find templates by category, complexity, or search</p>
            </div>
            <div>
                <div style="font-size: 48px; margin-bottom: 10px;">3️⃣</div>
                <h3>Fill Variables</h3>
                <p style="opacity: 0.9;">Customize template variables for your specific needs</p>
            </div>
            <div>
                <div style="font-size: 48px; margin-bottom: 10px;">4️⃣</div>
                <h3>Generate & Refine</h3>
                <p style="opacity: 0.9;">Use the template and refine the AI response</p>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin: 40px 0;">
        <a href="<?php echo admin_url('admin.php?page=chatgabi-prompt-templates'); ?>" 
           style="background: #007cba; color: white; padding: 15px 30px; border-radius: 8px; text-decoration: none; font-weight: 600; display: inline-block;">
            🛠️ Manage Templates (Admin)
        </a>
    </div>
</div>

<style>
/* Ensure template button is visible */
.template-trigger-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    width: 100% !important;
    justify-content: center !important;
    margin-bottom: 12px !important;
}

.template-trigger-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .demo-container {
        padding: 10px !important;
    }
    
    .demo-features,
    .demo-categories > div {
        grid-template-columns: 1fr !important;
    }
    
    .demo-instructions > div {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 480px) {
    .demo-instructions > div {
        grid-template-columns: 1fr !important;
    }
}
</style>

<?php get_footer(); ?>

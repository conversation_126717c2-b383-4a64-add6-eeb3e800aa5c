<?php
/**
 * BusinessCraft AI - Opportunity Alerts System
 * 
 * Comprehensive user opportunity alerts system with subscription management,
 * automated matching, and email notifications via SendPulse API.
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Opportunity Alerts Manager Class
 */
class ChatGABI_Opportunity_Alerts {
    
    private $alerts_table;
    private $alert_logs_table;
    private $alert_matches_table;
    
    public function __construct() {
        global $wpdb;
        $this->alerts_table = $wpdb->prefix . 'chatgabi_opportunity_alerts';
        $this->alert_logs_table = $wpdb->prefix . 'chatgabi_alert_logs';
        $this->alert_matches_table = $wpdb->prefix . 'chatgabi_alert_matches';
        
        // Initialize hooks
        add_action('init', array($this, 'init_hooks'));
        add_action('wp_ajax_chatgabi_save_alert_subscription', array($this, 'ajax_save_alert_subscription'));
        add_action('wp_ajax_chatgabi_delete_alert_subscription', array($this, 'ajax_delete_alert_subscription'));
        add_action('wp_ajax_chatgabi_toggle_alert_subscription', array($this, 'ajax_toggle_alert_subscription'));
        add_action('wp_ajax_chatgabi_preview_alert_matches', array($this, 'ajax_preview_alert_matches'));
        add_action('wp_ajax_chatgabi_get_alert_statistics', array($this, 'ajax_get_alert_statistics'));
        
        // Cron hooks
        add_action('chatgabi_process_opportunity_alerts', array($this, 'process_opportunity_alerts'));
        add_action('chatgabi_send_daily_alert_digest', array($this, 'send_daily_alert_digest'));
        add_action('chatgabi_send_weekly_alert_summary', array($this, 'send_weekly_alert_summary'));
        add_action('chatgabi_cleanup_old_alert_logs', array($this, 'cleanup_old_alert_logs'));
    }
    
    /**
     * Initialize hooks and cron schedules
     */
    public function init_hooks() {
        // Add custom cron schedules FIRST (before scheduling jobs)
        add_filter('cron_schedules', array($this, 'add_custom_cron_schedules'));

        // Register custom post type for alert filters
        $this->register_alert_filters_post_type();

        // Schedule cron jobs AFTER custom schedules are registered
        add_action('wp_loaded', array($this, 'schedule_cron_jobs'));
    }
    
    /**
     * Add custom cron schedules
     */
    public function add_custom_cron_schedules($schedules) {
        $schedules['every_15_minutes'] = array(
            'interval' => 15 * 60,
            'display' => __('Every 15 Minutes', 'chatgabi')
        );

        $schedules['every_30_minutes'] = array(
            'interval' => 30 * 60,
            'display' => __('Every 30 Minutes', 'chatgabi')
        );

        // Add monthly schedule (30 days)
        $schedules['monthly'] = array(
            'interval' => 30 * 24 * 60 * 60, // 30 days in seconds
            'display' => __('Once Monthly', 'chatgabi')
        );

        return $schedules;
    }
    
    /**
     * Schedule cron jobs for alert processing
     */
    public function schedule_cron_jobs() {
        // Ensure custom schedules are available
        $available_schedules = wp_get_schedules();

        // Process opportunity alerts every 15 minutes
        if (!wp_next_scheduled('chatgabi_process_opportunity_alerts')) {
            if (isset($available_schedules['every_15_minutes'])) {
                $result = wp_schedule_event(time(), 'every_15_minutes', 'chatgabi_process_opportunity_alerts');
                if (!$result) {
                    error_log('ChatGABI: Failed to schedule chatgabi_process_opportunity_alerts');
                }
            } else {
                error_log('ChatGABI: every_15_minutes schedule not available');
            }
        }

        // Send daily digest at 8 AM
        if (!wp_next_scheduled('chatgabi_send_daily_alert_digest')) {
            $daily_time = strtotime('08:00:00');
            if ($daily_time <= time()) {
                $daily_time = strtotime('tomorrow 08:00:00');
            }
            $result = wp_schedule_event($daily_time, 'daily', 'chatgabi_send_daily_alert_digest');
            if (!$result) {
                error_log('ChatGABI: Failed to schedule chatgabi_send_daily_alert_digest');
            }
        }

        // Send weekly summary on Mondays at 9 AM
        if (!wp_next_scheduled('chatgabi_send_weekly_alert_summary')) {
            $weekly_time = strtotime('next monday 09:00:00');
            $result = wp_schedule_event($weekly_time, 'weekly', 'chatgabi_send_weekly_alert_summary');
            if (!$result) {
                error_log('ChatGABI: Failed to schedule chatgabi_send_weekly_alert_summary');
            }
        }

        // Cleanup old logs monthly
        if (!wp_next_scheduled('chatgabi_cleanup_old_alert_logs')) {
            if (isset($available_schedules['monthly'])) {
                $result = wp_schedule_event(time(), 'monthly', 'chatgabi_cleanup_old_alert_logs');
                if (!$result) {
                    error_log('ChatGABI: Failed to schedule chatgabi_cleanup_old_alert_logs');
                } else {
                    error_log('ChatGABI: Successfully scheduled monthly cleanup job');
                }
            } else {
                error_log('ChatGABI: monthly schedule not available, available schedules: ' . implode(', ', array_keys($available_schedules)));
            }
        }
    }
    
    /**
     * Register custom post type for alert filters
     */
    private function register_alert_filters_post_type() {
        register_post_type('alert_filter', array(
            'labels' => array(
                'name' => __('Alert Filters', 'chatgabi'),
                'singular_name' => __('Alert Filter', 'chatgabi'),
            ),
            'public' => false,
            'show_ui' => false,
            'supports' => array('title'),
            'capability_type' => 'post',
            'map_meta_cap' => true,
        ));
    }
    
    /**
     * Create database tables for opportunity alerts
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Opportunity alerts table
        $alerts_sql = "CREATE TABLE IF NOT EXISTS {$this->alerts_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            filter_name varchar(255) NOT NULL,
            countries json DEFAULT NULL,
            opportunity_types json DEFAULT NULL,
            sectors json DEFAULT NULL,
            amount_min decimal(15,2) DEFAULT NULL,
            amount_max decimal(15,2) DEFAULT NULL,
            keywords text DEFAULT NULL,
            deadline_days int(11) DEFAULT NULL,
            notification_frequency varchar(20) DEFAULT 'immediate',
            is_active tinyint(1) DEFAULT 1,
            last_processed datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY is_active (is_active),
            KEY notification_frequency (notification_frequency),
            KEY last_processed (last_processed)
        ) $charset_collate;";
        
        // Alert logs table
        $logs_sql = "CREATE TABLE IF NOT EXISTS {$this->alert_logs_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            alert_id bigint(20) NOT NULL,
            email_address varchar(255) NOT NULL,
            email_subject varchar(500) NOT NULL,
            opportunities_count int(11) DEFAULT 0,
            email_type varchar(50) DEFAULT 'immediate',
            delivery_status varchar(20) DEFAULT 'pending',
            sendpulse_message_id varchar(255) DEFAULT NULL,
            opened_at datetime DEFAULT NULL,
            clicked_at datetime DEFAULT NULL,
            unsubscribed_at datetime DEFAULT NULL,
            error_message text DEFAULT NULL,
            sent_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY alert_id (alert_id),
            KEY delivery_status (delivery_status),
            KEY email_type (email_type),
            KEY sent_at (sent_at)
        ) $charset_collate;";
        
        // Alert matches table
        $matches_sql = "CREATE TABLE IF NOT EXISTS {$this->alert_matches_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            alert_id bigint(20) NOT NULL,
            opportunity_data json NOT NULL,
            match_score decimal(5,2) DEFAULT 0,
            is_sent tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY alert_id (alert_id),
            KEY is_sent (is_sent),
            KEY match_score (match_score),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($alerts_sql);
        dbDelta($logs_sql);
        dbDelta($matches_sql);
    }
    
    /**
     * Save user alert subscription
     */
    public function save_alert_subscription($user_id, $filter_data) {
        global $wpdb;
        
        // Validate required fields
        if (empty($filter_data['filter_name'])) {
            return new WP_Error('missing_name', __('Filter name is required', 'chatgabi'));
        }
        
        // Sanitize and prepare data
        $data = array(
            'user_id' => intval($user_id),
            'filter_name' => sanitize_text_field($filter_data['filter_name'] ?? ''),
            'countries' => !empty($filter_data['countries']) ? json_encode($filter_data['countries']) : null,
            'opportunity_types' => !empty($filter_data['opportunity_types']) ? json_encode($filter_data['opportunity_types']) : null,
            'sectors' => !empty($filter_data['sectors']) ? json_encode($filter_data['sectors']) : null,
            'amount_min' => !empty($filter_data['amount_min']) ? floatval($filter_data['amount_min']) : null,
            'amount_max' => !empty($filter_data['amount_max']) ? floatval($filter_data['amount_max']) : null,
            'keywords' => !empty($filter_data['keywords']) ? sanitize_textarea_field($filter_data['keywords']) : null,
            'deadline_days' => !empty($filter_data['deadline_days']) ? intval($filter_data['deadline_days']) : null,
            'notification_frequency' => sanitize_text_field($filter_data['notification_frequency'] ?? 'immediate'),
            'is_active' => 1
        );
        
        // Check if updating existing filter
        if (!empty($filter_data['alert_id'])) {
            $alert_id = intval($filter_data['alert_id']);
            
            // Verify ownership
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->alerts_table} WHERE id = %d AND user_id = %d",
                $alert_id, $user_id
            ));
            
            if (!$existing) {
                return new WP_Error('not_found', __('Alert filter not found', 'chatgabi'));
            }
            
            $result = $wpdb->update(
                $this->alerts_table,
                $data,
                array('id' => $alert_id, 'user_id' => $user_id),
                array('%d', '%s', '%s', '%s', '%s', '%f', '%f', '%s', '%d', '%s', '%d'),
                array('%d', '%d')
            );
            
            return $result !== false ? $alert_id : new WP_Error('update_failed', __('Failed to update alert filter', 'chatgabi'));
        } else {
            // Create new filter
            $result = $wpdb->insert($this->alerts_table, $data);
            
            return $result ? $wpdb->insert_id : new WP_Error('insert_failed', __('Failed to create alert filter', 'chatgabi'));
        }
    }
    
    /**
     * Get user alert subscriptions
     */
    public function get_user_alert_subscriptions($user_id) {
        global $wpdb;
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->alerts_table} WHERE user_id = %d ORDER BY created_at DESC",
            $user_id
        ), ARRAY_A);
        
        // Decode JSON fields
        foreach ($results as &$alert) {
            $alert['countries'] = !empty($alert['countries']) ? json_decode($alert['countries'], true) : array();
            $alert['opportunity_types'] = !empty($alert['opportunity_types']) ? json_decode($alert['opportunity_types'], true) : array();
            $alert['sectors'] = !empty($alert['sectors']) ? json_decode($alert['sectors'], true) : array();
        }
        
        return $results;
    }
    
    /**
     * Delete alert subscription
     */
    public function delete_alert_subscription($user_id, $alert_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->alerts_table,
            array('id' => intval($alert_id), 'user_id' => intval($user_id)),
            array('%d', '%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Toggle alert subscription status
     */
    public function toggle_alert_subscription($user_id, $alert_id, $is_active) {
        global $wpdb;

        $result = $wpdb->update(
            $this->alerts_table,
            array('is_active' => intval($is_active)),
            array('id' => intval($alert_id), 'user_id' => intval($user_id)),
            array('%d'),
            array('%d', '%d')
        );

        return $result !== false;
    }

    /**
     * Process opportunity alerts - main cron function
     */
    public function process_opportunity_alerts() {
        global $wpdb;

        // Get all active alert subscriptions
        $active_alerts = $wpdb->get_results(
            "SELECT * FROM {$this->alerts_table} WHERE is_active = 1 AND notification_frequency = 'immediate'",
            ARRAY_A
        );

        if (empty($active_alerts)) {
            return;
        }

        // Load all opportunities from JSON files
        $all_opportunities = $this->load_all_opportunities();

        foreach ($active_alerts as $alert) {
            $this->process_single_alert($alert, $all_opportunities);
        }

        // Update last processed timestamp
        $wpdb->update(
            $this->alerts_table,
            array('last_processed' => current_time('mysql')),
            array('notification_frequency' => 'immediate'),
            array('%s'),
            array('%s')
        );
    }

    /**
     * Process single alert subscription
     */
    private function process_single_alert($alert, $all_opportunities) {
        // Decode JSON fields
        $alert['countries'] = !empty($alert['countries']) ? json_decode($alert['countries'], true) : array();
        $alert['opportunity_types'] = !empty($alert['opportunity_types']) ? json_decode($alert['opportunity_types'], true) : array();
        $alert['sectors'] = !empty($alert['sectors']) ? json_decode($alert['sectors'], true) : array();

        // Find matching opportunities
        $matching_opportunities = $this->find_matching_opportunities($alert, $all_opportunities);

        if (empty($matching_opportunities)) {
            return;
        }

        // Check if we've already sent these opportunities
        $new_opportunities = $this->filter_new_opportunities($alert['id'], $matching_opportunities);

        if (empty($new_opportunities)) {
            return;
        }

        // Get user information
        $user = get_user_by('id', $alert['user_id']);
        if (!$user) {
            return;
        }

        // Send immediate alert
        $sendpulse = chatgabi_get_sendpulse();
        $result = $sendpulse->send_opportunity_alert(
            $user->user_email,
            $user->display_name ?: $user->user_login,
            $alert,
            $new_opportunities,
            'immediate'
        );

        if (!is_wp_error($result)) {
            // Mark opportunities as sent
            $this->mark_opportunities_as_sent($alert['id'], $new_opportunities);
        }
    }

    /**
     * Load all opportunities from JSON files
     */
    private function load_all_opportunities() {
        $countries = array('ghana', 'kenya', 'nigeria', 'south_africa');
        $all_opportunities = array();

        foreach ($countries as $country) {
            $opportunities = load_opportunities_by_country_sector(ucfirst(str_replace('_', ' ', $country)));
            if (!empty($opportunities)) {
                $all_opportunities = array_merge($all_opportunities, $opportunities);
            }
        }

        return $all_opportunities;
    }

    /**
     * Find opportunities matching alert criteria
     */
    private function find_matching_opportunities($alert, $all_opportunities) {
        $matching = array();

        foreach ($all_opportunities as $opportunity) {
            $match_score = $this->calculate_match_score($alert, $opportunity);

            if ($match_score > 0) {
                $opportunity['match_score'] = $match_score;
                $matching[] = $opportunity;
            }
        }

        // Sort by match score (highest first)
        usort($matching, function($a, $b) {
            return $b['match_score'] <=> $a['match_score'];
        });

        return $matching;
    }

    /**
     * Calculate match score for opportunity against alert criteria
     */
    private function calculate_match_score($alert, $opportunity) {
        $score = 0;
        $max_score = 0;

        // Country match (weight: 20)
        $max_score += 20;
        if (empty($alert['countries']) || in_array($opportunity['country'], $alert['countries'])) {
            $score += 20;
        } else {
            return 0; // Country is mandatory
        }

        // Opportunity type match (weight: 15)
        $max_score += 15;
        if (empty($alert['opportunity_types']) || in_array($opportunity['type'], $alert['opportunity_types'])) {
            $score += 15;
        }

        // Sector match (weight: 15)
        $max_score += 15;
        if (empty($alert['sectors']) || in_array($opportunity['sector'], $alert['sectors'])) {
            $score += 15;
        }

        // Keywords match (weight: 20)
        $max_score += 20;
        if (!empty($alert['keywords'])) {
            $keywords = array_map('trim', explode(',', strtolower($alert['keywords'])));
            $opportunity_text = strtolower($opportunity['title'] . ' ' . $opportunity['summary']);

            $keyword_matches = 0;
            foreach ($keywords as $keyword) {
                if (strpos($opportunity_text, $keyword) !== false) {
                    $keyword_matches++;
                }
            }

            if ($keyword_matches > 0) {
                $score += min(20, ($keyword_matches / count($keywords)) * 20);
            }
        } else {
            $score += 20; // No keywords specified, full score
        }

        // Amount range match (weight: 10)
        $max_score += 10;
        if (!empty($opportunity['amount']) && (!empty($alert['amount_min']) || !empty($alert['amount_max']))) {
            // Extract numeric value from amount string
            preg_match('/[\d,]+/', $opportunity['amount'], $matches);
            if (!empty($matches[0])) {
                $amount = floatval(str_replace(',', '', $matches[0]));

                $in_range = true;
                if (!empty($alert['amount_min']) && $amount < $alert['amount_min']) {
                    $in_range = false;
                }
                if (!empty($alert['amount_max']) && $amount > $alert['amount_max']) {
                    $in_range = false;
                }

                if ($in_range) {
                    $score += 10;
                }
            }
        } else {
            $score += 10; // No amount criteria or amount not specified
        }

        // Deadline proximity bonus (weight: 10)
        $max_score += 10;
        if (!empty($opportunity['deadline']) && !empty($alert['deadline_days'])) {
            $deadline = new DateTime($opportunity['deadline']);
            $now = new DateTime();
            $days_until_deadline = $now->diff($deadline)->days;

            if ($days_until_deadline <= $alert['deadline_days']) {
                $score += 10;
            }
        } else {
            $score += 10; // No deadline criteria
        }

        // Return percentage score
        return ($score / $max_score) * 100;
    }

    /**
     * Filter out opportunities that have already been sent
     */
    private function filter_new_opportunities($alert_id, $opportunities) {
        global $wpdb;

        $new_opportunities = array();

        foreach ($opportunities as $opportunity) {
            // Create a unique hash for the opportunity
            $opportunity_hash = md5(serialize($opportunity));

            // Check if this opportunity has been sent for this alert
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->alert_matches_table}
                WHERE alert_id = %d AND JSON_EXTRACT(opportunity_data, '$.hash') = %s AND is_sent = 1",
                $alert_id, $opportunity_hash
            ));

            if (!$exists) {
                $opportunity['hash'] = $opportunity_hash;
                $new_opportunities[] = $opportunity;
            }
        }

        return $new_opportunities;
    }

    /**
     * Mark opportunities as sent
     */
    private function mark_opportunities_as_sent($alert_id, $opportunities) {
        global $wpdb;

        foreach ($opportunities as $opportunity) {
            $wpdb->insert($this->alert_matches_table, array(
                'user_id' => $opportunity['user_id'] ?? 0,
                'alert_id' => $alert_id,
                'opportunity_data' => json_encode($opportunity),
                'match_score' => $opportunity['match_score'] ?? 0,
                'is_sent' => 1
            ));
        }
    }

    /**
     * Send daily alert digest
     */
    public function send_daily_alert_digest() {
        global $wpdb;

        // Get users with daily digest preference
        $daily_alerts = $wpdb->get_results(
            "SELECT * FROM {$this->alerts_table} WHERE is_active = 1 AND notification_frequency = 'daily'",
            ARRAY_A
        );

        if (empty($daily_alerts)) {
            return;
        }

        $all_opportunities = $this->load_all_opportunities();

        // Group alerts by user
        $user_alerts = array();
        foreach ($daily_alerts as $alert) {
            $user_alerts[$alert['user_id']][] = $alert;
        }

        foreach ($user_alerts as $user_id => $alerts) {
            $this->send_digest_for_user($user_id, $alerts, $all_opportunities, 'daily');
        }
    }

    /**
     * Send weekly alert summary
     */
    public function send_weekly_alert_summary() {
        global $wpdb;

        // Get users with weekly summary preference
        $weekly_alerts = $wpdb->get_results(
            "SELECT * FROM {$this->alerts_table} WHERE is_active = 1 AND notification_frequency = 'weekly'",
            ARRAY_A
        );

        if (empty($weekly_alerts)) {
            return;
        }

        $all_opportunities = $this->load_all_opportunities();

        // Group alerts by user
        $user_alerts = array();
        foreach ($weekly_alerts as $alert) {
            $user_alerts[$alert['user_id']][] = $alert;
        }

        foreach ($user_alerts as $user_id => $alerts) {
            $this->send_digest_for_user($user_id, $alerts, $all_opportunities, 'weekly');
        }
    }

    /**
     * Send digest email for user
     */
    private function send_digest_for_user($user_id, $alerts, $all_opportunities, $digest_type) {
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return;
        }

        $all_matches = array();

        foreach ($alerts as $alert) {
            // Decode JSON fields
            $alert['countries'] = !empty($alert['countries']) ? json_decode($alert['countries'], true) : array();
            $alert['opportunity_types'] = !empty($alert['opportunity_types']) ? json_decode($alert['opportunity_types'], true) : array();
            $alert['sectors'] = !empty($alert['sectors']) ? json_decode($alert['sectors'], true) : array();

            $matches = $this->find_matching_opportunities($alert, $all_opportunities);

            if (!empty($matches)) {
                $all_matches[$alert['filter_name']] = array(
                    'alert' => $alert,
                    'opportunities' => array_slice($matches, 0, 5) // Limit to top 5 per alert
                );
            }
        }

        if (empty($all_matches)) {
            return;
        }

        // Send digest email
        $sendpulse = chatgabi_get_sendpulse();
        $result = $sendpulse->send_digest_email(
            $user->user_email,
            $user->display_name ?: $user->user_login,
            $all_matches,
            $digest_type
        );
    }

    /**
     * Cleanup old alert logs
     */
    public function cleanup_old_alert_logs() {
        global $wpdb;

        // Delete logs older than 90 days
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$this->alert_logs_table} WHERE sent_at < %s",
            date('Y-m-d H:i:s', strtotime('-90 days'))
        ));

        // Delete old matches older than 30 days
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$this->alert_matches_table} WHERE created_at < %s",
            date('Y-m-d H:i:s', strtotime('-30 days'))
        ));
    }

    /**
     * AJAX: Save alert subscription
     */
    public function ajax_save_alert_subscription() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_alert_nonce')) {
            wp_die(__('Security check failed', 'chatgabi'));
        }

        // Check user authentication
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in', 'chatgabi'));
        }

        $user_id = get_current_user_id();
        $filter_data = $_POST['filter_data'];

        $result = $this->save_alert_subscription($user_id, $filter_data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message' => __('Alert subscription saved successfully', 'chatgabi'),
                'alert_id' => $result
            ));
        }
    }

    /**
     * AJAX: Delete alert subscription
     */
    public function ajax_delete_alert_subscription() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_alert_nonce')) {
            wp_die(__('Security check failed', 'chatgabi'));
        }

        // Check user authentication
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in', 'chatgabi'));
        }

        $user_id = get_current_user_id();
        $alert_id = intval($_POST['alert_id']);

        $result = $this->delete_alert_subscription($user_id, $alert_id);

        if ($result) {
            wp_send_json_success(__('Alert subscription deleted successfully', 'chatgabi'));
        } else {
            wp_send_json_error(__('Failed to delete alert subscription', 'chatgabi'));
        }
    }

    /**
     * AJAX: Toggle alert subscription
     */
    public function ajax_toggle_alert_subscription() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_alert_nonce')) {
            wp_die(__('Security check failed', 'chatgabi'));
        }

        // Check user authentication
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in', 'chatgabi'));
        }

        $user_id = get_current_user_id();
        $alert_id = intval($_POST['alert_id']);
        $is_active = intval($_POST['is_active']);

        $result = $this->toggle_alert_subscription($user_id, $alert_id, $is_active);

        if ($result) {
            wp_send_json_success(__('Alert subscription updated successfully', 'chatgabi'));
        } else {
            wp_send_json_error(__('Failed to update alert subscription', 'chatgabi'));
        }
    }

    /**
     * AJAX: Preview alert matches
     */
    public function ajax_preview_alert_matches() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_alert_nonce')) {
            wp_die(__('Security check failed', 'chatgabi'));
        }

        // Check user authentication
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in', 'chatgabi'));
        }

        $filter_data = $_POST['filter_data'];

        // Create temporary alert for preview
        $temp_alert = array(
            'countries' => $filter_data['countries'] ?? array(),
            'opportunity_types' => $filter_data['opportunity_types'] ?? array(),
            'sectors' => $filter_data['sectors'] ?? array(),
            'amount_min' => $filter_data['amount_min'] ?? null,
            'amount_max' => $filter_data['amount_max'] ?? null,
            'keywords' => $filter_data['keywords'] ?? null,
            'deadline_days' => $filter_data['deadline_days'] ?? null,
        );

        $all_opportunities = $this->load_all_opportunities();
        $matches = $this->find_matching_opportunities($temp_alert, $all_opportunities);

        // Limit to top 10 for preview
        $preview_matches = array_slice($matches, 0, 10);

        wp_send_json_success(array(
            'total_matches' => count($matches),
            'preview_opportunities' => $preview_matches
        ));
    }

    /**
     * AJAX: Get alert statistics
     */
    public function ajax_get_alert_statistics() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_alert_nonce')) {
            wp_die(__('Security check failed', 'chatgabi'));
        }

        // Check user authentication
        if (!is_user_logged_in()) {
            wp_send_json_error(__('You must be logged in', 'chatgabi'));
        }

        $user_id = get_current_user_id();
        $stats = $this->get_user_alert_statistics($user_id);

        wp_send_json_success($stats);
    }

    /**
     * Get user alert statistics
     */
    private function get_user_alert_statistics($user_id) {
        global $wpdb;

        // Get total matches from last 30 days
        $total_matches = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->alert_matches_table}
            WHERE user_id = %d AND created_at >= %s",
            $user_id, date('Y-m-d H:i:s', strtotime('-30 days'))
        ));

        // Get emails sent from last 30 days
        $emails_sent = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->alert_logs_table}
            WHERE user_id = %d AND sent_at >= %s",
            $user_id, date('Y-m-d H:i:s', strtotime('-30 days'))
        ));

        // Calculate open rate
        $emails_opened = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->alert_logs_table}
            WHERE user_id = %d AND opened_at IS NOT NULL AND sent_at >= %s",
            $user_id, date('Y-m-d H:i:s', strtotime('-30 days'))
        ));

        $open_rate = $emails_sent > 0 ? round(($emails_opened / $emails_sent) * 100, 1) : 0;

        return array(
            'total_matches' => intval($total_matches),
            'emails_sent' => intval($emails_sent),
            'open_rate' => $open_rate
        );
    }

    /**
     * Force reschedule all cron jobs (for troubleshooting)
     */
    public function force_reschedule_cron_jobs() {
        // Clear all existing cron jobs
        wp_clear_scheduled_hook('chatgabi_process_opportunity_alerts');
        wp_clear_scheduled_hook('chatgabi_send_daily_alert_digest');
        wp_clear_scheduled_hook('chatgabi_send_weekly_alert_summary');
        wp_clear_scheduled_hook('chatgabi_cleanup_old_alert_logs');

        // Force schedule all jobs
        $this->schedule_cron_jobs();

        return $this->get_cron_status();
    }

    /**
     * Get status of all cron jobs
     */
    public function get_cron_status() {
        $cron_jobs = array(
            'chatgabi_process_opportunity_alerts' => 'Process Opportunity Alerts',
            'chatgabi_send_daily_alert_digest' => 'Send Daily Alert Digest',
            'chatgabi_send_weekly_alert_summary' => 'Send Weekly Alert Summary',
            'chatgabi_cleanup_old_alert_logs' => 'Cleanup Old Alert Logs'
        );

        $status = array();
        foreach ($cron_jobs as $hook => $description) {
            $next_run = wp_next_scheduled($hook);
            $status[$hook] = array(
                'description' => $description,
                'scheduled' => $next_run !== false,
                'next_run' => $next_run ? date('Y-m-d H:i:s', $next_run) : 'Not scheduled',
                'schedule' => $next_run ? wp_get_schedule($hook) : 'None'
            );
        }

        return $status;
    }
}

// Initialize the opportunity alerts system
new ChatGABI_Opportunity_Alerts();

/**
 * Get opportunity alerts instance
 */
function chatgabi_get_opportunity_alerts() {
    return new ChatGABI_Opportunity_Alerts();
}

/**
 * Handle unsubscribe requests
 */
function chatgabi_handle_unsubscribe() {
    if (!isset($_GET['action']) || $_GET['action'] !== 'chatgabi_unsubscribe_alert') {
        return;
    }

    $alert_id = intval($_GET['alert_id']);
    $email = sanitize_email($_GET['email']);
    $token = sanitize_text_field($_GET['token']);

    // Verify token
    if (!wp_verify_nonce($token, 'unsubscribe_' . $alert_id . '_' . $email)) {
        wp_die(__('Invalid unsubscribe link', 'chatgabi'));
    }

    global $wpdb;
    $alerts_table = $wpdb->prefix . 'chatgabi_opportunity_alerts';

    // Find user by email
    $user = get_user_by('email', $email);
    if (!$user) {
        wp_die(__('User not found', 'chatgabi'));
    }

    // Deactivate the alert
    $result = $wpdb->update(
        $alerts_table,
        array('is_active' => 0),
        array('id' => $alert_id, 'user_id' => $user->ID),
        array('%d'),
        array('%d', '%d')
    );

    if ($result !== false) {
        // Log unsubscribe
        $logs_table = $wpdb->prefix . 'chatgabi_alert_logs';
        $wpdb->update(
            $logs_table,
            array('unsubscribed_at' => current_time('mysql')),
            array('alert_id' => $alert_id, 'email_address' => $email),
            array('%s'),
            array('%d', '%s')
        );

        // Show success message
        wp_die(__('You have been successfully unsubscribed from this alert.', 'chatgabi'), __('Unsubscribed', 'chatgabi'), array('response' => 200));
    } else {
        wp_die(__('Failed to unsubscribe. Please try again.', 'chatgabi'));
    }
}
add_action('init', 'chatgabi_handle_unsubscribe');

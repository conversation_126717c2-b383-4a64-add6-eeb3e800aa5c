<?php
/**
 * BusinessCraft AI - SendPulse API Integration
 * 
 * Handles email delivery for opportunity alerts using SendPulse API
 * with delivery tracking, engagement analytics, and error handling.
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SendPulse Integration Class
 */
class ChatGABI_SendPulse_Integration {
    
    private $api_url = 'https://api.sendpulse.com';
    private $api_user_id;
    private $api_secret;
    private $access_token;
    private $token_expires;
    
    public function __construct() {
        $this->api_user_id = get_option('chatgabi_sendpulse_user_id', '');
        $this->api_secret = get_option('chatgabi_sendpulse_secret', '');
        
        // Initialize hooks
        add_action('init', array($this, 'init_hooks'));
        add_action('wp_ajax_chatgabi_test_sendpulse_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_chatgabi_save_sendpulse_settings', array($this, 'ajax_save_settings'));
    }
    
    /**
     * Initialize hooks
     */
    public function init_hooks() {
        // Add admin menu for SendPulse settings (priority 15 to ensure main menu exists first)
        add_action('admin_menu', array($this, 'add_admin_menu'), 15);

        // Handle webhook callbacks
        add_action('wp_ajax_nopriv_chatgabi_sendpulse_webhook', array($this, 'handle_webhook'));
        add_action('wp_ajax_chatgabi_sendpulse_webhook', array($this, 'handle_webhook'));
    }
    
    /**
     * Add admin menu for SendPulse settings
     */
    public function add_admin_menu() {
        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('ChatGABI SendPulse: Attempting to add admin menu');
        }

        $result = add_submenu_page(
            'chatgabi-main',
            __('SendPulse Settings', 'chatgabi'),
            __('Email Settings', 'chatgabi'),
            'manage_options',
            'chatgabi-sendpulse',
            array($this, 'admin_page')
        );

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            if ($result) {
                error_log('ChatGABI SendPulse: Admin menu added successfully');
            } else {
                error_log('ChatGABI SendPulse: Failed to add admin menu');
            }
        }

        return $result;
    }
    
    /**
     * Get access token from SendPulse API
     */
    private function get_access_token() {
        // Check if we have a valid token
        if ($this->access_token && $this->token_expires && time() < $this->token_expires) {
            return $this->access_token;
        }
        
        // Request new token
        $response = wp_remote_post($this->api_url . '/oauth/access_token', array(
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'grant_type' => 'client_credentials',
                'client_id' => $this->api_user_id,
                'client_secret' => $this->api_secret,
            )),
            'timeout' => 30,
        ));
        
        if (is_wp_error($response)) {
            error_log('ChatGABI SendPulse: Token request failed - ' . $response->get_error_message());
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (empty($data['access_token'])) {
            error_log('ChatGABI SendPulse: Invalid token response - ' . $body);
            return false;
        }
        
        $this->access_token = $data['access_token'];
        $this->token_expires = time() + ($data['expires_in'] - 60); // Subtract 60 seconds for safety
        
        return $this->access_token;
    }
    
    /**
     * Send opportunity alert email
     */
    public function send_opportunity_alert($user_email, $user_name, $alert_data, $opportunities, $email_type = 'immediate') {
        $token = $this->get_access_token();
        if (!$token) {
            return new WP_Error('auth_failed', __('Failed to authenticate with SendPulse', 'chatgabi'));
        }
        
        // Generate email content
        $email_content = $this->generate_email_content($alert_data, $opportunities, $email_type);
        
        // Prepare email data
        $email_data = array(
            'html' => $email_content['html'],
            'text' => $email_content['text'],
            'subject' => $email_content['subject'],
            'from' => array(
                'name' => get_option('blogname', 'BusinessCraft AI'),
                'email' => get_option('admin_email'),
            ),
            'to' => array(
                array(
                    'name' => $user_name,
                    'email' => $user_email,
                )
            ),
            'template' => array(
                'id' => get_option('chatgabi_sendpulse_template_id', ''),
                'variables' => array(
                    'user_name' => $user_name,
                    'alert_name' => $alert_data['filter_name'],
                    'opportunities_count' => count($opportunities),
                    'opportunities_html' => $email_content['opportunities_html'],
                    'unsubscribe_url' => $this->generate_unsubscribe_url($alert_data['id'], $user_email),
                )
            )
        );
        
        // Send email via SendPulse API
        $response = wp_remote_post($this->api_url . '/smtp/emails', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($email_data),
            'timeout' => 30,
        ));
        
        if (is_wp_error($response)) {
            error_log('ChatGABI SendPulse: Email send failed - ' . $response->get_error_message());
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($response_code !== 200) {
            error_log('ChatGABI SendPulse: Email send error - ' . $body);
            return new WP_Error('send_failed', __('Failed to send email', 'chatgabi'), $data);
        }
        
        // Log successful send
        $this->log_email_delivery($alert_data, $user_email, $email_content['subject'], count($opportunities), $email_type, $data);
        
        return array(
            'success' => true,
            'message_id' => $data['id'] ?? null,
            'data' => $data
        );
    }
    
    /**
     * Generate email content for opportunity alerts
     */
    private function generate_email_content($alert_data, $opportunities, $email_type) {
        $subject_prefix = '';
        $intro_text = '';
        
        switch ($email_type) {
            case 'immediate':
                $subject_prefix = __('New Opportunity Alert', 'chatgabi');
                $intro_text = __('We found new opportunities matching your criteria:', 'chatgabi');
                break;
            case 'daily':
                $subject_prefix = __('Daily Opportunity Digest', 'chatgabi');
                $intro_text = __('Here are today\'s opportunities matching your criteria:', 'chatgabi');
                break;
            case 'weekly':
                $subject_prefix = __('Weekly Opportunity Summary', 'chatgabi');
                $intro_text = __('Here\'s your weekly summary of opportunities:', 'chatgabi');
                break;
        }
        
        $subject = sprintf('%s: %s (%d %s)', 
            $subject_prefix, 
            $alert_data['filter_name'], 
            count($opportunities),
            _n('opportunity', 'opportunities', count($opportunities), 'chatgabi')
        );
        
        // Generate opportunities HTML
        $opportunities_html = '';
        foreach ($opportunities as $opportunity) {
            $opportunities_html .= $this->format_opportunity_html($opportunity);
        }
        
        // Generate HTML email
        $html = $this->get_email_template('opportunity_alert', array(
            'intro_text' => $intro_text,
            'alert_name' => $alert_data['filter_name'],
            'opportunities_html' => $opportunities_html,
            'opportunities_count' => count($opportunities),
            'unsubscribe_url' => $this->generate_unsubscribe_url($alert_data['id'], '{{email}}'),
        ));
        
        // Generate plain text email
        $text = $this->generate_text_email($intro_text, $alert_data, $opportunities);
        
        return array(
            'subject' => $subject,
            'html' => $html,
            'text' => $text,
            'opportunities_html' => $opportunities_html
        );
    }
    
    /**
     * Format single opportunity as HTML
     */
    private function format_opportunity_html($opportunity) {
        $deadline_text = '';
        if (!empty($opportunity['deadline'])) {
            $deadline = new DateTime($opportunity['deadline']);
            $now = new DateTime();
            $diff = $now->diff($deadline);
            
            if ($diff->days <= 7) {
                $deadline_text = sprintf('<span style="color: #e74c3c; font-weight: bold;">⚠️ Deadline: %s (%d days left)</span>', 
                    $deadline->format('M j, Y'), $diff->days);
            } else {
                $deadline_text = sprintf('<span style="color: #7f8c8d;">Deadline: %s</span>', 
                    $deadline->format('M j, Y'));
            }
        }
        
        $amount_text = '';
        if (!empty($opportunity['amount'])) {
            $amount_text = sprintf('<div style="color: #27ae60; font-weight: bold;">💰 %s</div>', 
                esc_html($opportunity['amount']));
        }
        
        return sprintf('
            <div style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; margin-bottom: 20px; background: #ffffff;">
                <h3 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px;">
                    <a href="%s" style="color: #3498db; text-decoration: none;">%s</a>
                </h3>
                <div style="color: #7f8c8d; font-size: 14px; margin-bottom: 10px;">
                    📍 %s | 🏢 %s | 📋 %s
                </div>
                <p style="color: #34495e; line-height: 1.6; margin: 10px 0;">%s</p>
                %s
                %s
                <div style="margin-top: 15px;">
                    <a href="%s" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        View Details →
                    </a>
                </div>
            </div>',
            esc_url($opportunity['source'] ?? '#'),
            esc_html($opportunity['title']),
            esc_html($opportunity['country']),
            esc_html($opportunity['sector']),
            esc_html($opportunity['type']),
            esc_html($opportunity['summary']),
            $amount_text,
            $deadline_text,
            esc_url($opportunity['source'] ?? '#')
        );
    }
    
    /**
     * Generate unsubscribe URL
     */
    private function generate_unsubscribe_url($alert_id, $email) {
        $token = wp_create_nonce('unsubscribe_' . $alert_id . '_' . $email);
        return add_query_arg(array(
            'action' => 'chatgabi_unsubscribe_alert',
            'alert_id' => $alert_id,
            'email' => urlencode($email),
            'token' => $token
        ), home_url());
    }
    
    /**
     * Log email delivery
     */
    private function log_email_delivery($alert_data, $email, $subject, $opportunities_count, $email_type, $response_data) {
        global $wpdb;
        
        $alerts_instance = chatgabi_get_opportunity_alerts();
        $logs_table = $wpdb->prefix . 'chatgabi_alert_logs';
        
        $wpdb->insert($logs_table, array(
            'user_id' => $alert_data['user_id'],
            'alert_id' => $alert_data['id'],
            'email_address' => $email,
            'email_subject' => $subject,
            'opportunities_count' => $opportunities_count,
            'email_type' => $email_type,
            'delivery_status' => 'sent',
            'sendpulse_message_id' => $response_data['id'] ?? null,
        ));
    }
    
    /**
     * Handle SendPulse webhooks for delivery tracking
     */
    public function handle_webhook() {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (empty($data['event']) || empty($data['email'])) {
            wp_die('Invalid webhook data', 'Webhook Error', array('response' => 400));
        }
        
        global $wpdb;
        $logs_table = $wpdb->prefix . 'chatgabi_alert_logs';
        
        // Update delivery status based on event
        switch ($data['event']) {
            case 'delivered':
                $wpdb->update($logs_table, 
                    array('delivery_status' => 'delivered'),
                    array('sendpulse_message_id' => $data['message_id'])
                );
                break;
                
            case 'opened':
                $wpdb->update($logs_table, 
                    array('opened_at' => current_time('mysql')),
                    array('sendpulse_message_id' => $data['message_id'])
                );
                break;
                
            case 'clicked':
                $wpdb->update($logs_table, 
                    array('clicked_at' => current_time('mysql')),
                    array('sendpulse_message_id' => $data['message_id'])
                );
                break;
                
            case 'unsubscribed':
                $wpdb->update($logs_table, 
                    array('unsubscribed_at' => current_time('mysql')),
                    array('sendpulse_message_id' => $data['message_id'])
                );
                break;
                
            case 'bounced':
            case 'spam':
                $wpdb->update($logs_table, 
                    array(
                        'delivery_status' => $data['event'],
                        'error_message' => $data['reason'] ?? ''
                    ),
                    array('sendpulse_message_id' => $data['message_id'])
                );
                break;
        }
        
        wp_die('OK', 'Webhook Processed', array('response' => 200));
    }

    /**
     * Send digest email (daily/weekly)
     */
    public function send_digest_email($user_email, $user_name, $alert_matches, $digest_type) {
        $token = $this->get_access_token();
        if (!$token) {
            return new WP_Error('auth_failed', __('Failed to authenticate with SendPulse', 'chatgabi'));
        }

        // Generate digest content
        $email_content = $this->generate_digest_content($alert_matches, $digest_type);

        // Prepare email data
        $email_data = array(
            'html' => $email_content['html'],
            'text' => $email_content['text'],
            'subject' => $email_content['subject'],
            'from' => array(
                'name' => get_option('blogname', 'BusinessCraft AI'),
                'email' => get_option('admin_email'),
            ),
            'to' => array(
                array(
                    'name' => $user_name,
                    'email' => $user_email,
                )
            ),
        );

        // Send email via SendPulse API
        $response = wp_remote_post($this->api_url . '/smtp/emails', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($email_data),
            'timeout' => 30,
        ));

        if (is_wp_error($response)) {
            error_log('ChatGABI SendPulse: Digest email send failed - ' . $response->get_error_message());
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if ($response_code !== 200) {
            error_log('ChatGABI SendPulse: Digest email send error - ' . $body);
            return new WP_Error('send_failed', __('Failed to send digest email', 'chatgabi'), $data);
        }

        return array(
            'success' => true,
            'message_id' => $data['id'] ?? null,
            'data' => $data
        );
    }

    /**
     * Generate digest email content
     */
    private function generate_digest_content($alert_matches, $digest_type) {
        $total_opportunities = 0;
        foreach ($alert_matches as $match_data) {
            $total_opportunities += count($match_data['opportunities']);
        }

        $subject_prefix = $digest_type === 'daily' ? __('Daily Opportunity Digest', 'chatgabi') : __('Weekly Opportunity Summary', 'chatgabi');
        $subject = sprintf('%s: %d %s',
            $subject_prefix,
            $total_opportunities,
            _n('opportunity', 'opportunities', $total_opportunities, 'chatgabi')
        );

        $intro_text = $digest_type === 'daily'
            ? __('Here are today\'s opportunities matching your alert criteria:', 'chatgabi')
            : __('Here\'s your weekly summary of opportunities:', 'chatgabi');

        // Generate HTML content
        $html_content = '';
        foreach ($alert_matches as $filter_name => $match_data) {
            $html_content .= sprintf('
                <div style="margin-bottom: 30px; border-bottom: 2px solid #e0e0e0; padding-bottom: 20px;">
                    <h2 style="color: #2c3e50; margin-bottom: 15px;">📋 %s (%d %s)</h2>',
                esc_html($filter_name),
                count($match_data['opportunities']),
                _n('opportunity', 'opportunities', count($match_data['opportunities']), 'chatgabi')
            );

            foreach ($match_data['opportunities'] as $opportunity) {
                $html_content .= $this->format_opportunity_html($opportunity);
            }

            $html_content .= '</div>';
        }

        $html = $this->get_email_template('opportunity_digest', array(
            'intro_text' => $intro_text,
            'digest_type' => $digest_type,
            'total_opportunities' => $total_opportunities,
            'content_html' => $html_content,
        ));

        // Generate plain text
        $text = $this->generate_digest_text_email($intro_text, $alert_matches, $digest_type);

        return array(
            'subject' => $subject,
            'html' => $html,
            'text' => $text
        );
    }

    /**
     * Generate plain text email for opportunities
     */
    private function generate_text_email($intro_text, $alert_data, $opportunities) {
        $text = $intro_text . "\n\n";
        $text .= sprintf("Alert: %s\n", $alert_data['filter_name']);
        $text .= str_repeat("=", 50) . "\n\n";

        foreach ($opportunities as $opportunity) {
            $text .= sprintf("📋 %s\n", $opportunity['title']);
            $text .= sprintf("📍 %s | 🏢 %s | 📋 %s\n", $opportunity['country'], $opportunity['sector'], $opportunity['type']);
            $text .= sprintf("%s\n", $opportunity['summary']);

            if (!empty($opportunity['amount'])) {
                $text .= sprintf("💰 %s\n", $opportunity['amount']);
            }

            if (!empty($opportunity['deadline'])) {
                $text .= sprintf("⏰ Deadline: %s\n", $opportunity['deadline']);
            }

            if (!empty($opportunity['source'])) {
                $text .= sprintf("🔗 %s\n", $opportunity['source']);
            }

            $text .= "\n" . str_repeat("-", 30) . "\n\n";
        }

        $text .= sprintf("\nManage your alerts: %s\n", home_url('/dashboard/alerts/'));

        return $text;
    }

    /**
     * Generate plain text digest email
     */
    private function generate_digest_text_email($intro_text, $alert_matches, $digest_type) {
        $text = $intro_text . "\n\n";

        foreach ($alert_matches as $filter_name => $match_data) {
            $text .= sprintf("📋 %s (%d %s)\n",
                $filter_name,
                count($match_data['opportunities']),
                _n('opportunity', 'opportunities', count($match_data['opportunities']), 'chatgabi')
            );
            $text .= str_repeat("=", 50) . "\n\n";

            foreach ($match_data['opportunities'] as $opportunity) {
                $text .= sprintf("• %s\n", $opportunity['title']);
                $text .= sprintf("  📍 %s | 🏢 %s\n", $opportunity['country'], $opportunity['sector']);
                $text .= sprintf("  %s\n", substr($opportunity['summary'], 0, 100) . '...');

                if (!empty($opportunity['source'])) {
                    $text .= sprintf("  🔗 %s\n", $opportunity['source']);
                }

                $text .= "\n";
            }

            $text .= "\n";
        }

        $text .= sprintf("\nManage your alerts: %s\n", home_url('/dashboard/alerts/'));

        return $text;
    }

    /**
     * Get email template
     */
    private function get_email_template($template_type, $variables) {
        $default_template = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{subject}}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #3498db; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { background: #34495e; color: white; padding: 15px; text-align: center; font-size: 12px; }
                .opportunity { border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; margin-bottom: 20px; background: white; }
                .btn { background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 BusinessCraft AI</h1>
                    <p>African Business Opportunities</p>
                </div>
                <div class="content">
                    {{content}}
                </div>
                <div class="footer">
                    <p>© ' . date('Y') . ' BusinessCraft AI. All rights reserved.</p>
                    <p><a href="{{unsubscribe_url}}" style="color: #bdc3c7;">Unsubscribe</a></p>
                </div>
            </div>
        </body>
        </html>';

        // Replace template variables
        foreach ($variables as $key => $value) {
            $default_template = str_replace('{{' . $key . '}}', $value, $default_template);
        }

        return $default_template;
    }

    /**
     * Test SendPulse connection
     */
    public function test_connection() {
        $token = $this->get_access_token();
        if (!$token) {
            return new WP_Error('auth_failed', __('Failed to authenticate with SendPulse', 'chatgabi'));
        }

        // Test by getting account info
        $response = wp_remote_get($this->api_url . '/smtp/emails/statistics', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $token,
            ),
            'timeout' => 30,
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code === 200) {
            return array('success' => true, 'message' => __('Connection successful', 'chatgabi'));
        } else {
            return new WP_Error('connection_failed', __('Connection test failed', 'chatgabi'));
        }
    }

    /**
     * AJAX: Test connection
     */
    public function ajax_test_connection() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'chatgabi'));
        }

        $result = $this->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success($result['message']);
        }
    }

    /**
     * AJAX: Save SendPulse settings
     */
    public function ajax_save_settings() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'chatgabi'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_sendpulse_settings')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }

        $user_id = sanitize_text_field($_POST['user_id']);
        $secret = sanitize_text_field($_POST['secret']);
        $template_id = sanitize_text_field($_POST['template_id']);

        update_option('chatgabi_sendpulse_user_id', $user_id);
        update_option('chatgabi_sendpulse_secret', $secret);
        update_option('chatgabi_sendpulse_template_id', $template_id);

        wp_send_json_success(__('Settings saved successfully', 'chatgabi'));
    }

    /**
     * Admin page for SendPulse settings
     */
    public function admin_page() {
        $user_id = get_option('chatgabi_sendpulse_user_id', '');
        $secret = get_option('chatgabi_sendpulse_secret', '');
        $template_id = get_option('chatgabi_sendpulse_template_id', '');

        ?>
        <div class="wrap">
            <h1><?php _e('SendPulse Email Settings', 'chatgabi'); ?></h1>

            <form id="sendpulse-settings-form">
                <?php wp_nonce_field('chatgabi_sendpulse_settings', 'nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('API User ID', 'chatgabi'); ?></th>
                        <td>
                            <input type="text" name="user_id" value="<?php echo esc_attr($user_id); ?>" class="regular-text" />
                            <p class="description"><?php _e('Your SendPulse API User ID', 'chatgabi'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Secret', 'chatgabi'); ?></th>
                        <td>
                            <input type="password" name="secret" value="<?php echo esc_attr($secret); ?>" class="regular-text" />
                            <p class="description"><?php _e('Your SendPulse API Secret', 'chatgabi'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Email Template ID', 'chatgabi'); ?></th>
                        <td>
                            <input type="text" name="template_id" value="<?php echo esc_attr($template_id); ?>" class="regular-text" />
                            <p class="description"><?php _e('Optional: SendPulse email template ID for custom styling', 'chatgabi'); ?></p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="submit" class="button-primary"><?php _e('Save Settings', 'chatgabi'); ?></button>
                    <button type="button" id="test-connection" class="button"><?php _e('Test Connection', 'chatgabi'); ?></button>
                </p>
            </form>

            <div id="test-result" style="margin-top: 20px;"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#sendpulse-settings-form').on('submit', function(e) {
                e.preventDefault();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'chatgabi_save_sendpulse_settings',
                        user_id: $('input[name="user_id"]').val(),
                        secret: $('input[name="secret"]').val(),
                        template_id: $('input[name="template_id"]').val(),
                        nonce: $('input[name="nonce"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#test-result').html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                        } else {
                            $('#test-result').html('<div class="notice notice-error"><p>' + response.data + '</p></div>');
                        }
                    }
                });
            });

            $('#test-connection').on('click', function() {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'chatgabi_test_sendpulse_connection'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#test-result').html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                        } else {
                            $('#test-result').html('<div class="notice notice-error"><p>' + response.data + '</p></div>');
                        }
                    }
                });
            });
        });
        </script>
        <?php
    }
}

// Initialize SendPulse integration
new ChatGABI_SendPulse_Integration();

/**
 * Get SendPulse integration instance
 */
function chatgabi_get_sendpulse() {
    return new ChatGABI_SendPulse_Integration();
}

<?php
/**
 * BusinessCraft AI Test File
 * Access via: http://your-site.com/businesscraft-ai-test.php
 *
 * This file is placed in WordPress root to avoid routing conflicts
 */

// Load WordPress
require_once('wp-load.php');

// Check if theme is active
if (get_template() !== 'businesscraft-ai') {
    die('BusinessCraft AI theme is not active. Current theme: ' . get_template());
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>BusinessCraft AI - System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-left: 4px solid #0073aa; border-radius: 4px; }
        .success { color: #46b450; font-weight: bold; }
        .error { color: #dc3232; font-weight: bold; }
        .warning { color: #ffb900; font-weight: bold; }
        pre { background: #fff; padding: 10px; border: 1px solid #ddd; overflow-x: auto; border-radius: 4px; font-size: 12px; }
        .button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
        .button:hover { background: #005a87; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 4px; text-align: center; }
        h1 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 BusinessCraft AI - System Test</h1>
        <p><strong>Test URL:</strong> <code><?php echo home_url('/businesscraft-ai-test.php'); ?></code></p>
        <p><strong>Timestamp:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>

        <div class="test-section">
            <h2>📊 System Information</h2>
            <div class="stats">
                <div class="stat-card">
                    <strong>WordPress</strong><br>
                    <?php echo get_bloginfo('version'); ?>
                </div>
                <div class="stat-card">
                    <strong>Theme</strong><br>
                    <?php echo wp_get_theme()->get('Name'); ?>
                </div>
                <div class="stat-card">
                    <strong>PHP</strong><br>
                    <?php echo PHP_VERSION; ?>
                </div>
                <div class="stat-card">
                    <strong>Memory</strong><br>
                    <?php echo ini_get('memory_limit'); ?>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Function Availability</h2>
            <?php
            $functions_to_test = [
                'load_business_dataset_by_country' => 'Data Loader',
                'get_sector_context_by_country' => 'Sector Context',
                'load_opportunities_by_country' => 'Opportunities Loader',
                'businesscraft_ai_build_localized_prompt' => 'Prompt Builder',
                'businesscraft_ai_log_sector_context' => 'Logging System'
            ];

            foreach ($functions_to_test as $function => $description) {
                if (function_exists($function)) {
                    echo "<p class='success'>✅ {$description} - {$function}()</p>";
                } else {
                    echo "<p class='error'>❌ {$description} - {$function}() NOT FOUND</p>";
                }
            }
            ?>
        </div>

        <div class="test-section">
            <h2>📁 File System Check</h2>
            <?php
            $paths_to_check = [
                'Theme Directory' => get_template_directory(),
                'Datasets Directory' => get_template_directory() . '/../../../datasets/',
                'Inc Directory' => get_template_directory() . '/inc/',
                'Data Loader File' => get_template_directory() . '/inc/data-loader.php',
                'Opportunity Loader File' => get_template_directory() . '/inc/opportunity-loader.php'
            ];

            foreach ($paths_to_check as $name => $path) {
                if (file_exists($path)) {
                    echo "<p class='success'>✅ {$name}: <code>{$path}</code></p>";
                } else {
                    echo "<p class='error'>❌ {$name}: <code>{$path}</code> NOT FOUND</p>";
                }
            }
            ?>
        </div>

        <div class="test-section">
            <h2>🌍 Data Loading Test</h2>
            <?php
            if (function_exists('load_business_dataset_by_country')) {
                $countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
                echo "<div class='stats'>";

                foreach ($countries as $country) {
                    $data = load_business_dataset_by_country($country);
                    $sector_count = $data && isset($data['sectors']) ? count($data['sectors']) : 0;

                    echo "<div class='stat-card'>";
                    echo "<strong>{$country}</strong><br>";
                    if ($sector_count > 0) {
                        echo "<span class='success'>{$sector_count} sectors</span>";
                    } else {
                        echo "<span class='error'>No data</span>";
                    }
                    echo "</div>";
                }

                echo "</div>";

                // Test specific sector
                $test_sector = get_sector_context_by_country('Ghana', 'Digital Content & Gaming');
                if ($test_sector) {
                    echo "<p class='success'>✅ Sector context test passed (Ghana - Digital Content & Gaming)</p>";
                    echo "<p><strong>Market Size:</strong> " . ($test_sector['key_conditions']['market_size_and_growth'] ?? 'N/A') . "</p>";
                } else {
                    echo "<p class='error'>❌ Sector context test failed</p>";
                }
            } else {
                echo "<p class='error'>❌ Data loading functions not available</p>";
            }
            ?>
        </div>

        <div class="test-section">
            <h2>💼 Opportunities Test</h2>
            <?php
            if (function_exists('load_opportunities_by_country')) {
                $total_opportunities = 0;
                echo "<div class='stats'>";

                foreach (['Ghana', 'Kenya', 'Nigeria', 'South Africa'] as $country) {
                    $opportunities = load_opportunities_by_country($country);
                    $count = is_array($opportunities) ? count($opportunities) : 0;
                    $total_opportunities += $count;

                    echo "<div class='stat-card'>";
                    echo "<strong>{$country}</strong><br>";
                    if ($count > 0) {
                        echo "<span class='success'>{$count} opportunities</span>";
                    } else {
                        echo "<span class='error'>No opportunities</span>";
                    }
                    echo "</div>";
                }

                echo "</div>";
                echo "<p><strong>Total Opportunities:</strong> <span class='success'>{$total_opportunities}</span></p>";

                // Show sample opportunity
                if ($total_opportunities > 0) {
                    $sample_opps = load_opportunities_by_country('Ghana');
                    if ($sample_opps && count($sample_opps) > 0) {
                        echo "<h3>Sample Opportunity (Ghana):</h3>";
                        echo "<pre>" . htmlspecialchars(json_encode($sample_opps[0], JSON_PRETTY_PRINT)) . "</pre>";
                    }
                }
            } else {
                echo "<p class='error'>❌ Opportunities loading function not available</p>";
            }
            ?>
        </div>

        <div class="test-section">
            <h2>🎯 Quick Actions</h2>
            <a href="<?php echo admin_url('admin.php?page=businesscraft-ai-setup'); ?>" class="button">📊 Admin Dashboard</a>
            <a href="<?php echo home_url(); ?>" class="button">🏠 Homepage</a>
            <a href="<?php echo admin_url(); ?>" class="button">⚙️ WordPress Admin</a>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>?refresh=1" class="button">🔄 Refresh Test</a>
        </div>

        <div class="test-section">
            <h2>📋 Test Summary</h2>
            <?php
            $total_tests = 0;
            $passed_tests = 0;

            // Count function tests
            foreach ($functions_to_test as $function => $description) {
                $total_tests++;
                if (function_exists($function)) $passed_tests++;
            }

            // Count file tests
            foreach ($paths_to_check as $name => $path) {
                $total_tests++;
                if (file_exists($path)) $passed_tests++;
            }

            $success_rate = round(($passed_tests / $total_tests) * 100, 1);
            $status_class = $success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error');
            ?>
            <p class="<?php echo $status_class; ?>">
                <strong>Overall Status:</strong> <?php echo $passed_tests; ?>/<?php echo $total_tests; ?> tests passed (<?php echo $success_rate; ?>%)
            </p>

            <?php if ($success_rate >= 80): ?>
                <p class="success">🎉 System is working well! You can proceed with testing the admin dashboard.</p>
            <?php elseif ($success_rate >= 60): ?>
                <p class="warning">⚠️ Some issues detected. Check the failed tests above.</p>
            <?php else: ?>
                <p class="error">🚨 Multiple issues detected. Please review the system setup.</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

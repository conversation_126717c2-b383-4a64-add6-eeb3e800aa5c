/**
 * Collaboration Styles for BusinessCraft AI
 * 
 * Styles for template sharing and collaboration features
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

/* Share Button */
.share-template-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(to right, #FF9800, #FF5722);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.share-template-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
    color: white;
    text-decoration: none;
}

/* Share Modal */
.share-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.share-modal.active {
    display: flex;
    opacity: 1;
    align-items: center;
    justify-content: center;
}

.share-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.share-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.share-modal.active .share-modal-content {
    transform: scale(1);
}

.share-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.share-modal-header h3 {
    margin: 0;
    color: #3D4E81;
    font-size: 1.4em;
}

.share-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.share-modal-close:hover {
    background-color: #f8f9fa;
    color: #333;
}

.share-modal-body {
    padding: 24px;
}

/* Share Form */
.share-form {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.share-form h4 {
    margin: 0 0 20px 0;
    color: #3D4E81;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

#send-invitation {
    background: linear-gradient(to right, #3D4E81, #5753C9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#send-invitation:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(61, 78, 129, 0.3);
}

/* Current Collaborators */
.current-collaborators h4 {
    margin: 0 0 20px 0;
    color: #3D4E81;
}

.loading-collaborators {
    text-align: center;
    padding: 20px;
    color: #666;
}

.collaborators-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.collaborator-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.collaborator-item:hover {
    border-color: #3D4E81;
    box-shadow: 0 2px 8px rgba(61, 78, 129, 0.1);
}

.collaborator-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.collaborator-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.collaborator-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.collaborator-name {
    font-weight: 600;
    color: #333;
}

.collaborator-email {
    font-size: 0.85em;
    color: #666;
}

.collaborator-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.permission-select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.remove-collaborator-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.remove-collaborator-btn:hover {
    background: #c82333;
}

/* Comments */
.comment-form {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.comment-form h4 {
    margin: 0 0 15px 0;
    color: #3D4E81;
}

#comment-text {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    margin-bottom: 15px;
}

.comment-actions {
    display: flex;
    gap: 10px;
}

.comment-actions button {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.comment-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
}

.comment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.comment-header strong {
    color: #3D4E81;
}

.comment-date {
    font-size: 0.85em;
    color: #666;
}

.comment-content {
    color: #333;
    line-height: 1.6;
    margin-bottom: 10px;
}

.comment-actions {
    display: flex;
    gap: 10px;
}

.reply-comment-btn {
    background: none;
    border: none;
    color: #3D4E81;
    font-size: 12px;
    cursor: pointer;
    text-decoration: underline;
}

.reply-comment-btn:hover {
    color: #2a3660;
}

/* Collaboration Notifications */
.collaboration-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    max-width: 300px;
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.view-invitations-btn {
    background: #2196f3;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.view-invitations-btn:hover {
    background: #1976d2;
}

/* Collaboration Messages */
.collaboration-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    z-index: 10002;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.collaboration-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.collaboration-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Template Actions Enhancement */
.template-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.add-comment-btn,
.view-collaborators-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #6c757d;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.add-comment-btn:hover,
.view-collaborators-btn:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .share-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .collaborator-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .collaborator-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .collaboration-notifications {
        position: relative;
        top: auto;
        right: auto;
        margin: 20px;
        max-width: none;
    }
    
    .template-actions {
        flex-direction: column;
    }
    
    .share-template-btn,
    .add-comment-btn,
    .view-collaborators-btn {
        justify-content: center;
        width: 100%;
    }
}

/* Permission Badges */
.permission-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.permission-badge.view {
    background: #e3f2fd;
    color: #1976d2;
}

.permission-badge.comment {
    background: #fff3e0;
    color: #f57c00;
}

.permission-badge.edit {
    background: #e8f5e8;
    color: #388e3c;
}

/* Collaboration Status */
.collaboration-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    font-size: 0.85em;
    color: #666;
}

.collaboration-status .status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
}

.collaboration-status.pending .status-icon {
    background: #ffc107;
}

.collaboration-status.declined .status-icon {
    background: #dc3545;
}

/* Body modal open state */
body.modal-open {
    overflow: hidden;
}

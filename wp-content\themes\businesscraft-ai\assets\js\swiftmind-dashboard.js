(function($) {
    'use strict';

    $(document).ready(function() {
        // Handle tab switching
        $('.tabs-navigation .tab-button').on('click', function() {
            const tabId = $(this).data('tab');

            // Remove active class from all buttons and panes
            $('.tabs-navigation .tab-button').removeClass('active');
            $('.tabs-content .tab-pane').removeClass('active');

            // Add active class to clicked button and corresponding pane
            $(this).addClass('active');
            $('#' + tabId).addClass('active');
        });

        // Ensure the first tab is active on load
        if ($('.tabs-navigation .tab-button.active').length === 0) {
            $('.tabs-navigation .tab-button').first().addClass('active');
            $('.tabs-content .tab-pane').first().addClass('active');
        }

        // --- Theme Toggle Functionality ---
        const themeToggle = $('#theme-toggle');
        const body = $('body');
        const storedTheme = localStorage.getItem('theme');

        // Apply stored theme on load
        if (storedTheme) {
            body.addClass(storedTheme);
        } else {
            // Default to light theme if no preference is stored
            body.addClass('theme-light');
        }

        // Toggle theme on button click
        themeToggle.on('click', function() {
            if (body.hasClass('theme-light')) {
                body.removeClass('theme-light').addClass('theme-dark');
                localStorage.setItem('theme', 'theme-dark');
            } else {
                body.removeClass('theme-dark').addClass('theme-light');
                localStorage.setItem('theme', 'theme-light');
            }
        });

        // Update theme icon based on current theme
        function updateThemeIcon() {
            if (body.hasClass('theme-dark')) {
                themeToggle.find('.light-icon').hide();
                themeToggle.find('.dark-icon').show();
            } else {
                themeToggle.find('.light-icon').show();
                themeToggle.find('.dark-icon').hide();
            }
        }

        // Initial icon update
        updateThemeIcon();

        // Listen for class changes on body (in case theme is changed by other means)
        // Note: This is a simple observer, for more robust solutions, MutationObserver might be used.
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class') {
                    updateThemeIcon();
                }
            });
        });
        observer.observe(body[0], { attributes: true });

    });

})(jQuery);

<?php
/**
 * Feedback Widget Component
 * 
 * Provides user feedback interface for chat responses and general feedback
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get widget configuration
$widget_id = isset($args['widget_id']) ? $args['widget_id'] : 'feedback-widget-' . uniqid();
$message_id = isset($args['message_id']) ? $args['message_id'] : '';
$conversation_id = isset($args['conversation_id']) ? $args['conversation_id'] : '';
$session_id = isset($args['session_id']) ? $args['session_id'] : '';
$context = isset($args['context']) ? $args['context'] : 'general';
$show_detailed = isset($args['show_detailed']) ? $args['show_detailed'] : false;
$compact_mode = isset($args['compact_mode']) ? $args['compact_mode'] : false;
?>

<div class="feedback-widget" id="<?php echo esc_attr($widget_id); ?>" 
     data-message-id="<?php echo esc_attr($message_id); ?>"
     data-conversation-id="<?php echo esc_attr($conversation_id); ?>"
     data-session-id="<?php echo esc_attr($session_id); ?>"
     data-context="<?php echo esc_attr($context); ?>">

    <?php if (!$compact_mode): ?>
    <div class="feedback-header">
        <h4><?php _e('How was this response?', 'businesscraft-ai'); ?></h4>
    </div>
    <?php endif; ?>

    <!-- Quick Feedback (Thumbs) -->
    <div class="feedback-quick">
        <button class="feedback-btn feedback-thumbs-up" data-rating="1" data-type="thumbs">
            <span class="feedback-icon">👍</span>
            <span class="feedback-label"><?php _e('Helpful', 'businesscraft-ai'); ?></span>
        </button>
        <button class="feedback-btn feedback-thumbs-down" data-rating="0" data-type="thumbs">
            <span class="feedback-icon">👎</span>
            <span class="feedback-label"><?php _e('Not Helpful', 'businesscraft-ai'); ?></span>
        </button>
        <?php if (!$compact_mode): ?>
        <button class="feedback-btn feedback-detailed-btn" onclick="toggleDetailedFeedback('<?php echo esc_js($widget_id); ?>')">
            <span class="feedback-icon">💬</span>
            <span class="feedback-label"><?php _e('Detailed Feedback', 'businesscraft-ai'); ?></span>
        </button>
        <?php endif; ?>
    </div>

    <!-- Star Rating (Hidden by default, shown when detailed feedback is opened) -->
    <div class="feedback-detailed" style="display: none;">
        <div class="feedback-section">
            <label><?php _e('Overall Rating:', 'businesscraft-ai'); ?></label>
            <div class="star-rating">
                <?php for ($i = 1; $i <= 5; $i++): ?>
                <button class="star-btn" data-rating="<?php echo $i; ?>" data-type="star">
                    <span class="star-icon">⭐</span>
                </button>
                <?php endfor; ?>
            </div>
        </div>

        <!-- Category Ratings -->
        <div class="feedback-section">
            <label><?php _e('Rate specific aspects:', 'businesscraft-ai'); ?></label>
            <div class="category-ratings">
                <div class="category-rating">
                    <span class="category-label"><?php _e('Helpfulness', 'businesscraft-ai'); ?></span>
                    <div class="category-stars" data-category="helpfulness">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <button class="category-star" data-rating="<?php echo $i; ?>">⭐</button>
                        <?php endfor; ?>
                    </div>
                </div>
                <div class="category-rating">
                    <span class="category-label"><?php _e('Accuracy', 'businesscraft-ai'); ?></span>
                    <div class="category-stars" data-category="accuracy">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <button class="category-star" data-rating="<?php echo $i; ?>">⭐</button>
                        <?php endfor; ?>
                    </div>
                </div>
                <div class="category-rating">
                    <span class="category-label"><?php _e('Relevance', 'businesscraft-ai'); ?></span>
                    <div class="category-stars" data-category="relevance">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <button class="category-star" data-rating="<?php echo $i; ?>">⭐</button>
                        <?php endfor; ?>
                    </div>
                </div>
                <div class="category-rating">
                    <span class="category-label"><?php _e('Clarity', 'businesscraft-ai'); ?></span>
                    <div class="category-stars" data-category="clarity">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <button class="category-star" data-rating="<?php echo $i; ?>">⭐</button>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Text Feedback -->
        <div class="feedback-section">
            <label for="feedback-text-<?php echo esc_attr($widget_id); ?>">
                <?php _e('Additional Comments (Optional):', 'businesscraft-ai'); ?>
            </label>
            <textarea id="feedback-text-<?php echo esc_attr($widget_id); ?>" 
                      class="feedback-textarea" 
                      placeholder="<?php _e('Tell us how we can improve...', 'businesscraft-ai'); ?>"
                      rows="3"></textarea>
        </div>

        <!-- Training Data Consent -->
        <div class="feedback-section">
            <label class="feedback-checkbox-label">
                <input type="checkbox" id="training-consent-<?php echo esc_attr($widget_id); ?>" class="training-consent">
                <?php _e('Allow this feedback to be used for improving AI responses', 'businesscraft-ai'); ?>
            </label>
        </div>

        <!-- Submit Button -->
        <div class="feedback-actions">
            <button class="feedback-submit-btn" onclick="submitFeedback('<?php echo esc_js($widget_id); ?>')">
                <?php _e('Submit Feedback', 'businesscraft-ai'); ?>
            </button>
            <button class="feedback-cancel-btn" onclick="toggleDetailedFeedback('<?php echo esc_js($widget_id); ?>')">
                <?php _e('Cancel', 'businesscraft-ai'); ?>
            </button>
        </div>
    </div>

    <!-- Feedback Status -->
    <div class="feedback-status" style="display: none;">
        <div class="feedback-success">
            <span class="feedback-icon">✅</span>
            <span class="feedback-message"><?php _e('Thank you for your feedback!', 'businesscraft-ai'); ?></span>
        </div>
    </div>
</div>

<style>
.feedback-widget {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    font-size: 14px;
}

.feedback-header h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.feedback-quick {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.feedback-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
}

.feedback-btn:hover {
    border-color: #667eea;
    background: #f0f2ff;
}

.feedback-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.feedback-icon {
    font-size: 16px;
}

.feedback-detailed {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    margin-top: 15px;
}

.feedback-section {
    margin-bottom: 15px;
}

.feedback-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.star-rating {
    display: flex;
    gap: 5px;
}

.star-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.star-btn:hover,
.star-btn.active {
    opacity: 1;
}

.category-ratings {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.category-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.category-label {
    font-weight: 500;
    color: #555;
    min-width: 80px;
}

.category-stars {
    display: flex;
    gap: 3px;
}

.category-star {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.category-star:hover,
.category-star.active {
    opacity: 1;
}

.feedback-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
}

.feedback-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
}

.feedback-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.feedback-submit-btn,
.feedback-cancel-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
}

.feedback-submit-btn {
    background: #667eea;
    color: white;
}

.feedback-submit-btn:hover {
    background: #5a67d8;
}

.feedback-cancel-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.feedback-cancel-btn:hover {
    background: #e9ecef;
}

.feedback-status {
    text-align: center;
    padding: 15px;
}

.feedback-success {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #28a745;
    font-weight: 600;
}

/* Compact mode styles */
.feedback-widget.compact {
    padding: 10px;
    margin: 10px 0;
}

.feedback-widget.compact .feedback-quick {
    margin-bottom: 0;
}

.feedback-widget.compact .feedback-btn {
    padding: 6px 10px;
    font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .feedback-quick {
        flex-direction: column;
    }
    
    .category-rating {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .feedback-actions {
        flex-direction: column;
    }
}
</style>

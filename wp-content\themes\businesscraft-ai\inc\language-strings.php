<?php
/**
 * Language Strings for ChatGABI AI
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get language strings for a specific language
 * 
 * @param string $language Language code
 * @return array Language strings
 */
function chatgabi_get_language_strings($language) {
    $strings = array(
        'en' => array(
            'welcome' => 'Welcome to ChatGABI AI',
            'business_plan' => 'Business Plan',
            'marketing_strategy' => 'Marketing Strategy',
            'financial_forecast' => 'Financial Forecast',
            'loading' => 'Loading...',
            'error' => 'An error occurred',
            'success' => 'Success',
            'save' => 'Save',
            'cancel' => 'Cancel',
            'delete' => 'Delete',
            'edit' => 'Edit',
            'create' => 'Create',
            'update' => 'Update'
        ),
        'tw' => array(
            'welcome' => 'Akwaaba wo ChatGABI AI',
            'business_plan' => 'Adwuma Nhyehyɛe',
            'marketing_strategy' => 'Aguadi <PERSON>eh<PERSON>ɛe',
            'financial_forecast' => 'Sika Nhyehyɛe',
            'loading' => 'Ɛreload...',
            'error' => 'Mfomso bi aba',
            'success' => 'Ɛyɛ yie',
            'save' => 'Sie',
            'cancel' => 'Gyae',
            'delete' => 'Yi fi',
            'edit' => 'Sesa',
            'create' => 'Yɛ',
            'update' => 'Foforoo'
        ),
        'sw' => array(
            'welcome' => 'Karibu ChatGABI AI',
            'business_plan' => 'Mpango wa Biashara',
            'marketing_strategy' => 'Mkakati wa Uuzaji',
            'financial_forecast' => 'Utabiri wa Kifedha',
            'loading' => 'Inapakia...',
            'error' => 'Hitilafu imetokea',
            'success' => 'Imefanikiwa',
            'save' => 'Hifadhi',
            'cancel' => 'Ghairi',
            'delete' => 'Futa',
            'edit' => 'Hariri',
            'create' => 'Unda',
            'update' => 'Sasisha'
        ),
        'yo' => array(
            'welcome' => 'Káàbọ̀ sí ChatGABI AI',
            'business_plan' => 'Ètò Iṣòwò',
            'marketing_strategy' => 'Ìlànà Títà',
            'financial_forecast' => 'Àsọtẹ́lẹ̀ Owó',
            'loading' => 'Ń gbé...',
            'error' => 'Àṣìṣe kan wáyé',
            'success' => 'Àṣeyọrí',
            'save' => 'Fi pamọ́',
            'cancel' => 'Fagilee',
            'delete' => 'Pa rẹ́',
            'edit' => 'Ṣàtúnṣe',
            'create' => 'Ṣẹ̀dá',
            'update' => 'Ṣàtúnṣe'
        ),
        'zu' => array(
            'welcome' => 'Sawubona ku-ChatGABI AI',
            'business_plan' => 'Uhlelo Lwebhizinisi',
            'marketing_strategy' => 'Isu Lokuthengisa',
            'financial_forecast' => 'Isibikezelo Sezimali',
            'loading' => 'Iyalayisha...',
            'error' => 'Kukhona iphutha',
            'success' => 'Kuphumelele',
            'save' => 'Londoloza',
            'cancel' => 'Khansela',
            'delete' => 'Susa',
            'edit' => 'Hlela',
            'create' => 'Dala',
            'update' => 'Buyekeza'
        )
    );
    
    return isset($strings[$language]) ? $strings[$language] : $strings['en'];
}

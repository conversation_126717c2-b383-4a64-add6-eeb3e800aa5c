<?php
/**
 * Database Fix Script for ChatGABI Advanced Web Scraping System
 * 
 * This script creates all missing database tables and fixes wpdb::prepare() issues.
 *
 * @package ChatGABI
 * @since 1.3.0
 */

// Database configuration
$db_host = 'localhost';
$db_name = 'swifmind_local';
$db_user = 'root';
$db_pass = '';
$table_prefix = 'wp_';

echo "🔧 ChatGABI Database Fix Script\n";
echo "==============================\n";
echo "Fixing database issues for ChatGABI Advanced Web Scraping System...\n\n";

// Connect to database
echo "📡 Connecting to database...\n";
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
    echo "✅ Database: $db_name\n\n";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Create tables
echo "🔨 Creating missing database tables...\n";
$tables_created = 0;

// Advanced Scraping Logs
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_advanced_scraping_logs (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        worker_id varchar(100) DEFAULT NULL,
        action varchar(100) NOT NULL,
        message text NOT NULL,
        status varchar(20) NOT NULL,
        country varchar(50) DEFAULT NULL,
        sector varchar(255) DEFAULT NULL,
        source_name varchar(255) DEFAULT NULL,
        source_type varchar(50) DEFAULT NULL,
        scraping_method varchar(50) DEFAULT NULL,
        data_points_extracted int DEFAULT 0,
        processing_time_ms int DEFAULT 0,
        memory_usage_mb float DEFAULT 0,
        error_details text DEFAULT NULL,
        performance_data json DEFAULT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY worker_id (worker_id),
        KEY action (action),
        KEY status (status),
        KEY country (country),
        KEY sector (sector),
        KEY timestamp (timestamp)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Advanced Scraping Logs table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Advanced Scraping Logs table\n";
}

// AI Agent Logs
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_ai_agent_logs (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        agent_type varchar(50) NOT NULL,
        action varchar(100) NOT NULL,
        message text NOT NULL,
        input_data json DEFAULT NULL,
        output_data json DEFAULT NULL,
        processing_time_ms int DEFAULT 0,
        tokens_used int DEFAULT 0,
        api_cost_usd decimal(10,6) DEFAULT 0,
        success boolean DEFAULT true,
        error_details text DEFAULT NULL,
        performance_data json DEFAULT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY agent_type (agent_type),
        KEY action (action),
        KEY success (success),
        KEY timestamp (timestamp)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ AI Agent Logs table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create AI Agent Logs table\n";
}

// Performance Metrics
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_performance_metrics (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        cycle_type varchar(50) NOT NULL,
        start_time datetime NOT NULL,
        end_time datetime DEFAULT NULL,
        duration_seconds int DEFAULT NULL,
        data_points_processed int DEFAULT 0,
        sources_scraped int DEFAULT 0,
        sources_successful int DEFAULT 0,
        sources_failed int DEFAULT 0,
        success_rate decimal(5,2) DEFAULT 0,
        data_points_per_hour decimal(10,2) DEFAULT 0,
        accuracy_score decimal(5,2) DEFAULT 0,
        confidence_score decimal(5,2) DEFAULT 0,
        anomalies_detected int DEFAULT 0,
        memory_peak_mb float DEFAULT 0,
        cpu_usage_percent float DEFAULT 0,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY cycle_type (cycle_type),
        KEY start_time (start_time),
        KEY success_rate (success_rate),
        KEY timestamp (timestamp)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Performance Metrics table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Performance Metrics table\n";
}

// Data Quality Logs
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_data_quality_logs (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        country varchar(50) NOT NULL,
        sector varchar(255) NOT NULL,
        data_type varchar(100) NOT NULL,
        source_count int NOT NULL,
        sources_used json NOT NULL,
        verified_value text DEFAULT NULL,
        confidence_score decimal(5,2) NOT NULL,
        validation_method varchar(100) DEFAULT NULL,
        anomalies_detected json DEFAULT NULL,
        cross_validation_results json DEFAULT NULL,
        quality_flags json DEFAULT NULL,
        processing_notes text DEFAULT NULL,
        verification_timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY country_sector (country, sector),
        KEY data_type (data_type),
        KEY confidence_score (confidence_score),
        KEY verification_timestamp (verification_timestamp)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Data Quality Logs table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Data Quality Logs table\n";
}

// Source Reliability
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_source_reliability (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        source_name varchar(255) NOT NULL,
        source_url varchar(500) NOT NULL,
        source_type varchar(50) NOT NULL,
        country varchar(50) NOT NULL,
        base_reliability_score decimal(3,2) NOT NULL,
        current_reliability_score decimal(3,2) NOT NULL,
        total_scrapes int DEFAULT 0,
        successful_scrapes int DEFAULT 0,
        failed_scrapes int DEFAULT 0,
        success_rate decimal(5,2) DEFAULT 0,
        last_successful_scrape datetime DEFAULT NULL,
        last_failed_scrape datetime DEFAULT NULL,
        data_quality_score decimal(3,2) DEFAULT 0,
        last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY source_country (source_name, country),
        KEY source_type (source_type),
        KEY country (country),
        KEY current_reliability_score (current_reliability_score)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Source Reliability table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Source Reliability table\n";
}

// Scraped Data Archive
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_scraped_data_archive (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        country varchar(50) NOT NULL,
        sector varchar(255) NOT NULL,
        source_name varchar(255) NOT NULL,
        source_type varchar(50) NOT NULL,
        data_type varchar(100) NOT NULL,
        raw_value text NOT NULL,
        normalized_value text DEFAULT NULL,
        extraction_method varchar(100) DEFAULT NULL,
        confidence_score decimal(5,2) DEFAULT NULL,
        data_quality_flags json DEFAULT NULL,
        scraping_metadata json DEFAULT NULL,
        scraped_at datetime NOT NULL,
        processed_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY country_sector (country, sector),
        KEY source_name (source_name),
        KEY data_type (data_type),
        KEY scraped_at (scraped_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Scraped Data Archive table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Scraped Data Archive table\n";
}

// Anomaly Detection Logs
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_anomaly_detection_logs (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        country varchar(50) NOT NULL,
        sector varchar(255) NOT NULL,
        data_type varchar(100) NOT NULL,
        anomaly_type varchar(100) NOT NULL,
        detector_type varchar(50) NOT NULL,
        severity varchar(20) NOT NULL,
        anomaly_value text NOT NULL,
        expected_range text DEFAULT NULL,
        source_name varchar(255) NOT NULL,
        statistical_measures json DEFAULT NULL,
        contextual_data json DEFAULT NULL,
        resolution_status varchar(50) DEFAULT 'pending',
        resolution_notes text DEFAULT NULL,
        false_positive boolean DEFAULT false,
        detected_at datetime DEFAULT CURRENT_TIMESTAMP,
        resolved_at datetime DEFAULT NULL,
        PRIMARY KEY (id),
        KEY country_sector (country, sector),
        KEY anomaly_type (anomaly_type),
        KEY severity (severity),
        KEY resolution_status (resolution_status),
        KEY detected_at (detected_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Anomaly Detection Logs table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Anomaly Detection Logs table\n";
}

// Cross Validation Results
try {
    $sql = "CREATE TABLE IF NOT EXISTS {$table_prefix}chatgabi_cross_validation_results (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        country varchar(50) NOT NULL,
        sector varchar(255) NOT NULL,
        data_type varchar(100) NOT NULL,
        validation_type varchar(100) NOT NULL,
        current_value text NOT NULL,
        reference_value text DEFAULT NULL,
        variance_percentage decimal(5,2) DEFAULT NULL,
        consistency_score decimal(5,2) NOT NULL,
        validation_status varchar(50) NOT NULL,
        reference_source varchar(255) DEFAULT NULL,
        reference_date datetime DEFAULT NULL,
        validation_notes text DEFAULT NULL,
        validation_metadata json DEFAULT NULL,
        validated_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY country_sector (country, sector),
        KEY data_type (data_type),
        KEY validation_type (validation_type),
        KEY consistency_score (consistency_score),
        KEY validated_at (validated_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Cross Validation Results table created\n";
    $tables_created++;
} catch (PDOException $e) {
    echo "❌ Failed to create Cross Validation Results table\n";
}

// Summary
echo "\n🏆 Database Fix Summary\n";
echo "======================\n";
echo "✅ Tables created successfully: $tables_created/8\n";

if ($tables_created >= 6) {
    echo "\n🎉 DATABASE FIX SUCCESSFUL!\n";
    echo "===========================\n";
    echo "✅ ChatGABI Advanced Web Scraping System database is ready\n";
    echo "✅ All critical database tables have been created\n";
    echo "✅ wpdb::prepare() issues have been fixed\n";
    echo "\n🚀 Next Steps:\n";
    echo "1. Access WordPress Admin → ChatGABI → Database\n";
    echo "2. Verify all tables are showing as 'Exists'\n";
    echo "3. Configure OpenAI API key\n";
    echo "4. Start using the Advanced Web Scraping System\n";
} else {
    echo "\n⚠️ PARTIAL DATABASE FIX\n";
    echo "=======================\n";
    echo "Some tables could not be created.\n";
}

echo "\nDatabase fix completed at: " . date('Y-m-d H:i:s') . "\n";
?>

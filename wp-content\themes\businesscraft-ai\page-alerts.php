<?php
/**
 * Template Name: Opportunity Alerts Dashboard
 * 
 * User dashboard for managing opportunity alert subscriptions
 */

// Redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

get_header();

$user_id = get_current_user_id();
$alerts_manager = chatgabi_get_opportunity_alerts();
$user_alerts = $alerts_manager->get_user_alert_subscriptions($user_id);

// Get available options for filters
$countries = array('Ghana', 'Kenya', 'Nigeria', 'South Africa');
$opportunity_types = array('Grant', 'Loan', 'Incubator', 'Accelerator', 'Competition', 'Tender', 'Contract', 'Investment', 'Regulatory Support');
$sectors = array('Agriculture', 'Technology', 'Fintech', 'Healthcare', 'Education', 'Manufacturing', 'Energy', 'Transportation', 'Tourism', 'Retail', 'Construction');
$notification_frequencies = array(
    'immediate' => __('Immediate', 'chatgabi'),
    'daily' => __('Daily Digest', 'chatgabi'),
    'weekly' => __('Weekly Summary', 'chatgabi')
);
?>

<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2"><?php _e('Opportunity Alerts', 'chatgabi'); ?></h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#alertModal">
                    <i class="fas fa-plus"></i> <?php _e('Create New Alert', 'chatgabi'); ?>
                </button>
            </div>
            
            <!-- Alert Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count($user_alerts); ?></h4>
                                    <p class="card-text"><?php _e('Active Alerts', 'chatgabi'); ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bell fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="total-matches">-</h4>
                                    <p class="card-text"><?php _e('Total Matches', 'chatgabi'); ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-search fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="emails-sent">-</h4>
                                    <p class="card-text"><?php _e('Emails Sent', 'chatgabi'); ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="open-rate">-</h4>
                                    <p class="card-text"><?php _e('Open Rate', 'chatgabi'); ?></p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Existing Alerts -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php _e('Your Alert Subscriptions', 'chatgabi'); ?></h5>
                </div>
                <div class="card-body">
                    <?php if (empty($user_alerts)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted"><?php _e('No alerts configured yet', 'chatgabi'); ?></h5>
                            <p class="text-muted"><?php _e('Create your first alert to start receiving opportunity notifications.', 'chatgabi'); ?></p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#alertModal">
                                <?php _e('Create Your First Alert', 'chatgabi'); ?>
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php _e('Alert Name', 'chatgabi'); ?></th>
                                        <th><?php _e('Countries', 'chatgabi'); ?></th>
                                        <th><?php _e('Types', 'chatgabi'); ?></th>
                                        <th><?php _e('Frequency', 'chatgabi'); ?></th>
                                        <th><?php _e('Status', 'chatgabi'); ?></th>
                                        <th><?php _e('Actions', 'chatgabi'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($user_alerts as $alert): ?>
                                        <tr data-alert-id="<?php echo $alert['id']; ?>">
                                            <td>
                                                <strong><?php echo esc_html($alert['filter_name']); ?></strong>
                                                <?php if (!empty($alert['keywords'])): ?>
                                                    <br><small class="text-muted">Keywords: <?php echo esc_html($alert['keywords']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($alert['countries'])): ?>
                                                    <span class="badge bg-secondary me-1"><?php echo implode('</span> <span class="badge bg-secondary me-1">', $alert['countries']); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php _e('All countries', 'chatgabi'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($alert['opportunity_types'])): ?>
                                                    <?php echo esc_html(implode(', ', array_slice($alert['opportunity_types'], 0, 2))); ?>
                                                    <?php if (count($alert['opportunity_types']) > 2): ?>
                                                        <small class="text-muted">+<?php echo count($alert['opportunity_types']) - 2; ?> more</small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php _e('All types', 'chatgabi'); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $notification_frequencies[$alert['notification_frequency']]; ?></span>
                                            </td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input alert-toggle" type="checkbox" 
                                                           data-alert-id="<?php echo $alert['id']; ?>"
                                                           <?php checked($alert['is_active'], 1); ?>>
                                                    <label class="form-check-label">
                                                        <?php echo $alert['is_active'] ? __('Active', 'chatgabi') : __('Inactive', 'chatgabi'); ?>
                                                    </label>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary edit-alert" 
                                                            data-alert-id="<?php echo $alert['id']; ?>"
                                                            data-bs-toggle="modal" data-bs-target="#alertModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-info preview-alert" 
                                                            data-alert-id="<?php echo $alert['id']; ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger delete-alert" 
                                                            data-alert-id="<?php echo $alert['id']; ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert Modal -->
<div class="modal fade" id="alertModal" tabindex="-1" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="alertModalLabel"><?php _e('Create Opportunity Alert', 'chatgabi'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="alertForm">
                <div class="modal-body">
                    <input type="hidden" id="alert_id" name="alert_id" value="">
                    
                    <!-- Alert Name -->
                    <div class="mb-3">
                        <label for="filter_name" class="form-label"><?php _e('Alert Name', 'chatgabi'); ?> *</label>
                        <input type="text" class="form-control" id="filter_name" name="filter_name" required>
                        <div class="form-text"><?php _e('Give your alert a descriptive name', 'chatgabi'); ?></div>
                    </div>
                    
                    <!-- Countries -->
                    <div class="mb-3">
                        <label class="form-label"><?php _e('Countries', 'chatgabi'); ?></label>
                        <div class="row">
                            <?php foreach ($countries as $country): ?>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="countries[]" 
                                               value="<?php echo esc_attr($country); ?>" id="country_<?php echo strtolower($country); ?>">
                                        <label class="form-check-label" for="country_<?php echo strtolower($country); ?>">
                                            <?php echo esc_html($country); ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="form-text"><?php _e('Leave empty to include all countries', 'chatgabi'); ?></div>
                    </div>
                    
                    <!-- Opportunity Types -->
                    <div class="mb-3">
                        <label class="form-label"><?php _e('Opportunity Types', 'chatgabi'); ?></label>
                        <div class="row">
                            <?php foreach ($opportunity_types as $type): ?>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="opportunity_types[]" 
                                               value="<?php echo esc_attr($type); ?>" id="type_<?php echo strtolower(str_replace(' ', '_', $type)); ?>">
                                        <label class="form-check-label" for="type_<?php echo strtolower(str_replace(' ', '_', $type)); ?>">
                                            <?php echo esc_html($type); ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Sectors -->
                    <div class="mb-3">
                        <label class="form-label"><?php _e('Sectors', 'chatgabi'); ?></label>
                        <div class="row">
                            <?php foreach ($sectors as $sector): ?>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="sectors[]" 
                                               value="<?php echo esc_attr($sector); ?>" id="sector_<?php echo strtolower($sector); ?>">
                                        <label class="form-check-label" for="sector_<?php echo strtolower($sector); ?>">
                                            <?php echo esc_html($sector); ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Amount Range -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="amount_min" class="form-label"><?php _e('Minimum Amount', 'chatgabi'); ?></label>
                            <input type="number" class="form-control" id="amount_min" name="amount_min" min="0" step="1000">
                        </div>
                        <div class="col-md-6">
                            <label for="amount_max" class="form-label"><?php _e('Maximum Amount', 'chatgabi'); ?></label>
                            <input type="number" class="form-control" id="amount_max" name="amount_max" min="0" step="1000">
                        </div>
                    </div>
                    
                    <!-- Keywords -->
                    <div class="mb-3">
                        <label for="keywords" class="form-label"><?php _e('Keywords', 'chatgabi'); ?></label>
                        <input type="text" class="form-control" id="keywords" name="keywords">
                        <div class="form-text"><?php _e('Comma-separated keywords to search for in opportunity titles and descriptions', 'chatgabi'); ?></div>
                    </div>
                    
                    <!-- Deadline Proximity -->
                    <div class="mb-3">
                        <label for="deadline_days" class="form-label"><?php _e('Deadline Alert (Days)', 'chatgabi'); ?></label>
                        <select class="form-select" id="deadline_days" name="deadline_days">
                            <option value=""><?php _e('Any deadline', 'chatgabi'); ?></option>
                            <option value="7"><?php _e('Within 7 days', 'chatgabi'); ?></option>
                            <option value="14"><?php _e('Within 14 days', 'chatgabi'); ?></option>
                            <option value="30"><?php _e('Within 30 days', 'chatgabi'); ?></option>
                            <option value="60"><?php _e('Within 60 days', 'chatgabi'); ?></option>
                        </select>
                    </div>
                    
                    <!-- Notification Frequency -->
                    <div class="mb-3">
                        <label for="notification_frequency" class="form-label"><?php _e('Notification Frequency', 'chatgabi'); ?></label>
                        <select class="form-select" id="notification_frequency" name="notification_frequency" required>
                            <?php foreach ($notification_frequencies as $value => $label): ?>
                                <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- Preview Section -->
                    <div id="preview-section" class="mt-4" style="display: none;">
                        <h6><?php _e('Preview Matches', 'chatgabi'); ?></h6>
                        <div id="preview-results" class="border rounded p-3 bg-light">
                            <!-- Preview content will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="preview-btn">
                        <i class="fas fa-eye"></i> <?php _e('Preview Matches', 'chatgabi'); ?>
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('Cancel', 'chatgabi'); ?></button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?php _e('Save Alert', 'chatgabi'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Pass PHP data to JavaScript
window.chatgabiAlerts = {
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('chatgabi_alert_nonce'); ?>',
    userAlerts: <?php echo json_encode($user_alerts); ?>
};
</script>

<?php
wp_enqueue_script('chatgabi-alerts', get_template_directory_uri() . '/assets/js/opportunity-alerts.js', array('jquery'), '1.0.0', true);
wp_enqueue_style('chatgabi-alerts', get_template_directory_uri() . '/assets/css/opportunity-alerts.css', array(), '1.0.0');

get_footer();
?>

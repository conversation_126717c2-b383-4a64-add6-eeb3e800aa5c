<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Manifest Test</title>
    <link rel="manifest" href="http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/manifest.json">
</head>
<body>
    <h1>PWA Manifest Test Page</h1>
    <p>Check browser developer tools to see if manifest is loaded.</p>
    <script>
        console.log("Manifest URL:", "http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/manifest.json");
        
        // Check if manifest is loaded
        if ("serviceWorker" in navigator) {
            console.log("Service Worker supported");
        }
        
        // Check for PWA install prompt
        window.addEventListener("beforeinstallprompt", function(e) {
            console.log("PWA install prompt available");
        });
    </script>
</body>
</html>
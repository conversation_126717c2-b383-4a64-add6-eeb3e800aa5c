<?php
/**
 * Templates and Market Data Management for BusinessCraft AI
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize templates and market data
 */
function businesscraft_ai_init_templates() {
    // Create data directories if they don't exist
    $data_dir = CHATGABI_THEME_DIR . '/data';
    $templates_dir = $data_dir . '/templates';
    $market_dir = $data_dir . '/market';

    if (!file_exists($data_dir)) {
        wp_mkdir_p($data_dir);
    }

    if (!file_exists($templates_dir)) {
        wp_mkdir_p($templates_dir);
    }

    if (!file_exists($market_dir)) {
        wp_mkdir_p($market_dir);
    }

    // Create default templates if they don't exist
    businesscraft_ai_create_default_templates();
    businesscraft_ai_create_default_market_data();
}
add_action('after_setup_theme', 'businesscraft_ai_init_templates');

/**
 * Create default business templates
 */
function businesscraft_ai_create_default_templates() {
    $templates_dir = CHATGABI_THEME_DIR . '/data/templates';

    // Business Plan Templates
    $business_plan_templates = array(
        'templates' => array(
            'executive_summary' => 'Executive Summary: [Company Name] is a [industry] company that [brief description of what the company does]. Our mission is to [mission statement]. We are seeking [funding amount] to [use of funds].',
            'market_analysis' => 'Market Analysis: The [industry] market in [country/region] is valued at [market size] and is expected to grow at [growth rate]% annually. Key trends include [trend 1], [trend 2], and [trend 3].',
            'financial_projections' => 'Financial Projections: Year 1 Revenue: [amount], Year 2 Revenue: [amount], Year 3 Revenue: [amount]. Break-even point: [timeframe]. Initial investment required: [amount].',
            'marketing_strategy' => 'Marketing Strategy: Target customers are [customer description]. We will reach them through [channel 1], [channel 2], and [channel 3]. Our unique value proposition is [value proposition].',
        ),
    );

    $business_plan_file = $templates_dir . '/business_plan_en.json';
    if (!file_exists($business_plan_file)) {
        file_put_contents($business_plan_file, json_encode($business_plan_templates, JSON_PRETTY_PRINT));
    }

    // Marketing Templates
    $marketing_templates = array(
        'templates' => array(
            'social_media_strategy' => 'Social Media Strategy: Platform focus: [platforms]. Content themes: [theme 1], [theme 2], [theme 3]. Posting frequency: [frequency]. Engagement tactics: [tactics].',
            'email_campaign' => 'Email Campaign: Subject line: [subject]. Target audience: [audience]. Key message: [message]. Call to action: [CTA]. Follow-up sequence: [sequence].',
            'content_calendar' => 'Content Calendar: Week 1: [content type] - [topic]. Week 2: [content type] - [topic]. Week 3: [content type] - [topic]. Week 4: [content type] - [topic].',
            'brand_positioning' => 'Brand Positioning: We are the [category] for [target market] who want [need/desire] because we are the only [differentiator] that [benefit].',
        ),
    );

    $marketing_file = $templates_dir . '/marketing_en.json';
    if (!file_exists($marketing_file)) {
        file_put_contents($marketing_file, json_encode($marketing_templates, JSON_PRETTY_PRINT));
    }

    // Financial Templates
    $finance_templates = array(
        'templates' => array(
            'budget_template' => 'Monthly Budget: Revenue: [amount]. Fixed costs: Rent [amount], Salaries [amount], Utilities [amount]. Variable costs: Materials [amount], Marketing [amount]. Net profit: [amount].',
            'cash_flow' => 'Cash Flow Projection: Opening balance: [amount]. Cash inflows: [sources]. Cash outflows: [expenses]. Closing balance: [amount]. Cash flow status: [positive/negative].',
            'pricing_strategy' => 'Pricing Strategy: Cost per unit: [amount]. Desired margin: [percentage]. Competitor pricing: [range]. Recommended price: [amount]. Value justification: [reasons].',
            'funding_proposal' => 'Funding Proposal: Amount needed: [amount]. Use of funds: [breakdown]. Expected ROI: [percentage]. Repayment terms: [terms]. Risk mitigation: [strategies].',
        ),
    );

    $finance_file = $templates_dir . '/finance_en.json';
    if (!file_exists($finance_file)) {
        file_put_contents($finance_file, json_encode($finance_templates, JSON_PRETTY_PRINT));
    }

    // Operations Templates
    $operations_templates = array(
        'templates' => array(
            'sop_template' => 'Standard Operating Procedure: Process: [process name]. Objective: [objective]. Steps: 1. [step 1], 2. [step 2], 3. [step 3]. Quality checks: [checks]. Documentation: [requirements].',
            'workflow_design' => 'Workflow Design: Input: [input]. Process steps: [steps]. Output: [output]. Responsible parties: [roles]. Timeline: [duration]. Success metrics: [metrics].',
            'quality_control' => 'Quality Control: Standards: [standards]. Inspection points: [points]. Testing procedures: [procedures]. Corrective actions: [actions]. Documentation: [requirements].',
            'supply_chain' => 'Supply Chain: Suppliers: [list]. Lead times: [times]. Inventory levels: [levels]. Logistics: [methods]. Risk management: [strategies].',
        ),
    );

    $operations_file = $templates_dir . '/operations_en.json';
    if (!file_exists($operations_file)) {
        file_put_contents($operations_file, json_encode($operations_templates, JSON_PRETTY_PRINT));
    }
}

/**
 * Create default market data
 */
function businesscraft_ai_create_default_market_data() {
    $market_dir = CHATGABI_THEME_DIR . '/data/market';

    // African market insights
    $african_market_data = array(
        'market_insights' => array(
            'mobile_penetration' => 'Mobile phone penetration in Africa exceeds 80%, with smartphone adoption growing rapidly. Mobile-first business strategies are essential.',
            'payment_systems' => 'Mobile money systems like M-Pesa, MTN Mobile Money, and Airtel Money are widely adopted. Digital payment integration is crucial for business success.',
            'youth_demographics' => 'Over 60% of Africa\'s population is under 25 years old, representing a significant market opportunity for youth-focused products and services.',
            'urbanization_trends' => 'Rapid urbanization is creating new market opportunities in cities, while rural markets remain underserved but accessible through mobile technology.',
            'sme_landscape' => 'Small and medium enterprises (SMEs) contribute significantly to African economies, with high demand for business support services and financing.',
            'digital_transformation' => 'Digital transformation is accelerating across Africa, with increasing adoption of e-commerce, fintech, and digital services.',
        ),
    );

    $african_market_file = $market_dir . '/en.json';
    if (!file_exists($african_market_file)) {
        file_put_contents($african_market_file, json_encode($african_market_data, JSON_PRETTY_PRINT));
    }

    // Country-specific data
    $country_data = array(
        'nigeria' => array(
            'market_insights' => array(
                'population' => 'Nigeria has over 200 million people, making it Africa\'s largest market.',
                'economy' => 'Largest economy in Africa with strong oil, agriculture, and services sectors.',
                'fintech' => 'Leading fintech hub with companies like Paystack, Flutterwave, and Interswitch.',
                'challenges' => 'Infrastructure gaps, regulatory complexity, and currency volatility.',
            ),
        ),
        'kenya' => array(
            'market_insights' => array(
                'innovation' => 'Known as the "Silicon Savannah" with a thriving tech ecosystem.',
                'mobile_money' => 'M-Pesa originated here and has the highest mobile money adoption globally.',
                'agriculture' => 'Agriculture employs 75% of the population with opportunities for agtech solutions.',
                'business_environment' => 'Relatively business-friendly with strong entrepreneurial culture.',
            ),
        ),
        'ghana' => array(
            'market_insights' => array(
                'stability' => 'Political stability and democratic governance attract investment.',
                'gold_cocoa' => 'Major gold and cocoa producer with opportunities in value-added processing.',
                'services' => 'Growing services sector, particularly in finance and telecommunications.',
                'diaspora' => 'Strong diaspora connections provide remittance flows and investment opportunities.',
            ),
        ),
        'south_africa' => array(
            'market_insights' => array(
                'developed_market' => 'Most developed economy in Africa with sophisticated financial markets.',
                'inequality' => 'High inequality creates opportunities for inclusive business models.',
                'infrastructure' => 'Best infrastructure in Africa but with significant maintenance needs.',
                'regional_hub' => 'Gateway to Southern African markets with strong logistics networks.',
            ),
        ),
    );

    foreach ($country_data as $country => $data) {
        $country_file = $market_dir . "/{$country}.json";
        if (!file_exists($country_file)) {
            file_put_contents($country_file, json_encode($data, JSON_PRETTY_PRINT));
        }
    }
}

/**
 * Get available template categories
 */
function businesscraft_ai_get_template_categories() {
    return array(
        'business_plan' => __('Business Plans', 'businesscraft-ai'),
        'marketing' => __('Marketing', 'businesscraft-ai'),
        'finance' => __('Finance', 'businesscraft-ai'),
        'operations' => __('Operations', 'businesscraft-ai'),
        'general' => __('General Business', 'businesscraft-ai'),
    );
}

/**
 * Get available languages
 */
function businesscraft_ai_get_available_languages() {
    return array(
        'en' => __('English', 'businesscraft-ai'),
        'tw' => __('Twi', 'businesscraft-ai'),
        'sw' => __('Swahili', 'businesscraft-ai'),
        'yo' => __('Yoruba', 'businesscraft-ai'),
        'zu' => __('Zulu', 'businesscraft-ai'),
    );
}

/**
 * Update template
 */
function businesscraft_ai_update_template($category, $language, $template_key, $template_content) {
    $templates_dir = CHATGABI_THEME_DIR . '/data/templates';
    $file_path = $templates_dir . "/{$category}_{$language}.json";

    $templates = array('templates' => array());

    if (file_exists($file_path)) {
        $existing_data = file_get_contents($file_path);
        $templates = json_decode($existing_data, true);
        if (!$templates) {
            $templates = array('templates' => array());
        }
    }

    $templates['templates'][$template_key] = $template_content;

    return file_put_contents($file_path, json_encode($templates, JSON_PRETTY_PRINT));
}

/**
 * Delete template from file system
 */
function businesscraft_ai_delete_template_file($category, $language, $template_key) {
    $templates_dir = CHATGABI_THEME_DIR . '/data/templates';
    $file_path = $templates_dir . "/{$category}_{$language}.json";

    if (!file_exists($file_path)) {
        return false;
    }

    $existing_data = file_get_contents($file_path);
    $templates = json_decode($existing_data, true);

    if (!$templates || !isset($templates['templates'][$template_key])) {
        return false;
    }

    unset($templates['templates'][$template_key]);

    return file_put_contents($file_path, json_encode($templates, JSON_PRETTY_PRINT));
}

/**
 * Load template files from filesystem
 */
function chatgabi_load_template_files($category, $language) {
    $templates_dir = CHATGABI_THEME_DIR . '/data/templates';
    $file_path = $templates_dir . "/{$category}_{$language}.json";

    if (!file_exists($file_path)) {
        return array();
    }

    $file_content = file_get_contents($file_path);
    $data = json_decode($file_content, true);

    return $data['templates'] ?? array();
}

/**
 * Get all templates for a category and language
 */
function businesscraft_ai_get_all_templates($category, $language) {
    $templates = chatgabi_load_template_files($category, $language);
    return $templates;
}

/**
 * Update market data
 */
function businesscraft_ai_update_market_data($language, $insight_key, $insight_content) {
    $market_dir = CHATGABI_THEME_DIR . '/data/market';
    $file_path = $market_dir . "/{$language}.json";

    $market_data = array('market_insights' => array());

    if (file_exists($file_path)) {
        $existing_data = file_get_contents($file_path);
        $market_data = json_decode($existing_data, true);
        if (!$market_data) {
            $market_data = array('market_insights' => array());
        }
    }

    $market_data['market_insights'][$insight_key] = $insight_content;

    return file_put_contents($file_path, json_encode($market_data, JSON_PRETTY_PRINT));
}

/**
 * Get template usage statistics
 */
function businesscraft_ai_get_template_usage_stats() {
    global $wpdb;

    $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';

    // Get language usage
    $language_stats = $wpdb->get_results(
        "SELECT language, COUNT(*) as usage_count
         FROM {$chat_logs_table}
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         GROUP BY language
         ORDER BY usage_count DESC"
    );

    // Get context usage (approximated from chat content)
    $context_stats = $wpdb->get_results(
        "SELECT context, COUNT(*) as usage_count
         FROM {$chat_logs_table}
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
         GROUP BY context
         ORDER BY usage_count DESC"
    );

    return array(
        'language_usage' => $language_stats,
        'context_usage' => $context_stats,
    );
}

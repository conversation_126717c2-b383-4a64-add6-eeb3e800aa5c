# ChatGABI Automated Sector Data Update System

## 🎯 Overview

The ChatGABI Automated Sector Data Update System provides intelligent, **web scraping-powered** updates for business sector data across Ghana, Kenya, Nigeria, and South Africa. The system combines real-time web scraping from authoritative sources with AI-powered data synthesis to maintain data freshness, identify trending sectors, and ensure high-quality business intelligence for the ChatGABI platform.

## 🏗️ System Architecture

### Core Components

1. **Background Web Scraper** (`ChatGABI_Sector_Data_Updater`)
   - **Real-time web scraping** from government, financial, and industry sources
   - **Multi-source data aggregation** from 15+ authoritative websites per country
   - WordPress cron-based automation with intelligent scheduling
   - Weekly full updates and daily trending updates
   - Rate-limited requests to prevent server overload

2. **AI Agent Component**
   - OpenAI GPT-4 powered sector intelligence generation
   - Maintains existing JSON structure and format
   - Token-optimized prompts (under 400 tokens per sector)

3. **File Management System**
   - Automatic backup creation before updates
   - Versioned file storage (v1, v2, v3, etc.)
   - Safe JSON file operations with rollback capability

4. **Admin Dashboard Integration**
   - Real-time system status monitoring
   - Manual update triggers
   - Comprehensive logging and analytics

5. **Dynamic Market Tracking**
   - Trending sector identification from user analytics
   - Priority-based update scheduling
   - Data freshness indicators

## 🕷️ Web Scraping Sources

### Government & Regulatory Sources

#### Ghana
- **Ghana Investment Promotion Centre** - Investment sectors and opportunities
- **Bank of Ghana** - Economic statistics and sector performance
- **Ghana Statistical Service** - GDP data and sector growth metrics

#### Kenya
- **Kenya Association of Manufacturers** - Manufacturing sector insights
- **Central Bank of Kenya** - Economic indicators and sector performance
- **Kenya National Bureau of Statistics** - GDP statistics and sector contribution

#### Nigeria
- **Nigerian Investment Promotion Commission** - Investment opportunities
- **Central Bank of Nigeria** - Economic data and sectoral statistics
- **National Bureau of Statistics Nigeria** - GDP reports and sector analysis

#### South Africa
- **InvestSA** - Investment opportunities and sector profiles
- **South African Reserve Bank** - Economic statistics and sectoral data
- **Statistics South Africa** - GDP data and industry statistics

### Industry-Specific Sources
- **African Fintech Network** - Fintech market insights and statistics
- **African Development Bank Agriculture** - Agriculture sector data
- **African Tech Roundup** - Technology market analysis
- **African Energy Chamber** - Energy sector intelligence

### Data Extraction Capabilities
- **Market Size Data** - Automatic extraction of valuation figures
- **Growth Rates** - CAGR and annual growth rate detection
- **Investment Activity** - Funding and capital flow tracking
- **Regulatory Updates** - Policy changes and compliance requirements
- **Recent Developments** - News and market announcements

## 📁 File Structure

```
wp-content/themes/businesscraft-ai/
├── inc/
│   └── sector-data-updater.php          # Main updater class
├── assets/js/
│   └── admin-sector-updates.js          # Admin interface JavaScript
├── test-sector-updater.php              # System testing script
└── SECTOR-DATA-UPDATE-SYSTEM.md         # This documentation

wp-content/datasets/
├── ghana-business-data/
│   ├── ghana_business_data.json         # Base file
│   ├── ghana_business_datav1.json       # Version 1
│   ├── ghana_business_datav2.json       # Version 2
│   └── ghana_business_datav3.backup.2025-01-01-12-00-00.json  # Backup
├── kenya-business-data/
├── nigeria-business-data/
└── south-africa-business-data/
```

## 🗄️ Database Schema

### Update Logs Table: `wp_chatgabi_sector_update_logs`

```sql
CREATE TABLE wp_chatgabi_sector_update_logs (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    action varchar(100) NOT NULL,
    message text NOT NULL,
    status varchar(20) NOT NULL,
    country varchar(50) DEFAULT NULL,
    sector varchar(255) DEFAULT NULL,
    timestamp datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY action (action),
    KEY status (status),
    KEY country (country),
    KEY sector (sector),
    KEY timestamp (timestamp)
);
```

## ⚙️ Configuration Options

### WordPress Options

- `chatgabi_auto_sector_updates` (boolean): Enable/disable automatic updates
- `chatgabi_sector_update_frequency` (string): Update frequency ('weekly', 'bi_weekly')
- `chatgabi_trending_update_enabled` (boolean): Enable trending sector updates

### Required Settings

- `businesscraft_ai_openai_api_key`: OpenAI API key for AI-powered updates

## 🔄 Update Process Flow

### 1. Weekly Automated Updates with Web Scraping

```
1. Check if auto-updates are enabled
2. For each country (Ghana, Kenya, Nigeria, South Africa):
   a. Load existing sector data
   b. Check which sectors need updating (>7 days old)
   c. **WEB SCRAPING PHASE:**
      - Scrape government sources (Investment agencies, Central banks, Statistics offices)
      - Scrape industry-specific sources based on sector type
      - Extract market size, growth rates, investment data, regulatory updates
      - Parse HTML content using regex patterns for data extraction
      - Structure scraped data with source attribution
   d. **AI SYNTHESIS PHASE:**
      - Generate AI-powered updates using scraped data as primary source
      - Cross-reference multiple sources for accuracy validation
      - Integrate real-time market intelligence with existing structure
      - Maintain token efficiency while incorporating fresh data
   e. Validate generated data with enhanced quality checks
   f. Create backup of existing data
   g. Save updated data with new version number and source metadata
   h. Log all scraping and update activities
3. Clean up old backup files (>30 days)
```

### 2. Trending Sector Updates

```
1. Query sector analytics for frequently requested sectors (last 7 days)
2. Identify sectors with ≥3 queries
3. Prioritize top 10 trending sectors
4. Update trending sectors with fresh AI-generated data
5. Log trending update activities
```

### 3. Manual Updates

```
1. Admin triggers update via dashboard
2. Select specific country or all countries
3. Real-time progress tracking
4. Immediate feedback and results display
```

## 🤖 AI Prompt Engineering

### Sector Update Prompt Structure

```
You are a business intelligence expert specializing in African markets.
Update the business sector data for {SECTOR} in {COUNTRY} with current 2025 information.

CURRENT SECTOR DATA:
{existing_sector_json}

REQUIREMENTS:
1. Maintain the exact same JSON structure
2. Update market size, growth rates, and regulatory information to reflect 2025 realities
3. Keep the overview concise but informative
4. Ensure all data is specific to {COUNTRY}'s market context
5. Include recent policy changes, market trends, and investment opportunities
6. Keep total response under 400 tokens

FOCUS AREAS FOR UPDATE:
- Current market size and growth projections for 2025
- Recent regulatory changes or policy updates
- New investment opportunities or funding programs
- Emerging trends and technologies in this sector
- Key challenges and opportunities specific to {COUNTRY}

Return ONLY the updated JSON object with no additional text or formatting.
```

## 📊 Data Quality Validation

### Validation Checks

1. **Structure Validation**
   - Required fields: `sector_name`, `overview`, `key_conditions`
   - Required conditions: `regulatory_environment`, `market_size_and_growth`

2. **Content Validation**
   - Non-empty field values
   - Valid JSON structure
   - Reasonable data types

3. **Token Efficiency**
   - Maximum 400 tokens per sector
   - Optimized prompt structure
   - Efficient data representation

## 🔧 Admin Interface

### Dashboard Location
`WordPress Admin → ChatGABI → Sector Updates`

### Features

1. **System Status**
   - Auto-update status
   - API key configuration
   - Database table status
   - Recent update statistics
   - Next scheduled updates

2. **Manual Controls**
   - Country-specific updates
   - Bulk update all countries
   - Trending sector updates
   - Real-time progress tracking

3. **Settings**
   - Enable/disable auto-updates
   - Update frequency configuration
   - Trending updates toggle

## 🧪 Testing

### Test Script
Run `/test-sector-updater.php` to verify system functionality:

1. Class existence and initialization
2. Database table creation
3. OpenAI API configuration
4. Cron job scheduling
5. Existing data file accessibility
6. System status function
7. Admin page integration

### Expected Results
- 85%+ test pass rate for production readiness
- All core components functional
- Proper error handling and logging

## 🚀 Deployment Checklist

### Prerequisites
- [ ] OpenAI API key configured
- [ ] WordPress cron enabled
- [ ] Sufficient disk space for backups
- [ ] Admin user permissions

### Installation Steps
1. [ ] Include `sector-data-updater.php` in `functions.php`
2. [ ] Run database table creation
3. [ ] Configure OpenAI API key
4. [ ] Test system with `/test-sector-updater.php`
5. [ ] Enable auto-updates in admin dashboard
6. [ ] Monitor first automated update cycle

### Post-Deployment
- [ ] Monitor update logs for errors
- [ ] Verify data quality after first update
- [ ] Check API usage and costs
- [ ] Set up backup retention policy

## 📈 Performance Metrics

### Key Indicators
- **Update Success Rate**: Target >95%
- **API Token Usage**: <400 tokens per sector
- **Update Frequency**: Weekly for all sectors, daily for trending
- **Response Time**: <5 minutes per country update
- **Data Freshness**: <7 days for any sector

### Monitoring
- Update logs in database
- WordPress error logs
- OpenAI API usage tracking
- File system backup monitoring

## 🔒 Security Considerations

### API Key Protection
- Stored in WordPress options (encrypted)
- Never exposed in logs or frontend
- Rate limiting to prevent abuse

### File System Security
- Backup files in protected directory
- Atomic file operations
- Rollback capability for failed updates

### Access Control
- Admin-only access to update functions
- Nonce verification for all AJAX requests
- Capability checks for all operations

## 🐛 Troubleshooting

### Common Issues

1. **Updates Not Running**
   - Check cron job scheduling
   - Verify auto-updates enabled
   - Check OpenAI API key

2. **API Errors**
   - Verify API key validity
   - Check rate limiting
   - Monitor token usage

3. **File Save Errors**
   - Check directory permissions
   - Verify disk space
   - Check backup creation

### Debug Mode
Enable WordPress debug logging to monitor update activities:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 📞 Support

For issues or questions:
1. Check system status in admin dashboard
2. Review update logs for error messages
3. Run test script for diagnostic information
4. Check WordPress debug logs
5. Verify OpenAI API status and usage

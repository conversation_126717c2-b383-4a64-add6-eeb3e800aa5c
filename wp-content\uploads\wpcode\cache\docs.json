{"categories": {"getting-started": "Getting Started", "auto-insert-locations": "Auto-Insert Locations", "conversion-pixels": "Conversion Pixels", "functionality": "Functionality", "troubleshooting": "Troubleshooting", "account": "Account"}, "docs": {"4394": {"title": "Why Are My PHP Snippets Not Being Executed in WPCode?", "url": "https://wpcode.com/docs/why-are-my-php-snippets-not-being-executed-in-wpcode/", "categories": ["troubleshooting"]}, "3497": {"title": "Enable Permissions to Deploy From Library", "url": "https://wpcode.com/docs/deploy-from-library-missing-permissions/", "categories": ["functionality"]}, "3093": {"title": "Run Code Snippets on Demand", "url": "https://wpcode.com/docs/run-code-snippets-on-demand/", "categories": ["auto-insert-locations"]}, "3029": {"title": "How to use Snippet Files for JavaScript and CSS", "url": "https://wpcode.com/docs/snippet-files/", "categories": ["functionality"]}, "2988": {"title": "How to Generate an Invoice For Your WPCode Purchase", "url": "https://wpcode.com/docs/how-to-generate-an-invoice/", "categories": ["account"]}, "2979": {"title": "How to Upgrade Your WPCode License", "url": "https://wpcode.com/docs/how-to-upgrade-your-wpcode-license/", "categories": ["account"]}, "2973": {"title": "How to Log in If You’ve Lost Your Username or Password", "url": "https://wpcode.com/docs/how-to-log-in-if-youve-lost-your-username-or-password/", "categories": ["account"]}, "2848": {"title": "How to use the Multisite Addon", "url": "https://wpcode.com/docs/how-to-use-the-multisite-addon/", "categories": ["functionality"]}, "2508": {"title": "How to Renew your WPCode License", "url": "https://wpcode.com/docs/how-to-renew-your-wpcode-license/", "categories": ["account"]}, "2364": {"title": "Access Control", "url": "https://wpcode.com/docs/access-control/", "categories": ["functionality"]}, "2351": {"title": "I can't find the script I added in the frontend", "url": "https://wpcode.com/docs/i-cant-find-the-script-i-added-in-the-frontend/", "categories": ["troubleshooting"]}, "2349": {"title": "How to find where a script or code is coming from in WPCode", "url": "https://wpcode.com/docs/how-to-find-where-a-script-or-code-is-coming-from-in-wpcode/", "categories": ["troubleshooting"]}, "2302": {"title": "File Editor", "url": "https://wpcode.com/docs/file-editor/", "categories": ["functionality"]}, "2324": {"title": "How to fix WPCode Permissions Error", "url": "https://wpcode.com/docs/how-to-fix-wpcode-permissions-error/", "categories": ["troubleshooting"]}, "2290": {"title": "How to debug PHP errors in WPCode", "url": "https://wpcode.com/docs/how-to-debug-php-errors-in-wpcode/", "categories": ["troubleshooting"]}, "2142": {"title": "How To Use the Conversion Pixels Custom Click Tracking", "url": "https://wpcode.com/docs/conversion-pixels-custom-click-tracking/", "categories": ["conversion-pixels"]}, "2140": {"title": "How to find the right CSS Selector", "url": "https://wpcode.com/docs/finding-css-selector/", "categories": ["troubleshooting"]}, "2144": {"title": "How to find your Snapchat Pixel Id and Conversions API Token", "url": "https://wpcode.com/docs/how-to-find-your-snapchat-pixel-id-and-conversions-api-token/", "categories": ["conversion-pixels"]}, "2085": {"title": "Using Shortcode Attributes", "url": "https://wpcode.com/docs/shortcode-attributes/", "categories": ["functionality"]}, "2094": {"title": "Using Smart Tags", "url": "https://wpcode.com/docs/smart-tags/", "categories": ["functionality"]}, "2055": {"title": "Using Snippet Error Logging", "url": "https://wpcode.com/docs/using-snippet-error-logging/", "categories": ["troubleshooting"]}, "2002": {"title": "Usage Tracking", "url": "https://wpcode.com/docs/usage-tracking/", "categories": []}, "1894": {"title": "Using CSS Selectors to Insert Snippets Anywhere", "url": "https://wpcode.com/docs/using-css-selectors/", "categories": ["auto-insert-locations"]}, "1824": {"title": "How to create advanced scheduling rules", "url": "https://wpcode.com/docs/how-to-create-advanced-scheduling-rules/", "categories": ["functionality"]}, "1822": {"title": "Conditional Logic PHP Snippets", "url": "https://wpcode.com/docs/conditional-logic-php-snippets/", "categories": ["troubleshooting"]}, "1761": {"title": "How to find your TikTok Pixel ID and Events API Access Token", "url": "https://wpcode.com/docs/how-to-find-your-tiktok-pixel-id-and-events-api-access-token/", "categories": ["conversion-pixels"]}, "1759": {"title": "How to find your Pinterest Tag ID and Conversion Access Token", "url": "https://wpcode.com/docs/how-to-find-your-pinterest-tag-id-and-conversion-access-token/", "categories": ["conversion-pixels"]}, "1757": {"title": "How to find your Google Ads Tag ID & Conversion Label", "url": "https://wpcode.com/docs/how-to-find-your-google-ads-tag-id/", "categories": ["conversion-pixels"]}, "1755": {"title": "How to find your Google Analytics ID", "url": "https://wpcode.com/docs/how-to-find-your-google-analytics-id/", "categories": ["conversion-pixels"]}, "1753": {"title": "How to Find your Facebook Pixel ID and Conversions API Token", "url": "https://wpcode.com/docs/how-to-find-your-facebook-pixel-id-and-conversions-api-token/", "categories": ["conversion-pixels"]}, "1721": {"title": "WPCode Library 1-click install", "url": "https://wpcode.com/docs/update-plugin-for-1-click-install/", "categories": ["getting-started"]}, "1604": {"title": "How to Install and Activate WPCode: A Step-by-Step Guide", "url": "https://wpcode.com/docs/how-to-install-the-wpcode-plugin/", "categories": ["getting-started"]}, "65": {"title": "Using the Global Header & Footer settings", "url": "https://wpcode.com/docs/using-the-global-header-footer-settings/", "categories": ["getting-started"]}, "56": {"title": "How To Use Snippets Smart Conditional Logic", "url": "https://wpcode.com/docs/how-to-use-snippets-smart-conditional-logic/", "categories": ["getting-started"]}, "53": {"title": "A Complete Guide to Snippet Locations", "url": "https://wpcode.com/docs/a-complete-guide-to-snippet-locations/", "categories": ["getting-started"]}, "46": {"title": "PHP Error Handling & Safe Mode", "url": "https://wpcode.com/docs/php-error-handling-safe-mode/", "categories": ["troubleshooting"]}, "27": {"title": "How To Create Your First Snippet?", "url": "https://wpcode.com/docs/how-to-create-your-first-snippet/", "categories": ["getting-started"]}, "9": {"title": "What Type of Snippet Do I Need?", "url": "https://wpcode.com/docs/what-type-of-snippet-do-i-need/", "categories": ["getting-started"]}}}
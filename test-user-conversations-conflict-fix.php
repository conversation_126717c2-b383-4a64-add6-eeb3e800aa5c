<?php
/**
 * Test User Conversations Conflict Fix
 * 
 * Tests the resolution of chatgabi_get_user_conversations() function conflict
 * and verifies database schema compatibility
 */

// Include WordPress
require_once 'wp-config.php';

// Set up WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI User Conversations Conflict Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; font-weight: 600; }
        .error { color: #dc3545; font-weight: 600; }
        .warning { color: #ffc107; font-weight: 600; }
        .info { color: #17a2b8; font-weight: 600; }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            overflow-x: auto;
        }
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }
        .summary h2 { margin-top: 0; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 ChatGABI User Conversations Conflict Fix Test</h1>
            <p>Testing resolution of chatgabi_get_user_conversations() function conflict and database schema issues</p>
            <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <?php
        global $wpdb;
        $tests_passed = 0;
        $tests_total = 0;
        $issues_found = array();
        $fixes_applied = array();
        ?>

        <!-- Test 1: Database Schema Verification -->
        <div class="test-section">
            <h2>🗄️ Test 1: Database Schema Verification and Fixes</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Database Table and Column Analysis</h3>';
            
            // Check and fix database schema issues
            $schema_fixes = array();
            
            // 1. Check prompt templates table
            $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
            $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'");
            
            if ($templates_exists) {
                $columns = $wpdb->get_results("DESCRIBE $templates_table");
                $column_names = array_column($columns, 'Field');
                
                // Check for prompt_text vs prompt_content issue
                if (in_array('prompt_text', $column_names) && !in_array('prompt_content', $column_names)) {
                    echo '<p class="warning">⚠️ Templates table uses prompt_text column (expected prompt_content)</p>';
                    $schema_fixes['templates_column_mapping'] = 'prompt_text';
                } elseif (in_array('prompt_content', $column_names)) {
                    echo '<p class="success">✅ Templates table has correct prompt_content column</p>';
                } else {
                    echo '<p class="error">❌ Templates table missing both prompt_text and prompt_content columns</p>';
                    $issues_found[] = 'Templates table missing content column';
                }
                
                // Check for language column
                if (!in_array('language', $column_names) && !in_array('language_code', $column_names)) {
                    echo '<p class="warning">⚠️ Templates table missing language column</p>';
                    $schema_fixes['templates_language_missing'] = true;
                } else {
                    echo '<p class="success">✅ Templates table has language column</p>';
                }
            } else {
                echo '<p class="error">❌ Templates table does not exist</p>';
                $issues_found[] = 'Templates table missing';
            }
            
            // 2. Check feedback table
            $feedback_table = $wpdb->prefix . 'chatgabi_feedback';
            $feedback_exists = $wpdb->get_var("SHOW TABLES LIKE '$feedback_table'");
            
            if ($feedback_exists) {
                $columns = $wpdb->get_results("DESCRIBE $feedback_table");
                $column_names = array_column($columns, 'Field');
                
                // Check for helpfulness_score vs category_helpfulness
                if (!in_array('helpfulness_score', $column_names) && in_array('category_helpfulness', $column_names)) {
                    echo '<p class="warning">⚠️ Feedback table uses category_helpfulness (expected helpfulness_score)</p>';
                    $schema_fixes['feedback_column_mapping'] = array(
                        'helpfulness_score' => 'category_helpfulness',
                        'accuracy_score' => 'category_accuracy',
                        'relevance_score' => 'category_relevance',
                        'clarity_score' => 'category_clarity'
                    );
                } else {
                    echo '<p class="success">✅ Feedback table has expected score columns</p>';
                }
            } else {
                echo '<p class="error">❌ Feedback table does not exist</p>';
                $issues_found[] = 'Feedback table missing';
            }
            
            // 3. Check credit usage logs table
            $credit_logs_table = $wpdb->prefix . 'chatgabi_credit_usage_logs';
            $credit_logs_exists = $wpdb->get_var("SHOW TABLES LIKE '$credit_logs_table'");
            
            if (!$credit_logs_exists) {
                // Try alternative table names
                $alt_tables = array(
                    $wpdb->prefix . 'businesscraft_ai_credit_logs',
                    $wpdb->prefix . 'chatgabi_credit_transactions'
                );
                
                foreach ($alt_tables as $alt_table) {
                    if ($wpdb->get_var("SHOW TABLES LIKE '$alt_table'")) {
                        echo '<p class="success">✅ Found alternative credit table: ' . $alt_table . '</p>';
                        $schema_fixes['credit_table_alternative'] = $alt_table;
                        break;
                    }
                }
                
                if (!isset($schema_fixes['credit_table_alternative'])) {
                    echo '<p class="error">❌ No credit usage logs table found</p>';
                    $issues_found[] = 'Credit logs table missing';
                }
            } else {
                echo '<p class="success">✅ Credit usage logs table exists</p>';
            }
            
            if (empty($issues_found)) {
                $tests_passed++;
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Test 2: Function Conflict Resolution -->
        <div class="test-section">
            <h2>🔧 Test 2: Function Conflict Resolution</h2>
            
            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Function Declaration Status</h3>';
            
            // Check if both functions exist without conflict
            $user_pref_function_exists = function_exists('chatgabi_get_user_conversations');
            $db_optimizer_function_exists = false;
            
            // Check if database optimizer class exists
            if (class_exists('BusinessCraft_Database_Optimizer')) {
                echo '<p class="success">✅ Database optimizer class exists</p>';
                
                global $chatgabi_db_optimizer;
                if (isset($chatgabi_db_optimizer) && method_exists($chatgabi_db_optimizer, 'get_user_conversations')) {
                    echo '<p class="success">✅ Database optimizer get_user_conversations method exists</p>';
                    $db_optimizer_function_exists = true;
                } else {
                    echo '<p class="error">❌ Database optimizer method missing</p>';
                    $issues_found[] = 'Database optimizer method missing';
                }
            } else {
                echo '<p class="error">❌ Database optimizer class missing</p>';
                $issues_found[] = 'Database optimizer class missing';
            }
            
            if ($user_pref_function_exists) {
                echo '<p class="success">✅ Global chatgabi_get_user_conversations function exists</p>';
            } else {
                echo '<p class="error">❌ Global function missing</p>';
                $issues_found[] = 'Global function missing';
            }
            
            if ($user_pref_function_exists && $db_optimizer_function_exists) {
                echo '<p class="success">✅ No function redeclaration conflict detected</p>';
                $tests_passed++;
            } else {
                echo '<p class="error">❌ Function conflict may still exist</p>';
            }
            
            echo '</div>';
            ?>
        </div>

        <!-- Test 3: Database Query Fixes -->
        <div class="test-section">
            <h2>🔍 Test 3: Database Query Fixes</h2>

            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>Fixed Database Queries Test</h3>';

            try {
                // Test 1: Fixed prompt templates query
                if (isset($schema_fixes['templates_column_mapping'])) {
                    $prompt_column = $schema_fixes['templates_column_mapping'];
                } else {
                    $prompt_column = 'prompt_content';
                }

                $language_column = isset($schema_fixes['templates_language_missing']) ? "'en'" : 'language_code';

                $templates_query = $wpdb->prepare("
                    SELECT t.id, t.title, t.description, t.$prompt_column as prompt_content,
                           t.category_id, t.usage_count, t.rating_average,
                           $language_column as language_code,
                           c.name as category_name
                    FROM $templates_table t
                    LEFT JOIN {$wpdb->prefix}chatgabi_template_categories c ON t.category_id = c.id
                    WHERE t.is_public = 1
                    ORDER BY t.is_featured DESC, t.rating_average DESC, t.usage_count DESC
                    LIMIT %d", 5
                );

                $templates_result = $wpdb->get_results($templates_query);

                if ($wpdb->last_error) {
                    echo '<p class="error">❌ Templates query error: ' . $wpdb->last_error . '</p>';
                    $issues_found[] = 'Templates query failed: ' . $wpdb->last_error;
                } else {
                    echo '<p class="success">✅ Templates query executed successfully (' . count($templates_result) . ' results)</p>';
                }

                // Test 2: Fixed feedback analytics query
                if (isset($schema_fixes['feedback_column_mapping'])) {
                    $feedback_columns = $schema_fixes['feedback_column_mapping'];
                    $helpfulness_col = $feedback_columns['helpfulness_score'];
                    $accuracy_col = $feedback_columns['accuracy_score'];
                    $relevance_col = $feedback_columns['relevance_score'];
                    $clarity_col = $feedback_columns['clarity_score'];
                } else {
                    $helpfulness_col = 'helpfulness_score';
                    $accuracy_col = 'accuracy_score';
                    $relevance_col = 'relevance_score';
                    $clarity_col = 'clarity_score';
                }

                $feedback_query = $wpdb->prepare("
                    SELECT
                        AVG(rating_score) as avg_rating,
                        COUNT(*) as total_feedback,
                        SUM(CASE WHEN rating_score >= 4 THEN 1 ELSE 0 END) as positive_feedback,
                        SUM(CASE WHEN rating_score <= 2 THEN 1 ELSE 0 END) as negative_feedback,
                        AVG($helpfulness_col) as avg_helpfulness,
                        AVG($accuracy_col) as avg_accuracy,
                        AVG($relevance_col) as avg_relevance,
                        AVG($clarity_col) as avg_clarity,
                        user_country,
                        COUNT(DISTINCT user_id) as unique_users
                    FROM $feedback_table
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)
                    GROUP BY user_country
                    ORDER BY total_feedback DESC
                    LIMIT 10", 30
                );

                $feedback_result = $wpdb->get_results($feedback_query);

                if ($wpdb->last_error) {
                    echo '<p class="error">❌ Feedback query error: ' . $wpdb->last_error . '</p>';
                    $issues_found[] = 'Feedback query failed: ' . $wpdb->last_error;
                } else {
                    echo '<p class="success">✅ Feedback analytics query executed successfully (' . count($feedback_result) . ' results)</p>';
                }

                if (count($issues_found) <= 2) { // Allow for minor issues
                    $tests_passed++;
                }

            } catch (Exception $e) {
                echo '<p class="error">❌ Database query test failed: ' . $e->getMessage() . '</p>';
                $issues_found[] = 'Database query test exception: ' . $e->getMessage();
            }

            echo '</div>';
            ?>
        </div>

        <!-- Test 4: Function Integration Test -->
        <div class="test-section">
            <h2>⚡ Test 4: Function Integration Test</h2>

            <?php
            $tests_total++;
            echo '<div class="test-item">';
            echo '<h3>ChatGABI Function Integration</h3>';

            // Test the actual function call
            try {
                if (function_exists('chatgabi_get_user_conversations')) {
                    echo '<p class="info">🔧 Testing chatgabi_get_user_conversations function...</p>';

                    // Test with a sample user ID
                    $test_conversations = chatgabi_get_user_conversations(1, 5, 0);

                    if (is_array($test_conversations)) {
                        echo '<p class="success">✅ Function executed successfully, returned array with ' . count($test_conversations) . ' items</p>';
                        $tests_passed++;
                    } else {
                        echo '<p class="error">❌ Function did not return expected array</p>';
                        $issues_found[] = 'Function returned unexpected result';
                    }
                } else {
                    echo '<p class="error">❌ chatgabi_get_user_conversations function not available</p>';
                    $issues_found[] = 'Main function not available';
                }

                // Test database optimizer method
                if (class_exists('BusinessCraft_Database_Optimizer')) {
                    global $chatgabi_db_optimizer;
                    if (isset($chatgabi_db_optimizer) && method_exists($chatgabi_db_optimizer, 'get_user_conversations')) {
                        echo '<p class="info">🔧 Testing database optimizer method...</p>';

                        $optimizer_conversations = $chatgabi_db_optimizer->get_user_conversations(1, 5, 0);

                        if (is_array($optimizer_conversations)) {
                            echo '<p class="success">✅ Database optimizer method executed successfully</p>';
                        } else {
                            echo '<p class="error">❌ Database optimizer method failed</p>';
                            $issues_found[] = 'Database optimizer method failed';
                        }
                    }
                }

            } catch (Exception $e) {
                echo '<p class="error">❌ Function integration test failed: ' . $e->getMessage() . '</p>';
                $issues_found[] = 'Function integration error: ' . $e->getMessage();
            }

            echo '</div>';
            ?>
        </div>

        <!-- Summary -->
        <div class="summary">
            <h2>📊 ChatGABI Conflict Fix Summary</h2>
            <p><strong>Tests Passed:</strong> <?php echo $tests_passed; ?> / <?php echo $tests_total; ?></p>
            <p><strong>Success Rate:</strong> <?php echo round(($tests_passed / $tests_total) * 100, 1); ?>%</p>

            <?php if (!empty($issues_found)): ?>
                <h3>🚨 Issues Found:</h3>
                <ul>
                    <?php foreach ($issues_found as $issue): ?>
                        <li><?php echo esc_html($issue); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p class="success">🎉 All ChatGABI conflicts successfully resolved!</p>
            <?php endif; ?>

            <h3>✅ Resolution Summary:</h3>
            <ul>
                <li><strong>Function Conflicts:</strong> <?php echo function_exists('chatgabi_get_user_conversations') ? 'Resolved' : 'Unresolved'; ?></li>
                <li><strong>Database Schema:</strong> <?php echo empty($schema_fixes) ? 'Compatible' : 'Adapted'; ?></li>
                <li><strong>Performance Enhancements:</strong> <?php echo class_exists('BusinessCraft_Database_Optimizer') ? 'Active' : 'Inactive'; ?></li>
                <li><strong>WordPress Loading:</strong> <?php echo function_exists('wp_get_current_user') ? 'Successful' : 'Failed'; ?></li>
            </ul>

            <?php if (!empty($schema_fixes)): ?>
                <h3>🔧 Schema Adaptations Applied:</h3>
                <ul>
                    <?php foreach ($schema_fixes as $fix_type => $fix_value): ?>
                        <li><strong><?php echo esc_html($fix_type); ?>:</strong> <?php echo esc_html(is_array($fix_value) ? implode(', ', $fix_value) : $fix_value); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <h3>🔄 Next Steps:</h3>
            <ul>
                <li>Test ChatGABI user conversation functionality</li>
                <li>Verify database performance optimizations</li>
                <li>Monitor for any remaining database errors</li>
                <li>Test Redis caching if configured</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ChatGABI Conflict Fix Test Page Loaded');
            console.log('Tests Passed: <?php echo $tests_passed; ?> / <?php echo $tests_total; ?>');

            // Test if WordPress is accessible
            if (typeof wp !== 'undefined') {
                console.log('✅ WordPress JavaScript API available');
            } else {
                console.log('⚠️ WordPress JavaScript API not available');
            }
        });
    </script>
</body>
</html>

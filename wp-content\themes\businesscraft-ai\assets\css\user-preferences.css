/**
 * ChatGABI User Preferences Styles
 * 
 * Styles for the user preferences dashboard and form components
 */

/* Container and Layout */
.chatgabi-preferences-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.chatgabi-preferences-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* Header */
.preferences-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 20px;
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 124, 186, 0.2);
}

.preferences-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preferences-description {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* Form Sections */
.preferences-form {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.preferences-section {
    padding: 30px;
    border-bottom: 1px solid #e9ecef;
}

.preferences-section:last-child {
    border-bottom: none;
}

.section-title {
    display: flex;
    align-items: center;
    margin: 0 0 25px 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
}

.section-icon {
    margin-right: 12px;
    font-size: 1.6rem;
}

/* Grid Layout */
.preferences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

/* Preference Items */
.preference-item {
    display: flex;
    flex-direction: column;
}

.preference-label {
    font-weight: 600;
    color: #34495e;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.preference-select {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.preference-select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.preference-select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* Custom Checkbox */
.preference-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #34495e;
    font-size: 0.95rem;
}

.preference-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    background: white;
}

.preference-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #007cba;
    border-color: #007cba;
}

.preference-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.preference-checkbox:hover .checkmark {
    border-color: #007cba;
}

/* Preference Description */
.preference-description {
    margin: 8px 0 0 0;
    font-size: 0.85rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Action Buttons */
.preferences-actions {
    padding: 30px;
    background: #f8f9fa;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.save-preferences-btn,
.reset-preferences-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-preferences-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.save-preferences-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.save-preferences-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.reset-preferences-btn {
    background: #6c757d;
    color: white;
}

.reset-preferences-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Loading States */
.btn-loading {
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Messages */
.preferences-messages {
    margin-top: 20px;
}

.preference-message {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    font-weight: 500;
    animation: slideIn 0.3s ease;
}

.preference-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.preference-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.preference-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.message-icon {
    margin-right: 10px;
    font-size: 1.1rem;
}

.message-text {
    flex: 1;
}

.message-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;
    margin-left: 10px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Analytics Dashboard */
.analytics-dashboard {
    margin-top: 20px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.analytics-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-color: #007cba;
}

.analytics-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.analytics-content h3 {
    margin: 0 0 5px 0;
    font-size: 2rem;
    font-weight: 700;
    color: #007cba;
}

.analytics-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

.analytics-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.analytics-section h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.feature-usage-list {
    space-y: 10px;
}

.feature-usage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.feature-usage-item:last-child {
    border-bottom: none;
}

.feature-name {
    font-weight: 500;
    color: #34495e;
}

.feature-count {
    color: #007cba;
    font-weight: 600;
    font-size: 0.9rem;
}

.activity-timeline {
    space-y: 15px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.2rem;
    margin-top: 2px;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin: 0 0 4px 0;
    color: #34495e;
    font-size: 0.9rem;
    line-height: 1.4;
}

.activity-time {
    color: #6c757d;
    font-size: 0.8rem;
}

.analytics-preferences h4 {
    margin: 20px 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Account Management */
.account-management {
    margin-top: 20px;
}

.account-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.account-actions h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.export-btn {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.export-btn:hover {
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.import-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.import-btn:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.warning-btn {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.warning-btn:hover {
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.danger-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.danger-btn:hover {
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-icon {
    font-size: 1.1rem;
}

/* Import/Export Section */
.import-export-section {
    margin-top: 20px;
}

.file-upload-area {
    margin-top: 10px;
}

.backup-restore-info {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.info-card h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.info-card ul {
    margin: 0;
    padding-left: 20px;
}

.info-card li {
    margin-bottom: 8px;
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

.info-card li:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatgabi-preferences-container {
        padding: 15px;
    }
    
    .preferences-header {
        padding: 20px 15px;
    }
    
    .preferences-header h1 {
        font-size: 2rem;
    }
    
    .preferences-section {
        padding: 20px 15px;
    }
    
    .preferences-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .preferences-actions {
        padding: 20px 15px;
        flex-direction: column;
    }
    
    .save-preferences-btn,
    .reset-preferences-btn {
        width: 100%;
        justify-content: center;
    }

    .analytics-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .analytics-details {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .preferences-header h1 {
        font-size: 1.8rem;
    }
    
    .preferences-description {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.2rem;
    }
}

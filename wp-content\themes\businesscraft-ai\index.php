<?php
/**
 * The main template file
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

get_header(); ?>

<main class="site-main">
    <div class="container">
        <?php if (have_posts()) : ?>
            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                    <header class="entry-header">
                        <h1 class="entry-title">
                            <a href="<?php the_permalink(); ?>" rel="bookmark">
                                <?php the_title(); ?>
                            </a>
                        </h1>
                        <div class="entry-meta">
                            <span class="posted-on">
                                <?php echo get_the_date(); ?>
                            </span>
                            <span class="byline">
                                <?php _e('by', 'businesscraft-ai'); ?> 
                                <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>">
                                    <?php the_author(); ?>
                                </a>
                            </span>
                        </div>
                    </header>

                    <div class="entry-content">
                        <?php
                        if (is_singular()) {
                            the_content();
                        } else {
                            the_excerpt();
                        }
                        ?>
                    </div>

                    <?php if (!is_singular()) : ?>
                        <footer class="entry-footer">
                            <a href="<?php the_permalink(); ?>" class="read-more">
                                <?php _e('Read More', 'businesscraft-ai'); ?>
                            </a>
                        </footer>
                    <?php endif; ?>
                </article>
            <?php endwhile; ?>

            <?php
            // Pagination
            the_posts_pagination(array(
                'prev_text' => __('Previous', 'businesscraft-ai'),
                'next_text' => __('Next', 'businesscraft-ai'),
            ));
            ?>

        <?php else : ?>
            <div class="no-posts">
                <h2><?php _e('Nothing Found', 'businesscraft-ai'); ?></h2>
                <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'businesscraft-ai'); ?></p>
                <?php get_search_form(); ?>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php get_footer(); ?>

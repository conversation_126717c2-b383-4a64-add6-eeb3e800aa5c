<?php
/**
 * Comprehensive ChatGABI Feedback System Diagnostic & Fix
 * 
 * This script diagnoses and fixes all critical UI issues in the feedback system
 */

// Load WordPress
require_once 'wp-config.php';
require_once 'wp-load.php';

echo '<h1>🔧 ChatGABI Feedback System Comprehensive Fix</h1>';
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #dee2e6; }
.code { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
.button { display: inline-block; padding: 8px 16px; margin: 5px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; }
</style>';

$issues_found = 0;
$issues_fixed = 0;

// Step 1: Duplicate Menu Issue Analysis
echo '<div class="section">';
echo '<h2>🔍 Step 1: Duplicate Menu Issue Analysis</h2>';

global $wp_filter;

// Check admin_menu hooks
$admin_menu_hooks = array();
if (isset($wp_filter['admin_menu'])) {
    foreach ($wp_filter['admin_menu']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            $function_name = '';
            if (is_array($callback['function'])) {
                $function_name = is_object($callback['function'][0]) 
                    ? get_class($callback['function'][0]) . '::' . $callback['function'][1]
                    : $callback['function'][0] . '::' . $callback['function'][1];
            } elseif (is_string($callback['function'])) {
                $function_name = $callback['function'];
            }
            
            if (strpos($function_name, 'feedback') !== false || strpos($function_name, 'chatgabi') !== false) {
                $admin_menu_hooks[] = array(
                    'function' => $function_name,
                    'priority' => $priority
                );
            }
        }
    }
}

echo '<h3>Admin Menu Hooks Found:</h3>';
foreach ($admin_menu_hooks as $hook) {
    echo "<div class='info'>📌 {$hook['function']} (priority: {$hook['priority']})</div>";
}

// Check for duplicate feedback menu registrations
$feedback_registrations = 0;
foreach ($admin_menu_hooks as $hook) {
    if (strpos($hook['function'], 'feedback') !== false) {
        $feedback_registrations++;
    }
}

if ($feedback_registrations > 1) {
    echo "<div class='error'>❌ Found {$feedback_registrations} feedback menu registrations (should be 1)</div>";
    $issues_found++;
} else {
    echo "<div class='success'>✅ Feedback menu registration count is correct</div>";
}

echo '</div>';

// Step 2: Function Availability Check
echo '<div class="section">';
echo '<h2>🔍 Step 2: Critical Function Availability Check</h2>';

$required_functions = array(
    'chatgabi_render_text_feedback' => 'Text Feedback Renderer',
    'chatgabi_render_training_data' => 'Training Data Renderer', 
    'chatgabi_render_feedback_export' => 'Export Functionality',
    'chatgabi_feedback_admin_page' => 'Main Admin Page',
    'chatgabi_get_feedback_stats' => 'Statistics Function',
    'chatgabi_submit_feedback' => 'Feedback Submission',
    'chatgabi_export_feedback_data' => 'Data Export Function'
);

$missing_functions = array();
foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<div class='success'>✅ {$description}: {$function}()</div>";
    } else {
        echo "<div class='error'>❌ {$description}: {$function}() - MISSING</div>";
        $missing_functions[] = $function;
        $issues_found++;
    }
}

echo '</div>';

// Step 3: Database Table Verification
echo '<div class="section">';
echo '<h2>🔍 Step 3: Database Table Verification</h2>';

global $wpdb;
$feedback_table = $wpdb->prefix . 'chatgabi_feedback';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$feedback_table'") === $feedback_table;

if ($table_exists) {
    echo "<div class='success'>✅ Feedback table exists: {$feedback_table}</div>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $feedback_table");
    $required_columns = array('id', 'user_id', 'rating_score', 'feedback_text', 'created_at');
    $missing_columns = array();
    
    $existing_columns = array_column($columns, 'Field');
    foreach ($required_columns as $col) {
        if (!in_array($col, $existing_columns)) {
            $missing_columns[] = $col;
        }
    }
    
    if (empty($missing_columns)) {
        echo "<div class='success'>✅ All required columns present</div>";
    } else {
        echo "<div class='error'>❌ Missing columns: " . implode(', ', $missing_columns) . "</div>";
        $issues_found++;
    }
    
    // Check for sample data
    $data_count = $wpdb->get_var("SELECT COUNT(*) FROM $feedback_table");
    echo "<div class='info'>📊 Current feedback records: {$data_count}</div>";
    
} else {
    echo "<div class='error'>❌ Feedback table missing: {$feedback_table}</div>";
    $issues_found++;
}

echo '</div>';

// Step 4: Asset Loading Verification
echo '<div class="section">';
echo '<h2>🔍 Step 4: Asset Loading Verification</h2>';

$assets_to_check = array(
    'assets/js/feedback-rating.js' => 'Feedback Rating JavaScript',
    'assets/css/feedback-rating.css' => 'Feedback Rating CSS',
    'inc/feedback-system.php' => 'Feedback System Core',
    'inc/feedback-admin.php' => 'Feedback Admin Interface'
);

foreach ($assets_to_check as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        $file_size = filesize($file_path);
        echo "<div class='success'>✅ {$description}: " . number_format($file_size) . " bytes</div>";
    } else {
        echo "<div class='error'>❌ {$description}: File not found</div>";
        $issues_found++;
    }
}

echo '</div>';

// Step 5: AJAX Handler Verification
echo '<div class="section">';
echo '<h2>🔍 Step 5: AJAX Handler Verification</h2>';

$ajax_handlers = array(
    'wp_ajax_chatgabi_submit_feedback' => 'Submit Feedback',
    'wp_ajax_chatgabi_get_feedback' => 'Get Feedback',
    'wp_ajax_chatgabi_update_feedback' => 'Update Feedback'
);

foreach ($ajax_handlers as $hook => $description) {
    if (has_action($hook)) {
        echo "<div class='success'>✅ {$description} AJAX handler registered</div>";
    } else {
        echo "<div class='error'>❌ {$description} AJAX handler missing</div>";
        $issues_found++;
    }
}

echo '</div>';

// Step 6: Auto-Fix Implementation
echo '<div class="section">';
echo '<h2>🔧 Step 6: Auto-Fix Implementation</h2>';

if ($issues_found > 0) {
    echo "<div class='warning'>⚠️ Found {$issues_found} issues. Attempting auto-fix...</div>";
    
    // Fix 1: Create missing database table
    if (!$table_exists) {
        echo "<div class='info'>🔄 Creating feedback table...</div>";
        require_once get_template_directory() . '/inc/database.php';
        if (function_exists('chatgabi_create_feedback_tables')) {
            $result = chatgabi_create_feedback_tables();
            if ($result) {
                echo "<div class='success'>✅ Feedback table created successfully</div>";
                $issues_fixed++;
            } else {
                echo "<div class='error'>❌ Failed to create feedback table</div>";
            }
        }
    }
    
    // Fix 2: Initialize missing functions
    if (!empty($missing_functions)) {
        echo "<div class='info'>🔄 Loading missing functions...</div>";
        
        // Force reload feedback admin functions
        if (file_exists(get_template_directory() . '/inc/feedback-admin.php')) {
            require_once get_template_directory() . '/inc/feedback-admin.php';
            echo "<div class='success'>✅ Feedback admin functions reloaded</div>";
            $issues_fixed++;
        }
        
        // Force reload feedback system functions
        if (file_exists(get_template_directory() . '/inc/feedback-system.php')) {
            require_once get_template_directory() . '/inc/feedback-system.php';
            echo "<div class='success'>✅ Feedback system functions reloaded</div>";
            $issues_fixed++;
        }
    }
    
    // Fix 3: Re-register AJAX handlers
    if (function_exists('chatgabi_init_feedback_system')) {
        echo "<div class='info'>🔄 Re-initializing feedback system...</div>";
        chatgabi_init_feedback_system();
        echo "<div class='success'>✅ Feedback system re-initialized</div>";
        $issues_fixed++;
    }
    
} else {
    echo "<div class='success'>✅ No issues found - system is healthy!</div>";
}

echo '</div>';

// Step 7: Final Verification
echo '<div class="section">';
echo '<h2>✅ Step 7: Final Verification & Next Steps</h2>';

echo "<div class='info'>📊 <strong>Summary:</strong></div>";
echo "<div class='info'>• Issues Found: {$issues_found}</div>";
echo "<div class='info'>• Issues Fixed: {$issues_fixed}</div>";

if ($issues_found === $issues_fixed) {
    echo "<div class='success'>🎉 All issues have been resolved!</div>";
} else {
    $remaining = $issues_found - $issues_fixed;
    echo "<div class='warning'>⚠️ {$remaining} issues still need manual attention</div>";
}

echo '<h3>🚀 Next Steps:</h3>';
echo '<p><a href="/wp-admin/admin.php?page=chatgabi-feedback" class="button">📊 Test Feedback Dashboard</a></p>';
echo '<p><a href="/test-chat-feedback.php" class="button">💬 Test Chat Integration</a></p>';
echo '<p><a href="/create-sample-feedback.php" class="button">🎯 Create Sample Data</a></p>';

echo '<h3>🧪 Manual Testing Checklist:</h3>';
echo '<ol>';
echo '<li>✅ Check WordPress Admin → ChatGABI → User Feedback (should appear only once)</li>';
echo '<li>✅ Test all tabs: Overview, Ratings Analysis, Text Feedback, Training Data, Export</li>';
echo '<li>✅ Send a chat message and verify feedback interface appears</li>';
echo '<li>✅ Submit star ratings and text feedback</li>';
echo '<li>✅ Verify feedback appears in admin dashboard</li>';
echo '<li>✅ Test export functionality (CSV, JSON, XML)</li>';
echo '</ol>';

echo '</div>';

echo '<div class="section">';
echo '<h2>🔧 Advanced Debugging Information</h2>';
echo '<div class="code">';
echo '<strong>WordPress Version:</strong> ' . get_bloginfo('version') . '<br>';
echo '<strong>Theme:</strong> ' . get_template() . '<br>';
echo '<strong>Active Plugins:</strong> ' . count(get_option('active_plugins')) . '<br>';
echo '<strong>Current User:</strong> ' . (is_user_logged_in() ? wp_get_current_user()->user_login : 'Not logged in') . '<br>';
echo '<strong>Memory Limit:</strong> ' . ini_get('memory_limit') . '<br>';
echo '<strong>PHP Version:</strong> ' . PHP_VERSION . '<br>';
echo '</div>';
echo '</div>';
?>

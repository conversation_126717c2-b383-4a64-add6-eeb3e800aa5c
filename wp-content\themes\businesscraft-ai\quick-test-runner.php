<?php
/**
 * Quick Test Runner for ChatGABI Critical Fixes
 * Simple file-based test that outputs results to a log file
 */

// Start output buffering
ob_start();

// Set up error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== ChatGABI Quick Test Runner ===\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

// Test 1: Check if our new files exist
echo "Test 1: File Existence Check\n";
echo "-----------------------------\n";

$base_dir = __DIR__;
$required_files = array(
    'inc/secure-api-key-manager.php',
    'inc/enhanced-input-validator.php', 
    'inc/token-optimizer.php',
    'inc/rest-api.php',
    'inc/openai-integration.php'
);

$files_found = 0;
foreach ($required_files as $file) {
    $full_path = $base_dir . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ {$file}: EXISTS\n";
        $files_found++;
    } else {
        echo "❌ {$file}: MISSING\n";
    }
}

echo "Files Found: {$files_found}/" . count($required_files) . "\n\n";

// Test 2: Check wp-config.php for security updates
echo "Test 2: wp-config.php Security Check\n";
echo "------------------------------------\n";

$wp_config_paths = array(
    dirname(dirname(dirname(__DIR__))) . '/wp-config.php',
    'C:/xampp/htdocs/swifmind-local/wordpress/wp-config.php'
);

$wp_config_found = false;
$wp_config_content = '';

foreach ($wp_config_paths as $path) {
    if (file_exists($path)) {
        $wp_config_content = file_get_contents($path);
        $wp_config_found = true;
        echo "✅ wp-config.php found at: {$path}\n";
        break;
    }
}

if ($wp_config_found) {
    $security_checks = array(
        'businesscraft_ai_get_encrypted_api_key' => 'Secure API key function',
        '$_ENV[' => 'Environment variable usage',
        'API_KEY_ROTATION_ENABLED' => 'API key rotation config'
    );
    
    $security_score = 0;
    foreach ($security_checks as $pattern => $description) {
        if (strpos($wp_config_content, $pattern) !== false) {
            echo "✅ {$description}: FOUND\n";
            $security_score++;
        } else {
            echo "❌ {$description}: NOT FOUND\n";
        }
    }
    
    echo "Security Score: {$security_score}/" . count($security_checks) . "\n";
} else {
    echo "❌ wp-config.php not found\n";
}

echo "\n";

// Test 3: Check token optimizer for 400-token limits
echo "Test 3: Token Limit Compliance\n";
echo "------------------------------\n";

$token_optimizer_path = $base_dir . '/inc/token-optimizer.php';
if (file_exists($token_optimizer_path)) {
    $token_content = file_get_contents($token_optimizer_path);
    $token_400_count = substr_count($token_content, "'optimal_response' => 400");
    
    echo "✅ Token optimizer file exists\n";
    echo "✅ Found {$token_400_count} instances of 400-token limits\n";
    
    if ($token_400_count >= 3) {
        echo "✅ All major models have 400-token limits: PASS\n";
    } else {
        echo "❌ Missing 400-token limits: FAIL\n";
    }
} else {
    echo "❌ Token optimizer file not found\n";
}

echo "\n";

// Test 4: Check input validator security patterns
echo "Test 4: Input Validation Security\n";
echo "---------------------------------\n";

$validator_path = $base_dir . '/inc/enhanced-input-validator.php';
if (file_exists($validator_path)) {
    $validator_content = file_get_contents($validator_path);
    
    $security_patterns = array(
        'prompt_injection' => 'Prompt injection detection',
        'code_injection' => 'Code injection detection', 
        'sql_injection' => 'SQL injection detection',
        'sensitive_data' => 'Sensitive data detection'
    );
    
    echo "✅ Input validator file exists\n";
    
    $pattern_score = 0;
    foreach ($security_patterns as $pattern => $description) {
        if (strpos($validator_content, $pattern) !== false) {
            echo "✅ {$description}: IMPLEMENTED\n";
            $pattern_score++;
        } else {
            echo "❌ {$description}: MISSING\n";
        }
    }
    
    echo "Security Patterns: {$pattern_score}/" . count($security_patterns) . "\n";
} else {
    echo "❌ Input validator file not found\n";
}

echo "\n";

// Test 5: Check REST API integration
echo "Test 5: REST API Integration\n";
echo "----------------------------\n";

$rest_api_path = $base_dir . '/inc/rest-api.php';
if (file_exists($rest_api_path)) {
    $rest_content = file_get_contents($rest_api_path);
    
    $integration_checks = array(
        'businesscraft_ai_sanitize_ai_input' => 'Enhanced sanitization',
        'businesscraft_ai_validate_chat_message' => 'Enhanced validation',
        'enhanced-input-validator.php' => 'Validator inclusion'
    );
    
    echo "✅ REST API file exists\n";
    
    $integration_score = 0;
    foreach ($integration_checks as $pattern => $description) {
        if (strpos($rest_content, $pattern) !== false) {
            echo "✅ {$description}: INTEGRATED\n";
            $integration_score++;
        } else {
            echo "❌ {$description}: NOT INTEGRATED\n";
        }
    }
    
    echo "Integration Score: {$integration_score}/" . count($integration_checks) . "\n";
} else {
    echo "❌ REST API file not found\n";
}

echo "\n";

// Test 6: Check OpenAI integration updates
echo "Test 6: OpenAI Integration Updates\n";
echo "----------------------------------\n";

$openai_path = $base_dir . '/inc/openai-integration.php';
if (file_exists($openai_path)) {
    $openai_content = file_get_contents($openai_path);
    
    $openai_checks = array(
        'secure-api-key-manager.php' => 'Secure API key manager inclusion',
        'min($token_limits[\'optimal_response\'] ?? 400, 400)' => '400-token enforcement',
        'businesscraft_ai_monitor_api_key_usage' => 'API usage monitoring'
    );
    
    echo "✅ OpenAI integration file exists\n";
    
    $openai_score = 0;
    foreach ($openai_checks as $pattern => $description) {
        if (strpos($openai_content, $pattern) !== false) {
            echo "✅ {$description}: IMPLEMENTED\n";
            $openai_score++;
        } else {
            echo "❌ {$description}: NOT IMPLEMENTED\n";
        }
    }
    
    echo "OpenAI Updates: {$openai_score}/" . count($openai_checks) . "\n";
} else {
    echo "❌ OpenAI integration file not found\n";
}

echo "\n";

// Calculate overall results
$total_checks = array(
    'Files' => $files_found / count($required_files),
    'Security' => isset($security_score) ? $security_score / 3 : 0,
    'Tokens' => isset($token_400_count) && $token_400_count >= 3 ? 1 : 0,
    'Validation' => isset($pattern_score) ? $pattern_score / 4 : 0,
    'REST API' => isset($integration_score) ? $integration_score / 3 : 0,
    'OpenAI' => isset($openai_score) ? $openai_score / 3 : 0
);

$overall_score = array_sum($total_checks) / count($total_checks) * 100;

echo "=== QUICK TEST SUMMARY ===\n";
echo "Overall Implementation Score: " . round($overall_score) . "%\n\n";

foreach ($total_checks as $category => $score) {
    $percentage = round($score * 100);
    $status = $percentage >= 75 ? "✅ PASS" : ($percentage >= 50 ? "⚠️ PARTIAL" : "❌ FAIL");
    echo "{$category}: {$percentage}% {$status}\n";
}

echo "\n";

if ($overall_score >= 90) {
    echo "🎉 EXCELLENT! All critical fixes appear to be implemented!\n";
} elseif ($overall_score >= 75) {
    echo "✅ GOOD! Most critical fixes are implemented.\n";
} elseif ($overall_score >= 50) {
    echo "⚠️ PARTIAL! Some fixes need attention.\n";
} else {
    echo "❌ CRITICAL! Major implementation issues detected.\n";
}

echo "\n=== RECOMMENDATIONS ===\n";
if ($overall_score < 100) {
    echo "1. Address any failed checks above\n";
    echo "2. Run the database schema fixer if needed\n";
}
echo "3. Test the comprehensive audit script via browser\n";
echo "4. Verify functionality in WordPress admin\n";
echo "5. Test chat interface with real inputs\n";

echo "\nTest completed at: " . date('Y-m-d H:i:s') . "\n";

// Get the output and save to file
$output = ob_get_clean();

// Save to log file
$log_file = $base_dir . '/test-results-' . date('Y-m-d-H-i-s') . '.log';
file_put_contents($log_file, $output);

// Also output to screen
echo $output;
echo "\nResults saved to: {$log_file}\n";

// Return appropriate exit code
exit($overall_score >= 75 ? 0 : 1);
?>

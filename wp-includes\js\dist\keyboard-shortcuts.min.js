/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,o)=>{for(var n in o)e.o(o,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:o[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ShortcutProvider:()=>K,__unstableUseShortcutEventMatch:()=>R,store:()=>h,useShortcut:()=>T});var o={};e.r(o),e.d(o,{registerShortcut:()=>c,unregisterShortcut:()=>a});var n={};e.r(n),e.d(n,{getAllShortcutKeyCombinations:()=>m,getAllShortcutRawKeyCombinations:()=>p,getCategoryShortcuts:()=>b,getShortcutAliases:()=>w,getShortcutDescription:()=>f,getShortcutKeyCombination:()=>y,getShortcutRepresentation:()=>S});const r=window.wp.data;const i=function(e={},t){switch(t.type){case"REGISTER_SHORTCUT":return{...e,[t.name]:{category:t.category,keyCombination:t.keyCombination,aliases:t.aliases,description:t.description}};case"UNREGISTER_SHORTCUT":const{[t.name]:o,...n}=e;return n}return e};function c({name:e,category:t,description:o,keyCombination:n,aliases:r}){return{type:"REGISTER_SHORTCUT",name:e,category:t,keyCombination:n,aliases:r,description:o}}function a(e){return{type:"UNREGISTER_SHORTCUT",name:e}}const s=window.wp.keycodes,u=[],d={display:s.displayShortcut,raw:s.rawShortcut,ariaLabel:s.shortcutAriaLabel};function l(e,t){return e?e.modifier?d[t][e.modifier](e.character):e.character:null}function y(e,t){return e[t]?e[t].keyCombination:null}function S(e,t,o="display"){return l(y(e,t),o)}function f(e,t){return e[t]?e[t].description:null}function w(e,t){return e[t]&&e[t].aliases?e[t].aliases:u}const m=(0,r.createSelector)(((e,t)=>[y(e,t),...w(e,t)].filter(Boolean)),((e,t)=>[e[t]])),p=(0,r.createSelector)(((e,t)=>m(e,t).map((e=>l(e,"raw")))),((e,t)=>[e[t]])),b=(0,r.createSelector)(((e,t)=>Object.entries(e).filter((([,e])=>e.category===t)).map((([e])=>e))),(e=>[e])),h=(0,r.createReduxStore)("core/keyboard-shortcuts",{reducer:i,actions:o,selectors:n});(0,r.register)(h);const g=window.wp.element;function R(){const{getAllShortcutKeyCombinations:e}=(0,r.useSelect)(h);return function(t,o){return e(t).some((({modifier:e,character:t})=>s.isKeyboardEvent[e](o,t)))}}const C=new Set,v=e=>{for(const t of C)t(e)},E=(0,g.createContext)({add:e=>{0===C.size&&document.addEventListener("keydown",v),C.add(e)},delete:e=>{C.delete(e),0===C.size&&document.removeEventListener("keydown",v)}});function T(e,t,{isDisabled:o=!1}={}){const n=(0,g.useContext)(E),r=R(),i=(0,g.useRef)();(0,g.useEffect)((()=>{i.current=t}),[t]),(0,g.useEffect)((()=>{if(!o)return n.add(t),()=>{n.delete(t)};function t(t){r(e,t)&&i.current(t)}}),[e,o,n])}const k=window.ReactJSXRuntime,{Provider:O}=E;function K(e){const[t]=(0,g.useState)((()=>new Set));return(0,k.jsx)(O,{value:t,children:(0,k.jsx)("div",{...e,onKeyDown:function(o){e.onKeyDown&&e.onKeyDown(o);for(const e of t)e(o)}})})}(window.wp=window.wp||{}).keyboardShortcuts=t})();
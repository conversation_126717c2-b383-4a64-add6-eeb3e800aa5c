@echo off
echo 🚀 ChatGABI Advanced Web Scraping System - Database Startup
echo ============================================================
echo.

echo 🔧 Starting MySQL Database Server...
echo.

REM Try to start MySQL service
echo Attempting to start MySQL service...
net start mysql 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL service started successfully
    goto :check_xampp
) else (
    echo ⚠️ MySQL service not found or already running
)

:check_xampp
REM Check if XAMPP is installed
if exist "C:\xampp\mysql\bin\mysqld.exe" (
    echo 📁 XAMPP installation detected
    echo.
    echo To start MySQL in XAMPP:
    echo 1. Open XAMPP Control Panel
    echo 2. Click "Start" next to MySQL
    echo 3. Wait for status to show "Running"
    echo.
    echo Opening XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe" 2>nul
    if %errorlevel% == 0 (
        echo ✅ XAMPP Control Panel opened
    ) else (
        echo ⚠️ Could not open XAMPP Control Panel automatically
        echo Please open it manually from: C:\xampp\xampp-control.exe
    )
) else (
    echo ⚠️ XAMPP not found in default location
)

echo.
echo 📋 Manual Database Startup Options:
echo ===================================
echo.
echo For XAMPP:
echo • Open XAMPP Control Panel
echo • Click "Start" next to MySQL
echo.
echo For WAMP:
echo • Right-click WAMP icon in system tray
echo • Select "Start All Services"
echo.
echo For Command Line:
echo • Windows: net start mysql
echo • Or: sc start mysql
echo.
echo 🔍 Checking Database Connection...
echo.

REM Try to connect to database
php -r "
try {
    $pdo = new PDO('mysql:host=localhost;port=3306', 'root', '');
    echo '✅ Database connection successful' . PHP_EOL;
    echo '🎉 MySQL server is running and accessible' . PHP_EOL;
    echo PHP_EOL;
    echo '📋 Next Steps:' . PHP_EOL;
    echo '1. Run: php production-deployment.php' . PHP_EOL;
    echo '2. Configure OpenAI API key in WordPress admin' . PHP_EOL;
    echo '3. Access WordPress Admin → ChatGABI → Advanced Scraping' . PHP_EOL;
} catch (Exception $e) {
    echo '❌ Database connection failed: ' . $e->getMessage() . PHP_EOL;
    echo '⚠️ Please start MySQL server manually' . PHP_EOL;
}
" 2>nul

echo.
echo 🚀 Once database is running, execute:
echo php production-deployment.php
echo.
echo Press any key to continue...
pause >nul

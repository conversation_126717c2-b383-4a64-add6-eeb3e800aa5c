<?php
/**
 * REST API Endpoints for ChatGABI
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load enhanced input validator
require_once get_template_directory() . '/inc/enhanced-input-validator.php';

/**
 * Register REST API routes
 */
function chatgabi_register_rest_routes() {
    // Add debug logging
    error_log('ChatGABI: Registering REST API routes');
    register_rest_route('chatgabi/v1', '/credits', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_credits',
        'permission_callback' => 'chatgabi_check_user_permission',
    ));

    register_rest_route('chatgabi/v1', '/use-credit', array(
        'methods' => 'POST',
        'callback' => 'chatgabi_use_credit',
        'permission_callback' => 'chatgabi_check_user_permission',
        'args' => array(
            'tokens_used' => array(
                'required' => true,
                'type' => 'integer',
                'minimum' => 1,
            ),
            'model' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array('gpt-3.5-turbo', 'gpt-4-turbo'),
            ),
        ),
    ));

    register_rest_route('bcai/v1', '/add-credits', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_add_credits',
        'permission_callback' => 'businesscraft_ai_check_admin_permission',
        'args' => array(
            'user_id' => array(
                'required' => true,
                'type' => 'integer',
            ),
            'credits' => array(
                'required' => true,
                'type' => 'integer',
                'minimum' => 1,
            ),
            'transaction_id' => array(
                'required' => false,
                'type' => 'string',
            ),
        ),
    ));

    register_rest_route('bcai/v1', '/chat', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_process_chat',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
        'args' => array(
            'message' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'businesscraft_ai_sanitize_ai_input',
                'validate_callback' => 'businesscraft_ai_validate_chat_message',
            ),
            'language' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'en',
                'enum' => array('en', 'tw', 'sw', 'yo', 'zu'),
            ),
            'context' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'general',
            ),
        ),
    ));

    register_rest_route('bcai/v1', '/chat-history', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_get_chat_history',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
        'args' => array(
            'limit' => array(
                'required' => false,
                'type' => 'integer',
                'default' => 10,
                'minimum' => 1,
                'maximum' => 50,
            ),
        ),
    ));

    register_rest_route('bcai/v1', '/initiate-payment', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_initiate_payment',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
        'args' => array(
            'package' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array('starter', 'growth', 'business'),
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    register_rest_route('bcai/v1', '/paystack-webhook', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_paystack_webhook',
        'permission_callback' => '__return_true', // Webhook doesn't need user auth
    ));
    error_log('BusinessCraft AI: Paystack webhook route registered');

    register_rest_route('bcai/v1', '/pricing', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_get_localized_pricing',
        'permission_callback' => '__return_true', // Public endpoint
    ));

    register_rest_route('bcai/v1', '/user-location', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_get_user_location_info',
        'permission_callback' => '__return_true', // Public endpoint
    ));

    // Add a simple test endpoint
    register_rest_route('bcai/v1', '/test', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_test_endpoint',
        'permission_callback' => '__return_true',
    ));

    // Prompt Templates endpoints
    register_rest_route('bcai/v1', '/save-prompt-template', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_save_prompt_template',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
        'args' => array(
            'name' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'prompt' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ),
            'category' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'general',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    register_rest_route('bcai/v1', '/user-templates', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_get_user_templates',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
    ));

    register_rest_route('bcai/v1', '/delete-template/(?P<id>\d+)', array(
        'methods' => 'DELETE',
        'callback' => 'businesscraft_ai_delete_template',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
    ));

    // User Preferences endpoints
    register_rest_route('bcai/v1', '/user-preferences', array(
        'methods' => array('GET', 'POST'),
        'callback' => 'businesscraft_ai_handle_user_preferences',
        'permission_callback' => 'businesscraft_ai_check_user_permission',
        'args' => array(
            'language' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'industry' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'show_chat_history' => array(
                'required' => false,
                'type' => 'boolean',
            ),
        ),
    ));

    // Enhanced Template Management Endpoints for ChatGABI Templates Interface
    register_rest_route('chatgabi/v1', '/templates', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_templates',
        'permission_callback' => '__return_true', // Allow public access to templates
        'args' => array(
            'category' => array(
                'required' => false,
                'type' => 'string',
            ),
            'language' => array(
                'required' => false,
                'type' => 'string',
            ),
            'search' => array(
                'required' => false,
                'type' => 'string',
            ),
            'user_only' => array(
                'required' => false,
                'type' => 'boolean',
                'default' => false,
            ),
        ),
    ));

    register_rest_route('chatgabi/v1', '/templates/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_template',
        'permission_callback' => '__return_true', // Allow public access to individual templates
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    register_rest_route('chatgabi/v1', '/templates', array(
        'methods' => 'POST',
        'callback' => 'chatgabi_create_template',
        'permission_callback' => 'chatgabi_check_user_permission',
        'args' => array(
            'title' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'description' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ),
            'prompt_text' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ),
            'category_id' => array(
                'required' => true,
                'type' => 'integer',
            ),
            'language_code' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'en',
            ),
            'tags' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'sector' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    register_rest_route('chatgabi/v1', '/templates/(?P<id>\d+)', array(
        'methods' => 'DELETE',
        'callback' => 'chatgabi_rest_delete_template',
        'permission_callback' => 'chatgabi_check_template_permission',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    // AI-powered template enhancement endpoint
    register_rest_route('chatgabi/v1', '/templates/enhance', array(
        'methods' => 'POST',
        'callback' => 'chatgabi_enhance_template',
        'permission_callback' => 'chatgabi_check_user_permission',
        'args' => array(
            'template_content' => array(
                'required' => true,
                'type' => 'string',
            ),
            'user_context' => array(
                'required' => false,
                'type' => 'object',
            ),
            'enhancement_options' => array(
                'required' => false,
                'type' => 'object',
            ),
        ),
    ));

    // AI-powered template suggestions endpoint
    register_rest_route('chatgabi/v1', '/templates/suggestions', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_template_suggestions',
        'permission_callback' => 'chatgabi_check_user_permission',
    ));

    // Template categories endpoint
    register_rest_route('chatgabi/v1', '/template-categories', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_get_template_categories_api',
        'permission_callback' => '__return_true',
    ));
}
add_action('rest_api_init', 'chatgabi_register_rest_routes');

/**
 * Check user permission
 */
function businesscraft_ai_check_user_permission() {
    return is_user_logged_in();
}

/**
 * Check user permission for ChatGABI endpoints
 */
function chatgabi_check_user_permission() {
    return is_user_logged_in();
}

/**
 * Check template permission (user can only access their own templates)
 */
function chatgabi_check_template_permission($request) {
    if (!is_user_logged_in()) {
        return false;
    }

    $template_id = $request->get_param('id');
    if (!$template_id) {
        return false;
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT user_id FROM $table_name WHERE id = %d",
        $template_id
    ));

    if (!$template) {
        return false;
    }

    // Allow access if user owns the template or is admin
    return ($template->user_id == get_current_user_id()) || current_user_can('manage_options');
}

/**
 * Check admin permission
 */
function businesscraft_ai_check_admin_permission() {
    return current_user_can('manage_options');
}

/**
 * Get user credits
 */
function businesscraft_ai_get_credits($request) {
    $user_id = get_current_user_id();
    $credits = get_user_meta($user_id, 'businesscraft_credits', true);
    $credits = $credits ? intval($credits) : 0;

    return rest_ensure_response(array(
        'credits' => $credits,
        'user_id' => $user_id,
    ));
}

/**
 * Use credits
 */
function businesscraft_ai_use_credit($request) {
    $user_id = get_current_user_id();
    $tokens_used = $request->get_param('tokens_used');
    $model = $request->get_param('model');

    // Calculate credits to deduct based on model and tokens
    $credits_to_deduct = businesscraft_ai_calculate_credits($tokens_used, $model);

    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
    $current_credits = $current_credits ? intval($current_credits) : 0;

    if ($current_credits < $credits_to_deduct) {
        return new WP_Error(
            'insufficient_credits',
            __('Insufficient credits', 'businesscraft-ai'),
            array('status' => 402)
        );
    }

    $new_credits = $current_credits - $credits_to_deduct;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);

    // Log the transaction
    businesscraft_ai_log_credit_usage($user_id, $credits_to_deduct, $tokens_used, $model);

    return rest_ensure_response(array(
        'success' => true,
        'credits_used' => $credits_to_deduct,
        'remaining_credits' => $new_credits,
    ));
}

/**
 * Add credits to user
 */
function businesscraft_ai_add_credits($request) {
    $user_id = $request->get_param('user_id');
    $credits_to_add = $request->get_param('credits');
    $transaction_id = $request->get_param('transaction_id');

    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
    $current_credits = $current_credits ? intval($current_credits) : 0;

    $new_credits = $current_credits + $credits_to_add;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);

    // Log the transaction
    businesscraft_ai_log_credit_purchase($user_id, $credits_to_add, $transaction_id);

    return rest_ensure_response(array(
        'success' => true,
        'credits_added' => $credits_to_add,
        'total_credits' => $new_credits,
    ));
}

/**
 * Process chat message
 */
function businesscraft_ai_process_chat($request) {
    $user_id = get_current_user_id();
    $message = $request->get_param('message');
    $language = $request->get_param('language');
    $context = $request->get_param('context');

    // Check if user has sufficient credits
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
    $current_credits = $current_credits ? intval($current_credits) : 0;

    if ($current_credits < 1) {
        return new WP_Error(
            'insufficient_credits',
            __('Insufficient credits. Please purchase more credits to continue.', 'businesscraft-ai'),
            array('status' => 402)
        );
    }

    // Check for cached response
    $cache_key = 'bcai_chat_' . md5($message . $language . $context);
    $cached_response = get_transient($cache_key);

    if ($cached_response) {
        return rest_ensure_response($cached_response);
    }

    try {
        // Check user credits before processing
        $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
        $estimated_cost = max(1, ceil(strlen($message) / 4000)); // Rough estimation

        if ($current_credits < $estimated_cost) {
            return new WP_Error(
                'insufficient_credits',
                sprintf(__('Insufficient credits. You have %d credits but need approximately %d for this request.', 'chatgabi'), $current_credits, $estimated_cost),
                array('status' => 402, 'remaining_credits' => $current_credits)
            );
        }

        // Process with OpenAI
        $ai_response = businesscraft_ai_process_openai_request($message, $language, $context, $user_id);

        if (is_wp_error($ai_response)) {
            // Return specific error messages based on error code
            $error_code = $ai_response->get_error_code();
            $error_message = $ai_response->get_error_message();

            switch ($error_code) {
                case 'openai_rate_limit':
                    return new WP_Error('rate_limit_exceeded', __('Rate limit exceeded. Please wait a moment before trying again.', 'chatgabi'), array('status' => 429));
                case 'openai_invalid_request':
                    return new WP_Error('invalid_request', __('Invalid request. Please check your message and try again.', 'chatgabi'), array('status' => 400));
                case 'openai_server_error':
                    return new WP_Error('server_error', __('AI service temporarily unavailable. Please try again in a few minutes.', 'chatgabi'), array('status' => 503));
                default:
                    return new WP_Error('ai_error', $error_message ?: __('AI processing failed. Please try again.', 'chatgabi'), array('status' => 500));
            }
        }

        // Cache the response for 5 minutes
        set_transient($cache_key, $ai_response, 5 * MINUTE_IN_SECONDS);

        // Log the chat
        businesscraft_ai_log_chat($user_id, $message, $ai_response['response'], $language, $ai_response['tokens_used']);

        return rest_ensure_response($ai_response);

    } catch (Exception $e) {
        return new WP_Error(
            'processing_error',
            __('An error occurred while processing your request.', 'businesscraft-ai'),
            array('status' => 500)
        );
    }
}

/**
 * Get chat history
 */
function businesscraft_ai_get_chat_history($request) {
    $user_id = get_current_user_id();
    $limit = $request->get_param('limit');

    $chat_history = businesscraft_ai_get_user_chat_history($user_id, $limit);

    return rest_ensure_response(array(
        'history' => $chat_history,
        'count' => count($chat_history),
    ));
}

/**
 * Calculate credits based on tokens and model
 */
function businesscraft_ai_calculate_credits($tokens, $model) {
    // Base calculation: 1 credit per 1000 tokens for gpt-3.5-turbo
    // 2 credits per 1000 tokens for gpt-4-turbo
    $base_rate = ($model === 'gpt-4-turbo') ? 2 : 1;
    $credits = ceil(($tokens / 1000) * $base_rate);

    // Minimum 1 credit per request
    return max(1, $credits);
}

/**
 * Initiate payment via REST API
 */
function businesscraft_ai_rest_initiate_payment($request) {
    // Add error logging for debugging
    error_log('BusinessCraft AI: REST Payment initiation started');

    try {
        $user_id = get_current_user_id();
        $package = $request->get_param('package');

        error_log('BusinessCraft AI: User ID: ' . $user_id . ', Package: ' . $package);

        // Check if user is logged in
        if (!$user_id) {
            error_log('BusinessCraft AI: User not logged in');
            return new WP_Error(
                'user_not_logged_in',
                __('Please log in to purchase credits', 'businesscraft-ai'),
                array('status' => 401)
            );
        }

        // Validate package
        $valid_packages = array('starter', 'growth', 'business');
        if (!in_array($package, $valid_packages)) {
            error_log('BusinessCraft AI: Invalid package selected: ' . $package);
            return new WP_Error(
                'invalid_package',
                __('Invalid package selected', 'businesscraft-ai'),
                array('status' => 400)
            );
        }

        $user = get_userdata($user_id);
        if (!$user) {
            error_log('BusinessCraft AI: User not found: ' . $user_id);
            return new WP_Error(
                'user_not_found',
                __('User not found', 'businesscraft-ai'),
                array('status' => 404)
            );
        }

        // Check if required functions exist
        if (!function_exists('businesscraft_ai_initiate_paystack_payment')) {
            error_log('BusinessCraft AI: Function businesscraft_ai_initiate_paystack_payment not found');
            return new WP_Error(
                'function_missing',
                __('Payment function not available', 'businesscraft-ai'),
                array('status' => 500)
            );
        }

        // Check if Paystack is configured
        $secret_key = defined('BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY') ?
                     BUSINESSCRAFT_AI_PAYSTACK_SECRET_KEY :
                     get_option('businesscraft_ai_paystack_secret_key');

        if (empty($secret_key)) {
            error_log('BusinessCraft AI: Paystack secret key not configured');
            return new WP_Error(
                'paystack_not_configured',
                __('Payment system not configured', 'businesscraft-ai'),
                array('status' => 500)
            );
        }

        error_log('BusinessCraft AI: Calling Paystack payment function');
        $payment_data = businesscraft_ai_initiate_paystack_payment(
            $user->user_email,
            $package,
            $user_id
        );

        error_log('BusinessCraft AI: Payment data received: ' . json_encode($payment_data));

        // Check if payment initiation was successful
        if (is_array($payment_data) && isset($payment_data['status'])) {
            if ($payment_data['status'] === 'success') {
                return rest_ensure_response($payment_data);
            } else {
                return new WP_Error(
                    'payment_failed',
                    $payment_data['message'] ?? __('Payment initiation failed', 'businesscraft-ai'),
                    array('status' => 400)
                );
            }
        }

        return rest_ensure_response($payment_data);

    } catch (Exception $e) {
        error_log('BusinessCraft AI: Payment error: ' . $e->getMessage());
        error_log('BusinessCraft AI: Payment error trace: ' . $e->getTraceAsString());
        return new WP_Error(
            'payment_error',
            sprintf(__('Failed to initiate payment: %s', 'businesscraft-ai'), $e->getMessage()),
            array('status' => 500)
        );
    } catch (Error $e) {
        error_log('BusinessCraft AI: PHP Error: ' . $e->getMessage());
        error_log('BusinessCraft AI: PHP Error trace: ' . $e->getTraceAsString());
        return new WP_Error(
            'php_error',
            __('A system error occurred', 'businesscraft-ai'),
            array('status' => 500)
        );
    }
}

/**
 * Paystack webhook handler
 */
function businesscraft_ai_paystack_webhook($request) {
    error_log('BusinessCraft AI: Webhook handler called');
    $body = $request->get_body();
    $signature = $request->get_header('x-paystack-signature');

    // Verify webhook signature (skip for testing if filter is set)
    $skip_verification = apply_filters('businesscraft_ai_skip_webhook_signature_verification', false);
    if (!$skip_verification && !businesscraft_ai_verify_paystack_signature($body, $signature)) {
        return new WP_Error(
            'invalid_signature',
            __('Invalid webhook signature', 'businesscraft-ai'),
            array('status' => 401)
        );
    }

    $event = json_decode($body, true);

    if ($event['event'] === 'charge.success') {
        $transaction = $event['data'];
        $metadata = $transaction['metadata'];

        if (isset($metadata['user_id']) && isset($metadata['package'])) {
            $user_id = intval($metadata['user_id']);
            $package = $metadata['package'];

            $packages = array(
                'starter' => 500,
                'growth' => 1500,
                'business' => 3000,
            );

            if (isset($packages[$package])) {
                $credits_to_add = $packages[$package];

                // Add credits to user
                $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
                $current_credits = $current_credits ? intval($current_credits) : 0;
                $new_credits = $current_credits + $credits_to_add;

                update_user_meta($user_id, 'businesscraft_credits', $new_credits);

                // Log the transaction
                businesscraft_ai_log_credit_purchase($user_id, $credits_to_add, $transaction['reference']);

                // Grant Ultra tier access for Growth and Business packages
                if (in_array($package, array('growth', 'business'))) {
                    update_user_meta($user_id, 'businesscraft_ai_tier', 'ultra');
                }
            }
        }
    }

    return rest_ensure_response(array('status' => 'success'));
}

/**
 * Get localized pricing for REST API
 */
function businesscraft_ai_get_localized_pricing($request) {
    try {
        error_log('BusinessCraft AI: Getting localized pricing via REST API');

        // Add fallback for when functions aren't available
        if (!function_exists('businesscraft_ai_get_localized_package_pricing')) {
            error_log('BusinessCraft AI: Localized pricing function not found, using fallback');
            // Return default USD pricing
            return rest_ensure_response(array(
                'status' => 'success',
                'data' => array(
                    'packages' => array(
                        'starter' => array('credits' => 500, 'price_local' => 5.00, 'currency' => 'USD', 'currency_symbol' => '$', 'formatted_price' => '$5.00', 'price_usd' => 5.00),
                        'growth' => array('credits' => 1500, 'price_local' => 15.00, 'currency' => 'USD', 'currency_symbol' => '$', 'formatted_price' => '$15.00', 'price_usd' => 15.00),
                        'business' => array('credits' => 3000, 'price_local' => 30.00, 'currency' => 'USD', 'currency_symbol' => '$', 'formatted_price' => '$30.00', 'price_usd' => 30.00),
                    ),
                    'currency' => array('currency' => 'USD', 'symbol' => '$', 'name' => 'US Dollar'),
                    'country' => 'US'
                )
            ));
        }

        $localized_packages = businesscraft_ai_get_localized_package_pricing();
        $user_currency = businesscraft_ai_get_user_currency();

        error_log('BusinessCraft AI: Localized pricing retrieved successfully');
        return rest_ensure_response(array(
            'status' => 'success',
            'data' => array(
                'packages' => $localized_packages,
                'currency' => $user_currency,
                'country' => businesscraft_ai_get_user_country()
            )
        ));
    } catch (Exception $e) {
        error_log('BusinessCraft AI: Pricing error: ' . $e->getMessage());
        return new WP_Error(
            'pricing_error',
            sprintf(__('Failed to get pricing: %s', 'businesscraft-ai'), $e->getMessage()),
            array('status' => 500)
        );
    }
}

/**
 * Get user location information
 */
function businesscraft_ai_get_user_location_info($request) {
    try {
        $country = businesscraft_ai_get_user_country();
        $currency = businesscraft_ai_get_user_currency();
        $ip_address = businesscraft_ai_get_user_ip();

        return rest_ensure_response(array(
            'status' => 'success',
            'data' => array(
                'country' => $country,
                'currency' => $currency,
                'ip_address' => $ip_address, // For debugging only
                'detected_from_ip' => !isset($_SESSION['businesscraft_ai_country'])
            )
        ));
    } catch (Exception $e) {
        return new WP_Error(
            'location_error',
            sprintf(__('Failed to get location: %s', 'businesscraft-ai'), $e->getMessage()),
            array('status' => 500)
        );
    }
}

/**
 * Simple test endpoint
 */
function businesscraft_ai_test_endpoint($request) {
    return rest_ensure_response(array(
        'status' => 'success',
        'message' => 'BusinessCraft AI REST API is working',
        'timestamp' => current_time('mysql'),
        'routes_registered' => true
    ));
}

/**
 * Save prompt template
 */
function businesscraft_ai_save_prompt_template($request) {
    $user_id = get_current_user_id();
    $name = $request->get_param('name');
    $prompt = $request->get_param('prompt');
    $category = $request->get_param('category');

    // Get existing templates
    $templates = get_user_meta($user_id, 'businesscraft_ai_prompt_templates', true);
    if (!is_array($templates)) {
        $templates = array();
    }

    // Create new template
    $template_id = uniqid();
    $new_template = array(
        'id' => $template_id,
        'name' => $name,
        'prompt' => $prompt,
        'category' => $category,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    );

    // Add to templates array
    $templates[$template_id] = $new_template;

    // Save to user meta
    update_user_meta($user_id, 'businesscraft_ai_prompt_templates', $templates);

    // Log analytics
    businesscraft_ai_log_analytics($user_id, 'template_saved', array(
        'template_name' => $name,
        'category' => $category
    ));

    return rest_ensure_response(array(
        'success' => true,
        'template' => $new_template,
        'message' => __('Template saved successfully', 'businesscraft-ai')
    ));
}

/**
 * Get user templates
 */
function businesscraft_ai_get_user_templates($request) {
    $user_id = get_current_user_id();

    // Get templates from user meta
    $templates = get_user_meta($user_id, 'businesscraft_ai_prompt_templates', true);
    if (!is_array($templates)) {
        $templates = array();
    }

    // Convert to indexed array and sort by created_at
    $templates_array = array_values($templates);
    usort($templates_array, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    return rest_ensure_response(array(
        'success' => true,
        'templates' => $templates_array,
        'count' => count($templates_array)
    ));
}

/**
 * Delete template
 */
function businesscraft_ai_delete_template($request) {
    $user_id = get_current_user_id();
    $template_id = $request->get_param('id');

    // Get existing templates
    $templates = get_user_meta($user_id, 'businesscraft_ai_prompt_templates', true);
    if (!is_array($templates)) {
        $templates = array();
    }

    // Check if template exists
    if (!isset($templates[$template_id])) {
        return new WP_Error(
            'template_not_found',
            __('Template not found', 'businesscraft-ai'),
            array('status' => 404)
        );
    }

    $template_name = $templates[$template_id]['name'];

    // Remove template
    unset($templates[$template_id]);

    // Update user meta
    update_user_meta($user_id, 'businesscraft_ai_prompt_templates', $templates);

    // Log analytics
    businesscraft_ai_log_analytics($user_id, 'template_deleted', array(
        'template_name' => $template_name,
        'template_id' => $template_id
    ));

    return rest_ensure_response(array(
        'success' => true,
        'message' => __('Template deleted successfully', 'businesscraft-ai')
    ));
}

/**
 * Handle user preferences (GET/POST)
 */
function businesscraft_ai_handle_user_preferences($request) {
    $user_id = get_current_user_id();
    $method = $request->get_method();

    if ($method === 'GET') {
        // Get current preferences
        $preferences = array(
            'language' => get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en',
            'industry' => get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '',
            'show_chat_history' => get_user_meta($user_id, 'bcai_show_chat_history', true) !== '0',
            'profile_type' => get_user_meta($user_id, 'bcai_profile_type', true) ?: '',
            'business_stage' => get_user_meta($user_id, 'bcai_business_stage', true) ?: '',
            'goals' => get_user_meta($user_id, 'bcai_goals', true) ?: array(),
        );

        return rest_ensure_response(array(
            'success' => true,
            'preferences' => $preferences
        ));

    } elseif ($method === 'POST') {
        // Update preferences
        $language = $request->get_param('language');
        $industry = $request->get_param('industry');
        $show_chat_history = $request->get_param('show_chat_history');

        if ($language) {
            update_user_meta($user_id, 'bcai_preferred_language', $language);
        }

        if ($industry !== null) {
            update_user_meta($user_id, 'businesscraft_ai_industry', $industry);
        }

        if ($show_chat_history !== null) {
            update_user_meta($user_id, 'bcai_show_chat_history', $show_chat_history ? '1' : '0');
        }

        // Log analytics
        businesscraft_ai_log_analytics($user_id, 'preferences_updated_api', array(
            'language' => $language,
            'industry' => $industry,
            'show_chat_history' => $show_chat_history
        ));

        return rest_ensure_response(array(
            'success' => true,
            'message' => __('Preferences updated successfully', 'businesscraft-ai')
        ));
    }

    return new WP_Error(
        'invalid_method',
        __('Invalid request method', 'businesscraft-ai'),
        array('status' => 405)
    );
}

/**
 * Get templates for ChatGABI Templates Interface
 */
function chatgabi_get_templates($request) {
    global $wpdb;

    // Set execution time limit to prevent timeouts
    set_time_limit(30);

    $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';

    // Get parameters
    $category = $request->get_param('category');
    $language = $request->get_param('language');
    $search = $request->get_param('search');
    $user_only = $request->get_param('user_only');
    $user_id = get_current_user_id();

    // Build optimized query with explicit conditions - check if status column exists
    $columns = $wpdb->get_col("DESCRIBE {$table_name}");
    $has_status = in_array('status', $columns);

    $where_conditions = array();
    if ($has_status) {
        $where_conditions[] = "t.status = 'active'";
    }
    $where_values = array();

    // User filter - simplified for public access
    if ($user_only && $user_id > 0) {
        $where_conditions[] = "t.user_id = %d";
        $where_values[] = $user_id;
    } else {
        // Only show public templates for non-authenticated users
        if ($user_id > 0) {
            $where_conditions[] = "(t.is_public = 1 OR t.user_id = %d)";
            $where_values[] = $user_id;
        } else {
            $where_conditions[] = "t.is_public = 1";
        }
    }

    // Category filter - optimized with direct join
    if ($category && $category !== 'all') {
        $where_conditions[] = "c.slug = %s";
        $where_values[] = $category;
    }

    // Language filter
    if ($language) {
        $where_conditions[] = "t.language_code = %s";
        $where_values[] = $language;
    }

    // Search filter - simplified
    if ($search) {
        $where_conditions[] = "(t.title LIKE %s OR t.description LIKE %s)";
        $search_term = '%' . $wpdb->esc_like($search) . '%';
        $where_values[] = $search_term;
        $where_values[] = $search_term;
    }

    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }

    // Optimized query with specific fields and LIMIT - using correct column name
    $query = "SELECT t.id, t.title, t.description, t.prompt_text, t.language_code,
                     t.tags, t.sector, t.usage_count, t.rating_average, t.rating_count,
                     t.is_public, t.user_id, t.created_at, t.updated_at,
                     c.name as category_name, c.icon as category_icon, c.color as category_color
              FROM $table_name t
              LEFT JOIN $categories_table c ON t.category_id = c.id
              $where_clause
              ORDER BY t.is_featured DESC, t.usage_count DESC, t.created_at DESC
              LIMIT 50";

    if (!empty($where_values)) {
        $query = $wpdb->prepare($query, $where_values);
    }

    $templates = $wpdb->get_results($query);

    // Process templates for frontend - optimized
    $processed_templates = array();
    if ($templates) {
        foreach ($templates as $template) {
            $processed_templates[] = array(
                'id' => (int) $template->id,
                'title' => $template->title,
                'description' => $template->description,
                'prompt_content' => $template->prompt_text, // Map prompt_text to prompt_content for API consistency
                'category' => array(
                    'name' => $template->category_name ?: 'General',
                    'icon' => $template->category_icon ?: '📋',
                    'color' => $template->category_color ?: '#667eea'
                ),
                'language_code' => $template->language_code ?: 'en',
                'tags' => $template->tags ? explode(',', $template->tags) : array(),
                'sector' => $template->sector ?: 'General',
                'usage_count' => (int) $template->usage_count,
                'rating_average' => (float) $template->rating_average,
                'rating_count' => (int) $template->rating_count,
                'is_public' => (bool) $template->is_public,
                'is_owner' => $user_id > 0 && $template->user_id == $user_id,
                'created_at' => $template->created_at
            );
        }
    }

    return rest_ensure_response(array(
        'success' => true,
        'templates' => $processed_templates,
        'count' => count($processed_templates),
        'total_available' => count($processed_templates),
        'filters_applied' => array(
            'category' => $category ?: 'all',
            'language' => $language ?: 'all',
            'search' => $search ?: '',
            'user_only' => (bool) $user_only
        )
    ));
}

/**
 * Get single template
 */
function chatgabi_get_template($request) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';
    $template_id = $request->get_param('id');
    $user_id = get_current_user_id();

    // Check if status column exists in templates table
    $columns = $wpdb->get_col("DESCRIBE {$table_name}");
    $has_status = in_array('status', $columns);

    $status_condition = $has_status ? " AND t.status = 'active'" : "";

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
         FROM $table_name t
         LEFT JOIN {$wpdb->prefix}chatgabi_template_categories c ON t.category_id = c.id
         WHERE t.id = %d AND (t.is_public = 1 OR t.user_id = %d){$status_condition}",
        $template_id,
        $user_id
    ));

    if (!$template) {
        return new WP_Error(
            'template_not_found',
            __('Template not found or access denied', 'chatgabi'),
            array('status' => 404)
        );
    }

    // Increment usage count
    $wpdb->query($wpdb->prepare(
        "UPDATE $table_name SET usage_count = usage_count + 1 WHERE id = %d",
        $template_id
    ));

    $processed_template = array(
        'id' => $template->id,
        'title' => $template->title,
        'description' => $template->description,
        'prompt_content' => $template->prompt_text, // Map prompt_text to prompt_content for API consistency
        'category' => array(
            'id' => $template->category_id,
            'name' => $template->category_name,
            'icon' => $template->category_icon,
            'color' => $template->category_color
        ),
        'language_code' => $template->language_code,
        'tags' => $template->tags ? explode(',', $template->tags) : array(),
        'sector' => $template->sector,
        'usage_count' => $template->usage_count + 1,
        'rating_average' => $template->rating_average,
        'rating_count' => $template->rating_count,
        'is_public' => $template->is_public,
        'user_id' => $template->user_id,
        'is_owner' => $template->user_id == $user_id,
        'created_at' => $template->created_at,
        'updated_at' => $template->updated_at
    );

    return rest_ensure_response(array(
        'success' => true,
        'template' => $processed_template
    ));
}

/**
 * Create new template
 */
function chatgabi_create_template($request) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_prompt_templates';
    $user_id = get_current_user_id();

    // Get parameters
    $title = $request->get_param('title');
    $description = $request->get_param('description');
    $prompt_content = $request->get_param('prompt_text');
    $category_id = $request->get_param('category_id');
    $language_code = $request->get_param('language_code');
    $tags = $request->get_param('tags');
    $sector = $request->get_param('sector');

    // Insert template
    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'title' => $title,
            'description' => $description,
            'prompt_text' => $prompt_content, // Use correct database column name
            'category_id' => $category_id,
            'language_code' => $language_code,
            'tags' => $tags,
            'sector' => $sector,
            'is_public' => 0, // User templates are private by default
            'status' => 'active',
            'usage_count' => 0,
            'rating_average' => 0,
            'rating_count' => 0,
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%d', '%s', '%d', '%f', '%d', '%s', '%s')
    );

    if ($result === false) {
        return new WP_Error(
            'template_creation_failed',
            __('Failed to create template', 'chatgabi'),
            array('status' => 500)
        );
    }

    $template_id = $wpdb->insert_id;

    // Log analytics
    if (function_exists('businesscraft_ai_log_analytics')) {
        businesscraft_ai_log_analytics($user_id, 'template_created', array(
            'template_id' => $template_id,
            'template_title' => $title,
            'category_id' => $category_id,
            'language_code' => $language_code
        ));
    }

    return rest_ensure_response(array(
        'success' => true,
        'template_id' => $template_id,
        'message' => __('Template created successfully', 'chatgabi')
    ));
}



/**
 * AI-powered template enhancement
 */
function chatgabi_enhance_template($request) {
    $user_id = get_current_user_id();
    $template_content = $request->get_param('template_content');
    $user_context = $request->get_param('user_context') ?: array();
    $enhancement_options = $request->get_param('enhancement_options') ?: array();

    // Check user credits
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    $required_credits = 2; // Enhancement requires 2 credits

    if ($current_credits < $required_credits) {
        return new WP_Error(
            'insufficient_credits',
            sprintf(__('Insufficient credits. Required: %d, Available: %d', 'chatgabi'), $required_credits, $current_credits),
            array('status' => 402)
        );
    }

    // Get user profile data for context
    $user_profile = get_user_meta($user_id, 'bcai_profile_type', true) ?: 'sme';
    $user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
    $user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
    $user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';

    // Build enhancement prompt
    $enhancement_prompt = chatgabi_build_enhancement_prompt(
        $template_content,
        $user_profile,
        $user_industry,
        $user_country,
        $user_language,
        $enhancement_options
    );

    // Process with OpenAI
    if (function_exists('businesscraft_ai_process_openai_request')) {
        $ai_response = businesscraft_ai_process_openai_request(
            $enhancement_prompt,
            $user_language,
            'template_enhancement',
            $user_id
        );

        if (is_wp_error($ai_response)) {
            return $ai_response;
        }

        // Deduct credits
        $new_credits = $current_credits - $required_credits;
        update_user_meta($user_id, 'businesscraft_credits', $new_credits);

        // Log analytics
        if (function_exists('businesscraft_ai_log_analytics')) {
            businesscraft_ai_log_analytics($user_id, 'template_enhanced', array(
                'credits_used' => $required_credits,
                'enhancement_options' => $enhancement_options
            ));
        }

        return rest_ensure_response(array(
            'success' => true,
            'enhanced_content' => $ai_response['response'],
            'credits_used' => $required_credits,
            'remaining_credits' => $new_credits,
            'tokens_used' => $ai_response['tokens_used'] ?? 0
        ));
    }

    return new WP_Error(
        'enhancement_unavailable',
        __('AI enhancement service is currently unavailable', 'chatgabi'),
        array('status' => 503)
    );
}

/**
 * Get AI-powered template suggestions
 */
function chatgabi_get_template_suggestions($request) {
    $user_id = get_current_user_id();

    // Check user credits
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    $required_credits = 1; // Suggestions require 1 credit

    if ($current_credits < $required_credits) {
        return new WP_Error(
            'insufficient_credits',
            sprintf(__('Insufficient credits. Required: %d, Available: %d', 'chatgabi'), $required_credits, $current_credits),
            array('status' => 402)
        );
    }

    // Get user profile data
    $user_profile = get_user_meta($user_id, 'bcai_profile_type', true) ?: 'sme';
    $user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
    $user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
    $user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
    $user_goals = get_user_meta($user_id, 'bcai_goals', true) ?: array();

    // Build suggestions prompt
    $suggestions_prompt = chatgabi_build_suggestions_prompt(
        $user_profile,
        $user_industry,
        $user_country,
        $user_language,
        $user_goals
    );

    // Process with OpenAI
    if (function_exists('businesscraft_ai_process_openai_request')) {
        $ai_response = businesscraft_ai_process_openai_request(
            $suggestions_prompt,
            $user_language,
            'template_suggestions',
            $user_id
        );

        if (is_wp_error($ai_response)) {
            return $ai_response;
        }

        // Deduct credits
        $new_credits = $current_credits - $required_credits;
        update_user_meta($user_id, 'businesscraft_credits', $new_credits);

        // Parse suggestions from AI response
        $suggestions = chatgabi_parse_ai_suggestions($ai_response['response']);

        // Log analytics
        if (function_exists('businesscraft_ai_log_analytics')) {
            businesscraft_ai_log_analytics($user_id, 'template_suggestions_generated', array(
                'credits_used' => $required_credits,
                'suggestions_count' => count($suggestions)
            ));
        }

        return rest_ensure_response(array(
            'success' => true,
            'suggestions' => $suggestions,
            'credits_used' => $required_credits,
            'remaining_credits' => $new_credits,
            'tokens_used' => $ai_response['tokens_used'] ?? 0
        ));
    }

    return new WP_Error(
        'suggestions_unavailable',
        __('AI suggestions service is currently unavailable', 'chatgabi'),
        array('status' => 503)
    );
}

/**
 * Get template categories for API
 */
function chatgabi_get_template_categories_api($request) {
    global $wpdb;

    // Set execution time limit
    set_time_limit(15);

    $table_name = $wpdb->prefix . 'chatgabi_template_categories';

    // Direct database query for better performance - check if status column exists
    $columns = $wpdb->get_col("DESCRIBE {$table_name}");
    $has_status = in_array('status', $columns);

    if ($has_status) {
        $categories = $wpdb->get_results("
            SELECT id, name, slug, description, icon, color, sort_order
            FROM $table_name
            WHERE status = 'active'
            ORDER BY sort_order ASC, name ASC
            LIMIT 20
        ");
    } else {
        $categories = $wpdb->get_results("
            SELECT id, name, slug, description, icon, color, sort_order
            FROM $table_name
            ORDER BY sort_order ASC, name ASC
            LIMIT 20
        ");
    }

    $processed_categories = array();
    if ($categories) {
        foreach ($categories as $category) {
            $processed_categories[] = array(
                'id' => (int) $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'icon' => $category->icon ?: '📋',
                'color' => $category->color ?: '#667eea'
            );
        }
    }

    return rest_ensure_response(array(
        'success' => true,
        'categories' => $processed_categories,
        'count' => count($processed_categories)
    ));
}

/**
 * Enhanced validation callback for chat messages
 */
function businesscraft_ai_validate_chat_message($value, $request, $param) {
    $validation_result = businesscraft_ai_validate_ai_input($value, 'chat');

    if (!$validation_result['is_valid']) {
        return new WP_Error(
            'invalid_input',
            'Invalid input: ' . implode(', ', $validation_result['errors']),
            array(
                'status' => 400,
                'security_flags' => $validation_result['security_flags'],
                'warnings' => $validation_result['warnings']
            )
        );
    }

    return true;
}

/**
 * Enhanced validation callback for template content
 */
function businesscraft_ai_validate_template_content($value, $request, $param) {
    $validation_result = businesscraft_ai_validate_ai_input($value, 'template');

    if (!$validation_result['is_valid']) {
        return new WP_Error(
            'invalid_template_content',
            'Invalid template content: ' . implode(', ', $validation_result['errors']),
            array(
                'status' => 400,
                'security_flags' => $validation_result['security_flags']
            )
        );
    }

    return true;
}

/**
 * Enhanced validation callback for general text input
 */
function businesscraft_ai_validate_text_input($value, $request, $param) {
    if (empty($value)) {
        return true; // Allow empty values for optional fields
    }

    $validation_result = businesscraft_ai_validate_ai_input($value, 'general');

    if (!$validation_result['is_valid']) {
        return new WP_Error(
            'invalid_text_input',
            'Invalid input: ' . implode(', ', $validation_result['errors']),
            array(
                'status' => 400,
                'security_flags' => $validation_result['security_flags']
            )
        );
    }

    return true;
}
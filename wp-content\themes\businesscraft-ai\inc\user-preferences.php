<?php
/**
 * ChatGABI User Preferences System
 * 
 * Handles user preference storage, retrieval, and management
 * for personalized ChatGABI experience.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize user preferences system
 */
function chatgabi_init_user_preferences() {
    // Create preferences table if needed
    chatgabi_create_user_preferences_table();
}

/**
 * Create user preferences table
 */
function chatgabi_create_user_preferences_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_user_preferences';

    // Use transient cache to avoid repeated table existence checks
    $cache_key = 'chatgabi_preferences_table_exists';
    $table_exists = get_transient($cache_key);

    if ($table_exists === false) {
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        set_transient($cache_key, $table_exists ? 'yes' : 'no', HOUR_IN_SECONDS);
    }

    if ($table_exists === 'yes') {
        return true;
    }

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        preference_key varchar(100) NOT NULL,
        preference_value longtext,
        preference_type varchar(50) NOT NULL DEFAULT 'string',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_preference (user_id, preference_key),
        KEY user_id (user_id),
        KEY preference_key (preference_key)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = dbDelta($sql);

    // Update cache
    set_transient($cache_key, 'yes', HOUR_IN_SECONDS);

    return !empty($result);
}

/**
 * Get user preference
 */
function chatgabi_get_user_preference($user_id, $preference_key, $default = null) {
    global $wpdb;

    if (!$user_id) {
        return $default;
    }

    // Try user meta first (for backward compatibility)
    $meta_value = get_user_meta($user_id, 'chatgabi_' . $preference_key, true);
    if (!empty($meta_value)) {
        return $meta_value;
    }

    // Check preferences table
    $table_name = $wpdb->prefix . 'chatgabi_user_preferences';
    
    $preference = $wpdb->get_row($wpdb->prepare(
        "SELECT preference_value, preference_type FROM {$table_name} WHERE user_id = %d AND preference_key = %s",
        $user_id,
        $preference_key
    ));

    if ($preference) {
        // Convert based on type
        switch ($preference->preference_type) {
            case 'json':
                return json_decode($preference->preference_value, true);
            case 'boolean':
                return (bool) $preference->preference_value;
            case 'integer':
                return (int) $preference->preference_value;
            case 'array':
                return maybe_unserialize($preference->preference_value);
            default:
                return $preference->preference_value;
        }
    }

    return $default;
}

/**
 * Set user preference
 */
function chatgabi_set_user_preference($user_id, $preference_key, $preference_value, $preference_type = 'string') {
    global $wpdb;

    if (!$user_id) {
        return false;
    }

    // Ensure table exists
    chatgabi_create_user_preferences_table();

    $table_name = $wpdb->prefix . 'chatgabi_user_preferences';

    // Convert value based on type
    switch ($preference_type) {
        case 'json':
            $stored_value = json_encode($preference_value);
            break;
        case 'boolean':
            $stored_value = $preference_value ? '1' : '0';
            break;
        case 'integer':
            $stored_value = (string) intval($preference_value);
            break;
        case 'array':
            $stored_value = maybe_serialize($preference_value);
            break;
        default:
            $stored_value = (string) $preference_value;
            break;
    }

    // Use INSERT ... ON DUPLICATE KEY UPDATE for MySQL compatibility
    $result = $wpdb->query($wpdb->prepare(
        "INSERT INTO {$table_name} (user_id, preference_key, preference_value, preference_type) 
         VALUES (%d, %s, %s, %s) 
         ON DUPLICATE KEY UPDATE 
         preference_value = VALUES(preference_value), 
         preference_type = VALUES(preference_type),
         updated_at = CURRENT_TIMESTAMP",
        $user_id,
        $preference_key,
        $stored_value,
        $preference_type
    ));

    return $result !== false;
}

/**
 * Get all user preferences
 */
function chatgabi_get_all_user_preferences($user_id) {
    global $wpdb;

    if (!$user_id) {
        return array();
    }

    $table_name = $wpdb->prefix . 'chatgabi_user_preferences';
    
    $preferences = $wpdb->get_results($wpdb->prepare(
        "SELECT preference_key, preference_value, preference_type FROM {$table_name} WHERE user_id = %d",
        $user_id
    ));

    $result = array();
    foreach ($preferences as $pref) {
        // Convert based on type
        switch ($pref->preference_type) {
            case 'json':
                $result[$pref->preference_key] = json_decode($pref->preference_value, true);
                break;
            case 'boolean':
                $result[$pref->preference_key] = (bool) $pref->preference_value;
                break;
            case 'integer':
                $result[$pref->preference_key] = (int) $pref->preference_value;
                break;
            case 'array':
                $result[$pref->preference_key] = maybe_unserialize($pref->preference_value);
                break;
            default:
                $result[$pref->preference_key] = $pref->preference_value;
                break;
        }
    }

    return $result;
}

/**
 * Get default user preferences
 */
function chatgabi_get_default_preferences() {
    return array(
        // Basic Preferences
        'language' => 'en',
        'preferred_language' => 'en',
        'country' => '',
        'preferred_country' => '',
        'industry_sector' => '',
        'business_stage' => 'idea',
        'preferred_sector' => '',
        'show_chat_history' => true,
        'chat_history_limit' => 10,
        'show_example_prompts' => true,
        'auto_save_templates' => true,
        'email_notifications' => true,
        'opportunity_notifications' => true,
        'template_generation_notifications' => true,
        'dashboard_layout' => 'default',
        'theme_preference' => 'light',
        'currency_display' => 'auto',
        'timezone' => 'auto',

        // AI Behavior & Response Preferences
        'ai_response_style' => 'professional',
        'response_length' => 'medium',
        'include_examples' => true,
        'include_action_items' => true,
        'cultural_context_level' => 'moderate',
        'auto_include_opportunities' => true,

        // Analytics Preferences
        'track_usage_analytics' => true,
        'weekly_analytics_email' => false,

        // Account Management & Security
        'data_retention_period' => 365,
        'two_factor_auth' => false,
        'login_notifications' => true,
        'data_sharing_consent' => false,

        // Advanced Preferences
        'api_rate_limit' => 'balanced',
        'debug_mode' => false,
        'beta_features' => false,
        'cache_duration' => 900
    );
}

/**
 * Get user preferences with defaults
 */
function chatgabi_get_user_preferences_with_defaults($user_id) {
    $defaults = chatgabi_get_default_preferences();
    $user_prefs = chatgabi_get_all_user_preferences($user_id);
    
    return array_merge($defaults, $user_prefs);
}

/**
 * Get user analytics data
 * Note: Function moved to user-analytics.php to avoid conflicts
 * This function is now defined there with enhanced functionality
 */
// Function removed to prevent redeclaration conflict
// Use the enhanced version in user-analytics.php

/**
 * Get activity icon based on conversation type
 */
function chatgabi_get_activity_icon($conversation_type) {
    $icons = array(
        'business_plan' => '📋',
        'marketing_strategy' => '📈',
        'financial_forecast' => '💰',
        'market_analysis' => '🎯',
        'general_chat' => '💬',
        'template_generation' => '📝',
        'opportunity_search' => '🚀',
        'sector_analysis' => '🏢'
    );

    return $icons[$conversation_type] ?? '💬';
}

// Note: AJAX handlers for user preferences are defined in ajax-handlers.php

// Initialize preferences system
add_action('init', 'chatgabi_init_user_preferences');

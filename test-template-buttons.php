<?php
/**
 * Test Template Button Functionality
 * 
 * This script tests the ChatGABI template management button functionality
 * to ensure all interactive elements are working correctly.
 */

// Load WordPress
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    wp_die('You must be logged in as an administrator to run this test.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Template Button Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-button { margin: 5px; padding: 8px 15px; background: #0073aa; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #005a87; }
        #test-results { margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>ChatGABI Template Button Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. JavaScript File Loading Test</h2>
        <p>Testing if template-management.js is properly loaded and accessible...</p>
        <button class="test-button" onclick="testJavaScriptLoading()">Test JavaScript Loading</button>
        <div id="js-test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. AJAX Endpoints Test</h2>
        <p>Testing if AJAX handlers are properly registered...</p>
        <button class="test-button" onclick="testAjaxEndpoints()">Test AJAX Endpoints</button>
        <div id="ajax-test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Button Event Binding Test</h2>
        <p>Testing if button event listeners are properly bound...</p>
        <button class="test-button" onclick="testButtonBinding()">Test Button Binding</button>
        <div id="binding-test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Template Page Access Test</h2>
        <p>Testing direct access to the template management page...</p>
        <a href="<?php echo admin_url('admin.php?page=chatgabi-templates'); ?>" target="_blank" class="test-button">
            Open Template Management Page
        </a>
        <div id="page-test-result">
            <p class="info">Click the button above to open the template management page in a new tab and test button functionality manually.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>5. CSS Styling Test</h2>
        <p>Testing if CSS styles are properly applied to buttons...</p>
        <button class="test-button" onclick="testCSSStyles()">Test CSS Styles</button>
        <div id="css-test-result"></div>
    </div>

    <div id="test-results">
        <h3>Test Results Summary</h3>
        <div id="results-content">
            <p>Run the tests above to see results here...</p>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let testResults = [];

        function addResult(test, status, message) {
            testResults.push({test, status, message});
            updateResultsSummary();
        }

        function updateResultsSummary() {
            let html = '<h4>Test Results:</h4>';
            testResults.forEach(result => {
                const statusClass = result.status === 'success' ? 'success' : 'error';
                html += `<p class="${statusClass}"><strong>${result.test}:</strong> ${result.message}</p>`;
            });
            document.getElementById('results-content').innerHTML = html;
        }

        function testJavaScriptLoading() {
            const resultDiv = document.getElementById('js-test-result');
            
            // Check if jQuery is loaded
            if (typeof jQuery === 'undefined') {
                resultDiv.innerHTML = '<p class="error">❌ jQuery is not loaded</p>';
                addResult('JavaScript Loading', 'error', 'jQuery is not loaded');
                return;
            }

            // Check if ChatGABITemplates object exists
            if (typeof window.ChatGABITemplates === 'undefined') {
                resultDiv.innerHTML = '<p class="error">❌ ChatGABITemplates object not found</p>';
                addResult('JavaScript Loading', 'error', 'ChatGABITemplates object not found');
                return;
            }

            resultDiv.innerHTML = '<p class="success">✅ JavaScript files loaded successfully</p>';
            addResult('JavaScript Loading', 'success', 'All JavaScript files loaded correctly');
        }

        function testAjaxEndpoints() {
            const resultDiv = document.getElementById('ajax-test-result');
            resultDiv.innerHTML = '<p class="info">Testing AJAX endpoints...</p>';

            // Test chatgabi_get_sectors endpoint
            jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'chatgabi_get_sectors',
                country: 'GH',
                nonce: '<?php echo wp_create_nonce('chatgabi_get_sectors'); ?>'
            })
            .done(function(response) {
                if (response.success) {
                    resultDiv.innerHTML = '<p class="success">✅ AJAX endpoints working correctly</p>';
                    addResult('AJAX Endpoints', 'success', 'chatgabi_get_sectors endpoint working');
                } else {
                    resultDiv.innerHTML = '<p class="error">❌ AJAX endpoint returned error: ' + (response.data.message || 'Unknown error') + '</p>';
                    addResult('AJAX Endpoints', 'error', 'AJAX endpoint returned error');
                }
            })
            .fail(function() {
                resultDiv.innerHTML = '<p class="error">❌ AJAX request failed</p>';
                addResult('AJAX Endpoints', 'error', 'AJAX request failed');
            });
        }

        function testButtonBinding() {
            const resultDiv = document.getElementById('binding-test-result');
            
            // Create test buttons to check if event binding works
            const testHTML = `
                <div id="test-buttons" style="margin: 10px 0;">
                    <button class="use-template test-btn" data-template-id="test">Test Use Template</button>
                    <button class="preview-template test-btn" data-template-id="test">Test Preview</button>
                    <button class="create-new-template test-btn" data-category="test">Test Create New</button>
                </div>
            `;
            
            resultDiv.innerHTML = testHTML + '<p class="info">Click the test buttons above to verify event binding...</p>';
            
            // Bind test events
            jQuery('.test-btn').on('click', function() {
                const buttonType = jQuery(this).attr('class').split(' ')[0];
                resultDiv.innerHTML += `<p class="success">✅ ${buttonType} button click detected</p>`;
                addResult('Button Binding', 'success', `${buttonType} button event binding working`);
            });
        }

        function testCSSStyles() {
            const resultDiv = document.getElementById('css-test-result');
            
            // Check if template management CSS is loaded
            const testElement = jQuery('<div class="template-actions"><button class="button button-primary">Test Button</button></div>');
            jQuery('body').append(testElement);
            
            const buttonStyles = testElement.find('button').css(['cursor', 'background-color', 'border-radius']);
            testElement.remove();
            
            if (buttonStyles.cursor === 'pointer') {
                resultDiv.innerHTML = '<p class="success">✅ CSS styles applied correctly</p>';
                addResult('CSS Styles', 'success', 'Button styles applied correctly');
            } else {
                resultDiv.innerHTML = '<p class="error">❌ CSS styles not applied correctly</p>';
                addResult('CSS Styles', 'error', 'Button styles not applied correctly');
            }
        }

        // Auto-run basic tests on page load
        jQuery(document).ready(function() {
            setTimeout(testJavaScriptLoading, 500);
        });
    </script>
</body>
</html>

<?php
/**
 * Configure ChatGABI with Bright Data API Parameters
 * 
 * This script automatically configures ChatGABI with your Bright Data credentials
 *
 * @package ChatGABI
 * @since 1.4.0
 */

// Load WordPress
require_once(dirname(__FILE__) . '/../../../wp-config.php');

echo "🔧 ChatGABI Bright Data Configuration\n";
echo "====================================\n";
echo "Configuring ChatGABI with your Bright Data parameters...\n\n";

// Your Bright Data Parameters (from screenshot)
$brightdata_config = array(
    'api_key' => 'c1e16397d3e2745675613dcb784eaf95cce2dd6ae706bbc407061835a73404b2',
    'zone_id' => 'chatgabiafricanscraping_1',
    'endpoint' => 'api.brightdata.com',
    'pricing_model' => 'pay_as_you_go',
    'rate_cpm' => 2.50 // $2.50 per 1000 requests
);

echo "📊 Configuration Details:\n";
echo "------------------------\n";
printf("API Key: %s...%s\n", substr($brightdata_config['api_key'], 0, 8), substr($brightdata_config['api_key'], -8));
printf("Zone ID: %s\n", $brightdata_config['zone_id']);
printf("Endpoint: %s\n", $brightdata_config['endpoint']);
printf("Pricing: $%.2f per 1000 requests\n", $brightdata_config['rate_cpm']);

// Test API Connection
echo "\n🔍 Testing Bright Data API Connection...\n";
echo "---------------------------------------\n";

$test_url = 'https://geo.brdtest.com/mygeo.json';
$api_endpoint = 'https://api.brightdata.com/request';

$request_data = array(
    'zone' => $brightdata_config['zone_id'],
    'url' => $test_url,
    'format' => 'raw'
);

$curl = curl_init();
curl_setopt_array($curl, array(
    CURLOPT_URL => $api_endpoint,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json',
        'Authorization: Bearer ' . $brightdata_config['api_key']
    ),
    CURLOPT_POSTFIELDS => json_encode($request_data),
    CURLOPT_TIMEOUT => 30
));

$response = curl_exec($curl);
$http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$curl_error = curl_error($curl);
curl_close($curl);

if ($curl_error) {
    echo "❌ Connection test failed: $curl_error\n";
} elseif ($http_code === 200) {
    echo "✅ Connection test successful!\n";
    echo "✅ Response received from Bright Data API\n";
    
    // Parse response to show location
    $response_data = json_decode($response, true);
    if (isset($response_data['country'])) {
        echo "✅ Detected location: " . $response_data['country'] . "\n";
    }
} else {
    echo "❌ Connection test failed with HTTP code: $http_code\n";
    echo "Response: " . substr($response, 0, 200) . "...\n";
}

// Configure WordPress Options
echo "\n⚙️ Updating ChatGABI WordPress Configuration...\n";
echo "----------------------------------------------\n";

$wordpress_options = array(
    'chatgabi_brightdata_api_key' => $brightdata_config['api_key'],
    'chatgabi_brightdata_zone_id' => $brightdata_config['zone_id'],
    'chatgabi_monthly_budget_limit' => 215, // $215 monthly budget
    'chatgabi_enable_hybrid_scraping' => 1,
    'chatgabi_brightdata_endpoint' => $brightdata_config['endpoint'],
    'chatgabi_brightdata_pricing_model' => 'api_requests'
);

$updated_options = 0;
foreach ($wordpress_options as $option_name => $option_value) {
    $result = update_option($option_name, $option_value);
    if ($result) {
        echo "✅ Updated: $option_name\n";
        $updated_options++;
    } else {
        // Check if option already exists with same value
        $existing_value = get_option($option_name);
        if ($existing_value == $option_value) {
            echo "✅ Already set: $option_name\n";
            $updated_options++;
        } else {
            echo "❌ Failed to update: $option_name\n";
        }
    }
}

echo "\n📊 Configuration Summary:\n";
echo "------------------------\n";
printf("WordPress options updated: %d/%d\n", $updated_options, count($wordpress_options));

// Update Hybrid Router Budget
echo "\n💰 Updating Cost Calculations...\n";
echo "--------------------------------\n";

// Calculate costs based on Bright Data API pricing
$estimated_monthly_requests = 50000; // 50K requests/month
$cost_per_1k_requests = $brightdata_config['rate_cpm'];
$estimated_monthly_cost = ($estimated_monthly_requests / 1000) * $cost_per_1k_requests;

printf("Estimated monthly requests: %s\n", number_format($estimated_monthly_requests));
printf("Cost per 1K requests: $%.2f\n", $cost_per_1k_requests);
printf("Estimated monthly cost: $%.2f\n", $estimated_monthly_cost);

// Update router budget
update_option('chatgabi_brightdata_estimated_monthly_cost', $estimated_monthly_cost);
echo "✅ Updated cost projections in system\n";

// Test ChatGABI Integration
echo "\n🔧 Testing ChatGABI Integration...\n";
echo "---------------------------------\n";

// Load hybrid scraping router
require_once get_template_directory() . '/inc/hybrid-scraping-router.php';

try {
    $router = new ChatGABI_Hybrid_Scraping_Router();
    echo "✅ Hybrid scraping router loaded successfully\n";
    
    // Test routing for a Bright Data target
    $test_source = array(
        'url' => 'https://bog.gov.gh/test',
        'requires_js' => true
    );
    
    // Use reflection to test routing
    $reflection = new ReflectionClass($router);
    $method = $reflection->getMethod('analyze_routing_requirements');
    $method->setAccessible(true);
    
    $routing_result = $method->invoke($router, $test_source, 'Ghana', 'Government');
    
    if ($routing_result['api'] === 'brightdata') {
        echo "✅ Routing test passed: Government sites → Bright Data\n";
    } else {
        echo "⚠️  Routing test: Expected brightdata, got " . $routing_result['api'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Router test failed: " . $e->getMessage() . "\n";
}

// Final Status
echo "\n🎉 CONFIGURATION COMPLETE!\n";
echo "==========================\n";

if ($updated_options >= count($wordpress_options) - 1) {
    echo "✅ ChatGABI is now configured with Bright Data!\n";
    echo "✅ Hybrid scraping system ready for production\n";
    echo "✅ Estimated cost: $" . number_format($estimated_monthly_cost, 2) . "/month\n";
    echo "✅ Budget limit: $215/month\n";
    
    echo "\n🚀 Next Steps:\n";
    echo "1. Visit WordPress Admin → ChatGABI → Hybrid Scraping\n";
    echo "2. Verify settings are correctly displayed\n";
    echo "3. Click 'Test API Connections' to confirm\n";
    echo "4. Start using the hybrid scraping system\n";
    echo "5. Monitor usage in Bright Data dashboard\n";
    
} else {
    echo "⚠️  Configuration partially completed\n";
    echo "Please check WordPress permissions and try again\n";
}

echo "\n📊 WordPress Admin Configuration:\n";
echo "=================================\n";
echo "Navigate to: WordPress Admin → ChatGABI → Hybrid Scraping\n";
echo "You should see:\n";
printf("- Bright Data API Key: %s...%s\n", substr($brightdata_config['api_key'], 0, 8), substr($brightdata_config['api_key'], -8));
printf("- Bright Data Zone ID: %s\n", $brightdata_config['zone_id']);
echo "- Monthly Budget Limit: 215\n";
echo "- Enable Hybrid Scraping: ✓ Checked\n";

echo "\nConfiguration completed at: " . date('Y-m-d H:i:s') . "\n";
?>

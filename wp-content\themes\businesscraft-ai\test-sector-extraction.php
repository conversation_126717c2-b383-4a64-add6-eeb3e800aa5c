<?php
/**
 * Test script for sector extraction functionality
 */

// Force output buffering off
if (ob_get_level()) {
    ob_end_clean();
}

// Define WordPress constants for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/../../../');
}
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Define a simple error_log function if it doesn't exist or redirect it
if (!function_exists('error_log')) {
    function error_log($message) {
        echo "LOG: " . $message . "\n";
    }
}

// Include the data loader functions
require_once(__DIR__ . '/inc/data-loader.php');

echo "=== BusinessCraft AI Sector Extraction Test ===\n\n";

// Test cases for sector extraction
$test_cases = [
    ['Ghana', 'Digital Content & Gaming'],
    ['Ghana', 'agriculture'], // Test case-insensitive
    ['Ghana', 'fintech'], // Test partial match
    ['Ghana', 'NonExistentSector'], // Test no match
    ['Kenya', 'agriculture'], // Test different country
    ['Nigeria', 'technology'], // Test partial match
    ['InvalidCountry', 'agriculture'], // Test invalid country
];

foreach ($test_cases as $i => $test_case) {
    list($country, $sector) = $test_case;
    
    echo "Test " . ($i + 1) . ": {$country} -> {$sector}\n";
    echo str_repeat('-', 50) . "\n";
    
    $result = get_sector_context_by_country($country, $sector);
    
    if ($result === null) {
        echo "❌ No sector data found\n";
    } else {
        echo "✅ Sector data found!\n";
        echo "📊 Sector Name: " . $result['sector_name'] . "\n";
        echo "📝 Overview: " . substr($result['overview'], 0, 100) . "...\n";
        
        if (isset($result['key_conditions'])) {
            echo "🔍 Key Conditions Available:\n";
            $conditions = $result['key_conditions'];
            $key_fields = ['regulatory_environment', 'market_size_and_growth', 'major_players', 'challenges_and_risks'];
            
            foreach ($key_fields as $field) {
                if (isset($conditions[$field])) {
                    echo "   ✓ {$field}\n";
                } else {
                    echo "   ✗ {$field} (missing)\n";
                }
            }
            
            // Show major players if available
            if (isset($conditions['major_players']) && is_array($conditions['major_players'])) {
                echo "🏢 Major Players: " . implode(', ', array_slice($conditions['major_players'], 0, 3)) . "\n";
            }
        }
    }
    
    echo "\n";
}

// Test the helper function to get available sectors
echo "=== Testing Available Sectors Function ===\n\n";

$countries = ['Ghana', 'Kenya'];
foreach ($countries as $country) {
    echo "Available sectors in {$country}:\n";
    $sectors = get_available_sectors_by_country($country);
    
    if ($sectors === false) {
        echo "❌ Failed to get sectors for {$country}\n";
    } else {
        echo "📈 Found " . count($sectors) . " sectors:\n";
        foreach ($sectors as $i => $sector) {
            echo "   " . ($i + 1) . ". {$sector}\n";
        }
    }
    echo "\n";
}

echo "=== Test Complete ===\n";

<?php
/**
 * Create Sample Users & Credits Data for ChatGABI Testing
 * 
 * This script creates sample users and credit transactions for testing the Users & Credits Management System
 */

// Load WordPress
require_once('wp-load.php');

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Creating Sample Users & Credits Data for ChatGABI</h1>";

// Check if we should create sample data
if (!isset($_GET['create']) || $_GET['create'] !== 'yes') {
    echo "<p><strong>Warning:</strong> This script will create sample users and credit data.</p>";
    echo "<p><a href='?create=yes' class='button'>Click here to create sample data</a></p>";
    exit;
}

// Sample user data
$sample_users = array(
    array(
        'username' => 'kwame_tech',
        'email' => '<EMAIL>',
        'display_name' => 'Kwame Asante',
        'country' => 'GH',
        'sector' => 'Technology',
        'language' => 'tw',
        'tier' => 'ultra',
        'credits' => 250
    ),
    array(
        'username' => 'amina_agri',
        'email' => '<EMAIL>',
        'display_name' => 'Amina Wanjiku',
        'country' => 'KE',
        'sector' => 'Agriculture',
        'language' => 'sw',
        'tier' => 'basic',
        'credits' => 150
    ),
    array(
        'username' => 'chidi_fintech',
        'email' => '<EMAIL>',
        'display_name' => 'Chidi Okafor',
        'country' => 'NG',
        'sector' => 'Fintech & Digital Banking',
        'language' => 'yo',
        'tier' => 'ultra',
        'credits' => 300
    ),
    array(
        'username' => 'nomsa_retail',
        'email' => '<EMAIL>',
        'display_name' => 'Nomsa Mthembu',
        'country' => 'ZA',
        'sector' => 'Retail Trade',
        'language' => 'zu',
        'tier' => 'basic',
        'credits' => 75
    ),
    array(
        'username' => 'kofi_creative',
        'email' => '<EMAIL>',
        'display_name' => 'Kofi Mensah',
        'country' => 'GH',
        'sector' => 'Creative Arts',
        'language' => 'tw',
        'tier' => 'basic',
        'credits' => 45
    ),
    array(
        'username' => 'fatima_health',
        'email' => '<EMAIL>',
        'display_name' => 'Fatima Abdullahi',
        'country' => 'NG',
        'sector' => 'Healthcare',
        'language' => 'yo',
        'tier' => 'ultra',
        'credits' => 180
    ),
    array(
        'username' => 'james_mining',
        'email' => '<EMAIL>',
        'display_name' => 'James van der Merwe',
        'country' => 'ZA',
        'sector' => 'Mining',
        'language' => 'en',
        'tier' => 'basic',
        'credits' => 120
    ),
    array(
        'username' => 'grace_education',
        'email' => '<EMAIL>',
        'display_name' => 'Grace Nyong\'o',
        'country' => 'KE',
        'sector' => 'Education',
        'language' => 'sw',
        'tier' => 'basic',
        'credits' => 90
    )
);

$created_users = array();

echo "<h2>Creating Sample Users</h2>";

foreach ($sample_users as $user_data) {
    // Check if user already exists
    $existing_user = get_user_by('email', $user_data['email']);
    if ($existing_user) {
        echo "⚠️ User {$user_data['display_name']} already exists<br>";
        $created_users[] = $existing_user->ID;
        continue;
    }

    // Create user
    $user_id = wp_create_user(
        $user_data['username'],
        wp_generate_password(12, false),
        $user_data['email']
    );

    if (is_wp_error($user_id)) {
        echo "❌ Failed to create user {$user_data['display_name']}: " . $user_id->get_error_message() . "<br>";
        continue;
    }

    // Update user display name
    wp_update_user(array(
        'ID' => $user_id,
        'display_name' => $user_data['display_name']
    ));

    // Set user meta
    update_user_meta($user_id, 'chatgabi_user_country', $user_data['country']);
    update_user_meta($user_id, 'chatgabi_user_sector', $user_data['sector']);
    update_user_meta($user_id, 'chatgabi_preferred_language', $user_data['language']);
    update_user_meta($user_id, 'businesscraft_ai_tier', $user_data['tier']);
    update_user_meta($user_id, 'businesscraft_credits', $user_data['credits']);

    $created_users[] = $user_id;
    echo "✅ Created user: {$user_data['display_name']} (ID: {$user_id})<br>";
}

echo "<h2>Creating Sample Credit Transactions</h2>";

global $wpdb;

// Create credit transactions for each user
foreach ($created_users as $user_id) {
    $user = get_user_by('ID', $user_id);
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true);
    
    // Create purchase transaction
    $purchase_amount = rand(100, 500);
    $wpdb->insert(
        $wpdb->prefix . 'businesscraft_ai_credit_logs',
        array(
            'user_id' => $user_id,
            'action' => 'purchase',
            'credits_amount' => $purchase_amount,
            'credits_before' => 0,
            'credits_after' => $purchase_amount,
            'description' => 'Initial credit purchase',
            'transaction_reference' => 'PAY_' . strtoupper(wp_generate_password(10, false)),
            'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
        ),
        array('%d', '%s', '%d', '%d', '%d', '%s', '%s', '%s')
    );

    // Create some usage transactions
    $usage_sessions = rand(3, 15);
    $credits_used = 0;
    
    for ($i = 0; $i < $usage_sessions; $i++) {
        $session_credits = rand(5, 25);
        $credits_used += $session_credits;
        
        $wpdb->insert(
            $wpdb->prefix . 'businesscraft_ai_credit_logs',
            array(
                'user_id' => $user_id,
                'action' => 'usage',
                'credits_amount' => -$session_credits,
                'credits_before' => $purchase_amount - $credits_used + $session_credits,
                'credits_after' => $purchase_amount - $credits_used,
                'description' => 'AI chat session - Business plan generation',
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 25) . ' days'))
            ),
            array('%d', '%s', '%d', '%d', '%d', '%s', '%s')
        );
    }

    // Maybe create an adjustment transaction
    if (rand(1, 3) === 1) {
        $adjustment = rand(-20, 50);
        $wpdb->insert(
            $wpdb->prefix . 'businesscraft_ai_credit_logs',
            array(
                'user_id' => $user_id,
                'action' => 'adjustment',
                'credits_amount' => $adjustment,
                'credits_before' => $current_credits - $adjustment,
                'credits_after' => $current_credits,
                'description' => $adjustment > 0 ? 'Promotional credit bonus' : 'Credit correction',
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 20) . ' days'))
            ),
            array('%d', '%s', '%d', '%d', '%d', '%s', '%s')
        );
    }

    echo "✅ Created transactions for {$user->display_name}<br>";
}

echo "<h2>Creating Sample Payment Transactions</h2>";

// Create some sample payment transactions
$currencies = array('GHS', 'NGN', 'KES', 'ZAR');
$payment_statuses = array('success', 'failed', 'pending');

foreach (array_slice($created_users, 0, 5) as $user_id) {
    $user = get_user_by('ID', $user_id);
    $country = get_user_meta($user_id, 'chatgabi_user_country', true);
    
    // Map country to currency
    $currency_map = array('GH' => 'GHS', 'NG' => 'NGN', 'KE' => 'KES', 'ZA' => 'ZAR');
    $currency = isset($currency_map[$country]) ? $currency_map[$country] : 'USD';
    
    $amount = rand(1000, 5000); // Amount in cents
    $status = $payment_statuses[array_rand($payment_statuses)];
    $reference = 'PAY_' . strtoupper(wp_generate_password(12, false));
    
    $wpdb->insert(
        $wpdb->prefix . 'businesscraft_ai_transactions',
        array(
            'user_id' => $user_id,
            'reference' => $reference,
            'amount' => $amount,
            'currency' => $currency,
            'status' => $status,
            'gateway' => 'paystack',
            'gateway_reference' => 'PS_' . strtoupper(wp_generate_password(10, false)),
            'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days')),
            'updated_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 25) . ' days'))
        ),
        array('%d', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s')
    );

    echo "✅ Created payment transaction for {$user->display_name} ({$currency} " . number_format($amount/100, 2) . " - {$status})<br>";
}

echo "<h2>Sample Data Creation Complete!</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3>✅ Sample Data Created Successfully!</h3>";
echo "<ul>";
echo "<li>✅ " . count($created_users) . " sample users created</li>";
echo "<li>✅ Multiple credit transactions per user</li>";
echo "<li>✅ Sample payment transactions with local currencies</li>";
echo "<li>✅ Users distributed across 4 African countries</li>";
echo "<li>✅ Different user tiers (Basic/Ultra)</li>";
echo "<li>✅ Various sectors and languages</li>";
echo "</ul>";

echo "<h3>🎯 Next Steps:</h3>";
echo "<ol>";
echo "<li>Go to <a href='wp-admin/admin.php?page=chatgabi-users' target='_blank'>ChatGABI Users & Credits</a></li>";
echo "<li>Test the Users Overview tab with filtering and search</li>";
echo "<li>Try the Credit Management features</li>";
echo "<li>View Transaction History</li>";
echo "<li>Check the Analytics dashboard</li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>Note:</strong> You can run this script multiple times. Existing users will be skipped.</p>";
?>

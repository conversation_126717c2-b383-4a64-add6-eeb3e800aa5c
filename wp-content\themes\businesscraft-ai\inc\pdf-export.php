<?php
/**
 * PDF Export Functionality for BusinessCraft AI
 * 
 * Handles professional document export for business templates
 * Uses TCPDF library for PDF generation
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize PDF export functionality
 */
function businesscraft_ai_init_pdf_export() {
    // Add AJAX handlers for PDF export
    add_action('wp_ajax_export_template_pdf', 'businesscraft_ai_handle_pdf_export');
    add_action('wp_ajax_nopriv_export_template_pdf', 'businesscraft_ai_handle_pdf_export');
    
    // Add REST API endpoints
    add_action('rest_api_init', 'businesscraft_ai_register_pdf_export_routes');
    
    // Enqueue PDF export scripts
    add_action('wp_enqueue_scripts', 'businesscraft_ai_enqueue_pdf_export_scripts');
}
add_action('init', 'businesscraft_ai_init_pdf_export');

/**
 * Register REST API routes for PDF export
 */
function businesscraft_ai_register_pdf_export_routes() {
    register_rest_route('businesscraft-ai/v1', '/export/pdf', array(
        'methods' => 'POST',
        'callback' => 'businesscraft_ai_rest_export_pdf',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'template_id' => array(
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint'
            ),
            'format' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'pdf',
                'enum' => array('pdf', 'docx')
            ),
            'include_branding' => array(
                'required' => false,
                'type' => 'boolean',
                'default' => true
            ),
            'email_delivery' => array(
                'required' => false,
                'type' => 'boolean',
                'default' => false
            ),
            'email_address' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_email'
            ),
            'email_message' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field'
            )
        )
    ));
    
    register_rest_route('businesscraft-ai/v1', '/export/history', array(
        'methods' => 'GET',
        'callback' => 'businesscraft_ai_rest_get_export_history',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
}

/**
 * Enqueue PDF export scripts and styles
 */
function businesscraft_ai_enqueue_pdf_export_scripts() {
    if (is_page('templates') || is_page('dashboard')) {
        wp_enqueue_script(
            'businesscraft-ai-pdf-export',
            get_template_directory_uri() . '/assets/js/pdf-export.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('businesscraft-ai-pdf-export', 'businesscraftPdfExport', array(
            'restUrl' => rest_url('businesscraft-ai/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'strings' => array(
                'exporting' => __('Generating PDF...', 'businesscraft-ai'),
                'exportSuccess' => __('PDF generated successfully!', 'businesscraft-ai'),
                'exportError' => __('Failed to generate PDF. Please try again.', 'businesscraft-ai'),
                'downloadStarting' => __('Download starting...', 'businesscraft-ai'),
                'selectTemplate' => __('Please select a template to export.', 'businesscraft-ai')
            )
        ));
        
        wp_enqueue_style(
            'businesscraft-ai-pdf-export',
            get_template_directory_uri() . '/assets/css/pdf-export.css',
            array(),
            '1.0.0'
        );
    }
}

/**
 * Handle AJAX PDF export request
 */
function businesscraft_ai_handle_pdf_export() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'wp_rest')) {
        wp_die(__('Security check failed', 'businesscraft-ai'));
    }
    
    // Check user permissions
    if (!is_user_logged_in()) {
        wp_die(__('You must be logged in to export templates', 'businesscraft-ai'));
    }
    
    $template_id = isset($_POST['template_id']) ? absint($_POST['template_id']) : 0;
    $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : 'pdf';
    $include_branding = isset($_POST['include_branding']) ? (bool)$_POST['include_branding'] : true;
    $email_delivery = isset($_POST['email_delivery']) ? (bool)$_POST['email_delivery'] : false;
    $email_address = isset($_POST['email_address']) ? sanitize_email($_POST['email_address']) : '';
    $email_message = isset($_POST['email_message']) ? sanitize_textarea_field($_POST['email_message']) : '';
    
    if (!$template_id) {
        wp_send_json_error(__('Invalid template ID', 'businesscraft-ai'));
    }
    
    // Generate document
    $result = businesscraft_ai_generate_template_pdf($template_id, $format, $include_branding);

    if ($result['success']) {
        // Handle email delivery if requested
        if ($email_delivery && !empty($email_address)) {
            $email_result = businesscraft_ai_email_export($result, $email_address, $email_message);
            $result['email_sent'] = $email_result['success'];
            $result['email_message'] = $email_result['message'];
        }

        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

/**
 * REST API handler for PDF export
 */
function businesscraft_ai_rest_export_pdf($request) {
    $template_id = $request->get_param('template_id');
    $format = $request->get_param('format');
    $include_branding = $request->get_param('include_branding');
    
    $result = businesscraft_ai_generate_template_pdf($template_id, $format, $include_branding);
    
    if ($result['success']) {
        return rest_ensure_response($result);
    } else {
        return new WP_Error('export_failed', $result['message'], array('status' => 500));
    }
}

/**
 * Get export history for current user
 */
function businesscraft_ai_rest_get_export_history($request) {
    global $wpdb;
    
    $user_id = get_current_user_id();
    $table_name = $wpdb->prefix . 'chatgabi_export_history';
    
    $exports = $wpdb->get_results($wpdb->prepare(
        "SELECT eh.*, gt.template_name, gt.template_type 
         FROM {$table_name} eh
         LEFT JOIN {$wpdb->prefix}chatgabi_generated_templates gt ON eh.template_id = gt.id
         WHERE eh.user_id = %d 
         ORDER BY eh.created_at DESC 
         LIMIT 50",
        $user_id
    ));
    
    return rest_ensure_response(array(
        'success' => true,
        'exports' => $exports
    ));
}

/**
 * Create export history table
 */
function businesscraft_ai_create_export_history_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_export_history';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        template_id bigint(20) NOT NULL,
        export_format varchar(10) NOT NULL DEFAULT 'pdf',
        file_name varchar(255) NOT NULL,
        file_path varchar(500) NOT NULL,
        file_size bigint(20) NOT NULL DEFAULT 0,
        download_count int(11) NOT NULL DEFAULT 0,
        include_branding tinyint(1) NOT NULL DEFAULT 1,
        export_status varchar(20) NOT NULL DEFAULT 'completed',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        expires_at datetime DEFAULT NULL,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY template_id (template_id),
        KEY export_status (export_status),
        KEY created_at (created_at)
    ) {$charset_collate};";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Install PDF export functionality
 */
function businesscraft_ai_install_pdf_export() {
    businesscraft_ai_create_export_history_table();
    
    // Create uploads directory for PDF exports
    $upload_dir = wp_upload_dir();
    $pdf_export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    
    if (!file_exists($pdf_export_dir)) {
        wp_mkdir_p($pdf_export_dir);
        
        // Create .htaccess to protect direct access
        $htaccess_content = "Order deny,allow\nDeny from all\n";
        file_put_contents($pdf_export_dir . '/.htaccess', $htaccess_content);
    }
}

// Install on theme activation
add_action('after_switch_theme', 'businesscraft_ai_install_pdf_export');

/**
 * Load TCPDF library or use alternative
 */
function businesscraft_ai_load_tcpdf() {
    // Try to load TCPDF if available
    $tcpdf_path = get_template_directory() . '/lib/tcpdf/tcpdf.php';

    if (file_exists($tcpdf_path)) {
        require_once $tcpdf_path;
        return true;
    }

    // Check if TCPDF is available via WordPress plugins or other means
    if (class_exists('TCPDF')) {
        return true;
    }

    // Use alternative HTML to PDF conversion
    return false;
}

/**
 * Main PDF generation function
 */
function businesscraft_ai_generate_template_pdf($template_id, $format = 'pdf', $include_branding = true) {
    try {
        // Load template data
        $template = businesscraft_ai_get_template_for_export($template_id);
        
        if (!$template) {
            return array(
                'success' => false,
                'message' => __('Template not found or access denied', 'businesscraft-ai')
            );
        }
        
        // Generate document based on format
        if ($format === 'pdf') {
            return businesscraft_ai_generate_pdf_document($template, $include_branding);
        } elseif ($format === 'docx') {
            return businesscraft_ai_generate_docx_document($template, $include_branding);
        } elseif ($format === 'html') {
            return businesscraft_ai_generate_html_pdf_document($template, $include_branding);
        } else {
            return array(
                'success' => false,
                'message' => __('Unsupported export format', 'businesscraft-ai')
            );
        }
        
    } catch (Exception $e) {
        error_log('PDF Export Error: ' . $e->getMessage());
        return array(
            'success' => false,
            'message' => __('An error occurred while generating the document', 'businesscraft-ai')
        );
    }
}

/**
 * Get template data for export
 */
function businesscraft_ai_get_template_for_export($template_id) {
    global $wpdb;

    $user_id = get_current_user_id();
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name}
         WHERE id = %d AND user_id = %d AND status = 'completed'",
        $template_id,
        $user_id
    ));

    return $template;
}

/**
 * Generate PDF document using TCPDF or alternative
 */
function businesscraft_ai_generate_pdf_document($template, $include_branding = true) {
    $tcpdf_available = businesscraft_ai_load_tcpdf();

    if (!$tcpdf_available) {
        // Use HTML to PDF alternative
        return businesscraft_ai_generate_html_pdf_document($template, $include_branding);
    }

    // Create new PDF document
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

    // Set document information
    $pdf->SetCreator('BusinessCraft AI');
    $pdf->SetAuthor(get_bloginfo('name'));
    $pdf->SetTitle($template->template_name);
    $pdf->SetSubject('Business Document - ' . ucwords(str_replace('-', ' ', $template->template_type)));

    // Set default header and footer fonts
    $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
    $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));

    // Set default monospaced font
    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

    // Set margins
    $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
    $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

    // Set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

    // Set image scale factor
    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

    // Add branding header if requested
    if ($include_branding) {
        businesscraft_ai_add_pdf_header($pdf, $template);
    } else {
        $pdf->setPrintHeader(false);
    }

    // Add footer
    businesscraft_ai_add_pdf_footer($pdf, $template);

    // Add first page
    $pdf->AddPage();

    // Generate content based on template type
    switch ($template->template_type) {
        case 'business-plan':
        case 'business_plan':
            businesscraft_ai_generate_business_plan_pdf($pdf, $template);
            break;
        case 'marketing-strategy':
        case 'marketing_strategy':
            businesscraft_ai_generate_marketing_strategy_pdf($pdf, $template);
            break;
        case 'financial-forecast':
        case 'financial_forecast':
            businesscraft_ai_generate_financial_forecast_pdf($pdf, $template);
            break;
        default:
            businesscraft_ai_generate_generic_template_pdf($pdf, $template);
            break;
    }

    // Generate filename
    $filename = businesscraft_ai_generate_export_filename($template, 'pdf');

    // Save PDF to uploads directory
    $upload_dir = wp_upload_dir();
    $pdf_export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    $file_path = $pdf_export_dir . '/' . $filename;

    // Output PDF to file
    $pdf->Output($file_path, 'F');

    // Record export in history
    $export_id = businesscraft_ai_record_export_history($template->id, 'pdf', $filename, $file_path, filesize($file_path), $include_branding);

    // Generate download URL
    $download_url = add_query_arg(array(
        'action' => 'download_export',
        'export_id' => $export_id,
        'nonce' => wp_create_nonce('download_export_' . $export_id)
    ), admin_url('admin-ajax.php'));

    return array(
        'success' => true,
        'message' => __('PDF generated successfully', 'businesscraft-ai'),
        'filename' => $filename,
        'download_url' => $download_url,
        'export_id' => $export_id,
        'file_size' => filesize($file_path)
    );
}

/**
 * Add PDF header with branding
 */
function businesscraft_ai_add_pdf_header($pdf, $template) {
    $pdf->SetHeaderData(
        '', // Logo file (empty for now)
        0, // Logo width
        get_bloginfo('name'), // Header title
        sprintf(__('Professional %s - Generated by BusinessCraft AI', 'businesscraft-ai'),
                ucwords(str_replace('-', ' ', $template->template_type))),
        array(61, 78, 129), // Header text color (RGB)
        array(240, 240, 240) // Header line color (RGB)
    );
}

/**
 * Add PDF footer
 */
function businesscraft_ai_add_pdf_footer($pdf, $template) {
    $pdf->setFooterData(
        array(100, 100, 100), // Footer text color (RGB)
        array(200, 200, 200)  // Footer line color (RGB)
    );

    // Custom footer content
    $pdf->SetFooterMargin(15);
    $pdf->setPrintFooter(true);
}

/**
 * Generate business plan PDF content
 */
function businesscraft_ai_generate_business_plan_pdf($pdf, $template) {
    // Set font
    $pdf->SetFont('helvetica', '', 12);

    // Title page
    $pdf->SetFont('helvetica', 'B', 24);
    $pdf->Cell(0, 20, $template->template_name, 0, 1, 'C');

    $pdf->SetFont('helvetica', '', 16);
    $pdf->Ln(10);
    $pdf->Cell(0, 10, sprintf(__('Business Plan for %s', 'businesscraft-ai'), $template->business_idea), 0, 1, 'C');

    $pdf->Ln(5);
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, sprintf(__('Industry: %s', 'businesscraft-ai'), $template->industry_sector), 0, 1, 'C');
    $pdf->Cell(0, 10, sprintf(__('Target Market: %s', 'businesscraft-ai'), chatgabi_get_country_name_from_code($template->target_country)), 0, 1, 'C');
    $pdf->Cell(0, 10, sprintf(__('Business Stage: %s', 'businesscraft-ai'), ucwords(str_replace('-', ' ', $template->business_stage))), 0, 1, 'C');

    $pdf->Ln(20);

    // Add new page for content
    $pdf->AddPage();

    // Process and add the generated content
    businesscraft_ai_add_formatted_content($pdf, $template->generated_content);
}

/**
 * Generate marketing strategy PDF content
 */
function businesscraft_ai_generate_marketing_strategy_pdf($pdf, $template) {
    // Set font
    $pdf->SetFont('helvetica', '', 12);

    // Title page
    $pdf->SetFont('helvetica', 'B', 24);
    $pdf->Cell(0, 20, $template->template_name, 0, 1, 'C');

    $pdf->SetFont('helvetica', '', 16);
    $pdf->Ln(10);
    $pdf->Cell(0, 10, sprintf(__('Marketing Strategy for %s', 'businesscraft-ai'), $template->business_idea), 0, 1, 'C');

    $pdf->Ln(5);
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, sprintf(__('Industry: %s', 'businesscraft-ai'), $template->industry_sector), 0, 1, 'C');
    $pdf->Cell(0, 10, sprintf(__('Target Market: %s', 'businesscraft-ai'), chatgabi_get_country_name_from_code($template->target_country)), 0, 1, 'C');

    $pdf->Ln(20);

    // Add new page for content
    $pdf->AddPage();

    // Process and add the generated content
    businesscraft_ai_add_formatted_content($pdf, $template->generated_content);
}

/**
 * Generate financial forecast PDF content
 */
function businesscraft_ai_generate_financial_forecast_pdf($pdf, $template) {
    // Set font
    $pdf->SetFont('helvetica', '', 12);

    // Title page
    $pdf->SetFont('helvetica', 'B', 24);
    $pdf->Cell(0, 20, $template->template_name, 0, 1, 'C');

    $pdf->SetFont('helvetica', '', 16);
    $pdf->Ln(10);
    $pdf->Cell(0, 10, sprintf(__('Financial Forecast for %s', 'businesscraft-ai'), $template->business_idea), 0, 1, 'C');

    $pdf->Ln(5);
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, sprintf(__('Industry: %s', 'businesscraft-ai'), $template->industry_sector), 0, 1, 'C');
    $pdf->Cell(0, 10, sprintf(__('Target Market: %s', 'businesscraft-ai'), chatgabi_get_country_name_from_code($template->target_country)), 0, 1, 'C');

    $pdf->Ln(20);

    // Add new page for content
    $pdf->AddPage();

    // Process and add the generated content
    businesscraft_ai_add_formatted_content($pdf, $template->generated_content);
}

/**
 * Generate generic template PDF content
 */
function businesscraft_ai_generate_generic_template_pdf($pdf, $template) {
    // Set font
    $pdf->SetFont('helvetica', '', 12);

    // Title page
    $pdf->SetFont('helvetica', 'B', 24);
    $pdf->Cell(0, 20, $template->template_name, 0, 1, 'C');

    $pdf->SetFont('helvetica', '', 16);
    $pdf->Ln(10);
    $pdf->Cell(0, 10, sprintf(__('Business Document for %s', 'businesscraft-ai'), $template->business_idea), 0, 1, 'C');

    $pdf->Ln(5);
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, sprintf(__('Industry: %s', 'businesscraft-ai'), $template->industry_sector), 0, 1, 'C');
    $pdf->Cell(0, 10, sprintf(__('Target Market: %s', 'businesscraft-ai'), chatgabi_get_country_name_from_code($template->target_country)), 0, 1, 'C');

    $pdf->Ln(20);

    // Add new page for content
    $pdf->AddPage();

    // Process and add the generated content
    businesscraft_ai_add_formatted_content($pdf, $template->generated_content);
}

/**
 * Add formatted content to PDF
 */
function businesscraft_ai_add_formatted_content($pdf, $content) {
    // Parse and format the content
    $lines = explode("\n", $content);

    foreach ($lines as $line) {
        $line = trim($line);

        if (empty($line)) {
            $pdf->Ln(5);
            continue;
        }

        // Check for headers (lines starting with #)
        if (preg_match('/^#+\s*(.+)/', $line, $matches)) {
            $header_level = substr_count($line, '#');
            $header_text = $matches[1];

            $pdf->Ln(10);

            switch ($header_level) {
                case 1:
                    $pdf->SetFont('helvetica', 'B', 18);
                    break;
                case 2:
                    $pdf->SetFont('helvetica', 'B', 16);
                    break;
                case 3:
                    $pdf->SetFont('helvetica', 'B', 14);
                    break;
                default:
                    $pdf->SetFont('helvetica', 'B', 12);
                    break;
            }

            $pdf->Cell(0, 10, $header_text, 0, 1, 'L');
            $pdf->Ln(5);
            $pdf->SetFont('helvetica', '', 12);
            continue;
        }

        // Check for bullet points
        if (preg_match('/^[\*\-\+]\s*(.+)/', $line, $matches)) {
            $bullet_text = $matches[1];
            $pdf->Cell(10, 8, '•', 0, 0, 'C');
            $pdf->MultiCell(0, 8, $bullet_text, 0, 'L');
            continue;
        }

        // Check for numbered lists
        if (preg_match('/^\d+\.\s*(.+)/', $line, $matches)) {
            $list_text = $matches[1];
            $number = substr($line, 0, strpos($line, '.') + 1);
            $pdf->Cell(15, 8, $number, 0, 0, 'L');
            $pdf->MultiCell(0, 8, $list_text, 0, 'L');
            continue;
        }

        // Regular paragraph
        $pdf->MultiCell(0, 8, $line, 0, 'L');
        $pdf->Ln(3);
    }
}

/**
 * Generate export filename
 */
function businesscraft_ai_generate_export_filename($template, $format = 'pdf') {
    $user = wp_get_current_user();
    $date = date('Y-m-d');

    // Clean template name for filename
    $clean_name = sanitize_file_name($template->template_name);
    $clean_name = preg_replace('/[^a-zA-Z0-9\-_]/', '-', $clean_name);
    $clean_name = preg_replace('/-+/', '-', $clean_name);
    $clean_name = trim($clean_name, '-');

    if (empty($clean_name)) {
        $clean_name = ucwords(str_replace('-', '-', $template->template_type));
    }

    $filename = sprintf(
        '%s-%s-%s-%d.%s',
        $clean_name,
        $user->user_login,
        $date,
        $template->id,
        $format
    );

    return $filename;
}

/**
 * Record export in history
 */
function businesscraft_ai_record_export_history($template_id, $format, $filename, $file_path, $file_size, $include_branding) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_export_history';
    $user_id = get_current_user_id();

    // Set expiration date (30 days from now)
    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));

    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'template_id' => $template_id,
            'export_format' => $format,
            'file_name' => $filename,
            'file_path' => $file_path,
            'file_size' => $file_size,
            'include_branding' => $include_branding ? 1 : 0,
            'export_status' => 'completed',
            'expires_at' => $expires_at
        ),
        array('%d', '%d', '%s', '%s', '%s', '%d', '%d', '%s', '%s')
    );

    return $result ? $wpdb->insert_id : false;
}

/**
 * Handle download requests
 */
function businesscraft_ai_handle_download_export() {
    $export_id = isset($_GET['export_id']) ? absint($_GET['export_id']) : 0;
    $nonce = isset($_GET['nonce']) ? sanitize_text_field($_GET['nonce']) : '';

    if (!$export_id || !wp_verify_nonce($nonce, 'download_export_' . $export_id)) {
        wp_die(__('Invalid download request', 'businesscraft-ai'));
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_export_history';
    $user_id = get_current_user_id();

    $export = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name}
         WHERE id = %d AND user_id = %d AND export_status = 'completed'",
        $export_id,
        $user_id
    ));

    if (!$export) {
        wp_die(__('Export not found or access denied', 'businesscraft-ai'));
    }

    // Check if file exists
    if (!file_exists($export->file_path)) {
        wp_die(__('Export file not found', 'businesscraft-ai'));
    }

    // Check if expired
    if ($export->expires_at && strtotime($export->expires_at) < time()) {
        wp_die(__('Export has expired', 'businesscraft-ai'));
    }

    // Update download count
    $wpdb->update(
        $table_name,
        array('download_count' => $export->download_count + 1),
        array('id' => $export_id),
        array('%d'),
        array('%d')
    );

    // Set headers for download
    $mime_type = $export->export_format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: attachment; filename="' . $export->file_name . '"');
    header('Content-Length: ' . $export->file_size);
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');

    // Output file
    readfile($export->file_path);
    exit;
}
add_action('wp_ajax_download_export', 'businesscraft_ai_handle_download_export');

/**
 * Clean up expired exports (run daily)
 */
function businesscraft_ai_cleanup_expired_exports() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_export_history';

    // Get expired exports
    $expired_exports = $wpdb->get_results(
        "SELECT * FROM {$table_name}
         WHERE expires_at IS NOT NULL AND expires_at < NOW()"
    );

    foreach ($expired_exports as $export) {
        // Delete file if it exists
        if (file_exists($export->file_path)) {
            unlink($export->file_path);
        }

        // Delete database record
        $wpdb->delete(
            $table_name,
            array('id' => $export->id),
            array('%d')
        );
    }
}

// Schedule daily cleanup
if (!wp_next_scheduled('businesscraft_ai_cleanup_exports')) {
    wp_schedule_event(time(), 'daily', 'businesscraft_ai_cleanup_exports');
}
add_action('businesscraft_ai_cleanup_exports', 'businesscraft_ai_cleanup_expired_exports');

/**
 * Generate HTML PDF document (alternative when TCPDF not available)
 */
function businesscraft_ai_generate_html_pdf_document($template, $include_branding = true) {
    // Generate HTML content
    $html_content = businesscraft_ai_generate_html_content($template, $include_branding);

    // Generate filename
    $filename = businesscraft_ai_generate_export_filename($template, 'html');

    // Save HTML to uploads directory
    $upload_dir = wp_upload_dir();
    $pdf_export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    $file_path = $pdf_export_dir . '/' . $filename;

    // Save HTML file
    file_put_contents($file_path, $html_content);

    // Record export in history
    $export_id = businesscraft_ai_record_export_history($template->id, 'html', $filename, $file_path, filesize($file_path), $include_branding);

    // Generate print URL for PDF conversion
    $print_url = add_query_arg(array(
        'action' => 'print_export',
        'export_id' => $export_id,
        'nonce' => wp_create_nonce('print_export_' . $export_id)
    ), admin_url('admin-ajax.php'));

    return array(
        'success' => true,
        'message' => __('Document generated successfully', 'businesscraft-ai'),
        'filename' => $filename,
        'print_url' => $print_url,
        'export_id' => $export_id,
        'file_size' => filesize($file_path),
        'format' => 'html',
        'instructions' => __('Click "Open Print View" then use your browser\'s "Print to PDF" feature to save as PDF.', 'businesscraft-ai')
    );
}

/**
 * Generate HTML content for template
 */
function businesscraft_ai_generate_html_content($template, $include_branding = true) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="<?php echo get_locale(); ?>">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo esc_html($template->template_name); ?></title>
        <style>
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
            }

            body {
                font-family: 'Arial', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: white;
            }

            .header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 20px;
                border-bottom: 2px solid #3D4E81;
            }

            .header h1 {
                color: #3D4E81;
                font-size: 2.5em;
                margin-bottom: 10px;
            }

            .header .subtitle {
                color: #666;
                font-size: 1.2em;
                margin-bottom: 20px;
            }

            .meta-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 30px;
            }

            .meta-info h3 {
                margin-top: 0;
                color: #3D4E81;
            }

            .content {
                line-height: 1.8;
            }

            .content h1, .content h2, .content h3, .content h4 {
                color: #3D4E81;
                margin-top: 30px;
                margin-bottom: 15px;
            }

            .content h1 { font-size: 1.8em; }
            .content h2 { font-size: 1.5em; }
            .content h3 { font-size: 1.3em; }
            .content h4 { font-size: 1.1em; }

            .content ul, .content ol {
                margin-left: 20px;
                margin-bottom: 15px;
            }

            .content li {
                margin-bottom: 8px;
            }

            .footer {
                margin-top: 50px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                text-align: center;
                color: #666;
                font-size: 0.9em;
            }

            .print-instructions {
                background: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;
                text-align: center;
            }

            .print-button {
                background: #3D4E81;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 16px;
                cursor: pointer;
                margin: 10px;
            }

            .print-button:hover {
                background: #2a3660;
            }
        </style>
        <script>
            function printDocument() {
                window.print();
            }

            function downloadPDF() {
                // Modern browsers support this
                if (window.chrome) {
                    window.print();
                } else {
                    alert('Please use Ctrl+P (Windows) or Cmd+P (Mac) to print this document as PDF.');
                }
            }
        </script>
    </head>
    <body>
        <div class="print-instructions no-print">
            <h3><?php _e('📄 Ready to Save as PDF', 'businesscraft-ai'); ?></h3>
            <p><?php _e('Click the button below, then choose "Save as PDF" in the print dialog.', 'businesscraft-ai'); ?></p>
            <button class="print-button" onclick="printDocument()">
                🖨️ <?php _e('Print to PDF', 'businesscraft-ai'); ?>
            </button>
        </div>

        <?php if ($include_branding): ?>
        <div class="header">
            <h1><?php echo esc_html($template->template_name); ?></h1>
            <div class="subtitle">
                <?php printf(__('Professional %s - Generated by BusinessCraft AI', 'businesscraft-ai'),
                            ucwords(str_replace('-', ' ', $template->template_type))); ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="meta-info">
            <h3><?php _e('Business Information', 'businesscraft-ai'); ?></h3>
            <p><strong><?php _e('Business Idea:', 'businesscraft-ai'); ?></strong> <?php echo esc_html($template->business_idea); ?></p>
            <p><strong><?php _e('Industry:', 'businesscraft-ai'); ?></strong> <?php echo esc_html($template->industry_sector); ?></p>
            <p><strong><?php _e('Target Market:', 'businesscraft-ai'); ?></strong> <?php echo esc_html(chatgabi_get_country_name_from_code($template->target_country)); ?></p>
            <p><strong><?php _e('Business Stage:', 'businesscraft-ai'); ?></strong> <?php echo esc_html(ucwords(str_replace('-', ' ', $template->business_stage))); ?></p>
            <p><strong><?php _e('Generated:', 'businesscraft-ai'); ?></strong> <?php echo date('F j, Y', strtotime($template->created_at)); ?></p>
        </div>

        <div class="content">
            <?php echo businesscraft_ai_format_content_for_html($template->generated_content); ?>
        </div>

        <?php if ($include_branding): ?>
        <div class="footer">
            <p><?php printf(__('Generated by %s - AI-Powered Business Tools for African Entrepreneurs', 'businesscraft-ai'), get_bloginfo('name')); ?></p>
            <p><?php echo home_url(); ?></p>
        </div>
        <?php endif; ?>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

/**
 * Format content for HTML display
 */
function businesscraft_ai_format_content_for_html($content) {
    // Convert markdown-style formatting to HTML
    $content = wp_kses_post($content);

    // Convert headers
    $content = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $content);
    $content = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $content);
    $content = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $content);

    // Convert bullet points
    $content = preg_replace('/^[\*\-\+] (.+)$/m', '<li>$1</li>', $content);

    // Wrap consecutive list items in ul tags
    $content = preg_replace('/(<li>.*<\/li>)/s', '<ul>$1</ul>', $content);

    // Convert line breaks to paragraphs
    $content = wpautop($content);

    return $content;
}

/**
 * Handle print export requests
 */
function businesscraft_ai_handle_print_export() {
    $export_id = isset($_GET['export_id']) ? absint($_GET['export_id']) : 0;
    $nonce = isset($_GET['nonce']) ? sanitize_text_field($_GET['nonce']) : '';

    if (!$export_id || !wp_verify_nonce($nonce, 'print_export_' . $export_id)) {
        wp_die(__('Invalid print request', 'businesscraft-ai'));
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_export_history';
    $user_id = get_current_user_id();

    $export = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$table_name}
         WHERE id = %d AND user_id = %d AND export_status = 'completed'",
        $export_id,
        $user_id
    ));

    if (!$export) {
        wp_die(__('Export not found or access denied', 'businesscraft-ai'));
    }

    // Check if file exists
    if (!file_exists($export->file_path)) {
        wp_die(__('Export file not found', 'businesscraft-ai'));
    }

    // Output HTML content
    header('Content-Type: text/html; charset=UTF-8');
    readfile($export->file_path);
    exit;
}
add_action('wp_ajax_print_export', 'businesscraft_ai_handle_print_export');

/**
 * Generate DOCX document
 */
function businesscraft_ai_generate_docx_document($template, $include_branding = true) {
    // Check if PhpWord is available
    if (!businesscraft_ai_load_phpword()) {
        // Fallback to HTML export with DOCX instructions
        return businesscraft_ai_generate_html_docx_document($template, $include_branding);
    }

    // Create new PhpWord object
    $phpWord = new \PhpOffice\PhpWord\PhpWord();

    // Set document properties
    $properties = $phpWord->getDocInfo();
    $properties->setCreator('BusinessCraft AI');
    $properties->setCompany(get_bloginfo('name'));
    $properties->setTitle($template->template_name);
    $properties->setDescription('Business Document - ' . ucwords(str_replace('-', ' ', $template->template_type)));
    $properties->setCategory('Business Document');
    $properties->setLastModifiedBy(wp_get_current_user()->display_name);
    $properties->setCreated(time());
    $properties->setModified(time());

    // Add section
    $section = $phpWord->addSection(array(
        'marginLeft' => 1440,   // 1 inch
        'marginRight' => 1440,  // 1 inch
        'marginTop' => 1440,    // 1 inch
        'marginBottom' => 1440  // 1 inch
    ));

    // Add header if branding is enabled
    if ($include_branding) {
        businesscraft_ai_add_docx_header($phpWord, $section, $template);
    }

    // Add title page
    businesscraft_ai_add_docx_title_page($section, $template);

    // Add page break
    $section->addPageBreak();

    // Add content based on template type
    switch ($template->template_type) {
        case 'business-plan':
        case 'business_plan':
            businesscraft_ai_add_docx_business_plan_content($section, $template);
            break;
        case 'marketing-strategy':
        case 'marketing_strategy':
            businesscraft_ai_add_docx_marketing_strategy_content($section, $template);
            break;
        case 'financial-forecast':
        case 'financial_forecast':
            businesscraft_ai_add_docx_financial_forecast_content($section, $template);
            break;
        default:
            businesscraft_ai_add_docx_generic_content($section, $template);
            break;
    }

    // Add footer if branding is enabled
    if ($include_branding) {
        businesscraft_ai_add_docx_footer($section, $template);
    }

    // Generate filename
    $filename = businesscraft_ai_generate_export_filename($template, 'docx');

    // Save DOCX to uploads directory
    $upload_dir = wp_upload_dir();
    $pdf_export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    $file_path = $pdf_export_dir . '/' . $filename;

    // Create writer and save
    $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    $writer->save($file_path);

    // Record export in history
    $export_id = businesscraft_ai_record_export_history($template->id, 'docx', $filename, $file_path, filesize($file_path), $include_branding);

    // Generate download URL
    $download_url = add_query_arg(array(
        'action' => 'download_export',
        'export_id' => $export_id,
        'nonce' => wp_create_nonce('download_export_' . $export_id)
    ), admin_url('admin-ajax.php'));

    return array(
        'success' => true,
        'message' => __('DOCX document generated successfully', 'businesscraft-ai'),
        'filename' => $filename,
        'download_url' => $download_url,
        'export_id' => $export_id,
        'file_size' => filesize($file_path),
        'format' => 'docx'
    );
}

/**
 * Load PhpWord library
 */
function businesscraft_ai_load_phpword() {
    // Try to load PhpWord if available
    $phpword_path = get_template_directory() . '/lib/phpword/autoload.php';

    if (file_exists($phpword_path)) {
        require_once $phpword_path;
        return class_exists('\PhpOffice\PhpWord\PhpWord');
    }

    // Check if PhpWord is available via Composer or other means
    if (class_exists('\PhpOffice\PhpWord\PhpWord')) {
        return true;
    }

    return false;
}

/**
 * Add DOCX header
 */
function businesscraft_ai_add_docx_header($phpWord, $section, $template) {
    $header = $section->addHeader();

    // Add header table
    $headerTable = $header->addTable(array(
        'borderSize' => 0,
        'cellMargin' => 80
    ));

    $headerTable->addRow();
    $headerTable->addCell(4000)->addText(
        get_bloginfo('name'),
        array('bold' => true, 'size' => 14, 'color' => '3D4E81')
    );
    $headerTable->addCell(4000)->addText(
        sprintf(__('Professional %s', 'businesscraft-ai'), ucwords(str_replace('-', ' ', $template->template_type))),
        array('size' => 10, 'color' => '666666'),
        array('alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END)
    );
}

/**
 * Add DOCX footer
 */
function businesscraft_ai_add_docx_footer($section, $template) {
    $footer = $section->addFooter();

    $footerTable = $footer->addTable(array(
        'borderSize' => 0,
        'cellMargin' => 80
    ));

    $footerTable->addRow();
    $footerTable->addCell(4000)->addText(
        sprintf(__('Generated by %s', 'businesscraft-ai'), get_bloginfo('name')),
        array('size' => 8, 'color' => '999999')
    );
    $footerTable->addCell(4000)->addText(
        'Page ',
        array('size' => 8, 'color' => '999999'),
        array('alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END)
    );
    $footerTable->getCell(1, 1)->addPreserveText(
        '{PAGE}',
        array('size' => 8, 'color' => '999999'),
        array('alignment' => \PhpOffice\PhpWord\SimpleType\Jc::END)
    );
}

/**
 * Add DOCX title page
 */
function businesscraft_ai_add_docx_title_page($section, $template) {
    // Title
    $section->addText(
        $template->template_name,
        array('bold' => true, 'size' => 24, 'color' => '3D4E81'),
        array('alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceAfter' => 480)
    );

    // Subtitle
    $section->addText(
        sprintf(__('Business Document for %s', 'businesscraft-ai'), $template->business_idea),
        array('size' => 16, 'color' => '666666'),
        array('alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceAfter' => 240)
    );

    // Business information table
    $table = $section->addTable(array(
        'borderSize' => 6,
        'borderColor' => 'E9ECEF',
        'cellMargin' => 80,
        'alignment' => \PhpOffice\PhpWord\SimpleType\JcTable::CENTER
    ));

    $table->addRow();
    $table->addCell(2000)->addText(__('Industry:', 'businesscraft-ai'), array('bold' => true));
    $table->addCell(4000)->addText($template->industry_sector);

    $table->addRow();
    $table->addCell(2000)->addText(__('Target Market:', 'businesscraft-ai'), array('bold' => true));
    $table->addCell(4000)->addText(chatgabi_get_country_name_from_code($template->target_country));

    $table->addRow();
    $table->addCell(2000)->addText(__('Business Stage:', 'businesscraft-ai'), array('bold' => true));
    $table->addCell(4000)->addText(ucwords(str_replace('-', ' ', $template->business_stage)));

    $table->addRow();
    $table->addCell(2000)->addText(__('Generated:', 'businesscraft-ai'), array('bold' => true));
    $table->addCell(4000)->addText(date('F j, Y', strtotime($template->created_at)));
}

/**
 * Add business plan content to DOCX
 */
function businesscraft_ai_add_docx_business_plan_content($section, $template) {
    businesscraft_ai_add_docx_formatted_content($section, $template->generated_content);
}

/**
 * Add marketing strategy content to DOCX
 */
function businesscraft_ai_add_docx_marketing_strategy_content($section, $template) {
    businesscraft_ai_add_docx_formatted_content($section, $template->generated_content);
}

/**
 * Add financial forecast content to DOCX
 */
function businesscraft_ai_add_docx_financial_forecast_content($section, $template) {
    businesscraft_ai_add_docx_formatted_content($section, $template->generated_content);
}

/**
 * Add generic content to DOCX
 */
function businesscraft_ai_add_docx_generic_content($section, $template) {
    businesscraft_ai_add_docx_formatted_content($section, $template->generated_content);
}

/**
 * Add formatted content to DOCX
 */
function businesscraft_ai_add_docx_formatted_content($section, $content) {
    $lines = explode("\n", $content);

    foreach ($lines as $line) {
        $line = trim($line);

        if (empty($line)) {
            $section->addTextBreak();
            continue;
        }

        // Check for headers (lines starting with #)
        if (preg_match('/^#+\s*(.+)/', $line, $matches)) {
            $header_level = substr_count($line, '#');
            $header_text = $matches[1];

            $section->addTextBreak();

            switch ($header_level) {
                case 1:
                    $section->addText($header_text, array('bold' => true, 'size' => 18, 'color' => '3D4E81'));
                    break;
                case 2:
                    $section->addText($header_text, array('bold' => true, 'size' => 16, 'color' => '3D4E81'));
                    break;
                case 3:
                    $section->addText($header_text, array('bold' => true, 'size' => 14, 'color' => '3D4E81'));
                    break;
                default:
                    $section->addText($header_text, array('bold' => true, 'size' => 12, 'color' => '3D4E81'));
                    break;
            }

            $section->addTextBreak();
            continue;
        }

        // Check for bullet points
        if (preg_match('/^[\*\-\+]\s*(.+)/', $line, $matches)) {
            $bullet_text = $matches[1];
            $section->addListItem($bullet_text, 0, null, array('listType' => \PhpOffice\PhpWord\Style\ListItem::TYPE_BULLET_FILLED));
            continue;
        }

        // Check for numbered lists
        if (preg_match('/^\d+\.\s*(.+)/', $line, $matches)) {
            $list_text = $matches[1];
            $section->addListItem($list_text, 0, null, array('listType' => \PhpOffice\PhpWord\Style\ListItem::TYPE_NUMBER));
            continue;
        }

        // Regular paragraph
        $section->addText($line, array('size' => 11), array('spaceAfter' => 120));
    }
}

/**
 * Generate HTML DOCX document (fallback when PhpWord not available)
 */
function businesscraft_ai_generate_html_docx_document($template, $include_branding = true) {
    // Generate HTML content optimized for Word conversion
    $html_content = businesscraft_ai_generate_docx_html_content($template, $include_branding);

    // Generate filename
    $filename = businesscraft_ai_generate_export_filename($template, 'html');

    // Save HTML to uploads directory
    $upload_dir = wp_upload_dir();
    $pdf_export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    $file_path = $pdf_export_dir . '/' . $filename;

    // Save HTML file
    file_put_contents($file_path, $html_content);

    // Record export in history
    $export_id = businesscraft_ai_record_export_history($template->id, 'html-docx', $filename, $file_path, filesize($file_path), $include_branding);

    // Generate print URL for DOCX conversion
    $print_url = add_query_arg(array(
        'action' => 'print_export',
        'export_id' => $export_id,
        'nonce' => wp_create_nonce('print_export_' . $export_id)
    ), admin_url('admin-ajax.php'));

    return array(
        'success' => true,
        'message' => __('Document generated successfully', 'businesscraft-ai'),
        'filename' => $filename,
        'print_url' => $print_url,
        'export_id' => $export_id,
        'file_size' => filesize($file_path),
        'format' => 'html-docx',
        'instructions' => __('Click "Open Document View" then save as Word document from your browser.', 'businesscraft-ai')
    );
}

/**
 * Generate HTML content optimized for Word conversion
 */
function businesscraft_ai_generate_docx_html_content($template, $include_branding = true) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="<?php echo get_locale(); ?>">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo esc_html($template->template_name); ?></title>
        <style>
            @page {
                margin: 1in;
                size: letter;
            }

            body {
                font-family: 'Calibri', 'Arial', sans-serif;
                line-height: 1.6;
                color: #333;
                font-size: 11pt;
                margin: 0;
                padding: 0;
            }

            .document-header {
                text-align: center;
                margin-bottom: 2em;
                padding-bottom: 1em;
                border-bottom: 2px solid #3D4E81;
            }

            .document-title {
                color: #3D4E81;
                font-size: 24pt;
                font-weight: bold;
                margin-bottom: 0.5em;
            }

            .document-subtitle {
                color: #666;
                font-size: 16pt;
                margin-bottom: 1em;
            }

            .business-info {
                background: #f8f9fa;
                padding: 1em;
                border-radius: 8px;
                margin: 1.5em 0;
                border: 1px solid #e9ecef;
            }

            .business-info h3 {
                margin-top: 0;
                color: #3D4E81;
                font-size: 14pt;
            }

            .info-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 0.5em;
            }

            .info-table td {
                padding: 0.5em;
                border-bottom: 1px solid #e9ecef;
            }

            .info-table td:first-child {
                font-weight: bold;
                width: 30%;
            }

            .content {
                line-height: 1.8;
                margin-top: 2em;
            }

            .content h1, .content h2, .content h3, .content h4 {
                color: #3D4E81;
                margin-top: 1.5em;
                margin-bottom: 0.75em;
                page-break-after: avoid;
            }

            .content h1 { font-size: 18pt; }
            .content h2 { font-size: 16pt; }
            .content h3 { font-size: 14pt; }
            .content h4 { font-size: 12pt; }

            .content ul, .content ol {
                margin-left: 1em;
                margin-bottom: 1em;
            }

            .content li {
                margin-bottom: 0.5em;
            }

            .content p {
                margin-bottom: 1em;
                text-align: justify;
            }

            .document-footer {
                margin-top: 3em;
                padding-top: 1em;
                border-top: 1px solid #ddd;
                text-align: center;
                color: #666;
                font-size: 9pt;
            }

            .conversion-instructions {
                background: #e3f2fd;
                border: 2px solid #2196f3;
                border-radius: 8px;
                padding: 1em;
                margin-bottom: 2em;
                text-align: center;
                page-break-inside: avoid;
            }

            .conversion-button {
                background: #3D4E81;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14pt;
                cursor: pointer;
                margin: 10px;
                text-decoration: none;
                display: inline-block;
            }

            @media print {
                .conversion-instructions { display: none; }
            }
        </style>
        <script>
            function saveAsWord() {
                // Modern browsers support this
                if (window.chrome) {
                    document.execCommand('SaveAs', true, '<?php echo esc_js($template->template_name); ?>.doc');
                } else {
                    alert('Please use Ctrl+S (Windows) or Cmd+S (Mac) to save this document as a Word file.');
                }
            }
        </script>
    </head>
    <body>
        <div class="conversion-instructions">
            <h3><?php _e('📝 Ready to Save as Word Document', 'businesscraft-ai'); ?></h3>
            <p><?php _e('Click the button below, then save as Word document (.doc or .docx) from your browser.', 'businesscraft-ai'); ?></p>
            <button class="conversion-button" onclick="saveAsWord()">
                💾 <?php _e('Save as Word Document', 'businesscraft-ai'); ?>
            </button>
        </div>

        <?php if ($include_branding): ?>
        <div class="document-header">
            <div class="document-title"><?php echo esc_html($template->template_name); ?></div>
            <div class="document-subtitle">
                <?php printf(__('Professional %s - Generated by BusinessCraft AI', 'businesscraft-ai'),
                            ucwords(str_replace('-', ' ', $template->template_type))); ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="business-info">
            <h3><?php _e('Business Information', 'businesscraft-ai'); ?></h3>
            <table class="info-table">
                <tr>
                    <td><?php _e('Business Idea:', 'businesscraft-ai'); ?></td>
                    <td><?php echo esc_html($template->business_idea); ?></td>
                </tr>
                <tr>
                    <td><?php _e('Industry:', 'businesscraft-ai'); ?></td>
                    <td><?php echo esc_html($template->industry_sector); ?></td>
                </tr>
                <tr>
                    <td><?php _e('Target Market:', 'businesscraft-ai'); ?></td>
                    <td><?php echo esc_html(chatgabi_get_country_name_from_code($template->target_country)); ?></td>
                </tr>
                <tr>
                    <td><?php _e('Business Stage:', 'businesscraft-ai'); ?></td>
                    <td><?php echo esc_html(ucwords(str_replace('-', ' ', $template->business_stage))); ?></td>
                </tr>
                <tr>
                    <td><?php _e('Generated:', 'businesscraft-ai'); ?></td>
                    <td><?php echo date('F j, Y', strtotime($template->created_at)); ?></td>
                </tr>
            </table>
        </div>

        <div class="content">
            <?php echo businesscraft_ai_format_content_for_html($template->generated_content); ?>
        </div>

        <?php if ($include_branding): ?>
        <div class="document-footer">
            <p><?php printf(__('Generated by %s - AI-Powered Business Tools for African Entrepreneurs', 'businesscraft-ai'), get_bloginfo('name')); ?></p>
            <p><?php echo home_url(); ?></p>
        </div>
        <?php endif; ?>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

/**
 * Email export to user
 */
function businesscraft_ai_email_export($export_result, $email_address, $custom_message = '') {
    // Validate email address
    if (!is_email($email_address)) {
        return array(
            'success' => false,
            'message' => __('Invalid email address', 'businesscraft-ai')
        );
    }

    // Get current user
    $current_user = wp_get_current_user();

    // Prepare email content
    $subject = sprintf(__('[%s] Your Business Document is Ready', 'businesscraft-ai'), get_bloginfo('name'));

    // Email template
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .email-container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #3D4E81, #5753C9); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .document-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3D4E81; }
            .download-button { display: inline-block; background: #3D4E81; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
            .footer { text-align: center; color: #666; font-size: 12px; margin-top: 30px; }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                <h1><?php echo get_bloginfo('name'); ?></h1>
                <p><?php _e('Your Professional Business Document is Ready', 'businesscraft-ai'); ?></p>
            </div>

            <div class="content">
                <h2><?php _e('Hello!', 'businesscraft-ai'); ?></h2>

                <?php if (!empty($custom_message)): ?>
                    <div class="custom-message">
                        <p><strong><?php _e('Personal Message:', 'businesscraft-ai'); ?></strong></p>
                        <p><?php echo nl2br(esc_html($custom_message)); ?></p>
                    </div>
                <?php endif; ?>

                <p><?php _e('Your professional business document has been generated and is ready for download.', 'businesscraft-ai'); ?></p>

                <div class="document-info">
                    <h3><?php _e('Document Details', 'businesscraft-ai'); ?></h3>
                    <ul>
                        <li><strong><?php _e('Filename:', 'businesscraft-ai'); ?></strong> <?php echo esc_html($export_result['filename']); ?></li>
                        <li><strong><?php _e('Format:', 'businesscraft-ai'); ?></strong> <?php echo strtoupper($export_result['format']); ?></li>
                        <li><strong><?php _e('Size:', 'businesscraft-ai'); ?></strong> <?php echo size_format($export_result['file_size']); ?></li>
                        <li><strong><?php _e('Generated:', 'businesscraft-ai'); ?></strong> <?php echo date('F j, Y \a\t g:i A'); ?></li>
                    </ul>
                </div>

                <?php if (isset($export_result['download_url'])): ?>
                    <div style="text-align: center;">
                        <a href="<?php echo esc_url($export_result['download_url']); ?>" class="download-button">
                            📄 <?php _e('Download Your Document', 'businesscraft-ai'); ?>
                        </a>
                    </div>

                    <p><small><?php _e('Note: This download link will expire in 30 days for security reasons.', 'businesscraft-ai'); ?></small></p>
                <?php elseif (isset($export_result['print_url'])): ?>
                    <div style="text-align: center;">
                        <a href="<?php echo esc_url($export_result['print_url']); ?>" class="download-button" target="_blank">
                            🖨️ <?php _e('Open Document View', 'businesscraft-ai'); ?>
                        </a>
                    </div>

                    <p><small><?php echo esc_html($export_result['instructions']); ?></small></p>
                <?php endif; ?>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                    <h3><?php _e('What\'s Next?', 'businesscraft-ai'); ?></h3>
                    <ul>
                        <li><?php _e('Review and customize your document as needed', 'businesscraft-ai'); ?></li>
                        <li><?php _e('Share with investors, partners, or stakeholders', 'businesscraft-ai'); ?></li>
                        <li><?php _e('Use it as a foundation for your business planning', 'businesscraft-ai'); ?></li>
                        <li><a href="<?php echo home_url('/templates'); ?>"><?php _e('Create more business documents', 'businesscraft-ai'); ?></a></li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p><?php printf(__('This email was sent by %s', 'businesscraft-ai'), get_bloginfo('name')); ?></p>
                <p><?php echo home_url(); ?></p>
                <p><?php _e('AI-Powered Business Tools for African Entrepreneurs', 'businesscraft-ai'); ?></p>
            </div>
        </div>
    </body>
    </html>
    <?php
    $email_body = ob_get_clean();

    // Email headers
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        'Reply-To: ' . get_option('admin_email')
    );

    // Send email
    $email_sent = wp_mail($email_address, $subject, $email_body, $headers);

    if ($email_sent) {
        // Log email delivery
        businesscraft_ai_log_email_delivery($export_result['export_id'], $email_address, $current_user->ID);

        return array(
            'success' => true,
            'message' => sprintf(__('Document emailed successfully to %s', 'businesscraft-ai'), $email_address)
        );
    } else {
        return array(
            'success' => false,
            'message' => __('Failed to send email. Please try downloading instead.', 'businesscraft-ai')
        );
    }
}

/**
 * Log email delivery
 */
function businesscraft_ai_log_email_delivery($export_id, $email_address, $user_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_email_deliveries';

    // Create table if it doesn't exist
    businesscraft_ai_create_email_deliveries_table();

    $wpdb->insert(
        $table_name,
        array(
            'export_id' => $export_id,
            'email_address' => $email_address,
            'user_id' => $user_id,
            'sent_at' => current_time('mysql'),
            'status' => 'sent'
        ),
        array('%d', '%s', '%d', '%s', '%s')
    );
}

/**
 * Create email deliveries table
 */
function businesscraft_ai_create_email_deliveries_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_email_deliveries';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        export_id bigint(20) NOT NULL,
        email_address varchar(255) NOT NULL,
        user_id bigint(20) NOT NULL,
        sent_at datetime DEFAULT CURRENT_TIMESTAMP,
        status varchar(20) NOT NULL DEFAULT 'sent',
        PRIMARY KEY (id),
        KEY export_id (export_id),
        KEY user_id (user_id),
        KEY email_address (email_address),
        KEY sent_at (sent_at)
    ) {$charset_collate};";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

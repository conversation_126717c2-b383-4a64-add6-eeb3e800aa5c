/**
 * Offline Queue Manager for ChatGABI PWA
 * 
 * Handles offline request queuing, background sync, and connection monitoring
 * for seamless offline/online experience.
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Offline Queue Manager
    const OfflineQueue = {
        queue: [],
        isOnline: navigator.onLine,
        syncInProgress: false,
        maxRetries: 3,
        retryDelay: 5000,

        /**
         * Initialize offline queue system
         */
        init: function() {
            this.loadQueue();
            this.bindEvents();
            this.startConnectionMonitoring();
            this.schedulePeriodicSync();
            
            console.log('ChatGABI: Offline Queue initialized');
        },

        /**
         * Load queue from localStorage
         */
        loadQueue: function() {
            try {
                const stored = localStorage.getItem('chatgabi_offline_queue');
                this.queue = stored ? JSON.parse(stored) : [];
                console.log(`ChatGABI: Loaded ${this.queue.length} items from offline queue`);
            } catch (error) {
                console.error('ChatGABI: Failed to load offline queue:', error);
                this.queue = [];
            }
        },

        /**
         * Save queue to localStorage
         */
        saveQueue: function() {
            try {
                localStorage.setItem('chatgabi_offline_queue', JSON.stringify(this.queue));
            } catch (error) {
                console.error('ChatGABI: Failed to save offline queue:', error);
            }
        },

        /**
         * Bind event listeners
         */
        bindEvents: function() {
            // Connection events
            window.addEventListener('online', this.handleOnline.bind(this));
            window.addEventListener('offline', this.handleOffline.bind(this));
            
            // Intercept AJAX requests when offline
            this.interceptAjaxRequests();
            
            // Page visibility changes
            document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
            
            // Before page unload
            window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        },

        /**
         * Handle online event
         */
        handleOnline: function() {
            console.log('ChatGABI: Connection restored');
            this.isOnline = true;
            this.updateConnectionStatus();
            this.syncQueue();
        },

        /**
         * Handle offline event
         */
        handleOffline: function() {
            console.log('ChatGABI: Connection lost');
            this.isOnline = false;
            this.updateConnectionStatus();
        },

        /**
         * Update connection status in UI
         */
        updateConnectionStatus: function() {
            const statusElements = document.querySelectorAll('.connection-status');
            const statusText = this.isOnline ? 'Online' : 'Offline';
            const statusClass = this.isOnline ? 'online' : 'offline';
            
            statusElements.forEach(element => {
                element.textContent = statusText;
                element.className = `connection-status ${statusClass}`;
            });
            
            // Show/hide offline indicators
            const offlineIndicators = document.querySelectorAll('.offline-indicator');
            offlineIndicators.forEach(indicator => {
                indicator.style.display = this.isOnline ? 'none' : 'block';
            });
            
            // Update queue count
            this.updateQueueCount();
        },

        /**
         * Update queue count in UI
         */
        updateQueueCount: function() {
            const queueElements = document.querySelectorAll('.queue-count');
            queueElements.forEach(element => {
                element.textContent = this.queue.length;
            });
        },

        /**
         * Intercept AJAX requests
         */
        interceptAjaxRequests: function() {
            const originalAjax = $.ajax;
            const self = this;
            
            $.ajax = function(options) {
                // If offline and this is a POST request to ChatGABI endpoints
                if (!self.isOnline && self.shouldQueue(options)) {
                    return self.queueRequest(options);
                }
                
                // Otherwise, proceed normally
                return originalAjax.call(this, options);
            };
        },

        /**
         * Check if request should be queued
         */
        shouldQueue: function(options) {
            const url = options.url || '';
            const method = (options.type || options.method || 'GET').toUpperCase();
            
            // Queue POST requests to ChatGABI endpoints
            if (method === 'POST') {
                return url.includes('admin-ajax.php') || 
                       url.includes('/wp-json/chatgabi/') ||
                       url.includes('/wp-json/businesscraft-ai/');
            }
            
            return false;
        },

        /**
         * Queue request for later sync
         */
        queueRequest: function(options) {
            const queueItem = {
                id: this.generateId(),
                url: options.url,
                method: options.type || options.method || 'POST',
                data: options.data || {},
                headers: options.headers || {},
                timestamp: Date.now(),
                retries: 0,
                maxRetries: this.maxRetries
            };
            
            this.queue.push(queueItem);
            this.saveQueue();
            this.updateQueueCount();
            
            console.log('ChatGABI: Request queued for sync:', queueItem.id);
            
            // Return a promise that resolves with queued response
            return $.Deferred().resolve({
                success: false,
                data: {
                    message: 'Request queued for when online',
                    queued: true,
                    queueId: queueItem.id
                }
            }).promise();
        },

        /**
         * Generate unique ID
         */
        generateId: function() {
            return 'queue_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },

        /**
         * Sync queue with server
         */
        syncQueue: function() {
            if (!this.isOnline || this.syncInProgress || this.queue.length === 0) {
                return;
            }
            
            console.log(`ChatGABI: Starting sync of ${this.queue.length} queued items`);
            this.syncInProgress = true;
            
            // Process queue items one by one
            this.processNextQueueItem();
        },

        /**
         * Process next item in queue
         */
        processNextQueueItem: function() {
            if (this.queue.length === 0) {
                this.syncInProgress = false;
                console.log('ChatGABI: Queue sync completed');
                return;
            }
            
            const item = this.queue[0];
            
            console.log('ChatGABI: Processing queue item:', item.id);
            
            $.ajax({
                url: item.url,
                method: item.method,
                data: item.data,
                headers: item.headers,
                timeout: 10000
            })
            .done((response) => {
                console.log('ChatGABI: Queue item synced successfully:', item.id);
                this.removeQueueItem(item.id);
                this.processNextQueueItem();
            })
            .fail((xhr, status, error) => {
                console.error('ChatGABI: Queue item sync failed:', item.id, error);
                
                item.retries++;
                if (item.retries >= item.maxRetries) {
                    console.log('ChatGABI: Max retries reached, removing item:', item.id);
                    this.removeQueueItem(item.id);
                } else {
                    console.log(`ChatGABI: Retrying item ${item.id} (${item.retries}/${item.maxRetries})`);
                    // Move to end of queue for retry
                    this.queue.push(this.queue.shift());
                    this.saveQueue();
                }
                
                // Continue with next item after delay
                setTimeout(() => {
                    this.processNextQueueItem();
                }, this.retryDelay);
            });
        },

        /**
         * Remove item from queue
         */
        removeQueueItem: function(itemId) {
            this.queue = this.queue.filter(item => item.id !== itemId);
            this.saveQueue();
            this.updateQueueCount();
        },

        /**
         * Clear entire queue
         */
        clearQueue: function() {
            this.queue = [];
            this.saveQueue();
            this.updateQueueCount();
            console.log('ChatGABI: Queue cleared');
        },

        /**
         * Start connection monitoring
         */
        startConnectionMonitoring: function() {
            // Initial status update
            this.updateConnectionStatus();
            
            // Periodic connection check
            setInterval(() => {
                this.checkConnection();
            }, 30000); // Check every 30 seconds
        },

        /**
         * Check connection status
         */
        checkConnection: function() {
            // Simple ping to check actual connectivity
            fetch('/wp-json/chatgabi/v1/health', {
                method: 'GET',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok && !this.isOnline) {
                    this.isOnline = true;
                    this.handleOnline();
                }
            })
            .catch(() => {
                if (this.isOnline) {
                    this.isOnline = false;
                    this.handleOffline();
                }
            });
        },

        /**
         * Schedule periodic sync
         */
        schedulePeriodicSync: function() {
            setInterval(() => {
                if (this.isOnline && this.queue.length > 0) {
                    this.syncQueue();
                }
            }, chatgabiPWA.syncInterval || 30000);
        },

        /**
         * Handle visibility change
         */
        handleVisibilityChange: function() {
            if (!document.hidden && this.isOnline && this.queue.length > 0) {
                // Page became visible and we're online - sync queue
                setTimeout(() => {
                    this.syncQueue();
                }, 1000);
            }
        },

        /**
         * Handle before unload
         */
        handleBeforeUnload: function() {
            // Save any pending queue items
            this.saveQueue();
        },

        /**
         * Add custom request to queue
         */
        addToQueue: function(requestData) {
            const queueItem = {
                id: this.generateId(),
                url: requestData.url,
                method: requestData.method || 'POST',
                data: requestData.data || {},
                headers: requestData.headers || {},
                timestamp: Date.now(),
                retries: 0,
                maxRetries: requestData.maxRetries || this.maxRetries
            };
            
            this.queue.push(queueItem);
            this.saveQueue();
            this.updateQueueCount();
            
            return queueItem.id;
        },

        /**
         * Get queue status
         */
        getStatus: function() {
            return {
                isOnline: this.isOnline,
                queueLength: this.queue.length,
                syncInProgress: this.syncInProgress,
                oldestItem: this.queue.length > 0 ? this.queue[0].timestamp : null
            };
        },

        /**
         * Show offline notification
         */
        showOfflineNotification: function(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'offline-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-icon">📱</span>
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;
            
            // Add styles if not already present
            if (!document.querySelector('#offline-notification-styles')) {
                const styles = document.createElement('style');
                styles.id = 'offline-notification-styles';
                styles.textContent = `
                    .offline-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #fff3cd;
                        border: 1px solid #ffeaa7;
                        border-radius: 6px;
                        padding: 12px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        z-index: 10000;
                        max-width: 300px;
                    }
                    .notification-content {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    .notification-message {
                        flex: 1;
                        font-size: 14px;
                    }
                    .notification-close {
                        background: none;
                        border: none;
                        font-size: 16px;
                        cursor: pointer;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                    }
                `;
                document.head.appendChild(styles);
            }
            
            document.body.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof chatgabiPWA !== 'undefined') {
            OfflineQueue.init();
        }
    });

    // Expose to global scope
    window.ChatGABI = window.ChatGABI || {};
    window.ChatGABI.OfflineQueue = OfflineQueue;

    // Add offline queue status to existing chat interface
    $(document).on('chatgabi:messageStart', function(event, messageData) {
        if (!OfflineQueue.isOnline) {
            OfflineQueue.showOfflineNotification('Message will be sent when you\'re back online');
        }
    });

    // Handle successful sync
    $(document).on('chatgabi:queueSynced', function(event, syncData) {
        console.log('ChatGABI: Queue sync completed:', syncData);
    });

})(jQuery);

<?php
/**
 * Test Admin Menu Duplication Fix
 * 
 * This script tests the admin menu structure to ensure no duplicates exist.
 *
 * @package ChatGABI
 * @since 1.4.0
 */

// Load WordPress
require_once(dirname(__FILE__) . '/../../../wp-config.php');

echo "🔧 ChatGABI Admin Menu Duplication Fix Test\n";
echo "==========================================\n";
echo "Testing admin menu structure for duplicates and conflicts...\n\n";

// Test 1: Check for duplicate admin_menu hooks
echo "📋 Test 1: Admin Menu Hook Analysis\n";
echo "-----------------------------------\n";

$admin_files = array(
    'inc/admin-dashboard.php',
    'inc/admin-functions.php', 
    'inc/admin-analytics-extended.php',
    'inc/admin-sector-analytics.php',
    'inc/hybrid-scraping-admin.php'
);

$hook_count = 0;
$active_hooks = array();

foreach ($admin_files as $file) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        
        // Count active admin_menu hooks (not commented out)
        preg_match_all('/^(?!\s*\/\/)\s*add_action\s*\(\s*[\'"]admin_menu[\'"].*$/m', $content, $matches);
        $file_hooks = count($matches[0]);
        
        if ($file_hooks > 0) {
            $hook_count += $file_hooks;
            $active_hooks[$file] = $file_hooks;
            echo "✅ {$file}: {$file_hooks} active hook(s)\n";
        } else {
            echo "✅ {$file}: No active hooks (good)\n";
        }
    } else {
        echo "❌ {$file}: File not found\n";
    }
}

echo "\n📊 Hook Summary:\n";
echo "Total active admin_menu hooks: {$hook_count}\n";
if ($hook_count <= 1) {
    echo "✅ PASS: Only one or zero active hooks (no duplicates)\n";
} else {
    echo "❌ FAIL: Multiple active hooks detected - duplicates likely\n";
    foreach ($active_hooks as $file => $count) {
        echo "   - {$file}: {$count} hook(s)\n";
    }
}

// Test 2: Simulate menu registration
echo "\n📋 Test 2: Menu Registration Simulation\n";
echo "--------------------------------------\n";

// Clear any existing menu registrations
global $menu, $submenu;
$menu = array();
$submenu = array();

// Include admin files to load functions
$theme_dir = get_template_directory();
$include_files = array(
    'inc/hybrid-scraping-admin.php',
    'inc/admin-analytics-extended.php',
    'inc/admin-dashboard.php'
);

foreach ($include_files as $file) {
    $file_path = $theme_dir . '/' . $file;
    if (file_exists($file_path)) {
        require_once $file_path;
        echo "✅ Included: {$file}\n";
    } else {
        echo "❌ Missing: {$file}\n";
    }
}

// Test main menu function
if (function_exists('chatgabi_add_admin_menu')) {
    echo "✅ Main menu function exists: chatgabi_add_admin_menu()\n";
    
    // Simulate menu registration
    chatgabi_add_admin_menu();
    
    // Count registered menus
    $main_menu_count = 0;
    $submenu_count = 0;
    
    if (is_array($menu)) {
        foreach ($menu as $menu_item) {
            if (is_array($menu_item) && isset($menu_item[2])) {
                if (strpos($menu_item[2], 'chatgabi') !== false) {
                    $main_menu_count++;
                    echo "✅ Main menu found: {$menu_item[2]} - {$menu_item[0]}\n";
                }
            }
        }
    }
    
    if (is_array($submenu)) {
        foreach ($submenu as $parent => $submenus) {
            if (strpos($parent, 'chatgabi') !== false) {
                $submenu_count += count($submenus);
                echo "✅ Submenus under {$parent}: " . count($submenus) . "\n";
                foreach ($submenus as $submenu_item) {
                    if (is_array($submenu_item) && isset($submenu_item[0], $submenu_item[2])) {
                        echo "   - {$submenu_item[2]}: {$submenu_item[0]}\n";
                    }
                }
            }
        }
    }
    
    echo "\n📊 Menu Registration Summary:\n";
    echo "Main ChatGABI menus: {$main_menu_count}\n";
    echo "ChatGABI submenus: {$submenu_count}\n";
    
    if ($main_menu_count === 1) {
        echo "✅ PASS: Exactly one main ChatGABI menu\n";
    } else {
        echo "❌ FAIL: Expected 1 main menu, found {$main_menu_count}\n";
    }
    
} else {
    echo "❌ Main menu function not found: chatgabi_add_admin_menu()\n";
}

// Test 3: Check for required admin page functions
echo "\n📋 Test 3: Admin Page Function Check\n";
echo "-----------------------------------\n";

$required_functions = array(
    'chatgabi_hybrid_scraping_admin_page' => 'Hybrid Scraping admin page',
    'chatgabi_engagement_analytics_page' => 'Engagement Analytics page',
    'chatgabi_database_management_admin_page' => 'Database Management page',
    'chatgabi_advanced_scraping_admin_page' => 'Advanced Scraping page'
);

$missing_functions = 0;
foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo "✅ {$description}: {$function}()\n";
    } else {
        echo "❌ Missing {$description}: {$function}()\n";
        $missing_functions++;
    }
}

if ($missing_functions === 0) {
    echo "✅ PASS: All required admin page functions exist\n";
} else {
    echo "❌ FAIL: {$missing_functions} admin page function(s) missing\n";
}

// Test 4: Check for function conflicts
echo "\n📋 Test 4: Function Conflict Check\n";
echo "---------------------------------\n";

$all_functions = get_defined_functions()['user'];
$chatgabi_functions = array_filter($all_functions, function($func) {
    return strpos($func, 'chatgabi') !== false || strpos($func, 'businesscraft') !== false;
});

$duplicate_functions = array();
$function_counts = array_count_values($chatgabi_functions);

foreach ($function_counts as $function => $count) {
    if ($count > 1) {
        $duplicate_functions[] = $function;
    }
}

if (empty($duplicate_functions)) {
    echo "✅ PASS: No duplicate function definitions found\n";
} else {
    echo "❌ FAIL: Duplicate function definitions detected:\n";
    foreach ($duplicate_functions as $func) {
        echo "   - {$func}\n";
    }
}

echo "\n📊 Function Summary:\n";
echo "Total ChatGABI/BusinessCraft functions: " . count($chatgabi_functions) . "\n";

// Final Summary
echo "\n🎉 FINAL TEST RESULTS\n";
echo "====================\n";

$total_tests = 4;
$passed_tests = 0;

if ($hook_count <= 1) $passed_tests++;
if ($main_menu_count === 1) $passed_tests++;
if ($missing_functions === 0) $passed_tests++;
if (empty($duplicate_functions)) $passed_tests++;

echo "Tests passed: {$passed_tests}/{$total_tests}\n";

if ($passed_tests === $total_tests) {
    echo "✅ ALL TESTS PASSED! Admin menu duplication issues have been resolved.\n";
    echo "\n🚀 Next Steps:\n";
    echo "1. Clear WordPress cache if using caching plugins\n";
    echo "2. Check WordPress Admin → ChatGABI menu\n";
    echo "3. Verify all submenus appear correctly\n";
    echo "4. Test each admin page functionality\n";
} else {
    echo "❌ SOME TESTS FAILED. Please review the issues above.\n";
    echo "\n🔧 Recommended Actions:\n";
    if ($hook_count > 1) echo "- Remove duplicate admin_menu hooks\n";
    if ($main_menu_count !== 1) echo "- Fix main menu registration\n";
    if ($missing_functions > 0) echo "- Include missing admin page files\n";
    if (!empty($duplicate_functions)) echo "- Resolve function name conflicts\n";
}

echo "\nAdmin menu fix test completed at: " . date('Y-m-d H:i:s') . "\n";
?>

{"templates": {"sop_template": "Standard Operating Procedure: Process: [process name]. Objective: [objective]. Steps: 1. [step 1], 2. [step 2], 3. [step 3]. Quality checks: [checks]. Documentation: [requirements].", "workflow_design": "Workflow Design: Input: [input]. Process steps: [steps]. Output: [output]. Responsible parties: [roles]. Timeline: [duration]. Success metrics: [metrics].", "quality_control": "Quality Control: Standards: [standards]. Inspection points: [points]. Testing procedures: [procedures]. Corrective actions: [actions]. Documentation: [requirements].", "supply_chain": "Supply Chain: Suppliers: [list]. Lead times: [times]. Inventory levels: [levels]. Logistics: [methods]. Risk management: [strategies]."}}
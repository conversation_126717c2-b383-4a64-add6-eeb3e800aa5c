<?php
/**
 * Manual Test Results for Critical Fixes
 * Simple verification of our implementations
 */

// Determine WordPress root directory
$wp_root = dirname(dirname(dirname(__DIR__)));
$wp_load_path = $wp_root . '/wp-load.php';
$wp_config_path = $wp_root . '/wp-config.php';

// Load WordPress with proper error handling
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} elseif (file_exists($wp_config_path)) {
    require_once $wp_config_path;
    require_once $wp_root . '/wp-settings.php';
} else {
    die('Error: Could not find WordPress installation. Please ensure this script is in the correct directory.');
}

// Verify WordPress is loaded
if (!function_exists('get_template_directory')) {
    die('Error: WordPress not properly loaded - required functions not available');
}

echo "=== ChatGABI Critical Fixes Manual Verification ===\n\n";
echo "WordPress Root: {$wp_root}\n";
echo "WordPress Loaded: " . (function_exists('wp_get_version') ? "✅ YES" : "❌ NO") . "\n";
echo "Theme Directory: " . get_template_directory() . "\n\n";

// Test 1: Check if files exist
echo "Test 1: File Existence Check\n";
echo "-----------------------------\n";

$required_files = array(
    'inc/secure-api-key-manager.php' => 'Secure API Key Manager',
    'inc/enhanced-input-validator.php' => 'Enhanced Input Validator',
    'inc/token-optimizer.php' => 'Token Optimizer',
    'inc/rest-api.php' => 'REST API',
    'inc/openai-integration.php' => 'OpenAI Integration'
);

$files_exist = true;
foreach ($required_files as $file => $description) {
    $full_path = get_template_directory() . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ {$description}: EXISTS\n";
    } else {
        echo "❌ {$description}: MISSING\n";
        $files_exist = false;
    }
}

echo "\n";

// Test 2: Check wp-config.php modifications
echo "Test 2: wp-config.php Security Updates\n";
echo "--------------------------------------\n";

$wp_config_content = file_get_contents(ABSPATH . 'wp-config.php');

if (strpos($wp_config_content, 'businesscraft_ai_get_encrypted_api_key') !== false) {
    echo "✅ Secure API key function reference: FOUND\n";
} else {
    echo "❌ Secure API key function reference: NOT FOUND\n";
}

if (strpos($wp_config_content, '$_ENV[') !== false) {
    echo "✅ Environment variable usage: FOUND\n";
} else {
    echo "❌ Environment variable usage: NOT FOUND\n";
}

if (strpos($wp_config_content, 'API_KEY_ROTATION_ENABLED') !== false) {
    echo "✅ API key rotation config: FOUND\n";
} else {
    echo "❌ API key rotation config: NOT FOUND\n";
}

echo "\n";

// Test 3: Check token limits in code
echo "Test 3: Token Limit Compliance Check\n";
echo "------------------------------------\n";

$token_optimizer_content = file_get_contents(get_template_directory() . '/inc/token-optimizer.php');

$token_400_count = substr_count($token_optimizer_content, 'optimal_response\' => 400');
echo "✅ 400-token limits found: {$token_400_count} instances\n";

if ($token_400_count >= 3) {
    echo "✅ All models have 400-token limits: PASS\n";
} else {
    echo "❌ Missing 400-token limits: FAIL\n";
}

echo "\n";

// Test 4: Check input validation patterns
echo "Test 4: Input Validation Security Patterns\n";
echo "------------------------------------------\n";

$validator_content = file_get_contents(get_template_directory() . '/inc/enhanced-input-validator.php');

$security_patterns = array(
    'prompt_injection' => 'ignore\s+previous\s+instructions',
    'code_injection' => '<script',
    'sql_injection' => 'union\s+select',
    'sensitive_data' => 'api[_-]?key'
);

foreach ($security_patterns as $pattern_type => $pattern) {
    if (strpos($validator_content, $pattern) !== false) {
        echo "✅ {$pattern_type} detection: IMPLEMENTED\n";
    } else {
        echo "❌ {$pattern_type} detection: MISSING\n";
    }
}

echo "\n";

// Test 5: Check REST API integration
echo "Test 5: REST API Integration Check\n";
echo "----------------------------------\n";

$rest_api_content = file_get_contents(get_template_directory() . '/inc/rest-api.php');

if (strpos($rest_api_content, 'businesscraft_ai_sanitize_ai_input') !== false) {
    echo "✅ Enhanced sanitization callback: INTEGRATED\n";
} else {
    echo "❌ Enhanced sanitization callback: NOT INTEGRATED\n";
}

if (strpos($rest_api_content, 'businesscraft_ai_validate_chat_message') !== false) {
    echo "✅ Enhanced validation callback: INTEGRATED\n";
} else {
    echo "❌ Enhanced validation callback: NOT INTEGRATED\n";
}

if (strpos($rest_api_content, 'enhanced-input-validator.php') !== false) {
    echo "✅ Input validator inclusion: FOUND\n";
} else {
    echo "❌ Input validator inclusion: NOT FOUND\n";
}

echo "\n";

// Test 6: Check OpenAI integration updates
echo "Test 6: OpenAI Integration Security Updates\n";
echo "-------------------------------------------\n";

$openai_content = file_get_contents(get_template_directory() . '/inc/openai-integration.php');

if (strpos($openai_content, 'secure-api-key-manager.php') !== false) {
    echo "✅ Secure API key manager inclusion: FOUND\n";
} else {
    echo "❌ Secure API key manager inclusion: NOT FOUND\n";
}

if (strpos($openai_content, 'min($token_limits[\'optimal_response\'] ?? 400, 400)') !== false) {
    echo "✅ 400-token enforcement: IMPLEMENTED\n";
} else {
    echo "❌ 400-token enforcement: NOT IMPLEMENTED\n";
}

if (strpos($openai_content, 'businesscraft_ai_monitor_api_key_usage') !== false) {
    echo "✅ API key usage monitoring: INTEGRATED\n";
} else {
    echo "❌ API key usage monitoring: NOT INTEGRATED\n";
}

echo "\n";

// Summary
echo "=== IMPLEMENTATION VERIFICATION SUMMARY ===\n";

$checks = array(
    'Files Created' => $files_exist,
    'wp-config Security' => (strpos($wp_config_content, 'businesscraft_ai_get_encrypted_api_key') !== false),
    'Token Compliance' => ($token_400_count >= 3),
    'Input Validation' => (strpos($validator_content, 'prompt_injection') !== false),
    'REST API Integration' => (strpos($rest_api_content, 'businesscraft_ai_validate_chat_message') !== false),
    'OpenAI Security' => (strpos($openai_content, 'secure-api-key-manager.php') !== false)
);

$passed = 0;
$total = count($checks);

foreach ($checks as $check_name => $result) {
    echo ($result ? "✅" : "❌") . " {$check_name}: " . ($result ? "PASS" : "FAIL") . "\n";
    if ($result) $passed++;
}

echo "\n";
echo "Overall Score: {$passed}/{$total} (" . round(($passed / $total) * 100) . "%)\n";

if ($passed === $total) {
    echo "\n🎉 ALL CRITICAL FIXES SUCCESSFULLY IMPLEMENTED!\n";
    echo "ChatGABI is now compliant with audit requirements.\n";
} else {
    echo "\n⚠️ SOME IMPLEMENTATIONS NEED ATTENTION\n";
    echo "Please review failed checks above.\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "1. Test the chat interface with real user input\n";
echo "2. Monitor API key usage and security logs\n";
echo "3. Verify 400-token compliance in production\n";
echo "4. Run database schema fix if needed\n";
echo "5. Test input validation with various attack vectors\n";

echo "\n=== FILES MODIFIED/CREATED ===\n";
echo "Modified:\n";
echo "- wp-config.php (secure API key configuration)\n";
echo "- inc/openai-integration.php (security + token compliance)\n";
echo "- inc/token-optimizer.php (400-token enforcement)\n";
echo "- inc/rest-api.php (enhanced validation)\n";
echo "\nCreated:\n";
echo "- inc/secure-api-key-manager.php (NEW)\n";
echo "- inc/enhanced-input-validator.php (NEW)\n";
echo "- fix-database-schema-critical.php (NEW)\n";
echo "- test-critical-fixes.php (NEW)\n";
echo "- cli-test-critical-fixes.php (NEW)\n";
echo "- manual-test-results.php (NEW)\n";

echo "\n✅ Implementation Assessment Complete!\n";
?>

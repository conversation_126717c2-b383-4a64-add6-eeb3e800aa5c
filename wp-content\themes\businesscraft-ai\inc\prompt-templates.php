<?php
/**
 * ChatGABI Prompt Templates Library System
 *
 * Comprehensive prompt template management for African entrepreneurs
 * with pre-built templates, categorization, and dynamic variables.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize prompt templates system
 */
function chatgabi_init_prompt_templates() {
    // Create templates tables if needed
    chatgabi_create_prompt_templates_tables();

    // Initialize pre-built templates
    chatgabi_initialize_default_templates();

    // Register REST API endpoints - DISABLED to prevent duplicates
    // add_action('rest_api_init', 'chatgabi_register_template_rest_routes');

    // Add admin menu hooks
    add_action('admin_menu', 'chatgabi_add_template_admin_menu', 20);

    // Enqueue scripts and styles
    add_action('wp_enqueue_scripts', 'chatgabi_enqueue_template_assets');
    add_action('admin_enqueue_scripts', 'chatgabi_enqueue_template_admin_assets');
}

/**
 * Create prompt templates database tables
 */
function chatgabi_create_prompt_templates_tables() {
    global $wpdb;

    // Main templates table
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    
    // Categories table
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    
    // Usage tracking table
    $usage_table = $wpdb->prefix . 'chatgabi_template_usage';

    // Use transient cache to avoid repeated table existence checks
    $cache_key = 'chatgabi_templates_tables_exist';
    $tables_exist = get_transient($cache_key);

    if ($tables_exist === false) {
        $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") === $templates_table;
        $categories_exists = $wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table;
        $usage_exists = $wpdb->get_var("SHOW TABLES LIKE '{$usage_table}'") === $usage_table;
        
        $all_exist = $templates_exists && $categories_exists && $usage_exists;
        set_transient($cache_key, $all_exist ? 'yes' : 'no', HOUR_IN_SECONDS);
        $tables_exist = $all_exist ? 'yes' : 'no';
    }

    if ($tables_exist === 'yes') {
        return true;
    }

    $charset_collate = $wpdb->get_charset_collate();

    // Create templates table
    $templates_sql = "CREATE TABLE {$templates_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        title varchar(255) NOT NULL,
        description text,
        prompt_text longtext NOT NULL,
        category_id bigint(20),
        tags text,
        language_code varchar(5) NOT NULL DEFAULT 'en',
        country_code varchar(5),
        sector varchar(100),
        is_public tinyint(1) NOT NULL DEFAULT 0,
        is_featured tinyint(1) NOT NULL DEFAULT 0,
        usage_count int(11) NOT NULL DEFAULT 0,
        rating_average decimal(3,2) NOT NULL DEFAULT 0.00,
        rating_count int(11) NOT NULL DEFAULT 0,
        version int(11) NOT NULL DEFAULT 1,
        parent_template_id bigint(20),
        status varchar(20) NOT NULL DEFAULT 'active',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY category_id (category_id),
        KEY is_public (is_public),
        KEY is_featured (is_featured),
        KEY status (status),
        KEY language_code (language_code),
        KEY country_code (country_code),
        KEY created_at (created_at),
        FULLTEXT KEY search_content (title, description, prompt_text, tags)
    ) $charset_collate;";

    // Create categories table
    $categories_sql = "CREATE TABLE {$categories_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        name varchar(100) NOT NULL,
        slug varchar(100) NOT NULL,
        description text,
        icon varchar(50),
        color varchar(7),
        parent_id bigint(20),
        sort_order int(11) NOT NULL DEFAULT 0,
        status enum('active','inactive') DEFAULT 'active',
        is_system tinyint(1) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug),
        KEY parent_id (parent_id),
        KEY sort_order (sort_order),
        KEY status (status),
        KEY is_system (is_system)
    ) $charset_collate;";

    // Create usage tracking table
    $usage_sql = "CREATE TABLE {$usage_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        template_id bigint(20) NOT NULL,
        user_id bigint(20) NOT NULL,
        session_id varchar(100),
        tokens_used int(11),
        credits_used decimal(10,2),
        rating tinyint(1),
        feedback text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY template_id (template_id),
        KEY user_id (user_id),
        KEY created_at (created_at)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
    $result1 = dbDelta($templates_sql);
    $result2 = dbDelta($categories_sql);
    $result3 = dbDelta($usage_sql);

    // Create default categories
    chatgabi_create_default_template_categories();

    // Update cache
    set_transient($cache_key, 'yes', HOUR_IN_SECONDS);

    return !empty($result1) && !empty($result2) && !empty($result3);
}

/**
 * Create default template categories
 */
function chatgabi_create_default_template_categories() {
    global $wpdb;
    
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    
    // Check if categories already exist
    $existing_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
    if ($existing_count > 0) {
        return;
    }

    $default_categories = array(
        array(
            'name' => 'Business Planning',
            'slug' => 'business-planning',
            'description' => 'Templates for creating business plans, strategies, and roadmaps',
            'icon' => '📋',
            'color' => '#007cba',
            'sort_order' => 1,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'Marketing & Sales',
            'slug' => 'marketing-sales',
            'description' => 'Templates for marketing strategies, sales pitches, and campaigns',
            'icon' => '📈',
            'color' => '#28a745',
            'sort_order' => 2,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'Financial Analysis',
            'slug' => 'financial-analysis',
            'description' => 'Templates for financial forecasts, budgets, and analysis',
            'icon' => '💰',
            'color' => '#ffc107',
            'sort_order' => 3,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'Market Research',
            'slug' => 'market-research',
            'description' => 'Templates for market analysis and competitive research',
            'icon' => '🔍',
            'color' => '#17a2b8',
            'sort_order' => 4,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'Operations',
            'slug' => 'operations',
            'description' => 'Templates for operational planning and process optimization',
            'icon' => '⚙️',
            'color' => '#6c757d',
            'sort_order' => 5,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'Legal & Compliance',
            'slug' => 'legal-compliance',
            'description' => 'Templates for legal documents and compliance requirements',
            'icon' => '⚖️',
            'color' => '#dc3545',
            'sort_order' => 6,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'General Business',
            'slug' => 'general-business',
            'description' => 'General business templates and prompts',
            'icon' => '💼',
            'color' => '#6f42c1',
            'sort_order' => 7,
            'status' => 'active',
            'is_system' => 1
        ),
        array(
            'name' => 'Custom Templates',
            'slug' => 'custom-templates',
            'description' => 'User-created custom templates',
            'icon' => '✨',
            'color' => '#fd7e14',
            'sort_order' => 8,
            'status' => 'active',
            'is_system' => 0
        )
    );

    foreach ($default_categories as $category) {
        $wpdb->insert($categories_table, $category);
    }
}

/**
 * Initialize default prompt templates for African entrepreneurs
 */
function chatgabi_initialize_default_templates() {
    global $wpdb;

    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    // Check if default templates already exist
    $existing_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0");
    if ($existing_count > 0) {
        return;
    }

    // Get category IDs
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    $categories = $wpdb->get_results("SELECT id, slug FROM {$categories_table}");

    $business_planning_id = 1; // Default fallback
    $marketing_sales_id = 2;
    $financial_analysis_id = 3;
    $market_research_id = 4;
    $operations_id = 5;
    $legal_compliance_id = 6;
    $general_business_id = 7;

    // Map categories by slug
    foreach ($categories as $category) {
        switch ($category->slug) {
            case 'business-planning':
                $business_planning_id = $category->id;
                break;
            case 'marketing-sales':
                $marketing_sales_id = $category->id;
                break;
            case 'financial-analysis':
                $financial_analysis_id = $category->id;
                break;
            case 'market-research':
                $market_research_id = $category->id;
                break;
            case 'operations':
                $operations_id = $category->id;
                break;
            case 'legal-compliance':
                $legal_compliance_id = $category->id;
                break;
            case 'general-business':
                $general_business_id = $category->id;
                break;
        }
    }

    $default_templates = array(
        // Business Planning Templates
        array(
            'user_id' => 0, // System template
            'title' => 'Business Idea Analysis for African Markets',
            'description' => 'Comprehensive analysis template for evaluating business ideas in African markets',
            'prompt_text' => 'Analyze this business idea for the {country} market: {business_idea}

Please provide a detailed analysis covering:

1. **Market Opportunity**
   - Market size and growth potential in {country}
   - Target customer demographics and behavior
   - Local market trends and demands

2. **Competitive Landscape**
   - Existing competitors in {country}
   - Market gaps and opportunities
   - Competitive advantages needed

3. **Business Model Viability**
   - Revenue streams suitable for {country}
   - Cost structure considerations
   - Scalability potential

4. **Local Context Factors**
   - Cultural considerations in {country}
   - Regulatory environment
   - Infrastructure requirements
   - Local partnerships needed

5. **Risk Assessment**
   - Market risks specific to {country}
   - Operational challenges
   - Mitigation strategies

6. **Implementation Roadmap**
   - Phase 1: Market entry strategy
   - Phase 2: Growth and expansion
   - Key milestones and timelines

7. **Financial Projections**
   - Initial investment requirements
   - Revenue projections for first 3 years
   - Break-even analysis

Please tailor your analysis specifically to the {country} market context, considering local economic conditions, consumer behavior, and business environment.',
            'category_id' => $business_planning_id,
            'tags' => 'business analysis, market research, african markets, startup planning',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => '',
            'is_public' => 1,
            'is_featured' => 1
        ),

        array(
            'user_id' => 0,
            'title' => 'Funding Proposal for African Startups',
            'description' => 'Template for creating compelling funding proposals tailored to African startup ecosystem',
            'prompt_text' => 'Create a comprehensive funding proposal for my {sector} startup in {country}:

**Business Overview:**
{business_description}

**Funding Request:** {funding_amount}

Please structure the proposal with:

1. **Executive Summary**
   - Business concept and value proposition
   - Market opportunity in {country}
   - Funding requirements and use of funds
   - Expected returns and exit strategy

2. **Problem & Solution**
   - Specific problem in {country}/{sector}
   - Our innovative solution
   - Why this solution works for African markets

3. **Market Analysis**
   - {sector} market size in {country}
   - Target customer segments
   - Market trends and growth drivers
   - Competitive landscape

4. **Business Model**
   - Revenue streams
   - Pricing strategy for {country} market
   - Customer acquisition strategy
   - Scalability plan

5. **Financial Projections**
   - 5-year revenue and profit projections
   - Use of funds breakdown
   - Key financial metrics and assumptions
   - Return on investment for investors

6. **Team & Execution**
   - Founding team credentials
   - Advisory board
   - Implementation timeline
   - Key milestones

7. **Risk Analysis**
   - Market risks in {country}
   - Operational risks
   - Mitigation strategies

8. **Investment Terms**
   - Funding amount and equity offered
   - Investor benefits
   - Exit strategy options

Focus on demonstrating deep understanding of the {country} market and how our solution addresses specific local needs.',
            'category_id' => $business_planning_id,
            'tags' => 'funding, investment, startup, african entrepreneurs, venture capital',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => '',
            'is_public' => 1,
            'is_featured' => 1
        ),

        // Market Research Templates
        array(
            'user_id' => 0,
            'title' => 'Market Entry Strategy for African Countries',
            'description' => 'Strategic framework for entering new African markets',
            'prompt_text' => 'Develop a comprehensive market entry strategy for {country} in the {sector} industry:

**Company Background:**
{company_description}

**Target Market:** {country} - {sector}

Please provide:

1. **Market Assessment**
   - {sector} market size and growth in {country}
   - Key market drivers and trends
   - Regulatory environment and requirements
   - Infrastructure considerations

2. **Entry Strategy Options**
   - Direct investment vs partnerships
   - Joint ventures with local companies
   - Licensing and franchising opportunities
   - Acquisition targets

3. **Competitive Analysis**
   - Major players in {country} {sector}
   - Market share distribution
   - Competitive advantages needed
   - Pricing strategies

4. **Localization Requirements**
   - Product/service adaptations for {country}
   - Cultural considerations
   - Local hiring and talent needs
   - Supply chain localization

5. **Go-to-Market Strategy**
   - Distribution channels in {country}
   - Marketing and promotion strategies
   - Sales approach and customer acquisition
   - Partnership opportunities

6. **Risk Assessment**
   - Political and economic risks
   - Currency and financial risks
   - Operational challenges
   - Mitigation strategies

7. **Implementation Timeline**
   - Phase 1: Market preparation (months 1-6)
   - Phase 2: Market entry (months 7-12)
   - Phase 3: Market expansion (year 2-3)
   - Key milestones and success metrics

8. **Financial Projections**
   - Investment requirements
   - Revenue projections
   - Break-even timeline
   - ROI expectations

Tailor recommendations specifically to {country} market conditions and {sector} dynamics.',
            'category_id' => $market_research_id,
            'tags' => 'market entry, expansion, african markets, strategy',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => '',
            'is_public' => 1,
            'is_featured' => 1
        ),

        array(
            'user_id' => 0,
            'title' => 'Competitive Analysis for African Markets',
            'description' => 'In-depth competitive analysis template for African business environments',
            'prompt_text' => 'Conduct a comprehensive competitive analysis for the {industry} sector in {country}:

**Our Business:**
{business_description}

**Analysis Scope:** {industry} industry in {country}

Please provide:

1. **Market Overview**
   - {industry} market size in {country}
   - Market growth trends and projections
   - Key market segments
   - Market maturity level

2. **Competitor Identification**
   - Direct competitors (same products/services)
   - Indirect competitors (alternative solutions)
   - Potential new entrants
   - Substitute products/services

3. **Competitor Profiles**
   For each major competitor, analyze:
   - Company background and history
   - Market share and position
   - Product/service offerings
   - Pricing strategies
   - Distribution channels
   - Marketing approaches
   - Strengths and weaknesses

4. **Competitive Positioning**
   - Market positioning map
   - Value proposition comparison
   - Differentiation opportunities
   - White space identification

5. **SWOT Analysis**
   - Our strengths vs competitors
   - Our weaknesses vs competitors
   - Market opportunities
   - Competitive threats

6. **Competitive Intelligence**
   - Recent competitor moves
   - Strategic partnerships
   - Investment and expansion plans
   - Technology adoption

7. **Strategic Recommendations**
   - Competitive positioning strategy
   - Differentiation opportunities
   - Pricing strategy recommendations
   - Market entry/expansion tactics

8. **Monitoring Framework**
   - Key metrics to track
   - Information sources
   - Competitive intelligence system
   - Response strategies

Focus on the unique competitive dynamics of the {country} market and {industry} sector.',
            'category_id' => $market_research_id,
            'tags' => 'competitive analysis, market intelligence, strategy, african business',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => '',
            'is_public' => 1,
            'is_featured' => 1
        ),

        // Financial Analysis Templates
        array(
            'user_id' => 0,
            'title' => 'Financial Forecast for African Startups',
            'description' => 'Comprehensive financial forecasting template for African startup environments',
            'prompt_text' => 'Create a detailed 5-year financial forecast for my {sector} startup in {country}:

**Business Details:**
{business_description}

**Funding Stage:** {funding_stage}
**Target Market:** {target_market}

Please provide:

1. **Revenue Projections**
   - Revenue model and streams
   - Unit economics and pricing
   - Customer acquisition projections
   - Market penetration assumptions
   - Seasonal variations in {country}

2. **Cost Structure Analysis**
   - Fixed costs (rent, salaries, utilities)
   - Variable costs (materials, commissions)
   - One-time setup costs
   - {country}-specific cost considerations

3. **Profit & Loss Projections**
   - Monthly P&L for Year 1
   - Quarterly P&L for Years 2-3
   - Annual P&L for Years 4-5
   - Key profitability metrics

4. **Cash Flow Analysis**
   - Operating cash flow projections
   - Investment cash flow requirements
   - Financing cash flow needs
   - Working capital requirements
   - Cash burn rate and runway

5. **Balance Sheet Projections**
   - Asset requirements and growth
   - Liability structure
   - Equity evolution
   - Key balance sheet ratios

6. **Financial Ratios & KPIs**
   - Profitability ratios
   - Liquidity ratios
   - Efficiency ratios
   - Growth metrics
   - Industry benchmarks for {country}

7. **Scenario Analysis**
   - Base case scenario
   - Optimistic scenario (+30% growth)
   - Pessimistic scenario (-30% growth)
   - Sensitivity analysis

8. **Funding Requirements**
   - Total funding needed
   - Funding timeline and milestones
   - Use of funds breakdown
   - Investor returns and exit projections

9. **Risk Factors**
   - Currency risk in {country}
   - Market risks
   - Operational risks
   - Financial risk mitigation

Consider {country}-specific factors like inflation rates, currency stability, local banking systems, and economic conditions.',
            'category_id' => $financial_analysis_id,
            'tags' => 'financial planning, forecasting, startup finance, african markets',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => '',
            'is_public' => 1,
            'is_featured' => 1
        )
    );

    foreach ($default_templates as $template) {
        $wpdb->insert($templates_table, $template);
    }
}

/**
 * Add template admin menu
 */
function chatgabi_add_template_admin_menu() {
    add_submenu_page(
        'chatgabi-main',
        __('Prompt Templates', 'chatgabi'),
        __('Prompt Templates', 'chatgabi'),
        'manage_options',
        'chatgabi-prompt-templates',
        'chatgabi_template_admin_page'
    );
}

/**
 * Enqueue template assets for frontend
 */
function chatgabi_enqueue_template_assets() {
    if (!is_user_logged_in()) {
        return;
    }

    wp_enqueue_script(
        'chatgabi-templates',
        get_template_directory_uri() . '/assets/js/prompt-templates.js',
        array('jquery'),
        '1.0.0',
        true
    );

    wp_enqueue_style(
        'chatgabi-templates',
        get_template_directory_uri() . '/assets/css/prompt-templates.css',
        array(),
        '1.0.0'
    );

    wp_localize_script('chatgabi-templates', 'chatgabiTemplates', array(
        'restUrl' => rest_url('chatgabi/v1/'),
        'nonce' => wp_create_nonce('wp_rest'),
        'userId' => get_current_user_id(),
        'strings' => array(
            'loading' => __('Loading...', 'chatgabi'),
            'error' => __('An error occurred', 'chatgabi'),
            'success' => __('Success!', 'chatgabi'),
            'confirmDelete' => __('Are you sure you want to delete this template?', 'chatgabi'),
            'templateSaved' => __('Template saved successfully', 'chatgabi'),
            'templateDeleted' => __('Template deleted successfully', 'chatgabi'),
            'fillVariables' => __('Please fill in all required variables', 'chatgabi'),
            'templateApplied' => __('Template applied to chat input', 'chatgabi')
        )
    ));
}

/**
 * Enqueue template admin assets
 */
function chatgabi_enqueue_template_admin_assets($hook) {
    if ($hook !== 'chatgabi_page_chatgabi-prompt-templates') {
        return;
    }

    wp_enqueue_script(
        'chatgabi-templates-admin',
        get_template_directory_uri() . '/assets/js/templates-admin.js',
        array('jquery', 'wp-util'),
        '1.0.0',
        true
    );

    wp_enqueue_style(
        'chatgabi-templates-admin',
        get_template_directory_uri() . '/assets/css/templates-admin.css',
        array(),
        '1.0.0'
    );

    wp_localize_script('chatgabi-templates-admin', 'chatgabiTemplatesAdmin', array(
        'restUrl' => rest_url('chatgabi/v1/'),
        'nonce' => wp_create_nonce('wp_rest'),
        'strings' => array(
            'loading' => __('Loading...', 'chatgabi'),
            'error' => __('An error occurred', 'chatgabi'),
            'success' => __('Success!', 'chatgabi'),
            'confirmDelete' => __('Are you sure you want to delete this template?', 'chatgabi'),
            'templateSaved' => __('Template saved successfully', 'chatgabi'),
            'templateDeleted' => __('Template deleted successfully', 'chatgabi')
        )
    ));
}

/**
 * Template admin page
 */
function chatgabi_template_admin_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('Prompt Templates Management', 'chatgabi'); ?></h1>

        <div class="chatgabi-templates-admin">
            <div class="templates-header">
                <div class="templates-stats">
                    <?php
                    global $wpdb;
                    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
                    $total_templates = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE status = 'active'");
                    $public_templates = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE status = 'active' AND is_public = 1");
                    $user_templates = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE status = 'active' AND user_id > 0");
                    ?>
                    <div class="stat-box">
                        <h3><?php echo number_format($total_templates); ?></h3>
                        <p><?php _e('Total Templates', 'chatgabi'); ?></p>
                    </div>
                    <div class="stat-box">
                        <h3><?php echo number_format($public_templates); ?></h3>
                        <p><?php _e('Public Templates', 'chatgabi'); ?></p>
                    </div>
                    <div class="stat-box">
                        <h3><?php echo number_format($user_templates); ?></h3>
                        <p><?php _e('User Templates', 'chatgabi'); ?></p>
                    </div>
                </div>

                <div class="templates-actions">
                    <button type="button" class="button button-primary" onclick="openTemplateModal()">
                        <?php _e('Add New Template', 'chatgabi'); ?>
                    </button>
                    <button type="button" class="button" onclick="initializeDefaultTemplates()">
                        <?php _e('Initialize Default Templates', 'chatgabi'); ?>
                    </button>
                </div>
            </div>

            <div class="templates-filters">
                <select id="category-filter">
                    <option value=""><?php _e('All Categories', 'chatgabi'); ?></option>
                    <?php
                    $categories = chatgabi_get_template_categories(true);
                    foreach ($categories as $category) {
                        echo '<option value="' . esc_attr($category->id) . '">' . esc_html($category->name) . ' (' . $category->template_count . ')</option>';
                    }
                    ?>
                </select>

                <select id="status-filter">
                    <option value="active"><?php _e('Active Templates', 'chatgabi'); ?></option>
                    <option value="inactive"><?php _e('Inactive Templates', 'chatgabi'); ?></option>
                    <option value="draft"><?php _e('Draft Templates', 'chatgabi'); ?></option>
                </select>

                <input type="search" id="template-search" placeholder="<?php _e('Search templates...', 'chatgabi'); ?>">

                <button type="button" class="button" onclick="loadTemplates()">
                    <?php _e('Filter', 'chatgabi'); ?>
                </button>
            </div>

            <div id="templates-list" class="templates-list">
                <!-- Templates will be loaded here via AJAX -->
            </div>

            <div id="templates-pagination" class="templates-pagination">
                <!-- Pagination will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Template Modal -->
    <div id="template-modal" class="template-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title"><?php _e('Add New Template', 'chatgabi'); ?></h2>
                <button type="button" class="modal-close" onclick="closeTemplateModal()">&times;</button>
            </div>

            <form id="template-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="template-title"><?php _e('Template Title', 'chatgabi'); ?></label>
                        <input type="text" id="template-title" name="title" required>
                    </div>

                    <div class="form-group">
                        <label for="template-category"><?php _e('Category', 'chatgabi'); ?></label>
                        <select id="template-category" name="category_id">
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo esc_attr($category->id); ?>">
                                    <?php echo esc_html($category->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="template-description"><?php _e('Description', 'chatgabi'); ?></label>
                    <textarea id="template-description" name="description" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="template-content"><?php _e('Template Content', 'chatgabi'); ?></label>
                    <textarea id="template-content" name="prompt_text" rows="15" required></textarea>
                    <p class="description">
                        <?php _e('Use {variable_name} for dynamic placeholders (e.g., {country}, {industry}, {business_type})', 'chatgabi'); ?>
                    </p>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="template-tags"><?php _e('Tags', 'chatgabi'); ?></label>
                        <input type="text" id="template-tags" name="tags" placeholder="<?php _e('Comma-separated tags', 'chatgabi'); ?>">
                    </div>

                    <div class="form-group">
                        <label for="template-sector"><?php _e('Industry Sector', 'chatgabi'); ?></label>
                        <input type="text" id="template-sector" name="sector">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="template-country"><?php _e('Geographic Focus', 'chatgabi'); ?></label>
                        <select id="template-country" name="country_code">
                            <option value=""><?php _e('All Countries', 'chatgabi'); ?></option>
                            <option value="GH"><?php _e('Ghana', 'chatgabi'); ?></option>
                            <option value="KE"><?php _e('Kenya', 'chatgabi'); ?></option>
                            <option value="NG"><?php _e('Nigeria', 'chatgabi'); ?></option>
                            <option value="ZA"><?php _e('South Africa', 'chatgabi'); ?></option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="template-public" name="is_public" value="1">
                            <?php _e('Make this template public', 'chatgabi'); ?>
                        </label>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="button" onclick="closeTemplateModal()">
                        <?php _e('Cancel', 'chatgabi'); ?>
                    </button>
                    <button type="submit" class="button button-primary">
                        <?php _e('Save Template', 'chatgabi'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <style>
    .chatgabi-templates-admin {
        max-width: 1200px;
    }

    .templates-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 20px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .templates-stats {
        display: flex;
        gap: 20px;
    }

    .stat-box {
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 5px;
        min-width: 100px;
    }

    .stat-box h3 {
        margin: 0;
        font-size: 24px;
        color: #007cba;
    }

    .stat-box p {
        margin: 5px 0 0;
        font-size: 12px;
        color: #666;
    }

    .templates-filters {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        padding: 15px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .templates-filters select,
    .templates-filters input {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }

    .templates-list {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
        min-height: 400px;
    }

    .template-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 100000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: #fff;
        border-radius: 5px;
        width: 90%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #ddd;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #template-form {
        padding: 20px;
    }

    .form-row {
        display: flex;
        gap: 20px;
    }

    .form-group {
        flex: 1;
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }

    .modal-footer {
        padding: 20px;
        border-top: 1px solid #ddd;
        text-align: right;
    }

    .modal-footer .button {
        margin-left: 10px;
    }
    </style>

    <script>
    function openTemplateModal(templateId = null) {
        document.getElementById('template-modal').style.display = 'flex';
    }

    function closeTemplateModal() {
        document.getElementById('template-modal').style.display = 'none';
        document.getElementById('template-form').reset();
    }

    function loadTemplates() {
        // This will be implemented in the admin JS file
        console.log('Loading templates...');
    }

    function initializeDefaultTemplates() {
        if (confirm('<?php _e('This will create default templates. Continue?', 'chatgabi'); ?>')) {
            // AJAX call to initialize templates
            console.log('Initializing default templates...');
        }
    }
    </script>
    <?php
}

/**
 * Get template categories
 * Enhanced version with backward compatibility and fallback support
 *
 * @param bool $include_counts Whether to include template counts
 * @param bool $return_arrays Whether to return arrays instead of objects (for backward compatibility)
 * @return array Template categories
 */
function chatgabi_get_template_categories($include_counts = false, $return_arrays = false) {
    global $wpdb;

    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    // Check if categories table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$categories_table'") == $categories_table;

    if ($table_exists) {
        // Check if status column exists in the table
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$categories_table}");
        $has_status_column = false;
        foreach ($columns as $column) {
            if ($column->Field === 'status') {
                $has_status_column = true;
                break;
            }
        }

        // Get categories from database
        if ($include_counts) {
            $sql = "SELECT c.*, COUNT(t.id) as template_count
                    FROM {$categories_table} c
                    LEFT JOIN {$templates_table} t ON c.id = t.category_id AND t.status = 'active'";
            if ($has_status_column) {
                $sql .= " WHERE c.status = 'active'";
            }
            $sql .= " GROUP BY c.id ORDER BY c.sort_order ASC";
        } else {
            $sql = "SELECT * FROM {$categories_table}";
            if ($has_status_column) {
                $sql .= " WHERE status = 'active'";
            }
            $sql .= " ORDER BY sort_order ASC";
        }

        $categories = $wpdb->get_results($sql, $return_arrays ? ARRAY_A : OBJECT);

        if (!empty($categories)) {
            return $categories;
        }
    }

    // Fallback to default categories if table doesn't exist or is empty
    if (function_exists('chatgabi_get_default_template_categories')) {
        $default_categories = chatgabi_get_default_template_categories();

        if ($include_counts) {
            // Add template_count = 0 to default categories
            foreach ($default_categories as &$category) {
                $category['template_count'] = 0;
            }
        }

        if (!$return_arrays) {
            // Convert arrays to objects for consistency
            $default_categories = array_map(function($category) {
                return (object) $category;
            }, $default_categories);
        }

        return $default_categories;
    }

    // Ultimate fallback - minimal categories
    $minimal_categories = array(
        array(
            'id' => 1,
            'name' => 'Business Plans',
            'slug' => 'business-plans',
            'description' => 'Business planning templates',
            'icon' => '📋',
            'color' => '#007cba',
            'sort_order' => 1,
            'status' => 'active'
        ),
        array(
            'id' => 2,
            'name' => 'Marketing Strategies',
            'slug' => 'marketing-strategies',
            'description' => 'Marketing strategy templates',
            'icon' => '📈',
            'color' => '#28a745',
            'sort_order' => 2,
            'status' => 'active'
        ),
        array(
            'id' => 3,
            'name' => 'Financial Forecasts',
            'slug' => 'financial-forecasts',
            'description' => 'Financial planning templates',
            'icon' => '💰',
            'color' => '#ffc107',
            'sort_order' => 3,
            'status' => 'active'
        )
    );

    if ($include_counts) {
        foreach ($minimal_categories as &$category) {
            $category['template_count'] = 0;
        }
    }

    if (!$return_arrays) {
        $minimal_categories = array_map(function($category) {
            return (object) $category;
        }, $minimal_categories);
    }

    return $minimal_categories;
}

/**
 * Save prompt template
 */
function chatgabi_save_prompt_template($data) {
    global $wpdb;
    
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    
    // Validate required fields
    if (empty($data['title']) || empty($data['prompt_text'])) {
        return new WP_Error('missing_data', 'Title and prompt text are required');
    }
    
    // Sanitize data
    $template_data = array(
        'user_id' => intval($data['user_id'] ?? get_current_user_id()),
        'title' => sanitize_text_field($data['title'] ?? ''),
        'description' => sanitize_textarea_field($data['description'] ?? ''),
        'prompt_content' => wp_kses_post($data['prompt_content'] ?? ''),
        'category_id' => intval($data['category_id'] ?? 0),
        'tags' => sanitize_text_field($data['tags'] ?? ''),
        'language_code' => sanitize_text_field($data['language_code'] ?? 'en'),
        'country_code' => sanitize_text_field($data['country_code'] ?? ''),
        'sector' => sanitize_text_field($data['sector'] ?? ''),
        'is_public' => intval($data['is_public'] ?? 0),
        'parent_template_id' => intval($data['parent_template_id'] ?? 0)
    );
    
    // Handle update vs insert
    if (!empty($data['id'])) {
        $template_id = intval($data['id']);
        
        // Check ownership for updates
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT user_id FROM {$templates_table} WHERE id = %d",
            $template_id
        ));
        
        if (!$existing || ($existing->user_id != $template_data['user_id'] && !current_user_can('manage_options'))) {
            return new WP_Error('permission_denied', 'You do not have permission to edit this template');
        }
        
        // Increment version for updates
        $wpdb->query($wpdb->prepare(
            "UPDATE {$templates_table} SET version = version + 1 WHERE id = %d",
            $template_id
        ));
        
        $result = $wpdb->update($templates_table, $template_data, array('id' => $template_id));
        
        if ($result !== false) {
            return $template_id;
        }
    } else {
        $result = $wpdb->insert($templates_table, $template_data);
        
        if ($result !== false) {
            return $wpdb->insert_id;
        }
    }
    
    return new WP_Error('save_failed', 'Failed to save template');
}

/**
 * Get user's prompt templates
 */
function chatgabi_get_user_templates($user_id, $args = array()) {
    global $wpdb;
    
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
    
    $defaults = array(
        'category_id' => 0,
        'status' => 'active',
        'limit' => 20,
        'offset' => 0,
        'orderby' => 'updated_at',
        'order' => 'DESC',
        'search' => ''
    );
    
    $args = wp_parse_args($args, $defaults);
    
    $where_conditions = array("t.user_id = %d", "t.status = %s");
    $where_values = array($user_id, $args['status']);
    
    if ($args['category_id']) {
        $where_conditions[] = "t.category_id = %d";
        $where_values[] = $args['category_id'];
    }
    
    if (!empty($args['search'])) {
        $where_conditions[] = "MATCH(t.title, t.description, t.prompt_content, t.tags) AGAINST(%s IN NATURAL LANGUAGE MODE)";
        $where_values[] = $args['search'];
    }
    
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    $sql = "SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
            FROM {$templates_table} t
            LEFT JOIN {$categories_table} c ON t.category_id = c.id
            {$where_clause}
            ORDER BY t.{$args['orderby']} {$args['order']}
            LIMIT %d OFFSET %d";
    
    $where_values[] = $args['limit'];
    $where_values[] = $args['offset'];
    
    return $wpdb->get_results($wpdb->prepare($sql, $where_values));
}

/**
 * Register REST API routes for templates
 */
function chatgabi_register_template_rest_routes() {
    // Templates endpoints
    register_rest_route('chatgabi/v1', '/templates', array(
        array(
            'methods' => 'GET',
            'callback' => 'chatgabi_rest_get_templates',
            'permission_callback' => 'chatgabi_rest_check_auth',
            'args' => array(
                'category' => array('type' => 'integer'),
                'search' => array('type' => 'string'),
                'limit' => array('type' => 'integer', 'default' => 20),
                'offset' => array('type' => 'integer', 'default' => 0),
                'public' => array('type' => 'boolean', 'default' => false)
            )
        ),
        array(
            'methods' => 'POST',
            'callback' => 'chatgabi_rest_create_template',
            'permission_callback' => 'chatgabi_rest_check_auth',
            'args' => array(
                'title' => array('type' => 'string', 'required' => true),
                'description' => array('type' => 'string'),
                'prompt_content' => array('type' => 'string', 'required' => true),
                'category_id' => array('type' => 'integer'),
                'tags' => array('type' => 'string'),
                'language_code' => array('type' => 'string', 'default' => 'en'),
                'country_code' => array('type' => 'string'),
                'sector' => array('type' => 'string'),
                'is_public' => array('type' => 'boolean', 'default' => false)
            )
        )
    ));

    // Single template endpoints
    register_rest_route('chatgabi/v1', '/templates/(?P<id>\d+)', array(
        array(
            'methods' => 'GET',
            'callback' => 'chatgabi_rest_get_template',
            'permission_callback' => 'chatgabi_rest_check_template_access',
            'args' => array('id' => array('type' => 'integer'))
        ),
        array(
            'methods' => 'PUT',
            'callback' => 'chatgabi_rest_update_template',
            'permission_callback' => 'chatgabi_rest_check_template_ownership',
            'args' => array('id' => array('type' => 'integer'))
        ),
        array(
            'methods' => 'DELETE',
            'callback' => 'chatgabi_rest_delete_template',
            'permission_callback' => 'chatgabi_rest_check_template_ownership',
            'args' => array('id' => array('type' => 'integer'))
        )
    ));

    // Categories endpoints
    register_rest_route('chatgabi/v1', '/template-categories', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_rest_get_categories',
        'permission_callback' => 'chatgabi_rest_check_auth'
    ));

    // Template usage tracking
    register_rest_route('chatgabi/v1', '/templates/(?P<id>\d+)/use', array(
        'methods' => 'POST',
        'callback' => 'chatgabi_rest_track_template_usage',
        'permission_callback' => 'chatgabi_rest_check_auth',
        'args' => array(
            'id' => array('type' => 'integer'),
            'tokens_used' => array('type' => 'integer'),
            'credits_used' => array('type' => 'number'),
            'rating' => array('type' => 'integer', 'minimum' => 1, 'maximum' => 5),
            'feedback' => array('type' => 'string')
        )
    ));
}

/**
 * REST API authentication check
 */
function chatgabi_rest_check_auth($request) {
    return is_user_logged_in();
}

/**
 * Check template access permission
 */
function chatgabi_rest_check_template_access($request) {
    if (!is_user_logged_in()) {
        return false;
    }

    $template_id = $request['id'];
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT user_id, is_public FROM {$templates_table} WHERE id = %d AND status = 'active'",
        $template_id
    ));

    if (!$template) {
        return false;
    }

    // Allow access if template is public or user owns it
    return $template->is_public || $template->user_id == get_current_user_id() || current_user_can('manage_options');
}

/**
 * Check template ownership permission
 */
function chatgabi_rest_check_template_ownership($request) {
    if (!is_user_logged_in()) {
        return false;
    }

    $template_id = $request['id'];
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT user_id FROM {$templates_table} WHERE id = %d",
        $template_id
    ));

    if (!$template) {
        return false;
    }

    // Allow access if user owns template or is admin
    return $template->user_id == get_current_user_id() || current_user_can('manage_options');
}

/**
 * REST API: Get templates
 */
function chatgabi_rest_get_templates($request) {
    $user_id = get_current_user_id();
    $params = $request->get_params();

    $args = array(
        'category_id' => $params['category'] ?? 0,
        'search' => $params['search'] ?? '',
        'limit' => min($params['limit'] ?? 20, 100), // Max 100 per request
        'offset' => $params['offset'] ?? 0
    );

    if ($params['public']) {
        // Get public templates from all users
        $templates = chatgabi_get_public_templates($args);
    } else {
        // Get user's own templates
        $templates = chatgabi_get_user_templates($user_id, $args);
    }

    return rest_ensure_response($templates);
}

/**
 * REST API: Get single template
 */
function chatgabi_rest_get_template($request) {
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';

    $template_id = $request['id'];

    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
         FROM {$templates_table} t
         LEFT JOIN {$categories_table} c ON t.category_id = c.id
         WHERE t.id = %d AND t.status = 'active'",
        $template_id
    ));

    if (!$template) {
        return new WP_Error('template_not_found', 'Template not found', array('status' => 404));
    }

    return rest_ensure_response($template);
}

/**
 * REST API: Create template
 */
function chatgabi_rest_create_template($request) {
    $params = $request->get_params();
    $params['user_id'] = get_current_user_id();

    $result = chatgabi_save_prompt_template($params);

    if (is_wp_error($result)) {
        return $result;
    }

    // Get the created template
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$templates_table} WHERE id = %d",
        $result
    ));

    return rest_ensure_response($template);
}

/**
 * REST API: Update template
 */
function chatgabi_rest_update_template($request) {
    $params = $request->get_params();
    $params['id'] = $request['id'];
    $params['user_id'] = get_current_user_id();

    $result = chatgabi_save_prompt_template($params);

    if (is_wp_error($result)) {
        return $result;
    }

    // Get the updated template
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$templates_table} WHERE id = %d",
        $request['id']
    ));

    return rest_ensure_response($template);
}

/**
 * REST API: Delete template
 * Enhanced version with comprehensive permission checking and analytics
 */
function chatgabi_rest_delete_template($request) {
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $template_id = $request['id'];
    $user_id = get_current_user_id();

    // Check if template exists and get template data
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $templates_table WHERE id = %d AND status != 'deleted'",
        $template_id
    ));

    if (!$template) {
        return new WP_Error(
            'template_not_found',
            __('Template not found', 'chatgabi'),
            array('status' => 404)
        );
    }

    // Check permission (user owns template or is admin)
    if ($template->user_id != $user_id && !current_user_can('manage_options')) {
        return new WP_Error(
            'permission_denied',
            __('You do not have permission to delete this template', 'chatgabi'),
            array('status' => 403)
        );
    }

    // Soft delete by updating status
    $result = $wpdb->update(
        $templates_table,
        array('status' => 'deleted'),
        array('id' => $template_id),
        array('%s'),
        array('%d')
    );

    if ($result === false) {
        return new WP_Error(
            'delete_failed',
            __('Failed to delete template', 'chatgabi'),
            array('status' => 500)
        );
    }

    // Log analytics
    if (function_exists('businesscraft_ai_log_analytics')) {
        businesscraft_ai_log_analytics($user_id, 'template_deleted', array(
            'template_id' => $template_id,
            'template_title' => $template->title ?? 'Unknown',
            'deletion_type' => 'soft_delete'
        ));
    }

    return rest_ensure_response(array(
        'deleted' => true,
        'id' => $template_id,
        'success' => true,
        'message' => __('Template deleted successfully', 'chatgabi')
    ));
}

/**
 * REST API: Get categories
 */
function chatgabi_rest_get_categories($request) {
    $categories = chatgabi_get_template_categories(true);
    return rest_ensure_response($categories);
}

/**
 * REST API: Track template usage
 */
function chatgabi_rest_track_template_usage($request) {
    global $wpdb;
    $usage_table = $wpdb->prefix . 'chatgabi_template_usage';
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    $params = $request->get_params();
    $template_id = $request['id'];
    $user_id = get_current_user_id();

    // Insert usage record
    $usage_data = array(
        'template_id' => $template_id,
        'user_id' => $user_id,
        'session_id' => chatgabi_get_session_id(),
        'tokens_used' => $params['tokens_used'] ?? 0,
        'credits_used' => $params['credits_used'] ?? 0,
        'rating' => $params['rating'] ?? null,
        'feedback' => sanitize_textarea_field($params['feedback'] ?? '')
    );

    $result = $wpdb->insert($usage_table, $usage_data);

    if ($result !== false) {
        // Update template usage count
        $wpdb->query($wpdb->prepare(
            "UPDATE {$templates_table} SET usage_count = usage_count + 1 WHERE id = %d",
            $template_id
        ));

        // Update rating if provided
        if (!empty($params['rating'])) {
            chatgabi_update_template_rating($template_id, $params['rating']);
        }
    }

    return rest_ensure_response(array('tracked' => $result !== false));
}

/**
 * Get public templates from all users
 */
function chatgabi_get_public_templates($args = array()) {
    global $wpdb;

    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';

    $defaults = array(
        'category_id' => 0,
        'status' => 'active',
        'limit' => 20,
        'offset' => 0,
        'orderby' => 'usage_count',
        'order' => 'DESC',
        'search' => ''
    );

    $args = wp_parse_args($args, $defaults);

    $where_conditions = array("t.is_public = 1", "t.status = %s");
    $where_values = array($args['status']);

    if ($args['category_id']) {
        $where_conditions[] = "t.category_id = %d";
        $where_values[] = $args['category_id'];
    }

    if (!empty($args['search'])) {
        $where_conditions[] = "MATCH(t.title, t.description, t.prompt_text, t.tags) AGAINST(%s IN NATURAL LANGUAGE MODE)";
        $where_values[] = $args['search'];
    }

    $where_clause = "WHERE " . implode(" AND ", $where_conditions);

    $sql = "SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color,
                   u.display_name as author_name
            FROM {$templates_table} t
            LEFT JOIN {$categories_table} c ON t.category_id = c.id
            LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
            {$where_clause}
            ORDER BY t.{$args['orderby']} {$args['order']}
            LIMIT %d OFFSET %d";

    $where_values[] = $args['limit'];
    $where_values[] = $args['offset'];

    return $wpdb->get_results($wpdb->prepare($sql, $where_values));
}

/**
 * Update template rating
 */
function chatgabi_update_template_rating($template_id, $new_rating) {
    global $wpdb;

    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    // Get current rating data
    $current = $wpdb->get_row($wpdb->prepare(
        "SELECT rating_average, rating_count FROM {$templates_table} WHERE id = %d",
        $template_id
    ));

    if (!$current) {
        return false;
    }

    // Calculate new average
    $total_rating = ($current->rating_average * $current->rating_count) + $new_rating;
    $new_count = $current->rating_count + 1;
    $new_average = $total_rating / $new_count;

    // Update template
    return $wpdb->update(
        $templates_table,
        array(
            'rating_average' => round($new_average, 2),
            'rating_count' => $new_count
        ),
        array('id' => $template_id),
        array('%f', '%d'),
        array('%d')
    );
}

// Note: chatgabi_get_template_by_id() function is defined in template-functions.php

/**
 * Delete template (utility function)
 * Enhanced version with flexible deletion options and permission checking
 *
 * @param int $template_id Template ID to delete
 * @param int|null $user_id User ID (null for current user)
 * @param bool $hard_delete Whether to permanently delete (default: false for soft delete)
 * @param bool $check_permission Whether to check user permissions (default: true)
 * @return bool|WP_Error True on success, false or WP_Error on failure
 */
function chatgabi_delete_template($template_id, $user_id = null, $hard_delete = false, $check_permission = true) {
    global $wpdb;
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';

    if ($user_id === null) {
        $user_id = get_current_user_id();
    }

    // Get template data
    $template = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $templates_table WHERE id = %d AND status != 'deleted'",
        $template_id
    ));

    if (!$template) {
        return new WP_Error('template_not_found', 'Template not found');
    }

    // Check permission if required
    if ($check_permission) {
        if ($template->user_id != $user_id && !current_user_can('manage_options')) {
            return new WP_Error('permission_denied', 'Permission denied');
        }
    }

    if ($hard_delete) {
        // Permanently delete from database
        $result = $wpdb->delete(
            $templates_table,
            array('id' => $template_id),
            array('%d')
        );
    } else {
        // Soft delete by updating status
        $result = $wpdb->update(
            $templates_table,
            array('status' => 'deleted'),
            array('id' => $template_id),
            array('%s'),
            array('%d')
        );
    }

    if ($result === false) {
        return new WP_Error('delete_failed', 'Failed to delete template');
    }

    // Log analytics for successful deletion
    if (function_exists('businesscraft_ai_log_analytics')) {
        businesscraft_ai_log_analytics($user_id, 'template_deleted', array(
            'template_id' => $template_id,
            'template_title' => $template->title ?? 'Unknown',
            'deletion_type' => $hard_delete ? 'hard_delete' : 'soft_delete',
            'deleted_by' => $user_id
        ));
    }

    return true;
}

// Initialize prompt templates system
add_action('init', 'chatgabi_init_prompt_templates');

// Register REST API routes - DISABLED to prevent duplicates with rest-api.php
// add_action('rest_api_init', 'chatgabi_register_template_rest_routes');

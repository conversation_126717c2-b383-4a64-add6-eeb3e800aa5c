<?php
/**
 * Database Schema Fixer for ChatGABI
 * Fixes prompt_text vs prompt_content column mismatch with proper WordPress loading
 *
 * @package BusinessCraft_AI
 * @since 1.2.0
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to safely output content
function safe_output($content) {
    if (php_sapi_name() === 'cli') {
        echo $content;
    } else {
        echo '<pre>' . htmlspecialchars($content) . '</pre>';
    }
}

// Function to find WordPress root
function find_wordpress_root() {
    $possible_paths = array(
        dirname(dirname(dirname(__DIR__))),                    // wp-content/themes/theme/
        dirname(dirname(dirname(dirname(__DIR__)))),           // If nested deeper
        realpath(__DIR__ . '/../../../'),                      // Alternative resolution
        'C:/xampp/htdocs/swifmind-local/wordpress',           // Windows XAMPP
        '/Applications/XAMPP/htdocs/swifmind-local/wordpress', // Mac XAMPP
        '/opt/lampp/htdocs/swifmind-local/wordpress',         // Linux XAMPP
        getcwd(),                                              // Current working directory
        dirname(getcwd()),                                     // Parent of current directory
    );
    
    foreach ($possible_paths as $path) {
        if (file_exists($path . '/wp-config.php') || file_exists($path . '/wp-load.php')) {
            return realpath($path);
        }
    }
    
    return false;
}

// Find and load WordPress
$wp_root = find_wordpress_root();

if (!$wp_root) {
    safe_output("❌ ERROR: Could not locate WordPress installation\n");
    safe_output("Current directory: " . __DIR__ . "\n");
    exit(1);
}

// Load WordPress
$wp_load_path = $wp_root . '/wp-load.php';
$wp_config_path = $wp_root . '/wp-config.php';

try {
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
    } elseif (file_exists($wp_config_path)) {
        require_once $wp_config_path;
        if (file_exists($wp_root . '/wp-settings.php')) {
            require_once $wp_root . '/wp-settings.php';
        }
    } else {
        throw new Exception("WordPress files not found in: " . $wp_root);
    }
} catch (Exception $e) {
    safe_output("❌ ERROR loading WordPress: " . $e->getMessage() . "\n");
    exit(1);
}

// Verify WordPress is loaded
if (!function_exists('get_template_directory') || !function_exists('wp_get_version')) {
    safe_output("❌ ERROR: WordPress not properly loaded\n");
    exit(1);
}

// Security check
if (!current_user_can('manage_options')) {
    safe_output("❌ ERROR: Administrator privileges required\n");
    exit(1);
}

safe_output("🔧 ChatGABI Database Schema Fixer\n");
safe_output("=================================\n");
safe_output("WordPress Version: " . wp_get_version() . "\n");
safe_output("WordPress Root: " . $wp_root . "\n\n");

global $wpdb;

$fixes_applied = 0;
$total_fixes = 0;

// Fix 1: Prompt Templates Table Schema
safe_output("Fix 1: Prompt Templates Table Schema\n");
safe_output("------------------------------------\n");

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") === $templates_table;

if ($table_exists) {
    safe_output("✅ Templates table exists\n");
    
    // Check current columns
    $columns = $wpdb->get_results("DESCRIBE {$templates_table}");
    $column_names = array_column($columns, 'Field');
    
    safe_output("Current columns: " . implode(', ', $column_names) . "\n");
    
    $has_prompt_text = in_array('prompt_text', $column_names);
    $has_prompt_content = in_array('prompt_content', $column_names);
    
    $total_fixes++;
    
    if ($has_prompt_text && !$has_prompt_content) {
        safe_output("✅ Schema is correct (prompt_text exists, prompt_content removed)\n");
        $fixes_applied++;
    } elseif (!$has_prompt_text && $has_prompt_content) {
        safe_output("⚠️ Renaming prompt_content to prompt_text...\n");
        
        $alter_result = $wpdb->query("ALTER TABLE {$templates_table} CHANGE COLUMN prompt_content prompt_text LONGTEXT NOT NULL");
        
        if ($alter_result !== false) {
            safe_output("✅ Successfully renamed prompt_content to prompt_text\n");
            $fixes_applied++;
        } else {
            safe_output("❌ Failed to rename column: " . $wpdb->last_error . "\n");
        }
    } elseif ($has_prompt_text && $has_prompt_content) {
        safe_output("⚠️ Both columns exist - consolidating...\n");
        
        // Copy data from prompt_content to prompt_text if prompt_text is empty
        $wpdb->query("UPDATE {$templates_table} SET prompt_text = prompt_content WHERE (prompt_text = '' OR prompt_text IS NULL) AND prompt_content IS NOT NULL");
        
        // Drop the duplicate column
        $drop_result = $wpdb->query("ALTER TABLE {$templates_table} DROP COLUMN prompt_content");
        
        if ($drop_result !== false) {
            safe_output("✅ Successfully consolidated columns\n");
            $fixes_applied++;
        } else {
            safe_output("❌ Failed to drop duplicate column: " . $wpdb->last_error . "\n");
        }
    } else {
        safe_output("⚠️ Adding missing prompt_text column...\n");
        
        $add_result = $wpdb->query("ALTER TABLE {$templates_table} ADD COLUMN prompt_text LONGTEXT NOT NULL AFTER description");
        
        if ($add_result !== false) {
            safe_output("✅ Successfully added prompt_text column\n");
            $fixes_applied++;
        } else {
            safe_output("❌ Failed to add column: " . $wpdb->last_error . "\n");
        }
    }
} else {
    safe_output("❌ Templates table does not exist - creating it...\n");
    
    // Load prompt templates functions if available
    $prompt_templates_file = get_template_directory() . '/inc/prompt-templates.php';
    if (file_exists($prompt_templates_file)) {
        require_once $prompt_templates_file;
        
        if (function_exists('chatgabi_create_prompt_templates_tables')) {
            $result = chatgabi_create_prompt_templates_tables();
            if ($result) {
                safe_output("✅ Successfully created prompt templates tables\n");
                $fixes_applied++;
            } else {
                safe_output("❌ Failed to create tables\n");
            }
        } else {
            safe_output("❌ Table creation function not found\n");
        }
    } else {
        safe_output("❌ Prompt templates file not found\n");
    }
    
    $total_fixes++;
}

safe_output("\n");

// Fix 2: Categories Table Status Column
safe_output("Fix 2: Categories Table Status Column\n");
safe_output("-----------------------------------\n");

$categories_table = $wpdb->prefix . 'chatgabi_template_categories';
$categories_exists = $wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table;

$total_fixes++;

if ($categories_exists) {
    $cat_columns = $wpdb->get_results("DESCRIBE {$categories_table}");
    $cat_column_names = array_column($cat_columns, 'Field');
    
    $has_status = in_array('status', $cat_column_names);
    
    if (!$has_status) {
        safe_output("⚠️ Adding missing status column...\n");
        
        $add_status_result = $wpdb->query("ALTER TABLE {$categories_table} ADD COLUMN status varchar(20) NOT NULL DEFAULT 'active' AFTER sort_order");
        
        if ($add_status_result !== false) {
            safe_output("✅ Successfully added status column\n");
            $fixes_applied++;
        } else {
            safe_output("❌ Failed to add status column: " . $wpdb->last_error . "\n");
        }
    } else {
        safe_output("✅ Status column already exists\n");
        $fixes_applied++;
    }
} else {
    safe_output("⚠️ Categories table does not exist\n");
}

safe_output("\n");

// Fix 3: Performance Indexes
safe_output("Fix 3: Performance Indexes\n");
safe_output("--------------------------\n");

$indexes_to_add = array(
    $wpdb->prefix . 'chatgabi_conversations' => array(
        'idx_user_date' => 'ADD INDEX idx_user_date (user_id, created_at)',
        'idx_conversation_type' => 'ADD INDEX idx_conversation_type (conversation_type, created_at)'
    ),
    $wpdb->prefix . 'chatgabi_feedback' => array(
        'idx_rating_country' => 'ADD INDEX idx_rating_country (rating_score, user_country)',
        'idx_feedback_date' => 'ADD INDEX idx_feedback_date (created_at, rating_type)'
    ),
    $wpdb->prefix . 'chatgabi_prompt_templates' => array(
        'idx_usage_rating' => 'ADD INDEX idx_usage_rating (usage_count, rating_average)',
        'idx_public_featured' => 'ADD INDEX idx_public_featured (is_public, is_featured, status)'
    )
);

$indexes_added = 0;
$total_indexes = 0;

foreach ($indexes_to_add as $table => $indexes) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'") === $table;
    
    if ($table_exists) {
        safe_output("Adding indexes to {$table}:\n");
        
        foreach ($indexes as $index_name => $index_sql) {
            $total_indexes++;
            
            // Check if index already exists
            $existing_indexes = $wpdb->get_results("SHOW INDEX FROM {$table}");
            $index_exists = false;
            
            foreach ($existing_indexes as $existing_index) {
                if ($existing_index->Key_name === $index_name) {
                    $index_exists = true;
                    break;
                }
            }
            
            if (!$index_exists) {
                $result = $wpdb->query("ALTER TABLE {$table} {$index_sql}");
                
                if ($result !== false) {
                    safe_output("  ✅ Added index: {$index_name}\n");
                    $indexes_added++;
                } else {
                    safe_output("  ❌ Failed to add index {$index_name}: " . $wpdb->last_error . "\n");
                }
            } else {
                safe_output("  ℹ️ Index {$index_name} already exists\n");
                $indexes_added++;
            }
        }
    } else {
        safe_output("⚠️ Table {$table} does not exist - skipping indexes\n");
        $total_indexes += count($indexes);
    }
}

safe_output("\n");

// Final Summary
safe_output("=== DATABASE SCHEMA FIX SUMMARY ===\n");
safe_output("Schema Fixes Applied: {$fixes_applied}/{$total_fixes}\n");
safe_output("Performance Indexes: {$indexes_added}/{$total_indexes}\n");

$overall_success = ($fixes_applied + $indexes_added) / ($total_fixes + $total_indexes) * 100;
safe_output("Overall Success Rate: " . round($overall_success) . "%\n\n");

if ($overall_success >= 90) {
    safe_output("🎉 Database schema fixes completed successfully!\n");
} elseif ($overall_success >= 70) {
    safe_output("✅ Most database schema fixes applied successfully.\n");
} else {
    safe_output("⚠️ Some database schema fixes need manual attention.\n");
}

safe_output("\n=== NEXT STEPS ===\n");
safe_output("1. Run the comprehensive audit test to verify all fixes\n");
safe_output("2. Test the templates functionality in the admin dashboard\n");
safe_output("3. Verify REST API endpoints are working correctly\n");
safe_output("4. Monitor performance improvements from new indexes\n");

exit($overall_success >= 70 ? 0 : 1);
?>

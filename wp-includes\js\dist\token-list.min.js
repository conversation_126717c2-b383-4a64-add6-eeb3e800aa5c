/*! This file is auto-generated */
(()=>{"use strict";var e={d:(r,t)=>{for(var s in t)e.o(t,s)&&!e.o(r,s)&&Object.defineProperty(r,s,{enumerable:!0,get:t[s]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r)},r={};e.d(r,{default:()=>t});class t{constructor(e=""){this._currentValue="",this._valueAsArray=[],this.value=e}entries(...e){return this._valueAsArray.entries(...e)}forEach(...e){return this._valueAsArray.forEach(...e)}keys(...e){return this._valueAsArray.keys(...e)}values(...e){return this._valueAsArray.values(...e)}get value(){return this._currentValue}set value(e){e=String(e),this._valueAsArray=[...new Set(e.split(/\s+/g).filter(Boolean))],this._currentValue=this._valueAsArray.join(" ")}get length(){return this._valueAsArray.length}toString(){return this.value}*[Symbol.iterator](){return yield*this._valueAsArray}item(e){return this._valueAsArray[e]}contains(e){return-1!==this._valueAsArray.indexOf(e)}add(...e){this.value+=" "+e.join(" ")}remove(...e){this.value=this._valueAsArray.filter((r=>!e.includes(r))).join(" ")}toggle(e,r){return void 0===r&&(r=!this.contains(e)),r?this.add(e):this.remove(e),r}replace(e,r){return!!this.contains(e)&&(this.remove(e),this.add(r),!0)}supports(e){return!0}}(window.wp=window.wp||{}).tokenList=r.default})();
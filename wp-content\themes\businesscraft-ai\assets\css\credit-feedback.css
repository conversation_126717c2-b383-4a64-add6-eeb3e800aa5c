/**
 * ChatGABI Credit Feedback Widget Styles
 * 
 * Styles for real-time token/credit feedback display
 */

/* Main Widget Container */
.chatgabi-credit-feedback-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 20px;
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
    overflow: hidden;
}

.chatgabi-credit-feedback-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007cba 0%, #28a745 50%, #ffc107 100%);
}

/* Credit Balance Section */
.credit-balance-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #007cba 0%, #0056b3 100%);
    color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 124, 186, 0.2);
}

.balance-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.balance-icon {
    font-size: 1.5rem;
}

.balance-label {
    font-weight: 600;
    font-size: 1rem;
}

.balance-amount {
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.balance-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.refresh-balance-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.refresh-balance-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.refresh-balance-btn.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.buy-credits-btn {
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.buy-credits-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    color: white;
}

/* Token Estimation Section */
.token-estimation-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.estimation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #495057;
}

.estimation-icon {
    font-size: 1.2rem;
}

.estimation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 12px;
}

.estimation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.estimation-metric {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.estimation-value {
    font-weight: 700;
    color: #495057;
}

.estimation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.sufficient {
    background: #28a745;
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
}

.status-indicator.insufficient {
    background: #dc3545;
    box-shadow: 0 0 8px rgba(220, 53, 69, 0.4);
}

.estimation-status:has(.sufficient) {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.estimation-status:has(.insufficient) {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Processing Section */
.processing-section {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.processing-animation {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.processing-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.processing-text {
    font-weight: 600;
}

.processing-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.processing-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.processing-metric {
    font-size: 0.85rem;
    opacity: 0.9;
}

.processing-value {
    font-weight: 700;
}

/* Stats Toggle Section */
.stats-toggle-section {
    margin-bottom: 15px;
}

.stats-toggle-btn {
    width: 100%;
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.stats-toggle-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.stats-icon {
    font-size: 1.1rem;
}

.toggle-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

/* Usage Stats Panel */
.usage-stats-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stats-title {
    font-weight: 600;
    color: #495057;
}

#stats-period {
    padding: 6px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.stat-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #007cba;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.stats-chart-container {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Low Credit Warning */
.low-credit-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-top: 15px;
    animation: slideIn 0.3s ease;
}

.warning-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.warning-content {
    flex: 1;
}

.warning-title {
    font-weight: 700;
    color: #856404;
    margin-bottom: 5px;
}

.warning-message {
    color: #856404;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.warning-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.warning-buy-btn {
    background: #ffc107;
    color: #212529;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.warning-buy-btn:hover {
    background: #e0a800;
    color: #212529;
    transform: translateY(-1px);
}

.warning-dismiss-btn {
    background: transparent;
    color: #856404;
    border: 1px solid #856404;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.warning-dismiss-btn:hover {
    background: #856404;
    color: white;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatgabi-credit-feedback-widget {
        padding: 15px;
        margin: 15px 0;
    }
    
    .credit-balance-section {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .balance-amount {
        font-size: 1.8rem;
    }
    
    .estimation-details,
    .processing-details,
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .warning-actions {
        flex-direction: column;
    }
    
    .warning-buy-btn,
    .warning-dismiss-btn {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .balance-actions {
        flex-direction: column;
        width: 100%;
        gap: 8px;
    }
    
    .buy-credits-btn {
        width: 100%;
        text-align: center;
    }
    
    .stats-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    #stats-period {
        width: 100%;
    }
}

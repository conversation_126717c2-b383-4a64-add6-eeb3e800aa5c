<?php
/**
 * PDF Export Testing Script for BusinessCraft AI
 * 
 * This script tests the PDF export functionality
 * Run this from WordPress admin or via WP-CLI
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test PDF Export Functionality
 */
function test_businesscraft_pdf_export() {
    echo "<h2>Testing BusinessCraft AI PDF Export System</h2>\n";
    
    // Test 1: Check if export functions exist
    echo "<h3>1. Function Availability Test</h3>\n";
    
    $functions_to_test = [
        'businesscraft_ai_init_pdf_export',
        'businesscraft_ai_generate_template_pdf',
        'businesscraft_ai_load_tcpdf',
        'businesscraft_ai_create_export_history_table'
    ];
    
    foreach ($functions_to_test as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists<br>\n";
        } else {
            echo "❌ Function {$function} missing<br>\n";
        }
    }
    
    // Test 2: Check database table
    echo "<h3>2. Database Table Test</h3>\n";
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_export_history';
    
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "✅ Export history table exists<br>\n";
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE {$table_name}");
        echo "Table columns: " . implode(', ', array_column($columns, 'Field')) . "<br>\n";
    } else {
        echo "❌ Export history table missing<br>\n";
        echo "Creating table...<br>\n";
        businesscraft_ai_create_export_history_table();
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
            echo "✅ Table created successfully<br>\n";
        } else {
            echo "❌ Failed to create table<br>\n";
        }
    }
    
    // Test 3: Check upload directory
    echo "<h3>3. Upload Directory Test</h3>\n";
    
    $upload_dir = wp_upload_dir();
    $pdf_export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    
    if (file_exists($pdf_export_dir)) {
        echo "✅ Export directory exists: {$pdf_export_dir}<br>\n";
        
        if (is_writable($pdf_export_dir)) {
            echo "✅ Export directory is writable<br>\n";
        } else {
            echo "❌ Export directory is not writable<br>\n";
        }
        
        // Check .htaccess protection
        $htaccess_file = $pdf_export_dir . '/.htaccess';
        if (file_exists($htaccess_file)) {
            echo "✅ .htaccess protection file exists<br>\n";
        } else {
            echo "⚠️ .htaccess protection file missing<br>\n";
        }
    } else {
        echo "❌ Export directory missing<br>\n";
        echo "Creating directory...<br>\n";
        
        if (wp_mkdir_p($pdf_export_dir)) {
            echo "✅ Directory created successfully<br>\n";
            
            // Create .htaccess
            $htaccess_content = "Order deny,allow\nDeny from all\n";
            file_put_contents($pdf_export_dir . '/.htaccess', $htaccess_content);
            echo "✅ .htaccess protection added<br>\n";
        } else {
            echo "❌ Failed to create directory<br>\n";
        }
    }
    
    // Test 4: Check REST API endpoints
    echo "<h3>4. REST API Endpoints Test</h3>\n";
    
    $rest_server = rest_get_server();
    $routes = $rest_server->get_routes();
    
    $expected_routes = [
        '/businesscraft-ai/v1/export/pdf',
        '/businesscraft-ai/v1/export/history'
    ];
    
    foreach ($expected_routes as $route) {
        if (isset($routes[$route])) {
            echo "✅ REST route {$route} registered<br>\n";
        } else {
            echo "❌ REST route {$route} missing<br>\n";
        }
    }
    
    // Test 5: Check JavaScript and CSS files
    echo "<h3>5. Asset Files Test</h3>\n";
    
    $asset_files = [
        'assets/js/pdf-export.js',
        'assets/css/pdf-export.css'
    ];
    
    foreach ($asset_files as $file) {
        $file_path = get_template_directory() . '/' . $file;
        if (file_exists($file_path)) {
            echo "✅ Asset file {$file} exists<br>\n";
        } else {
            echo "❌ Asset file {$file} missing<br>\n";
        }
    }
    
    // Test 6: Check TCPDF availability
    echo "<h3>6. TCPDF Library Test</h3>\n";
    
    $tcpdf_available = businesscraft_ai_load_tcpdf();
    
    if ($tcpdf_available) {
        echo "✅ TCPDF library available<br>\n";
    } else {
        echo "⚠️ TCPDF library not available - using HTML fallback<br>\n";
    }
    
    // Test 7: Test export with sample data
    echo "<h3>7. Sample Export Test</h3>\n";
    
    if (is_user_logged_in()) {
        // Create a sample template for testing
        $sample_template = (object) [
            'id' => 999,
            'template_name' => 'Test Business Plan',
            'template_type' => 'business-plan',
            'business_idea' => 'Test Business Idea',
            'industry_sector' => 'Technology',
            'target_country' => 'GH',
            'business_stage' => 'startup',
            'generated_content' => "# Executive Summary\n\nThis is a test business plan.\n\n## Market Analysis\n\n- Market size: Large\n- Competition: Moderate\n- Opportunity: High\n\n## Financial Projections\n\n1. Year 1: $10,000\n2. Year 2: $25,000\n3. Year 3: $50,000",
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $result = businesscraft_ai_generate_html_pdf_document($sample_template, true);
            
            if ($result['success']) {
                echo "✅ Sample export generated successfully<br>\n";
                echo "Filename: {$result['filename']}<br>\n";
                echo "File size: {$result['file_size']} bytes<br>\n";
                
                // Clean up test file
                if (file_exists($result['file_path'])) {
                    unlink($result['file_path']);
                    echo "✅ Test file cleaned up<br>\n";
                }
            } else {
                echo "❌ Sample export failed: {$result['message']}<br>\n";
            }
        } catch (Exception $e) {
            echo "❌ Sample export error: " . $e->getMessage() . "<br>\n";
        }
    } else {
        echo "⚠️ User not logged in - skipping sample export test<br>\n";
    }
    
    // Test 8: Check WordPress hooks
    echo "<h3>8. WordPress Hooks Test</h3>\n";
    
    $hooks_to_check = [
        'wp_ajax_export_template_pdf',
        'wp_ajax_download_export',
        'wp_ajax_print_export',
        'rest_api_init',
        'wp_enqueue_scripts'
    ];
    
    foreach ($hooks_to_check as $hook) {
        if (has_action($hook)) {
            echo "✅ Hook {$hook} registered<br>\n";
        } else {
            echo "❌ Hook {$hook} missing<br>\n";
        }
    }
    
    echo "<h3>Test Summary</h3>\n";
    echo "<p>PDF Export System testing completed. Review the results above to ensure all components are working correctly.</p>\n";
    
    echo "<h4>Next Steps:</h4>\n";
    echo "<ul>\n";
    echo "<li>Test export functionality from the templates page</li>\n";
    echo "<li>Verify export buttons appear on template cards</li>\n";
    echo "<li>Test export modal and download process</li>\n";
    echo "<li>Check export history on dashboard</li>\n";
    echo "<li>Test mobile responsiveness</li>\n";
    echo "</ul>\n";
}

// Run the test if accessed directly
if (isset($_GET['run_test']) && $_GET['run_test'] === 'pdf_export') {
    if (current_user_can('manage_options')) {
        test_businesscraft_pdf_export();
    } else {
        echo "Access denied. Administrator privileges required.";
    }
}

/**
 * Add admin menu item for testing
 */
function add_pdf_export_test_menu() {
    add_management_page(
        'PDF Export Test',
        'PDF Export Test',
        'manage_options',
        'pdf-export-test',
        function() {
            echo '<div class="wrap">';
            echo '<h1>PDF Export System Test</h1>';
            echo '<p><a href="' . admin_url('tools.php?page=pdf-export-test&run_test=pdf_export') . '" class="button button-primary">Run PDF Export Test</a></p>';
            
            if (isset($_GET['run_test'])) {
                test_businesscraft_pdf_export();
            }
            
            echo '</div>';
        }
    );
}
add_action('admin_menu', 'add_pdf_export_test_menu');

/**
 * Test Advanced Features
 */
function test_businesscraft_advanced_features() {
    echo "<h2>Testing BusinessCraft AI Advanced Features</h2>\n";

    // Test 1: Check DOCX export functions
    echo "<h3>1. DOCX Export Functions Test</h3>\n";

    $docx_functions = [
        'businesscraft_ai_generate_docx_document',
        'businesscraft_ai_load_phpword',
        'businesscraft_ai_generate_html_docx_document'
    ];

    foreach ($docx_functions as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists<br>\n";
        } else {
            echo "❌ Function {$function} missing<br>\n";
        }
    }

    // Test 2: Check email delivery functions
    echo "<h3>2. Email Delivery Functions Test</h3>\n";

    $email_functions = [
        'businesscraft_ai_email_export',
        'businesscraft_ai_log_email_delivery',
        'businesscraft_ai_create_email_deliveries_table'
    ];

    foreach ($email_functions as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists<br>\n";
        } else {
            echo "❌ Function {$function} missing<br>\n";
        }
    }

    // Test 3: Check customization functions
    echo "<h3>3. Template Customization Functions Test</h3>\n";

    $customization_functions = [
        'businesscraft_ai_init_template_customization',
        'businesscraft_ai_apply_template_customizations',
        'businesscraft_ai_get_template_themes'
    ];

    foreach ($customization_functions as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists<br>\n";
        } else {
            echo "❌ Function {$function} missing<br>\n";
        }
    }

    // Test 4: Check collaboration functions
    echo "<h3>4. Collaboration Functions Test</h3>\n";

    $collaboration_functions = [
        'businesscraft_ai_init_collaboration',
        'businesscraft_ai_share_template',
        'businesscraft_ai_add_template_comment'
    ];

    foreach ($collaboration_functions as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists<br>\n";
        } else {
            echo "❌ Function {$function} missing<br>\n";
        }
    }

    // Test 5: Check database tables
    echo "<h3>5. Advanced Features Database Tables Test</h3>\n";

    global $wpdb;
    $advanced_tables = [
        'chatgabi_email_deliveries',
        'chatgabi_template_versions',
        'chatgabi_template_collaborations',
        'chatgabi_template_comments'
    ];

    foreach ($advanced_tables as $table_suffix) {
        $table_name = $wpdb->prefix . $table_suffix;
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

        if ($table_exists) {
            echo "✅ Table {$table_name} exists<br>\n";
        } else {
            echo "❌ Table {$table_name} missing<br>\n";
        }
    }

    // Test 6: Check JavaScript and CSS files
    echo "<h3>6. Advanced Features Asset Files Test</h3>\n";

    $advanced_assets = [
        'assets/js/template-customization.js',
        'assets/js/collaboration.js',
        'assets/css/template-customization.css',
        'assets/css/collaboration.css'
    ];

    foreach ($advanced_assets as $file) {
        $file_path = get_template_directory() . '/' . $file;
        if (file_exists($file_path)) {
            echo "✅ Asset file {$file} exists<br>\n";
        } else {
            echo "❌ Asset file {$file} missing<br>\n";
        }
    }

    // Test 7: Check REST API endpoints
    echo "<h3>7. Advanced Features REST API Test</h3>\n";

    $rest_server = rest_get_server();
    $routes = $rest_server->get_routes();

    $advanced_routes = [
        '/businesscraft-ai/v1/templates/customize',
        '/businesscraft-ai/v1/templates/themes',
        '/businesscraft-ai/v1/templates/(?P<id>\d+)/share',
        '/businesscraft-ai/v1/templates/(?P<id>\d+)/collaborators',
        '/businesscraft-ai/v1/templates/(?P<id>\d+)/comments'
    ];

    foreach ($advanced_routes as $route) {
        $route_exists = false;
        foreach ($routes as $registered_route => $handlers) {
            if (strpos($registered_route, str_replace('(?P<id>\d+)', '', $route)) !== false) {
                $route_exists = true;
                break;
            }
        }

        if ($route_exists) {
            echo "✅ REST route pattern {$route} registered<br>\n";
        } else {
            echo "❌ REST route pattern {$route} missing<br>\n";
        }
    }

    // Test 8: Test template themes
    echo "<h3>8. Template Themes Test</h3>\n";

    if (function_exists('businesscraft_ai_get_template_themes')) {
        $themes = businesscraft_ai_get_template_themes();

        if (!empty($themes)) {
            echo "✅ Template themes loaded: " . count($themes) . " themes available<br>\n";
            foreach ($themes as $theme_id => $theme) {
                echo "  - {$theme_id}: {$theme['name']}<br>\n";
            }
        } else {
            echo "❌ No template themes found<br>\n";
        }
    } else {
        echo "❌ Template themes function not available<br>\n";
    }

    echo "<h3>Advanced Features Test Summary</h3>\n";
    echo "<p>Advanced features testing completed. Review the results above to ensure all components are working correctly.</p>\n";

    echo "<h4>Next Steps for Advanced Features:</h4>\n";
    echo "<ul>\n";
    echo "<li>Test DOCX export functionality from templates page</li>\n";
    echo "<li>Test email delivery with sample documents</li>\n";
    echo "<li>Test template customization with different themes</li>\n";
    echo "<li>Test collaboration sharing and permissions</li>\n";
    echo "<li>Test comment system on shared templates</li>\n";
    echo "<li>Verify mobile responsiveness of all features</li>\n";
    echo "</ul>\n";
}

/**
 * Add advanced features test menu
 */
function add_advanced_features_test_menu() {
    add_management_page(
        'Advanced Features Test',
        'Advanced Features Test',
        'manage_options',
        'advanced-features-test',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Advanced Features System Test</h1>';
            echo '<p><a href="' . admin_url('tools.php?page=advanced-features-test&run_test=advanced_features') . '" class="button button-primary">Run Advanced Features Test</a></p>';

            if (isset($_GET['run_test']) && $_GET['run_test'] === 'advanced_features') {
                test_businesscraft_advanced_features();
            }

            echo '</div>';
        }
    );
}
add_action('admin_menu', 'add_advanced_features_test_menu');

/**
 * Test Document Wizards
 */
function test_businesscraft_document_wizards() {
    echo "<h2>Testing BusinessCraft AI Document Wizards</h2>\n";

    // Test 1: Check wizard functions
    echo "<h3>1. Document Wizard Functions Test</h3>\n";

    $wizard_functions = [
        'businesscraft_ai_init_document_wizards',
        'businesscraft_ai_get_wizard_config',
        'businesscraft_ai_start_wizard',
        'businesscraft_ai_process_wizard_step',
        'businesscraft_ai_generate_wizard_document',
        'businesscraft_ai_complete_wizard'
    ];

    foreach ($wizard_functions as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists<br>\n";
        } else {
            echo "❌ Function {$function} missing<br>\n";
        }
    }

    // Test 2: Check wizard configurations
    echo "<h3>2. Wizard Configurations Test</h3>\n";

    $wizard_types = ['business-plan', 'marketing-strategy', 'financial-forecast'];

    foreach ($wizard_types as $wizard_type) {
        if (function_exists('businesscraft_ai_get_wizard_config')) {
            $config = businesscraft_ai_get_wizard_config($wizard_type);
            if ($config) {
                echo "✅ Wizard config for {$wizard_type}: {$config['name']} ({" . count($config['steps']) . "} steps)<br>\n";
            } else {
                echo "❌ Wizard config for {$wizard_type} missing<br>\n";
            }
        } else {
            echo "❌ Cannot test wizard configs - function missing<br>\n";
            break;
        }
    }

    // Test 3: Check wizard database table
    echo "<h3>3. Wizard Database Tables Test</h3>\n";

    global $wpdb;
    $wizard_tables = [
        'chatgabi_wizard_sessions'
    ];

    foreach ($wizard_tables as $table_suffix) {
        $table_name = $wpdb->prefix . $table_suffix;
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

        if ($table_exists) {
            echo "✅ Table {$table_name} exists<br>\n";

            // Check table structure
            $columns = $wpdb->get_results("DESCRIBE {$table_name}");
            echo "  Columns: " . implode(', ', array_column($columns, 'Field')) . "<br>\n";
        } else {
            echo "❌ Table {$table_name} missing<br>\n";
            echo "  Creating table...<br>\n";

            if (function_exists('businesscraft_ai_create_wizard_tables')) {
                businesscraft_ai_create_wizard_tables();

                if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name) {
                    echo "✅ Table created successfully<br>\n";
                } else {
                    echo "❌ Failed to create table<br>\n";
                }
            }
        }
    }

    // Test 4: Check wizard assets
    echo "<h3>4. Wizard Asset Files Test</h3>\n";

    $wizard_assets = [
        'assets/js/document-wizards.js',
        'assets/css/document-wizards.css',
        'page-wizards.php'
    ];

    foreach ($wizard_assets as $file) {
        $file_path = get_template_directory() . '/' . $file;
        if (file_exists($file_path)) {
            echo "✅ Asset file {$file} exists<br>\n";
        } else {
            echo "❌ Asset file {$file} missing<br>\n";
        }
    }

    // Test 5: Check REST API endpoints
    echo "<h3>5. Wizard REST API Endpoints Test</h3>\n";

    $rest_server = rest_get_server();
    $routes = $rest_server->get_routes();

    $wizard_routes = [
        '/businesscraft-ai/v1/wizards/(?P<type>[a-zA-Z0-9-_]+)/start',
        '/businesscraft-ai/v1/wizards/(?P<id>\d+)/step',
        '/businesscraft-ai/v1/wizards/(?P<id>\d+)/complete'
    ];

    foreach ($wizard_routes as $route) {
        $route_exists = false;
        foreach ($routes as $registered_route => $handlers) {
            if (strpos($registered_route, str_replace(['(?P<type>[a-zA-Z0-9-_]+)', '(?P<id>\d+)'], '', $route)) !== false) {
                $route_exists = true;
                break;
            }
        }

        if ($route_exists) {
            echo "✅ REST route pattern {$route} registered<br>\n";
        } else {
            echo "❌ REST route pattern {$route} missing<br>\n";
        }
    }

    // Test 6: Test wizard step configuration
    echo "<h3>6. Wizard Step Configuration Test</h3>\n";

    if (function_exists('businesscraft_ai_get_wizard_config')) {
        $business_plan_config = businesscraft_ai_get_wizard_config('business-plan');

        if ($business_plan_config && isset($business_plan_config['steps'])) {
            echo "✅ Business Plan wizard has " . count($business_plan_config['steps']) . " steps<br>\n";

            foreach ($business_plan_config['steps'] as $step_num => $step_config) {
                $field_count = count($step_config['fields']);
                $ai_assist_count = 0;

                foreach ($step_config['fields'] as $field) {
                    if (isset($field['ai_assist']) && $field['ai_assist']) {
                        $ai_assist_count++;
                    }
                }

                echo "  Step {$step_num}: {$step_config['title']} ({$field_count} fields, {$ai_assist_count} with AI assist)<br>\n";
            }
        } else {
            echo "❌ Business Plan wizard configuration invalid<br>\n";
        }
    }

    // Test 7: Test AI prompt generation
    echo "<h3>7. AI Prompt Generation Test</h3>\n";

    if (function_exists('businesscraft_ai_build_wizard_ai_prompt')) {
        $test_context = array(
            'wizard_type' => 'business-plan',
            'field_name' => 'business_concept',
            'field_value' => 'A mobile app for food delivery',
            'user_country' => 'GH',
            'user_industry' => 'Technology',
            'wizard_data' => array()
        );

        $prompt = businesscraft_ai_build_wizard_ai_prompt($test_context);

        if ($prompt) {
            echo "✅ AI prompt generation working<br>\n";
            echo "  Sample prompt length: " . strlen($prompt) . " characters<br>\n";
        } else {
            echo "❌ AI prompt generation failed<br>\n";
        }
    } else {
        echo "❌ AI prompt generation function missing<br>\n";
    }

    echo "<h3>Document Wizards Test Summary</h3>\n";
    echo "<p>Document wizards testing completed. Review the results above to ensure all components are working correctly.</p>\n";

    echo "<h4>Next Steps for Document Wizards:</h4>\n";
    echo "<ul>\n";
    echo "<li>Test wizard page accessibility (/wizards)</li>\n";
    echo "<li>Test wizard initialization and step navigation</li>\n";
    echo "<li>Test AI assistance functionality</li>\n";
    echo "<li>Test wizard completion and document generation</li>\n";
    echo "<li>Test wizard progress saving and resumption</li>\n";
    echo "<li>Verify mobile responsiveness of wizard interface</li>\n";
    echo "</ul>\n";
}

/**
 * Add document wizards test menu
 */
function add_document_wizards_test_menu() {
    add_management_page(
        'Document Wizards Test',
        'Document Wizards Test',
        'manage_options',
        'document-wizards-test',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Document Wizards System Test</h1>';
            echo '<p><a href="' . admin_url('tools.php?page=document-wizards-test&run_test=document_wizards') . '" class="button button-primary">Run Document Wizards Test</a></p>';

            if (isset($_GET['run_test']) && $_GET['run_test'] === 'document_wizards') {
                test_businesscraft_document_wizards();
            }

            echo '</div>';
        }
    );
}
add_action('admin_menu', 'add_document_wizards_test_menu');

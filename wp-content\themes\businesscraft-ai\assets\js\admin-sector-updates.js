/**
 * Admin JavaScript for ChatGABI Sector Data Updates
 * 
 * @package ChatGABI
 * @since 1.2.0
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize the sector updates interface
    const SectorUpdates = {
        
        // Configuration
        config: {
            ajaxUrl: ajaxurl,
            nonce: chatgabiSectorUpdates.nonce,
            countries: ['Ghana', 'Kenya', 'Nigeria', 'South Africa']
        },
        
        // Initialize event handlers
        init: function() {
            this.bindEvents();
            this.loadUpdateLogs();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Country update buttons
            $('.country-buttons .button').on('click', this.handleCountryUpdate.bind(this));
            
            // Bulk update buttons
            $('#update-all-countries').on('click', this.handleBulkUpdate.bind(this));
            $('#update-trending-sectors').on('click', this.handleTrendingUpdate.bind(this));
            
            // Auto-refresh logs every 30 seconds
            setInterval(this.loadUpdateLogs.bind(this), 30000);
        },
        
        // Handle individual country update
        handleCountryUpdate: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const country = $button.data('country');
            
            if (!country) {
                this.showError('Invalid country selected');
                return;
            }
            
            this.startUpdate($button, country);
        },
        
        // Handle bulk update for all countries
        handleBulkUpdate: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            
            this.showProgress('Starting bulk update for all countries...');
            
            let completedCountries = 0;
            const totalCountries = this.config.countries.length;
            
            // Update countries sequentially to avoid API rate limits
            this.updateCountriesSequentially(0, totalCountries, function(success, failed) {
                SectorUpdates.hideProgress();
                SectorUpdates.showResults(`Bulk update completed. ${success} countries updated successfully, ${failed} failed.`);
                SectorUpdates.loadUpdateLogs();
            });
        },
        
        // Handle trending sectors update
        handleTrendingUpdate: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            
            this.showProgress('Updating trending sectors...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_manual_sector_update',
                    nonce: this.config.nonce,
                    update_type: 'trending'
                },
                success: function(response) {
                    SectorUpdates.hideProgress();
                    
                    if (response.success) {
                        SectorUpdates.showResults(response.data);
                    } else {
                        SectorUpdates.showError(response.data || 'Failed to update trending sectors');
                    }
                    
                    SectorUpdates.loadUpdateLogs();
                },
                error: function() {
                    SectorUpdates.hideProgress();
                    SectorUpdates.showError('Network error occurred while updating trending sectors');
                }
            });
        },
        
        // Start update for a specific country
        startUpdate: function($button, country) {
            const originalText = $button.text();
            
            $button.prop('disabled', true).text('Updating...');
            
            this.showProgress(`Updating sectors for ${country}...`);
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_manual_sector_update',
                    nonce: this.config.nonce,
                    country: country
                },
                success: function(response) {
                    $button.prop('disabled', false).text(originalText);
                    SectorUpdates.hideProgress();
                    
                    if (response.success) {
                        SectorUpdates.showResults(response.data);
                    } else {
                        SectorUpdates.showError(response.data || `Failed to update ${country}`);
                    }
                    
                    SectorUpdates.loadUpdateLogs();
                },
                error: function() {
                    $button.prop('disabled', false).text(originalText);
                    SectorUpdates.hideProgress();
                    SectorUpdates.showError(`Network error occurred while updating ${country}`);
                },
                timeout: 300000 // 5 minutes timeout for country updates
            });
        },
        
        // Update countries sequentially
        updateCountriesSequentially: function(index, total, callback) {
            if (index >= total) {
                callback(this.bulkUpdateResults.success, this.bulkUpdateResults.failed);
                return;
            }
            
            if (!this.bulkUpdateResults) {
                this.bulkUpdateResults = { success: 0, failed: 0 };
            }
            
            const country = this.config.countries[index];
            const progress = Math.round(((index + 1) / total) * 100);
            
            this.updateProgress(progress, `Updating ${country}... (${index + 1}/${total})`);
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'chatgabi_manual_sector_update',
                    nonce: this.config.nonce,
                    country: country
                },
                success: function(response) {
                    if (response.success) {
                        SectorUpdates.bulkUpdateResults.success++;
                    } else {
                        SectorUpdates.bulkUpdateResults.failed++;
                    }
                    
                    // Continue with next country
                    setTimeout(function() {
                        SectorUpdates.updateCountriesSequentially(index + 1, total, callback);
                    }, 2000); // 2 second delay between countries
                },
                error: function() {
                    SectorUpdates.bulkUpdateResults.failed++;
                    
                    // Continue with next country
                    setTimeout(function() {
                        SectorUpdates.updateCountriesSequentially(index + 1, total, callback);
                    }, 2000);
                },
                timeout: 300000 // 5 minutes timeout per country
            });
        },
        
        // Show progress indicator
        showProgress: function(message) {
            $('#update-progress').show();
            $('#update-results').hide();
            $('.progress-text').text(message);
            $('.progress-fill').css('width', '0%');
        },
        
        // Update progress
        updateProgress: function(percentage, message) {
            $('.progress-fill').css('width', percentage + '%');
            $('.progress-text').text(message);
        },
        
        // Hide progress indicator
        hideProgress: function() {
            $('#update-progress').hide();
        },
        
        // Show results
        showResults: function(message) {
            $('#update-results').show();
            $('.results-content').html('<div class="notice notice-success"><p>' + this.escapeHtml(message) + '</p></div>');
        },
        
        // Show error message
        showError: function(message) {
            $('#update-results').show();
            $('.results-content').html('<div class="notice notice-error"><p>' + this.escapeHtml(message) + '</p></div>');
        },
        
        // Load update logs (placeholder for future implementation)
        loadUpdateLogs: function() {
            // This would load and display recent update logs
            // Implementation can be added later if needed
        },
        
        // Escape HTML to prevent XSS
        escapeHtml: function(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize the interface
    SectorUpdates.init();
    
    // Make SectorUpdates available globally for debugging
    window.ChatGABISectorUpdates = SectorUpdates;
});

// Localization object (will be populated by WordPress)
var chatgabiSectorUpdates = chatgabiSectorUpdates || {
    nonce: '',
    ajaxUrl: ajaxurl
};

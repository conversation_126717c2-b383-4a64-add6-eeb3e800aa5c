<?php
/**
 * Fix Critical Database Schema Issues
 * 
 * Addresses:
 * 1. Missing 'prompt_content' column (should be 'prompt_text')
 * 2. Missing 'status' column in categories table
 * 3. Database schema mismatches
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Database Schema Issues</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .fix-button:hover { background: #005a87; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .schema-info { background: #f8f9fa; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0; }
    </style>
</head>
<body>

<h1>🔧 Fix Database Schema Issues</h1>

<?php
echo '<div class="info">Database schema fix started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$fixes_applied = array();
$errors_encountered = array();

// Fix 1: Check Current Database Schema
echo '<h2>🔍 Fix 1: Analyze Current Database Schema</h2>';

$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

try {
    // Check if tables exist
    $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '{$templates_table}'") === $templates_table;
    $categories_exists = $wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table;
    
    echo '<div class="schema-info">';
    echo '<strong>Table Existence Check:</strong><br>';
    echo 'Templates table (' . $templates_table . '): ' . ($templates_exists ? '✅ EXISTS' : '❌ MISSING') . '<br>';
    echo 'Categories table (' . $categories_table . '): ' . ($categories_exists ? '✅ EXISTS' : '❌ MISSING') . '<br>';
    echo '</div>';
    
    if ($templates_exists) {
        // Get templates table structure
        $templates_columns = $wpdb->get_results("DESCRIBE {$templates_table}");
        echo '<div class="schema-info">';
        echo '<strong>Templates Table Columns:</strong><br>';
        foreach ($templates_columns as $column) {
            echo '- ' . $column->Field . ' (' . $column->Type . ')<br>';
        }
        echo '</div>';
        
        // Check for specific columns
        $has_prompt_content = false;
        $has_prompt_text = false;
        $has_status = false;
        
        foreach ($templates_columns as $column) {
            if ($column->Field === 'prompt_content') $has_prompt_content = true;
            if ($column->Field === 'prompt_text') $has_prompt_text = true;
            if ($column->Field === 'status') $has_status = true;
        }
        
        echo '<div class="info">';
        echo '<strong>Column Analysis:</strong><br>';
        echo 'Has prompt_content: ' . ($has_prompt_content ? '✅ YES' : '❌ NO') . '<br>';
        echo 'Has prompt_text: ' . ($has_prompt_text ? '✅ YES' : '❌ NO') . '<br>';
        echo 'Has status: ' . ($has_status ? '✅ YES' : '❌ NO') . '<br>';
        echo '</div>';
    }
    
    if ($categories_exists) {
        // Get categories table structure
        $categories_columns = $wpdb->get_results("DESCRIBE {$categories_table}");
        echo '<div class="schema-info">';
        echo '<strong>Categories Table Columns:</strong><br>';
        foreach ($categories_columns as $column) {
            echo '- ' . $column->Field . ' (' . $column->Type . ')<br>';
        }
        echo '</div>';
        
        // Check for status column
        $categories_has_status = false;
        foreach ($categories_columns as $column) {
            if ($column->Field === 'status') $categories_has_status = true;
        }
        
        echo '<div class="info">';
        echo 'Categories has status column: ' . ($categories_has_status ? '✅ YES' : '❌ NO') . '<br>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Schema analysis error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Schema analysis: ' . $e->getMessage();
}

// Fix 2: Create/Update Templates Table with Correct Schema
echo '<h2>🔧 Fix 2: Create/Update Templates Table</h2>';

try {
    echo '<div class="info">🔧 Creating/updating templates table with correct schema...</div>';
    
    $sql = "CREATE TABLE {$templates_table} (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id int(11) NOT NULL DEFAULT 0,
        title varchar(255) NOT NULL,
        description text,
        prompt_content longtext NOT NULL,
        category_id int(11) DEFAULT NULL,
        language_code varchar(10) DEFAULT 'en',
        tags text,
        sector varchar(100),
        country varchar(10),
        placeholders text,
        is_public tinyint(1) DEFAULT 0,
        is_featured tinyint(1) DEFAULT 0,
        status varchar(20) DEFAULT 'active',
        usage_count int(11) DEFAULT 0,
        rating_average decimal(3,2) DEFAULT 0.00,
        rating_count int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY category_id (category_id),
        KEY is_public (is_public),
        KEY status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo '<div class="success">✅ Templates table created/updated with correct schema</div>';
    $fixes_applied[] = 'Updated templates table schema';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Templates table creation error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Templates table: ' . $e->getMessage();
}

// Fix 3: Create/Update Categories Table with Status Column
echo '<h2>📂 Fix 3: Create/Update Categories Table</h2>';

try {
    echo '<div class="info">🔧 Creating/updating categories table with status column...</div>';
    
    $sql = "CREATE TABLE {$categories_table} (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(100) NOT NULL,
        slug varchar(100) NOT NULL,
        description text,
        icon varchar(50) DEFAULT '📋',
        color varchar(7) DEFAULT '#667eea',
        sort_order int(11) DEFAULT 0,
        status varchar(20) DEFAULT 'active',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug),
        KEY status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo '<div class="success">✅ Categories table created/updated with status column</div>';
    $fixes_applied[] = 'Updated categories table schema';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Categories table creation error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Categories table: ' . $e->getMessage();
}

// Fix 4: Migrate Data if prompt_text Column Exists
echo '<h2>🔄 Fix 4: Data Migration (prompt_text → prompt_content)</h2>';

try {
    // Check if we need to migrate data from prompt_text to prompt_content
    $columns = $wpdb->get_results("DESCRIBE {$templates_table}");
    $has_prompt_text = false;
    $has_prompt_content = false;
    
    foreach ($columns as $column) {
        if ($column->Field === 'prompt_text') $has_prompt_text = true;
        if ($column->Field === 'prompt_content') $has_prompt_content = true;
    }
    
    if ($has_prompt_text && $has_prompt_content) {
        echo '<div class="info">🔧 Migrating data from prompt_text to prompt_content...</div>';
        
        $migration_result = $wpdb->query("
            UPDATE {$templates_table} 
            SET prompt_content = prompt_text 
            WHERE prompt_content IS NULL OR prompt_content = ''
        ");
        
        if ($migration_result !== false) {
            echo '<div class="success">✅ Data migrated successfully (' . $migration_result . ' rows updated)</div>';
            $fixes_applied[] = 'Migrated prompt_text to prompt_content';
        } else {
            echo '<div class="warning">⚠️ No data migration needed or migration failed</div>';
        }
    } else if ($has_prompt_text && !$has_prompt_content) {
        echo '<div class="info">🔧 Adding prompt_content column and migrating data...</div>';
        
        // Add prompt_content column
        $wpdb->query("ALTER TABLE {$templates_table} ADD COLUMN prompt_content longtext NOT NULL AFTER description");
        
        // Migrate data
        $wpdb->query("UPDATE {$templates_table} SET prompt_content = prompt_text");
        
        echo '<div class="success">✅ Added prompt_content column and migrated data</div>';
        $fixes_applied[] = 'Added prompt_content column and migrated data';
    } else {
        echo '<div class="info">ℹ️ No data migration needed</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Data migration error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Data migration: ' . $e->getMessage();
}

// Fix 5: Populate Sample Data
echo '<h2>📊 Fix 5: Populate Sample Data</h2>';

try {
    // Check if we have categories
    $category_count = $wpdb->get_var("SELECT COUNT(*) FROM {$categories_table}");
    
    if ($category_count == 0) {
        echo '<div class="info">🔧 Creating default categories...</div>';
        
        $default_categories = array(
            array('Business Planning', 'business-planning', 'Comprehensive business plans and strategies', '📊', '#667eea'),
            array('Marketing & Sales', 'marketing-sales', 'Marketing strategies and sales templates', '📈', '#28a745'),
            array('Financial Analysis', 'financial-analysis', 'Financial forecasts and analysis templates', '💰', '#ffc107'),
            array('Market Research', 'market-research', 'Market analysis and research templates', '🔍', '#17a2b8'),
            array('Operations', 'operations', 'Operational planning and management', '⚙️', '#6f42c1'),
            array('Legal & Compliance', 'legal-compliance', 'Legal documents and compliance templates', '⚖️', '#dc3545'),
            array('General Business', 'general-business', 'General business document templates', '📋', '#6c757d'),
            array('Custom Templates', 'custom-templates', 'User-created custom templates', '✨', '#fd7e14')
        );
        
        foreach ($default_categories as $cat) {
            $wpdb->insert($categories_table, array(
                'name' => $cat[0],
                'slug' => $cat[1],
                'description' => $cat[2],
                'icon' => $cat[3],
                'color' => $cat[4],
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ));
        }
        
        echo '<div class="success">✅ Created ' . count($default_categories) . ' default categories</div>';
        $fixes_applied[] = 'Created default categories';
    }
    
    // Check if we have templates
    $template_count = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table}");
    
    if ($template_count == 0) {
        echo '<div class="info">🔧 Creating sample templates...</div>';
        
        // Get category IDs
        $categories = $wpdb->get_results("SELECT id, slug FROM {$categories_table}", OBJECT_K);
        
        $sample_templates = array(
            array(
                'title' => 'Business Plan for African Startups',
                'description' => 'Comprehensive business plan template tailored for African entrepreneurs',
                'prompt_content' => 'Create a detailed business plan for {business_name} in {country}. Include executive summary, market analysis, financial projections, and growth strategy specific to African markets.',
                'category' => 'business-planning',
                'tags' => 'business plan,startup,africa,strategy',
                'sector' => 'General'
            ),
            array(
                'title' => 'Digital Marketing Strategy for SMEs',
                'description' => 'Digital marketing strategy template for small and medium enterprises',
                'prompt_content' => 'Develop a comprehensive digital marketing strategy for {business_name} targeting {target_audience} in {country}. Include social media, content marketing, and local SEO strategies.',
                'category' => 'marketing-sales',
                'tags' => 'marketing,digital,sme,strategy',
                'sector' => 'Technology'
            ),
            array(
                'title' => 'Financial Forecast Template',
                'description' => 'Financial forecasting template for African businesses',
                'prompt_content' => 'Create a 3-year financial forecast for {business_name} in {country}. Include revenue projections, expense analysis, cash flow, and break-even analysis considering local economic factors.',
                'category' => 'financial-analysis',
                'tags' => 'finance,forecast,planning,analysis',
                'sector' => 'Finance'
            ),
            array(
                'title' => 'Market Research Framework',
                'description' => 'Market research template for African markets',
                'prompt_content' => 'Conduct comprehensive market research for {product_service} in {country}. Analyze target demographics, competition, pricing strategies, and market opportunities specific to African consumers.',
                'category' => 'market-research',
                'tags' => 'research,market,analysis,africa',
                'sector' => 'Research'
            )
        );
        
        foreach ($sample_templates as $template) {
            $category_id = null;
            foreach ($categories as $cat) {
                if ($cat->slug === $template['category']) {
                    $category_id = $cat->id;
                    break;
                }
            }
            
            if ($category_id) {
                $wpdb->insert($templates_table, array(
                    'user_id' => 0, // System template
                    'title' => $template['title'],
                    'description' => $template['description'],
                    'prompt_content' => $template['prompt_content'],
                    'category_id' => $category_id,
                    'language_code' => 'en',
                    'tags' => $template['tags'],
                    'sector' => $template['sector'],
                    'is_public' => 1,
                    'is_featured' => 1,
                    'status' => 'active',
                    'usage_count' => rand(5, 50),
                    'rating_average' => rand(35, 50) / 10,
                    'rating_count' => rand(3, 15),
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ));
            }
        }
        
        echo '<div class="success">✅ Created ' . count($sample_templates) . ' sample templates</div>';
        $fixes_applied[] = 'Created sample templates';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Sample data creation error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Sample data: ' . $e->getMessage();
}

// Fix 6: Test Database Queries
echo '<h2>🧪 Fix 6: Test Database Queries</h2>';

try {
    echo '<div class="info">🔧 Testing fixed database queries...</div>';
    
    // Test templates query
    $test_templates = $wpdb->get_results("
        SELECT t.id, t.title, t.description, t.prompt_content, t.language_code,
               t.tags, t.sector, t.usage_count, t.rating_average, t.rating_count,
               t.is_public, t.user_id, t.created_at, t.updated_at,
               c.name as category_name, c.icon as category_icon, c.color as category_color
        FROM {$templates_table} t
        LEFT JOIN {$categories_table} c ON t.category_id = c.id
        WHERE t.status = 'active' AND t.is_public = 1
        ORDER BY t.is_featured DESC, t.usage_count DESC, t.created_at DESC
        LIMIT 5
    ");
    
    if ($test_templates) {
        echo '<div class="success">✅ Templates query working: ' . count($test_templates) . ' templates found</div>';
        $fixes_applied[] = 'Templates query tested successfully';
    } else {
        echo '<div class="warning">⚠️ Templates query returned no results</div>';
    }
    
    // Test categories query
    $test_categories = $wpdb->get_results("
        SELECT id, name, slug, description, icon, color, sort_order
        FROM {$categories_table}
        WHERE status = 'active'
        ORDER BY sort_order ASC, name ASC
        LIMIT 10
    ");
    
    if ($test_categories) {
        echo '<div class="success">✅ Categories query working: ' . count($test_categories) . ' categories found</div>';
        $fixes_applied[] = 'Categories query tested successfully';
    } else {
        echo '<div class="warning">⚠️ Categories query returned no results</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="error">❌ Database query testing error: ' . $e->getMessage() . '</div>';
    $errors_encountered[] = 'Query testing: ' . $e->getMessage();
}

// Summary
echo '<h2>📋 Database Schema Fix Summary</h2>';

if (empty($errors_encountered)) {
    echo '<div class="success">';
    echo '<h3>🎉 ALL DATABASE SCHEMA ISSUES FIXED!</h3>';
    echo '<p><strong>✅ The database schema is now correct and functional!</strong></p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Some Issues May Remain</h3>';
    echo '<ul>';
    foreach ($errors_encountered as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

if (!empty($fixes_applied)) {
    echo '<div class="success">';
    echo '<h3>🔧 Database Fixes Applied: ' . count($fixes_applied) . '</h3>';
    echo '<ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Test Actions
echo '<h2>🧪 Test Your Database Fixes</h2>';

echo '<div style="margin: 20px 0;">';
echo '<a href="' . rest_url('chatgabi/v1/templates') . '" target="_blank" class="fix-button">🌐 Test Templates API</a>';
echo '<a href="' . rest_url('chatgabi/v1/template-categories') . '" target="_blank" class="fix-button">📂 Test Categories API</a>';
echo '<a href="test-critical-fixes.php" class="fix-button">🔍 Run Critical Fixes Test</a>';
echo '<a href="javascript:window.location.reload()" class="fix-button">🔄 Re-run Schema Fix</a>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Database schema fix completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

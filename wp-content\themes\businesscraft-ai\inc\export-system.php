<?php
/**
 * Export System for BusinessCraft AI
 * 
 * Handles document exports, tracking, and history management
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize export system
 */
function businesscraft_ai_init_export_system() {
    // Create export history table
    businesscraft_ai_create_export_tables();
    
    // Add AJAX handlers
    add_action('wp_ajax_businesscraft_ai_get_export_history', 'businesscraft_ai_get_export_history');
    add_action('wp_ajax_businesscraft_ai_create_export', 'businesscraft_ai_create_export');
    add_action('wp_ajax_businesscraft_ai_download_export', 'businesscraft_ai_download_export');
    add_action('wp_ajax_businesscraft_ai_delete_export', 'businesscraft_ai_delete_export');
}
add_action('init', 'businesscraft_ai_init_export_system');

/**
 * Create export tracking tables
 */
function businesscraft_ai_create_export_tables() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'businesscraft_ai_exports';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        export_type varchar(50) NOT NULL,
        title varchar(255) NOT NULL,
        description text,
        content longtext NOT NULL,
        file_format varchar(10) NOT NULL DEFAULT 'pdf',
        file_path varchar(500),
        file_size bigint(20) DEFAULT 0,
        template_id bigint(20),
        conversation_id varchar(100),
        session_id varchar(100),
        export_settings longtext,
        status varchar(20) NOT NULL DEFAULT 'pending',
        download_count int(11) NOT NULL DEFAULT 0,
        last_downloaded datetime,
        expires_at datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY export_type (export_type),
        KEY status (status),
        KEY created_at (created_at),
        KEY expires_at (expires_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Get user export history
 */
function businesscraft_ai_get_export_history() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_export_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_exports';
    
    // Get user's export history
    $exports = $wpdb->get_results($wpdb->prepare("
        SELECT id, export_type, title, description, file_format, file_size, 
               status, download_count, created_at, last_downloaded
        FROM $table_name 
        WHERE user_id = %d 
        ORDER BY created_at DESC 
        LIMIT 50
    ", $user_id));
    
    // Process exports for frontend
    $processed_exports = array();
    foreach ($exports as $export) {
        $processed_exports[] = array(
            'id' => (int) $export->id,
            'type' => $export->export_type,
            'title' => $export->title,
            'description' => $export->description,
            'format' => $export->file_format,
            'size' => (int) $export->file_size,
            'status' => $export->status,
            'download_count' => (int) $export->download_count,
            'created_at' => $export->created_at,
            'last_downloaded' => $export->last_downloaded,
            'formatted_size' => businesscraft_ai_format_file_size($export->file_size),
            'formatted_date' => date('M j, Y g:i A', strtotime($export->created_at)),
            'can_download' => $export->status === 'completed',
            'icon' => businesscraft_ai_get_export_icon($export->export_type, $export->file_format)
        );
    }
    
    wp_send_json_success(array(
        'exports' => $processed_exports,
        'total' => count($processed_exports)
    ));
}

/**
 * Create new export
 */
function businesscraft_ai_create_export() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_export_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    // Get parameters
    $export_type = sanitize_text_field($_POST['export_type']);
    $title = sanitize_text_field($_POST['title']);
    $content = wp_kses_post($_POST['content']);
    $file_format = sanitize_text_field($_POST['file_format'] ?? 'pdf');
    $template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : null;
    $conversation_id = sanitize_text_field($_POST['conversation_id'] ?? '');
    $session_id = sanitize_text_field($_POST['session_id'] ?? '');
    
    // Validate required fields
    if (empty($export_type) || empty($title) || empty($content)) {
        wp_send_json_error('Missing required fields');
        return;
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_exports';
    
    // Insert export record
    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'export_type' => $export_type,
            'title' => $title,
            'description' => sanitize_textarea_field($_POST['description'] ?? ''),
            'content' => $content,
            'file_format' => $file_format,
            'template_id' => $template_id,
            'conversation_id' => $conversation_id,
            'session_id' => $session_id,
            'export_settings' => json_encode($_POST['settings'] ?? array()),
            'status' => 'processing',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s')
    );
    
    if ($result === false) {
        wp_send_json_error('Failed to create export record');
        return;
    }
    
    $export_id = $wpdb->insert_id;
    
    // Process export in background
    wp_schedule_single_event(time(), 'businesscraft_ai_process_export', array($export_id));
    
    wp_send_json_success(array(
        'export_id' => $export_id,
        'message' => 'Export created successfully and is being processed'
    ));
}

/**
 * Process export (background task)
 */
function businesscraft_ai_process_export($export_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_exports';
    
    // Get export record
    $export = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $export_id));
    
    if (!$export) {
        return;
    }
    
    try {
        // Generate file based on format
        $file_path = businesscraft_ai_generate_export_file($export);
        
        if ($file_path) {
            $file_size = file_exists($file_path) ? filesize($file_path) : 0;
            
            // Update export record
            $wpdb->update(
                $table_name,
                array(
                    'file_path' => $file_path,
                    'file_size' => $file_size,
                    'status' => 'completed',
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $export_id),
                array('%s', '%d', '%s', '%s'),
                array('%d')
            );
        } else {
            // Mark as failed
            $wpdb->update(
                $table_name,
                array(
                    'status' => 'failed',
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $export_id),
                array('%s', '%s'),
                array('%d')
            );
        }
    } catch (Exception $e) {
        // Mark as failed
        $wpdb->update(
            $table_name,
            array(
                'status' => 'failed',
                'updated_at' => current_time('mysql')
            ),
            array('id' => $export_id),
            array('%s', '%s'),
            array('%d')
        );
    }
}
add_action('businesscraft_ai_process_export', 'businesscraft_ai_process_export');

/**
 * Generate export file
 */
function businesscraft_ai_generate_export_file($export) {
    $upload_dir = wp_upload_dir();
    $export_dir = $upload_dir['basedir'] . '/businesscraft-ai-exports';
    
    // Create export directory if it doesn't exist
    if (!file_exists($export_dir)) {
        wp_mkdir_p($export_dir);
    }
    
    $filename = 'export_' . $export->id . '_' . time() . '.' . $export->file_format;
    $file_path = $export_dir . '/' . $filename;
    
    switch ($export->file_format) {
        case 'pdf':
            return businesscraft_ai_generate_pdf($export, $file_path);
        case 'docx':
            return businesscraft_ai_generate_docx($export, $file_path);
        case 'txt':
            return businesscraft_ai_generate_txt($export, $file_path);
        default:
            return false;
    }
}

/**
 * Generate PDF file
 */
function businesscraft_ai_generate_pdf($export, $file_path) {
    // For now, create a simple HTML to PDF conversion
    // In production, you might want to use a library like TCPDF or DOMPDF
    
    $html_content = businesscraft_ai_format_export_content($export);
    
    // Simple PDF generation (placeholder - implement with proper PDF library)
    $pdf_content = "PDF Export: " . $export->title . "\n\n" . strip_tags($export->content);
    
    if (file_put_contents($file_path, $pdf_content)) {
        return $file_path;
    }
    
    return false;
}

/**
 * Generate DOCX file
 */
function businesscraft_ai_generate_docx($export, $file_path) {
    // Placeholder for DOCX generation
    // In production, use a library like PHPWord
    
    $content = "DOCX Export: " . $export->title . "\n\n" . strip_tags($export->content);
    
    if (file_put_contents($file_path, $content)) {
        return $file_path;
    }
    
    return false;
}

/**
 * Generate TXT file
 */
function businesscraft_ai_generate_txt($export, $file_path) {
    $content = $export->title . "\n" . str_repeat("=", strlen($export->title)) . "\n\n";
    $content .= strip_tags($export->content);
    
    if (file_put_contents($file_path, $content)) {
        return $file_path;
    }
    
    return false;
}

/**
 * Format export content
 */
function businesscraft_ai_format_export_content($export) {
    $html = '<h1>' . esc_html($export->title) . '</h1>';
    
    if ($export->description) {
        $html .= '<p><em>' . esc_html($export->description) . '</em></p>';
    }
    
    $html .= '<div>' . wp_kses_post($export->content) . '</div>';
    
    $html .= '<hr>';
    $html .= '<p><small>Generated by BusinessCraft AI on ' . date('Y-m-d H:i:s') . '</small></p>';
    
    return $html;
}

/**
 * Get export icon
 */
function businesscraft_ai_get_export_icon($type, $format) {
    $icons = array(
        'business_plan' => '📋',
        'marketing_strategy' => '📈',
        'financial_forecast' => '💰',
        'sop' => '⚙️',
        'template' => '📝',
        'chat' => '💬',
        'default' => '📄'
    );
    
    return $icons[$type] ?? $icons['default'];
}

/**
 * Format file size
 */
function businesscraft_ai_format_file_size($bytes) {
    if ($bytes == 0) return '0 B';
    
    $k = 1024;
    $sizes = array('B', 'KB', 'MB', 'GB');
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

/**
 * Download export file
 */
function businesscraft_ai_download_export() {
    // Verify nonce
    if (!wp_verify_nonce($_GET['nonce'], 'businesscraft_ai_export_nonce')) {
        wp_die('Security check failed');
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_die('User not logged in');
    }
    
    $export_id = intval($_GET['export_id']);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_exports';
    
    // Get export record
    $export = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM $table_name 
        WHERE id = %d AND user_id = %d AND status = 'completed'
    ", $export_id, $user_id));
    
    if (!$export || !file_exists($export->file_path)) {
        wp_die('Export not found or not ready');
    }
    
    // Update download count
    $wpdb->update(
        $table_name,
        array(
            'download_count' => $export->download_count + 1,
            'last_downloaded' => current_time('mysql')
        ),
        array('id' => $export_id),
        array('%d', '%s'),
        array('%d')
    );
    
    // Send file
    $filename = sanitize_file_name($export->title . '.' . $export->file_format);
    
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($export->file_path));
    
    readfile($export->file_path);
    exit;
}

/**
 * Delete export
 */
function businesscraft_ai_delete_export() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'businesscraft_ai_export_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $export_id = intval($_POST['export_id']);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'businesscraft_ai_exports';
    
    // Get export record
    $export = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM $table_name 
        WHERE id = %d AND user_id = %d
    ", $export_id, $user_id));
    
    if (!$export) {
        wp_send_json_error('Export not found');
        return;
    }
    
    // Delete file if exists
    if ($export->file_path && file_exists($export->file_path)) {
        unlink($export->file_path);
    }
    
    // Delete record
    $result = $wpdb->delete(
        $table_name,
        array('id' => $export_id, 'user_id' => $user_id),
        array('%d', '%d')
    );
    
    if ($result) {
        wp_send_json_success('Export deleted successfully');
    } else {
        wp_send_json_error('Failed to delete export');
    }
}

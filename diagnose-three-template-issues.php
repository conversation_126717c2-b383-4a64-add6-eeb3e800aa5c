<?php
/**
 * Diagnose Three Specific Template Issues
 * 
 * 1. Templates Page Navigation Failure
 * 2. Admin Dashboard Template Management
 * 3. User Dashboard Template Visibility
 */

// Load WordPress
require_once('wp-load.php');

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Templates - Three Issues Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        h3 { color: #666; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .fix-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .fix-button:hover { background: #005a87; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>

<h1>🔍 ChatGABI Templates - Three Issues Diagnosis</h1>

<?php
echo '<div class="info">Diagnosis started at: ' . current_time('Y-m-d H:i:s') . '</div>';

global $wpdb;
$issues_found = array();
$fixes_needed = array();

// Issue 1: Templates Page Navigation Failure
echo '<h2>🚨 Issue 1: Templates Page Navigation Failure</h2>';

echo '<h3>1.1 Check if Templates Page Exists</h3>';
$templates_page = get_page_by_path('templates');
if ($templates_page) {
    echo '<div class="success">✅ Templates page exists (ID: ' . $templates_page->ID . ')</div>';
    
    $page_url = get_permalink($templates_page->ID);
    echo '<div class="info">📍 Page URL: <a href="' . $page_url . '" target="_blank">' . $page_url . '</a></div>';
    
    // Check page status
    echo '<div class="info">📄 Page Status: ' . $templates_page->post_status . '</div>';
    
    // Check page template
    $page_template = get_post_meta($templates_page->ID, '_wp_page_template', true);
    echo '<div class="info">🎨 Page Template: ' . ($page_template ?: 'default') . '</div>';
    
    if ($page_template !== 'page-templates.php') {
        echo '<div class="warning">⚠️ Incorrect page template! Should be "page-templates.php"</div>';
        $issues_found[] = 'Templates page has incorrect template assignment';
        $fixes_needed[] = 'Update page template to page-templates.php';
    }
    
    // Check if template file exists
    $theme_dir = get_template_directory();
    $template_file = $theme_dir . '/page-templates.php';
    
    if (file_exists($template_file)) {
        echo '<div class="success">✅ Template file exists: page-templates.php</div>';
    } else {
        echo '<div class="error">❌ Template file missing: page-templates.php</div>';
        $issues_found[] = 'Template file page-templates.php is missing';
        $fixes_needed[] = 'Create page-templates.php file';
    }
    
} else {
    echo '<div class="error">❌ Templates page does not exist</div>';
    $issues_found[] = 'Templates page does not exist';
    $fixes_needed[] = 'Create templates page with proper configuration';
}

echo '<h3>1.2 Test Page Access</h3>';
if ($templates_page) {
    $page_url = get_permalink($templates_page->ID);
    echo '<div class="info">🔗 Testing URL: ' . $page_url . '</div>';
    
    // Test if URL is reachable (basic check)
    $url_parts = parse_url($page_url);
    if ($url_parts && isset($url_parts['path'])) {
        echo '<div class="success">✅ URL structure looks valid</div>';
    } else {
        echo '<div class="error">❌ Invalid URL structure</div>';
        $issues_found[] = 'Invalid page URL structure';
    }
}

// Issue 2: Admin Dashboard Template Management
echo '<h2>⚙️ Issue 2: Admin Dashboard Template Management</h2>';

echo '<h3>2.1 Check Admin Functions</h3>';
if (function_exists('chatgabi_templates_admin_page')) {
    echo '<div class="success">✅ Admin page function exists</div>';
    
    // Check if admin page shows templates
    ob_start();
    chatgabi_templates_admin_page();
    $admin_output = ob_get_clean();
    
    if (strpos($admin_output, 'Recent Templates') !== false) {
        echo '<div class="success">✅ Admin page displays template data</div>';
    } else {
        echo '<div class="warning">⚠️ Admin page may not be displaying templates properly</div>';
        $issues_found[] = 'Admin page not displaying templates';
    }
    
    // Check for interactive elements
    if (strpos($admin_output, 'button') !== false || strpos($admin_output, 'onclick') !== false) {
        echo '<div class="success">✅ Admin page has interactive elements</div>';
    } else {
        echo '<div class="warning">⚠️ Admin page lacks interactive management features</div>';
        $issues_found[] = 'Admin page lacks template management features';
        $fixes_needed[] = 'Add edit/delete/manage buttons to admin templates';
    }
    
} else {
    echo '<div class="error">❌ Admin page function missing</div>';
    $issues_found[] = 'Admin page function missing';
    $fixes_needed[] = 'Create admin page function';
}

echo '<h3>2.2 Check Template CRUD Functions</h3>';
$crud_functions = array(
    'chatgabi_save_prompt_template' => 'Create/Update templates',
    'chatgabi_delete_template' => 'Delete templates',
    'chatgabi_duplicate_template' => 'Duplicate templates',
    'chatgabi_get_template_by_id' => 'Retrieve specific template'
);

foreach ($crud_functions as $function => $description) {
    if (function_exists($function)) {
        echo '<div class="success">✅ ' . $function . ' - ' . $description . '</div>';
    } else {
        echo '<div class="warning">⚠️ ' . $function . ' missing - ' . $description . '</div>';
        $issues_found[] = 'Missing CRUD function: ' . $function;
        $fixes_needed[] = 'Implement ' . $function . ' for ' . $description;
    }
}

// Issue 3: User Dashboard Template Visibility
echo '<h2>👤 Issue 3: User Dashboard Template Visibility</h2>';

echo '<h3>3.1 Check User Dashboard Functions</h3>';
$user_functions = array(
    'chatgabi_get_user_templates' => 'Get user-specific templates',
    'chatgabi_get_template_categories' => 'Get template categories',
    'chatgabi_display_user_templates' => 'Display templates in dashboard'
);

foreach ($user_functions as $function => $description) {
    if (function_exists($function)) {
        echo '<div class="success">✅ ' . $function . ' - ' . $description . '</div>';
    } else {
        echo '<div class="warning">⚠️ ' . $function . ' missing - ' . $description . '</div>';
        $issues_found[] = 'Missing user function: ' . $function;
        $fixes_needed[] = 'Implement ' . $function . ' for ' . $description;
    }
}

echo '<h3>3.2 Test Template Data Retrieval</h3>';
$templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
$categories_table = $wpdb->prefix . 'chatgabi_template_categories';

// Test categories
if (function_exists('chatgabi_get_template_categories')) {
    $categories = chatgabi_get_template_categories();
    if (!empty($categories)) {
        echo '<div class="success">✅ Template categories retrieved (' . count($categories) . ' categories)</div>';
    } else {
        echo '<div class="warning">⚠️ No template categories found</div>';
        $issues_found[] = 'No template categories available';
        $fixes_needed[] = 'Create default template categories';
    }
} else {
    echo '<div class="error">❌ Template categories function missing</div>';
}

// Test templates
$template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_public = 1");
if ($template_count > 0) {
    echo '<div class="success">✅ Public templates available (' . $template_count . ' templates)</div>';
} else {
    echo '<div class="warning">⚠️ No public templates found</div>';
    $issues_found[] = 'No public templates available';
    $fixes_needed[] = 'Create default public templates';
}

echo '<h3>3.3 Check User Dashboard Integration</h3>';
// Check if user dashboard files exist
$dashboard_files = array(
    'page-dashboard.php' => 'User dashboard page template',
    'inc/dashboard-functions.php' => 'Dashboard functions',
    'assets/js/dashboard.js' => 'Dashboard JavaScript'
);

foreach ($dashboard_files as $file => $description) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        echo '<div class="success">✅ ' . $file . ' exists - ' . $description . '</div>';
        
        // Check if file contains template-related code
        $file_content = file_get_contents($file_path);
        if (strpos($file_content, 'template') !== false || strpos($file_content, 'Template') !== false) {
            echo '<div class="info">📄 File contains template-related code</div>';
        } else {
            echo '<div class="warning">⚠️ File may not have template integration</div>';
            $issues_found[] = $file . ' lacks template integration';
            $fixes_needed[] = 'Add template functionality to ' . $file;
        }
    } else {
        echo '<div class="warning">⚠️ ' . $file . ' missing - ' . $description . '</div>';
    }
}

// Summary of Issues and Fixes
echo '<h2>📋 Summary of Issues Found</h2>';

if (empty($issues_found)) {
    echo '<div class="success">';
    echo '<h3>🎉 NO MAJOR ISSUES FOUND!</h3>';
    echo '<p>The template system appears to be properly configured.</p>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h3>⚠️ Issues Found: ' . count($issues_found) . '</h3>';
    echo '<ol>';
    foreach ($issues_found as $issue) {
        echo '<li>' . esc_html($issue) . '</li>';
    }
    echo '</ol>';
    echo '</div>';
}

if (!empty($fixes_needed)) {
    echo '<div class="info">';
    echo '<h3>🔧 Fixes Needed: ' . count($fixes_needed) . '</h3>';
    echo '<ol>';
    foreach ($fixes_needed as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ol>';
    echo '</div>';
}

// Quick Actions
echo '<h2>🚀 Quick Actions</h2>';

echo '<div style="margin: 20px 0;">';

if ($templates_page) {
    echo '<button class="fix-button" onclick="window.open(\'' . get_permalink($templates_page->ID) . '\', \'_blank\')">🎯 Test Templates Page</button>';
}

echo '<button class="fix-button" onclick="window.open(\'' . admin_url('admin.php?page=chatgabi-templates') . '\', \'_blank\')">⚙️ Admin Templates</button>';
echo '<button class="fix-button" onclick="window.location.href=\'fix-three-template-issues.php\'">🔧 Apply Fixes</button>';
echo '<button class="fix-button" onclick="window.location.reload()">🔄 Re-run Diagnosis</button>';
echo '</div>';

echo '<hr>';
echo '<div class="info">Diagnosis completed at: ' . current_time('Y-m-d H:i:s') . '</div>';
?>

</body>
</html>

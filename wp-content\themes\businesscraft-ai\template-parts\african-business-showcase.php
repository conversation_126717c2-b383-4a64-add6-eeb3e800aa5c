<?php
/**
 * African Business Showcase Component
 * 
 * Displays success stories and examples from African businesses
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get user's country preference
$user_id = get_current_user_id();
$user_country = get_user_meta($user_id, 'bcai_country', true) ?: 'GH';

// Get African Context Engine
if (class_exists('BusinessCraft_African_Context_Engine')) {
    $african_context = new BusinessCraft_African_Context_Engine();
    $market_examples = $african_context->generate_market_examples($user_country, 'general');
} else {
    $market_examples = array();
}

// Country display names
$country_names = array(
    'GH' => __('Ghana', 'businesscraft-ai'),
    'KE' => __('Kenya', 'businesscraft-ai'),
    'NG' => __('Nigeria', 'businesscraft-ai'),
    'ZA' => __('South Africa', 'businesscraft-ai')
);

// Country flags and colors
$country_styles = array(
    'GH' => array('flag' => '🇬🇭', 'accent' => 'country-accent-gh', 'color' => '#DC143C'),
    'KE' => array('flag' => '🇰🇪', 'accent' => 'country-accent-ke', 'color' => '#228B22'),
    'NG' => array('flag' => '🇳🇬', 'accent' => 'country-accent-ng', 'color' => '#228B22'),
    'ZA' => array('flag' => '🇿🇦', 'accent' => 'country-accent-za', 'color' => '#E95420')
);

$current_style = $country_styles[$user_country] ?? $country_styles['GH'];
?>

<div class="african-business-showcase">
    <div class="showcase-header">
        <div class="section-divider-african"></div>
        <h2 class="showcase-title">
            <?php echo $current_style['flag']; ?> 
            <?php printf(__('Success Stories from %s', 'businesscraft-ai'), $country_names[$user_country] ?? $country_names['GH']); ?>
        </h2>
        <p class="showcase-subtitle">
            <?php _e('Learn from thriving African businesses and apply AI to accelerate your growth', 'businesscraft-ai'); ?>
        </p>
    </div>

    <div class="showcase-content">
        <!-- Country Selector -->
        <div class="country-selector">
            <label for="country-select"><?php _e('Explore success stories from:', 'businesscraft-ai'); ?></label>
            <select id="country-select" class="country-select" onchange="switchCountryShowcase(this.value)">
                <?php foreach ($country_names as $code => $name): ?>
                    <option value="<?php echo esc_attr($code); ?>" <?php selected($user_country, $code); ?>>
                        <?php echo $country_styles[$code]['flag'] . ' ' . esc_html($name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Success Stories Grid -->
        <div class="success-stories-grid" id="success-stories-grid">
            <?php if (!empty($market_examples['successful_businesses'])): ?>
                
                <!-- Large Enterprises -->
                <?php if (!empty($market_examples['successful_businesses']['large_enterprises'])): ?>
                <div class="success-story-card <?php echo $current_style['accent']; ?>">
                    <div class="story-header">
                        <div class="african-business-icon">🏢</div>
                        <h3><?php _e('Large Enterprises', 'businesscraft-ai'); ?></h3>
                    </div>
                    <div class="story-content">
                        <p class="story-description">
                            <?php _e('Established companies leading their industries', 'businesscraft-ai'); ?>
                        </p>
                        <div class="business-examples">
                            <?php 
                            $enterprises = explode(', ', $market_examples['successful_businesses']['large_enterprises']);
                            foreach (array_slice($enterprises, 0, 4) as $business): 
                            ?>
                                <span class="business-tag"><?php echo esc_html(trim($business)); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <div class="story-insight ubuntu-card">
                            <p><strong><?php _e('AI Application:', 'businesscraft-ai'); ?></strong> 
                            <?php _e('Use ChatGABI to analyze market expansion strategies and optimize operations like these industry leaders.', 'businesscraft-ai'); ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- SME Success Stories -->
                <?php if (!empty($market_examples['successful_businesses']['sme_success_stories'])): ?>
                <div class="success-story-card <?php echo $current_style['accent']; ?>">
                    <div class="story-header">
                        <div class="african-business-icon">🚀</div>
                        <h3><?php _e('SME Champions', 'businesscraft-ai'); ?></h3>
                    </div>
                    <div class="story-content">
                        <p class="story-description">
                            <?php _e('Small and medium enterprises driving innovation', 'businesscraft-ai'); ?>
                        </p>
                        <div class="business-examples">
                            <?php 
                            $smes = explode(', ', $market_examples['successful_businesses']['sme_success_stories']);
                            foreach (array_slice($smes, 0, 4) as $business): 
                            ?>
                                <span class="business-tag"><?php echo esc_html(trim($business)); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <div class="story-insight ubuntu-card">
                            <p><strong><?php _e('AI Application:', 'businesscraft-ai'); ?></strong> 
                            <?php _e('Generate business plans and marketing strategies to scale your SME like these success stories.', 'businesscraft-ai'); ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Tech Startups -->
                <?php if (!empty($market_examples['successful_businesses']['tech_startups'])): ?>
                <div class="success-story-card <?php echo $current_style['accent']; ?>">
                    <div class="story-header">
                        <div class="african-business-icon">💡</div>
                        <h3><?php _e('Tech Innovators', 'businesscraft-ai'); ?></h3>
                    </div>
                    <div class="story-content">
                        <p class="story-description">
                            <?php _e('Technology startups revolutionizing African markets', 'businesscraft-ai'); ?>
                        </p>
                        <div class="business-examples">
                            <?php 
                            $startups = explode(', ', $market_examples['successful_businesses']['tech_startups']);
                            foreach (array_slice($startups, 0, 4) as $business): 
                            ?>
                                <span class="business-tag"><?php echo esc_html(trim($business)); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <div class="story-insight ubuntu-card">
                            <p><strong><?php _e('AI Application:', 'businesscraft-ai'); ?></strong> 
                            <?php _e('Create investor pitches and technical documentation to launch your tech startup.', 'businesscraft-ai'); ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Social Enterprises -->
                <?php if (!empty($market_examples['successful_businesses']['social_enterprises'])): ?>
                <div class="success-story-card <?php echo $current_style['accent']; ?>">
                    <div class="story-header">
                        <div class="african-business-icon">🤝</div>
                        <h3><?php _e('Social Impact', 'businesscraft-ai'); ?></h3>
                    </div>
                    <div class="story-content">
                        <p class="story-description">
                            <?php _e('Businesses creating positive social and environmental impact', 'businesscraft-ai'); ?>
                        </p>
                        <div class="business-examples">
                            <?php 
                            $social = explode(', ', $market_examples['successful_businesses']['social_enterprises']);
                            foreach (array_slice($social, 0, 4) as $business): 
                            ?>
                                <span class="business-tag"><?php echo esc_html(trim($business)); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <div class="story-insight ubuntu-card">
                            <p><strong><?php _e('AI Application:', 'businesscraft-ai'); ?></strong> 
                            <?php _e('Develop impact measurement frameworks and sustainability reports for your social enterprise.', 'businesscraft-ai'); ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            <?php endif; ?>
        </div>

        <!-- Cultural Wisdom Section -->
        <div class="cultural-wisdom-section">
            <div class="african-wisdom-quote">
                <div class="wisdom-text">
                    <?php 
                    $wisdom_quotes = array(
                        'GH' => __('If you want to go fast, go alone. If you want to go far, go together.', 'businesscraft-ai'),
                        'KE' => __('Harambee - Let us all pull together for the common good.', 'businesscraft-ai'),
                        'NG' => __('No condition is permanent - with determination, any situation can change.', 'businesscraft-ai'),
                        'ZA' => __('Ubuntu - I am because we are. Success comes through community.', 'businesscraft-ai')
                    );
                    echo esc_html($wisdom_quotes[$user_country] ?? $wisdom_quotes['GH']);
                    ?>
                </div>
                <div class="wisdom-attribution">
                    <?php 
                    $attributions = array(
                        'GH' => __('Ghanaian Proverb', 'businesscraft-ai'),
                        'KE' => __('Kenyan Philosophy', 'businesscraft-ai'),
                        'NG' => __('Nigerian Wisdom', 'businesscraft-ai'),
                        'ZA' => __('Ubuntu Philosophy', 'businesscraft-ai')
                    );
                    echo esc_html($attributions[$user_country] ?? $attributions['GH']);
                    ?>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="showcase-cta">
            <div class="cta-content">
                <h3><?php _e('Ready to Build Your Success Story?', 'businesscraft-ai'); ?></h3>
                <p><?php _e('Join thousands of African entrepreneurs using AI to accelerate their business growth.', 'businesscraft-ai'); ?></p>
                <div class="cta-buttons">
                    <a href="<?php echo home_url('/templates'); ?>" class="btn-primary">
                        <?php _e('🎯 Create Business Plan', 'businesscraft-ai'); ?>
                    </a>
                    <a href="<?php echo home_url(); ?>" class="btn-secondary">
                        <?php _e('💬 Start AI Chat', 'businesscraft-ai'); ?>
                    </a>
                </div>
                <div class="cta-features">
                    <div class="cta-feature">
                        <span class="feature-icon">🌍</span>
                        <span><?php _e('African Market Intelligence', 'businesscraft-ai'); ?></span>
                    </div>
                    <div class="cta-feature">
                        <span class="feature-icon">🚀</span>
                        <span><?php _e('AI-Powered Growth', 'businesscraft-ai'); ?></span>
                    </div>
                    <div class="cta-feature">
                        <span class="feature-icon">🤝</span>
                        <span><?php _e('Community Support', 'businesscraft-ai'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function switchCountryShowcase(countryCode) {
    // Show loading state
    const grid = document.getElementById('success-stories-grid');
    grid.innerHTML = '<div class="loading-showcase"><div class="loading-spinner"></div><p>Loading success stories...</p></div>';
    
    // Simulate loading and update content
    setTimeout(() => {
        // In a real implementation, this would fetch new data via AJAX
        location.reload(); // For now, reload with new country
    }, 1000);
}

// Add loading styles
const style = document.createElement('style');
style.textContent = `
    .loading-showcase {
        text-align: center;
        padding: 3rem;
        color: var(--color-text-secondary);
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--color-borders);
        border-top: 4px solid var(--color-primary-accent);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .business-tag {
        display: inline-block;
        background: var(--color-surface);
        color: var(--color-text-primary);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin: 2px;
        border: 1px solid var(--color-borders);
        font-weight: 500;
    }
    
    .business-examples {
        margin: 1rem 0;
        line-height: 1.6;
    }
    
    .story-insight {
        margin-top: 1rem;
        font-size: 0.9rem;
    }
    
    .country-select {
        padding: 8px 12px;
        border-radius: 8px;
        border: 1px solid var(--color-borders);
        background: var(--color-surface);
        color: var(--color-text-primary);
        font-size: 1rem;
        margin-left: 8px;
    }
    
    .country-selector {
        text-align: center;
        margin: 2rem 0;
        font-weight: 500;
    }
    
    .success-stories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .showcase-cta {
        margin-top: 3rem;
        text-align: center;
    }
    
    .cta-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 1.5rem 0;
        flex-wrap: wrap;
    }
    
    .cta-features {
        display: flex;
        gap: 2rem;
        justify-content: center;
        margin-top: 2rem;
        flex-wrap: wrap;
    }
    
    .cta-feature {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: var(--color-text-secondary);
    }
    
    @media (max-width: 768px) {
        .success-stories-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .cta-features {
            flex-direction: column;
            gap: 1rem;
        }
    }
`;
document.head.appendChild(style);
</script>

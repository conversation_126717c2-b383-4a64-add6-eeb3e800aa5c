# ChatGABI System Health Test - PowerShell Script
# Run this script to test the error fixing and refactoring results

Write-Host "🧪 ChatGABI System Health Test" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

$testsPasssed = 0
$testsFailed = 0

function Test-ChatGABI {
    param(
        [string]$TestName,
        [scriptblock]$TestScript
    )
    
    Write-Host "Testing $TestName... " -NoNewline
    
    try {
        $result = & $TestScript
        if ($result) {
            Write-Host "✅ PASSED" -ForegroundColor Green
            $script:testsPasssed++
            return $true
        } else {
            Write-Host "❌ FAILED" -ForegroundColor Red
            $script:testsFailed++
            return $false
        }
    } catch {
        Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        $script:testsFailed++
        return $false
    }
}

# Test 1: WordPress Basic Load
Test-ChatGABI "WordPress Basic Load" {
    $output = php -r "require_once 'wp-config.php'; require_once ABSPATH . 'wp-load.php'; echo 'OK';" 2>&1
    return $output -match "OK"
}

# Test 2: Functions.php Syntax
Test-ChatGABI "Functions.php Syntax" {
    $output = php -l wp-content/themes/businesscraft-ai/functions.php 2>&1
    return $output -match "No syntax errors"
}

# Test 3: AJAX Handlers Syntax
Test-ChatGABI "AJAX Handlers Syntax" {
    $output = php -l wp-content/themes/businesscraft-ai/inc/ajax-handlers.php 2>&1
    return $output -match "No syntax errors"
}

# Test 4: Language Functions Syntax
Test-ChatGABI "Language Functions Syntax" {
    $output = php -l wp-content/themes/businesscraft-ai/inc/language-functions.php 2>&1
    return $output -match "No syntax errors"
}

# Test 5: Template Functions Syntax
Test-ChatGABI "Template Functions Syntax" {
    $output = php -l wp-content/themes/businesscraft-ai/inc/template-functions.php 2>&1
    return $output -match "No syntax errors"
}

# Test 6: User Preference Functions Syntax
Test-ChatGABI "User Preference Functions Syntax" {
    $output = php -l wp-content/themes/businesscraft-ai/inc/user-preference-functions.php 2>&1
    return $output -match "No syntax errors"
}

# Test 7: Admin Functions Syntax
Test-ChatGABI "Admin Functions Syntax" {
    $output = php -l wp-content/themes/businesscraft-ai/inc/admin-functions.php 2>&1
    return $output -match "No syntax errors"
}

# Test 8: File Existence
Test-ChatGABI "Required Files Existence" {
    $requiredFiles = @(
        "wp-content/themes/businesscraft-ai/inc/ajax-handlers.php",
        "wp-content/themes/businesscraft-ai/inc/language-functions.php",
        "wp-content/themes/businesscraft-ai/inc/template-functions.php",
        "wp-content/themes/businesscraft-ai/inc/user-preference-functions.php",
        "wp-content/themes/businesscraft-ai/inc/admin-functions.php"
    )
    
    foreach ($file in $requiredFiles) {
        if (!(Test-Path $file)) {
            Write-Host "Missing file: $file" -ForegroundColor Red
            return $false
        }
    }
    return $true
}

# Test 9: File Line Counts
Test-ChatGABI "File Size Check" {
    $fileLimits = @{
        "wp-content/themes/businesscraft-ai/functions.php" = 1200
        "wp-content/themes/businesscraft-ai/inc/ajax-handlers.php" = 350
        "wp-content/themes/businesscraft-ai/inc/language-functions.php" = 350
        "wp-content/themes/businesscraft-ai/inc/template-functions.php" = 350
        "wp-content/themes/businesscraft-ai/inc/user-preference-functions.php" = 350
        "wp-content/themes/businesscraft-ai/inc/admin-functions.php" = 350
    }
    
    foreach ($file in $fileLimits.Keys) {
        if (Test-Path $file) {
            $lineCount = (Get-Content $file).Count
            if ($lineCount -gt $fileLimits[$file]) {
                Write-Host "File $file has $lineCount lines (max: $($fileLimits[$file]))" -ForegroundColor Red
                return $false
            }
        }
    }
    return $true
}

# Test 10: No Duplicate Functions
Test-ChatGABI "No Duplicate Functions" {
    $duplicateCheck = php -r "
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
    echo 'No duplicates found';
    " 2>&1
    
    return $duplicateCheck -match "No duplicates found" -and $duplicateCheck -notmatch "Cannot redeclare"
}

# Test 11: Core Functions Available
Test-ChatGABI "Core Functions Available" {
    $functionCheck = php -r "
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
    
    `$functions = [
        'chatgabi_get_supported_languages',
        'chatgabi_get_user_preferred_language',
        'chatgabi_load_business_plan_templates',
        'chatgabi_get_user_preferences'
    ];
    
    foreach (`$functions as `$function) {
        if (!function_exists(`$function)) {
            echo 'Missing: ' . `$function;
            exit(1);
        }
    }
    echo 'All functions available';
    " 2>&1
    
    return $functionCheck -match "All functions available"
}

# Test 12: Database Connection
Test-ChatGABI "Database Connection" {
    $dbCheck = php -r "
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-load.php';
    global `$wpdb;
    `$result = `$wpdb->get_var('SELECT 1');
    echo (`$result == 1) ? 'DB OK' : 'DB Failed';
    " 2>&1
    
    return $dbCheck -match "DB OK"
}

# Test 13: Website Accessibility
Test-ChatGABI "Website Accessibility" {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/swifmind-local/wordpress/" -TimeoutSec 10 -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

# Test 14: Admin Dashboard Accessibility
Test-ChatGABI "Admin Dashboard Accessibility" {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/swifmind-local/wordpress/wp-admin/" -TimeoutSec 10 -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

# Test 15: System Health Test Page
Test-ChatGABI "System Health Test Page" {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/swifmind-local/wordpress/test-system-health.php" -TimeoutSec 10 -ErrorAction Stop
        return $response.StatusCode -eq 200 -and $response.Content -match "System Health Test"
    } catch {
        return $false
    }
}

# Results Summary
Write-Host ""
Write-Host "📊 Test Results Summary" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host "Tests Passed: " -NoNewline
Write-Host "$testsPasssed" -ForegroundColor Green
Write-Host "Tests Failed: " -NoNewline
Write-Host "$testsFailed" -ForegroundColor Red

$successRate = [math]::Round(($testsPasssed / ($testsPasssed + $testsFailed)) * 100, 1)
Write-Host "Success Rate: $successRate%"

if ($testsFailed -eq 0) {
    Write-Host ""
    Write-Host "🎉 ALL TESTS PASSED! System is healthy." -ForegroundColor Green
    Write-Host "✅ Critical error has been resolved" -ForegroundColor Green
    Write-Host "✅ Code refactoring was successful" -ForegroundColor Green
    Write-Host "✅ ChatGABI AI is fully operational" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️ Some tests failed. Please review the issues above." -ForegroundColor Yellow
}

# System Information
Write-Host ""
Write-Host "🔧 System Information" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

# PHP Version
$phpVersion = php -r "echo PHP_VERSION;"
Write-Host "PHP Version: $phpVersion"

# File line counts
Write-Host ""
Write-Host "📁 File Line Counts:" -ForegroundColor Cyan
$filesToCount = @(
    "wp-content/themes/businesscraft-ai/functions.php",
    "wp-content/themes/businesscraft-ai/inc/ajax-handlers.php",
    "wp-content/themes/businesscraft-ai/inc/language-functions.php",
    "wp-content/themes/businesscraft-ai/inc/template-functions.php",
    "wp-content/themes/businesscraft-ai/inc/user-preference-functions.php",
    "wp-content/themes/businesscraft-ai/inc/admin-functions.php"
)

foreach ($file in $filesToCount) {
    if (Test-Path $file) {
        $lineCount = (Get-Content $file).Count
        $fileName = Split-Path $file -Leaf
        Write-Host "- $fileName`: $lineCount lines"
    }
}

# Performance check
Write-Host ""
Write-Host "⚡ Quick Performance Check:" -ForegroundColor Cyan
$startTime = Get-Date
$loadTest = php -r "
require_once 'wp-config.php';
require_once ABSPATH . 'wp-load.php';
echo 'Load complete';
" 2>&1
$endTime = Get-Date
$loadTime = ($endTime - $startTime).TotalMilliseconds

Write-Host "WordPress Load Time: $([math]::Round($loadTime, 2)) ms"

if ($loadTime -lt 1000) {
    Write-Host "✅ Load time is good" -ForegroundColor Green
} else {
    Write-Host "⚠️ Load time is slow" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "- System is ready for WhatsApp Business API Integration"
Write-Host "- Advanced Analytics Dashboard development can proceed"
Write-Host "- Mobile App development can begin"

# Exit with appropriate code
if ($testsFailed -gt 0) {
    exit 1
} else {
    exit 0
}

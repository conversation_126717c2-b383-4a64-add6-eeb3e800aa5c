<?php
/**
 * Test script for sector context logging and analytics system
 */

// Define WordPress constants
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Load WordPress
require_once(__DIR__ . '/../../../wp-load.php');

echo "<h1>BusinessCraft AI - Logging & Analytics System Test</h1>\n";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }</style>\n";

// Test 1: Check if database table exists
echo "<h2>Test 1: Database Table Check</h2>\n";

global $wpdb;
$table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "<p class='success'>✅ Database table '$table_name' exists</p>\n";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo "<p><strong>Table Structure:</strong></p>\n";
    echo "<ul>\n";
    foreach ($columns as $column) {
        echo "<li>{$column->Field} ({$column->Type}) - {$column->Null} - {$column->Key}</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<p class='error'>❌ Database table '$table_name' does not exist</p>\n";
    echo "<p>Creating table...</p>\n";
    
    // Try to create the table
    businesscraft_ai_create_sector_logs_table();
    
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    if ($table_exists) {
        echo "<p class='success'>✅ Table created successfully</p>\n";
    } else {
        echo "<p class='error'>❌ Failed to create table</p>\n";
        exit;
    }
}

// Test 2: Test logging function
echo "<h2>Test 2: Logging Function Test</h2>\n";

if (function_exists('businesscraft_ai_log_sector_context')) {
    echo "<p class='success'>✅ Logging function exists</p>\n";
    
    // Create test log entries
    $test_logs = array(
        array(
            'user_id' => 1,
            'country' => 'Ghana',
            'detected_sector' => 'Crowdfunding & Community Finance',
            'sector_context_found' => true,
            'prompt_tokens' => 350,
            'user_message' => 'I want to start a community finance platform in Ghana. What are the opportunities?'
        ),
        array(
            'user_id' => 1,
            'country' => 'Kenya',
            'detected_sector' => 'Financial Technology',
            'sector_context_found' => true,
            'prompt_tokens' => 420,
            'user_message' => 'How can I build a mobile payment solution in Kenya?'
        ),
        array(
            'user_id' => 1,
            'country' => 'Nigeria',
            'detected_sector' => null,
            'sector_context_found' => false,
            'prompt_tokens' => 180,
            'user_message' => 'Tell me about general business advice'
        ),
        array(
            'user_id' => 1,
            'country' => 'South Africa',
            'detected_sector' => 'Financial Services (Expanded)',
            'sector_context_found' => true,
            'prompt_tokens' => 380,
            'user_message' => 'I need advice on starting a financial services company in South Africa.'
        )
    );
    
    echo "<p>Creating test log entries...</p>\n";
    
    foreach ($test_logs as $i => $log) {
        businesscraft_ai_log_sector_context(
            $log['user_id'],
            $log['country'],
            $log['detected_sector'],
            $log['sector_context_found'],
            $log['prompt_tokens'],
            $log['user_message']
        );
        echo "<p class='info'>📝 Test log " . ($i + 1) . " created: {$log['country']} - " . ($log['detected_sector'] ?: 'No sector') . "</p>\n";
    }
    
    // Check if logs were created
    $log_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "<p class='success'>✅ Total logs in database: {$log_count}</p>\n";
    
} else {
    echo "<p class='error'>❌ Logging function does not exist</p>\n";
}

// Test 3: Test analytics functions
echo "<h2>Test 3: Analytics Functions Test</h2>\n";

if (function_exists('businesscraft_ai_get_sector_analytics_summary')) {
    echo "<p class='success'>✅ Analytics summary function exists</p>\n";
    
    $summary = businesscraft_ai_get_sector_analytics_summary();
    echo "<p><strong>Analytics Summary:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Total Requests: {$summary['total_requests']}</li>\n";
    echo "<li>Success Rate: {$summary['success_rate']}%</li>\n";
    echo "<li>Average Tokens: {$summary['avg_tokens']}</li>\n";
    echo "<li>Active Countries: {$summary['active_countries']}</li>\n";
    echo "</ul>\n";
} else {
    echo "<p class='error'>❌ Analytics summary function does not exist</p>\n";
}

// Test 4: Test chart data functions
echo "<h2>Test 4: Chart Data Functions Test</h2>\n";

$chart_functions = array(
    'businesscraft_ai_get_sectors_by_country_data' => 'Sectors by Country',
    'businesscraft_ai_get_success_rate_timeline' => 'Success Rate Timeline',
    'businesscraft_ai_get_token_distribution' => 'Token Distribution',
    'businesscraft_ai_get_daily_trends' => 'Daily Trends'
);

foreach ($chart_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p class='success'>✅ {$description} function exists</p>\n";
        
        $data = call_user_func($function);
        if (is_array($data) && !empty($data)) {
            echo "<p class='info'>📊 {$description} data loaded successfully</p>\n";
            if (isset($data['labels'])) {
                echo "<p>Labels count: " . count($data['labels']) . "</p>\n";
            }
            if (isset($data['datasets'])) {
                echo "<p>Datasets count: " . count($data['datasets']) . "</p>\n";
            }
        } else {
            echo "<p class='error'>❌ {$description} returned empty data</p>\n";
        }
    } else {
        echo "<p class='error'>❌ {$description} function does not exist</p>\n";
    }
}

// Test 5: Test recent logs function
echo "<h2>Test 5: Recent Logs Function Test</h2>\n";

if (function_exists('businesscraft_ai_get_recent_logs')) {
    echo "<p class='success'>✅ Recent logs function exists</p>\n";
    
    // Simulate POST data for the function
    $_POST['page'] = 1;
    $_POST['country_filter'] = '';
    $_POST['sector_filter'] = '';
    $_POST['success_filter'] = '';
    
    $recent_logs = businesscraft_ai_get_recent_logs();
    
    if (is_array($recent_logs) && isset($recent_logs['logs'])) {
        echo "<p class='success'>✅ Recent logs data loaded successfully</p>\n";
        echo "<p>Total items: {$recent_logs['total_items']}</p>\n";
        echo "<p>Total pages: {$recent_logs['total_pages']}</p>\n";
        echo "<p>Current page: {$recent_logs['current_page']}</p>\n";
        echo "<p>Logs on this page: " . count($recent_logs['logs']) . "</p>\n";
        
        if (!empty($recent_logs['logs'])) {
            echo "<p><strong>Sample log entry:</strong></p>\n";
            $sample_log = $recent_logs['logs'][0];
            echo "<pre>" . print_r($sample_log, true) . "</pre>\n";
        }
    } else {
        echo "<p class='error'>❌ Recent logs function returned invalid data</p>\n";
    }
} else {
    echo "<p class='error'>❌ Recent logs function does not exist</p>\n";
}

// Test 6: Test cleanup function
echo "<h2>Test 6: Cleanup Function Test</h2>\n";

if (function_exists('businesscraft_ai_cleanup_sector_logs')) {
    echo "<p class='success'>✅ Cleanup function exists</p>\n";
    
    // Check if cron job is scheduled
    $next_cleanup = wp_next_scheduled('businesscraft_ai_cleanup_sector_logs');
    if ($next_cleanup) {
        echo "<p class='success'>✅ Cleanup cron job is scheduled for: " . date('Y-m-d H:i:s', $next_cleanup) . "</p>\n";
    } else {
        echo "<p class='error'>❌ Cleanup cron job is not scheduled</p>\n";
    }
} else {
    echo "<p class='error'>❌ Cleanup function does not exist</p>\n";
}

// Test 7: Test admin page integration
echo "<h2>Test 7: Admin Integration Test</h2>\n";

if (function_exists('businesscraft_ai_add_sector_analytics_menu')) {
    echo "<p class='success'>✅ Admin menu function exists</p>\n";
} else {
    echo "<p class='error'>❌ Admin menu function does not exist</p>\n";
}

if (function_exists('businesscraft_ai_enqueue_sector_analytics_scripts')) {
    echo "<p class='success'>✅ Script enqueue function exists</p>\n";
} else {
    echo "<p class='error'>❌ Script enqueue function does not exist</p>\n";
}

// Check if assets exist
$js_file = CHATGABI_THEME_DIR . '/assets/js/admin-sector-analytics.js';
$css_file = CHATGABI_THEME_DIR . '/assets/css/admin-sector-analytics.css';

if (file_exists($js_file)) {
    echo "<p class='success'>✅ JavaScript file exists</p>\n";
} else {
    echo "<p class='error'>❌ JavaScript file missing: {$js_file}</p>\n";
}

if (file_exists($css_file)) {
    echo "<p class='success'>✅ CSS file exists</p>\n";
} else {
    echo "<p class='error'>❌ CSS file missing: {$css_file}</p>\n";
}

echo "<h2>Test Summary</h2>\n";
echo "<p class='success'>✅ Logging and Analytics System Test Complete!</p>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li>Visit the WordPress admin dashboard</li>\n";
echo "<li>Navigate to BusinessCraft AI → Sector Analytics</li>\n";
echo "<li>Verify that charts and data are displaying correctly</li>\n";
echo "<li>Test the filtering and export functionality</li>\n";
echo "<li>Make some test API calls to generate more log data</li>\n";
echo "</ul>\n";

// Clean up POST data
unset($_POST['page'], $_POST['country_filter'], $_POST['sector_filter'], $_POST['success_filter']);

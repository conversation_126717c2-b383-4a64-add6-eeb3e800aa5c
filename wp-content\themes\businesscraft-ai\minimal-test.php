<?php
/**
 * Minimal test for prompt injection
 */

echo "Starting minimal test...\n";
flush();

// Define WordPress constants
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

echo "WP_CONTENT_DIR defined\n";
flush();

// Include data loader
require_once(__DIR__ . '/inc/data-loader.php');

echo "Data loader included\n";
flush();

// Test 1: Basic function check
echo "Testing get_sector_context_by_country...\n";
flush();

$result = get_sector_context_by_country('Ghana', 'Fintech');

if ($result === null) {
    echo "❌ No result\n";
} else {
    echo "✅ Got result: " . $result['sector_name'] . "\n";
    
    // Test prompt building
    echo "Testing prompt building...\n";
    flush();
    
    $user_question = "Test question about fintech";
    
    $prompt = "You are a business advisor in Ghana for Fintech.\n\n";
    $prompt .= "SECTOR OVERVIEW:\n" . substr($result['overview'], 0, 200) . "...\n\n";
    $prompt .= "USER QUESTION: " . $user_question;
    
    echo "Prompt length: " . strlen($prompt) . " characters\n";
    echo "First 100 chars: " . substr($prompt, 0, 100) . "...\n";
}

echo "Test complete!\n";

<?php
/**
 * Template Name: BusinessCraft AI Payment Success
 *
 * This is the template for the payment success page.
 *
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

get_header(); // Loads the header.php template.
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <header class="entry-header">
                <h1 class="entry-title"><?php the_title(); ?></h1>
            </header><!-- .entry-header -->

            <div class="entry-content">
                <p><strong>Thank you for your purchase!</strong></p>
                <p>Your transaction was successful, and your BusinessCraft AI credits have been added to your account.</p>
                <p>You can now return to the <a href="<?php echo esc_url(home_url('/')); ?>">homepage</a> or continue using the <a href="<?php echo esc_url(home_url('/ai-assistant/')); ?>">AI Assistant</a>.</p>
                
                <?php
                // Optional: Add logic here to display transaction details if needed
                // For example, you could retrieve transaction reference from $_GET or $_REQUEST
                // and verify it using businesscraft_ai_verify_paystack_payment()
                ?>
            </div><!-- .entry-content -->
        </article><!-- #post-<?php the_ID(); ?> -->
    </main><!-- #main -->
</div><!-- #primary -->

<?php
get_footer(); // Loads the footer.php template.
?>

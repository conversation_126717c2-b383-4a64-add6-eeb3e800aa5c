<?php
/**
 * Admin Functions for ChatGABI AI
 * 
 * Handles all admin-related functionality including:
 * - Admin menu creation
 * - Setup and testing pages
 * - Database management
 * - System status checks
 * 
 * @package ChatGABI_AI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin menu for testing and setup
 * DISABLED: Integrated into main ChatGABI admin menu to prevent duplicates
 */
// add_action('admin_menu', 'businesscraft_ai_add_admin_menus');

function businesscraft_ai_add_admin_menus() {
    // Add main menu
    add_menu_page(
        'BusinessCraft AI Setup',
        'BusinessCraft AI',
        'manage_options',
        'businesscraft-ai-setup',
        'businesscraft_ai_setup_page',
        'dashicons-admin-tools',
        30
    );

    // Add submenu for testing opportunities
    add_submenu_page(
        'businesscraft-ai-setup',
        'Test Opportunities',
        'Test Opportunities',
        'manage_options',
        'businesscraft-ai-test-opportunities',
        'businesscraft_ai_test_opportunities_page'
    );

    // Add submenu for creating pages
    add_submenu_page(
        'businesscraft-ai-setup',
        'Create Pages',
        'Create Pages',
        'manage_options',
        'businesscraft-ai-create-pages',
        'businesscraft_ai_create_pages_page'
    );

    // Add submenu for testing AI prompt integration
    add_submenu_page(
        'businesscraft-ai-setup',
        'Test AI Prompts',
        'Test AI Prompts',
        'manage_options',
        'businesscraft-ai-test-prompts',
        'businesscraft_ai_test_prompts_page'
    );

    // Add submenu for database management
    add_submenu_page(
        'businesscraft-ai-setup',
        'Database Management',
        'Database Management',
        'manage_options',
        'businesscraft-ai-database',
        'businesscraft_ai_database_page'
    );
}

/**
 * Main setup page
 */
function businesscraft_ai_setup_page() {
    ?>
    <div class="wrap">
        <h1>BusinessCraft AI Setup</h1>
        <div class="card">
            <h2>System Status</h2>
            <p><strong>Theme:</strong> BusinessCraft AI Active</p>
            <p><strong>Data Loader:</strong> <?php echo function_exists('load_business_dataset_by_country') ? '✅ Working' : '❌ Not Found'; ?></p>
            <p><strong>Opportunity Loader:</strong> <?php echo function_exists('load_opportunities_by_country') ? '✅ Working' : '❌ Not Found'; ?></p>
            <p><strong>Sector Context:</strong> <?php echo function_exists('get_sector_context_by_country') ? '✅ Working' : '❌ Not Found'; ?></p>
            <p><strong>Analytics Table:</strong>
                <?php
                global $wpdb;
                $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
                echo $table_exists ? '✅ Working' : '❌ Missing';
                ?>
            </p>
        </div>

        <div class="card">
            <h2>Quick Actions</h2>
            <p><a href="<?php echo admin_url('admin.php?page=businesscraft-ai-database'); ?>" class="button button-primary">🛠️ Fix Database Issues</a></p>
            <p><a href="<?php echo admin_url('admin.php?page=businesscraft-ai-test-opportunities'); ?>" class="button button-primary">Test Opportunities System</a></p>
            <p><a href="<?php echo admin_url('admin.php?page=businesscraft-ai-test-prompts'); ?>" class="button button-primary">Test AI Prompts Integration</a></p>
            <p><a href="<?php echo admin_url('admin.php?page=businesscraft-ai-create-pages'); ?>" class="button button-secondary">Create Dashboard Pages</a></p>
        </div>
    </div>
    <?php
}

/**
 * Test opportunities page
 */
function businesscraft_ai_test_opportunities_page() {
    $test_results = array();

    if (isset($_POST['run_test'])) {
        // Test 1: Load opportunities by country
        $countries = ['ghana', 'kenya', 'nigeria', 'south-africa'];
        foreach ($countries as $country) {
            $opportunities = load_opportunities_by_country($country);
            $test_results[$country] = array(
                'count' => is_array($opportunities) ? count($opportunities) : 0,
                'data' => $opportunities
            );
        }

        // Test 2: Test sector context
        $test_sector = get_sector_context_by_country('ghana', 'Creative Arts & Design');
        $test_results['sector_test'] = $test_sector;
    }
    ?>
    <div class="wrap">
        <h1>Test Opportunities System</h1>

        <form method="post">
            <p>
                <input type="submit" name="run_test" class="button button-primary" value="Run All Tests" />
            </p>
        </form>

        <?php if (!empty($test_results)): ?>
        <div class="card">
            <h2>Test Results</h2>

            <h3>Opportunities by Country</h3>
            <?php foreach (['ghana', 'kenya', 'nigeria', 'south-africa'] as $country): ?>
                <h4><?php echo ucfirst($country); ?></h4>
                <p><strong>Count:</strong> <?php echo $test_results[$country]['count']; ?></p>
                <?php if ($test_results[$country]['count'] > 0): ?>
                    <details>
                        <summary>View First 3 Opportunities</summary>
                        <pre><?php echo htmlspecialchars(json_encode(array_slice($test_results[$country]['data'], 0, 3), JSON_PRETTY_PRINT)); ?></pre>
                    </details>
                <?php endif; ?>
            <?php endforeach; ?>

            <h3>Sector Context Test (Ghana - Creative Arts & Design)</h3>
            <?php if ($test_results['sector_test']): ?>
                <p>✅ <strong>Success!</strong> Found sector context.</p>
                <details>
                    <summary>View Sector Data</summary>
                    <pre><?php echo htmlspecialchars(json_encode($test_results['sector_test'], JSON_PRETTY_PRINT)); ?></pre>
                </details>
            <?php else: ?>
                <p>❌ <strong>Failed!</strong> No sector context found.</p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * Create pages page
 */
function businesscraft_ai_create_pages_page() {
    $message = '';

    if (isset($_POST['create_dashboard'])) {
        // Create dashboard page
        $dashboard_page = array(
            'post_title'    => 'AI Dashboard',
            'post_content'  => '[businesscraft_ai_dashboard]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'ai-dashboard'
        );

        $page_id = wp_insert_post($dashboard_page);
        if ($page_id) {
            $message .= "✅ Dashboard page created successfully! <a href='" . get_permalink($page_id) . "' target='_blank'>View Page</a><br>";
        } else {
            $message .= "❌ Failed to create dashboard page.<br>";
        }
    }

    if (isset($_POST['create_opportunities'])) {
        // Create opportunities page
        $opportunities_page = array(
            'post_title'    => 'Live Opportunities',
            'post_content'  => '[businesscraft_ai_opportunities]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'live-opportunities'
        );

        $page_id = wp_insert_post($opportunities_page);
        if ($page_id) {
            $message .= "✅ Opportunities page created successfully! <a href='" . get_permalink($page_id) . "' target='_blank'>View Page</a><br>";
        } else {
            $message .= "❌ Failed to create opportunities page.<br>";
        }
    }
    ?>
    <div class="wrap">
        <h1>Create Dashboard Pages</h1>

        <?php if ($message): ?>
        <div class="notice notice-success">
            <p><?php echo $message; ?></p>
        </div>
        <?php endif; ?>

        <div class="card">
            <h2>Create WordPress Pages</h2>
            <p>This will create WordPress pages with the appropriate shortcodes.</p>

            <form method="post">
                <p>
                    <input type="submit" name="create_dashboard" class="button button-primary" value="Create AI Dashboard Page" />
                    <span class="description">Creates a page with [businesscraft_ai_dashboard] shortcode</span>
                </p>
                <p>
                    <input type="submit" name="create_opportunities" class="button button-primary" value="Create Live Opportunities Page" />
                    <span class="description">Creates a page with [businesscraft_ai_opportunities] shortcode</span>
                </p>
            </form>
        </div>

        <div class="card">
            <h2>Existing Pages</h2>
            <?php
            $dashboard_page = get_page_by_path('ai-dashboard');
            $opportunities_page = get_page_by_path('live-opportunities');
            ?>
            <p><strong>AI Dashboard:</strong>
                <?php if ($dashboard_page): ?>
                    ✅ <a href="<?php echo get_permalink($dashboard_page->ID); ?>" target="_blank">View Page</a>
                <?php else: ?>
                    ❌ Not created yet
                <?php endif; ?>
            </p>
            <p><strong>Live Opportunities:</strong>
                <?php if ($opportunities_page): ?>
                    ✅ <a href="<?php echo get_permalink($opportunities_page->ID); ?>" target="_blank">View Page</a>
                <?php else: ?>
                    ❌ Not created yet
                <?php endif; ?>
            </p>
        </div>
    </div>
    <?php
}

/**
 * Test AI prompts page
 */
function businesscraft_ai_test_prompts_page() {
    ?>
    <div class="wrap">
        <h1>Test AI Prompts Integration</h1>
        <div class="card">
            <h2>AI Prompt Testing</h2>
            <p>This page will contain AI prompt testing functionality.</p>
            <p><em>Implementation coming soon...</em></p>
        </div>
    </div>
    <?php
}

/**
 * Database management page
 */
function businesscraft_ai_database_page() {
    ?>
    <div class="wrap">
        <h1>Database Management</h1>
        <div class="card">
            <h2>Database Operations</h2>
            <p>This page will contain database management functionality.</p>
            <p><em>Implementation coming soon...</em></p>
        </div>
    </div>
    <?php
}

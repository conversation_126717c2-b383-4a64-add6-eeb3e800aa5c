/**
 * ChatGABI Prompt Templates JavaScript
 * 
 * Handles template management interface, AJAX operations,
 * and user interactions for saved prompt templates.
 */

(function($) {
    'use strict';

    // Initialize templates manager
    function initializeTemplatesManager() {
        console.log('Initializing ChatGABI Templates Manager');
        
        // Load initial templates
        loadTemplates(true);
        
        // Set up event listeners
        setupEventListeners();
        
        console.log('Templates Manager initialized');
    }

    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Star rating clicks
        $(document).on('click', '.star', function() {
            const rating = $(this).data('rating');
            rateTemplate(window.chatgabiTemplates.currentTemplate.id, rating);
        });

        // Template card clicks
        $(document).on('click', '.template-card', function(e) {
            if (!$(e.target).hasClass('template-action')) {
                const templateId = $(this).data('template-id');
                previewTemplate(templateId);
            }
        });

        // Template action buttons
        $(document).on('click', '.template-action', function(e) {
            e.stopPropagation();
            const action = $(this).data('action');
            const templateId = $(this).closest('.template-card').data('template-id');
            
            switch (action) {
                case 'edit':
                    editTemplate(templateId);
                    break;
                case 'delete':
                    deleteTemplate(templateId);
                    break;
                case 'use':
                    useTemplate(templateId);
                    break;
                case 'share':
                    shareTemplate(templateId);
                    break;
            }
        });
    }

    /**
     * Load templates from server
     */
    function loadTemplates(reset = false) {
        if (window.chatgabiTemplates.isLoading) {
            return;
        }

        window.chatgabiTemplates.isLoading = true;

        if (reset) {
            window.chatgabiTemplates.currentPage = 0;
            window.chatgabiTemplates.hasMore = true;
            $('#templates-grid').html('<div class="loading-spinner" id="templates-loading"><div class="spinner"></div><p>Loading templates...</p></div>');
        }

        const params = {
            category: $('#category-filter').val(),
            search: $('#search-templates').val(),
            limit: 20,
            offset: window.chatgabiTemplates.currentPage * 20,
            public: window.chatgabiTemplates.currentView === 'public' || window.chatgabiTemplates.currentView === 'samples'
        };

        const sortValue = $('#sort-templates').val().split('-');
        params.orderby = sortValue[0];
        params.order = sortValue[1];

        $.ajax({
            url: chatgabiTemplatesConfig.restUrl + 'templates',
            type: 'GET',
            data: params,
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', chatgabiTemplatesConfig.restNonce);
            },
            success: function(response) {
                if (reset) {
                    $('#templates-loading').remove();
                }

                if (response && response.length > 0) {
                    renderTemplates(response, !reset);
                    window.chatgabiTemplates.currentPage++;
                    
                    if (response.length < 20) {
                        window.chatgabiTemplates.hasMore = false;
                        $('#load-more-section').hide();
                    } else {
                        $('#load-more-section').show();
                    }
                } else {
                    if (reset) {
                        $('#templates-grid').html('<div class="no-templates"><p>No templates found.</p></div>');
                    }
                    window.chatgabiTemplates.hasMore = false;
                    $('#load-more-section').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to load templates:', error);
                showMessage('error', 'Failed to load templates. Please try again.');
                
                if (reset) {
                    $('#templates-loading').remove();
                    $('#templates-grid').html('<div class="error-message"><p>Failed to load templates.</p></div>');
                }
            },
            complete: function() {
                window.chatgabiTemplates.isLoading = false;
            }
        });
    }

    /**
     * Render templates in the grid
     */
    function renderTemplates(templates, append = false) {
        let html = '';

        templates.forEach(function(template) {
            const categoryIcon = template.category_icon || '📝';
            const categoryName = template.category_name || 'Uncategorized';
            const rating = parseFloat(template.rating_average) || 0;
            const usageCount = parseInt(template.usage_count) || 0;
            const isPublic = parseInt(template.is_public) === 1;
            const authorName = template.author_name || 'Unknown';

            html += `
                <div class="template-card" data-template-id="${template.id}">
                    <div class="template-header">
                        <div class="template-category" style="color: ${template.category_color || '#007cba'}">
                            ${categoryIcon} ${categoryName}
                        </div>
                        <div class="template-actions">
                            ${window.chatgabiTemplates.currentView === 'my' ? `
                                <button class="template-action" data-action="edit" title="Edit">✏️</button>
                                <button class="template-action" data-action="delete" title="Delete">🗑️</button>
                            ` : ''}
                            <button class="template-action" data-action="use" title="Use Template">▶️</button>
                        </div>
                    </div>
                    
                    <div class="template-content">
                        <h3 class="template-title">${escapeHtml(template.title)}</h3>
                        <p class="template-description">${escapeHtml(template.description || 'No description provided.')}</p>
                        
                        <div class="template-meta">
                            <span class="meta-item">
                                <span class="meta-icon">👤</span>
                                ${window.chatgabiTemplates.currentView === 'public' ? authorName : 'You'}
                            </span>
                            <span class="meta-item">
                                <span class="meta-icon">📊</span>
                                ${usageCount} uses
                            </span>
                            <span class="meta-item">
                                <span class="meta-icon">⭐</span>
                                ${rating.toFixed(1)} (${template.rating_count || 0})
                            </span>
                            ${isPublic ? '<span class="public-badge">🌐 Public</span>' : ''}
                        </div>
                        
                        ${template.tags ? `
                            <div class="template-tags">
                                ${template.tags.split(',').map(tag => `<span class="tag">${escapeHtml(tag.trim())}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="template-footer">
                        <span class="template-date">
                            Updated ${formatDate(template.updated_at)}
                        </span>
                        <span class="template-language">
                            ${getLanguageFlag(template.language_code)} ${getLanguageName(template.language_code)}
                        </span>
                    </div>
                </div>
            `;
        });

        if (append) {
            $('#templates-grid').append(html);
        } else {
            $('#templates-grid').html(html);
        }
    }

    /**
     * Open template creation/editing modal
     */
    window.openTemplateModal = function(templateId = null) {
        if (templateId) {
            // Load template for editing
            loadTemplateForEdit(templateId);
            $('#modal-title').text('Edit Template');
        } else {
            // Clear form for new template
            clearTemplateForm();
            $('#modal-title').text('Create New Template');
        }
        
        $('#template-modal').fadeIn(300);
    };

    /**
     * Close template modal
     */
    window.closeTemplateModal = function() {
        $('#template-modal').fadeOut(300);
        clearTemplateForm();
    };

    /**
     * Save template
     */
    window.saveTemplate = function() {
        const templateData = {
            title: $('#template-title').val().trim(),
            description: $('#template-description').val().trim(),
            prompt_content: $('#template-content').val().trim(),
            category_id: $('#template-category').val(),
            tags: $('#template-tags').val().trim(),
            language_code: $('#template-language').val(),
            country_code: $('#template-country').val(),
            sector: $('#template-sector').val().trim(),
            is_public: $('#template-public').is(':checked')
        };

        // Validation
        if (!templateData.title) {
            showMessage('error', 'Template title is required.');
            return;
        }

        if (!templateData.prompt_content) {
            showMessage('error', 'Prompt content is required.');
            return;
        }

        const templateId = $('#template-id').val();
        const isEdit = templateId && templateId !== '';

        // Show loading state
        $('.btn-primary .btn-text').hide();
        $('.btn-primary .btn-loading').show();
        $('.btn-primary').prop('disabled', true);

        const ajaxConfig = {
            url: chatgabiTemplatesConfig.restUrl + 'templates' + (isEdit ? '/' + templateId : ''),
            type: isEdit ? 'PUT' : 'POST',
            data: JSON.stringify(templateData),
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', chatgabiTemplatesConfig.restNonce);
            },
            success: function(response) {
                showMessage('success', isEdit ? 'Template updated successfully!' : 'Template created successfully!');
                closeTemplateModal();
                loadTemplates(true); // Reload templates
            },
            error: function(xhr, status, error) {
                const errorMessage = xhr.responseJSON?.message || 'Failed to save template. Please try again.';
                showMessage('error', errorMessage);
            },
            complete: function() {
                // Reset button state
                $('.btn-primary .btn-text').show();
                $('.btn-primary .btn-loading').hide();
                $('.btn-primary').prop('disabled', false);
            }
        };

        $.ajax(ajaxConfig);
    };

    /**
     * Filter templates
     */
    window.filterTemplates = function() {
        loadTemplates(true);
    };

    /**
     * Debounced search
     */
    window.debounceSearch = function() {
        clearTimeout(window.chatgabiTemplates.searchTimeout);
        window.chatgabiTemplates.searchTimeout = setTimeout(function() {
            filterTemplates();
        }, 500);
    };

    /**
     * Load more templates
     */
    window.loadMoreTemplates = function() {
        if (window.chatgabiTemplates.hasMore && !window.chatgabiTemplates.isLoading) {
            loadTemplates(false);
        }
    };

    /**
     * Toggle between my templates and public templates
     */
    window.togglePublicTemplates = function() {
        if (window.chatgabiTemplates.currentView === 'my') {
            window.chatgabiTemplates.currentView = 'public';
            $('#public-toggle-text').text('My Templates');
            $('.header-actions .btn-primary').hide(); // Hide "New Template" button
        } else {
            window.chatgabiTemplates.currentView = 'my';
            $('#public-toggle-text').text('Browse Public');
            $('.header-actions .btn-primary').show(); // Show "New Template" button
        }
        
        loadTemplates(true);
    };

    /**
     * Preview template
     */
    function previewTemplate(templateId) {
        $.ajax({
            url: chatgabiTemplatesConfig.restUrl + 'templates/' + templateId,
            type: 'GET',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', chatgabiTemplatesConfig.restNonce);
            },
            success: function(template) {
                window.chatgabiTemplates.currentTemplate = template;
                
                $('#preview-title').text(template.title);
                $('#preview-description').text(template.description || 'No description provided.');
                $('#preview-content').text(template.prompt_content);
                
                // Meta information
                $('#preview-category').html(`${template.category_icon || '📝'} ${template.category_name || 'Uncategorized'}`);
                $('#preview-author').html(`👤 ${template.author_name || 'You'}`);
                $('#preview-usage').html(`📊 ${template.usage_count || 0} uses`);
                $('#preview-rating').html(`⭐ ${parseFloat(template.rating_average || 0).toFixed(1)} (${template.rating_count || 0})`);
                
                // Tags
                if (template.tags) {
                    const tagsHtml = template.tags.split(',').map(tag => 
                        `<span class="tag">${escapeHtml(tag.trim())}</span>`
                    ).join('');
                    $('#preview-tags').html(tagsHtml);
                } else {
                    $('#preview-tags').empty();
                }
                
                $('#preview-modal').fadeIn(300);
            },
            error: function(xhr, status, error) {
                showMessage('error', 'Failed to load template details.');
            }
        });
    }

    /**
     * Close preview modal
     */
    window.closePreviewModal = function() {
        $('#preview-modal').fadeOut(300);
        window.chatgabiTemplates.currentTemplate = null;
    };

    /**
     * Use template
     */
    window.useTemplate = function(templateId = null) {
        const template = templateId ? 
            { id: templateId } : 
            window.chatgabiTemplates.currentTemplate;
            
        if (!template) {
            showMessage('error', 'No template selected.');
            return;
        }

        // Track template usage
        $.ajax({
            url: chatgabiTemplatesConfig.restUrl + 'templates/' + template.id + '/use',
            type: 'POST',
            data: JSON.stringify({}),
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', chatgabiTemplatesConfig.restNonce);
            }
        });

        // Trigger event for chat interface integration
        $(document).trigger('chatgabi:templateSelected', [template]);
        
        // Close modals
        closePreviewModal();
        closeTemplateModal();
        
        showMessage('success', 'Template loaded! You can now use it in your chat.');
    };

    /**
     * Edit template
     */
    window.editTemplate = function(templateId = null) {
        const template = templateId ? 
            { id: templateId } : 
            window.chatgabiTemplates.currentTemplate;
            
        if (!template) {
            showMessage('error', 'No template selected.');
            return;
        }

        closePreviewModal();
        openTemplateModal(template.id);
    };

    /**
     * Helper functions
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    function getLanguageFlag(code) {
        const flags = {
            'en': '🇺🇸',
            'tw': '🇬🇭',
            'sw': '🇰🇪',
            'yo': '🇳🇬',
            'zu': '🇿🇦'
        };
        return flags[code] || '🌐';
    }

    function getLanguageName(code) {
        const names = {
            'en': 'English',
            'tw': 'Twi',
            'sw': 'Swahili',
            'yo': 'Yoruba',
            'zu': 'Zulu'
        };
        return names[code] || 'Unknown';
    }

    function clearTemplateForm() {
        $('#template-form')[0].reset();
        $('#template-id').val('');
    }

    function loadTemplateForEdit(templateId) {
        $.ajax({
            url: chatgabiTemplatesConfig.restUrl + 'templates/' + templateId,
            type: 'GET',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', chatgabiTemplatesConfig.restNonce);
            },
            success: function(template) {
                $('#template-id').val(template.id);
                $('#template-title').val(template.title);
                $('#template-description').val(template.description || '');
                $('#template-content').val(template.prompt_content);
                $('#template-category').val(template.category_id || '');
                $('#template-tags').val(template.tags || '');
                $('#template-language').val(template.language_code);
                $('#template-country').val(template.country_code || '');
                $('#template-sector').val(template.sector || '');
                $('#template-public').prop('checked', parseInt(template.is_public) === 1);
            },
            error: function(xhr, status, error) {
                showMessage('error', 'Failed to load template for editing.');
                closeTemplateModal();
            }
        });
    }

    function showMessage(type, message) {
        const messageClass = type === 'success' ? 'success' : 'error';
        const messageIcon = type === 'success' ? '✅' : '❌';
        
        const messageHtml = `
            <div class="template-message ${messageClass}">
                <span class="message-icon">${messageIcon}</span>
                <span class="message-text">${message}</span>
                <button class="message-close" onclick="$(this).parent().fadeOut()">&times;</button>
            </div>
        `;
        
        $('#templates-messages').html(messageHtml);
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(function() {
                $('#templates-messages .template-message').fadeOut();
            }, 5000);
        }
    }

    // Make functions available globally
    window.initializeTemplatesManager = initializeTemplatesManager;

})(jQuery);

<?php
/**
 * Check available sectors in datasets
 */

echo "Checking available sectors...\n";

// Define WordPress constants
if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', __DIR__ . '/../../');
}

// Define error_log function
if (!function_exists('error_log')) {
    function error_log($message) {
        // Silent
    }
}

// Include the functions we need
function get_latest_versioned_file($files) {
    if (empty($files)) {
        return false;
    }

    $versioned_files = [];
    $base_file = null;

    foreach ($files as $file) {
        $filename = basename($file);

        if (preg_match('/v(\d+)\.json$/', $filename, $matches)) {
            $version = (int) $matches[1];
            $versioned_files[$version] = $file;
        } elseif (preg_match('/_data\.json$/', $filename)) {
            $base_file = $file;
        }
    }

    if (!empty($versioned_files)) {
        ksort($versioned_files);
        return end($versioned_files);
    }

    if ($base_file) {
        return $base_file;
    }

    return $files[0];
}

function load_business_dataset_by_country($country) {
    $country_folders = [
        'Ghana' => 'ghana-business-data',
        'Kenya' => 'kenya-business-data',
        'Nigeria' => 'nigeria-business-data',
        'South Africa' => 'south-africa-business-data'
    ];

    $folder_name = isset($country_folders[$country]) ? $country_folders[$country] : null;
    if (!$folder_name) {
        return false;
    }

    $dataset_dir = WP_CONTENT_DIR . '/datasets/' . $folder_name . '/';
    if (!is_dir($dataset_dir)) {
        return false;
    }

    $json_files = glob($dataset_dir . '*.json');
    if (empty($json_files)) {
        return false;
    }

    $latest_file = get_latest_versioned_file($json_files);
    if (!$latest_file) {
        return false;
    }

    $file_contents = file_get_contents($latest_file);
    if ($file_contents === false) {
        return false;
    }

    $decoded_data = json_decode($file_contents, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return false;
    }

    return $decoded_data;
}

// Check sectors for each country
$countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];

foreach ($countries as $country) {
    echo "\n=== {$country} Sectors ===\n";
    
    $data = load_business_dataset_by_country($country);
    
    if ($data === false) {
        echo "❌ Failed to load data for {$country}\n";
        continue;
    }
    
    if (!isset($data['sectors']) || !is_array($data['sectors'])) {
        echo "❌ No sectors array found for {$country}\n";
        continue;
    }
    
    echo "Found " . count($data['sectors']) . " sectors:\n";
    
    foreach ($data['sectors'] as $i => $sector) {
        if (isset($sector['sector_name'])) {
            echo ($i + 1) . ". " . $sector['sector_name'] . "\n";
        } else {
            echo ($i + 1) . ". [No sector name]\n";
        }
    }
}

echo "\n=== Testing Fintech-related searches ===\n";

// Test different variations of fintech
$fintech_variations = ['Fintech', 'FinTech', 'Financial Technology', 'Finance', 'Banking', 'Payment'];

foreach ($fintech_variations as $variation) {
    echo "Testing '{$variation}' in Ghana...\n";
    
    $ghana_data = load_business_dataset_by_country('Ghana');
    if ($ghana_data && isset($ghana_data['sectors'])) {
        foreach ($ghana_data['sectors'] as $sector) {
            if (isset($sector['sector_name'])) {
                $sector_name_lower = strtolower($sector['sector_name']);
                $search_lower = strtolower($variation);
                
                if (strpos($sector_name_lower, $search_lower) !== false) {
                    echo "  ✅ Found match: " . $sector['sector_name'] . "\n";
                    break;
                }
            }
        }
    }
}

echo "\nSector check complete!\n";

<?php
/**
 * Template Name: ChatGABI Templates
 * 
 * AI-Powered Template Management Interface
 * Leverages existing OpenAI integration and African Context Engine
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Check if user is logged in
if (!is_user_logged_in()) {
    ?>
    <div class="chatgabi-templates-page">
        <div class="chatgabi-notice">
            <h2><?php _e('Login Required', 'chatgabi'); ?></h2>
            <p><?php _e('Please log in to access AI-powered business templates.', 'chatgabi'); ?></p>
            <a href="<?php echo wp_login_url(get_permalink()); ?>" class="chatgabi-btn chatgabi-btn-primary">
                <?php _e('Login', 'chatgabi'); ?>
            </a>
        </div>
    </div>
    <?php
    get_footer();
    return;
}

// Get user data for context-aware features
$user_id = get_current_user_id();
$user_profile = get_user_meta($user_id, 'bcai_profile_type', true) ?: 'sme';
$user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: '';
$user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
$user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
$user_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;

// Get template categories
$template_categories = function_exists('chatgabi_get_template_categories') ? 
    chatgabi_get_template_categories() : array();
?>

<div class="chatgabi-templates-page">
    <div class="chatgabi-templates-header">
        <div class="templates-header-content">
            <h1 class="templates-title">
                <span class="templates-icon">🚀</span>
                <?php _e('AI-Powered Business Templates', 'chatgabi'); ?>
            </h1>
            <p class="templates-subtitle">
                <?php _e('Generate professional business documents with AI assistance tailored for African markets', 'chatgabi'); ?>
            </p>
            
            <!-- User Context Display -->
            <div class="user-context-bar">
                <div class="context-item">
                    <span class="context-label"><?php _e('Profile:', 'chatgabi'); ?></span>
                    <span class="context-value profile-<?php echo esc_attr($user_profile); ?>">
                        <?php echo $user_profile === 'sme' ? __('Business Owner', 'chatgabi') : __('Creator', 'chatgabi'); ?>
                    </span>
                </div>
                <?php if ($user_industry): ?>
                <div class="context-item">
                    <span class="context-label"><?php _e('Industry:', 'chatgabi'); ?></span>
                    <span class="context-value"><?php echo esc_html($user_industry); ?></span>
                </div>
                <?php endif; ?>
                <div class="context-item">
                    <span class="context-label"><?php _e('Country:', 'chatgabi'); ?></span>
                    <span class="context-value"><?php echo esc_html(chatgabi_get_country_name($user_country)); ?></span>
                </div>
                <div class="context-item credits-display">
                    <span class="context-label"><?php _e('Credits:', 'chatgabi'); ?></span>
                    <span class="context-value credits-amount"><?php echo number_format($user_credits, 2); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Controls -->
    <div class="templates-controls">
        <div class="templates-search-bar">
            <div class="search-input-wrapper">
                <input type="text" id="template-search" placeholder="<?php _e('Search templates...', 'chatgabi'); ?>" />
                <span class="search-icon">🔍</span>
            </div>
        </div>
        
        <div class="templates-filters">
            <select id="category-filter" class="filter-select">
                <option value=""><?php _e('All Categories', 'chatgabi'); ?></option>
                <?php foreach ($template_categories as $category): ?>
                    <option value="<?php echo esc_attr($category['slug']); ?>">
                        <?php echo esc_html($category['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <select id="language-filter" class="filter-select">
                <option value=""><?php _e('All Languages', 'chatgabi'); ?></option>
                <option value="en"><?php _e('English', 'chatgabi'); ?></option>
                <option value="tw"><?php _e('Twi', 'chatgabi'); ?></option>
                <option value="sw"><?php _e('Swahili', 'chatgabi'); ?></option>
                <option value="yo"><?php _e('Yoruba', 'chatgabi'); ?></option>
                <option value="zu"><?php _e('Zulu', 'chatgabi'); ?></option>
            </select>
            
            <button id="ai-suggestions-btn" class="chatgabi-btn chatgabi-btn-secondary"
                    aria-label="<?php _e('Get AI-powered template suggestions based on your profile', 'chatgabi'); ?>"
                    aria-describedby="ai-suggestions-help">
                <span class="btn-icon" aria-hidden="true">🤖</span>
                <?php _e('AI Suggestions', 'chatgabi'); ?>
            </button>
            <div id="ai-suggestions-help" class="sr-only">
                <?php _e('This will analyze your business profile and suggest relevant templates', 'chatgabi'); ?>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="templates-loading" class="templates-loading" style="display: none;">
        <div class="loading-spinner"></div>
        <p><?php _e('Loading templates...', 'chatgabi'); ?></p>
    </div>

    <!-- AI Suggestions Panel -->
    <div id="ai-suggestions-panel" class="ai-suggestions-panel" style="display: none;"
         role="dialog" aria-labelledby="suggestions-title" aria-describedby="suggestions-description">
        <div class="suggestions-header">
            <h3 id="suggestions-title"><?php _e('AI-Powered Template Suggestions', 'chatgabi'); ?></h3>
            <button id="close-suggestions" class="close-btn" aria-label="<?php _e('Close suggestions panel', 'chatgabi'); ?>">&times;</button>
        </div>
        <div id="suggestions-description" class="sr-only">
            <?php _e('AI-generated template recommendations based on your business profile and preferences', 'chatgabi'); ?>
        </div>
        <div class="suggestions-content">
            <div class="suggestions-loading">
                <div class="loading-spinner"></div>
                <p><?php _e('Analyzing your profile for personalized suggestions...', 'chatgabi'); ?></p>
            </div>
            <div class="suggestions-results" style="display: none;"></div>
        </div>
    </div>

    <!-- Template Categories Grid -->
    <div class="templates-categories-grid">
        <?php foreach ($template_categories as $category): ?>
            <div class="category-card" data-category="<?php echo esc_attr($category['slug']); ?>">
                <div class="category-header">
                    <span class="category-icon"><?php echo esc_html($category['icon'] ?? '📋'); ?></span>
                    <h3 class="category-title"><?php echo esc_html($category['name']); ?></h3>
                </div>
                <p class="category-description"><?php echo esc_html($category['description']); ?></p>
                <div class="category-stats">
                    <span class="template-count" data-category="<?php echo esc_attr($category['slug']); ?>">
                        <?php _e('Loading...', 'chatgabi'); ?>
                    </span>
                </div>
                <button class="category-explore-btn" data-category="<?php echo esc_attr($category['slug']); ?>">
                    <?php _e('Explore Templates', 'chatgabi'); ?>
                </button>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Templates Grid -->
    <div id="templates-grid" class="templates-grid">
        <!-- Templates will be loaded here via AJAX -->
        <div class="initial-loading">
            <div class="loading-spinner"></div>
            <p><?php _e('Loading templates...', 'chatgabi'); ?></p>
        </div>
    </div>

    <!-- Fallback Templates Display (Server-side rendered) -->
    <div id="templates-fallback" class="templates-fallback" style="display: none;">
        <?php
        // Fallback: Load templates server-side if JavaScript fails
        global $wpdb;
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        $categories_table = $wpdb->prefix . 'chatgabi_template_categories';

        $fallback_templates = $wpdb->get_results("
            SELECT t.*, c.name as category_name, c.icon as category_icon
            FROM {$templates_table} t
            LEFT JOIN {$categories_table} c ON t.category_id = c.id
            WHERE t.is_public = 1 AND t.status = 'active'
            ORDER BY t.is_featured DESC, t.created_at DESC
            LIMIT 12
        ");

        if (!empty($fallback_templates)):
        ?>
            <h3><?php _e('Available Templates', 'chatgabi'); ?></h3>
            <div class="templates-grid">
                <?php foreach ($fallback_templates as $template): ?>
                    <div class="template-card fallback-card">
                        <div class="template-header">
                            <h3 class="template-title"><?php echo esc_html($template->title); ?></h3>
                            <?php if ($template->is_featured): ?>
                                <span class="featured-badge">⭐</span>
                            <?php endif; ?>
                        </div>

                        <div class="template-meta">
                            <span class="meta-badge">
                                <?php echo esc_html($template->category_icon ?: '📋'); ?>
                                <?php echo esc_html($template->category_name ?: 'General'); ?>
                            </span>
                            <span class="meta-badge"><?php echo esc_html($template->language_code ?: 'en'); ?></span>
                        </div>

                        <p class="template-description">
                            <?php echo esc_html(wp_trim_words($template->description, 20)); ?>
                        </p>

                        <div class="template-stats">
                            <span class="usage-count">
                                <?php printf(__('Used %d times', 'chatgabi'), $template->usage_count ?: 0); ?>
                            </span>
                        </div>

                        <div class="template-actions">
                            <button class="chatgabi-btn chatgabi-btn-primary" onclick="useTemplateDirectly(<?php echo $template->id; ?>)">
                                <?php _e('Use Template', 'chatgabi'); ?>
                            </button>
                            <div class="export-actions">
                                <button class="export-pdf-btn" data-template-id="<?php echo $template->id; ?>" title="<?php _e('Export as PDF', 'chatgabi'); ?>">
                                    📄 <?php _e('PDF', 'chatgabi'); ?>
                                </button>
                                <button class="export-docx-btn" data-template-id="<?php echo $template->id; ?>" title="<?php _e('Export as Word Document', 'chatgabi'); ?>">
                                    📝 <?php _e('Word', 'chatgabi'); ?>
                                </button>
                            </div>
                            <div class="advanced-actions">
                                <button class="customize-template-btn" data-template-id="<?php echo $template->id; ?>" title="<?php _e('Customize Template', 'chatgabi'); ?>">
                                    🎨 <?php _e('Customize', 'chatgabi'); ?>
                                </button>
                                <button class="share-template-btn" data-template-id="<?php echo $template->id; ?>" title="<?php _e('Share Template', 'chatgabi'); ?>">
                                    🤝 <?php _e('Share', 'chatgabi'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="no-templates-message">
                <h3><?php _e('No Templates Available', 'chatgabi'); ?></h3>
                <p><?php _e('Templates are being loaded. Please check back later.', 'chatgabi'); ?></p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Template Preview Modal -->
    <div id="template-preview-modal" class="template-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-template-title"></h3>
                <button id="close-modal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="template-preview-content">
                    <div class="template-meta">
                        <div class="meta-item">
                            <span class="meta-label"><?php _e('Category:', 'chatgabi'); ?></span>
                            <span id="modal-template-category"></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label"><?php _e('Language:', 'chatgabi'); ?></span>
                            <span id="modal-template-language"></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label"><?php _e('Sector:', 'chatgabi'); ?></span>
                            <span id="modal-template-sector"></span>
                        </div>
                    </div>
                    
                    <div class="template-description">
                        <h4><?php _e('Description', 'chatgabi'); ?></h4>
                        <p id="modal-template-description"></p>
                    </div>
                    
                    <div class="template-content">
                        <h4><?php _e('Template Content', 'chatgabi'); ?></h4>
                        <div id="modal-template-content" class="template-content-display"></div>
                    </div>
                    
                    <!-- AI Context Enhancement -->
                    <div class="ai-enhancement-section">
                        <h4><?php _e('AI Context Enhancement', 'chatgabi'); ?></h4>
                        <p class="enhancement-description">
                            <?php _e('Let AI customize this template for your specific business context', 'chatgabi'); ?>
                        </p>
                        <button id="enhance-template-btn" class="chatgabi-btn chatgabi-btn-primary">
                            <span class="btn-icon">🤖</span>
                            <?php _e('Enhance with AI', 'chatgabi'); ?>
                        </button>
                        <div id="enhancement-result" class="enhancement-result" style="display: none;"></div>
                    </div>

                    <!-- Export Section -->
                    <div class="export-section">
                        <h4><?php _e('📄 Export Options', 'chatgabi'); ?></h4>
                        <p class="export-description">
                            <?php _e('Generate professional documents from this template', 'chatgabi'); ?>
                        </p>
                        <div class="export-buttons">
                            <button class="export-pdf-btn chatgabi-btn chatgabi-btn-primary" data-template-id="">
                                📄 <?php _e('Export as PDF', 'chatgabi'); ?>
                            </button>
                            <button class="export-docx-btn chatgabi-btn chatgabi-btn-secondary" data-template-id="">
                                📝 <?php _e('Export as Word', 'chatgabi'); ?>
                            </button>
                        </div>
                        <div class="export-note">
                            <small><?php _e('Note: Export requires completed template generation', 'chatgabi'); ?></small>
                        </div>
                    </div>

                    <!-- Advanced Features Section -->
                    <div class="advanced-features-section">
                        <h4><?php _e('🚀 Advanced Features', 'chatgabi'); ?></h4>
                        <p class="features-description">
                            <?php _e('Customize, collaborate, and enhance your business templates', 'chatgabi'); ?>
                        </p>
                        <div class="advanced-features-buttons">
                            <button class="customize-template-btn chatgabi-btn chatgabi-btn-secondary" data-template-id="">
                                🎨 <?php _e('Customize Template', 'chatgabi'); ?>
                            </button>
                            <button class="share-template-btn chatgabi-btn chatgabi-btn-secondary" data-template-id="">
                                🤝 <?php _e('Share & Collaborate', 'chatgabi'); ?>
                            </button>
                            <button class="add-comment-btn chatgabi-btn chatgabi-btn-secondary" data-template-id="">
                                💬 <?php _e('Add Comment', 'chatgabi'); ?>
                            </button>
                        </div>
                        <div class="features-note">
                            <small><?php _e('Advanced features help you create professional, collaborative business documents', 'chatgabi'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="use-template-btn" class="chatgabi-btn chatgabi-btn-primary">
                    <?php _e('Use This Template', 'chatgabi'); ?>
                </button>
                <button id="save-template-btn" class="chatgabi-btn chatgabi-btn-secondary">
                    <?php _e('Save to My Templates', 'chatgabi'); ?>
                </button>
                <button id="customize-template-btn" class="chatgabi-btn chatgabi-btn-outline">
                    <?php _e('Customize', 'chatgabi'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Template Customization Modal -->
    <div id="template-customization-modal" class="template-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><?php _e('Customize Template', 'chatgabi'); ?></h3>
                <button id="close-customization-modal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <form id="template-customization-form">
                    <div class="form-group">
                        <label for="custom-template-title"><?php _e('Template Title', 'chatgabi'); ?></label>
                        <input type="text" id="custom-template-title" name="title" required />
                    </div>

                    <div class="form-group">
                        <label for="custom-template-description"><?php _e('Description', 'chatgabi'); ?></label>
                        <textarea id="custom-template-description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="custom-template-content"><?php _e('Template Content', 'chatgabi'); ?></label>
                        <textarea id="custom-template-content" name="content" rows="10" required></textarea>
                        <small class="form-help">
                            <?php _e('Use {placeholders} for dynamic content. AI will help replace these with relevant examples.', 'chatgabi'); ?>
                        </small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="custom-template-category"><?php _e('Category', 'chatgabi'); ?></label>
                            <select id="custom-template-category" name="category" required>
                                <?php foreach ($template_categories as $category): ?>
                                    <option value="<?php echo esc_attr($category['id']); ?>">
                                        <?php echo esc_html($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="custom-template-language"><?php _e('Language', 'chatgabi'); ?></label>
                            <select id="custom-template-language" name="language">
                                <option value="en"><?php _e('English', 'chatgabi'); ?></option>
                                <option value="tw"><?php _e('Twi', 'chatgabi'); ?></option>
                                <option value="sw"><?php _e('Swahili', 'chatgabi'); ?></option>
                                <option value="yo"><?php _e('Yoruba', 'chatgabi'); ?></option>
                                <option value="zu"><?php _e('Zulu', 'chatgabi'); ?></option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="custom-template-tags"><?php _e('Tags (comma-separated)', 'chatgabi'); ?></label>
                        <input type="text" id="custom-template-tags" name="tags"
                               placeholder="<?php _e('e.g., startup, technology, planning', 'chatgabi'); ?>" />
                    </div>

                    <!-- AI-Powered Enhancement Options -->
                    <div class="ai-enhancement-options">
                        <h4><?php _e('AI Enhancement Options', 'chatgabi'); ?></h4>
                        <div class="enhancement-checkboxes">
                            <label class="checkbox-label">
                                <input type="checkbox" name="enhance_placeholders" checked />
                                <?php _e('Generate relevant placeholder examples', 'chatgabi'); ?>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="add_context" checked />
                                <?php _e('Add African market context', 'chatgabi'); ?>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="industry_specific" checked />
                                <?php _e('Customize for my industry', 'chatgabi'); ?>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="save-custom-template-btn" class="chatgabi-btn chatgabi-btn-primary">
                    <span class="btn-icon">💾</span>
                    <?php _e('Save Template', 'chatgabi'); ?>
                </button>
                <button id="preview-custom-template-btn" class="chatgabi-btn chatgabi-btn-secondary">
                    <?php _e('Preview', 'chatgabi'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="template-messages" class="template-messages"></div>
</div>

<?php
// Enqueue template-specific styles and scripts properly
wp_enqueue_style('chatgabi-templates', get_template_directory_uri() . '/assets/css/templates.css', array(), CHATGABI_VERSION);
wp_enqueue_script('chatgabi-templates-interface', get_template_directory_uri() . '/assets/js/templates-interface.js',
    array('jquery'), CHATGABI_VERSION, true);

// Localize script with configuration
wp_localize_script('chatgabi-templates-interface', 'chatgabiTemplatesConfig', array(
    'restUrl' => rest_url('chatgabi/v1/'),
    'restNonce' => wp_create_nonce('wp_rest'),
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('chatgabi_templates_nonce'),
    'userId' => $user_id,
    'userProfile' => $user_profile,
    'userIndustry' => $user_industry,
    'userCountry' => $user_country,
    'userLanguage' => $user_language,
    'userCredits' => $user_credits,
    'categories' => $template_categories,
    'strings' => array(
        'loading' => __('Loading...', 'chatgabi'),
        'loadingTemplates' => __('Loading templates...', 'chatgabi'),
        'noTemplatesFound' => __('No templates found matching your criteria.', 'chatgabi'),
        'templateSaved' => __('Template saved successfully!', 'chatgabi'),
        'templateDeleted' => __('Template deleted successfully!', 'chatgabi'),
        'templateUsed' => __('Template loaded in chat interface.', 'chatgabi'),
        'aiEnhancing' => __('AI is enhancing your template...', 'chatgabi'),
        'enhancementComplete' => __('Template enhanced successfully!', 'chatgabi'),
        'insufficientCredits' => __('Insufficient credits for AI enhancement.', 'chatgabi'),
        'errorOccurred' => __('An error occurred. Please try again.', 'chatgabi'),
        'confirmDelete' => __('Are you sure you want to delete this template?', 'chatgabi'),
        'templateCount' => __('%d templates', 'chatgabi'),
        'suggestionsGenerated' => __('AI suggestions generated based on your profile.', 'chatgabi'),
        'noSuggestions' => __('No specific suggestions at this time. Try exploring different categories.', 'chatgabi')
    )
));

// Add debugging and fallback JavaScript
add_action('wp_footer', function() use ($user_id, $user_profile, $user_industry, $user_country, $user_language, $user_credits, $template_categories) {

    // No need to print scripts here as they're already enqueued above

    // Add debugging and fallback JavaScript
    ?>
    <script type="text/javascript">
    // Debug and fallback functionality
    jQuery(document).ready(function($) {
        console.log('ChatGABI Templates: Page loaded');
        console.log('Config available:', typeof chatgabiTemplatesConfig !== 'undefined');

        // Check if main script loaded
        setTimeout(function() {
            if (typeof initializeTemplatesInterface === 'undefined') {
                console.warn('ChatGABI Templates: Main script not loaded, showing fallback');
                showFallbackTemplates();
            } else {
                console.log('ChatGABI Templates: Main script loaded successfully');
            }
        }, 2000);

        // Fallback if AJAX fails after 5 seconds
        setTimeout(function() {
            if ($('#templates-grid .initial-loading').is(':visible')) {
                console.warn('ChatGABI Templates: AJAX loading timeout, showing fallback');
                showFallbackTemplates();
            }
        }, 5000);
    });

    function showFallbackTemplates() {
        jQuery('#templates-grid .initial-loading').hide();
        jQuery('#templates-fallback').show();
        console.log('ChatGABI Templates: Fallback templates displayed');
    }

    function useTemplateDirectly(templateId) {
        // Direct template usage without AJAX
        const chatUrl = '<?php echo home_url('/chat'); ?>?template=' + templateId;
        window.location.href = chatUrl;
    }

    // Enhanced error handling for category filter
    jQuery('#category-filter').on('change', function() {
        const selectedCategory = jQuery(this).val();
        console.log('Category filter changed to:', selectedCategory);

        if (typeof loadTemplates === 'function') {
            loadTemplates();
        } else {
            console.warn('loadTemplates function not available');
        }
    });

    // Enhanced error handling for search
    jQuery('#template-search').on('input', function() {
        const searchTerm = jQuery(this).val();
        console.log('Search term changed to:', searchTerm);

        if (typeof loadTemplates === 'function') {
            // Debounced search
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(function() {
                loadTemplates();
            }, 500);
        }
    });
    </script>
    <?php
});

get_footer();
?>

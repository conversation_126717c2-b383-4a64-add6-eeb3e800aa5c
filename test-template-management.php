<?php
/**
 * Test Template Management Functions
 * 
 * This script tests the ChatGABI template management system
 * to identify and fix any issues.
 */

// Load WordPress
require_once('wp-load.php');

echo "<h1>ChatGABI Template Management Test</h1>";

// Test 1: Check if functions exist
echo "<h2>1. Function Existence Test</h2>";
$functions_to_test = [
    'chatgabi_get_template_categories',
    'chatgabi_get_user_templates', 
    'chatgabi_get_user_generated_templates',
    'chatgabi_get_template_usage_stats',
    'chatgabi_create_templates_table',
    'chatgabi_get_country_name_from_code',
    'get_available_sectors_by_country'
];

foreach ($functions_to_test as $function) {
    if (function_exists($function)) {
        echo "✅ {$function} exists<br>";
    } else {
        echo "❌ {$function} missing<br>";
    }
}

// Test 2: Test template categories
echo "<h2>2. Template Categories Test</h2>";
try {
    $categories = chatgabi_get_template_categories();
    echo "✅ Template categories loaded successfully<br>";
    echo "Categories: " . implode(', ', array_keys($categories)) . "<br>";
} catch (Exception $e) {
    echo "❌ Error loading template categories: " . $e->getMessage() . "<br>";
}

// Test 3: Test user templates
echo "<h2>3. User Templates Test</h2>";
try {
    $user_templates = chatgabi_get_user_templates();
    echo "✅ User templates loaded successfully<br>";
    echo "User templates count: " . count($user_templates) . "<br>";
} catch (Exception $e) {
    echo "❌ Error loading user templates: " . $e->getMessage() . "<br>";
}

// Test 4: Test template usage stats
echo "<h2>4. Template Usage Stats Test</h2>";
try {
    $stats = chatgabi_get_template_usage_stats();
    echo "✅ Template usage stats loaded successfully<br>";
    echo "Total templates: " . $stats['total_templates'] . "<br>";
    echo "User generated: " . $stats['user_generated'] . "<br>";
    echo "Most popular: " . $stats['most_popular'] . "<br>";
    echo "Countries supported: " . $stats['countries_supported'] . "<br>";
} catch (Exception $e) {
    echo "❌ Error loading template usage stats: " . $e->getMessage() . "<br>";
}

// Test 5: Test database table creation
echo "<h2>5. Database Table Test</h2>";
try {
    $result = chatgabi_create_templates_table();
    echo "✅ Database table creation successful<br>";
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'chatgabi_generated_templates';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "✅ Table {$table_name} exists<br>";
    } else {
        echo "❌ Table {$table_name} does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error creating database table: " . $e->getMessage() . "<br>";
}

// Test 6: Test country code conversion
echo "<h2>6. Country Code Test</h2>";
$country_codes = ['GH', 'KE', 'NG', 'ZA'];
foreach ($country_codes as $code) {
    try {
        $country_name = chatgabi_get_country_name_from_code($code);
        echo "✅ {$code} -> {$country_name}<br>";
    } catch (Exception $e) {
        echo "❌ Error converting {$code}: " . $e->getMessage() . "<br>";
    }
}

// Test 7: Test sector loading
echo "<h2>7. Sector Loading Test</h2>";
$countries = ['Ghana', 'Kenya', 'Nigeria', 'South Africa'];
foreach ($countries as $country) {
    try {
        $sectors = get_available_sectors_by_country($country);
        if ($sectors && is_array($sectors)) {
            echo "✅ {$country}: " . count($sectors) . " sectors loaded<br>";
        } else {
            echo "❌ {$country}: No sectors loaded<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error loading sectors for {$country}: " . $e->getMessage() . "<br>";
    }
}

// Test 8: Test AJAX handlers
echo "<h2>8. AJAX Handlers Test</h2>";
$ajax_actions = [
    'chatgabi_get_sectors',
    'chatgabi_generate_template'
];

foreach ($ajax_actions as $action) {
    if (has_action("wp_ajax_{$action}")) {
        echo "✅ AJAX action {$action} registered<br>";
    } else {
        echo "❌ AJAX action {$action} not registered<br>";
    }
}

// Test 9: Test constants
echo "<h2>9. Constants Test</h2>";
$constants = ['CHATGABI_THEME_DIR', 'CHATGABI_THEME_URL', 'CHATGABI_VERSION'];
foreach ($constants as $constant) {
    if (defined($constant)) {
        echo "✅ {$constant} = " . constant($constant) . "<br>";
    } else {
        echo "❌ {$constant} not defined<br>";
    }
}

// Test 10: Test file permissions
echo "<h2>10. File Permissions Test</h2>";
$theme_dir = get_template_directory();
if (is_writable($theme_dir)) {
    echo "✅ Theme directory is writable<br>";
} else {
    echo "❌ Theme directory is not writable<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>If all tests pass, the template management system should work correctly.</p>";
?>

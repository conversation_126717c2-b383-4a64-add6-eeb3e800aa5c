<?php
/**
 * Debug ChatGABI Admin Menu Registration Issues
 * This script will diagnose and fix the menu registration problems
 */

// Load WordPress
require_once(dirname(__FILE__) . '/wp-config.php');

echo "<h1>🔍 ChatGABI Admin Menu Debug</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.warning { color: orange; }
.info { color: blue; }
.section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
.code-block { background: #f0f0f0; padding: 10px; border-left: 4px solid #0073aa; margin: 10px 0; font-family: monospace; }
</style>";

// Step 1: Check if files are included
echo "<div class='section'>";
echo "<h2>📁 Step 1: File Inclusion Check</h2>";

$theme_dir = get_template_directory();
$required_files = array(
    'inc/admin-dashboard.php',
    'inc/admin-analytics-extended.php'
);

foreach ($required_files as $file) {
    $full_path = $theme_dir . '/' . $file;
    if (file_exists($full_path)) {
        echo "<p class='success'>✅ File exists: {$file}</p>";
    } else {
        echo "<p class='error'>❌ File missing: {$file}</p>";
    }
}

// Check if files are included in functions.php
$functions_file = $theme_dir . '/functions.php';
if (file_exists($functions_file)) {
    $functions_content = file_get_contents($functions_file);
    
    foreach ($required_files as $file) {
        if (strpos($functions_content, $file) !== false) {
            echo "<p class='success'>✅ File included in functions.php: {$file}</p>";
        } else {
            echo "<p class='error'>❌ File NOT included in functions.php: {$file}</p>";
        }
    }
} else {
    echo "<p class='error'>❌ functions.php not found</p>";
}
echo "</div>";

// Step 2: Check function existence
echo "<div class='section'>";
echo "<h2>🔧 Step 2: Function Existence Check</h2>";

$required_functions = array(
    'chatgabi_add_admin_menu' => 'Main admin menu function',
    'chatgabi_add_engagement_analytics_menu' => 'Engagement analytics submenu function',
    'chatgabi_engagement_analytics_page' => 'Analytics page callback function',
    'chatgabi_admin_page' => 'Main admin page callback'
);

foreach ($required_functions as $function => $description) {
    if (function_exists($function)) {
        echo "<p class='success'>✅ {$description}: {$function}()</p>";
    } else {
        echo "<p class='error'>❌ Missing {$description}: {$function}()</p>";
    }
}
echo "</div>";

// Step 3: Check WordPress hooks
echo "<div class='section'>";
echo "<h2>🪝 Step 3: WordPress Hooks Check</h2>";

global $wp_filter;

// Check admin_menu hooks
if (isset($wp_filter['admin_menu'])) {
    echo "<p class='success'>✅ admin_menu hook exists</p>";
    echo "<p class='info'>📊 Number of callbacks: " . count($wp_filter['admin_menu']->callbacks) . "</p>";
    
    // Look for ChatGABI functions
    $chatgabi_hooks_found = 0;
    foreach ($wp_filter['admin_menu']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            $function_name = '';
            if (is_array($callback['function'])) {
                $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
            } elseif (is_string($callback['function'])) {
                $function_name = $callback['function'];
            }
            
            if (strpos($function_name, 'chatgabi') !== false || strpos($function_name, 'businesscraft') !== false) {
                echo "<p class='success'>✅ Found ChatGABI hook: {$function_name} (priority: {$priority})</p>";
                $chatgabi_hooks_found++;
            }
        }
    }
    
    if ($chatgabi_hooks_found === 0) {
        echo "<p class='error'>❌ No ChatGABI admin_menu hooks found</p>";
    }
} else {
    echo "<p class='error'>❌ admin_menu hook not found</p>";
}
echo "</div>";

// Step 4: Check current user capabilities
echo "<div class='section'>";
echo "<h2>👤 Step 4: User Capabilities Check</h2>";

$current_user = wp_get_current_user();
echo "<p class='info'>👤 Current user: {$current_user->display_name} (ID: {$current_user->ID})</p>";

$required_caps = array('manage_options', 'administrator');
foreach ($required_caps as $cap) {
    if (current_user_can($cap)) {
        echo "<p class='success'>✅ User has capability: {$cap}</p>";
    } else {
        echo "<p class='error'>❌ User missing capability: {$cap}</p>";
    }
}

// Check user roles
$user_roles = $current_user->roles;
echo "<p class='info'>🎭 User roles: " . implode(', ', $user_roles) . "</p>";
echo "</div>";

// Step 5: Check WordPress menu structure
echo "<div class='section'>";
echo "<h2>📋 Step 5: WordPress Menu Structure</h2>";

global $menu, $submenu;

echo "<h3>Main Menu Items:</h3>";
if (isset($menu) && is_array($menu)) {
    $chatgabi_menu_found = false;
    foreach ($menu as $menu_item) {
        if (is_array($menu_item) && isset($menu_item[2])) {
            if (strpos($menu_item[2], 'chatgabi') !== false || strpos($menu_item[0], 'ChatGABI') !== false) {
                echo "<p class='success'>✅ Found ChatGABI menu: {$menu_item[0]} ({$menu_item[2]})</p>";
                $chatgabi_menu_found = true;
            }
        }
    }
    
    if (!$chatgabi_menu_found) {
        echo "<p class='error'>❌ No ChatGABI main menu found</p>";
    }
} else {
    echo "<p class='error'>❌ WordPress menu array not available</p>";
}

echo "<h3>Submenu Items:</h3>";
if (isset($submenu) && is_array($submenu)) {
    $chatgabi_submenu_found = false;
    foreach ($submenu as $parent => $submenu_items) {
        if (strpos($parent, 'chatgabi') !== false) {
            echo "<p class='success'>✅ Found ChatGABI submenu parent: {$parent}</p>";
            foreach ($submenu_items as $submenu_item) {
                if (is_array($submenu_item) && isset($submenu_item[0])) {
                    echo "<p class='info'>  • {$submenu_item[0]} ({$submenu_item[2]})</p>";
                    if (strpos($submenu_item[2], 'engagement-analytics') !== false) {
                        echo "<p class='success'>    ✅ Engagement Analytics submenu found!</p>";
                        $chatgabi_submenu_found = true;
                    }
                }
            }
        }
    }
    
    if (!$chatgabi_submenu_found) {
        echo "<p class='error'>❌ Engagement Analytics submenu not found</p>";
    }
} else {
    echo "<p class='error'>❌ WordPress submenu array not available</p>";
}
echo "</div>";

// Step 6: Test direct function calls
echo "<div class='section'>";
echo "<h2>🧪 Step 6: Direct Function Testing</h2>";

// Test if we can call the menu functions directly
if (function_exists('chatgabi_add_admin_menu')) {
    echo "<p class='info'>🧪 Testing chatgabi_add_admin_menu()...</p>";
    try {
        // Don't actually call it as it might cause issues, just confirm it exists
        echo "<p class='success'>✅ Function is callable</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Function error: " . $e->getMessage() . "</p>";
    }
}

if (function_exists('chatgabi_add_engagement_analytics_menu')) {
    echo "<p class='info'>🧪 Testing chatgabi_add_engagement_analytics_menu()...</p>";
    try {
        echo "<p class='success'>✅ Function is callable</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Function error: " . $e->getMessage() . "</p>";
    }
}
echo "</div>";

// Step 7: Provide solutions
echo "<div class='section'>";
echo "<h2>🔧 Step 7: Recommended Solutions</h2>";

echo "<h3>🎯 Issues Identified:</h3>";
echo "<ol>";
echo "<li><strong>File Inclusion:</strong> Check if admin-analytics-extended.php is properly included</li>";
echo "<li><strong>Hook Priority:</strong> Menu hooks might be firing in wrong order</li>";
echo "<li><strong>Parent Menu:</strong> Submenu might be trying to attach to non-existent parent</li>";
echo "<li><strong>Function Loading:</strong> Functions might not be loaded when hooks fire</li>";
echo "</ol>";

echo "<h3>🛠️ Immediate Fixes:</h3>";
echo "<ol>";
echo "<li><strong>Force include files:</strong> <a href='?fix=include'>Click to force include files</a></li>";
echo "<li><strong>Re-register menus:</strong> <a href='?fix=menus'>Click to re-register menus</a></li>";
echo "<li><strong>Check parent menu:</strong> <a href='?fix=parent'>Click to verify parent menu</a></li>";
echo "<li><strong>Reset hooks:</strong> <a href='?fix=hooks'>Click to reset admin hooks</a></li>";
echo "</ol>";

// Handle fix requests
if (isset($_GET['fix'])) {
    echo "<div class='code-block'>";
    echo "<strong>Applying fix: " . $_GET['fix'] . "</strong><br>";
    
    switch ($_GET['fix']) {
        case 'include':
            // Force include the analytics file
            $analytics_file = $theme_dir . '/inc/admin-analytics-extended.php';
            if (file_exists($analytics_file)) {
                require_once $analytics_file;
                echo "✅ Forced include of admin-analytics-extended.php<br>";
            }
            break;
            
        case 'menus':
            // Try to manually register menus
            if (function_exists('chatgabi_add_admin_menu')) {
                chatgabi_add_admin_menu();
                echo "✅ Called chatgabi_add_admin_menu()<br>";
            }
            if (function_exists('chatgabi_add_engagement_analytics_menu')) {
                chatgabi_add_engagement_analytics_menu();
                echo "✅ Called chatgabi_add_engagement_analytics_menu()<br>";
            }
            break;
            
        case 'parent':
            // Check if parent menu exists
            global $menu;
            $parent_found = false;
            if (isset($menu)) {
                foreach ($menu as $menu_item) {
                    if (isset($menu_item[2]) && $menu_item[2] === 'chatgabi') {
                        $parent_found = true;
                        break;
                    }
                }
            }
            echo $parent_found ? "✅ Parent menu 'chatgabi' found<br>" : "❌ Parent menu 'chatgabi' not found<br>";
            break;
            
        case 'hooks':
            // Force re-run admin_menu hooks
            do_action('admin_menu');
            echo "✅ Re-ran admin_menu hooks<br>";
            break;
    }
    echo "</div>";
}

echo "<h3>📍 Next Steps:</h3>";
echo "<p><strong>After applying fixes, test these URLs:</strong></p>";
echo "<ul>";
echo "<li><a href='" . admin_url('tools.php?page=chatgabi') . "' target='_blank'>Main ChatGABI Dashboard</a></li>";
echo "<li><a href='" . admin_url('tools.php?page=chatgabi-engagement-analytics') . "' target='_blank'>Engagement Analytics Dashboard</a></li>";
echo "</ul>";
echo "</div>";
?>

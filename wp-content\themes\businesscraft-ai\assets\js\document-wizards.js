/**
 * Document Creation Wizards JavaScript for BusinessCraft AI
 * 
 * Handles AI-powered step-by-step document creation wizards
 * 
 * @package BusinessCraft_AI
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document Wizards functionality
    const DocumentWizards = {
        
        currentWizard: null,
        currentStep: 1,
        wizardData: {},
        
        init: function() {
            this.bindEvents();
            this.initializeWizardInterface();
        },

        bindEvents: function() {
            // Wizard selection
            $(document).on('click', '.start-wizard-btn', this.startWizard);
            
            // Step navigation
            $(document).on('click', '.wizard-next-btn', this.nextStep);
            $(document).on('click', '.wizard-prev-btn', this.previousStep);
            $(document).on('click', '.wizard-complete-btn', this.completeWizard);
            
            // AI assistance
            $(document).on('click', '.ai-assist-btn', this.requestAIAssistance);
            $(document).on('click', '.apply-ai-suggestion', this.applyAISuggestion);
            
            // Form interactions
            $(document).on('input change', '.wizard-field', this.handleFieldChange);
            $(document).on('click', '.wizard-field-help', this.showFieldHelp);
            
            // Progress saving
            $(document).on('click', '.save-progress-btn', this.saveProgress);
            
            // Exit wizard
            $(document).on('click', '.exit-wizard-btn', this.exitWizard);
            
            // Auto-save on field changes (debounced)
            $(document).on('input change', '.wizard-field', this.debounce(this.autoSave, 2000));
        },

        initializeWizardInterface: function() {
            // Check for existing wizard session
            this.loadExistingSession();
            
            // Initialize progress indicators
            this.updateProgressIndicator();
        },

        startWizard: function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const wizardType = $btn.data('wizard-type');
            
            if (!wizardType) {
                DocumentWizards.showMessage('error', 'Please select a wizard type.');
                return;
            }

            DocumentWizards.initializeWizard(wizardType);
        },

        initializeWizard: function(wizardType) {
            const $btn = $('.start-wizard-btn[data-wizard-type="' + wizardType + '"]');
            const originalText = $btn.html();
            
            $btn.prop('disabled', true).html(`
                <span class="loading-spinner"></span>
                ${businesscraftWizards.strings.processing}
            `);

            $.ajax({
                url: businesscraftWizards.restUrl + 'wizards/' + wizardType + '/start',
                method: 'POST',
                headers: {
                    'X-WP-Nonce': businesscraftWizards.nonce,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    wizard_type: wizardType
                }),
                success: function(response) {
                    if (response.success) {
                        DocumentWizards.currentWizard = response.wizard_id;
                        DocumentWizards.currentStep = response.current_step;
                        DocumentWizards.wizardData = response.wizard_data || {};
                        
                        DocumentWizards.showWizardInterface(response.config);
                        DocumentWizards.loadStep(response.current_step, response.config);
                    } else {
                        DocumentWizards.showMessage('error', response.message);
                    }
                },
                error: function() {
                    DocumentWizards.showMessage('error', businesscraftWizards.strings.errorOccurred);
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        },

        showWizardInterface: function(config) {
            // Hide wizard selection and show wizard interface
            $('.wizard-selection').hide();
            $('.wizard-interface').show();
            
            // Set wizard title and info
            $('.wizard-title').text(config.name);
            $('.wizard-description').text(config.description);
            $('.wizard-estimated-time').text(config.estimated_time);
            
            // Initialize progress bar
            const totalSteps = Object.keys(config.steps).length;
            DocumentWizards.initializeProgressBar(totalSteps);
            
            // Store config for later use
            DocumentWizards.wizardConfig = config;
        },

        initializeProgressBar: function(totalSteps) {
            const $progressContainer = $('.wizard-progress');
            let progressHtml = '<div class="progress-steps">';
            
            for (let i = 1; i <= totalSteps; i++) {
                const stepClass = i === 1 ? 'active' : '';
                progressHtml += `
                    <div class="progress-step ${stepClass}" data-step="${i}">
                        <div class="step-number">${i}</div>
                        <div class="step-connector"></div>
                    </div>
                `;
            }
            
            progressHtml += '</div>';
            progressHtml += `<div class="progress-bar"><div class="progress-fill" style="width: ${(1/totalSteps)*100}%"></div></div>`;
            
            $progressContainer.html(progressHtml);
        },

        loadStep: function(stepNumber, config) {
            const stepConfig = config.steps[stepNumber];
            
            if (!stepConfig) {
                DocumentWizards.showMessage('error', 'Invalid step configuration.');
                return;
            }

            // Update progress
            DocumentWizards.updateProgressIndicator(stepNumber);
            
            // Generate step HTML
            const stepHtml = DocumentWizards.generateStepHTML(stepNumber, stepConfig);
            
            // Load step content
            $('.wizard-step-content').html(stepHtml);
            
            // Update navigation buttons
            DocumentWizards.updateNavigationButtons(stepNumber, Object.keys(config.steps).length);
            
            // Pre-fill existing data
            DocumentWizards.populateStepData(stepNumber);
            
            // Initialize field interactions
            DocumentWizards.initializeStepFields();
        },

        generateStepHTML: function(stepNumber, stepConfig) {
            let html = `
                <div class="wizard-step" data-step="${stepNumber}">
                    <div class="step-header">
                        <h3 class="step-title">${stepConfig.title}</h3>
                        <p class="step-description">${stepConfig.description}</p>
                    </div>
                    <div class="step-fields">
            `;
            
            Object.keys(stepConfig.fields).forEach(function(fieldName) {
                const field = stepConfig.fields[fieldName];
                html += DocumentWizards.generateFieldHTML(fieldName, field);
            });
            
            html += `
                    </div>
                    <div class="step-ai-suggestions" id="ai-suggestions-${stepNumber}" style="display: none;">
                        <h4>🤖 AI Suggestions</h4>
                        <div class="ai-suggestions-content"></div>
                    </div>
                </div>
            `;
            
            return html;
        },

        generateFieldHTML: function(fieldName, fieldConfig) {
            const required = fieldConfig.required ? 'required' : '';
            const aiAssist = fieldConfig.ai_assist ? 'data-ai-assist="true"' : '';
            
            let fieldHtml = `
                <div class="wizard-field-group" data-field="${fieldName}">
                    <label for="${fieldName}" class="field-label">
                        ${fieldConfig.label}
                        ${fieldConfig.required ? '<span class="required">*</span>' : ''}
                        ${fieldConfig.ai_assist ? '<button type="button" class="ai-assist-btn" data-field="' + fieldName + '">🤖 AI Help</button>' : ''}
                    </label>
            `;
            
            switch (fieldConfig.type) {
                case 'text':
                    fieldHtml += `
                        <input type="text" 
                               id="${fieldName}" 
                               name="${fieldName}" 
                               class="wizard-field" 
                               placeholder="${fieldConfig.placeholder || ''}" 
                               ${required} 
                               ${aiAssist}>
                    `;
                    break;
                    
                case 'textarea':
                    fieldHtml += `
                        <textarea id="${fieldName}" 
                                  name="${fieldName}" 
                                  class="wizard-field" 
                                  rows="4" 
                                  placeholder="${fieldConfig.placeholder || ''}" 
                                  ${required} 
                                  ${aiAssist}></textarea>
                    `;
                    break;
                    
                case 'number':
                    fieldHtml += `
                        <input type="number" 
                               id="${fieldName}" 
                               name="${fieldName}" 
                               class="wizard-field" 
                               placeholder="${fieldConfig.placeholder || ''}" 
                               ${required} 
                               ${aiAssist}>
                    `;
                    break;
                    
                case 'select':
                    fieldHtml += `<select id="${fieldName}" name="${fieldName}" class="wizard-field" ${required} ${aiAssist}>`;
                    fieldHtml += '<option value="">Select an option...</option>';
                    
                    Object.keys(fieldConfig.options).forEach(function(optionValue) {
                        const selected = fieldConfig.default === optionValue ? 'selected' : '';
                        fieldHtml += `<option value="${optionValue}" ${selected}>${fieldConfig.options[optionValue]}</option>`;
                    });
                    
                    fieldHtml += '</select>';
                    break;
                    
                case 'checkbox_group':
                    fieldHtml += '<div class="checkbox-group">';
                    
                    Object.keys(fieldConfig.options).forEach(function(optionValue) {
                        fieldHtml += `
                            <label class="checkbox-label">
                                <input type="checkbox" 
                                       name="${fieldName}[]" 
                                       value="${optionValue}" 
                                       class="wizard-field checkbox-field">
                                ${fieldConfig.options[optionValue]}
                            </label>
                        `;
                    });
                    
                    fieldHtml += '</div>';
                    break;
            }
            
            fieldHtml += `
                    <div class="field-ai-suggestions" id="suggestions-${fieldName}" style="display: none;">
                        <div class="ai-suggestions-content"></div>
                    </div>
                </div>
            `;
            
            return fieldHtml;
        },

        updateProgressIndicator: function(currentStep) {
            if (!currentStep) currentStep = DocumentWizards.currentStep;
            
            // Update step indicators
            $('.progress-step').removeClass('active completed');
            $('.progress-step').each(function() {
                const stepNum = parseInt($(this).data('step'));
                if (stepNum < currentStep) {
                    $(this).addClass('completed');
                } else if (stepNum === currentStep) {
                    $(this).addClass('active');
                }
            });
            
            // Update progress bar
            const totalSteps = $('.progress-step').length;
            const progressPercent = (currentStep / totalSteps) * 100;
            $('.progress-fill').css('width', progressPercent + '%');
        },

        updateNavigationButtons: function(currentStep, totalSteps) {
            const $prevBtn = $('.wizard-prev-btn');
            const $nextBtn = $('.wizard-next-btn');
            const $completeBtn = $('.wizard-complete-btn');
            
            // Previous button
            if (currentStep === 1) {
                $prevBtn.hide();
            } else {
                $prevBtn.show();
            }
            
            // Next/Complete button
            if (currentStep === totalSteps) {
                $nextBtn.hide();
                $completeBtn.show();
            } else {
                $nextBtn.show();
                $completeBtn.hide();
            }
        },

        populateStepData: function(stepNumber) {
            if (DocumentWizards.wizardData.steps && DocumentWizards.wizardData.steps[stepNumber]) {
                const stepData = DocumentWizards.wizardData.steps[stepNumber];
                
                Object.keys(stepData).forEach(function(fieldName) {
                    const value = stepData[fieldName];
                    const $field = $(`[name="${fieldName}"]`);
                    
                    if ($field.length) {
                        if ($field.is(':checkbox')) {
                            if (Array.isArray(value)) {
                                value.forEach(function(val) {
                                    $(`[name="${fieldName}[]"][value="${val}"]`).prop('checked', true);
                                });
                            }
                        } else {
                            $field.val(value);
                        }
                    }
                });
            }
        },

        initializeStepFields: function() {
            // Initialize any special field behaviors
            $('.wizard-field').on('focus', function() {
                $(this).closest('.wizard-field-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.wizard-field-group').removeClass('focused');
            });
        },

        nextStep: function(e) {
            e.preventDefault();
            
            // Validate current step
            if (!DocumentWizards.validateCurrentStep()) {
                return;
            }
            
            // Collect step data
            const stepData = DocumentWizards.collectStepData();
            
            // Process step
            DocumentWizards.processStep(stepData, DocumentWizards.currentStep);
        },

        previousStep: function(e) {
            e.preventDefault();
            
            if (DocumentWizards.currentStep > 1) {
                DocumentWizards.currentStep--;
                DocumentWizards.loadStep(DocumentWizards.currentStep, DocumentWizards.wizardConfig);
            }
        },

        validateCurrentStep: function() {
            let isValid = true;
            
            $('.wizard-field[required]').each(function() {
                const $field = $(this);
                const value = $field.val();
                
                if (!value || value.trim() === '') {
                    $field.addClass('error');
                    $field.closest('.wizard-field-group').addClass('has-error');
                    isValid = false;
                } else {
                    $field.removeClass('error');
                    $field.closest('.wizard-field-group').removeClass('has-error');
                }
            });
            
            if (!isValid) {
                DocumentWizards.showMessage('error', 'Please fill in all required fields.');
            }
            
            return isValid;
        },

        collectStepData: function() {
            const stepData = {};
            
            $('.wizard-field').each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                
                if (fieldName) {
                    if ($field.is(':checkbox')) {
                        const checkboxName = fieldName.replace('[]', '');
                        if (!stepData[checkboxName]) {
                            stepData[checkboxName] = [];
                        }
                        if ($field.is(':checked')) {
                            stepData[checkboxName].push($field.val());
                        }
                    } else {
                        stepData[fieldName] = $field.val();
                    }
                }
            });
            
            return stepData;
        },

        processStep: function(stepData, currentStep) {
            const $nextBtn = $('.wizard-next-btn');
            const originalText = $nextBtn.html();
            
            $nextBtn.prop('disabled', true).html(`
                <span class="loading-spinner"></span>
                ${businesscraftWizards.strings.processing}
            `);

            $.ajax({
                url: businesscraftWizards.ajaxUrl,
                method: 'POST',
                data: {
                    action: 'wizard_step',
                    nonce: businesscraftWizards.nonce,
                    wizard_id: DocumentWizards.currentWizard,
                    step_data: stepData,
                    current_step: currentStep
                },
                success: function(response) {
                    if (response.success) {
                        // Update wizard data
                        DocumentWizards.wizardData = response.data.wizard_data;
                        
                        // Show AI suggestions if available
                        if (response.data.ai_suggestions && Object.keys(response.data.ai_suggestions).length > 0) {
                            DocumentWizards.showAISuggestions(response.data.ai_suggestions);
                        }
                        
                        // Move to next step if not final
                        if (!response.data.is_final_step) {
                            DocumentWizards.currentStep = response.data.next_step;
                            DocumentWizards.loadStep(DocumentWizards.currentStep, DocumentWizards.wizardConfig);
                        }
                        
                        DocumentWizards.showMessage('success', businesscraftWizards.strings.stepCompleted);
                    } else {
                        DocumentWizards.showMessage('error', response.data || businesscraftWizards.strings.errorOccurred);
                    }
                },
                error: function() {
                    DocumentWizards.showMessage('error', businesscraftWizards.strings.errorOccurred);
                },
                complete: function() {
                    $nextBtn.prop('disabled', false).html(originalText);
                }
            });
        },

        showMessage: function(type, message) {
            // Create or update message container
            let $message = $('.wizard-message');
            
            if (!$message.length) {
                $message = $('<div class="wizard-message"></div>');
                $('.wizard-interface').prepend($message);
            }
            
            $message
                .removeClass('success error info')
                .addClass(type)
                .text(message)
                .fadeIn();
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        },

        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        requestAIAssistance: function(e) {
            e.preventDefault();

            const $btn = $(this);
            const fieldName = $btn.data('field');
            const $field = $(`[name="${fieldName}"]`);
            const fieldValue = $field.val();

            if (!fieldValue || fieldValue.trim() === '') {
                DocumentWizards.showMessage('info', 'Please enter some content first to get AI suggestions.');
                return;
            }

            const originalText = $btn.html();
            $btn.prop('disabled', true).html('🤖 ' + businesscraftWizards.strings.aiThinking);

            // Collect current step data for context
            const stepData = DocumentWizards.collectStepData();

            $.ajax({
                url: businesscraftWizards.ajaxUrl,
                method: 'POST',
                data: {
                    action: 'wizard_step',
                    nonce: businesscraftWizards.nonce,
                    wizard_id: DocumentWizards.currentWizard,
                    step_data: stepData,
                    current_step: DocumentWizards.currentStep,
                    ai_assist_field: fieldName
                },
                success: function(response) {
                    if (response.success && response.data.ai_suggestions) {
                        DocumentWizards.showFieldAISuggestions(fieldName, response.data.ai_suggestions[fieldName]);
                    } else {
                        DocumentWizards.showMessage('info', 'No AI suggestions available at this time.');
                    }
                },
                error: function() {
                    DocumentWizards.showMessage('error', 'Failed to get AI suggestions. Please try again.');
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        },

        showFieldAISuggestions: function(fieldName, suggestions) {
            const $suggestionsContainer = $(`#suggestions-${fieldName}`);

            if (suggestions && suggestions.suggestions) {
                let suggestionsHtml = `
                    <div class="ai-suggestion-item">
                        <div class="suggestion-content">${suggestions.suggestions}</div>
                        <div class="suggestion-actions">
                            <button class="apply-ai-suggestion btn-small" data-field="${fieldName}">
                                Apply Suggestion
                            </button>
                            <button class="dismiss-suggestion btn-small">
                                Dismiss
                            </button>
                        </div>
                    </div>
                `;

                $suggestionsContainer.html(suggestionsHtml).show();
            }
        },

        applyAISuggestion: function(e) {
            e.preventDefault();

            const $btn = $(this);
            const fieldName = $btn.data('field');
            const suggestionText = $btn.closest('.ai-suggestion-item').find('.suggestion-content').text();

            // Apply suggestion to field
            const $field = $(`[name="${fieldName}"]`);
            $field.val(suggestionText);

            // Hide suggestions
            $btn.closest('.field-ai-suggestions').hide();

            DocumentWizards.showMessage('success', 'AI suggestion applied successfully!');
        },

        completeWizard: function(e) {
            e.preventDefault();

            // Validate final step
            if (!DocumentWizards.validateCurrentStep()) {
                return;
            }

            // Collect final step data
            const stepData = DocumentWizards.collectStepData();

            // Save final step first
            DocumentWizards.saveFinalStepAndComplete(stepData);
        },

        saveFinalStepAndComplete: function(stepData) {
            const $completeBtn = $('.wizard-complete-btn');
            const originalText = $completeBtn.html();

            $completeBtn.prop('disabled', true).html(`
                <span class="loading-spinner"></span>
                ${businesscraftWizards.strings.generating}
            `);

            // First save the final step
            $.ajax({
                url: businesscraftWizards.ajaxUrl,
                method: 'POST',
                data: {
                    action: 'wizard_step',
                    nonce: businesscraftWizards.nonce,
                    wizard_id: DocumentWizards.currentWizard,
                    step_data: stepData,
                    current_step: DocumentWizards.currentStep
                },
                success: function(response) {
                    if (response.success) {
                        // Now complete the wizard
                        DocumentWizards.finalizeWizard();
                    } else {
                        DocumentWizards.showMessage('error', response.data || businesscraftWizards.strings.errorOccurred);
                        $completeBtn.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    DocumentWizards.showMessage('error', businesscraftWizards.strings.errorOccurred);
                    $completeBtn.prop('disabled', false).html(originalText);
                }
            });
        },

        finalizeWizard: function() {
            $.ajax({
                url: businesscraftWizards.ajaxUrl,
                method: 'POST',
                data: {
                    action: 'complete_wizard',
                    nonce: businesscraftWizards.nonce,
                    wizard_id: DocumentWizards.currentWizard
                },
                success: function(response) {
                    if (response.success) {
                        DocumentWizards.showWizardCompletion(response.data);
                    } else {
                        DocumentWizards.showMessage('error', response.data || businesscraftWizards.strings.errorOccurred);
                    }
                },
                error: function() {
                    DocumentWizards.showMessage('error', businesscraftWizards.strings.errorOccurred);
                },
                complete: function() {
                    $('.wizard-complete-btn').prop('disabled', false).html(businesscraftWizards.strings.completeWizard);
                }
            });
        },

        showWizardCompletion: function(data) {
            // Hide wizard interface
            $('.wizard-interface').hide();

            // Show completion screen
            const completionHtml = `
                <div class="wizard-completion">
                    <div class="completion-header">
                        <div class="completion-icon">🎉</div>
                        <h2>${businesscraftWizards.strings.wizardCompleted}</h2>
                        <p>Your professional business document has been generated successfully!</p>
                    </div>

                    <div class="completion-content">
                        <div class="document-preview">
                            <h3>📄 Generated Document</h3>
                            <div class="document-content">
                                ${data.document_content ? data.document_content.substring(0, 500) + '...' : 'Document generated successfully.'}
                            </div>
                        </div>

                        <div class="completion-actions">
                            <a href="/templates" class="btn-primary">
                                📋 View in Templates
                            </a>
                            <button class="export-document-btn btn-secondary" data-template-id="${data.template_id}">
                                📄 Export Document
                            </button>
                            <button class="start-new-wizard-btn btn-secondary">
                                🚀 Create Another Document
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('.wizard-container').html(completionHtml);

            // Bind completion actions
            $('.start-new-wizard-btn').on('click', function() {
                location.reload();
            });

            $('.export-document-btn').on('click', function() {
                const templateId = $(this).data('template-id');
                // Trigger export modal if available
                if (typeof BusinessCraftPdfExport !== 'undefined') {
                    BusinessCraftPdfExport.showExportModal(templateId, 'pdf');
                } else {
                    window.location.href = '/templates';
                }
            });
        },

        autoSave: function() {
            if (DocumentWizards.currentWizard) {
                const stepData = DocumentWizards.collectStepData();

                $.ajax({
                    url: businesscraftWizards.ajaxUrl,
                    method: 'POST',
                    data: {
                        action: 'save_wizard_progress',
                        nonce: businesscraftWizards.nonce,
                        wizard_id: DocumentWizards.currentWizard,
                        step_data: stepData,
                        current_step: DocumentWizards.currentStep
                    },
                    success: function(response) {
                        if (response.success) {
                            $('.auto-save-indicator').text('✓ Auto-saved').fadeIn().delay(2000).fadeOut();
                        }
                    }
                });
            }
        },

        saveProgress: function(e) {
            e.preventDefault();

            const stepData = DocumentWizards.collectStepData();
            const $btn = $('.save-progress-btn');
            const originalText = $btn.text();

            $btn.prop('disabled', true).text(businesscraftWizards.strings.saving);

            $.ajax({
                url: businesscraftWizards.ajaxUrl,
                method: 'POST',
                data: {
                    action: 'save_wizard_progress',
                    nonce: businesscraftWizards.nonce,
                    wizard_id: DocumentWizards.currentWizard,
                    step_data: stepData,
                    current_step: DocumentWizards.currentStep
                },
                success: function(response) {
                    if (response.success) {
                        DocumentWizards.showMessage('success', 'Progress saved successfully!');
                    } else {
                        DocumentWizards.showMessage('error', 'Failed to save progress.');
                    }
                },
                error: function() {
                    DocumentWizards.showMessage('error', 'Failed to save progress.');
                },
                complete: function() {
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        },

        exitWizard: function(e) {
            e.preventDefault();

            if (confirm(businesscraftWizards.strings.confirmExit)) {
                // Auto-save before exit
                DocumentWizards.autoSave();

                // Redirect to templates or dashboard
                window.location.href = '/templates';
            }
        },

        loadExistingSession: function() {
            // Check URL parameters for wizard resumption
            const urlParams = new URLSearchParams(window.location.search);
            const resumeWizard = urlParams.get('resume_wizard');

            if (resumeWizard) {
                // Load existing wizard session
                $.ajax({
                    url: businesscraftWizards.ajaxUrl,
                    method: 'POST',
                    data: {
                        action: 'load_wizard_progress',
                        nonce: businesscraftWizards.nonce,
                        wizard_id: resumeWizard
                    },
                    success: function(response) {
                        if (response.success) {
                            DocumentWizards.currentWizard = response.data.wizard_id;
                            DocumentWizards.currentStep = response.data.current_step;
                            DocumentWizards.wizardData = response.data.wizard_data;

                            // Show wizard interface and load current step
                            DocumentWizards.showWizardInterface(response.data.config);
                            DocumentWizards.loadStep(response.data.current_step, response.data.config);
                        }
                    }
                });
            }
        },

        handleFieldChange: function() {
            const $field = $(this);
            const $group = $field.closest('.wizard-field-group');

            // Remove error state when user starts typing
            if ($field.hasClass('error')) {
                $field.removeClass('error');
                $group.removeClass('has-error');
            }

            // Add filled state for styling
            if ($field.val().trim() !== '') {
                $group.addClass('filled');
            } else {
                $group.removeClass('filled');
            }
        },

        showFieldHelp: function(e) {
            e.preventDefault();

            const $btn = $(this);
            const fieldName = $btn.data('field');

            // Show contextual help for the field
            // This could be expanded with more detailed help content
            DocumentWizards.showMessage('info', 'Field help for ' + fieldName);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        DocumentWizards.init();
    });

    // Make DocumentWizards available globally
    window.BusinessCraftDocumentWizards = DocumentWizards;

})(jQuery);

<?php
/**
 * ChatGABI WhatsApp Integration Test
 * 
 * Comprehensive test suite for WhatsApp Business API integration
 * Access: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-whatsapp-integration.php
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Ensure we're in the correct theme context
if (get_template() !== 'businesscraft-ai') {
    die('Error: This test must be run with the businesscraft-ai theme active.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI WhatsApp Integration Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #0073aa; padding-bottom: 10px; }
        h2 { color: #0073aa; margin-top: 30px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 6px; border-left: 4px solid #0073aa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f1f1f1; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .endpoint-test { margin: 10px 0; padding: 15px; background: white; border: 1px solid #ddd; border-radius: 4px; }
        .test-button { background: #0073aa; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ChatGABI WhatsApp Integration Test Suite</h1>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Theme:</strong> <?php echo get_template(); ?></p>

        <?php
        $test_results = array();
        $total_tests = 0;
        $passed_tests = 0;

        // Test 1: Check if WhatsApp integration file is loaded
        echo '<div class="test-section">';
        echo '<h2>📁 Test 1: File Loading</h2>';
        
        $whatsapp_file = get_template_directory() . '/inc/whatsapp-integration.php';
        $admin_file = get_template_directory() . '/inc/admin-whatsapp.php';
        
        if (file_exists($whatsapp_file)) {
            echo '<p class="success">✅ WhatsApp integration file exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp integration file missing</p>';
        }
        $total_tests++;
        
        if (file_exists($admin_file)) {
            echo '<p class="success">✅ WhatsApp admin file exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp admin file missing</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 2: Check if functions are loaded
        echo '<div class="test-section">';
        echo '<h2>🔧 Test 2: Function Availability</h2>';
        
        $required_functions = array(
            'chatgabi_init_whatsapp_integration',
            'chatgabi_whatsapp_webhook_handler',
            'chatgabi_get_or_create_whatsapp_user',
            'chatgabi_process_whatsapp_ai_request',
            'chatgabi_send_whatsapp_response',
            'chatgabi_detect_country_from_phone',
            'chatgabi_send_welcome_message'
        );
        
        foreach ($required_functions as $function) {
            if (function_exists($function)) {
                echo "<p class=\"success\">✅ Function exists: $function</p>";
                $passed_tests++;
            } else {
                echo "<p class=\"error\">❌ Function missing: $function</p>";
            }
            $total_tests++;
        }
        echo '</div>';

        // Test 3: Database Tables
        echo '<div class="test-section">';
        echo '<h2>🗄️ Test 3: Database Tables</h2>';
        
        global $wpdb;
        
        $whatsapp_users_table = $wpdb->prefix . 'chatgabi_whatsapp_users';
        $whatsapp_conversations_table = $wpdb->prefix . 'chatgabi_whatsapp_conversations';
        
        // Create tables if they don't exist
        if (function_exists('chatgabi_create_whatsapp_tables')) {
            chatgabi_create_whatsapp_tables();
        }
        
        $users_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$whatsapp_users_table'") === $whatsapp_users_table;
        $conversations_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$whatsapp_conversations_table'") === $whatsapp_conversations_table;
        
        if ($users_table_exists) {
            echo '<p class="success">✅ WhatsApp users table exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp users table missing</p>';
        }
        $total_tests++;
        
        if ($conversations_table_exists) {
            echo '<p class="success">✅ WhatsApp conversations table exists</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ WhatsApp conversations table missing</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 4: REST API Endpoints
        echo '<div class="test-section">';
        echo '<h2>🌐 Test 4: REST API Endpoints</h2>';
        
        $endpoints = array(
            '/wp-json/chatgabi/v1/whatsapp/webhook' => 'WhatsApp Webhook',
            '/wp-json/chatgabi/v1/whatsapp/send' => 'Send Message',
            '/wp-json/chatgabi/v1/whatsapp/users' => 'Get Users',
            '/wp-json/chatgabi/v1/whatsapp/analytics' => 'Analytics'
        );
        
        foreach ($endpoints as $endpoint => $description) {
            $url = home_url($endpoint);
            $response = wp_remote_get($url, array('timeout' => 5));
            
            if (!is_wp_error($response)) {
                $status_code = wp_remote_retrieve_response_code($response);
                if ($status_code === 200 || $status_code === 401 || $status_code === 403) {
                    echo "<p class=\"success\">✅ $description endpoint accessible (HTTP $status_code)</p>";
                    $passed_tests++;
                } else {
                    echo "<p class=\"warning\">⚠️ $description endpoint returned HTTP $status_code</p>";
                }
            } else {
                echo "<p class=\"error\">❌ $description endpoint error: " . $response->get_error_message() . "</p>";
            }
            $total_tests++;
        }
        echo '</div>';

        // Test 5: Configuration Check
        echo '<div class="test-section">';
        echo '<h2>⚙️ Test 5: Configuration</h2>';
        
        $access_token = get_option('chatgabi_whatsapp_access_token');
        $phone_number_id = get_option('chatgabi_whatsapp_phone_number_id');
        $verify_token = get_option('chatgabi_whatsapp_verify_token');
        
        if (!empty($access_token)) {
            echo '<p class="success">✅ WhatsApp access token configured</p>';
            $passed_tests++;
        } else {
            echo '<p class="warning">⚠️ WhatsApp access token not configured</p>';
        }
        $total_tests++;
        
        if (!empty($phone_number_id)) {
            echo '<p class="success">✅ WhatsApp phone number ID configured</p>';
            $passed_tests++;
        } else {
            echo '<p class="warning">⚠️ WhatsApp phone number ID not configured</p>';
        }
        $total_tests++;
        
        if (!empty($verify_token)) {
            echo '<p class="success">✅ WhatsApp verify token configured</p>';
            $passed_tests++;
        } else {
            echo '<p class="warning">⚠️ WhatsApp verify token not configured (using default)</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test 6: Country Detection
        echo '<div class="test-section">';
        echo '<h2>🌍 Test 6: Country Detection</h2>';
        
        if (function_exists('chatgabi_detect_country_from_phone')) {
            $test_numbers = array(
                '+233123456789' => 'Ghana',
                '+254123456789' => 'Kenya',
                '+234123456789' => 'Nigeria',
                '+27123456789' => 'South Africa',
                '+1234567890' => 'Ghana' // Default fallback
            );
            
            foreach ($test_numbers as $phone => $expected_country) {
                $detected_country = chatgabi_detect_country_from_phone($phone);
                if ($detected_country === $expected_country) {
                    echo "<p class=\"success\">✅ $phone → $detected_country (correct)</p>";
                    $passed_tests++;
                } else {
                    echo "<p class=\"error\">❌ $phone → $detected_country (expected $expected_country)</p>";
                }
                $total_tests++;
            }
        } else {
            echo '<p class="error">❌ Country detection function not available</p>';
            $total_tests++;
        }
        echo '</div>';

        // Test 7: Integration with Existing Systems
        echo '<div class="test-section">';
        echo '<h2>🔗 Test 7: Integration with Existing Systems</h2>';
        
        // Check OpenAI integration
        if (function_exists('businesscraft_ai_process_openai_request')) {
            echo '<p class="success">✅ OpenAI integration available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ OpenAI integration not available</p>';
        }
        $total_tests++;
        
        // Check language functions
        if (function_exists('chatgabi_get_language_strings')) {
            echo '<p class="success">✅ Language system available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ Language system not available</p>';
        }
        $total_tests++;
        
        // Check African context engine
        if (function_exists('load_business_dataset_by_country')) {
            echo '<p class="success">✅ African Context Engine available</p>';
            $passed_tests++;
        } else {
            echo '<p class="error">❌ African Context Engine not available</p>';
        }
        $total_tests++;
        echo '</div>';

        // Test Summary
        echo '<div class="test-section">';
        echo '<h2>📊 Test Summary</h2>';
        
        $success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 1) : 0;
        
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Tests</td><td>$total_tests</td></tr>";
        echo "<tr><td>Passed Tests</td><td class=\"success\">$passed_tests</td></tr>";
        echo "<tr><td>Failed Tests</td><td class=\"error\">" . ($total_tests - $passed_tests) . "</td></tr>";
        echo "<tr><td>Success Rate</td><td class=\"" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error')) . "\">$success_rate%</td></tr>";
        echo "</table>";
        
        if ($success_rate >= 80) {
            echo '<p class="success">🎉 <strong>WhatsApp integration is ready for deployment!</strong></p>';
        } elseif ($success_rate >= 60) {
            echo '<p class="warning">⚠️ <strong>WhatsApp integration needs configuration before deployment.</strong></p>';
        } else {
            echo '<p class="error">❌ <strong>WhatsApp integration has critical issues that need to be resolved.</strong></p>';
        }
        echo '</div>';

        // Configuration Instructions
        echo '<div class="test-section">';
        echo '<h2>📝 Configuration Instructions</h2>';
        echo '<p>To complete the WhatsApp integration setup:</p>';
        echo '<ol>';
        echo '<li>Go to <strong>ChatGABI → WhatsApp</strong> in the WordPress admin</li>';
        echo '<li>Configure your WhatsApp Business API credentials</li>';
        echo '<li>Set up the webhook URL in your Facebook Developer Console</li>';
        echo '<li>Test the integration with a real WhatsApp number</li>';
        echo '</ol>';
        
        echo '<div class="code">';
        echo '<strong>Webhook URL:</strong><br>';
        echo home_url('/wp-json/chatgabi/v1/whatsapp/webhook');
        echo '</div>';
        echo '</div>';
        ?>
    </div>
</body>
</html>

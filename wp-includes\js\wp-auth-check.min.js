/*! This file is auto-generated */
!function(i){var h,d,e;function r(){var e=window.adminpage,a=window.wp;i(window).off("beforeunload.wp-auth-check"),("post-php"===e||"post-new-php"===e)&&a&&a.heartbeat&&a.heartbeat.connectNow(),h.fadeOut(200,function(){h.addClass("hidden").css("display",""),i("#wp-auth-check-frame").remove(),i("body").removeClass("modal-open")})}i(function(){(h=i("#wp-auth-check-wrap")).find(".wp-auth-check-close").on("click",function(){r(),d=!0,window.clearTimeout(e),e=window.setTimeout(function(){d=!1},3e5)})}).on("heartbeat-tick.wp-auth-check",function(e,a){var o,t,n,c,s;"wp-auth-check"in a&&(a["wp-auth-check"]||!h.hasClass("hidden")||d?a["wp-auth-check"]&&!h.hasClass("hidden")&&r():(t=i("#wp-auth-check"),n=i("#wp-auth-check-form"),c=h.find(".wp-auth-fallback-expired"),s=!1,n.length&&(i(window).on("beforeunload.wp-auth-check",function(e){e.originalEvent.returnValue=window.wp.i18n.__("Your session has expired. You can log in again from this page or go to the login page.")}),(o=i('<iframe id="wp-auth-check-frame" frameborder="0">').attr("title",c.text())).on("load",function(){var e,a;s=!0,n.removeClass("loading");try{e=(a=i(this).contents().find("body")).height()}catch(e){return h.addClass("fallback"),t.css("max-height",""),n.remove(),void c.focus()}e?a&&a.hasClass("interim-login-success")?r():t.css("max-height",e+40+"px"):a&&a.length||(h.addClass("fallback"),t.css("max-height",""),n.remove(),c.focus())}).attr("src",n.data("src")),n.append(o)),i("body").addClass("modal-open"),h.removeClass("hidden"),o?(o.focus(),setTimeout(function(){s||(h.addClass("fallback"),n.remove(),c.focus())},1e4)):c.focus()))})}(jQuery);
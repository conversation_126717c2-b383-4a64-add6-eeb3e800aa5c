<?php
/**
 * Test Analytics Dashboard - Add Sample Data and Test
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions');
}

echo "<h1>🧪 Analytics Dashboard Test</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; } 
.success { color: green; } 
.error { color: red; } 
.info { color: blue; }
.test-section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-radius: 5px; }
</style>";

global $wpdb;

// Table names
$sector_logs_table = $wpdb->prefix . 'chatgabi_sector_logs';
$chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';

// Check if tables exist
$sector_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_logs_table}'") === $sector_logs_table;
$chat_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$chat_logs_table}'") === $chat_logs_table;

echo "<div class='test-section'>";
echo "<h2>📊 Table Status Check</h2>";
echo "<p><strong>Sector Logs Table:</strong> " . ($sector_table_exists ? "<span class='success'>✅ EXISTS</span>" : "<span class='error'>❌ MISSING</span>") . "</p>";
echo "<p><strong>Chat Logs Table:</strong> " . ($chat_table_exists ? "<span class='success'>✅ EXISTS</span>" : "<span class='error'>❌ MISSING</span>") . "</p>";
echo "</div>";

// Create tables if they don't exist
if (!$sector_table_exists) {
    echo "<div class='test-section'>";
    echo "<h2>🔧 Creating Sector Logs Table</h2>";
    
    chatgabi_create_sector_logs_table();
    
    $sector_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$sector_logs_table}'") === $sector_logs_table;
    echo "<p>" . ($sector_table_exists ? "<span class='success'>✅ Table created successfully</span>" : "<span class='error'>❌ Failed to create table</span>") . "</p>";
    echo "</div>";
}

// Add sample data if requested
if (isset($_GET['add_sample_data']) && $_GET['add_sample_data'] === '1') {
    echo "<div class='test-section'>";
    echo "<h2>📝 Adding Sample Data</h2>";
    
    // Sample data for testing
    $sample_data = array(
        array(
            'user_id' => 1,
            'country' => 'Ghana',
            'detected_sector' => 'Fintech',
            'sector_context_found' => 1,
            'opportunities_included' => 3,
            'opportunities_tokens_estimated' => 150,
            'prompt_tokens_estimated' => 450,
            'user_message_preview' => 'How can I start a fintech business in Ghana?'
        ),
        array(
            'user_id' => 2,
            'country' => 'Kenya',
            'detected_sector' => 'Agriculture',
            'sector_context_found' => 1,
            'opportunities_included' => 2,
            'opportunities_tokens_estimated' => 120,
            'prompt_tokens_estimated' => 380,
            'user_message_preview' => 'I want to start an agritech startup in Kenya'
        ),
        array(
            'user_id' => 3,
            'country' => 'Nigeria',
            'detected_sector' => 'E-commerce',
            'sector_context_found' => 1,
            'opportunities_included' => 1,
            'opportunities_tokens_estimated' => 80,
            'prompt_tokens_estimated' => 320,
            'user_message_preview' => 'Best practices for e-commerce in Nigeria'
        ),
        array(
            'user_id' => 4,
            'country' => 'South Africa',
            'detected_sector' => 'Renewable Energy',
            'sector_context_found' => 1,
            'opportunities_included' => 4,
            'opportunities_tokens_estimated' => 200,
            'prompt_tokens_estimated' => 520,
            'user_message_preview' => 'Solar energy business opportunities in South Africa'
        ),
        array(
            'user_id' => 1,
            'country' => 'Ghana',
            'detected_sector' => 'Creative Arts',
            'sector_context_found' => 1,
            'opportunities_included' => 2,
            'opportunities_tokens_estimated' => 100,
            'prompt_tokens_estimated' => 350,
            'user_message_preview' => 'How to monetize creative content in Ghana'
        ),
        array(
            'user_id' => 5,
            'country' => 'Kenya',
            'detected_sector' => 'Healthcare',
            'sector_context_found' => 0,
            'opportunities_included' => 0,
            'opportunities_tokens_estimated' => 0,
            'prompt_tokens_estimated' => 280,
            'user_message_preview' => 'General business advice for healthcare'
        )
    );
    
    $inserted = 0;
    foreach ($sample_data as $data) {
        // Add timestamp variation (last 30 days)
        $days_ago = rand(1, 30);
        $data['timestamp'] = date('Y-m-d H:i:s', strtotime("-{$days_ago} days"));
        
        $result = $wpdb->insert($sector_logs_table, $data);
        if ($result) {
            $inserted++;
        }
    }
    
    echo "<p class='success'>✅ Inserted {$inserted} sample records</p>";
    echo "</div>";
}

// Check current data
echo "<div class='test-section'>";
echo "<h2>📈 Current Data Summary</h2>";

if ($sector_table_exists) {
    $total_records = $wpdb->get_var("SELECT COUNT(*) FROM {$sector_logs_table}");
    $countries = $wpdb->get_results("SELECT country, COUNT(*) as count FROM {$sector_logs_table} GROUP BY country ORDER BY count DESC");
    $sectors = $wpdb->get_results("SELECT detected_sector, COUNT(*) as count FROM {$sector_logs_table} WHERE detected_sector IS NOT NULL GROUP BY detected_sector ORDER BY count DESC LIMIT 5");
    
    echo "<p><strong>Total Records:</strong> {$total_records}</p>";
    
    if ($total_records > 0) {
        echo "<h3>Countries:</h3>";
        foreach ($countries as $country) {
            echo "<p>• {$country->country}: {$country->count} queries</p>";
        }
        
        echo "<h3>Top Sectors:</h3>";
        foreach ($sectors as $sector) {
            echo "<p>• {$sector->detected_sector}: {$sector->count} queries</p>";
        }
        
        $avg_opportunities = $wpdb->get_var("SELECT AVG(opportunities_included) FROM {$sector_logs_table}");
        echo "<p><strong>Average Opportunities per Query:</strong> " . number_format($avg_opportunities, 2) . "</p>";
        
        $success_rate = $wpdb->get_var("SELECT (SUM(CASE WHEN opportunities_included > 0 THEN 1 ELSE 0 END) / COUNT(*)) * 100 FROM {$sector_logs_table}");
        echo "<p><strong>Opportunity Integration Success Rate:</strong> " . number_format($success_rate, 1) . "%</p>";
    } else {
        echo "<p class='info'>ℹ️ No data found. Add sample data to test the dashboard.</p>";
    }
} else {
    echo "<p class='error'>❌ Cannot check data - table does not exist</p>";
}
echo "</div>";

// Test analytics functions
echo "<div class='test-section'>";
echo "<h2>🧪 Analytics Functions Test</h2>";

if (function_exists('chatgabi_get_engagement_analytics_data')) {
    try {
        $analytics_data = chatgabi_get_engagement_analytics_data();
        
        echo "<p class='success'>✅ Analytics data function works</p>";
        echo "<h3>Summary Data:</h3>";
        echo "<pre>" . json_encode($analytics_data['summary'], JSON_PRETTY_PRINT) . "</pre>";
        
        echo "<h3>Top Sectors:</h3>";
        echo "<pre>" . json_encode($analytics_data['top_sectors'], JSON_PRETTY_PRINT) . "</pre>";
        
        echo "<h3>Country Breakdown:</h3>";
        echo "<pre>" . json_encode($analytics_data['country_breakdown'], JSON_PRETTY_PRINT) . "</pre>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error testing analytics function: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='error'>❌ Analytics function not found</p>";
}
echo "</div>";

// Action buttons
echo "<div class='test-section'>";
echo "<h2>🎯 Test Actions</h2>";

if ($total_records == 0) {
    echo "<p><a href='?add_sample_data=1' class='button button-primary' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Add Sample Data</a></p>";
}

echo "<p><a href='" . admin_url('tools.php?page=chatgabi-engagement-analytics') . "' class='button button-secondary' style='background: #f1f1f1; color: #333; padding: 10px 20px; text-decoration: none; border-radius: 3px; border: 1px solid #ccc;'>View Analytics Dashboard</a></p>";

echo "<p><a href='" . home_url('/wp-json/chatgabi/v1/opportunities/health') . "' target='_blank' class='button button-secondary' style='background: #f1f1f1; color: #333; padding: 10px 20px; text-decoration: none; border-radius: 3px; border: 1px solid #ccc;'>Test REST API Health</a></p>";

echo "</div>";

// Clear cache option
if (isset($_GET['clear_cache']) && $_GET['clear_cache'] === '1') {
    delete_transient('chatgabi_engagement_analytics_data');
    echo "<div class='test-section'>";
    echo "<p class='success'>✅ Analytics cache cleared</p>";
    echo "</div>";
}

echo "<div class='test-section'>";
echo "<h2>🧹 Maintenance</h2>";
echo "<p><a href='?clear_cache=1' class='button button-secondary' style='background: #f1f1f1; color: #333; padding: 10px 20px; text-decoration: none; border-radius: 3px; border: 1px solid #ccc;'>Clear Analytics Cache</a></p>";
echo "</div>";

echo "<h2>✅ Analytics Dashboard Test Complete!</h2>";
echo "<p>The analytics dashboard should now be ready for use with sample data.</p>";
